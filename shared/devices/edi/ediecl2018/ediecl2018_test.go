package ediecl2018

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// buildRMSEngineMsg creates a message with the specified version and revision
func buildRMSEngineMsg(version, revision byte) []byte {
	msg := make([]byte, RMSEngineRecordCount*RMSEngineDataLength)

	// Set version record header
	msg[0] = 0x01    // Message type
	msg[1] = 0x02    // Device address
	msg[2] = 0x03    // Command code
	msg[3] = 0x00    // Response status
	msg[4] = 0x00    // Reserved
	msg[5] = 0x00    // Reserved
	msg[6] = 0x00    // Reserved
	msg[7] = 0x00    // Reserved
	msg[8] = version // Version
	// Calculate version record checksum
	var sum byte
	for i := 0; i < RMSEngineDataLength-1; i++ {
		sum += msg[i]
	}
	msg[RMSEngineDataLength-1] = ^sum

	// Set revision record header (same as version)
	copy(msg[RMSEngineRevisionRecordStartOffset:RMSEngineRevisionRecordStartOffset+HeaderLength],
		msg[RMSEngineVersionRecordStartOffset:RMSEngineVersionRecordStartOffset+HeaderLength])
	msg[RMSEngineRevisionOffset] = revision // Revision
	// Calculate revision record checksum
	sum = 0
	for i := RMSEngineRevisionRecordStartOffset; i < RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1; i++ {
		sum += msg[i]
	}
	msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] = ^sum

	return msg
}

func TestRMSEngineData(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(t *testing.T, rmsEngine *helper.RmsEngineRecord)
	}{
		{
			name: "invalid message length",
			byteMsg: func() []byte {
				// Create a message that's too short
				msg := make([]byte, RMSEngineRecordCount*RMSEngineDataLength-1)
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "invalid checksum in version record",
			byteMsg: func() []byte {
				msg := buildRMSEngineMsg(0x01, 0x02)
				msg[RMSEngineDataLength-1] = ^msg[RMSEngineDataLength-1] // Flip bits of checksum will always cause an invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "invalid checksum in revision record",
			byteMsg: func() []byte {
				msg := buildRMSEngineMsg(0x01, 0x02)
				msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] = ^msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] // Flip bits of checksum will always cause an invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "mismatched headers",
			byteMsg: func() []byte {
				msg := buildRMSEngineMsg(0x01, 0x02)
				// Modify revision record header to be different
				msg[RMSEngineRevisionRecordStartOffset] = 0x02 // Different message type
				// Recalculate revision record checksum
				var sum byte
				for i := RMSEngineRevisionRecordStartOffset; i < RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1; i++ {
					sum += msg[i]
				}
				msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrRMSEngineDataHeaderMismatch,
		},
		{
			name:    "valid message",
			byteMsg: buildRMSEngineMsg(0x01, 0x02),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, rmsEngine *helper.RmsEngineRecord) {
				assert.NotNil(t, rmsEngine)
				assert.Equal(t, DeviceModel, rmsEngine.DeviceModel)
				assert.Equal(t, "0.1", rmsEngine.EngineVersion)
				assert.Equal(t, "0.2", rmsEngine.EngineRevision)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2018{}
			rmsEngine, err := device.RMSEngineData(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, rmsEngine)
			} else {
				assert.NoError(t, err)
				if tt.validateRecord != nil {
					tt.validateRecord(t, rmsEngine)
				}
			}
		})
	}
}

// buildMonitorIDNameMsg creates a message with the specified length and optional monitor name
func buildMonitorIDNameMsg(msgLen int, monitorName string) []byte {
	msg := make([]byte, msgLen)
	msg[0] = 0x01 // Message type
	msg[1] = 0x02 // Device address
	msg[2] = 0x03 // Command code
	msg[3] = 0x00 // Response status
	msg[4] = 0x00 // Reserved
	msg[5] = 0x01 // Monitor ID ls byte
	msg[6] = 0x00 // Monitor ID ms byte

	if monitorName != "" {
		copy(msg[MonitorNameStartOffset:], []byte(monitorName))
	}

	// Calculate checksum
	var sum byte
	for i := 0; i < len(msg)-1; i++ {
		sum += msg[i]
	}
	msg[len(msg)-1] = ^sum

	return msg
}

func TestMonitorIDandName(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(t *testing.T, monitor *helper.MonitorNameAndId)
	}{
		{
			name: "nil header",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength,
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header:        nil,
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name: "invalid checksum",
			byteMsg: func() []byte {
				msg := buildMonitorIDNameMsg(
					HeaderLength+K2018IDNameLength+ChecksumLength,
					"",
				)
				msg[len(msg)-1] = ^msg[len(msg)-1] // Flipping checksum bit will always cause invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "invalid message length",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength-1, // One byte too short
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "valid message with firmware revision > 0x50 and comm version >= 0x27",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLengthFirmware27+ChecksumLength,
				"Test Monitor Name",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, DeviceModel, monitor.DeviceModel)
				assert.Equal(t, int64(1), monitor.MonitorId)
				assert.Equal(t, "Test Monitor Name", monitor.MonitorName)
			},
		},
		{
			name: "valid message with firmware revision <= 0x50",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength,
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x50),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, DeviceModel, monitor.DeviceModel)
				assert.Equal(t, int64(0), monitor.MonitorId)
				assert.Equal(t, "", monitor.MonitorName)
			},
		},
		{
			name: "valid message with firmware revision > 0x50 but comm version < 0x27",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength,
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x26),
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, DeviceModel, monitor.DeviceModel)
				assert.Equal(t, int64(0), monitor.MonitorId)
				assert.Equal(t, "", monitor.MonitorName)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2018{}
			monitor, err := device.MonitorIDandName(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, monitor)
			} else {
				assert.NoError(t, err)
				if tt.validateRecord != nil {
					tt.validateRecord(t, monitor)
				}
			}
		})
	}
}
