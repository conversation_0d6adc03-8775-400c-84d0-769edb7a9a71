package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	PF2018LogLength                = 97
	PreviousFailRecordsCountOffset = HeaderLength
	PreviousFailRecordsCountLength = 1
	PreviousFailRecordsStartOffset = PreviousFailRecordsCountOffset + 1
)

/*
Byte Message Layout
==================

+-------------------------------+
|Header (7 bytes)               |
+-------------------------------+
|Number of records (1 byte)     |
+-------------------------------+
|Record #1 (97 bytes)           |
+-------------------------------+
|Record #2 (97 bytes)           |
+-------------------------------+
|              ...              |
+-------------------------------+
|Record #N (97 bytes)           |
+-------------------------------+
|Checksum (1 byte)              |
+-------------------------------+

Header and number of records (8 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte
[7] Number of records (0-255)

Record Structure (97 bytes per record)
-----------------------------------
[0]   Fault status byte (bit 7: d_gyr_status, bits 0-6: fault code)
[1-3] Fault status (3 bytes, bit field for channel faults)
[4-6] Green status (3 bytes, bit field for green channel status)
[7-9] Yellow status (3 bytes, bit field for yellow channel status)
[10-12] Red status (3 bytes, bit field for red channel status)
[13] NV status byte (bits 7-6: special functions, bit 3: MC Coil EE, bit 0: Red Enable)
[14-15] AC line voltage and frequency
[16] sec (BCD)
[17] min (BCD)
[18] hour (BCD)
[19] day (BCD)
[20] month (BCD)
[21] year (BCD)
[22] Temperature (value - 40)
[23-24] T24VDC Input (if comm version > 0x36)
[25-26] T24VDC Input (if comm version > 0x36)
[27] MC Coil EE voltage
[28] Special Function 2 voltage
[29] Special Function 1 voltage
[30] T48VDC Signal Bus voltage
[31-85] Channel voltage readings (72 bytes, 24 channels × 3 colors)
[86-88] Red recurrent pulse status (3 bytes)
[89-91] Yellow recurrent pulse status (3 bytes)
[92-94] Green recurrent pulse status (3 bytes)
[95-96] GFON status (3 bytes)

Checksum (1 byte)
----------------
[Last byte] Additive checksum (one's complement of sum of all previous bytes)
*/

// LogPreviousFail parses the previous fail log bytes into a LogPreviousFailRecords struct
func (device EDIECL2010) LogPreviousFail(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogPreviousFailRecords, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	if err = validateDeviceInfo(header); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrUnsupportedDevice, err)
	}

	numRecords := int(byteMsg[PreviousFailRecordsCountOffset])
	byteMessageLength := HeaderLength + PreviousFailRecordsCountLength + numRecords*PF2018LogLength + ChecksumLength
	allRecords = &helper.LogPreviousFailRecords{
		DeviceModel: header.Model.String(),
		RawMessage:  byteMsg,
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	for idx := range numRecords {
		offset := PreviousFailRecordsStartOffset + idx*PF2018LogLength
		record, err := parsePreviousFailRecord(byteMsg, offset, header, httpHeader)
		if err != nil {
			return nil, fmt.Errorf("failed to parse record %d: %w", idx, err)
		}
		allRecords.Records = append(allRecords.Records, *record)
	}

	return allRecords, nil
}

// parsePreviousFailRecord extracts a single record from the response bytes
func parsePreviousFailRecord(responseBytes []byte, startByte int, headerDetail *helper.HeaderRecord, httpHeader *pubsubdata.HeaderDetails) (record *helper.LogPreviousFailRecord, err error) {
	record = &helper.LogPreviousFailRecord{}
	commVersion := helper.ConvertBCDStringToInt(headerDetail.CommVersion)
	// Extract fault and status
	fault := responseBytes[startByte] & 0x7F // Mask off d_gyr_status bit (b7)
	nvstat := responseBytes[startByte+13] & 0xC0

	// Parse multi-byte status fields
	faultStatus := combineBytes(responseBytes[startByte+1 : startByte+4])
	greenStatus := combineBytes(responseBytes[startByte+4 : startByte+7])
	yellowStatus := combineBytes(responseBytes[startByte+7 : startByte+10])
	redStatus := combineBytes(responseBytes[startByte+10 : startByte+13])
	redRPStatus := combineBytes(responseBytes[startByte+85 : startByte+88])
	yellowRPStatus := combineBytes(responseBytes[startByte+88 : startByte+91])
	greenRPStatus := combineBytes(responseBytes[startByte+91 : startByte+94])
	gfonStatus := combineBytes(responseBytes[startByte+94 : startByte+97])

	// Parse RMS voltages
	greenRmsVoltages, yellowRmsVoltages, redRmsVoltages := parseRmsVoltages(
		responseBytes, startByte, headerDetail,
	)

	// Hydrate the record
	faultString, showFaultStatus := getFaultStatus2018(headerDetail, fault, faultStatus, nvstat)
	record.Fault = faultString
	record.Temperature = int64(responseBytes[startByte+22]) - 40
	record.AcLine = formatAcLine(responseBytes[startByte+14], responseBytes[startByte+15], headerDetail.Volt220)
	record.T48VDCSignalBus, record.RedEnable = formatT48VDCOrRedEnable(
		responseBytes[startByte+13], responseBytes[startByte+30], headerDetail,
	)
	record.MCCoilEE = formatMCCoilEE(responseBytes[startByte+13], responseBytes[startByte+27])
	record.SpecialFunction1 = formatSpecialFunction(
		responseBytes[startByte+13], responseBytes[startByte+29], 1, headerDetail,
	)
	record.SpecialFunction2 = formatSpecialFunction(
		responseBytes[startByte+13], responseBytes[startByte+28], 2, headerDetail,
	)
	record.WDTMonitor = formatWDTMonitor(responseBytes[startByte+13])
	record.T24VDCInput = formatT24VDCInput(responseBytes[startByte+25], responseBytes[startByte+26], int(commVersion))

	// Parse channel-specific statuses
	record.FaultStatus = parseFaultStatus(faultStatus, showFaultStatus, int(headerDetail.MaxChannels))
	record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
		responseBytes[startByte+20], responseBytes[startByte+19],
		responseBytes[startByte+21], responseBytes[startByte+18],
		responseBytes[startByte+17], responseBytes[startByte+16],
		httpHeader.GatewayTimezone,
	)
	if err != nil {
		return nil, err
	}
	record.ChannelGreenStatus = helper.ParseChannelStatus(uint32(greenStatus), int(headerDetail.MaxChannels))
	record.ChannelYellowStatus = helper.ParseChannelStatus(uint32(yellowStatus), int(headerDetail.MaxChannels))
	record.ChannelRedStatus = helper.ParseChannelStatus(uint32(redStatus), int(headerDetail.MaxChannels))
	record.NextConflictingChannels = parseConflictingChannels(fault, gfonStatus, int(headerDetail.MaxChannels))
	record.ChannelGreenRecurrentPulseStatus, record.ChannelYellowRecurrentPulseStatus, record.ChannelRedRecurrentPulseStatus = parseRecurrentPulseStatus(
		greenRPStatus, yellowRPStatus, redRPStatus, int(headerDetail.MaxChannels),
	)

	// Assign RMS voltages
	record.ChannelRedRmsVoltage = redRmsVoltages
	record.ChannelYellowRmsVoltage = yellowRmsVoltages
	record.ChannelGreenRmsVoltage = greenRmsVoltages

	return record, nil
}

// parseRmsVoltages extracts RMS voltages for green, yellow, and red channels
func parseRmsVoltages(responseBytes []byte, startByte int, headerDetail *helper.HeaderRecord) ([]int32, []int32, []int32) {
	var greenRms, yellowRms, redRms []int32
	const (
		greenOffset  = 84
		yellowOffset = 66
		redOffset    = 48
	)

	for j := 0; j < int(headerDetail.MaxChannels); j++ {
		greenVolt := adjustVoltage(int32(responseBytes[startByte+greenOffset-j]), headerDetail)
		yellowVolt := adjustVoltage(int32(responseBytes[startByte+yellowOffset-j]), headerDetail)
		redVolt := adjustVoltage(int32(responseBytes[startByte+redOffset-j]), headerDetail)

		greenRms = append(greenRms, greenVolt)
		yellowRms = append(yellowRms, yellowVolt)
		redRms = append(redRms, redVolt)
	}

	return greenRms, yellowRms, redRms
}

// formatAcLine formats the AC line voltage and frequency
func formatAcLine(volt, freq byte, volt220 bool) string {
	if volt220 {
		return fmt.Sprintf("%d Vrms @ %d Hz", int(volt)*2, int(freq))
	}
	return fmt.Sprintf("%d Vrms @ %d Hz", int(volt), int(freq))
}

// formatT48VDCOrRedEnable formats the 48VDC signal bus or red enable status
func formatT48VDCOrRedEnable(status, volt byte, headerDetail *helper.HeaderRecord) (string, string) {
	adjustedVolt := adjustVoltage(int32(volt), headerDetail)
	if headerDetail.VoltDC {
		if helper.IsBitSet(uint32(status), 0) {
			return fmt.Sprintf("%d Vrms", adjustedVolt), "Active (12 Vrms)"
		}
		return fmt.Sprintf("%d Vrms", adjustedVolt), ""
	}
	if helper.IsBitSet(uint32(status), 0) {
		return "", fmt.Sprintf("Active (%d Vrms)", adjustedVolt)
	}
	return "", fmt.Sprintf("Off (%d Vrms)", adjustedVolt)
}

// formatMCCoilEE formats the MC Coil EE status
func formatMCCoilEE(status, volt byte) string {
	if helper.IsBitSet(uint32(status), 3) {
		return fmt.Sprintf("Flash (%d Vrms)", int(volt))
	}
	return fmt.Sprintf("Auto (%d Vrms)", int(volt))
}

// formatSpecialFunction formats special function status
func formatSpecialFunction(status, volt byte, bit int, headerDetail *helper.HeaderRecord) string {
	adjustedVolt := adjustVoltage(int32(volt), headerDetail)
	if helper.IsBitSet(uint32(status), bit) {
		return fmt.Sprintf("Active (%d Vrms)", adjustedVolt)
	}
	return fmt.Sprintf("Off (%d Vrms)", adjustedVolt)
}

// formatWDTMonitor formats the WDT monitor status
func formatWDTMonitor(status byte) string {
	if helper.IsBitSet(uint32(status), 7) {
		return "Active"
	}
	return "Off"
}

// formatT24VDCInput formats the 24VDC input
func formatT24VDCInput(volt1, volt2 byte, commVersion int) string {
	if commVersion > 0x36 {
		return fmt.Sprintf("%.1f Vrms, %.1f Vac", float32(volt1)/8, float32(volt2)/32)
	}
	return ""
}

// parseFaultStatus parses the fault status for each channel
func parseFaultStatus(faultStatus int, showFaultStatus bool, maxChannels int) []bool {
	var status []bool
	if !showFaultStatus {
		return status
	}
	for j := 0; j < maxChannels; j++ {
		status = append(status, helper.IsBitSet(uint32(faultStatus), j))
	}
	return status
}

// parseConflictingChannels parses conflicting channels for fault 22
func parseConflictingChannels(fault byte, gfonStatus int, maxChannels int) []bool {
	var result []bool
	if fault != 22 {
		return result
	}
	for j := 0; j < maxChannels; j++ {
		result = append(result, helper.IsBitSet(uint32(gfonStatus), j))
	}
	return result
}

// parseRecurrentPulseStatus parses recurrent pulse statuses
func parseRecurrentPulseStatus(green, yellow, red int, maxChannels int) ([]bool, []bool, []bool) {
	var greenStatus, yellowStatus, redStatus []bool
	if green == 0 && yellow == 0 && red == 0 {
		return greenStatus, yellowStatus, redStatus
	}
	for j := 0; j < maxChannels; j++ {
		greenStatus = append(greenStatus, helper.IsBitSet(uint32(green), j))
		yellowStatus = append(yellowStatus, helper.IsBitSet(uint32(yellow), j))
		redStatus = append(redStatus, helper.IsBitSet(uint32(red), j))
	}
	return greenStatus, yellowStatus, redStatus
}

func getFaultStatus2018(headerRecord *helper.HeaderRecord, fault byte, faultStatus int, nvStat byte) (string, bool) {
	var faultString string
	showFaultStatus := true

	const NVPrevWD byte = 0x40

	switch fault {
	case 1:
		faultString = "+24VDC Low Fault"
		showFaultStatus = false
	case 2:
		faultString = "CU Watchdog Fault"
		showFaultStatus = false
		nvStat = (nvStat & (^NVPrevWD))
	case 3:
		faultString = "Conflict Fault"
	case 4:
		faultString = "Dual Indication Fault"
	case 5:
		faultString = "Red Fail Fault"
	case 6:
		faultString = "Clearance (Skipped Yellow) Fault"
	case 7:
		faultString = "BND Fault"
	case 8:
		switch faultStatus {
		case 1430:
			faultString = "Diagnostic Fault (RMS Engine A/D error)"
		case 1670:
			faultString = "Diagnostic Fault (RMS Engine comm error)"
		case 2000:
			faultString = "Diagnostic Fault (EEprom error)"
		case 2500:
			faultString = "Diagnostic Fault (program card serial path)"
		case 3300:
			faultString = "Diagnostic Fault (switch serial path)"
		case 5000:
			faultString = "Diagnostic Fault (display serial path)"
		case 10000:
			faultString = "Diagnostic Fault (logic serial path)"
		}
	case 9:
		faultString = "Program Card Ajar Fault"
		showFaultStatus = false
	case 12:
		faultString = "Red Cable Fault"
	case 13:
		faultString = "Configuration Change Fault"
		commVersion := helper.ConvertBCDStringToInt(headerRecord.CommVersion)
		if commVersion > 0x30 {
			faultString += fmt.Sprintf(" (Check Value = %d)", faultStatus)
		}
		showFaultStatus = false
	case 14:
		faultString = "Clearance (Short Yellow) Fault"
	case 15:
		faultString = "Recurrent Pulse Conflict"
	case 16:
		faultString = "Recurrent Pulse Dual Indication"
	case 17:
		faultString = "Recurrent Pulse Red Fail"
	case 18:
		faultString = "+48VDC Fault"
	case 19:
		faultString = "Data Key Absent"
		showFaultStatus = false
	case 20:
		faultString = "Data Key FCS Error"
		showFaultStatus = false
	case 21:
		faultString = "Data Key Invalid Parameter Error"
		showFaultStatus = false
	case 22:
		faultString = "Minimum Yellow + Red Clearance Fault"
		showFaultStatus = false
	case 23:
		faultString = "+24VDC High Fault"
		showFaultStatus = false
	case 24:
		faultString = "+24VDC Ripple Fault"
		showFaultStatus = false
	case 25:
		faultString = "Program Card Fault (Program Card is not 18 Channel)"
		showFaultStatus = false
	case 27:
		faultString = "FYA Flash Rate Fault"
	default:
		faultString = "undefined fault type"
	}
	if nvStat&NVPrevWD != 0 {
		faultString += " with previous Watchdog Fault Status"
	}
	return faultString, showFaultStatus
}
