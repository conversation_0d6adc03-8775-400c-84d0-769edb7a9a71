package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	MR2018LogLength                = 6
	MonitorResetRecordsCountLength = 1
	MonitorResetRecordsCountOffset = HeaderLength
	MonitorResetRecordsStartOffset = MonitorResetRecordsCountOffset + 1
)

/*
Byte Message Layout
==================

+-------------------------------+
|Header (7 bytes)               |
+-------------------------------+
|Number of records (1 byte)     |
+-------------------------------+
|Record #1 (6 bytes)            |
+-------------------------------+
|Record #2 (6 bytes)            |
+-------------------------------+
|              ...              |
+-------------------------------+
|Record #N (6 bytes)            |
+-------------------------------+
|Checksum (1 byte)              |
+-------------------------------+

Header and number of records (8 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte
[7] Number of records (0-255)

Record Structure (6 bytes per record)
-----------------------------------
[8] Second in BCD format (00-59)
[9] Minute in BCD format (00-59)
[10] Hour in BCD format (00-23)
[11] Day in BCD format (01-31)
[12] Month in BCD format (01-12)
[13] Year in BCD format (00-99)

Checksum (1 byte)
----------------
[Last byte] XOR checksum of all previous bytes
*/

// LogMonitorReset parses the monitor reset log bytes into a LogMonitorResetRecords struct
func (device EDIECL2010) LogMonitorReset(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogMonitorResetRecords, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	if err = validateDeviceInfo(header); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrUnsupportedDevice, err)
	}

	numRecords := int(byteMsg[MonitorResetRecordsCountOffset])
	byteMessageLength := HeaderLength + MonitorResetRecordsCountLength + numRecords*MR2018LogLength + ChecksumLength
	allRecords = &helper.LogMonitorResetRecords{
		DeviceModel: header.Model.String(),
		RawMessage:  byteMsg,
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	for idx := range numRecords {
		offset := MonitorResetRecordsStartOffset + idx*MR2018LogLength
		record := helper.LogMonitorResetRecord{}
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
			byteMsg[offset+4],
			byteMsg[offset+3],
			byteMsg[offset+5],
			byteMsg[offset+2],
			byteMsg[offset+1],
			byteMsg[offset+0],
			httpHeader.GatewayTimezone,
		)
		if err != nil {
			return nil, err
		}
		allRecords.Records = append(allRecords.Records, record)
	}

	return allRecords, nil
}
