package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	Get2018RmsStatusLength = 96
	FaultCodeOffset        = HeaderLength
)

/*
Byte Message Format
===================

+-------------------------------+
|Header (7 bytes)               |
+-------------------------------+
|RMS Status (96 bytes)          |
+-------------------------------+

Header (7 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte

RMS Status (96 bytes)
---------------------
[7] Fault Code
[8] 	Reserved byte
[9] 	Reserved byte
[10-12] 	Fault Status
[13-15] 	Channel Green Status
[16-18] 	Channel Yellow Status
[19-21] 	Channel Red Status
[22] Second in BCD format (00-59)
[23] Minute in BCD format (00-59)
[24] Hour in BCD format (00-23)
[25] Day in BCD format (01-31)
[26] Month in BCD format (01-12)
[27] Year in BCD format (00-99)
[28] 	Temperature
[29-30] 	Reserved byte
[31-51] 	Voltages Red
[52-70] 	Voltages Yellow
[70-88] 	Voltages Green
[89-101] 	Reserved bytes
[102] XOR checksum of all previous bytes
*/

// RMSStatus parses the RMS Status message
func (device EDIECL2010) RMSStatus(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsStatusRecord *helper.RmsStatusRecord, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	if err = validateDeviceInfo(header); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrUnsupportedDevice, err)
	}

	// Checksum is included in the RMS Status
	byteMessageLength := HeaderLength + Get2018RmsStatusLength
	rmsStatusRecord = &helper.RmsStatusRecord{
		DeviceModel: header.Model.String(),
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	// hydrate IsFaulted
	faultCode := int(byteMsg[FaultCodeOffset])
	if faultCode == 0x00 {
		rmsStatusRecord.IsFaulted = false
		rmsStatusRecord.Fault = "No Fault"
	} else {
		rmsStatusRecord.IsFaulted = true
		faultStatus := combineBytes(byteMsg[10:13])
		rmsStatusRecord.Fault, rmsStatusRecord.FaultStatus = device.getFaultMessage(faultCode, faultStatus, header)
	}

	// hydrate the Channel Statuses
	rmsStatusRecord.ChannelGreenStatus = parseChannelStatus(combineBytes(byteMsg[13:16]), int(header.MaxChannels))
	rmsStatusRecord.ChannelYellowStatus = parseChannelStatus(combineBytes(byteMsg[16:19]), int(header.MaxChannels))
	rmsStatusRecord.ChannelRedStatus = parseChannelStatus(combineBytes(byteMsg[19:22]), int(header.MaxChannels))

	// hydrate the MonitorTime
	rmsStatusRecord.MonitorTime, err = helper.ConvertBCDBytesToDateTimeII(
		byteMsg[26], byteMsg[25],
		byteMsg[27], byteMsg[24],
		byteMsg[23], byteMsg[22],
		httpHeader.GatewayTimezone)
	if err != nil {
		return nil, err
	}

	// hydrate the Temperature
	rmsStatusRecord.Temperature = convertToFahrenheit(int64(byteMsg[28]))

	// hydrate the voltages
	rmsStatusRecord.VoltagesRed = formatVoltages(byteMsg[34:52], header)
	rmsStatusRecord.VoltagesYellow = formatVoltages(byteMsg[52:70], header)
	rmsStatusRecord.VoltagesGreen = formatVoltages(byteMsg[70:88], header)

	return rmsStatusRecord, nil
}

func (device EDIECL2010) getFaultMessage(faultCode int, faultStatus int, header *helper.HeaderRecord) (faultMessage string, faultStatusMessage string) {
	commVersion := helper.ConvertBCDStringToInt(header.CommVersion)
	switch faultCode {
	case 1:
		faultMessage = "+24VDC Low Fault (VDC Fail)"
	case 2:
		faultMessage = "CU Watchdog Fault (WDT Error)"
	case 3:
		faultMessage = "Conflict Fault"
	case 4:
		faultMessage = "Dual Indication Fault"
	case 5:
		faultMessage = "Red Fail Fault"
	case 6:
		faultMessage = "Clearance (Skipped Yellow) Fault"
	case 7:
		faultMessage = "BND Fault"
	case 8:
		switch faultStatus {
		case 1430:
			faultStatusMessage = "(RMS Engine A/D error)"
		case 1670:
			faultStatusMessage = "(RMS Engine comm error)"
		case 2000:
			faultStatusMessage = "(EEprom error)"
		case 2500:
			faultStatusMessage = "(program card serial path)"
		case 3330:
			faultStatusMessage = "(switch serial path)"
		case 5000:
			faultStatusMessage = "(display serial path)"
		case 10000:
			faultStatusMessage = "(logic serial path)"
		default:
			faultStatusMessage = ""
		}
	case 9:
		faultMessage = "Program Card Ajar Fault"
	case 10:
		faultMessage = "AC Line Low Voltage"
	case 12:
		faultMessage = "Red Cable Fault"
	case 13:
		faultMessage = "Configuration Change Fault"
		if commVersion >= 0x30 {
			faultStatusMessage = fmt.Sprintf("Check Value = %d", faultStatus)
		}
	case 14:
		faultMessage = "Clearance (Short Yellow) Fault"
	case 15:
		faultMessage = "Recurrent Pulse Conflict"
	case 16:
		faultMessage = "Recurrent Pulse Dual Indication"
	case 17:
		faultMessage = "Recurrent Pulse Red Fail"
	case 18:
		faultMessage = "+48VDC Fault (VDC Fail)"
	case 19:
		faultMessage = "Data Key Absent"
	case 20:
		faultMessage = "Data Key FCS Error"
	case 21:
		faultMessage = "Data Key Invalid Parameter Error"
	case 22:
		faultMessage = "Minimum Yellow + Red Clearance Fault"
	case 23:
		faultMessage = "+24VDC High Fault"
	case 24:
		faultMessage = "+24VDC Ripple Fault"
	case 25:
		faultMessage = "Program Card Fault"
		faultStatusMessage = "(Program Card is not 18 Channel)"
	case 26:
		faultMessage = "AC Line Frequency Fault"
	case 27:
		faultMessage = "FYA Flash Rate Fault"
	default:
		faultMessage = "undefined fault type"
	}

	return faultMessage, faultStatusMessage
}

func formatVoltages(rawVoltages []byte, header *helper.HeaderRecord) []int64 {
	size := len(rawVoltages)
	voltages := make([]int64, size)
	for idx, volt := range rawVoltages {
		voltages[size-1-idx] = int64(adjustVoltage(int32(volt), header))
	}
	return voltages
}
