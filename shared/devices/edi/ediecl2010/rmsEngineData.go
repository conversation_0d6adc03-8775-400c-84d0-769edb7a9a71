package ediecl2010

import (
	"bytes"
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	RMSEngineRecordCount = 2
	RMSEngineDataLength  = 10

	// Version record
	RMSEngineVersionRecordStartOffset = 0
	RMSEngineVersionOffset            = 8

	// Revision record
	RMSEngineRevisionRecordStartOffset = 10
	RMSEngineRevisionOffset            = RMSEngineVersionOffset + RMSEngineDataLength
)

/*
Byte Message Format
===================

+-------------------------------+
|RMS Engine Version (10 bytes)  |
+-------------------------------+
|RMS Engine Revision (10 bytes) |
+-------------------------------+

*/

// RMSEngineData parses the rms engine data from the byte message
func (device EDIECL2010) RMSEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsEngineDetail *helper.RmsEngineRecord, err error) {
	rmsEngineDetail = &helper.RmsEngineRecord{
		DeviceModel: header.Model.String(),
	}

	byteMessageLength := RMSEngineRecordCount * RMSEngineDataLength
	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	err = helper.ValidateChecksum(byteMsg[RMSEngineVersionRecordStartOffset : RMSEngineVersionRecordStartOffset+RMSEngineDataLength])
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	err = helper.ValidateChecksum(byteMsg[RMSEngineRevisionRecordStartOffset : RMSEngineRevisionRecordStartOffset+RMSEngineDataLength])
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	versionHeader := byteMsg[RMSEngineVersionRecordStartOffset : RMSEngineVersionRecordStartOffset+HeaderLength]
	revisionHeader := byteMsg[RMSEngineRevisionRecordStartOffset : RMSEngineRevisionRecordStartOffset+HeaderLength]
	if !bytes.Equal(versionHeader, revisionHeader) {
		return nil, fmt.Errorf("%w : %v", helper.ErrRMSEngineDataHeaderMismatch, err)
	}

	rmsEngineDetail.EngineVersion = helper.ConvertByteToString(byteMsg[RMSEngineVersionOffset])
	rmsEngineDetail.EngineRevision = helper.ConvertByteToDecimalFormat(byteMsg[RMSEngineRevisionOffset])

	return rmsEngineDetail, nil
}
