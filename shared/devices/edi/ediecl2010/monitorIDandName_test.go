package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// buildMonitorIDNameMsg creates a message with the specified length and optional monitor name
func buildMonitorIDNameMsg(msgLen int, monitorName string) []byte {
	msg := make([]byte, msgLen)
	msg[0] = 0x01 // Message type
	msg[1] = 0x02 // Device address
	msg[2] = 0x03 // Command code
	msg[3] = 0x00 // Response status
	msg[4] = 0x00 // Reserved
	msg[5] = 0x01 // Monitor ID ls byte
	msg[6] = 0x00 // Monitor ID ms byte

	if monitorName != "" {
		copy(msg[MonitorNameStartOffset:], []byte(monitorName))
	}

	// Calculate checksum
	var sum byte
	for i := 0; i < len(msg)-1; i++ {
		sum += msg[i]
	}
	msg[len(msg)-1] = ^sum

	return msg
}

func TestMonitorIDandName(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(t *testing.T, monitor *helper.MonitorNameAndId)
	}{
		{
			name: "nil header",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength,
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header:        nil,
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name: "invalid checksum",
			byteMsg: func() []byte {
				msg := buildMonitorIDNameMsg(
					HeaderLength+K2018IDNameLength+ChecksumLength,
					"",
				)
				msg[len(msg)-1] = ^msg[len(msg)-1] // Flipping checksum bit will always cause invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "invalid message length",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength-1, // One byte too short
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "valid message with firmware revision > 0x50 and comm version >= 0x27",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLengthFirmware27+ChecksumLength,
				"Test Monitor Name",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, helper.Ecl2010.String(), monitor.DeviceModel)
				assert.Equal(t, int64(1), monitor.MonitorId)
				assert.Equal(t, "Test Monitor Name", monitor.MonitorName)
			},
		},
		{
			name: "valid message with firmware revision <= 0x50",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength,
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x50),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x27),
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, helper.Ecl2010.String(), monitor.DeviceModel)
				assert.Equal(t, int64(0), monitor.MonitorId)
				assert.Equal(t, "", monitor.MonitorName)
			},
		},
		{
			name: "valid message with firmware revision > 0x50 but comm version < 0x27",
			byteMsg: buildMonitorIDNameMsg(
				HeaderLength+K2018IDNameLength+ChecksumLength,
				"",
			),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:            helper.Ecl2010,
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x26),
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, monitor *helper.MonitorNameAndId) {
				assert.NotNil(t, monitor)
				assert.Equal(t, helper.Ecl2010.String(), monitor.DeviceModel)
				assert.Equal(t, int64(0), monitor.MonitorId)
				assert.Equal(t, "", monitor.MonitorName)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			monitor, err := device.MonitorIDandName(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, monitor)
			} else {
				assert.NoError(t, err)
				if tt.validateRecord != nil {
					tt.validateRecord(t, monitor)
				}
			}
		})
	}
}
