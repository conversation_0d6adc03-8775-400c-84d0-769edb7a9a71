package ediecl2010

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestGetFaultMessage(t *testing.T) {
	tests := []struct {
		name              string
		faultCode         int
		faultStatus       int
		header            *helper.HeaderRecord
		expectedMessage   string
		expectedStatusMsg string
	}{
		{
			name:              "fault code 0 - no fault",
			faultCode:         0,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "undefined fault type",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 1 - +24VDC Low Fault",
			faultCode:         1,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "+24VDC Low Fault (VDC Fail)",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 2 - CU Watchdog Fault",
			faultCode:         2,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "CU Watchdog Fault (WDT Error)",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 3 - Conflict Fault",
			faultCode:         3,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Conflict Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 4 - Dual Indication Fault",
			faultCode:         4,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Dual Indication Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 5 - Red Fail Fault",
			faultCode:         5,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Red Fail Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 6 - Clearance Fault",
			faultCode:         6,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Clearance (Skipped Yellow) Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 7 - BND Fault",
			faultCode:         7,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "BND Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 8 - RMS Engine A/D error",
			faultCode:         8,
			faultStatus:       1430,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "(RMS Engine A/D error)",
		},
		{
			name:              "fault code 8 - RMS Engine comm error",
			faultCode:         8,
			faultStatus:       1670,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "(RMS Engine comm error)",
		},
		{
			name:              "fault code 8 - EEprom error",
			faultCode:         8,
			faultStatus:       2000,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "(EEprom error)",
		},
		{
			name:              "fault code 8 - program card serial path",
			faultCode:         8,
			faultStatus:       2500,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "(program card serial path)",
		},
		{
			name:              "fault code 8 - switch serial path",
			faultCode:         8,
			faultStatus:       3330,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "(switch serial path)",
		},
		{
			name:              "fault code 8 - display serial path",
			faultCode:         8,
			faultStatus:       5000,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "(display serial path)",
		},
		{
			name:              "fault code 8 - logic serial path",
			faultCode:         8,
			faultStatus:       10000,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "(logic serial path)",
		},
		{
			name:              "fault code 8 - unknown status",
			faultCode:         8,
			faultStatus:       9999,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 9 - Program Card Ajar Fault",
			faultCode:         9,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Program Card Ajar Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 10 - AC Line Low Voltage",
			faultCode:         10,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "AC Line Low Voltage",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 12 - Red Cable Fault",
			faultCode:         12,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Red Cable Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 13 - Configuration Change Fault (CommVersion < 0x30)",
			faultCode:         13,
			faultStatus:       123,
			header:            &helper.HeaderRecord{CommVersion: helper.ConvertByteToDecimalFormat(0x29)},
			expectedMessage:   "Configuration Change Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 13 - Configuration Change Fault (CommVersion >= 0x30)",
			faultCode:         13,
			faultStatus:       123,
			header:            &helper.HeaderRecord{CommVersion: helper.ConvertByteToDecimalFormat(0x30)},
			expectedMessage:   "Configuration Change Fault",
			expectedStatusMsg: "Check Value = 123",
		},
		{
			name:              "fault code 14 - Clearance Short Yellow Fault",
			faultCode:         14,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Clearance (Short Yellow) Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 15 - Recurrent Pulse Conflict",
			faultCode:         15,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Recurrent Pulse Conflict",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 16 - Recurrent Pulse Dual Indication",
			faultCode:         16,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Recurrent Pulse Dual Indication",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 17 - Recurrent Pulse Red Fail",
			faultCode:         17,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Recurrent Pulse Red Fail",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 18 - +48VDC Fault",
			faultCode:         18,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "+48VDC Fault (VDC Fail)",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 19 - Data Key Absent",
			faultCode:         19,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Data Key Absent",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 20 - Data Key FCS Error",
			faultCode:         20,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Data Key FCS Error",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 21 - Data Key Invalid Parameter Error",
			faultCode:         21,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Data Key Invalid Parameter Error",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 22 - Minimum Yellow + Red Clearance Fault",
			faultCode:         22,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Minimum Yellow + Red Clearance Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 23 - +24VDC High Fault",
			faultCode:         23,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "+24VDC High Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 24 - +24VDC Ripple Fault",
			faultCode:         24,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "+24VDC Ripple Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 25 - Program Card Fault",
			faultCode:         25,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "Program Card Fault",
			expectedStatusMsg: "(Program Card is not 18 Channel)",
		},
		{
			name:              "fault code 26 - AC Line Frequency Fault",
			faultCode:         26,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "AC Line Frequency Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "fault code 27 - FYA Flash Rate Fault",
			faultCode:         27,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "FYA Flash Rate Fault",
			expectedStatusMsg: "",
		},
		{
			name:              "undefined fault code",
			faultCode:         99,
			faultStatus:       0,
			header:            &helper.HeaderRecord{},
			expectedMessage:   "undefined fault type",
			expectedStatusMsg: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			message, statusMsg := device.getFaultMessage(tt.faultCode, tt.faultStatus, tt.header)
			assert.Equal(t, tt.expectedMessage, message)
			assert.Equal(t, tt.expectedStatusMsg, statusMsg)
		})
	}
}

func TestFormatVoltages(t *testing.T) {
	tests := []struct {
		name           string
		rawVoltages    []byte
		header         *helper.HeaderRecord
		expectedResult []int64
	}{
		{
			name:           "empty voltage array",
			rawVoltages:    []byte{},
			header:         &helper.HeaderRecord{MaxChannels: 0},
			expectedResult: []int64{},
		},
		{
			name:           "single voltage",
			rawVoltages:    []byte{100},
			header:         &helper.HeaderRecord{MaxChannels: 1},
			expectedResult: []int64{100},
		},
		{
			name:           "multiple voltages",
			rawVoltages:    []byte{100, 150, 200},
			header:         &helper.HeaderRecord{MaxChannels: 3},
			expectedResult: []int64{200, 150, 100},
		},
		{
			name:           "voltages with adjustment",
			rawVoltages:    []byte{100, 150, 200},
			header:         &helper.HeaderRecord{MaxChannels: 3, MainsDC: true},
			expectedResult: []int64{200, 150, 100},
		},
		{
			name:           "voltages with max channels",
			rawVoltages:    []byte{100, 150, 200, 250, 255},
			header:         &helper.HeaderRecord{MaxChannels: 3},
			expectedResult: []int64{255, 250, 200, 150, 100},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatVoltages(tt.rawVoltages, tt.header)
			assert.Equal(t, tt.expectedResult, result)
			assert.Equal(t, len(tt.rawVoltages), len(result), "Result length should match input length")
		})
	}
}

func TestRMSStatus(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(byteMsg []byte) func(t *testing.T, record *helper.RmsStatusRecord)
		validateError  func(t *testing.T, err error)
	}{
		{
			name: "nil header",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+Get2018RmsStatusLength)
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			header:        nil,
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name: "invalid checksum",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+Get2018RmsStatusLength)
				// Set valid BCD date fields to avoid early validation failure
				msg[22] = 0x00         // Seconds
				msg[23] = 0x00         // Minutes
				msg[24] = 0x12         // Hours
				msg[25] = 0x01         // Day
				msg[26] = 0x01         // Month
				msg[27] = 0x24         // Year
				msg[len(msg)-1] = 0xFF // Invalid checksum (since message is made here, it's fine to set to 0xFF)
				return msg
			}(),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			header:        &helper.HeaderRecord{Model: helper.Ecl2010, CommVersion: helper.ConvertByteToDecimalFormat(0x27)},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "unsupported device",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+Get2018RmsStatusLength)
				// Set valid BCD date fields to avoid early validation failure
				msg[22] = 0x00 // Seconds
				msg[23] = 0x00 // Minutes
				msg[24] = 0x12 // Hours
				msg[25] = 0x01 // Day
				msg[26] = 0x01 // Month
				msg[27] = 0x24 // Year
				// Set device model to NSM3E in the message
				msg[1] = byte(helper.Nsm3e)
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			header:        &helper.HeaderRecord{Model: helper.Nsm3e, CommVersion: helper.ConvertByteToDecimalFormat(0x27), FirmwareRevision: helper.ConvertByteToDecimalFormat(0x51)},
			expectedError: helper.ErrUnsupportedDevice,
		},
		{
			name: "invalid message length",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+Get2018RmsStatusLength-1) // One byte too short
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			header:        &helper.HeaderRecord{Model: helper.Ecl2010, CommVersion: helper.ConvertByteToDecimalFormat(0x27)},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "no fault",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+Get2018RmsStatusLength)
				msg[FaultCodeOffset] = 0x00 // No fault code
				msg[13] = 0x00              // Green status high byte
				msg[14] = 0x00              // Green status mid byte
				msg[15] = 0x01              // Green status low byte (channel 1)
				msg[16] = 0x00              // Yellow status high byte
				msg[17] = 0x00              // Yellow status mid byte
				msg[18] = 0x02              // Yellow status low byte (channel 2)
				msg[19] = 0x00              // Red status high byte
				msg[20] = 0x00              // Red status mid byte
				msg[21] = 0x04              // Red status low byte (channel 3)
				msg[22] = 0x00
				msg[23] = 0x00
				msg[24] = 0x12
				msg[25] = 0x01
				msg[26] = 0x01
				msg[27] = 0x24
				msg[28] = 80
				for i := 31; i < 88; i++ {
					msg[i] = 100
				}
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			header:        &helper.HeaderRecord{Model: helper.Ecl2010, CommVersion: helper.ConvertByteToDecimalFormat(0x27), MaxChannels: 3},
			expectedError: nil,
			validateRecord: func(byteMsg []byte) func(t *testing.T, record *helper.RmsStatusRecord) {
				return func(t *testing.T, record *helper.RmsStatusRecord) {
					greenVal := int(byteMsg[13])*256*256 + int(byteMsg[14])*256 + int(byteMsg[15])
					yellowVal := int(byteMsg[16])*256*256 + int(byteMsg[17])*256 + int(byteMsg[18])
					redVal := int(byteMsg[19])*256*256 + int(byteMsg[20])*256 + int(byteMsg[21])
					t.Logf("Green combineBytes: %06x, Yellow: %06x, Red: %06x", greenVal, yellowVal, redVal)
					t.Logf("ChannelGreenStatus: %v", record.ChannelGreenStatus)
					t.Logf("ChannelYellowStatus: %v", record.ChannelYellowStatus)
					t.Logf("ChannelRedStatus: %v", record.ChannelRedStatus)
					assert.NotNil(t, record)
					assert.Equal(t, helper.Ecl2010.String(), record.DeviceModel)
					assert.False(t, record.IsFaulted)
					assert.Equal(t, "No Fault", record.Fault)
					assert.Equal(t, "", record.FaultStatus)
					assert.Equal(t, []bool{true, false, false}, record.ChannelGreenStatus)
					assert.Equal(t, []bool{false, true, false}, record.ChannelYellowStatus)
					assert.Equal(t, []bool{false, false, true}, record.ChannelRedStatus)
					assert.Equal(t, int64(40), record.Temperature)
					assert.Len(t, record.VoltagesRed, 18)
					assert.Len(t, record.VoltagesYellow, 18)
					assert.Len(t, record.VoltagesGreen, 18)
				}
			},
		},
		{
			name: "with fault",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+Get2018RmsStatusLength)
				msg[FaultCodeOffset] = 0x01 // Fault code 1 (+24VDC Low Fault)
				msg[13] = 0x00              // Green status high byte
				msg[14] = 0x00              // Green status mid byte
				msg[15] = 0x01              // Green status low byte (channel 1)
				msg[16] = 0x00              // Yellow status high byte
				msg[17] = 0x00              // Yellow status mid byte
				msg[18] = 0x02              // Yellow status low byte (channel 2)
				msg[19] = 0x00              // Red status high byte
				msg[20] = 0x00              // Red status mid byte
				msg[21] = 0x04              // Red status low byte (channel 3)
				msg[22] = 0x00
				msg[23] = 0x00
				msg[24] = 0x12
				msg[25] = 0x01
				msg[26] = 0x01
				msg[27] = 0x24
				msg[28] = 80
				for i := 31; i < 88; i++ {
					msg[i] = 100
				}
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			header:        &helper.HeaderRecord{Model: helper.Ecl2010, CommVersion: helper.ConvertByteToDecimalFormat(0x27), MaxChannels: 3},
			expectedError: nil,
			validateRecord: func(byteMsg []byte) func(t *testing.T, record *helper.RmsStatusRecord) {
				return func(t *testing.T, record *helper.RmsStatusRecord) {
					greenVal := int(byteMsg[13])*256*256 + int(byteMsg[14])*256 + int(byteMsg[15])
					yellowVal := int(byteMsg[16])*256*256 + int(byteMsg[17])*256 + int(byteMsg[18])
					redVal := int(byteMsg[19])*256*256 + int(byteMsg[20])*256 + int(byteMsg[21])
					t.Logf("Green combineBytes: %06x, Yellow: %06x, Red: %06x", greenVal, yellowVal, redVal)
					t.Logf("ChannelGreenStatus: %v", record.ChannelGreenStatus)
					t.Logf("ChannelYellowStatus: %v", record.ChannelYellowStatus)
					t.Logf("ChannelRedStatus: %v", record.ChannelRedStatus)
					assert.NotNil(t, record)
					assert.Equal(t, helper.Ecl2010.String(), record.DeviceModel)
					assert.True(t, record.IsFaulted)
					assert.Equal(t, "+24VDC Low Fault (VDC Fail)", record.Fault)
					assert.Equal(t, "", record.FaultStatus)
					assert.Equal(t, []bool{true, false, false}, record.ChannelGreenStatus)
					assert.Equal(t, []bool{false, true, false}, record.ChannelYellowStatus)
					assert.Equal(t, []bool{false, false, true}, record.ChannelRedStatus)
					assert.Equal(t, int64(40), record.Temperature)
					assert.Len(t, record.VoltagesRed, 18)
					assert.Len(t, record.VoltagesYellow, 18)
					assert.Len(t, record.VoltagesGreen, 18)
				}
			},
		},
		{
			name: "invalid BCD date-time",
			byteMsg: func() []byte {
				msg := make([]byte, HeaderLength+Get2018RmsStatusLength)
				// Set valid BCD date fields to avoid early validation failure
				msg[22] = 0x99 // Invalid seconds
				msg[23] = 0x99 // Invalid minutes
				msg[24] = 0x99 // Invalid hours
				msg[25] = 0x99 // Invalid day
				msg[26] = 0x99 // Invalid month
				msg[27] = 0x99 // Invalid year
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			httpHeader:    &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"},
			header:        &helper.HeaderRecord{Model: helper.Ecl2010, CommVersion: helper.ConvertByteToDecimalFormat(0x27)},
			expectedError: helper.ErrValidateDateTimePartsMon,
			validateRecord: func(byteMsg []byte) func(t *testing.T, record *helper.RmsStatusRecord) {
				return func(t *testing.T, record *helper.RmsStatusRecord) {
					assert.Nil(t, record)
				}
			},
			validateError: func(t *testing.T, err error) {
				assert.Error(t, err)
				assert.True(t, errors.Is(err, helper.ErrValidateDateTimePartsMon), "expected error to be ErrValidateDateTimePartsMon")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			record, err := device.RMSStatus(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				if tt.validateError != nil {
					tt.validateError(t, err)
				}
				assert.Nil(t, record)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, record)
				if tt.validateRecord != nil {
					tt.validateRecord(tt.byteMsg)(t, record)
				}
			}
		})
	}
}
