package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	K2018IDNameLength           = 40
	K2018IDNameLengthFirmware27 = 42
	MonitorIDlsByteOffset       = 5
	MonitorIDmsByteOffset       = 6
	MonitorNameStartOffset      = HeaderLength + 2
)

/*
Byte Message Format
===================

+-------------------------------+
|Header (7 bytes)               |
+-------------------------------+
|Monitor name (40/42 bytes)     |
+-------------------------------+
|Checksum (1 byte)              |
+-------------------------------+

Header (7 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Monitor ID ls byte
[6] Monitor ID ms byte

*/

// MonitorIDandName parses the monitor ID and name from the byte message
func (device EDIECL2010) MonitorIDandName(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (monitor *helper.MonitorNameAndId, err error) {
	if header == nil {
		return nil, fmt.Errorf("%w header is nil", helper.ErrMsgHeaderRecordNil)
	}

	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}
	commVersion := helper.ConvertBCDStringToInt(header.CommVersion)
	firmwareRevision := helper.ConvertBCDStringToInt(header.FirmwareRevision)
	byteMessageLength := HeaderLength + K2018IDNameLength + ChecksumLength
	monitorID := 0
	monitorName := ""
	if commVersion >= 0x27 && firmwareRevision > 0x50 {
		byteMessageLength = HeaderLength + K2018IDNameLengthFirmware27 + ChecksumLength
	}

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	if commVersion >= 0x27 && firmwareRevision > 0x50 {
		monitorID = int(helper.ConvertLSandMStoUnint16(byteMsg[MonitorIDlsByteOffset], byteMsg[MonitorIDmsByteOffset]))
		monitorName = helper.GetMonitorName(byteMsg[MonitorNameStartOffset : byteMessageLength-ChecksumLength])
	}

	monitor = &helper.MonitorNameAndId{
		DeviceModel: header.Model.String(),
		MonitorId:   int64(monitorID),
		MonitorName: monitorName,
	}

	return monitor, nil
}
