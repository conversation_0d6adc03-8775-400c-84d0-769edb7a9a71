package ediecl2010

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
)

// validateDeviceInfo validates the device is supported or not
func validateDeviceInfo(header *helper.HeaderRecord) error {
	firmwareRevision := helper.ConvertBCDStringToInt(header.FirmwareRevision)
	if header.Model != helper.Ecl2010 && firmwareRevision > 0x50 {
		return fmt.Errorf("%w model mismatch", helper.ErrUnsupportedDevice)
	}

	return nil
}

// combineBytes converts three bytes into a single integer in big-endian order
func combineBytes(bytes []byte) int {
	return int(bytes[0])*256*256 + int(bytes[1])*256 + int(bytes[2])
}

// parseChannelStatus parses channel status for green, yellow, or red
func parseChannelStatus(status int, maxChannels int) []bool {
	var result []bool
	for j := 0; j < maxChannels; j++ {
		result = append(result, helper.IsBitSet(uint32(status), j))
	}
	return result
}

// adjustVoltage applies voltage adjustments based on header details
func adjustVoltage(volt int32, headerDetail *helper.HeaderRecord) int32 {
	if headerDetail.Volt220 {
		volt *= 2
	}
	if headerDetail.VoltDC {
		volt /= 4
	}
	return volt
}

// convertToFahrenheit converts to Fahrenheit
func convertToFahrenheit(temperature int64) int64 {
	return temperature - 40
}
