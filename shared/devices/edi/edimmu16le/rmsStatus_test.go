package edimmu16le

import (
	"errors"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// baseMsgRMSStatus returns a 106-byte slice pre-filled with valid defaults for RMSStatus
func baseMsgRMSStatus() []byte {
	msg := make([]byte, 106)
	// temperature at byte 38: raw=50 => 10°F
	msg[38] = 50
	// channel statuses: green bits 0-1 set (MaxChannels=2)
	msg[26] = 0x03 // low byte
	msg[27] = 0x00 // high byte
	// yellow & red statuses = 0
	msg[28], msg[29] = 0, 0
	msg[30], msg[31] = 0, 0
	// BCD date/time parts: sec, min, hr, day, mon, yr
	msg[39] = 0x18 // sec = 18
	msg[40] = 0x16 // min = 16
	msg[41] = 0x09 // hr  = 9
	msg[42] = 0x08 // day = 8
	msg[43] = 0x07 // mon = 7
	msg[44] = 0x21 // yr  = 21 -> 2021
	// voltages: green=1, yellow=2, red=3
	for i := 47; i <= 62; i++ {
		msg[i] = 1
	}
	for i := 63; i <= 78; i++ {
		msg[i] = 2
	}
	for i := 79; i <= 94; i++ {
		msg[i] = 3
	}
	return msg
}

func TestRMSStatus_Errors(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2}

	// 1) length error
	if _, err := d.RMSStatus(hdr, make([]byte, 105), helperHdr); err == nil {
		t.Errorf("expected length error for len<106, got nil")
	}

	// 2) checksum error
	msg := baseMsgRMSStatus()
	msg[len(msg)-1] = 0x00 // wrong checksum
	if _, err := d.RMSStatus(hdr, msg, helperHdr); !errors.Is(err, helper.ErrMsgByteChecksum) {
		t.Errorf("expected checksum error, got %v", err)
	}

	// 3) date parts validation error: invalid month (0x13 => 19)
	msg = baseMsgRMSStatus()
	// set BCD month to 0x13 (invalid)
	msg[43] = 0x13
	msg[len(msg)-1] = computeChecksum(msg)
	if _, err := d.RMSStatus(hdr, msg, helperHdr); !errors.Is(err, helper.ErrValidateDateTimePartsMon) {
		t.Errorf("expected date parts validation error, got %v", err)
	}
}

func TestRMSStatus_NoFault(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2, Volt220: false}

	msg := baseMsgRMSStatus()
	// set fault = 0 => no fault
	msg[7] = 0x00
	msg[len(msg)-1] = computeChecksum(msg)

	rec, err := d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	// no fault
	if rec.IsFaulted {
		t.Error("expected IsFaulted=false for fault=0")
	}
	if rec.Fault != "No Fault" {
		t.Errorf("expected Fault='No Fault', got '%s'", rec.Fault)
	}
	// channel statuses
	if len(rec.ChannelGreenStatus) != 2 || !rec.ChannelGreenStatus[0] || !rec.ChannelGreenStatus[1] {
		t.Error("expected both green channels true")
	}
	if len(rec.ChannelYellowStatus) != 2 || rec.ChannelYellowStatus[0] || rec.ChannelYellowStatus[1] {
		t.Error("expected both yellow channels false")
	}
	if len(rec.ChannelRedStatus) != 2 || rec.ChannelRedStatus[0] || rec.ChannelRedStatus[1] {
		t.Error("expected both red channels false")
	}
	// temperature
	if rec.Temperature != 10 {
		t.Errorf("expected Temperature=10, got %d", rec.Temperature)
	}
	// voltages
	if len(rec.VoltagesGreen) != 16 || rec.VoltagesGreen[0] != 1 {
		t.Errorf("expected first green voltage=1, got %v", rec.VoltagesGreen)
	}
	if len(rec.VoltagesYellow) != 16 || rec.VoltagesYellow[0] != 2 {
		t.Errorf("expected first yellow voltage=2, got %v", rec.VoltagesYellow)
	}
	if len(rec.VoltagesRed) != 16 || rec.VoltagesRed[0] != 3 {
		t.Errorf("expected first red voltage=3, got %v", rec.VoltagesRed)
	}
	// monitor time
	exp := time.Date(2021, 7, 8, 9, 16, 18, 0, time.UTC)
	if !rec.MonitorTime.Equal(exp) {
		t.Errorf("expected MonitorTime=%v, got %v", exp, rec.MonitorTime)
	}
}

func TestRMSStatus_CVMFaultAndDefault(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	// MaxChannels small to speed up
	helperHdr := &helper.HeaderRecord{MaxChannels: 1, Volt220: true}

	// CVM Fault (fault=1)
	msg := baseMsgRMSStatus()
	msg[7] = 0x01
	msg[len(msg)-1] = computeChecksum(msg)
	rec, err := d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error for CVM fault: %v", err)
	}
	if !rec.IsFaulted || rec.Fault != "CVM Fault" {
		t.Errorf("expected CVM Fault, got IsFaulted=%v, Fault='%s'", rec.IsFaulted, rec.Fault)
	}

	// undefined fault (default)
	msg = baseMsgRMSStatus()
	msg[7] = 0xFF
	msg[len(msg)-1] = computeChecksum(msg)
	rec, err = d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error for default fault: %v", err)
	}
	if !rec.IsFaulted || rec.Fault != "Undefined Fault Type Error" {
		t.Errorf("expected default fault, got IsFaulted=%v, Fault='%s'", rec.IsFaulted, rec.Fault)
	}

	// check other green yellow red statuses
	msg = baseMsgRMSStatus()
	msg[26] = 0 // low byte
	msg[27] = 0 // high byte
	msg[28], msg[29] = 0x03, 0x00
	msg[30], msg[31] = 0x03, 0x00
	msg[len(msg)-1] = computeChecksum(msg)
	rec, err = d.RMSStatus(hdr, msg, helperHdr)
	if err != nil {
		t.Fatalf("unexpected error for default fault: %v", err)
	}
	// channel statuses
	if rec.ChannelGreenStatus[0] {
		t.Error("expected both green channels false")
	}
	if !rec.ChannelYellowStatus[0] {
		t.Error("expected both yellow channels true")
	}
	if !rec.ChannelRedStatus[0] {
		t.Error("expected both red channels true")
	}
}

// Add missing test cases for RMSStatus additional error conditions
func TestRMSStatus_AdditionalErrors(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2}

	testCases := []struct {
		name    string
		modify  func([]byte)
		wantErr error
	}{
		{
			name: "InvalidDayBCD",
			modify: func(msg []byte) {
				msg[42] = 0x32 // day = 50 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name: "InvalidHourBCD",
			modify: func(msg []byte) {
				msg[41] = 0x25 // hour = 37 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name: "InvalidMinBCD",
			modify: func(msg []byte) {
				msg[40] = 0x61 // min = 97 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name: "InvalidSecBCD",
			modify: func(msg []byte) {
				msg[39] = 0x61 // sec = 97 (invalid BCD)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := baseMsgRMSStatus()
			tc.modify(msg)
			_, err := d.RMSStatus(hdr, msg, helperHdr)
			if !errors.Is(err, tc.wantErr) {
				t.Errorf("expected error %v, got %v", tc.wantErr, err)
			}
		})
	}
}

// Add test for specific RMSStatus fault cases that might be missing
func TestRMSStatus_AdditionalFaultCases(t *testing.T) {
	t.Parallel()
	d := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	helperHdr := &helper.HeaderRecord{MaxChannels: 2, Volt220: true}

	t.Run("Volt220 voltage multiplication", func(t *testing.T) {
		t.Parallel()
		msg := baseMsgRMSStatus()
		msg[7] = 0x00 // no fault
		// Set specific voltage values to test Volt220 multiplication
		// Note: voltage arrays are reversed in the code (62 down to 47 for green)
		msg[62] = 25 // first green voltage (channel 0)
		msg[78] = 30 // first yellow voltage (channel 0)
		msg[94] = 35 // first red voltage (channel 0)
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := d.RMSStatus(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// With Volt220=true, voltages should be doubled
		if rec.VoltagesGreen[0] != 50 {
			t.Errorf("expected green voltage 50 (25*2), got %d", rec.VoltagesGreen[0])
		}
		if rec.VoltagesYellow[0] != 60 {
			t.Errorf("expected yellow voltage 60 (30*2), got %d", rec.VoltagesYellow[0])
		}
		if rec.VoltagesRed[0] != 70 {
			t.Errorf("expected red voltage 70 (35*2), got %d", rec.VoltagesRed[0])
		}
	})

	t.Run("Specific fault types with faultStatus", func(t *testing.T) {
		t.Parallel()
		msg := baseMsgRMSStatus()
		msg[7] = 32   // Conflict Fault
		msg[8] = 0x01 // faultStatus low byte
		msg[9] = 0x00 // faultStatus high byte
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := d.RMSStatus(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if !rec.IsFaulted {
			t.Error("expected IsFaulted=true for fault=32")
		}
		if rec.Fault != "Conflict Fault" {
			t.Errorf("expected Fault='Conflict Fault', got '%s'", rec.Fault)
		}
	})
}
