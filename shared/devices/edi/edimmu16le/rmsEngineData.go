package edimmu16le

import (
	"bytes"
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	RMSEngineDataLength = 20
)

/*
===============================================================================
Byte Message Layout (RMSEngineData)
===============================================================================

+-------------------------------+
|RMS Engine Version (10 bytes)  |
+-------------------------------+
|RMS Engine Revision (10 bytes) |
+-------------------------------+

RMS Engine Version (10 bytes):
  Header (7 bytes):
		[0] Command code
		[1-7] Reserved bytes
	[8] Engine version
	[9] Checksum

RMS Engine Revision (10 bytes):
  Header (7 bytes):
		[0] Command code
		[1-7] Reserved bytes
	[8] Engine revision
	[9] Checksum

*/

func (device EDIMMU216LE) RMSEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsEngineDetail *helper.RmsEngineRecord, err error) {
	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, RMSEngineDataLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), RMSEngineDataLength)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg[0:10])
	if err != nil {
		return nil, fmt.Errorf("%w 0-9 : %v", helper.ErrMsgByteChecksum, err)
	}

	// Validate the checksum of revison
	err = helper.ValidateChecksum(byteMsg[10:20])
	if err != nil {
		return nil, fmt.Errorf("%w 10-19 : %v", helper.ErrMsgByteChecksum, err)
	}

	// 1-6:   7-byte version request header
	// byte 0 is the command and that wont match and is not included
	versionHeader := byteMsg[1:7]
	// 11-16: 7-byte revision request header
	// byte 0 is the command and that wont match and is not included
	revisionHeader := byteMsg[11:17]

	// Validate the headers match
	if !bytes.Equal(versionHeader, revisionHeader) {
		return nil, fmt.Errorf("%w", helper.ErrRMSEngineDataHeaderMismatch)
	}

	return &helper.RmsEngineRecord{
		DeviceModel:    header.Model.String(),
		EngineVersion:  helper.ConvertByteToString(byteMsg[8]),
		EngineRevision: helper.ConvertByteToDecimalFormat(byteMsg[18]),
	}, nil
}
