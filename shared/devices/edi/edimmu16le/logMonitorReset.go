package edimmu16le

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	LogMonitorResetRecordLength = 7
)

/*
===============================================================================
Byte Message Layout (LogMonitorReset)
===============================================================================

+-------------------------------+
|        Header (7 bytes)       |
+-------------------------------+
|   Number of records (1 byte)  |
+-------------------------------+
|   Log Monitor Reset Records   |
|   (7 bytes per record)        |
+-------------------------------+
|   Checksum (1 byte)           |
+-------------------------------+

Header and number of records (8 bytes):
  [0] Message type identifier
  [1] Device address
  [2] Command code
  [3] Response status
  [4] Reserved byte
  [5] Reserved byte
  [6] Reserved byte
  [7] Number of records (0-255)

Log Monitor Reset Record (7 bytes per record):
  [0] Seconds
  [1] Minutes
  [2] Hours
  [3] Day of month
  [4] Month
  [5] Year
  [6] Reset type
*/

func (device EDIMMU216LE) LogMonitorReset(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogMonitorResetRecords, err error) {
	if len(byteMsg) < HeaderLength {
		return nil, fmt.Errorf("%w byte length less then %d", helper.ErrMsgByteLen, HeaderLength)
	}

	numberOfRecords := int(byteMsg[HeaderLength])
	length := HeaderLength + RecordCountLength + (numberOfRecords * LogMonitorResetRecordLength) + ChecksumLength

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	allRecords = &helper.LogMonitorResetRecords{
		DeviceModel: header.Model.String(),
		Records:     make([]helper.LogMonitorResetRecord, numberOfRecords),
		RawMessage:  byteMsg,
	}

	// Parse the records
	for idx := range numberOfRecords {
		var record helper.LogMonitorResetRecord
		offset := HeaderLength + RecordCountLength + idx*LogMonitorResetRecordLength

		// pick out the raw byte
		resetTypeFromResponse := byteMsg[offset+6]

		// build a Go string and take its address
		var resetTypeText string
		switch resetTypeFromResponse {
		case 1:
			resetTypeText = "MONITOR MANUAL RESET EVENT #"
		case 2:
			resetTypeText = "MONITOR NON-LATCHED FAULT RESET EVENT #"
		case 4:
			resetTypeText = "MONITOR EXTERNAL RESET EVENT #"
		case 8:
			resetTypeText = "MONITOR REMOTE RESET EVENT #"
		default:
			resetTypeText = "MONITOR RESET EVENT #"
		}
		record.ResetType = resetTypeText

		// call your BCD->time.Time converter directly
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
			byteMsg[offset+4],
			byteMsg[offset+3],
			byteMsg[offset+5],
			byteMsg[offset+2],
			byteMsg[offset+1],
			byteMsg[offset+0],
			httpHeader.GatewayTimezone,
		)
		if err != nil {
			return nil, err
		}

		allRecords.Records[idx] = record
	}

	return allRecords, nil
}
