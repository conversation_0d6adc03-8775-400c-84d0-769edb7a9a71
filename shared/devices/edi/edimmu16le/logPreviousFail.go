package edimmu16le

import (
	"encoding/binary"
	"fmt"
	"slices"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	LogPreviousFailRecordLength = 98
)

/*
===============================================================================
Byte Message Layout (LogPreviousFail)
===============================================================================

+-------------------------------+
|        Header (7 bytes)       |
+-------------------------------+
|   Number of records (1 byte)  |
+-------------------------------+
|   Log Previous Fail Records   |
|   (98 bytes per record)       |
+-------------------------------+
|   Checksum (1 byte)           |
+-------------------------------+

Header and number of records (8 bytes):
  [0] Message type identifier
  [1] Device address
  [2] Command code
  [3] Response status
  [4] Reserved byte
  [5] Reserved byte
  [6] Reserved byte
  [7] Number of records (0-255)

Log Previous Fail Record (98 bytes per record):
  [0]     Fault code
  [1-2]   Fault status (Little Endian UInt16)
  [3-4]   Field Check Status Green (Little Endian UInt16)
  [5-6]   Field Check Status Yellow (Little Endian UInt16)
  [7-8]   Field Check Status Red (Little Endian UInt16)
  [9-10]  Recurrent Pulse Status Green (Little Endian UInt16)
  [11-12] Recurrent Pulse Status Yellow (Little Endian UInt16)
  [13-14] Recurrent Pulse Status Red (Little Endian UInt16)
  [15-16] Recurrent Pulse Status Walk (Little Endian UInt16)
  [17-18] GGFON Status (Little Endian UInt16)
  [19-20] Green Status (Little Endian UInt16)
  [21-22] Yellow Status (Little Endian UInt16)
  [23-24] Red Status (Little Endian UInt16)
  [25-26] Walk Status (Little Endian UInt16)
  [27]    Control Status
  [28]    AC Line Frequency
  [29]    24V minus 1 (reserved)
  [30]    AC Line Voltage
  [31]    Temperature
  [32]    BCD Seconds
  [33]    BCD Minutes
  [34]    BCD Hour
  [35]    BCD Date
  [36]    BCD Month
  [37]    BCD Year
  [38]    Spare
  [39]    Red Enable RMS Voltage
  [40-51] Green Channel RMS Voltages (12 bytes)
  [52-55] Walk Voltages 1-4 (Green channels)
  [56-67] Yellow Channel RMS Voltages (12 bytes)
  [68-71] Walk Voltages 5-8 (Yellow channels)
  [72-83] Red Channel RMS Voltages (12 bytes)
  [84-87] Reserved
  [88-91] Walk Voltages 9-12 (Walk channels)
  [92-97] Reserved (Field Check Enable values commented out)
*/

func (device EDIMMU216LE) LogPreviousFail(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogPreviousFailRecords, err error) {
	// Rountines.bas - DisplayPFMMULog
	if len(byteMsg) < HeaderLength {
		return nil, fmt.Errorf("%w byte length less then %d", helper.ErrMsgByteLen, HeaderLength)
	}

	numberOfRecords := int(byteMsg[HeaderLength])
	length := HeaderLength + RecordCountLength + (numberOfRecords * LogPreviousFailRecordLength) + ChecksumLength

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	allRecords = &helper.LogPreviousFailRecords{
		DeviceModel: header.Model.String(),
		Records:     make([]helper.LogPreviousFailRecord, numberOfRecords),
		RawMessage:  byteMsg,
	}

	for idx := range numberOfRecords {
		offset := HeaderLength + RecordCountLength + idx*LogPreviousFailRecordLength
		var record helper.LogPreviousFailRecord

		fault := int(byteMsg[offset+0])

		faultStatus := int(binary.LittleEndian.Uint16(byteMsg[offset+1 : offset+3]))

		faultMsg, _, faultStatusFlag := FaultTextAndStatus(fault, faultStatus, int(helper.ConvertBCDStringToInt(header.CommVersion)))
		record.Fault = faultMsg

		fcStatusG := int(binary.LittleEndian.Uint16(byteMsg[offset+3 : offset+5]))
		fcStatusY := int(binary.LittleEndian.Uint16(byteMsg[offset+5 : offset+7]))
		fcStatusR := int(binary.LittleEndian.Uint16(byteMsg[offset+7 : offset+9]))
		rpStatusG := int(binary.LittleEndian.Uint16(byteMsg[offset+9 : offset+11]))
		rpStatusY := int(binary.LittleEndian.Uint16(byteMsg[offset+11 : offset+13]))
		rpStatusR := int(binary.LittleEndian.Uint16(byteMsg[offset+13 : offset+15]))
		rpStatusW := int(binary.LittleEndian.Uint16(byteMsg[offset+15 : offset+17]))
		ggfonStatus := int(binary.LittleEndian.Uint16(byteMsg[offset+17 : offset+19]))
		green := int(binary.LittleEndian.Uint16(byteMsg[offset+19 : offset+21]))
		yellow := int(binary.LittleEndian.Uint16(byteMsg[offset+21 : offset+23]))
		red := int(binary.LittleEndian.Uint16(byteMsg[offset+23 : offset+25]))
		walk := int(binary.LittleEndian.Uint16(byteMsg[offset+25 : offset+27]))

		controlStatus := byteMsg[offset+27]
		type16Mode := helper.IsBitSet(uint32(controlStatus), 1)

		acLineFrequency := byteMsg[offset+28]

		// _24Vminus1 := byteMsg[offset+29]

		acLineVoltage := byteMsg[offset+30]

		temperature := byteMsg[offset+31]

		bcdSeconds := byteMsg[offset+32]
		bcdMinutes := byteMsg[offset+33]
		bcdHour := byteMsg[offset+34]
		bcdDate := byteMsg[offset+35]
		bcdMonth := byteMsg[offset+36]
		bcdYear := byteMsg[offset+37]

		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(bcdMonth, bcdDate, bcdYear, bcdHour, bcdMinutes, bcdSeconds, httpHeader.GatewayTimezone)
		if err != nil {
			return nil, err
		}
		// spare := byteMsg[offset+38]

		redEnableRmsVoltage := byteMsg[offset+39]

		// fcEnableG := int(byteMsg[offset+93])
		// fcEnableG = fcEnableG*256 + int(byteMsg[offset+92])

		// fcEnableY := int(byteMsg[offset+95])
		// fcEnableY = fcEnableY*256 + int(byteMsg[offset+94])

		// fcEnableR := int(byteMsg[offset+97])
		// fcEnableR = fcEnableR*256 + int(byteMsg[offset+96])

		// display data in console

		var channelCount int
		if type16Mode {
			channelCount = 16 // status loop
		} else {
			channelCount = 12
		}

		if faultStatusFlag {
			record.FaultStatus = helper.ParseChannelStatus(uint32(faultStatus), channelCount)
		}
		record.ChannelRedStatus = helper.ParseChannelStatus(uint32(red), channelCount)
		record.ChannelYellowStatus = helper.ParseChannelStatus(uint32(yellow), channelCount)
		record.ChannelGreenStatus = helper.ParseChannelStatus(uint32(green), channelCount)
		// Fix ChannelWalkStatus assignment: only assign if !type16Mode
		if !type16Mode {
			record.ChannelWalkStatus = helper.ParseChannelStatus(uint32(walk), channelCount)
		}
		// R+Y Clearance Status  -- TODO
		if fault == 64 {
			record.NextConflictingChannels = helper.ParseChannelStatus(uint32(ggfonStatus), channelCount)
		}
		// Channel RMS voltages
		redRmsVoltageOffset := offset + 72
		record.ChannelRedRmsVoltage = helper.ParseVoltages(byteMsg[redRmsVoltageOffset:redRmsVoltageOffset+channelCount], header, normalizeVoltages)
		slices.Reverse(record.ChannelRedRmsVoltage)
		yellowRmsVoltageOffset := offset + 56
		record.ChannelYellowRmsVoltage = helper.ParseVoltages(byteMsg[yellowRmsVoltageOffset:yellowRmsVoltageOffset+channelCount], header, normalizeVoltages)
		slices.Reverse(record.ChannelYellowRmsVoltage)
		greenRmsVoltageOffset := offset + 40
		record.ChannelGreenRmsVoltage = helper.ParseVoltages(byteMsg[greenRmsVoltageOffset:greenRmsVoltageOffset+channelCount], header, normalizeVoltages)
		slices.Reverse(record.ChannelGreenRmsVoltage)

		// walk voltages
		if !type16Mode {
			// walk voltages 1-4
			greenChannel4RmsVoltage := byteMsg[offset+52]
			greenChannel3RmsVoltage := byteMsg[offset+53]
			greenChannel2RmsVoltage := byteMsg[offset+54]
			greenChannel1RmsVoltage := byteMsg[offset+55]
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel1RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel2RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel3RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel4RmsVoltage), header))
			// walk voltages 5-8
			yellowChannel4RmsVoltage := byteMsg[offset+68]
			yellowChannel3RmsVoltage := byteMsg[offset+69]
			yellowChannel2RmsVoltage := byteMsg[offset+70]
			yellowChannel1RmsVoltage := byteMsg[offset+71]
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel1RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel2RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel3RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel4RmsVoltage), header))
			// walk voltages 9-12
			walkChannel12RmsVoltage := byteMsg[offset+88]
			walkChannel11RmsVoltage := byteMsg[offset+89]
			walkChannel10RmsVoltage := byteMsg[offset+90]
			walkChannel9RmsVoltage := byteMsg[offset+91]
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel9RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel10RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel11RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel12RmsVoltage), header))
		}

		// field check status
		walkFcStatus := 0
		if helper.IsBitSet(uint32(controlStatus), 2) {
			// SDLC TYPE-12 mode: map high fcStatusG bits -> walk bits
			if fcStatusG&0x800 != 0 {
				walkFcStatus |= 0x80
			} // fc bit11 -> walk bit7
			if fcStatusG&0x400 != 0 {
				walkFcStatus |= 0x20
			} // fc bit10 -> walk bit5
			if fcStatusG&0x200 != 0 {
				walkFcStatus |= 0x08
			} // fc bit9  -> walk bit3
			if fcStatusG&0x100 != 0 {
				walkFcStatus |= 0x02
			} // fc bit8  -> walk bit1
		}

		// only if any field-check word is non-zero do we even populate the slices:
		if fcStatusR != 0 || fcStatusY != 0 || fcStatusG != 0 {
			// 1) always build the three R/Y/G FC slices
			for i := 0; i < channelCount; i++ {
				record.ChannelRedFieldCheckStatus = append(record.ChannelRedFieldCheckStatus,
					helper.IsBitSet(uint32(fcStatusR), i))
				record.ChannelYellowFieldCheckStatus = append(record.ChannelYellowFieldCheckStatus,
					helper.IsBitSet(uint32(fcStatusY), i))
				record.ChannelGreenFieldCheckStatus = append(record.ChannelGreenFieldCheckStatus,
					helper.IsBitSet(uint32(fcStatusG), i))
			}

			// 2) then decide what to do with the walk-FC slice:
			if !helper.IsBitSet(uint32(controlStatus), 2) {
				// DEFAULT (no TYPE-12 bit): give them 12 false entries
				for i := 0; i < channelCount; i++ {
					record.ChannelWalkFieldCheckStatus = append(record.ChannelWalkFieldCheckStatus, false)
				}
			} else if walkFcStatus > 0 {
				// TYPE-12 *with* mapping bits: append exactly those mapped bits
				for i := 0; i < channelCount; i++ {
					record.ChannelWalkFieldCheckStatus = append(record.ChannelWalkFieldCheckStatus,
						helper.IsBitSet(uint32(walkFcStatus), i))
				}
			}
			// otherwise (TYPE-12 but no mapped bits) -> leave ChannelWalkFieldCheckStatus at len=0
		}

		// --- recurrent-pulse status --------------------------------------------

		if rpStatusR != 0 || rpStatusY != 0 || rpStatusG != 0 || rpStatusW != 0 {
			for i := 0; i < channelCount; i++ {
				record.ChannelRedRecurrentPulseStatus = append(record.ChannelRedRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusR), i))
				record.ChannelYellowRecurrentPulseStatus = append(record.ChannelYellowRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusY), i))
				record.ChannelGreenRecurrentPulseStatus = append(record.ChannelGreenRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusG), i))
				if !type16Mode {
					record.ChannelWalkRecurrentPulseStatus = append(record.ChannelWalkRecurrentPulseStatus,
						helper.IsBitSet(uint32(rpStatusW), i))
				}
			}
		}

		// now do the recurrent-pulse block, correctly populating the *recurrent-pulse* walk slice:
		if rpStatusR != 0 || rpStatusY != 0 || rpStatusG != 0 || rpStatusW != 0 {
			for i := 0; i < channelCount; i++ {
				record.ChannelRedRecurrentPulseStatus = append(
					record.ChannelRedRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusR), i),
				)
				record.ChannelYellowRecurrentPulseStatus = append(
					record.ChannelYellowRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusY), i),
				)
				record.ChannelGreenRecurrentPulseStatus = append(
					record.ChannelGreenRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusG), i),
				)
				if !type16Mode {
					record.ChannelWalkRecurrentPulseStatus = append(
						record.ChannelWalkRecurrentPulseStatus,
						helper.IsBitSet(uint32(rpStatusW), i),
					)
				}
			}
		}

		//
		// ac line voltage and frequency
		if header.Volt220 {
			record.AcLine = fmt.Sprintf("%d Vrms @ %dHz", int(acLineVoltage)*2, acLineFrequency)
		} else {
			record.AcLine = fmt.Sprintf("%d Vrms @ %dHz", int(acLineVoltage), acLineFrequency)
		}
		// re, ls bit status
		var redEnable string
		if controlStatus&0x80 > 0 {
			redEnable = "Active"
		} else {
			redEnable = "Off"
		}
		if header.Volt220 {
			record.RedEnable = fmt.Sprintf("%s (%d Vrms)", redEnable, int(redEnableRmsVoltage)*2)
		} else {
			record.RedEnable = fmt.Sprintf("%s (%d Vrms)", redEnable, int(redEnableRmsVoltage))
		}
		if type16Mode {
			if controlStatus&0x40 > 0 {
				record.LsFlashBit = true
			} else {
				record.LsFlashBit = false
			}
		}
		//
		// temperature
		record.Temperature = int64(temperature) - 40
		allRecords.Records[idx] = record
	}

	return allRecords, nil
}

func normalizeVoltages(preNormalizedVoltage int, headerDetail *helper.HeaderRecord) (normalizedVoltage int32) {
	normalizedVoltage = int32(preNormalizedVoltage)
	if headerDetail.Volt220 {
		normalizedVoltage *= 2
	}
	if headerDetail.VoltDC {
		normalizedVoltage = normalizedVoltage / 4
	}
	return normalizedVoltage
}
