package edimmu16le

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	MonitorNameLength = 30
	MonitorIdLOffset  = 5
	MonitorIdMSOffset = 6
)

/*
===============================================================================
Byte Message Layout (MonitorIDandName)
===============================================================================

+-------------------------------+
|        Header (7 bytes)       |
+-------------------------------+
|   Monitor Name (30 bytes)     |
+-------------------------------+
|   Checksum (1 byte)           |
+-------------------------------+

Header (7 bytes):
  [0] Message type identifier
  [1] Device address
  [2] Command code
  [3] Response status
  [4] Reserved byte
  [5] Monitor ID (LS byte)
  [6] Monitor ID (MS byte)

Monitor Name (30 bytes):
  [0-29] Monitor name (ASCII, null-terminated)
*/

func (device EDIMMU216LE) MonitorIDandName(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (monitor *helper.MonitorNameAndId, err error) {
	length := HeaderLength + MonitorNameLength + ChecksumLength

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	monitor = &helper.MonitorNameAndId{
		DeviceModel: header.Model.String(),
		MonitorId:   int64(helper.ConvertLSandMStoUnint16(byteMsg[MonitorIdLOffset], byteMsg[MonitorIdMSOffset])),
		MonitorName: helper.GetMonitorName(byteMsg[HeaderLength:(length - 1)]),
	}

	return monitor, nil
}
