package edimmu16le

import (
	"errors"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestLogMonitorReset(t *testing.T) {
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	hdr := &helper.HeaderRecord{}

	// buildMsgMonitorReset builds the full payload including count+checksum
	// (same as your production helper)
	buildMsgMonitorReset := func(nRecs int, recs [][]byte) []byte {
		total := HeaderLength + RecordCountLength + nRecs*LogMonitorResetRecordLength + ChecksumLength
		msg := make([]byte, total)
		msg[HeaderLength] = byte(nRecs)
		pos := HeaderLength + RecordCountLength
		for _, r := range recs {
			copy(msg[pos:], r)
			pos += LogMonitorResetRecordLength
		}
		// one's‐complement checksum over all but last byte
		var sum byte
		for _, b := range msg[:len(msg)-1] {
			sum += b
		}
		msg[len(msg)-1] = ^sum
		return msg
	}

	// makeRec(year,month,day,minute,hour,second,type)
	makeRec := func(year, month, day, hour, minute, second, typ byte) []byte {
		return []byte{second, minute, hour, day, month, year, typ}
	}

	// reference time = 2014-02-28 05:16:45 UTC
	wantTime := time.Date(2014, 2, 28, 5, 16, 45, 0, time.UTC)

	tests := []struct {
		name      string
		recs      [][]byte
		wantErr   error
		wantTypes []string
	}{
		{
			name:    "TooShort",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:    "BadLength",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:    "BadChecksum",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrMsgByteChecksum,
		},
		// invalid BCD time bytes
		{
			name:    "InvalidMonthBCD",
			recs:    [][]byte{makeRec(0x14, 0x1A, 0x28, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name:    "InvalidDayBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x32, 0x05, 0x16, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name:    "InvalidHourBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x25, 0x16, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name:    "InvalidMinBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x61, 0x45, 1)},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name:    "InvalidSecBCD",
			recs:    [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x61, 1)},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
		{
			name:      "Type1",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1)},
			wantTypes: []string{"MONITOR MANUAL RESET EVENT #"},
		},
		{
			name:      "Type2",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 2)},
			wantTypes: []string{"MONITOR NON-LATCHED FAULT RESET EVENT #"},
		},
		{
			name:      "Type4",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 4)},
			wantTypes: []string{"MONITOR EXTERNAL RESET EVENT #"},
		},
		{
			name:      "Type8",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 8)},
			wantTypes: []string{"MONITOR REMOTE RESET EVENT #"},
		},
		{
			name:      "Default",
			recs:      [][]byte{makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 0)},
			wantTypes: []string{"MONITOR RESET EVENT #"},
		},
		{
			name: "Multiple",
			recs: [][]byte{
				makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 1),
				makeRec(0x14, 0x02, 0x28, 0x05, 0x16, 0x45, 4),
			},
			wantTypes: []string{
				"MONITOR MANUAL RESET EVENT #",
				"MONITOR EXTERNAL RESET EVENT #",
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := buildMsgMonitorReset(len(tc.recs), tc.recs)

			switch tc.name {
			case "BadLength":
				// drop last byte to trigger length error
				msg = msg[:len(msg)-1]
			case "BadChecksum":
				// flip checksum
				msg[len(msg)-1] ^= 0xFF
			case "TooShort":
				msg = make([]byte, 6)
			}

			out, err := dev.LogMonitorReset(httpHdr, msg, hdr)
			if tc.wantErr != nil {
				if err == nil || !errors.Is(err, tc.wantErr) {
					t.Fatalf("expected %v, got %v", tc.wantErr, err)
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			if len(out.Records) != len(tc.wantTypes) {
				t.Fatalf("got %d records, want %d", len(out.Records), len(tc.wantTypes))
			}
			for i, rec := range out.Records {
				if rec.ResetType != tc.wantTypes[i] {
					t.Errorf("record %d ResetType = %q; want %q", i, rec.ResetType, tc.wantTypes[i])
				}
				if !rec.DateTime.Equal(wantTime) {
					t.Errorf("record %d DateTime = %v; want %v", i, rec.DateTime, wantTime)
				}
			}
		})
	}
}
