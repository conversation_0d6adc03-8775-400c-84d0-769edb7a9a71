package edimmu16le

import (
	"fmt"
)

type EDIMMU216LE struct{}

const (
	HeaderLength      = 7
	RecordCountLength = 1
	ChecksumLength    = 1
)

const (
	FaultCVM                    = "CVM Fault"
	Fault24V2                   = "24V-2 Fault"
	FaultCVM24V2                = "CVM & 24V-2 Fault"
	Fault24V1                   = "24V-1 Fault"
	FaultCVM24V1                = "CVM & 24V-1 Fault"
	Fault24V1And24V2            = "24V-1 & 24V-2 Fault"
	FaultCVM24V1And24V2         = "CVM & 24V-1 & 24V-2 Fault"
	FaultExternalWatchdog       = "External Watchdog Fault"
	FaultProgramCardAjar        = "Program Card Ajar Fault"
	FaultConflict               = "Conflict Fault"
	FaultRedFail                = "Red Fail Fault"
	FaultShortYellow            = "Clearance (Short Yellow) Fault"
	FaultSkippedYellow          = "Clearance (Skipped Yellow) Fault"
	FaultYellowRedClearance     = "Clearance (Yellow + Red) Fault"
	FaultPort1                  = "Port 1 Fault"
	FaultDiagnostic             = "Diagnostic Fault"
	FaultDualIndication         = "Dual Indication Fault"
	FaultFieldCheck             = "Field Check Fault"
	FaultType                   = "Type Fault"
	FaultLocalFlashActive       = "Local Flash Active"
	FaultConfigChange           = "Configuration Change Fault"
	FaultRecurrentPulseConflict = "Recurrent Pulse Conflict Fault"
	FaultRecurrentPulseRedFail  = "Recurrent Pulse Red Fail Fault"
	FaultRecurrentPulseDualInd  = "Recurrent Pulse Dual Indication Fault"
	Fault48Vdc                  = "48 Vdc Power Supply Fault"
	FaultFYAFlashRate           = "FYA Flash Rate Fault"
	FaultConflictFYA            = "Conflict Fault, FYA Yellow Trap"
	FaultRecurrentPulseFYA      = "Recurrent Pulse Conflict Fault, FYA Yellow Trap"
	FaultUndefined              = "Undefined Fault Type Error"
)

const (
	StatusRMSAD           = " (RMS-Engine A/D error)"
	StatusRMSComm         = " (RMS-Engine comm error)"
	StatusEEProm          = " (EEprom error)"
	StatusProgramCardPath = " (Program Card serial path)"
	StatusSwitchPath      = " (Switch serial path)"
	StatusLogicPath       = " (24V logic serial path)"
	StatusCardNotFound    = " (Program Card memory not found)"
	StatusCardDataError   = " (Program Card memory data error)"
	StatusCardMatchError  = " (Program Card memory match error)"
)

// Replace both GetMMUFaultText and getFault with a single function
func FaultTextAndStatus(fault int, faultStatus int, commVersion int) (faultString string, faultStatusString string, showFaultStatus bool) {
	showFaultStatus = true
	faultStatusString = ""

	switch fault {
	case 1:
		faultString = FaultCVM
		showFaultStatus = false
	case 2:
		faultString = Fault24V2
		showFaultStatus = false
	case 3:
		faultString = FaultCVM24V2
		showFaultStatus = false
	case 4:
		faultString = Fault24V1
		showFaultStatus = false
	case 5:
		faultString = FaultCVM24V1
		showFaultStatus = false
	case 6:
		faultString = Fault24V1And24V2
		showFaultStatus = false
	case 7:
		faultString = FaultCVM24V1And24V2
		showFaultStatus = false
	case 8:
		faultString = FaultExternalWatchdog
		showFaultStatus = false
	case 24:
		faultString = FaultProgramCardAjar
		showFaultStatus = false
	case 32:
		faultString = FaultConflict
	case 40:
		faultString = FaultRedFail
	case 48:
		faultString = FaultShortYellow
	case 56:
		faultString = FaultSkippedYellow
	case 64:
		faultString = FaultYellowRedClearance
	case 72:
		faultString = FaultPort1
		showFaultStatus = false
	case 80:
		faultString = FaultDiagnostic
		switch faultStatus {
		case 33:
			faultString += StatusRMSAD
		case 40:
			faultString += StatusRMSComm
		case 50:
			faultString += StatusEEProm
		case 200:
			faultString += StatusProgramCardPath
		case 29:
			faultString += StatusSwitchPath
		case 25:
			faultString += StatusLogicPath
		case 1:
			faultString += StatusCardNotFound
		case 2:
			faultString += StatusCardDataError
		case 3:
			faultString += StatusCardMatchError
		}
		showFaultStatus = false
	case 88:
		faultString = FaultDualIndication
	case 96:
		faultString = FaultFieldCheck
	case 104:
		faultString = FaultType
		showFaultStatus = false
	case 112:
		faultString = FaultLocalFlashActive
		showFaultStatus = false
	case 120:
		faultString = FaultConfigChange
		if commVersion > 0x32 {
			faultString += fmt.Sprintf(" (%d)", faultStatus)
		}
		showFaultStatus = false
	case 136:
		faultString = FaultRecurrentPulseConflict
	case 144:
		faultString = FaultRecurrentPulseRedFail
	case 152:
		faultString = FaultRecurrentPulseDualInd
	case 160:
		faultString = Fault48Vdc
	case 168:
		faultString = FaultFYAFlashRate
	case 176:
		faultString = FaultConflictFYA
	case 184:
		faultString = FaultRecurrentPulseFYA
	default:
		faultString = FaultUndefined
	}
	// Add logic for faultStatusString as in getFault
	if fault == 80 {
		switch faultStatus {
		case 200:
			faultStatusString = StatusProgramCardPath
		case 50:
			faultStatusString = StatusEEProm
		case 40:
			faultStatusString = StatusRMSComm
		case 33:
			faultStatusString = StatusRMSAD
		case 29:
			faultStatusString = StatusSwitchPath
		case 25:
			faultStatusString = StatusLogicPath
		case 1:
			faultStatusString = StatusCardNotFound
		case 2:
			faultStatusString = StatusCardDataError
		case 3:
			faultStatusString = StatusCardMatchError
		}
	}
	if fault == 120 && commVersion > 0x32 {
		faultStatusString = fmt.Sprintf("Check Value = %d", faultStatus)
	}
	return
}
