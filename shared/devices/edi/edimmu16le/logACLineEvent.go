package edimmu16le

import (
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const LogACLineEventRecordSize = 8

/*
===============================================================================
Byte Message Layout (LogACLineEvent)
===============================================================================

+-------------------------------+
|        Header (7 bytes)       |
+-------------------------------+
|   Number of records (1 byte)  |
+-------------------------------+
|   Log AC Line Event Records   |
|   (8 bytes per record)        |
+-------------------------------+
|   Checksum (1 byte)           |
+-------------------------------+

Header and number of records (8 bytes):
  [0] Message type identifier
  [1] Device address
  [2] Command code
  [3] Response status
  [4] Reserved byte
  [5] Reserved byte
  [6] Reserved byte
  [7] Number of records (0-255)

Log AC Line Event Record (8 bytes per record):
  [0] Event Type (used as eventType)
  [1] Line Voltage (used as linevoltage)
  [2] Seconds (BCD, passed as sec)
  [3] Minutes (BCD, passed as min)
  [4] Hours (BCD, passed as hr)
  [5] Day of Month (BCD, passed as day)
  [6] Month (BCD, passed as month)
  [7] Year (BCD, passed as yr)
*/

func (device EDIMMU216LE) LogACLineEvent(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogACLineEventRecords, err error) {
	if len(byteMsg) < 7 {
		return nil, fmt.Errorf("%w byte length less then 7", helper.ErrMsgByteLen)
	}
	numberOfRecords := int(byteMsg[HeaderLength])
	length := HeaderLength + RecordCountLength + (numberOfRecords * LogACLineEventRecordSize) + ChecksumLength

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	allRecords = &helper.LogACLineEventRecords{
		DeviceModel: header.Model.String(),
		VoltageType: func() int64 {
			if header.MainsDC {
				return 2
			}
			return 1
		}(),
		Records:    make([]helper.LogACLineEventRecord, numberOfRecords),
		RawMessage: byteMsg,
	}

	for i := range numberOfRecords {
		var record helper.LogACLineEventRecord
		offset := HeaderLength + RecordCountLength + i*LogACLineEventRecordSize
		eventType := byteMsg[offset+0]
		linevoltage := byteMsg[offset+1]

		record.EventType = helper.GetACLineEventType(helper.MonitorModel(header.Model), header.MainsDC, int64(linevoltage), header.PowerDownLevel, eventType)
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(byteMsg[offset+6], byteMsg[offset+5], byteMsg[offset+7], byteMsg[offset+4], byteMsg[offset+3], byteMsg[offset+2], httpHeader.GatewayTimezone)
		if err != nil {
			return nil, err
		}
		if int64(linevoltage) < header.BlackoutLevel {
			record.LineVoltageRms = 0
		} else {
			record.LineVoltageRms = int64(linevoltage)
		}

		allRecords.Records[i] = record
	}

	return allRecords, nil
}
