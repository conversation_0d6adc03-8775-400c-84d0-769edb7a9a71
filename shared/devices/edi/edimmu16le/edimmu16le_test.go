package edimmu16le

import (
	"testing"

	"synapse-its.com/shared/devices/edi/helper"
)

// computeChecksum returns the 1's-complement of the low byte of the sum of all but the last byte.
func computeChecksum(msg []byte) byte {
	var sum byte
	// sum all of the data (all but the last byte)
	for _, b := range msg[:len(msg)-1] {
		sum += b
	}
	// one's-complement so that sum(data)+checksum == 0xFF
	return ^sum
}

// brute-force a valid checksum by trying all possible last-byte values
func computeValidChecksum(t *testing.T, msg []byte) {
	for b := 0; b < 256; b++ {
		msg[len(msg)-1] = byte(b)
		if err := helper.ValidateChecksum(msg); err == nil {
			return
		}
	}
	t.<PERSON><PERSON>("unable to find a valid checksum for test message")
}

func TestFaultTextAndStatus(t *testing.T) {
	t.<PERSON>()
	tests := []struct {
		name        string
		fault       int
		faultStatus int
		commVersion int
		wantString  string
		wantStatus  string
		wantShow    bool
	}{
		// All cases from both previous tests, merged and deduped
		{"CVM", 1, 0, 0, FaultCVM, "", false},
		{"24V-2", 2, 0, 0, Fault24V2, "", false},
		{"CVM&24V-2", 3, 0, 0, FaultCVM24V2, "", false},
		{"24V-1", 4, 0, 0, Fault24V1, "", false},
		{"CVM&24V-1", 5, 0, 0, FaultCVM24V1, "", false},
		{"24V1&24V2", 6, 0, 0, Fault24V1And24V2, "", false},
		{"CVM&24V1&24V2", 7, 0, 0, FaultCVM24V1And24V2, "", false},
		{"ExternalWD", 8, 0, 0, FaultExternalWatchdog, "", false},
		{"ProgramCardAjar", 24, 0, 0, FaultProgramCardAjar, "", false},
		{"Conflict", 32, 0, 0, FaultConflict, "", true},
		{"RedFail", 40, 0, 0, FaultRedFail, "", true},
		{"ShortYellow", 48, 0, 0, FaultShortYellow, "", true},
		{"SkippedYellow", 56, 0, 0, FaultSkippedYellow, "", true},
		{"YellowRedClearance", 64, 0, 0, FaultYellowRedClearance, "", true},
		{"Port1", 72, 0, 0, FaultPort1, "", false},
		{"Diag33", 80, 33, 0, FaultDiagnostic + StatusRMSAD, StatusRMSAD, false},
		{"Diag40", 80, 40, 0, FaultDiagnostic + StatusRMSComm, StatusRMSComm, false},
		{"Diag50", 80, 50, 0, FaultDiagnostic + StatusEEProm, StatusEEProm, false},
		{"Diag200", 80, 200, 0, FaultDiagnostic + StatusProgramCardPath, StatusProgramCardPath, false},
		{"Diag29", 80, 29, 0, FaultDiagnostic + StatusSwitchPath, StatusSwitchPath, false},
		{"Diag25", 80, 25, 0, FaultDiagnostic + StatusLogicPath, StatusLogicPath, false},
		{"Diag1", 80, 1, 0, FaultDiagnostic + StatusCardNotFound, StatusCardNotFound, false},
		{"Diag2", 80, 2, 0, FaultDiagnostic + StatusCardDataError, StatusCardDataError, false},
		{"Diag3", 80, 3, 0, FaultDiagnostic + StatusCardMatchError, StatusCardMatchError, false},
		{"DiagDef", 80, 999, 0, FaultDiagnostic, "", false},
		{"DualInd", 88, 0, 0, FaultDualIndication, "", true},
		{"FieldChk", 96, 0, 0, FaultFieldCheck, "", true},
		{"TypeChg", 104, 0, 0, FaultType, "", false},
		{"LocalFlash", 112, 0, 0, FaultLocalFlashActive, "", false},
		{"ConfigLowVer", 120, 1234, 0x32, FaultConfigChange, "", false},
		{"ConfigHighVer", 120, 42, 0x33, FaultConfigChange + " (42)", "Check Value = 42", false},
		{"RPConf", 136, 0, 0, FaultRecurrentPulseConflict, "", true},
		{"RPRed", 144, 0, 0, FaultRecurrentPulseRedFail, "", true},
		{"RPDual", 152, 0, 0, FaultRecurrentPulseDualInd, "", true},
		{"48Vdc", 160, 0, 0, Fault48Vdc, "", true},
		{"FYAFlash", 168, 0, 0, FaultFYAFlashRate, "", true},
		{"FYA22", 176, 0, 0, FaultConflictFYA, "", true},
		{"FYA23", 184, 0, 0, FaultRecurrentPulseFYA, "", true},
		{"Undefined", 0xFF, 0, 0, FaultUndefined, "", true},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			gotStr, gotStatus, gotShow := FaultTextAndStatus(tc.fault, tc.faultStatus, tc.commVersion)
			if gotStr != tc.wantString || gotStatus != tc.wantStatus || gotShow != tc.wantShow {
				t.Errorf("FaultTextAndStatus(%d,%d,commVer=%#x) = (%q,%q,%v), want (%q,%q,%v)",
					tc.fault, tc.faultStatus, tc.commVersion,
					gotStr, gotStatus, gotShow, tc.wantString, tc.wantStatus, tc.wantShow)
			}
		})
	}
}
