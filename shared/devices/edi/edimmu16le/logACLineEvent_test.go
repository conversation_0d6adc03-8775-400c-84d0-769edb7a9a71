package edimmu16le

import (
	"errors"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// buildMsg builds a correctly-sized LogACLineEvent payload:
//
//	HeaderLength=7, count byte at 7, then N×8-byte records, then checksum.
func buildMsgLogACLineEvent(nRecs int, recs [][]byte) []byte {
	totalLen := HeaderLength + RecordCountLength + nRecs*LogACLineEventRecordSize + ChecksumLength
	msg := make([]byte, totalLen)

	// record count at offset 7
	msg[HeaderLength] = byte(nRecs)

	// copy in each 8-byte record, forcing first byte = 0x41
	start := HeaderLength + RecordCountLength
	for _, r := range recs {
		if len(r) != LogACLineEventRecordSize {
			panic("each record slice must be exactly 8 bytes")
		}
		r[0] = 0x41
		copy(msg[start:], r)
		start += LogACLineEventRecordSize
	}

	// now compute and append the one's-complement checksum
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func TestLogACLineEvent(t *testing.T) {
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	base := &helper.HeaderRecord{
		Model:          helper.Mmu16le,
		MainsDC:        false,
		PowerDownLevel: 50,
		BlackoutLevel:  20,
	}

	makeRec := func(evType, lv, sec, min, hr, day, mon, yr byte) []byte {
		return []byte{evType, lv, sec, min, hr, day, mon, yr}
	}
	utcTime, _ := time.LoadLocation("UTC")

	type tc struct {
		name          string
		header        *helper.HeaderRecord
		recs          [][]byte
		corrupt       func(msg []byte) // if non-nil, applied after build
		wantErr       error
		wantVoltType  int64
		wantLineRms   int64
		wantEventType string
		wantDate      time.Time
	}
	tests := []tc{
		{
			name:         "Above blackout, AC mode",
			header:       base,
			recs:         [][]byte{makeRec(1, 25, 0x12, 0x34, 0x1, 0x2, 0x3, 0x21)},
			wantErr:      nil,
			wantVoltType: 1,
			wantLineRms:  25,
			wantEventType: helper.GetACLineEventType(
				base.Model, base.MainsDC, 25, base.PowerDownLevel, 0x41,
			),
			wantDate: time.Date(int(2021), time.Month(3), int(2), int(1), int(34), int(12), 0, utcTime),
		},
		{
			name: "Below blackout, DC mode",
			header: func() *helper.HeaderRecord {
				h := *base
				h.MainsDC = true
				h.BlackoutLevel = 30
				return &h
			}(),
			recs:         [][]byte{makeRec(2, 20, 0x5, 0x15, 0x10, 0x6, 0x7, 0x22)},
			wantErr:      nil,
			wantVoltType: 2,
			wantLineRms:  0, // 20 < blackout 30
			wantEventType: helper.GetACLineEventType(
				base.Model, true, 20, base.PowerDownLevel, 0x41,
			),
			wantDate: time.Date(int(2022), time.Month(7), int(6), int(10), int(15), int(5), 0, utcTime),
		},

		// ---- error cases ----
		{
			name:    "BadLength",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x5, 0x15, 0x10, 0x6, 0x7, 0x22)},
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:   "BadChecksum",
			header: base,
			recs:   [][]byte{makeRec(1, 25, 0x5, 0x15, 0x10, 0x6, 0x7, 0x22)},
			corrupt: func(msg []byte) {
				// flip the checksum byte
				msg[len(msg)-1] ^= 0xFF
			},
			wantErr: helper.ErrMsgByteChecksum,
		},
		{
			name:    "TooShort",
			header:  base,
			recs:    nil, // ignored
			wantErr: helper.ErrMsgByteLen,
		},

		{
			name:    "InvalidMonthBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x1A, 0x28, 0x05, 0x16, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name:    "InvalidDayBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x02, 0x05, 0x55, 0x05, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name:    "InvalidHourBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x02, 0x28, 0x25, 0x05, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name:    "InvalidMinBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x14, 0x77, 0x12, 0x05, 0x07, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name:    "InvalidSecBCD",
			header:  base,
			recs:    [][]byte{makeRec(1, 25, 0x80, 0x02, 0x12, 0x05, 0x05, 0x45)},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			var msg []byte
			switch tc.name {
			case "TooShort":
				msg = make([]byte, 6)
			case "BadLength":
				msg = buildMsgLogACLineEvent(len(tc.recs), tc.recs)
				msg = msg[:len(msg)-1]
			default:
				msg = buildMsgLogACLineEvent(len(tc.recs), tc.recs)
			}

			if tc.corrupt != nil {
				tc.corrupt(msg)
			}
			out, err := dev.LogACLineEvent(httpHdr, msg, tc.header)

			if tc.wantErr != nil {
				if err == nil {
					t.Fatalf("expected error %v, got nil", tc.wantErr)
				}
				if !errors.Is(err, tc.wantErr) {
					t.Fatalf("error = %v; want %v", err, tc.wantErr)
				}
				return
			}

			// success path
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if out.DeviceModel != base.Model.String() {
				t.Errorf("DeviceModel = %q; want %q", out.DeviceModel, base.Model.String())
			}
			if out.VoltageType != tc.wantVoltType {
				t.Errorf("VoltageType = %d; want %d", out.VoltageType, tc.wantVoltType)
			}
			if len(out.Records) != 1 {
				t.Fatalf("got %d records; want 1", len(out.Records))
			}
			rec := out.Records[0]
			if rec.LineVoltageRms != tc.wantLineRms {
				t.Errorf("LineVoltageRms = %d; want %d", rec.LineVoltageRms, tc.wantLineRms)
			}
			if rec.EventType != tc.wantEventType {
				t.Errorf("EventType = %v; want %v", rec.EventType, tc.wantEventType)
			}
			if !rec.DateTime.Equal(tc.wantDate) {
				t.Errorf("DateTime = %v; want %v", rec.DateTime, tc.wantDate)
			}
		})
	}
}

// Add test for LogACLineEvent additional cases
func TestLogACLineEvent_AdditionalCases(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	t.Run("MainsDC voltage type", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{MainsDC: true}
		recs := [][]byte{
			{1, 25, 0x45, 0x16, 0x05, 0x28, 0x02, 0x14}, // eventType=1, voltage=25, valid BCD date
		}
		msg := buildMsgLogACLineEvent(1, recs)

		result, err := dev.LogACLineEvent(hdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// MainsDC should set VoltageType to 2
		if result.VoltageType != 2 {
			t.Errorf("expected VoltageType=2 for MainsDC, got %d", result.VoltageType)
		}
	})

	t.Run("Voltage below blackout level", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{MainsDC: false, BlackoutLevel: 30}
		recs := [][]byte{
			{1, 20, 0x45, 0x16, 0x05, 0x28, 0x02, 0x14}, // voltage=20 < blackoutLevel=30, valid BCD date
		}
		msg := buildMsgLogACLineEvent(1, recs)

		result, err := dev.LogACLineEvent(hdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Voltage below blackout level should be set to 0
		if result.Records[0].LineVoltageRms != 0 {
			t.Errorf("expected LineVoltageRms=0 for voltage below blackout, got %d", result.Records[0].LineVoltageRms)
		}
	})
}
