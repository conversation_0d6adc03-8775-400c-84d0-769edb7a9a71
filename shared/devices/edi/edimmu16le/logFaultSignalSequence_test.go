package edimmu16le

import (
	"testing"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// build a minimal LogFaultSignalSequence message with N records and given faultType
func buildMsg(numRecords int, faultType byte) []byte {
	const headerLen = 7
	const traceLen = 12

	// total length = headerLen + numRecords*traceLen + 1(checksum)
	total := headerLen + 2 + numRecords*traceLen + 2
	msg := make([]byte, total)

	// byte 8 = faultType,  byte 9 = numberOfRecords
	msg[8] = faultType
	msg[9] = byte(numRecords)

	return msg
}

func TestLogFaultSignalSequence(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}
	// our parser only needs MaxChannels; CommVersion isn't used in this path
	headerDetail := &helper.HeaderRecord{MaxChannels: 2}

	tests := []struct {
		name          string
		build         func() []byte
		wantErr       bool
		wantFaultType string
	}{
		{
			name: "InvalidLength",
			build: func() []byte {
				return make([]byte, 5) // way too short
			},
			wantErr: true,
		},
		{
			name: "InvalidLength2",
			build: func() []byte {
				msg := make([]byte, 10) // too short
				msg[9] = 2
				return msg
			},
			wantErr: true,
		},
		{
			name: "BadChecksum",
			build: func() []byte {
				// correct length for 1 record, but leave checksum = 0
				msg := buildMsg(1, 1)
				// zero-out the whole trace
				for i := 10; i < len(msg)-1; i++ {
					msg[i] = 0
				}
				msg[len(msg)-1] = 0 // wrong checksum
				return msg
			},
			wantErr: true,
		},
		{
			name: "NormalTimestamp",
			build: func() []byte {
				msg := buildMsg(2, 1) // faultType=1 -> "CVM Fault"
				// set a "normal" timestamp of 100 (0x00,0x64) in the first two trace bytes
				msg[10] = 0x00
				msg[11] = 0x64
				// leave all other trace-status bytes at 0
				for i := 12; i < 10+12; i++ {
					msg[i] = 0
				}
				msg[23] = 0x00
				msg[24] = 0x64
				// leave all other trace-status bytes at 0
				for i := 25; i < 23+12; i++ {
					msg[i] = 0
				}
				computeValidChecksum(t, msg)
				return msg
			},
			wantErr:       false,
			wantFaultType: "CVM Fault",
		},
		{
			name: "RolloverTimestamp",
			build: func() []byte {
				msg := buildMsg(2, 1)
				// set timestamp = 0xFF,0xFF -> 65535 > 65530 so should be clamped
				msg[10] = 0xFF
				msg[11] = 0xFF
				for i := 12; i < 10+12; i++ {
					msg[i] = 0
				}
				msg[23] = 0xFF
				msg[24] = 0xFF
				// leave all other trace-status bytes at 0
				for i := 25; i < 23+12; i++ {
					msg[i] = 0
				}
				computeValidChecksum(t, msg)
				return msg
			},
			wantErr:       false,
			wantFaultType: "CVM Fault",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := tc.build()
			records, err := dev.LogFaultSignalSequence(hdr, msg, headerDetail)
			if tc.wantErr {
				if err == nil {
					t.Fatalf("expected error, got none")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			// we at least got a non-nil result with FaultType set
			if records.FaultType != tc.wantFaultType {
				t.Errorf("FaultType = %q; want %q", records.FaultType, tc.wantFaultType)
			}
			if len(records.Records) == 0 {
				t.Errorf("no trace records returned")
			}
		})
	}
}

// Add test for TraceRecordParser additional edge cases
func TestTraceRecordParser_AdditionalCases(t *testing.T) {
	t.Parallel()

	t.Run("Complex timestamp and channel status", func(t *testing.T) {
		t.Parallel()
		data := make([]byte, 12)
		// Set complex timestamp (bytes 0-1): 0x1234 = 4660
		data[0] = 0x12
		data[1] = 0x34

		// Set some channel statuses
		data[2] = 0x03 // green low: channels 0,1 on
		data[3] = 0x00 // green high
		data[4] = 0x0C // yellow low: channels 2,3 on
		data[5] = 0x00 // yellow high
		data[6] = 0x30 // red low: channels 4,5 on
		data[7] = 0x00 // red high
		data[8] = 0xC0 // walk low: channels 6,7 on
		data[9] = 0x00 // walk high

		data[10] = 0x00 // EE_SF_RE = false
		data[11] = 42   // AcVoltage = 42

		hdr := &helper.HeaderRecord{MaxChannels: 8}
		tb := traceRecordParser(data, hdr)

		if tb == nil {
			t.Fatal("expected non-nil TraceBuffer")
		}

		if tb.Timestamp != 4660 {
			t.Errorf("Timestamp = %d; want 4660", tb.Timestamp)
		}

		// Check specific channel statuses
		if !tb.Greens[0] || !tb.Greens[1] {
			t.Error("expected green channels 0,1 to be true")
		}
		if !tb.Yellows[2] || !tb.Yellows[3] {
			t.Error("expected yellow channels 2,3 to be true")
		}
		if !tb.Reds[4] || !tb.Reds[5] {
			t.Error("expected red channels 4,5 to be true")
		}
		if !tb.Walks[6] || !tb.Walks[7] {
			t.Error("expected walk channels 6,7 to be true")
		}

		if tb.AcVoltage != 42 {
			t.Errorf("AcVoltage = %d; want 42", tb.AcVoltage)
		}
	})
}

func TestTraceRecordParser_InvalidLength(t *testing.T) {
	t.Parallel()
	// too short -> should return nil
	if tb := traceRecordParser([]byte{0, 1, 2}, &helper.HeaderRecord{MaxChannels: 1}); tb != nil {
		t.Errorf("expected nil for invalid length, got %+v", tb)
	}
}

func TestTraceRecordParser_AllFalseAndEE_SF_RE_False(t *testing.T) {
	t.Parallel()
	// 12-byte slice all zero -> everything false, AcVoltage=0, EE_SF_RE=false
	data := make([]byte, 12)
	hdr := &helper.HeaderRecord{MaxChannels: 4}
	tb := traceRecordParser(data, hdr)
	if tb == nil {
		t.Fatal("expected non-nil TraceBuffer")
	}
	// Timestamp = 0
	if tb.Timestamp != 0 {
		t.Errorf("Timestamp = %d; want 0", tb.Timestamp)
	}
	// All channel slices should be len=4, all false
	for i, sl := range [][]bool{tb.Greens, tb.Yellows, tb.Reds, tb.Walks} {
		if len(sl) != 4 {
			t.Errorf("slice %d length = %d; want 4", i, len(sl))
			continue
		}
		for j, v := range sl {
			if v {
				t.Errorf("slice %d[%d] = true; want false", i, j)
			}
		}
	}
	if tb.EE_SF_RE {
		t.Error("EE_SF_RE = true; want false")
	}
	if tb.AcVoltage != 0 {
		t.Errorf("AcVoltage = %d; want 0", tb.AcVoltage)
	}
}

func TestTraceRecordParser_EE_SF_RE_True(t *testing.T) {
	t.Parallel()
	// build a minimal 12-byte packet
	data := make([]byte, 12)
	// set bit7 in byte[10]
	data[10] = 0x80
	// everything else can be zero
	hdr := &helper.HeaderRecord{MaxChannels: 1}

	tb := traceRecordParser(data, hdr)
	if tb == nil {
		t.Fatal("TraceBuffer should not be nil")
	}

	if !tb.EE_SF_RE {
		t.Errorf("EE_SF_RE = false; want true when bit7 of byte 10 is set")
	}
}
