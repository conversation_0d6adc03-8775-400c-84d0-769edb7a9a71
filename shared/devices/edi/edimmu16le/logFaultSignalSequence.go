package edimmu16le

import (
	"encoding/binary"
	"fmt"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	EventTypeLength   = 1
	FaultTypeLength   = 1
	TraceRecordLength = 12
	TraceMaxTimestamp = 65530

	FaultTypeOffset        = HeaderLength + EventTypeLength
	TraceRecordCountOffset = HeaderLength + EventTypeLength + FaultTypeLength
	TraceRecordsOffset     = HeaderLength + EventTypeLength + FaultTypeLength + RecordCountLength
)

/*
===============================================================================
Byte Message Layout (EDIMMU216LE)
===============================================================================

+-------------------------------+
|        Header (7 bytes)       |
+-------------------------------+
|   Event count (1 byte)        |
+-------------------------------+
|   Fault Type (1 byte)         |
+-------------------------------+
|   Trace record count (1 byte) |
+-------------------------------+
|   Trace Records               |
|   (12 bytes per record)       |
+-------------------------------+
|   Checksum (1 byte)           |
+-------------------------------+

Header (7 bytes):
  [0] Message type identifier
  [1] Device address
  [2] Command code
  [3] Response status
  [4] Reserved byte
  [5] Reserved byte
  [6] Reserved byte

Trace Records (12 bytes per record):
  [0-1]   Timestamp (big-endian)
  [2-3]   Green status (big-endian)
  [4-5]   Yellow status (big-endian)
  [6-7]   Red status (big-endian)
  [8-9]   Walk status (big-endian)
  [10]    EE_SF_RE (bit 7)
  [11]    AC voltage

*/

func (device EDIMMU216LE) LogFaultSignalSequence(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.FaultSignalSequenceRecords, err error) {
	if len(byteMsg) < TraceRecordsOffset {
		return nil, fmt.Errorf("%w byte length less then %d", helper.ErrMsgByteLen, TraceRecordsOffset)
	}
	numberOfRecords := int64(byteMsg[TraceRecordCountOffset])
	length := HeaderLength + EventTypeLength + FaultTypeLength + RecordCountLength + (numberOfRecords * TraceRecordLength) + ChecksumLength

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, int(length)) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	// skip past the header and parse the trace
	faultType := byteMsg[FaultTypeOffset]
	faultText, _, _ := FaultTextAndStatus(int(faultType), -1, int(helper.ConvertBCDStringToInt(header.CommVersion)))

	allRecords = &helper.FaultSignalSequenceRecords{
		DeviceModel: header.Model.String(),
		FaultType:   faultText,
		Records:     make([]helper.TraceBuffer, numberOfRecords),
		RawMessage:  byteMsg,
	}

	for idx := range numberOfRecords {
		offset := TraceRecordsOffset + idx*TraceRecordLength
		traceBuffer := byteMsg[offset : offset+TraceRecordLength]
		traceRecord := traceRecordParser(traceBuffer, header)
		// since we are reading the logs from the failed event, backwards, we must consider the log timestamp having rolled-over
		if traceRecord.Timestamp > TraceMaxTimestamp {
			traceRecord.Timestamp = TraceMaxTimestamp
		}
		allRecords.Records[idx] = *traceRecord
	}

	// adjust time stamps
	helper.NormalizeTimestamps(allRecords) // double-check this to make sure the data is modified in the structure
	allRecords = helper.Performcalcs(allRecords)

	return allRecords, nil
}

// parse the trace record bytes returning a hydrated trace record parser struct
// this functions takes in a 12 byte slice and deconstructs it into it's corresponding json message
func traceRecordParser(traceBytes []byte, headerDetail *helper.HeaderRecord) (record *helper.TraceBuffer) {
	// validate the length of the trace bytes
	if len(traceBytes) != TraceRecordLength {
		return nil
	}

	// parse the trace bytes
	timestamp := int64(binary.BigEndian.Uint16(traceBytes[0:2]))
	greenStatus := uint32(binary.LittleEndian.Uint16(traceBytes[2:4]))
	yellowStatus := uint32(binary.LittleEndian.Uint16(traceBytes[4:6]))
	redStatus := uint32(binary.LittleEndian.Uint16(traceBytes[6:8]))
	walkStatus := uint32(binary.LittleEndian.Uint16(traceBytes[8:10]))
	ee_sf_re := traceBytes[10]&0x80 > 0
	acVoltage := int(traceBytes[11])

	// return the trace record
	return &helper.TraceBuffer{
		Timestamp:      timestamp,
		Greens:         helper.ParseChannelStatus(greenStatus, int(headerDetail.MaxChannels)),
		Yellows:        helper.ParseChannelStatus(yellowStatus, int(headerDetail.MaxChannels)),
		Reds:           helper.ParseChannelStatus(redStatus, int(headerDetail.MaxChannels)),
		Walks:          helper.ParseChannelStatus(walkStatus, int(headerDetail.MaxChannels)),
		EE_SF_RE:       ee_sf_re,
		AcVoltage:      acVoltage,
		BufferRawBytes: traceBytes,
	}
}
