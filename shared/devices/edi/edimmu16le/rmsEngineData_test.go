package edimmu16le

import (
	"errors"
	"testing"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestRMSEngineData(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}                       // not used
	helperHdr := &helper.HeaderRecord{Model: helper.Mmu16le} // not used

	buildValid := func() []byte {
		msg := make([]byte, 20)
		// pick header bytes 0..7
		for i := 0; i < 8; i++ {
			msg[i] = byte(0x11 + i)
		}

		// filler at idx 8
		msg[8] = 0x99
		// compute version checksum at idx 9
		msg[9] = computeChecksum(msg[:10])

		// revision preamble at idx 10
		msg[10] = 0x00
		// copy versionHeader (idx1..6) into revisionHeader slots idx11..16
		copy(msg[11:17], msg[1:7])

		// filler at idx 18
		msg[18] = 0x77
		// compute revision checksum at idx 18 over msg[10..19]
		msg[19] = computeChecksum(msg[10:20])

		return msg
	}

	t.Run("success", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		r, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		if r.DeviceModel != helper.Mmu16le.String() {
			t.Errorf("DeviceModel = %q; want %q", r.DeviceModel, helper.Mmu16le.String())
		}
		// note: the code (quirk!) sets EngineVersion = byteMsg[8], EngineRevision = byteMsg[18]
		if r.EngineVersion != helper.ConvertByteToString(msg[8]) {
			t.Errorf("EngineVersion = %s; want %s", r.EngineVersion, helper.ConvertByteToString(msg[8]))
		}
		if r.EngineRevision != helper.ConvertByteToDecimalFormat(msg[18]) {
			t.Errorf("EngineRevision = %s; want %s", r.EngineRevision, helper.ConvertByteToDecimalFormat(msg[18]))
		}
	})

	t.Run("error: length", func(t *testing.T) {
		t.Parallel()
		_, err := dev.RMSEngineData(hdr, []byte{0x00}, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteLen", err)
		}
	})

	t.Run("error: bad version checksum", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		msg[8] ^= 0xFF // break version checksum
		_, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteChecksum", err)
		}
	})

	t.Run("error: bad revision checksum", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		msg[18] ^= 0xFF // break revision checksum
		_, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteChecksum", err)
		}
	})

	t.Run("error: header mismatch", func(t *testing.T) {
		t.Parallel()
		msg := buildValid()
		// tweak one byte of the revision header so versionHeader != revisionHeader
		msg[11] ^= 0xFF
		// recalc revision checksum
		msg[19] = computeChecksum(msg[10:20])
		_, err := dev.RMSEngineData(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrRMSEngineDataHeaderMismatch) {
			t.Errorf("err = %v; want helper.ErrRMSEngineDataHeaderMismatch", err)
		}
	})
}
