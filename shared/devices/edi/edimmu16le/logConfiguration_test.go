package edimmu16le

import (
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func buildBaseConfigMsg(recCount int, modify func(rec []byte)) []byte {
	const (
		hdrLen  = 7
		recSize = 64
	)
	length := hdrLen + recCount*recSize + 2
	msg := make([]byte, length)
	// set record count
	msg[7] = byte(recCount)
	// prepare record area
	start := hdrLen + 1
	for i := 0; i < recCount; i++ {
		rec := msg[start : start+recSize]
		// zero init, then apply modifier
		if modify != nil {
			modify(rec)
		}
		start += recSize
	}
	// append checksum
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func logConfigurationBaseHeader() *pubsubdata.HeaderDetails {
	return &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
}

func logConfigurationBaseHelperHeader() *helper.HeaderRecord {
	return &helper.HeaderRecord{MaxChannels: 16, FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29)}
}

func logConfigurationBuildValidMessage(recCount int) []byte {
	// total length = 7 + recCount*64 + 1
	length := HeaderLength + RecordCountLength + recCount*LogConfigurationRecordSize + ChecksumLength
	msg := make([]byte, length)

	// numberOfRecords
	msg[HeaderLength] = byte(recCount)

	// one record only: fill at offset = HeaderLength+1
	start := HeaderLength + RecordCountLength

	// 1) conflict maps: make all zero to force permissives = ["2","3",...]
	for off := 0; off < 30; off += 2 {
		// two-byte words at start+off, start+off+1
		msg[start+off] = 0x00
		msg[start+off+1] = 0x00
	}

	// 2) mycd/myrcd = 0x0000 to test true; fieldCheckG/Y/R, dualEnableYR/GR/GY, redFailEnable = 0xFFFF to test true
	offs := []int{30, 32, 34, 36, 38, 40, 42, 44, 46}
	for _, off := range offs {
		// words at off, off+1
		v := uint16(0xFFFF)
		msg[start+off] = byte(v & 0xff)
		msg[start+off+1] = byte(v >> 8)
	}
	// but mycd/myrcd are at off=30,32: override to zero
	msg[start+30] = 0
	msg[start+31] = 0
	msg[start+32] = 0
	msg[start+33] = 0

	// 3) vmMinFlash, options1, options2, fyaEnable1, fyaEnable2, selectJumpers1
	msg[start+48] = 0x2C // 0010_1100
	msg[start+49] = 0xF3 // 1111_0011
	msg[start+50] = 0x9A // 1001_1010
	msg[start+51] = 0x00 // low nibble => Mode A branch
	msg[start+52] = 0x10 // trap_mode only => test FYA trap bit
	msg[start+53] = 0x07 // 0000_0111 => legacy bits for legacy test

	// 4) BCD timestamp: sec, min, hr, day, mon, yr
	msg[start+55] = 0x00 // seconds
	msg[start+56] = 0x00 // minutes
	msg[start+57] = 0x00 // hours
	msg[start+58] = 0x02 // day = 2
	msg[start+59] = 0x01 // month = Jan
	msg[start+60] = 0x21 // year = '21 => 2021

	// 5) configChangeSource
	msg[start+61] = 3 // Program Card Entry

	// 6) crc low/high at offsets 62/63
	msg[start+62] = 0x0A
	msg[start+63] = 0x00

	// now checksum of whole message
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func TestLogConfiguration_AllBranches(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}

	hdrModern := logConfigurationBaseHeader()
	// helperHdr := logConfigurationBaseHelperHeader()
	// set CommVersion>=0x38, FirmwareRevision>0x73, FirmwareVersion>0x28 for modern-FYA high-Ver branches
	hdrModern.GatewayTimezone = "UTC"
	hh := &helper.HeaderRecord{
		CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
		FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74),
		FirmwareVersion:  helper.ConvertByteToString(0x29),
		MaxChannels:      16,
		Model:            helper.Mmu16le,
	}

	msg := logConfigurationBuildValidMessage(1)
	rec, err := dev.LogConfiguration(hdrModern, msg, hh)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	if rec.DeviceModel != hh.Model.String() {
		t.Errorf("DeviceModel = %q; want %q", rec.DeviceModel, hh.Model.String())
	}
	if len(rec.Record) != 1 {
		t.Fatalf("got %d records; want 1", len(rec.Record))
	}
	c := rec.Record[0]

	// DateTime
	wantDT := time.Date(2021, 1, 2, 0, 0, 0, 0, time.UTC)
	if !c.DateTime.Equal(wantDT) {
		t.Errorf("DateTime = %v; want %v", c.DateTime, wantDT)
	}

	// permissives length
	if len(c.Ch01Permissives) != 15 {
		t.Errorf("Ch01Permissives len = %d; want 15", len(c.Ch01Permissives))
	}

	// mycd=0 => all true; myrcd=0 => all true
	for i, v := range c.MinimumYellowClearanceEnable {
		if !v {
			t.Errorf("MinYellowClear[%d]=false; want true", i)
		}
	}
	for i, v := range c.MinimumYellowRedClearanceEnable {
		if !v {
			t.Errorf("MinYellowRedClear[%d]=false; want true", i)
		}
	}

	// field checks & dual & redFail all true
	for i := 0; i < 16; i++ {
		if !c.FieldCheckEnableRed[i] ||
			!c.FieldCheckEnableYellow[i] ||
			!c.FieldCheckEnableGreen[i] ||
			!c.GreenRedDualEnable[i] ||
			!c.YellowRedDualEnable[i] ||
			!c.GreenYellowDualEnable[i] ||
			!c.RedFailEnable[i] {
			t.Errorf("bit %d: expected all true flags", i)
		}
	}

	// options1 flags
	if c.RecurrentPulse {
		t.Error("RecurrentPulse = true; want false")
	}
	if !c.WatchdogEnableSwitch {
		t.Error("WatchdogEnableSwitch = false; want true")
	}
	if !c.WalkEnableTs1 {
		t.Error("WalkEnableTs1 = false; want true")
	}
	if c.X24VIiInputThreshold != "12 Vdc" {
		t.Errorf("X24VIiInputThreshold = %q; want 12 Vdc", c.X24VIiInputThreshold)
	}
	if !c.LogCvmFaults {
		t.Error("LogCvmFaults = false; want true")
	}
	if !c.ProgramCardMemory {
		t.Error("ProgramCardMemory = false; want true")
	}
	if !c.LEDguardThresholds {
		t.Error("LEDguardThresholds = false; want true")
	}

	// options2 flags
	if !c.ForceType_16Mode {
		t.Error("ForceType_16Mode = false; want true")
	}
	if c.Type_12WithSdlcMode {
		t.Error("Type_12WithSdlcMode = true; want false")
	}
	if c.VmCvm_24V_3XdayLatch {
		t.Error("VmCvm_24V_3XdayLatch = true; want false")
	}

	// vmMinFlash => "13 seconds"
	if c.MinimumFlashTime != "13 seconds" {
		t.Errorf("MinimumFlashTime = %q; want 13 seconds", c.MinimumFlashTime)
	}
	if !c.CvmLatchEnable {
		t.Error("CvmLatchEnable = false; want true")
	}
	if c.X24VLatchEnable {
		t.Error("X24VLatchEnable = true; want false")
	}

	// selectJumpers1 => bits 0,1,2 => all true => Inhibit & Port & TypeMode="16"
	if !c.X24VoltInhibit {
		t.Error("X24VoltInhibit = false; want true")
	}
	if !c.Port_1Disable {
		t.Error("Port_1Disable = false; want true")
	}
	if c.TypeMode != "16" {
		t.Errorf("TypeMode = %q; want 16", c.TypeMode)
	}

	// modern FYA, no remap => FlashingYellowArrows="Mode A, Channel Pairs: <none>"
	if got := c.FlashingYellowArrows[0]; got != "Mode A, Channel Pairs: <none>" {
		t.Errorf("FlashingYellowArrows = %q; want Mode A, Channel Pairs: <none>", got)
	}
	if c.FyaRedAndYellowEnable != "Channels: <none>" {
		t.Errorf("FyaRedAndYellowEnable = %q; want Channels: <none>", c.FyaRedAndYellowEnable)
	}
	if !c.FyaYellowTrapDetection {
		t.Error("FyaYellowTrapDetection = false; want true")
	}
	if c.FyaFlashRateDetection {
		t.Error("FyaFlashRateDetection = true; want false")
	}

	// PLT5 suppression: options2&0x8 => ball suppression
	if c.Pplt5Suppression != "PPLT5 Ball Suppression = " {
		t.Errorf("Pplt5Suppression = %q; want PPLT5 Ball Suppression = ", c.Pplt5Suppression)
	}

	// source & CRC
	if c.ChangeSource != "Program Card Entry" {
		t.Errorf("ChangeSource = %q; want Program Card Entry", c.ChangeSource)
	}
	if c.CheckValue != "10" {
		t.Errorf("CheckValue = %q; want 10", c.CheckValue)
	}
}

func TestLogConfiguration_Errors(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := logConfigurationBaseHeader()
	hh := logConfigurationBaseHelperHeader()

	t.Run("short length", func(t *testing.T) {
		t.Parallel()
		_, err := dev.LogConfiguration(hdr, make([]byte, 10), hh)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("err = %v; want helper.ErrMsgByteLen", err)
		}
	})

	t.Run("bad checksum", func(t *testing.T) {
		t.Parallel()
		msg := logConfigurationBuildValidMessage(1)
		// break checksum
		msg[len(msg)-1] ^= 0xFF
		_, err := dev.LogConfiguration(hdr, msg, hh)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want helper.ErrMsgByteChecksum", err)
		}
	})
}

func TestLogConfiguration_LegacyFYA(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := logConfigurationBaseHeader()
	hh := &helper.HeaderRecord{
		CommVersion:     helper.ConvertByteToDecimalFormat(0x30), // <0x38 => legacy path
		FirmwareVersion: helper.ConvertByteToDecimalFormat(0x20),
		MaxChannels:     16,
	}

	msg := logConfigurationBuildValidMessage(1)
	// clear selectJumpers1 bits so FlashingYellowArrows="<none>"
	msg[7+1+53] = 0x00
	msg[len(msg)-1] = computeChecksum(msg)

	rec, err := dev.LogConfiguration(hdr, msg, hh)
	if err != nil {
		t.Fatalf("unexpected error: %v", err)
	}
	c := rec.Record[0]
	if got := c.FlashingYellowArrows[0]; got != "<none>" {
		t.Errorf("legacy FlashingYellowArrows = %q; want <none>", got)
	}
}

func TestLogConfiguration_BCDErrors(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	hh := &helper.HeaderRecord{MaxChannels: 16, FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29), FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), CommVersion: helper.ConvertByteToDecimalFormat(0x38)}

	tests := []struct {
		name    string
		modify  func(rec []byte)
		wantErr error
	}{
		{
			name: "InvalidMonthBCD",
			modify: func(rec []byte) {
				// month at offset 59 -> invalid BCD 0x1A
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x00, 0x02, 0x1A, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name: "InvalidDayBCD",
			modify: func(rec []byte) {
				// day at offset 58 -> invalid BCD 0x32 (>31)
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x00, 0x32, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name: "InvalidHourBCD",
			modify: func(rec []byte) {
				// hour at offset 57 -> invalid BCD 0x25 (>23)
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x25, 0x02, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name: "InvalidMinBCD",
			modify: func(rec []byte) {
				// minute at offset 56 -> invalid BCD 0x61
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x61, 0x00, 0x02, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name: "InvalidSecBCD",
			modify: func(rec []byte) {
				// second at offset 55 -> invalid BCD 0x61
				rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x61, 0x00, 0x00, 0x02, 0x01, 0x21
			},
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			msg := buildBaseConfigMsg(1, tc.modify)
			// invoke
			_, err := dev.LogConfiguration(hdr, msg, hh)
			if !errors.Is(err, tc.wantErr) {
				t.Fatalf("%s: expected %v, got %v", tc.name, tc.wantErr, err)
			}
		})
	}
}

func TestLogConfiguration_AdditionalBranches(t *testing.T) {
	dev := EDIMMU216LE{}
	baseHdr := logConfigurationBaseHeader()

	type sub struct {
		name       string
		modifyMsg  func([]byte)
		modifyHH   func(*helper.HeaderRecord)
		assertFunc func(*testing.T, helper.ConfigurationChangeLogRecord)
	}

	subtests := []sub{
		{
			name: "X24VThreshold_24Vdc",
			// clear bit0 of options1 (offset 49)
			modifyMsg: func(msg []byte) {
				msg[7+1+49] &^= 0x1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				if c.X24VIiInputThreshold != "24 Vdc" {
					t.Errorf("Threshold = %q; want 24 Vdc", c.X24VIiInputThreshold)
				}
			},
		},
		{
			name: "FYA_Remap_ModeE_And_Pairs",
			// turn on remap bit in fyaEnable2 (offset 52) and set fyaEnable1 to 0xD1 to hit Mode E + low-nibble=1
			modifyMsg: func(msg []byte) {
				msg[7+1+51] = 0xD1 // fyaEnable1
				msg[7+1+52] = 0x40 // fyaEnable2 remap bit
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				got := c.FlashingYellowArrows[0]
				if got != "Mode E, Channel Pairs: 1-9" {
					t.Errorf("FYA remap = %q; want Mode E, Channel Pairs: 1-9", got)
				}
			},
		},
		{
			name: "Pplt5_ArrowSuppressionElse",
			// clear bit3 of options2 (offset 50) so we go into Arrow Suppression = branch
			modifyMsg: func(msg []byte) {
				msg[7+1+50] = 0x03
				msg[len(msg)-1] = computeChecksum(msg)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				if c.Pplt5Suppression != "PPLT5 Arrow Suppression = <none>" {
					t.Errorf("Pplt5Suppression = %q; want PPLT5 Arrow Suppression = <none>", c.Pplt5Suppression)
				}
			},
		},
		{
			name: "Pplt5_Suppression_Jumpers_12Mode",
			// legacy-FYA path cleanup: force selectJumpers1 bits for <0x38 CommVersion>
			modifyMsg: func(msg []byte) {
				msg[7+1+53] = 0xE1 // bits 1-4 => 0001_1110
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x37)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				// 0x1E => suppress 2,3,4,5 pairs => "3-10","5-11","7-12" in legacy 12-mode
				if !strings.Contains(c.Pplt5Suppression, "3-10") ||
					!strings.Contains(c.Pplt5Suppression, "5-11") ||
					!strings.Contains(c.Pplt5Suppression, "7-12") {
					t.Errorf("legacy Pplt5Suppression = %q; want channels 3-10,5-11,7-12", c.Pplt5Suppression)
				}
			},
		},
		{
			name: "ConfigChangeSource_Cases",
			modifyMsg: func(msg []byte) {
				// we'll iterate sources 1,2,4, and 0
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				// no-op, tested inside loop below
			},
		},
		{
			name: "FYA_Remap_ModeF_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 ← fya_msb_hi + fya_srcY + fya_fYa_lo + fya_ry_lo = 0x20+0x40+0x80+0x10 = 0xF0
				msg[7+1+51] = 0xF0 // fyaEnable1
				msg[7+1+52] = 0x40 // fyaEnable2 remap bit
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode F, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Remap_ModeI_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 ← fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50
				msg[7+1+51] = 0x50
				msg[7+1+52] = 0x40 // remap
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode I, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Remap_ModeJ_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 ← fya_msb_hi + fya_srcY + fya_ry_lo = 0x20+0x40+0x10 = 0x70
				msg[7+1+51] = 0x70
				msg[7+1+52] = 0x40
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode J, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Remap_ModeError_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 = 0x00 (no valid high-nibble), still remap bit set
				msg[7+1+51] = 0x00
				msg[7+1+52] = 0x40
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode <error>, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeB_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi = 0x20
				msg[7+1+51] = 0x20
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeC_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_fYa_lo + fya_ry_lo = 0x80 + 0x10 = 0x90
				msg[7+1+51] = 0x90
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode C, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeD_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi + fya_fYa_lo + fya_ry_lo = 0x20+0x80+0x10 = 0xB0
				msg[7+1+51] = 0xB0
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode D, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeG_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50
				msg[7+1+51] = 0x50
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode G, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeH_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi + fya_srcY + fya_ry_lo = 0x20+0x40+0x10 = 0x70
				msg[7+1+51] = 0x70
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode H, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeK_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_ry_lo = 0x10
				msg[7+1+51] = 0x10
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode K, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeL_NoPairs",
			modifyMsg: func(msg []byte) {
				// fya_msb_hi + fya_ry_lo = 0x20+0x10 = 0x30
				msg[7+1+51] = 0x30
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode L, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Default_ModeError",
			modifyMsg: func(msg []byte) {
				// high-nibble = 0xC0 triggers the default case, low-nibble=0 so no channel pairs
				msg[7+1+51] = 0xC0 // fyaEnable1
				// leave fyaEnable2 at 0 (no remap)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // enable the new-style FYA logic
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode <error>, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_Default_ModeError_LegacyFYA",
			modifyMsg: func(msg []byte) {
				// high-nibble = 0xC0 triggers the default case, low-nibble=0 so no channel pairs
				msg[7+1+51] = 0xC0 // fyaEnable1
				// leave fyaEnable2 at 0 (no remap)
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // enable new-style FYA logic
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode <error>, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_ModeA_NoPairs",
			modifyMsg: func(msg []byte) {
				// fyaEnable1 = 0x00 for Mode A (high nibble = 0)
				msg[7+1+51] = 0x00
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode A, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_2",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_3",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_4",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x74)
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_5",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x73) // ≤0x73 -> else-branch
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FYA_FirmwareVer_Test_6",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38)      // so we take the new-style FYA logic
				hh.FirmwareRevision = helper.ConvertByteToDecimalFormat(0x73) // ≤0x73 so we hit the "else" and get Channels: <none>
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_1",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_2",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_3",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "Legacy_FYA_Test_4",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // >= 0x38 to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_1",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_2",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_3",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
		{
			name: "FirmwareVersion_Test_4",
			modifyMsg: func(msg []byte) {
				// minimum msg modifications to trigger FYA logic
				msg[7+1+51] = 0x20 // fyaEnable1
				msg[len(msg)-1] = computeChecksum(msg)
			},
			modifyHH: func(hh *helper.HeaderRecord) {
				hh.FirmwareVersion = helper.ConvertByteToDecimalFormat(0x28)
				hh.CommVersion = helper.ConvertByteToDecimalFormat(0x38) // Set CommVersion to use modern FYA
			},
			assertFunc: func(t *testing.T, c helper.ConfigurationChangeLogRecord) {
				want := "Mode B, Channel Pairs: <none>"
				if got := c.FlashingYellowArrows[0]; got != want {
					t.Errorf("FlashingYellowArrows = %q; want %q", got, want)
				}
			},
		},
	}

	for _, st := range subtests {
		t.Run(st.name, func(t *testing.T) {
			hdr := baseHdr
			hh := logConfigurationBaseHelperHeader()
			if st.modifyHH != nil {
				st.modifyHH(hh)
			}
			msg := logConfigurationBuildValidMessage(1)
			if st.modifyMsg != nil {
				st.modifyMsg(msg)
			}
			rec, err := dev.LogConfiguration(hdr, msg, hh)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			c := rec.Record[0]
			st.assertFunc(t, c)
		})
	}

	// now ConfigChangeSource 1,2,4,0
	for _, src := range []byte{1, 2, 4, 0} {
		t.Run(fmt.Sprintf("Source%d", src), func(t *testing.T) {
			t.Parallel()
			hdr := baseHdr
			hh := logConfigurationBaseHelperHeader()
			msg := logConfigurationBuildValidMessage(1)
			msg[7+1+61] = src
			msg[len(msg)-1] = computeChecksum(msg)
			rec, err := dev.LogConfiguration(hdr, msg, hh)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			want := map[byte]string{
				1: "Front Panel Entry",
				2: "ECcom Download",
				3: "Program Card Entry",
				4: "Front Panel 'Set Default' Entry",
				0: "",
			}[src]
			if got := rec.Record[0].ChangeSource; got != want {
				t.Errorf("ChangeSource for %d = %q; want %q", src, got, want)
			}
		})
	}
}

// Add test for LogConfiguration edge cases
func TestLogConfiguration_AdditionalEdgeCases(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	t.Run("Invalid byte length", func(t *testing.T) {
		t.Parallel()
		hh := &helper.HeaderRecord{MaxChannels: 16}
		msg := make([]byte, 10) // too short
		msg[7] = 1              // 1 record

		_, err := dev.LogConfiguration(hdr, msg, hh)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("expected byte length error, got %v", err)
		}
	})

	t.Run("Checksum validation error", func(t *testing.T) {
		t.Parallel()
		hh := &helper.HeaderRecord{MaxChannels: 16}
		msg := logConfigurationBuildValidMessage(1)
		msg[len(msg)-1] = 0xFF // corrupt checksum

		_, err := dev.LogConfiguration(hdr, msg, hh)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("expected checksum error, got %v", err)
		}
	})

	t.Run("Legacy FYA with different Type mode", func(t *testing.T) {
		t.Parallel()
		hh := &helper.HeaderRecord{
			MaxChannels:      16,
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74),
			CommVersion:      helper.ConvertByteToDecimalFormat(0x30), // < 0x38 for legacy
		}

		msg := buildBaseConfigMsg(1, func(rec []byte) {
			// Set valid BCD time
			rec[55], rec[56], rec[57], rec[58], rec[59], rec[60] = 0x00, 0x00, 0x00, 0x02, 0x01, 0x21
			// Set selectJumpers1 for Type 12 mode and FYA bits
			rec[53] = 0x78 // bits 3,4,5,6 set for FYA channels, bit 0 clear for Type 12
		})

		result, err := dev.LogConfiguration(hdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		record := result.Record[0]
		// Should use legacy FYA logic for Type 12 mode
		if record.TypeMode != "12" {
			t.Errorf("expected TypeMode=12, got %s", record.TypeMode)
		}
	})
}

func TestLogConfiguration_MissingBranchCoverage(t *testing.T) {
	dev := EDIMMU216LE{}
	baseHdr := logConfigurationBaseHeader()

	t.Run("FYA_Remap_ModeI_With_Pairs", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74),
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Mode I remap: fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50, with channel pairs
		msg[7+1+51] = 0x51 // fyaEnable1: Mode I with bit 0 set
		msg[7+1+52] = 0x40 // fyaEnable2: fya_remap bit set
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		if !strings.Contains(fya, "Mode I") || !strings.Contains(fya, "1-9") {
			t.Errorf("Expected Mode I with 1-9 pairs, got %q", fya)
		}
	})

	t.Run("FYA_ErrorMode_InvalidCombination", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToString(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Invalid combination that triggers default case - 0xC0 doesn't match any valid case
		msg[7+1+51] = 0xC0 // Invalid combination - high nibble 0xC0 doesn't match any case
		msg[7+1+52] = 0x00 // No remap
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		if !strings.Contains(fya, "Mode <error>") {
			t.Errorf("Expected Mode <error> for invalid combination, got %q", fya)
		}
	})

	t.Run("FyaRedAndGreenDisable_Mode6_HighFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Mode G (6): fya_srcY + fya_ry_lo = 0x40+0x10 = 0x50
		msg[7+1+51] = 0x50 // Mode G
		msg[7+1+52] = 0x02 // fyaEnable2 with bit 1 set
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// For Mode G with high firmware revision, should set FyaRedAndGreenDisable
		if rec.Record[0].FyaRedAndGreenDisable != "Channels: 10" {
			t.Errorf("Expected FyaRedAndGreenDisable for mode 6, got %q", rec.Record[0].FyaRedAndGreenDisable)
		}
	})

	t.Run("LegacyFYA_Type16_WithFYAC", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x37), // < 0x38 for legacy
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// selectJumpers1: Type 16 (bit 0) + FYA bits + FYAC (bit 7)
		msg[7+1+53] = 0xF9 // 0x78 (FYA bits) + 0x80 (FYAC) + 0x01 (Type 16)
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		// Legacy FYA logic overwrites string, so only last condition (7-16) is kept
		if !strings.Contains(fya, "7-16") || !strings.Contains(fya, "(Mode=FYAC)") {
			t.Errorf("Expected 7-16 (Mode=FYAC) for legacy FYA, got %q", fya)
		}
	})

	t.Run("Pplt5Suppression_Type16_HighFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29), // > 0x28
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// options2 with high nibble set, selectJumpers1 with suppression bits + Type 16
		msg[7+1+50] = 0xF0 // options2 high nibble set
		msg[7+1+53] = 0xF1 // suppression bits + Type 16
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// For Type 16 with high firmware, should use 1-9, 3-10, 5-11, 7-12
		pplt5 := rec.Record[0].Pplt5Suppression
		if !strings.Contains(pplt5, "1-9") || !strings.Contains(pplt5, "3-10") || !strings.Contains(pplt5, "5-11") || !strings.Contains(pplt5, "7-12") {
			t.Errorf("Expected Type 16 high FW channels in PPLT5, got %q", pplt5)
		}
	})

	t.Run("FYA_ChannelPairs_Case1357911", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode B (0x20) which gives nema_FyaMode = 1, and set low nibble to 0x0F to trigger all channel pairs
		msg[7+1+51] = 0x2F // fyaEnable1: Mode B (0x20) + all channel pairs (0x0F)
		msg[7+1+52] = 0x00 // fyaEnable2: no remap
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		fya := rec.Record[0].FlashingYellowArrows[0]
		// Should hit case 1 and show 13-16 channel pairs
		if !strings.Contains(fya, "1-13") || !strings.Contains(fya, "3-14") || !strings.Contains(fya, "3-15") || !strings.Contains(fya, "7-16") {
			t.Errorf("Expected 13-16 channel pairs for nema_FyaMode=1, got %q", fya)
		}
	})

	t.Run("FYA_RedYellowEnable_Case01", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode A (0x00) which gives nema_FyaMode = 0
		msg[7+1+51] = 0x00 // fyaEnable1: Mode A (high nibble = 0)
		msg[7+1+52] = 0x0F // fyaEnable2: set low nibble to trigger all case 0,1 branches
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Should hit case 0 in FyaRedAndYellowEnable logic
		if rec.Record[0].FyaRedAndYellowEnable != "Channels: 7" {
			t.Errorf("Expected FyaRedAndYellowEnable='Channels: 7', got %q", rec.Record[0].FyaRedAndYellowEnable)
		}
	})

	t.Run("FYA_IndividualChannelPairs_Case1357911", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test individual channel pairs for case 1,3,5,7,9,11
		testCases := []struct {
			name           string
			fyaEnable1     byte
			expectedString string
		}{
			{"bit2_3-14", 0x22, "3-14"}, // Mode B (0x20) + bit 1 (0x02) -> 3-14
			{"bit4_3-15", 0x24, "3-15"}, // Mode B (0x20) + bit 2 (0x04) -> 3-15
			{"bit8_7-16", 0x28, "7-16"}, // Mode B (0x20) + bit 3 (0x08) -> 7-16
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaEnable1 // fyaEnable1
				msg[7+1+52] = 0x00          // fyaEnable2: no remap
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				fya := rec.Record[0].FlashingYellowArrows[0]
				if !strings.Contains(fya, tc.expectedString) {
					t.Errorf("Expected %s in FYA, got %q", tc.expectedString, fya)
				}
			})
		}
	})

	t.Run("FYA_RedYellowEnable_Case24810", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode C (0x90) which gives nema_FyaMode = 2
		msg[7+1+51] = 0x90 // fyaEnable1: Mode C (fya_fYa_lo + fya_ry_lo = 0x80+0x10)
		msg[7+1+52] = 0x01 // fyaEnable2: set bit 0 to trigger case 2,4,8,10
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Should hit case 2 in FyaRedAndYellowEnable logic
		if rec.Record[0].FyaRedAndYellowEnable != "Channels: 9" {
			t.Errorf("Expected FyaRedAndYellowEnable='Channels: 9', got %q", rec.Record[0].FyaRedAndYellowEnable)
		}
	})

	t.Run("FYA_RedYellowEnable_Case35911", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// Set fyaEnable1 to Mode D (0xB0) which gives nema_FyaMode = 3
		msg[7+1+51] = 0xB0 // fyaEnable1: Mode D (fya_msb_hi + fya_fYa_lo + fya_ry_lo = 0x20+0x80+0x10)
		msg[7+1+52] = 0x01 // fyaEnable2: set bit 0 to trigger case 3,5,9,11
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// Should hit case 3 in FyaRedAndYellowEnable logic
		if rec.Record[0].FyaRedAndYellowEnable != "Channels: 13" {
			t.Errorf("Expected FyaRedAndYellowEnable='Channels: 13', got %q", rec.Record[0].FyaRedAndYellowEnable)
		}
	})

	t.Run("FYA_AdditionalIndividualBits", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test additional individual bits that are still uncovered
		testCases := []struct {
			name       string
			fyaEnable2 byte
			nemaMode   byte // Mode to set nema_FyaMode
			expectedCh string
		}{
			{"case24810_bit2", 0x02, 0x90, "10"}, // Mode C (nema_FyaMode=2) + fyaEnable2 bit 1
			{"case24810_bit4", 0x04, 0x90, "11"}, // Mode C (nema_FyaMode=2) + fyaEnable2 bit 2
			{"case24810_bit8", 0x08, 0x90, "12"}, // Mode C (nema_FyaMode=2) + fyaEnable2 bit 3
			{"case35911_bit2", 0x02, 0xB0, "14"}, // Mode D (nema_FyaMode=3) + fyaEnable2 bit 1
			{"case35911_bit4", 0x04, 0xB0, "15"}, // Mode D (nema_FyaMode=3) + fyaEnable2 bit 2
			{"case35911_bit8", 0x08, 0xB0, "16"}, // Mode D (nema_FyaMode=3) + fyaEnable2 bit 3
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.nemaMode   // fyaEnable1 to set nema_FyaMode
				msg[7+1+52] = tc.fyaEnable2 // fyaEnable2 to trigger individual bits
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				expected := "Channels: " + tc.expectedCh
				if rec.Record[0].FyaRedAndYellowEnable != expected {
					t.Errorf("Expected FyaRedAndYellowEnable='%s', got %q", expected, rec.Record[0].FyaRedAndYellowEnable)
				}
			})
		}
	})

	t.Run("FYA_Case6_FyaRedAndGreenDisable", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 6 (Mode G) FyaRedAndGreenDisable individual bits
		testCases := []struct {
			name       string
			fyaEnable2 byte
			expectedCh string
		}{
			{"case6_bit1", 0x01, "9"},  // fyaEnable2 bit 0 -> Channel 9
			{"case6_bit4", 0x04, "11"}, // fyaEnable2 bit 2 -> Channel 11
			{"case6_bit8", 0x08, "12"}, // fyaEnable2 bit 3 -> Channel 12
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = 0x50          // Mode G (fya_srcY + fya_ry_lo = 0x40+0x10)
				msg[7+1+52] = tc.fyaEnable2 // fyaEnable2 to trigger individual bits
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				expected := "Channels: " + tc.expectedCh
				if rec.Record[0].FyaRedAndGreenDisable != expected {
					t.Errorf("Expected FyaRedAndGreenDisable='%s', got %q", expected, rec.Record[0].FyaRedAndGreenDisable)
				}
			})
		}
	})

	t.Run("FYA_Case7_FyaRedAndGreenDisable", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 7 (Mode H) FyaRedAndGreenDisable individual bits
		testCases := []struct {
			name       string
			fyaEnable2 byte
			expectedCh string
		}{
			{"case7_bit1", 0x01, "13"}, // fyaEnable2 bit 0 -> Channel 13
			{"case7_bit2", 0x02, "14"}, // fyaEnable2 bit 1 -> Channel 14
			{"case7_bit4", 0x04, "15"}, // fyaEnable2 bit 2 -> Channel 15
			{"case7_bit8", 0x08, "16"}, // fyaEnable2 bit 3 -> Channel 16
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = 0x70          // Mode H (fya_msb_hi + fya_srcY + fya_ry_lo = 0x20+0x40+0x10)
				msg[7+1+52] = tc.fyaEnable2 // fyaEnable2 to trigger individual bits
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				expected := "Channels: " + tc.expectedCh
				if rec.Record[0].FyaRedAndGreenDisable != expected {
					t.Errorf("Expected FyaRedAndGreenDisable='%s', got %q", expected, rec.Record[0].FyaRedAndGreenDisable)
				}
			})
		}
	})

	t.Run("FYA_Case67_LowFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x73), // <= 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 6,7 with low firmware revision (else branch)
		testCases := []struct {
			name     string
			fyaMode  byte
			modeName string
		}{
			{"case6_low_fw", 0x50, "G"}, // Mode G with low firmware
			{"case7_low_fw", 0x70, "H"}, // Mode H with low firmware
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaMode // fyaEnable1 to set mode
				msg[7+1+52] = 0x01       // fyaEnable2 with some bits set
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				// Should hit else branch and set FyaRedAndYellowEnable
				if rec.Record[0].FyaRedAndYellowEnable != "Channels: <none>" {
					t.Errorf("Expected FyaRedAndYellowEnable='Channels: <none>' for low firmware, got %q", rec.Record[0].FyaRedAndYellowEnable)
				}
			})
		}
	})

	t.Run("FYA_Case67_NoFyaEnable2", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:      helper.ConvertByteToDecimalFormat(0x38),
			FirmwareRevision: helper.ConvertByteToDecimalFormat(0x74), // > 0x73
			FirmwareVersion:  helper.ConvertByteToString(0x29),
			MaxChannels:      16,
		}

		// Test case 6,7 with fyaEnable2&0xf == 0 (else branch)
		testCases := []struct {
			name     string
			fyaMode  byte
			modeName string
		}{
			{"case6_no_enable2", 0x50, "G"}, // Mode G with no fyaEnable2
			{"case7_no_enable2", 0x70, "H"}, // Mode H with no fyaEnable2
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaMode // fyaEnable1 to set mode
				msg[7+1+52] = 0x00       // fyaEnable2 with no low nibble bits set
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				// Should hit else branch and set FyaRedAndGreenDisable to <none>
				if rec.Record[0].FyaRedAndGreenDisable != "Channels: <none>" {
					t.Errorf("Expected FyaRedAndGreenDisable='Channels: <none>' for no fyaEnable2, got %q", rec.Record[0].FyaRedAndGreenDisable)
				}
			})
		}
	})

	t.Run("PPLT5_Type16_LowFirmware", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x28), // <= 0x28
			MaxChannels:     16,
		}

		msg := logConfigurationBuildValidMessage(1)
		// options2 with high nibble set, selectJumpers1 with Type 16 + suppression bits
		msg[7+1+50] = 0xF0 // options2 high nibble set
		msg[7+1+53] = 0xF1 // Type 16 (bit 0) + suppression bits (0x10,0x20,0x40,0x80)
		msg[len(msg)-1] = computeChecksum(msg)

		rec, err := dev.LogConfiguration(baseHdr, msg, hh)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		// For Type 16 with low firmware, should use 1-13, 3-14, 5-15, 7-16
		pplt5 := rec.Record[0].Pplt5Suppression
		if !strings.Contains(pplt5, "1-13") || !strings.Contains(pplt5, "3-14") || !strings.Contains(pplt5, "5-15") || !strings.Contains(pplt5, "7-16") {
			t.Errorf("Expected Type 16 low FW channels in PPLT5, got %q", pplt5)
		}
	})

	t.Run("PPLT5_Type12_IndividualBits", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test individual PPLT5 suppression bits for Type 12
		testCases := []struct {
			name           string
			selectJumpers1 byte
			expectedCh     string
		}{
			{"type12_bit10", 0x10, "1-9"},  // Type 12 + bit 4 -> 1-9
			{"type12_bit20", 0x20, "3-10"}, // Type 12 + bit 5 -> 3-10
			{"type12_bit40", 0x40, "5-11"}, // Type 12 + bit 6 -> 5-11
			{"type12_bit80", 0x80, "7-12"}, // Type 12 + bit 7 -> 7-12
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+50] = 0xF0              // options2 high nibble set
				msg[7+1+53] = tc.selectJumpers1 // Type 12 (no bit 0) + individual suppression bit
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				if !strings.Contains(rec.Record[0].Pplt5Suppression, tc.expectedCh) {
					t.Errorf("Expected %s in PPLT5 suppression, got %q", tc.expectedCh, rec.Record[0].Pplt5Suppression)
				}
			})
		}
	})

	t.Run("FYA_FinalMissingChannelPairs", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test the final missing individual channel pair conditions in case 1,3,5,7,9,11
		// These are the exact lines that are still uncovered (lines 1018, 1021, 1024)
		testCases := []struct {
			name           string
			fyaEnable1     byte
			expectedString string
		}{
			{"missing_bit2", 0x22, "3-14"}, // Mode B (0x20) + bit 1 (0x02) -> should hit fyaEnable1&0x2 > 0 body
			{"missing_bit4", 0x24, "3-15"}, // Mode B (0x20) + bit 2 (0x04) -> should hit fyaEnable1&0x4 > 0 body
			{"missing_bit8", 0x28, "7-16"}, // Mode B (0x20) + bit 3 (0x08) -> should hit fyaEnable1&0x8 > 0 body
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaEnable1 // fyaEnable1 to trigger nema_FyaMode=1 and specific bit
				msg[7+1+52] = 0x00          // fyaEnable2: no remap
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				fya := rec.Record[0].FlashingYellowArrows[0]
				if !strings.Contains(fya, tc.expectedString) {
					t.Errorf("Expected %s in FYA channel pairs, got %q", tc.expectedString, fya)
				}
			})
		}
	})

	t.Run("FYA_MissingCase024681012_ChannelPairs", func(t *testing.T) {
		hh := &helper.HeaderRecord{
			CommVersion:     helper.ConvertByteToDecimalFormat(0x38),
			FirmwareVersion: helper.ConvertByteToDecimalFormat(0x29),
			MaxChannels:     16,
		}

		// Test the missing individual channel pair conditions in case 0,2,4,6,8,10
		// These are the exact lines that are still uncovered
		testCases := []struct {
			name           string
			fyaEnable1     byte
			expectedString string
		}{
			{"case0_bit2", 0x02, "3-10"}, // Mode A (0x00) + bit 1 (0x02) -> should hit fyaEnable1&0x2 > 0 body for case 0
			{"case0_bit4", 0x04, "5-11"}, // Mode A (0x00) + bit 2 (0x04) -> should hit fyaEnable1&0x4 > 0 body for case 0
			{"case0_bit8", 0x08, "7-12"}, // Mode A (0x00) + bit 3 (0x08) -> should hit fyaEnable1&0x8 > 0 body for case 0
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				msg := logConfigurationBuildValidMessage(1)
				msg[7+1+51] = tc.fyaEnable1 // fyaEnable1 to trigger nema_FyaMode=0 and specific bit
				msg[7+1+52] = 0x00          // fyaEnable2: no remap
				msg[len(msg)-1] = computeChecksum(msg)

				rec, err := dev.LogConfiguration(baseHdr, msg, hh)
				if err != nil {
					t.Fatalf("unexpected error: %v", err)
				}

				fya := rec.Record[0].FlashingYellowArrows[0]
				if !strings.Contains(fya, tc.expectedString) {
					t.Errorf("Expected %s in FYA channel pairs, got %q", tc.expectedString, fya)
				}
			})
		}
	})
}
