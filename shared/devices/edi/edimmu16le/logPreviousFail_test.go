package edimmu16le

import (
	"testing"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func buildMsgPrevFail(nRecs int, modify func(rec []byte)) []byte {
	const hdrLen = 7
	const recSize = 98
	length := hdrLen + nRecs*recSize + 2
	msg := make([]byte, length)
	msg[7] = byte(nRecs)
	start := hdrLen + 1
	for i := 0; i < nRecs; i++ {
		rec := msg[start : start+recSize]
		// default zeros; let caller tweak
		if modify != nil {
			modify(rec)
		}
		start += recSize
	}
	// final checksum
	msg[len(msg)-1] = computeChecksum(msg)
	return msg
}

func TestLogPreviousFail(t *testing.T) {
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	baseHeader := &helper.HeaderRecord{Volt220: false, Model: helper.Mmu16le}

	tests := []struct {
		name      string
		header    *helper.HeaderRecord
		msg       []byte
		wantErr   error
		assertRec func(t *testing.T, rec *helper.LogPreviousFailRecords)
	}{
		{
			name:    "TooShort",
			header:  baseHeader,
			msg:     make([]byte, 6),
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:    "BadLength",
			header:  baseHeader,
			msg:     make([]byte, 10),
			wantErr: helper.ErrMsgByteLen,
		},
		{
			name:   "BadChecksum",
			header: baseHeader,
			msg: func() []byte {
				m := buildMsgPrevFail(1, nil)
				// corrupt the checksum
				m[len(m)-1] ^= 0xFF
				return m
			}(),
			wantErr: helper.ErrMsgByteChecksum,
		},
		// BCD conversion errors
		{
			name:   "InvalidMonthBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x1A, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsMon,
		},
		{
			name:   "InvalidDayBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x32, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsDay,
		},
		{
			name:   "InvalidHourBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x25, 0x28, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsHour,
		},
		{
			name:   "InvalidMinBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x61, 0x05, 0x28, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsMin,
		},
		{
			name:   "InvalidSecBCD",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x61, 0x16, 0x05, 0x28, 0x02, 0x14
			}),
			wantErr: helper.ErrValidateDateTimePartsSec,
		},
		{
			name:   "Basic",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				if out.DeviceModel != baseHeader.Model.String() {
					t.Errorf("DeviceModel = %q; want %q", out.DeviceModel, baseHeader.Model.String())
				}
				if len(out.Records) != 1 {
					t.Fatalf("got %d records; want 1", len(out.Records))
				}
				r := out.Records[0]

				// default fault (case 0 -> default branch)
				if r.Fault != "Undefined Fault Type Error" {
					t.Errorf("Fault = %q; want Undefined Fault Type Error", r.Fault)
				}
				// showFaultStatus==true for default -> FaultStatus slice length = j+1 = 12
				if len(r.FaultStatus) != 12 {
					t.Errorf("FaultStatus len = %d; want 12", len(r.FaultStatus))
				}
				// channel reds, yellows, greens always 12; walks too because type16Mode=false
				if len(r.ChannelRedStatus) != 12 || len(r.ChannelWalkStatus) != 12 {
					t.Errorf("ChannelStatus lens = R:%d W:%d; want 12,12",
						len(r.ChannelRedStatus), len(r.ChannelWalkStatus))
				}
				// fault!=64 -> no NextConflictingChannels
				if len(r.NextConflictingChannels) != 0 {
					t.Errorf("NextConflictingChannels = %d; want 0", len(r.NextConflictingChannels))
				}
				// no volt220, default acLine=0 Vrms @ 0Hz
				if r.AcLine != "0 Vrms @ 0Hz" {
					t.Errorf("AcLine = %q; want 0 Vrms @ 0Hz", r.AcLine)
				}
				// controlStatus bit7=0 -> redEnable Off(0)
				if r.RedEnable != "Off (0 Vrms)" {
					t.Errorf("RedEnable = %q; want Off (0 Vrms)", r.RedEnable)
				}
				// no lsFlashBit
				if r.LsFlashBit {
					t.Error("LsFlashBit = true; want false")
				}
				// temperature raw byte=0 -> 0-40 == -40
				if r.Temperature != -40 {
					t.Errorf("Temperature = %d; want -40", r.Temperature)
				}
			},
		},
		{
			name:   "FaultStatusBits",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// set faultStatus = 0x0009 -> bits 0 and 3
				// the app uses raw hex decimals as the date values
				rec[1] = 0x09  // low byte
				rec[2] = 0x00  // high byte
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// we expect 12 entries (j = 11 -> 0..11)
				if len(r.FaultStatus) != 12 {
					t.Fatalf("FaultStatus length = %d; want 12", len(r.FaultStatus))
				}
				// bit-0 and bit-3 should be true
				if !r.FaultStatus[0] || !r.FaultStatus[3] {
					t.Errorf("FaultStatus bits = %v; want index 0 and 3 true", r.FaultStatus)
				}
				// and verify a bit we didn't set remains false
				if r.FaultStatus[1] {
					t.Errorf("FaultStatus[1] = true; want false")
				}
			},
		},
		{
			name:   "ChannelStatusBits",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// rec[23..24] = red; turn on bit 2
				rec[23] = 0x04
				rec[24] = 0x00
				// rec[21..22] = yellow; turn on bit 1
				rec[21] = 0x02
				rec[22] = 0x00
				// rec[19..20] = green; turn on bit 0
				rec[19] = 0x01
				rec[20] = 0x00
				// rec[25..26] = walk; turn on bit 3
				rec[25] = 0x08
				rec[26] = 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelRedStatus[2] {
					t.Errorf("ChannelRedStatus[2]=false; want true")
				}
				if !r.ChannelYellowStatus[1] {
					t.Errorf("ChannelYellowStatus[1]=false; want true")
				}
				if !r.ChannelGreenStatus[0] {
					t.Errorf("ChannelGreenStatus[0]=false; want true")
				}
				if !r.ChannelWalkStatus[3] {
					t.Errorf("ChannelWalkStatus[3]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// enable the "type 12 w/ sdlc" bit
				rec[27] |= 0x04

				// fcStatusG -> rec[3..4] big-endian -> set bit 11 (0x0800)
				rec[3], rec[4] = 0x00, 0x08

				// fcStatusY -> rec[6..5] -> set bit 1 (0x0002)
				rec[6], rec[5] = 0x00, 0x02

				// fcStatusR -> rec[8..7] -> set bit 0 (0x0001)
				rec[8], rec[7] = 0x00, 0x01

				// leave all the other status words at 0

				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// Red field-check bit 0
				if !r.ChannelRedFieldCheckStatus[0] {
					t.Errorf("RedFieldCheck[0]=false; want true")
				}
				// Yellow field-check bit 1
				if !r.ChannelYellowFieldCheckStatus[1] {
					t.Errorf("YellowFieldCheck[1]=false; want true")
				}
				// Green field-check bit 11 (we masked &0xfff, but bit 11 remains)
				if !r.ChannelGreenFieldCheckStatus[11] {
					t.Errorf("GreenFieldCheck[11]=false; want true")
				}
				// with TYPE-12 + only G-FC bits set, we still get an array of 12 walk-FC flags (all false)
				if len(r.ChannelWalkFieldCheckStatus) != 12 {
					t.Errorf("ChannelWalkFieldCheckStatus length = %d; want 12", len(r.ChannelWalkFieldCheckStatus))
				}
			},
		},
		{
			name:   "RecurrentPulse_and_WalkRP_Status",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// rpStatusR -> rec[13..14] -> set bit 1
				rec[14], rec[13] = 0x00, 0x02
				// rpStatusY -> rec[11..12] -> set bit 2
				rec[12], rec[11] = 0x00, 0x04
				// rpStatusG -> rec[9..10] -> set bit 3
				rec[10], rec[9] = 0x00, 0x08
				// rpStatusW -> rec[15..16] -> set bit 4
				rec[16], rec[15] = 0x00, 0x10
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// Red RP bit 1
				if !r.ChannelRedRecurrentPulseStatus[1] {
					t.Errorf("RedRP[1]=false; want true")
				}
				// Yellow RP bit 2
				if !r.ChannelYellowRecurrentPulseStatus[2] {
					t.Errorf("YellowRP[2]=false; want true")
				}
				// Green RP bit 3
				if !r.ChannelGreenRecurrentPulseStatus[3] {
					t.Errorf("GreenRP[3]=false; want true")
				}
				// Walk RP bit 4 (type16Mode==false)
				if !r.ChannelWalkRecurrentPulseStatus[4] {
					t.Errorf("WalkRP[4]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12_Walk6",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// enable the "type 12 w/ sdlc" bit
				rec[27] |= 0x04
				// fcStatusG bit10 -> 0x0400 -> walkFcStatus |= 0x20 (bit index 5)
				rec[4], rec[3] = 0x04, 0x00
				// force fcStatusR non-zero so we populate walk-FC
				rec[7], rec[8] = 0x01, 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelWalkFieldCheckStatus[5] {
					t.Errorf("WalkFieldCheck[5]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12_Walk4",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[27] |= 0x04
				// fcStatusG bit9 -> 0x0200 -> walkFcStatus |= 0x08 (bit index 3)
				rec[4], rec[3] = 0x02, 0x00
				// force fcStatusR non-zero so we populate walk-FC
				rec[7], rec[8] = 0x01, 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelWalkFieldCheckStatus[3] {
					t.Errorf("WalkFieldCheck[3]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_and_WalkFcStatus_Type12_Walk2",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				rec[27] |= 0x04
				// fcStatusG bit8 -> 0x0100 -> walkFcStatus |= 0x02 (bit index 1)
				rec[4], rec[3] = 0x01, 0x00
				// force fcStatusR non-zero so we populate walk-FC
				rec[7], rec[8] = 0x01, 0x00
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if !r.ChannelWalkFieldCheckStatus[1] {
					t.Errorf("WalkFieldCheck[1]=false; want true")
				}
			},
		},
		{
			name:   "FieldCheck_WalkFcStatus_AllFalse",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// leave controlStatus bit2 == 0 so type16Mode=false (j=11)
				// set fcStatusR (bytes 7..8) to non-zero so we enter the field-check block:
				rec[7], rec[8] = 0x01, 0x00 // fcStatusR = 1
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				// we should have exactly 12 walk-FC bits (0..11)
				if len(r.ChannelWalkFieldCheckStatus) != 12 {
					t.Fatalf("WalkFieldCheck len = %d; want 12", len(r.ChannelWalkFieldCheckStatus))
				}
				// and since walkFcStatus stayed 0, every bit must be false
				for i, v := range r.ChannelWalkFieldCheckStatus {
					if v {
						t.Errorf("WalkFieldCheck[%d]=true; want false", i)
					}
				}
			},
		},
		{
			name:   "LsFlashBit_False_Type16",
			header: baseHeader,
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// set bit1 so type16Mode==true, leave bit6 clear => LsFlashBit=false
				rec[27] = 0x02
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				r := out.Records[0]
				if r.LsFlashBit {
					t.Errorf("LsFlashBit = true; want false")
				}
			},
		},
		{
			name: "Complex",
			header: func() *helper.HeaderRecord {
				h := *baseHeader
				h.Volt220 = true
				h.Model = helper.Mmu16le
				return &h
			}(),
			msg: buildMsgPrevFail(1, func(rec []byte) {
				// fault = 64 -> case 64 path for next-conflicts
				rec[0] = 64
				// set some bits in ggfonStatus (bytes 17,18)
				rec[17], rec[18] = 0x05, 0x00
				// controlStatus at offset 27: bits 1(type16),6(lsFlash),7(redEnable)
				rec[27] = 0xC2 // 1100 0010
				// acLineFrequency @ offset 28
				rec[28] = 60
				// acLineVoltage @ offset 30
				rec[30] = 120
				// raw temp @ offset 31 -> 50 -> 50-40 == 10
				rec[31] = 50
				// redEnableRmsVoltage @ offset 39
				rec[39] = 10
				// the app uses raw hex decimals as the date values
				rec[32] = 0x45 // sec
				rec[32] = 0x45 // sec
				rec[33] = 0x16 // min
				rec[34] = 0x05 // hour
				rec[35] = 0x28 // day
				rec[36] = 0x02 // month
				rec[37] = 0x14 // year
			}),
			assertRec: func(t *testing.T, out *helper.LogPreviousFailRecords) {
				if out.DeviceModel != baseHeader.Model.String() {
					t.Errorf("DeviceModel = %q; want %q", out.DeviceModel, baseHeader.Model.String())
				}
				if len(out.Records) != 1 {
					t.Fatalf("got %d records; want 1", len(out.Records))
				}
				r := out.Records[0]

				// case 64 -> "Clearance (Yellow + Red) Fault"
				if r.Fault != "Clearance (Yellow + Red) Fault" {
					t.Errorf("Fault = %q; want Clearance (Yellow + Red) Fault", r.Fault)
				}
				// default showFaultStatus=true -> len = j+1 = 16
				if len(r.FaultStatus) != 16 {
					t.Errorf("FaultStatus len = %d; want 16", len(r.FaultStatus))
				}
				// type16Mode=true -> j=15 -> RedStatus len=16, WalkStatus=0
				if len(r.ChannelRedStatus) != 16 || len(r.ChannelWalkStatus) != 0 {
					t.Errorf("ChannelRedStatus len=%d,WalkStatus len=%d; want 16,0",
						len(r.ChannelRedStatus), len(r.ChannelWalkStatus))
				}
				// fault==64 -> next-conflicts len=16
				if len(r.NextConflictingChannels) != 16 {
					t.Errorf("NextConflictingChannels len=%d; want 16",
						len(r.NextConflictingChannels))
				}
				// acLine: Volt220=true -> 120*2 @60
				if r.AcLine != "240 Vrms @ 60Hz" {
					t.Errorf("AcLine = %q; want 240 Vrms @ 60Hz", r.AcLine)
				}
				// redEnable bit7=1 -> Active; redEnableRms=10*2=20
				if r.RedEnable != "Active (20 Vrms)" {
					t.Errorf("RedEnable = %q; want Active (20 Vrms)", r.RedEnable)
				}
				// lsFlashBit bit6=1 -> true
				if !r.LsFlashBit {
					t.Error("LsFlashBit = false; want true")
				}
				// temperature 50−40=10
				if r.Temperature != 10 {
					t.Errorf("Temperature = %d; want 10", r.Temperature)
				}
			},
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			got, err := dev.LogPreviousFail(httpHdr, tc.msg, tc.header)
			if tc.wantErr != nil {
				if err == nil || err.Error()[:len(tc.wantErr.Error())] != tc.wantErr.Error() {
					t.Fatalf("expected error %v, got %v", tc.wantErr, err)
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			tc.assertRec(t, got)
		})
	}
}

// Add missing test cases for LogPreviousFail edge cases
func TestLogPreviousFail_AdditionalCases(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	t.Run("Type16Mode with conflicting channels", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{Volt220: false}
		msg := buildMsgPrevFail(1, func(rec []byte) {
			// Set fault = 64 (clearance fault) to trigger NextConflictingChannels logic
			rec[0] = 64
			// Set controlStatus byte to enable type16Mode (bit 1)
			rec[27] = 0x02
			// Set valid BCD time
			rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x02, 0x14
			// Set ggfonStatus to test NextConflictingChannels
			rec[17] = 0x01 // low byte
			rec[18] = 0x00 // high byte
		})

		result, err := dev.LogPreviousFail(httpHdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if len(result.Records) != 1 {
			t.Fatalf("expected 1 record, got %d", len(result.Records))
		}

		record := result.Records[0]
		// In type16Mode, we expect 16 channels (j=15)
		if len(record.ChannelRedStatus) != 16 {
			t.Errorf("expected 16 red channels in type16Mode, got %d", len(record.ChannelRedStatus))
		}

		// Check NextConflictingChannels is populated for fault 64
		if len(record.NextConflictingChannels) == 0 {
			t.Error("expected NextConflictingChannels to be populated for fault 64")
		}
	})

	t.Run("SDLC Type-12 mode walk field check mapping", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{Volt220: false}
		msg := buildMsgPrevFail(1, func(rec []byte) {
			// Set controlStatus to enable SDLC TYPE-12 mode (bit 2)
			rec[27] = 0x04
			// Set valid BCD time
			rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x02, 0x14
			// Set fcStatusG with high bits to test walk FC mapping
			rec[3] = 0x00 // low byte
			rec[4] = 0x0F // high byte (bits 8,9,10,11 set)
		})

		result, err := dev.LogPreviousFail(httpHdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		record := result.Records[0]
		// Check that walk field check status is populated when fcStatusG has high bits
		if len(record.ChannelWalkFieldCheckStatus) == 0 {
			t.Error("expected ChannelWalkFieldCheckStatus to be populated in SDLC TYPE-12 mode")
		}
	})

	t.Run("Volt220 voltage normalization", func(t *testing.T) {
		t.Parallel()
		header := &helper.HeaderRecord{Volt220: true}
		msg := buildMsgPrevFail(1, func(rec []byte) {
			// Set valid BCD time
			rec[32], rec[33], rec[34], rec[35], rec[36], rec[37] = 0x45, 0x16, 0x05, 0x28, 0x02, 0x14
			// Set controlStatus byte to enable type16Mode (bit 1)
			rec[27] = 0x02
			// Set some RMS voltages
			rec[55] = 50 // greenChannel1RmsVoltage
			rec[71] = 60 // yellowChannel1RmsVoltage
			rec[87] = 70 // redChannel1RmsVoltage
		})

		result, err := dev.LogPreviousFail(httpHdr, msg, header)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		record := result.Records[0]
		// With Volt220=true, voltages should be doubled
		if len(record.ChannelGreenRmsVoltage) > 0 && record.ChannelGreenRmsVoltage[0] != 100 {
			t.Errorf("expected green voltage 100 (50*2), got %d", record.ChannelGreenRmsVoltage[0])
		}
	})
}

func TestNormalizeVoltages(t *testing.T) {
	type args struct {
		input  int
		header *helper.HeaderRecord
	}
	tests := []struct {
		name string
		args args
		want int32
	}{
		{"no flags", args{10, &helper.HeaderRecord{Volt220: false, VoltDC: false}}, 10},
		{"volt220 only", args{10, &helper.HeaderRecord{Volt220: true, VoltDC: false}}, 20},
		{"voltDC only", args{20, &helper.HeaderRecord{Volt220: false, VoltDC: true}}, 5},
		{"both flags", args{8, &helper.HeaderRecord{Volt220: true, VoltDC: true}}, 4},
	}
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			if got := normalizeVoltages(tc.args.input, tc.args.header); got != tc.want {
				t.Errorf("normalizeVoltages(%d, %+v) = %d, want %d", tc.args.input, tc.args.header, got, tc.want)
			}
		})
	}
}
