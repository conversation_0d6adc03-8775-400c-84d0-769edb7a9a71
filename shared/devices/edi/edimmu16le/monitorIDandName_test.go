package edimmu16le

import (
	"errors"
	"testing"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestMonitorIDandName(t *testing.T) {
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}                       // not used by MonitorIDandName
	helperHdr := &helper.HeaderRecord{Model: helper.Mmu16le} // not used by MonitorIDandName

	t.Run("success", func(t *testing.T) {
		t.Parallel()
		// build a 7+30+1=38 byte message
		msg := make([]byte, 38)
		// arbitrary header bytes [0..4]
		for i := 0; i < 5; i++ {
			msg[i] = byte(0x10 + i)
		}
		// monitor ID = 0x0102
		msg[5] = 0x02 // LS
		msg[6] = 0x01 // MS
		// fill name starting at offset 7 with "TestName"
		copy(msg[7:], []byte("TestName"))
		// leave the rest zero
		// compute and write checksum at last byte
		msg[len(msg)-1] = computeChecksum(msg)

		mon, err := dev.MonitorIDandName(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}
		if mon.DeviceModel != helper.Mmu16le.String() {
			t.Errorf("DeviceModel = %q; want %q", mon.DeviceModel, helper.Mmu16le.String())
		}
		if mon.MonitorId != 0x0102 {
			t.Errorf("MonitorId = %d; want 0x0102", mon.MonitorId)
		}
		if mon.MonitorName != "TestName" {
			t.Errorf("MonitorName = %q; want %q", mon.MonitorName, "TestName")
		}
	})

	t.Run("error: length too short", func(t *testing.T) {
		t.Parallel()
		short := make([]byte, 37)
		_, err := dev.MonitorIDandName(hdr, short, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteLen) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteLen", err)
		}
	})

	t.Run("error: bad checksum", func(t *testing.T) {
		t.Parallel()
		// build correct-length but leave bad checksum
		msg := make([]byte, 38)
		msg[5], msg[6] = 1, 0
		msg[7] = 'X' // a printable
		// bad checksum:
		msg[len(msg)-1] = 0xFF
		_, err := dev.MonitorIDandName(hdr, msg, helperHdr)
		if !errors.Is(err, helper.ErrMsgByteChecksum) {
			t.Errorf("err = %v; want wrap of helper.ErrMsgByteChecksum", err)
		}
	})
}

// Add test for MonitorIDandName edge case
func TestMonitorIDandName_EdgeCase(t *testing.T) {
	t.Parallel()
	dev := EDIMMU216LE{}
	hdr := &pubsubdata.HeaderDetails{}
	helperHdr := &helper.HeaderRecord{}

	t.Run("Monitor name with null bytes", func(t *testing.T) {
		t.Parallel()
		msg := make([]byte, 38)
		// Set monitor ID
		msg[5] = 0xFF // LS
		msg[6] = 0xFF // MS -> should give 0xFFFF = 65535
		// Set name with embedded null bytes
		copy(msg[7:], []byte("Test\x00Name\x00"))
		msg[len(msg)-1] = computeChecksum(msg)

		mon, err := dev.MonitorIDandName(hdr, msg, helperHdr)
		if err != nil {
			t.Fatalf("unexpected error: %v", err)
		}

		if mon.MonitorId != 65535 {
			t.Errorf("MonitorId = %d; want 65535", mon.MonitorId)
		}

		// GetMonitorName should handle null bytes properly
		if mon.MonitorName == "" {
			t.Error("MonitorName should not be empty")
		}
	})
}
