package edimmu16le

import (
	"encoding/binary"
	"fmt"
	"slices"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	RMSStatusLength = 106
)

/*
===============================================================================
Byte Message Layout (RMSStatus)
===============================================================================

+-------------------------------+
|        Header (7 bytes)       |
+-------------------------------+
|   RMS Status Data (99 bytes)  |
+-------------------------------+
|   Checksum (1 byte)           |
+-------------------------------+

Header (7 bytes):
  [0] Message type identifier
  [1] Device address
  [2] Command code
  [3] Response status
  [4] Reserved byte
  [5] Reserved byte
  [6] Reserved byte

RMS Status Data (99 bytes):
  [7]     Fault (1 byte)
           - Indicates if a fault is present (0x00 = No Fault, otherwise faulted).
  [8-9]   Fault Status (2 bytes, little endian)
           - Provides additional status/reason for the fault.
  [26-27] Channel Green Status (2 bytes, little endian)
           - Bitfield representing the status of each green channel (up to header.MaxChannels).
  [28-29] Channel Yellow Status (2 bytes, little endian)
           - Bitfield representing the status of each yellow channel (up to header.MaxChannels).
  [30-31] Channel Red Status (2 bytes, little endian)
           - Bitfield representing the status of each red channel (up to header.MaxChannels).
  [38]    Temperature (1 byte)
           - Temperature value in Celsius + 40 (needs to be converted to Fahrenheit).
  [39]    Monitor Time - Seconds (BCD)
  [40]    Monitor Time - Minutes (BCD)
  [41]    Monitor Time - Hours (BCD)
  [42]    Monitor Time - Date (BCD)
  [43]    Monitor Time - Month (BCD)
  [44]    Monitor Time - Year (BCD)
  [47-62] Green Channel Voltages (16 bytes)
           - RMS voltage values for green channels (up to header.MaxChannels).
  [63-78] Yellow Channel Voltages (16 bytes)
           - RMS voltage values for yellow channels (up to header.MaxChannels).
  [79-94] Red Channel Voltages (16 bytes)
           - RMS voltage values for red channels (up to header.MaxChannels).

Checksum (1 byte) follows all data.
*/
// ===============================================================================

func (device EDIMMU216LE) RMSStatus(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsStatusRecord *helper.RmsStatusRecord, err error) {
	rmsStatusRecord = &helper.RmsStatusRecord{}

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, RMSStatusLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), RMSStatusLength)
	}

	// Validate the checksum
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	// Convert header.CommVersion (which is a bcd string) to an int
	commVersion := helper.ConvertBCDStringToInt(header.CommVersion)

	// get the monitor time
	rmsStatusRecord.MonitorTime, err = helper.ConvertBCDBytesToDateTimeII(byteMsg[43], byteMsg[42], byteMsg[44], byteMsg[41], byteMsg[40], byteMsg[39], httpHeader.GatewayTimezone)
	if err != nil {
		return nil, err
	}

	// inspect the datetime of the monitor -- the data should be sensical; not the overall date reported,
	// but the parts that comprise the date time should be sensical
	rmsStatusRecord.DeviceModel = header.Model.String()

	// hydrate IsFaulted
	fault := byteMsg[7]
	if fault == 0x00 {
		rmsStatusRecord.IsFaulted = false
		rmsStatusRecord.Fault = "No Fault"
	} else {
		rmsStatusRecord.IsFaulted = true

		// hydrate the fault reason
		faultStatus := uint32(byteMsg[8])
		faultStatus = faultStatus*256 + uint32(byteMsg[9])

		rmsStatusRecord.Fault, rmsStatusRecord.FaultStatus, _ = FaultTextAndStatus(int(fault), int(faultStatus), int(commVersion))
	}

	// hydrate the Channel Statuses
	greenStatus := binary.LittleEndian.Uint16(byteMsg[26:28])
	yellowStatus := binary.LittleEndian.Uint16(byteMsg[28:30])
	redStatus := binary.LittleEndian.Uint16(byteMsg[30:32])
	rmsStatusRecord.ChannelRedStatus = helper.ParseChannelStatus(uint32(redStatus), int(header.MaxChannels))
	rmsStatusRecord.ChannelYellowStatus = helper.ParseChannelStatus(uint32(yellowStatus), int(header.MaxChannels))
	rmsStatusRecord.ChannelGreenStatus = helper.ParseChannelStatus(uint32(greenStatus), int(header.MaxChannels))

	// hydrate the temperature
	rmsStatusRecord.Temperature = int64(byteMsg[38]) - 40 // convert to Fahrenheit

	// hydrate the voltages
	normalizeVoltages := func(preNormalizedVoltage int, headerDetail *helper.HeaderRecord) (normalizedVoltage int64) {
		if header.Volt220 {
			return int64(preNormalizedVoltage) * 2
		}
		return int64(preNormalizedVoltage)
	}
	greenRmsVoltageOffset := 47
	rmsStatusRecord.VoltagesGreen = helper.ParseVoltages(byteMsg[greenRmsVoltageOffset:greenRmsVoltageOffset+16], header, normalizeVoltages)
	slices.Reverse(rmsStatusRecord.VoltagesGreen)

	yellowRmsVoltageOffset := 63
	rmsStatusRecord.VoltagesYellow = helper.ParseVoltages(byteMsg[yellowRmsVoltageOffset:yellowRmsVoltageOffset+16], header, normalizeVoltages)
	slices.Reverse(rmsStatusRecord.VoltagesYellow)

	redRmsVoltageOffset := 79
	rmsStatusRecord.VoltagesRed = helper.ParseVoltages(byteMsg[redRmsVoltageOffset:redRmsVoltageOffset+16], header, normalizeVoltages)
	slices.Reverse(rmsStatusRecord.VoltagesRed)

	return rmsStatusRecord, nil
}
