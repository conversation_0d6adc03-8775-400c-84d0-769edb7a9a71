package edimmu16le

import (
	"encoding/binary"
	"fmt"
	"math"
	"strconv"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	LogConfigurationRecordSize = 64

	// FYA/PLT5 related constants
	fya_ry_lo  = 0x10 // fya_enable1: 1=RY on 1,3,5,7 / 0=RY is 9:12 or 13:16
	fya_msb_hi = 0x20 // fya_enable1: Mode=0 9:12 / Mode=1 13:16
	fya_srcY   = 0x40 // fya_enable1: Mode=0 source Green / Mode=1 source Yellow
	fya_fYa_lo = 0x80 // fya_enable1: 0=fYa is 9:12 or 13:16 / 1=fYa on 1,3,5,7
	trap_mode  = 0x10 // fya_enable2: Mode=0 none / Mode=1 detect trap
	fya_flash  = 0x20 // fya_enable2: Mode=0 stuck FYA no fault / Mode=1 stuck FYA fault
	fya_remap  = 0x40 // fya_enable2: Mode=0 none / Mode=1 remap Ped Y to loner
)

/*
===============================================================================
Byte Message Layout (LogConfiguration)
===============================================================================

+-------------------------------+
|        Header (7 bytes)       |
+-------------------------------+
|   Number of records (1 byte)  |
+-------------------------------+
|   Log Configuration Records   |
|   (64 bytes per record)       |
+-------------------------------+
|   Checksum (1 byte)           |
+-------------------------------+

Header and number of records (8 bytes):
  [0] Message type identifier
  [1] Device address
  [2] Command code
  [3] Response status
  [4] Reserved byte
  [5] Reserved byte
  [6] Reserved byte
  [7] Number of records (0-255)

Log Configuration Record (64 bytes per record):
  [0-29]   Channel permissives (15 channels, 2 bytes each)
  [30-31]  MYCD (Min Yellow Cutoff Disable) bitfield
  [32-33]  MYRCD (Min Yellow+Red Cutoff Disable) bitfield
  [34-35]  Field Check Green bitfield
  [36-37]  Field Check Yellow bitfield
  [38-39]  Field Check Red bitfield
  [40-41]  Dual Enable YR bitfield
  [42-43]  Dual Enable GR bitfield
  [44-45]  Dual Enable GY bitfield
  [46-47]  Red Fail Enable bitfield
  [48]     VM Min Flash/24V Latch/CVM Latch (bitfield)
  [49]     Options1 (bitfield)
  [50]     Options2 (bitfield)
  [51]     FYA Enable1 (bitfield)
  [52]     FYA Enable2 (bitfield)
  [53]     Select Jumpers1 (bitfield)
  [54]     Config Change Source
  [55-56]  CRC (2 bytes, little endian)
  [57-63]  Reserved/Unused

Checksum (1 byte) follows all records.
*/

func (device EDIMMU216LE) LogConfiguration(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.ConfigurationChangeLogRecords, err error) {
	numberOfRecords := int(byteMsg[HeaderLength])
	length := HeaderLength + RecordCountLength + (numberOfRecords * LogConfigurationRecordSize) + ChecksumLength

	// Convert header.CommVersion (which is a bcd string) to an int
	commVersion := helper.ConvertBCDStringToInt(header.CommVersion)
	firmwareRevision := helper.ConvertBCDStringToInt(header.FirmwareRevision)
	firmwareVersion := helper.ConvertBCDStringToInt(header.FirmwareVersion)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	allRecords = &helper.ConfigurationChangeLogRecords{
		DeviceModel: header.Model.String(),
		Record:      make([]helper.ConfigurationChangeLogRecord, numberOfRecords),
		RawMessage:  byteMsg,
	}

	for idx := range numberOfRecords {
		var cfr helper.ConfigurationChangeLogRecord

		offset := HeaderLength + RecordCountLength + idx*LogConfigurationRecordSize

		// pick out the raw bytes
		permissives := processPermissives(byteMsg[offset : offset+30])
		mycd := int(binary.LittleEndian.Uint16(byteMsg[offset+30 : offset+32]))
		myrcd := int(binary.LittleEndian.Uint16(byteMsg[offset+32 : offset+34]))
		fieldCheckG := int(binary.LittleEndian.Uint16(byteMsg[offset+34 : offset+36]))
		fieldCheckY := int(binary.LittleEndian.Uint16(byteMsg[offset+36 : offset+38]))
		fieldCheckR := int(binary.LittleEndian.Uint16(byteMsg[offset+38 : offset+40]))
		dualEnableYR := int(binary.LittleEndian.Uint16(byteMsg[offset+40 : offset+42]))
		dualEnableGR := int(binary.LittleEndian.Uint16(byteMsg[offset+42 : offset+44]))
		dualEnableGY := int(binary.LittleEndian.Uint16(byteMsg[offset+44 : offset+46]))
		redFailEnable := int(binary.LittleEndian.Uint16(byteMsg[offset+46 : offset+48]))

		vmMinFlash := byteMsg[offset+48]
		options1 := byteMsg[offset+49]
		options2 := byteMsg[offset+50]
		fyaEnable1 := byteMsg[offset+51]
		fyaEnable2 := byteMsg[offset+52]
		selectJumpers1 := byteMsg[offset+53]
		// selectJumpers2 := byteMsg[offset+54]

		bcdSeconds := byteMsg[offset+55]
		bcdMinutes := byteMsg[offset+56]
		bcdHour := byteMsg[offset+57]
		bcdDate := byteMsg[offset+58]
		bcdMonth := byteMsg[offset+59]
		bcdYear := byteMsg[offset+60]
		theDateTime, err := helper.ConvertBCDBytesToDateTimeII(bcdMonth, bcdDate, bcdYear, bcdHour, bcdMinutes, bcdSeconds, httpHeader.GatewayTimezone)
		if err != nil {
			return nil, err
		}
		configChangeSource := byteMsg[offset+61]

		crc := int(byteMsg[offset+63])<<8 + int(byteMsg[offset+62])

		cfr.DateTime = theDateTime

		// assign permissives
		cfr.Ch01Permissives = permissives[0]
		cfr.Ch02Permissives = permissives[1]
		cfr.Ch03Permissives = permissives[2]
		cfr.Ch04Permissives = permissives[3]
		cfr.Ch05Permissives = permissives[4]
		cfr.Ch06Permissives = permissives[5]
		cfr.Ch07Permissives = permissives[6]
		cfr.Ch08Permissives = permissives[7]
		cfr.Ch09Permissives = permissives[8]
		cfr.Ch10Permissives = permissives[9]
		cfr.Ch11Permissives = permissives[10]
		cfr.Ch12Permissives = permissives[11]
		cfr.Ch13Permissives = permissives[12]
		cfr.Ch14Permissives = permissives[13]
		cfr.Ch15Permissives = permissives[14]

		// MYCD Jumpers and related bitfields
		cfr.MinimumYellowClearanceEnable,
			cfr.MinimumYellowRedClearanceEnable,
			cfr.FieldCheckEnableRed,
			cfr.FieldCheckEnableYellow,
			cfr.FieldCheckEnableGreen,
			cfr.GreenRedDualEnable,
			cfr.YellowRedDualEnable,
			cfr.GreenYellowDualEnable,
			cfr.RedFailEnable = processJumpers(mycd, myrcd, fieldCheckR, fieldCheckY, fieldCheckG, dualEnableGR, dualEnableYR, dualEnableGY, redFailEnable)

		// options1 display
		cfr.RecurrentPulse,
			cfr.WatchdogEnableSwitch,
			cfr.WalkEnableTs1,
			cfr.LogCvmFaults,
			cfr.ProgramCardMemory,
			cfr.LEDguardThresholds,
			cfr.X24VIiInputThreshold = parseOptions1(options1)

		// options2 display
		cfr.ForceType_16Mode,
			cfr.Type_12WithSdlcMode,
			cfr.VmCvm_24V_3XdayLatch = parseOptions2(options2)

		// min flash jumpers, 24v latch, cvm latch
		temp := math.Max(6, float64(int(vmMinFlash&0xf)+1))
		cfr.MinimumFlashTime = fmt.Sprintf("%d seconds", int(temp))
		cfr.CvmLatchEnable = vmMinFlash&0x20 > 0
		cfr.X24VLatchEnable = vmMinFlash&0x10 > 0
		cfr.X24VoltInhibit = selectJumpers1&0x4 > 0
		cfr.Port_1Disable = selectJumpers1&0x2 > 0
		if selectJumpers1&0x1 > 0 {
			cfr.TypeMode = "16"
		} else {
			cfr.TypeMode = "12"
		}
		// FYA logic
		cfr.FlashingYellowArrows,
			cfr.FyaRedAndYellowEnable,
			cfr.FyaRedAndGreenDisable,
			cfr.FyaYellowTrapDetection,
			cfr.FyaFlashRateDetection = processFYA(int(commVersion), int(firmwareRevision), fyaEnable1, fyaEnable2, selectJumpers1)

		// PLT5 Arrow Suppression
		cfr.Pplt5Suppression = processPLT5Suppression(options2, selectJumpers1, int(firmwareVersion))

		// change source
		var source string
		switch configChangeSource {
		case 1:
			source = "Front Panel Entry"
		case 2:
			source = "ECcom Download"
		case 3:
			source = "Program Card Entry"
		case 4:
			source = "Front Panel 'Set Default' Entry"
		default:
			source = ""
		}
		cfr.ChangeSource = source
		// crc value
		cfr.CheckValue = strconv.Itoa(crc)
		allRecords.Record[idx] = cfr
	}

	return allRecords, nil
}

// Helper to process channel permissives
// Accepts 30-byte slice, returns [15][]string for channels 1-15
func processPermissives(conflictBytes []byte) [15][]string {
	var result [15][]string
	for ch := 0; ch < 15; ch++ {
		conflictMap := int(binary.LittleEndian.Uint16(conflictBytes[ch*2 : ch*2+2]))
		primaryChannel := ch + 1
		primaryMask := 1 << primaryChannel
		mask := primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMap&mask == 0 {
				result[ch] = append(result[ch], strconv.Itoa(i))
			}
			mask *= 2
		}
	}
	return result
}

// Helper to process MYCD Jumpers and related bitfields
func processJumpers(mycd, myrcd, fieldCheckR, fieldCheckY, fieldCheckG, dualEnableGR, dualEnableYR, dualEnableGY, redFailEnable int) (minY, minYR, fcR, fcY, fcG, grDual, yrDual, gyDual, redFail []bool) {
	for i := 1; i <= 16; i++ {
		mask := 1 << (i - 1)
		minY = append(minY, mycd&mask == 0)
		minYR = append(minYR, myrcd&mask == 0)
		fcR = append(fcR, fieldCheckR&mask > 0)
		fcY = append(fcY, fieldCheckY&mask > 0)
		fcG = append(fcG, fieldCheckG&mask > 0)
		grDual = append(grDual, dualEnableGR&mask > 0)
		yrDual = append(yrDual, dualEnableYR&mask > 0)
		gyDual = append(gyDual, dualEnableGY&mask > 0)
		redFail = append(redFail, redFailEnable&mask > 0)
	}
	return
}

// Helper to parse options1 bitfield
func parseOptions1(options1 byte) (recurrentPulse, watchdogEnableSwitch, walkEnableTs1, logCvmFaults, programCardMemory, ledGuardThresholds bool, x24VIiInputThreshold string) {
	recurrentPulse = options1&0x40 == 0
	watchdogEnableSwitch = options1&0x20 > 0
	walkEnableTs1 = options1&0x10 > 0
	logCvmFaults = options1&0x4 == 0
	programCardMemory = options1&0x2 > 0
	ledGuardThresholds = options1&0x80 > 0
	if options1&0x1 > 0 {
		x24VIiInputThreshold = "12 Vdc"
	} else {
		x24VIiInputThreshold = "24 Vdc"
	}
	return
}

// Helper to parse options2 bitfield
func parseOptions2(options2 byte) (forceType16Mode, type12WithSdlcMode, vmCvm24V3XdayLatch bool) {
	forceType16Mode = options2&0x2 > 0
	type12WithSdlcMode = options2&0x4 > 0
	vmCvm24V3XdayLatch = options2&0x1 > 0
	return
}

// Helper to process FYA logic
func processFYA(commVersion, firmwareRevision int, fyaEnable1, fyaEnable2, selectJumpers1 byte) (FlashingYellowArrows []string, FyaRedAndYellowEnable, FyaRedAndGreenDisable string, FyaYellowTrapDetection, FyaFlashRateDetection bool) {
	var FlashingYellowArrow string
	var fyaRedAndYellowEnable, fyaRedAndGreenDisable string
	var fyaYellowTrapDetection, fyaFlashRateDetection bool
	if commVersion >= 0x38 {
		var nema_FyaMode int
		if fyaEnable2&fya_remap > 0 {
			switch fyaEnable1 & 0xf0 {
			case fya_srcY + fya_fYa_lo + fya_ry_lo:
				nema_FyaMode = 4
				FlashingYellowArrow = "Mode E"
			case fya_msb_hi + fya_srcY + fya_fYa_lo + fya_ry_lo:
				nema_FyaMode = 5
				FlashingYellowArrow = "Mode F"
			case fya_srcY + fya_ry_lo:
				nema_FyaMode = 8
				FlashingYellowArrow = "Mode I"
			case fya_msb_hi + fya_srcY + fya_ry_lo:
				nema_FyaMode = 9
				FlashingYellowArrow = "Mode J"
			default:
				nema_FyaMode = 0
				FlashingYellowArrow = "Mode <error>"
			}
		} else {
			switch fyaEnable1 & 0xf0 {
			case 0:
				nema_FyaMode = 0
				FlashingYellowArrow = "Mode A"
			case fya_msb_hi:
				nema_FyaMode = 1
				FlashingYellowArrow = "Mode B"
			case fya_fYa_lo + fya_ry_lo:
				nema_FyaMode = 2
				FlashingYellowArrow = "Mode C"
			case fya_msb_hi + fya_fYa_lo + fya_ry_lo:
				nema_FyaMode = 3
				FlashingYellowArrow = "Mode D"
			case fya_srcY + fya_ry_lo:
				nema_FyaMode = 6
				FlashingYellowArrow = "Mode G"
			case fya_msb_hi + fya_srcY + fya_ry_lo:
				nema_FyaMode = 7
				FlashingYellowArrow = "Mode H"
			case fya_ry_lo:
				nema_FyaMode = 10
				FlashingYellowArrow = "Mode K"
			case fya_msb_hi + fya_ry_lo:
				nema_FyaMode = 11
				FlashingYellowArrow = "Mode L"
			default:
				nema_FyaMode = 0
				FlashingYellowArrow = "Mode <error>"
			}
		}
		if fyaEnable1&0xf > 0 {
			switch nema_FyaMode {
			case 0, 2, 4, 6, 8, 10:
				if fyaEnable1&0x1 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 1-9"
				}
				if fyaEnable1&0x2 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 3-10"
				}
				if fyaEnable1&0x4 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 5-11"
				}
				if fyaEnable1&0x8 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 7-12"
				}
			case 1, 3, 5, 7, 9, 11:
				if fyaEnable1&0x1 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 1-13"
				}
				if fyaEnable1&0x2 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 3-14"
				}
				if fyaEnable1&0x4 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 3-15"
				}
				if fyaEnable1&0x8 > 0 {
					FlashingYellowArrow += ", Channel Pairs: 7-16"
				}
			}
		} else {
			FlashingYellowArrow += ", Channel Pairs: <none>"
		}
		if fyaEnable2&0xf > 0 {
			switch nema_FyaMode {
			case 0, 1:
				if fyaEnable2&0x1 > 0 {
					fyaRedAndYellowEnable = "Channels: 1"
				}
				if fyaEnable2&0x2 > 0 {
					fyaRedAndYellowEnable = "Channels: 3"
				}
				if fyaEnable2&0x4 > 0 {
					fyaRedAndYellowEnable = "Channels: 5"
				}
				if fyaEnable2&0x8 > 0 {
					fyaRedAndYellowEnable = "Channels: 7"
				}
			case 2, 4, 8, 10:
				if fyaEnable2&0x1 > 0 {
					fyaRedAndYellowEnable = "Channels: 9"
				}
				if fyaEnable2&0x2 > 0 {
					fyaRedAndYellowEnable = "Channels: 10"
				}
				if fyaEnable2&0x4 > 0 {
					fyaRedAndYellowEnable = "Channels: 11"
				}
				if fyaEnable2&0x8 > 0 {
					fyaRedAndYellowEnable = "Channels: 12"
				}
			case 3, 5, 9, 11:
				if fyaEnable2&0x1 > 0 {
					fyaRedAndYellowEnable = "Channels: 13"
				}
				if fyaEnable2&0x2 > 0 {
					fyaRedAndYellowEnable = "Channels: 14"
				}
				if fyaEnable2&0x4 > 0 {
					fyaRedAndYellowEnable = "Channels: 15"
				}
				if fyaEnable2&0x8 > 0 {
					fyaRedAndYellowEnable = "Channels: 16"
				}
			case 6:
				if firmwareRevision > 0x73 {
					if fyaEnable2&0x1 > 0 {
						fyaRedAndGreenDisable = "Channels: 9"
					}
					if fyaEnable2&0x2 > 0 {
						fyaRedAndGreenDisable = "Channels: 10"
					}
					if fyaEnable2&0x4 > 0 {
						fyaRedAndGreenDisable = "Channels: 11"
					}
					if fyaEnable2&0x8 > 0 {
						fyaRedAndGreenDisable = "Channels: 12"
					}
				} else {
					fyaRedAndYellowEnable = "Channels: <none>"
				}
			case 7:
				if firmwareRevision > 0x73 {
					if fyaEnable2&0x1 > 0 {
						fyaRedAndGreenDisable = "Channels: 13"
					}
					if fyaEnable2&0x2 > 0 {
						fyaRedAndGreenDisable = "Channels: 14"
					}
					if fyaEnable2&0x4 > 0 {
						fyaRedAndGreenDisable = "Channels: 15"
					}
					if fyaEnable2&0x8 > 0 {
						fyaRedAndGreenDisable = "Channels: 16"
					}
				} else {
					fyaRedAndYellowEnable = "Channels: <none>"
				}
			}
		} else {
			switch nema_FyaMode {
			case 6, 7:
				if firmwareRevision > 0x73 {
					fyaRedAndGreenDisable = "Channels: <none>"
				} else {
					fyaRedAndYellowEnable = "Channels: <none>"
				}
			default:
				fyaRedAndYellowEnable = "Channels: <none>"
			}
		}
		fyaYellowTrapDetection = fyaEnable2&trap_mode > 0
		fyaFlashRateDetection = fyaEnable2&fya_flash > 0
	} else {
		// legacy EDI FYA
		if selectJumpers1&0x78 > 0 {
			if selectJumpers1&0x1 > 0 { // type 16
				if selectJumpers1&0x8 > 0 {
					FlashingYellowArrow = "1-13 "
				}
				if selectJumpers1&0x10 > 0 {
					FlashingYellowArrow = "3-14 "
				}
				if selectJumpers1&0x20 > 0 {
					FlashingYellowArrow = "5-15 "
				}
				if selectJumpers1&0x40 > 0 {
					FlashingYellowArrow = "7-16 "
				}
			} else { // type 12
				if selectJumpers1&0x8 > 0 {
					FlashingYellowArrow = "1-9 "
				}
				if selectJumpers1&0x10 > 0 {
					FlashingYellowArrow = "3-10 "
				}
				if selectJumpers1&0x20 > 0 {
					FlashingYellowArrow = "5-11 "
				}
				if selectJumpers1&0x40 > 0 {
					FlashingYellowArrow = "7-12 "
				}
			}
			if selectJumpers1&0x80 > 0 {
				FlashingYellowArrow += "(Mode=FYAC)"
			} else {
				FlashingYellowArrow += "(Mode=FYA)"
			}
		} else {
			FlashingYellowArrow = "<none>"
		}
	}
	return []string{FlashingYellowArrow}, fyaRedAndYellowEnable, fyaRedAndGreenDisable, fyaYellowTrapDetection, fyaFlashRateDetection
}

// Helper to process PLT5 suppression logic
func processPLT5Suppression(options2, selectJumpers1 byte, firmwareVersion int) string {
	var suppression string
	if options2&0x8 > 0 {
		suppression = "PPLT5 Ball Suppression = "
	} else {
		suppression = "PPLT5 Arrow Suppression = "
	}
	if options2&0xf0 > 0 {
		if selectJumpers1&0x1 > 0 {
			if firmwareVersion > 0x28 {
				if selectJumpers1&0x10 > 0 {
					suppression += "1-9"
				}
				if selectJumpers1&0x20 > 0 {
					suppression += "3-10"
				}
				if selectJumpers1&0x40 > 0 {
					suppression += "5-11"
				}
				if selectJumpers1&0x80 > 0 {
					suppression += "7-12"
				}
			} else {
				if selectJumpers1&0x10 > 0 {
					suppression += "1-13"
				}
				if selectJumpers1&0x20 > 0 {
					suppression += "3-14"
				}
				if selectJumpers1&0x40 > 0 {
					suppression += "5-15"
				}
				if selectJumpers1&0x80 > 0 {
					suppression += "7-16"
				}
			}
		} else {
			if selectJumpers1&0x10 > 0 {
				suppression += "1-9"
			}
			if selectJumpers1&0x20 > 0 {
				suppression += "3-10"
			}
			if selectJumpers1&0x40 > 0 {
				suppression += "5-11"
			}
			if selectJumpers1&0x80 > 0 {
				suppression += "7-12"
			}
		}
	} else {
		suppression += "<none>"
	}
	return suppression
}
