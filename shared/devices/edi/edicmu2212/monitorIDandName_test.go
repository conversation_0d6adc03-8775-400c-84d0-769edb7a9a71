package edicmu2212

import (
	"errors"
	"testing"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestMonitorIDandName(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	header := &helper.HeaderRecord{Model: helper.CMU2212_lv}

	tests := []struct {
		name        string
		byteMsg     []byte
		httpHeader  *pubsubdata.HeaderDetails
		header      *helper.HeaderRecord
		wantMonitor *helper.MonitorNameAndId
		wantErr     error
	}{
		{
			name:       "valid monitor info",
			byteMsg:    buildValidMonitorIDandNameMsg(),
			httpHeader: httpHeader,
			header:     header,
			wantMonitor: &helper.MonitorNameAndId{
				DeviceModel: header.Model.String(),
				MonitorId:   12345,
				MonitorName: "Test Monitor",
			},
			wantErr: nil,
		},
		{
			name:       "invalid message length",
			byteMsg:    []byte{0x01, 0x02}, // Too short
			httpHeader: httpHeader,
			header:     header,
			wantErr:    helper.ErrMsgByteLen,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			t.Parallel()
			got, err := device.MonitorIDandName(test.httpHeader, test.byteMsg, test.header)
			if err != nil && test.wantErr == nil {
				t.Errorf("MonitorIDandName() unexpected error = %v", err)
				return
			}
			if err == nil && test.wantErr != nil {
				t.Errorf("MonitorIDandName() expected error = %v, got nil", test.wantErr)
				return
			}
			if err != nil && test.wantErr != nil {
				if !errors.Is(err, test.wantErr) {
					t.Errorf("MonitorIDandName() error = %v, wantErr %v", err, test.wantErr)
				}
				return
			}
			if test.wantMonitor != nil {
				if got == nil {
					t.Error("MonitorIDandName() returned nil monitor when monitor was expected")
					return
				}
				if got.DeviceModel != test.wantMonitor.DeviceModel {
					t.Errorf("MonitorIDandName() DeviceModel = %v, want %v", got.DeviceModel, test.wantMonitor.DeviceModel)
				}
				if got.MonitorId != test.wantMonitor.MonitorId {
					t.Errorf("MonitorIDandName() MonitorId = %v, want %v", got.MonitorId, test.wantMonitor.MonitorId)
				}
				if got.MonitorName != test.wantMonitor.MonitorName {
					t.Errorf("MonitorIDandName() MonitorName = %v, want %v", got.MonitorName, test.wantMonitor.MonitorName)
				}
			}
		})
	}
}

func buildValidMonitorIDandNameMsg() []byte {
	msg := make([]byte, HeaderLength+K2018IDNameLength+1)

	// Set header bytes (all zeros for test)
	for i := 0; i < HeaderLength; i++ {
		msg[i] = 0x00
	}

	// Set monitor ID (12345)
	msg[MonitorIDlsByteIndicator] = 0x39 // 57
	msg[MonitorIDmsByteIndicator] = 0x30 // 48

	// Set monitor name
	copy(msg[HeaderLength:], []byte("Test Monitor"))

	// Add checksum
	msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1])

	return msg
}
