package edicmu2212

import (
	"fmt"
	"strings"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

const (
	PFCMU2LogLength  = 273
	PFCMU2LogLengthJ = 274
	ScalePoint220    = 240 // Voltage scaling threshold
	ScalePoint2212   = 112 // Assumed for current scaling
)

// Cabinet status bit masks
const (
	CabLocalFlashBit  = 0x80
	CabMCCoilBit      = 0x10
	CabMCSecondaryBit = 0x20
	CabFTRCoilBit     = 0x01
	CabFrontDoorBit   = 0x02
	CabRearDoorBit    = 0x08
)

/*
Byte Message Layout
===================================

+-------------------------------+
|     Header (7 bytes)          |
+-------------------------------+
|   Number of records (1 byte)  |
+-------------------------------+
|  Record #1 (273/274 bytes)    |
+-------------------------------+
|  Record #2 (273/274 bytes)    |
+-------------------------------+
|              ...              |
+-------------------------------+
|  Record #N (273/274 bytes)    |
+-------------------------------+
|     Checksum (1 byte)         |
+-------------------------------+


Detailed Byte Message Layout
===================================

Header and number of records (8 bytes)
---------------
[0] Message type identifier
[1] Device address
[2] Command code
[3] Response status
[4] Reserved byte
[5] Reserved byte
[6] Reserved byte
[7] Number of records (0-255)

Record Structure (273 bytes per record / 274 for Issue J+)
-----------------------------------
[0] Fault code
[1-4] Fault status
[5-8] Red status
[9-12] Yellow status
[13-16] Green status
[17-20] Red Field Check Status
[21-24] Yellow Field Check Status
[25-28] Green Field Check Status
[29] Cabinet Status
[30] Reversed byte
[31] AC line voltage
[32-47] Signal voltages
[48-79] Red RMS voltages
[80-111] Yellow RMS voltages
[112-143] Green RMS voltages
[144-175] Red RMS currents
[176-207] Yellow RMS currents
[208-239] Green RMS currents
[240] Second in BCD format (00-59)
[241] Minute in BCD format (00-59)
[242] Hour in BCD format (00-23)
[243] Day in BCD format (01-31)
[244] Month in BCD format (01-12)
[245] Year in BCD format (00-99)
[246] 24Vdc Supply
[247] 12Vdc Supply
[248] Temperature in Celsius
[249-252] Red current status
[253-256] Yellow current status
[257-260] Green current status
[261-264] Red Field Check Status
[265-268] Yellow Field Check Status
[269-272] Green Field Check Status
[273] 48Vdc Supply (Issue J+)

Checksum (1 byte)
----------------
[Last byte] Additive checksum (one's complement of sum of all previous bytes)
*/

// LogPreviousFail parses the previous fail log from the byte message
func (device EDICMU2212) LogPreviousFail(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogPreviousFailRecords, err error) {
	if err = helper.ValidateChecksum(byteMsg); err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}
	commVersion := helper.ConvertBCDStringToInt(header.CommVersion)
	numRecords := int(byteMsg[HeaderLength])
	byteMessageLength := func() int {
		if commVersion > 0x39 {
			return HeaderLength + numRecords*PFCMU2LogLengthJ + HeaderOffset
		}
		return HeaderLength + numRecords*PFCMU2LogLength + HeaderOffset
	}()

	if !helper.VerifyByteLen(byteMsg, byteMessageLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), byteMessageLength)
	}

	allRecords = &helper.LogPreviousFailRecords{
		DeviceModel: header.Model.String(),
		RawMessage:  byteMsg,
	}

	for idx := range numRecords {
		record, err := parsePreviousFailRecord(byteMsg, HeaderLength+1+idx*PFCMU2LogLength, httpHeader, header)
		if err != nil {
			return nil, fmt.Errorf("failed to parse record %d: %w", idx, err)
		}
		allRecords.Records = append(allRecords.Records, *record)
	}

	return allRecords, nil
}

func parsePreviousFailRecord(byteMsg []byte, offset int, httpHeader *pubsubdata.HeaderDetails, header *helper.HeaderRecord) (record *helper.LogPreviousFailRecord, err error) {
	record = &helper.LogPreviousFailRecord{}

	// Extract fault code
	faultCode := byteMsg[offset]
	commVersion := helper.ConvertBCDStringToInt(header.CommVersion)
	// Mains voltage
	record.AcLine = parseACLineVoltage(int(byteMsg[offset+31]), header)
	// 48Vdc supply (byte 283, Issue J+)
	if commVersion > 0x39 && header.VoltDC {
		record.T48VDCSignalBus = fmt.Sprintf("%.1f Vrms", float64(byteMsg[offset+273])/4)
	} else {
		record.T48VDCSignalBus = "0.0 Vrms"
	}
	// 24Vdc supply
	record.T24VDCInput = fmt.Sprintf("%.1f Vrms", float64(byteMsg[offset+246])/4)
	// 12Vdc supply
	record.T12VDCInput = fmt.Sprintf("%.1f Vrms", float64(byteMsg[offset+247])/4)

	// Parse RMS voltages
	record.ChannelRedRmsVoltage = parseVoltages(byteMsg[offset+48:offset+80], header)
	record.ChannelYellowRmsVoltage = parseVoltages(byteMsg[offset+80:offset+112], header)
	record.ChannelGreenRmsVoltage = parseVoltages(byteMsg[offset+112:offset+144], header)

	// Parse RMS currents
	record.ChannelRedRmsCurrent = parseCurrents(byteMsg[offset+144 : offset+176])
	record.ChannelYellowRmsCurrent = parseCurrents(byteMsg[offset+176 : offset+208])
	record.ChannelGreenRmsCurrent = parseCurrents(byteMsg[offset+208 : offset+240])

	// Parse cabinet status
	cabStatus := byteMsg[offset+29]
	record.LsFlashBit = cabStatus&CabLocalFlashBit != 0
	record.RedEnable = strTernary(cabStatus&CabMCCoilBit != 0, "Active", "Off")
	record.MCCoilEE = strTernary(cabStatus&CabMCSecondaryBit != 0, "Active", "Off")
	record.SpecialFunction1 = strTernary(cabStatus&CabFTRCoilBit != 0, "Auto", "Flash")
	record.SpecialFunction2 = strTernary(cabStatus&CabFrontDoorBit != 0, "Open", "Closed")

	// Temperature in Farenheit
	record.Temperature = int64(byteMsg[offset+248]) - 40

	// Datetime
	record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
		byteMsg[offset+244], byteMsg[offset+243],
		byteMsg[offset+245], byteMsg[offset+242],
		byteMsg[offset+241], byteMsg[offset+240],
		httpHeader.GatewayTimezone,
	)
	if err != nil {
		return nil, err
	}

	// Parse multi-byte status fields
	faultStatusLS := helper.CombineBytes(byteMsg[offset+1 : offset+3])
	faultStatusMS := helper.CombineBytes(byteMsg[offset+3 : offset+5])
	faultStatus := helper.CombineBytes(byteMsg[offset+1 : offset+5])
	redStatus := helper.CombineBytes(byteMsg[offset+5 : offset+9])
	yellowStatus := helper.CombineBytes(byteMsg[offset+9 : offset+13])
	greenStatus := helper.CombineBytes(byteMsg[offset+13 : offset+17])
	redFCStatus := helper.CombineBytes(byteMsg[offset+17 : offset+21])
	yellowFCStatus := helper.CombineBytes(byteMsg[offset+21 : offset+25])
	greenFCStatus := helper.CombineBytes(byteMsg[offset+25 : offset+29])
	redCurrentStatus := helper.CombineBytes(byteMsg[offset+249 : offset+253])
	yellowCurrentStatus := helper.CombineBytes(byteMsg[offset+253 : offset+257])
	greenCurrentStatus := helper.CombineBytes(byteMsg[offset+257 : offset+261])
	redRPStatus := helper.CombineBytes(byteMsg[offset+261 : offset+265])
	yellowRPStatus := helper.CombineBytes(byteMsg[offset+265 : offset+269])
	greenRPStatus := helper.CombineBytes(byteMsg[offset+269 : offset+273])

	// Hydrate the record
	record.Fault = getFaultMessage(faultCode, faultStatusLS, faultStatusMS)
	record.FaultStatus = parseStatus(faultStatus, 4)
	record.ChannelRedStatus = parseStatus(redStatus, 4)
	record.ChannelYellowStatus = parseStatus(yellowStatus, 4)
	record.ChannelGreenStatus = parseStatus(greenStatus, 4)
	record.ChannelRedFieldCheckStatus = parseStatus(redFCStatus, 4)
	record.ChannelYellowFieldCheckStatus = parseStatus(yellowFCStatus, 4)
	record.ChannelGreenFieldCheckStatus = parseStatus(greenFCStatus, 4)
	record.ChannelRedRecurrentPulseStatus = parseStatus(redRPStatus, 4)
	record.ChannelYellowRecurrentPulseStatus = parseStatus(yellowRPStatus, 4)
	record.ChannelGreenRecurrentPulseStatus = parseStatus(greenRPStatus, 4)
	record.ChannelRedCurrentStatus = parseStatus(redCurrentStatus, 4)
	record.ChannelYellowCurrentStatus = parseStatus(yellowCurrentStatus, 4)
	record.ChannelGreenCurrentStatus = parseStatus(greenCurrentStatus, 4)
	if faultCode == 3 {
		record.NextConflictingChannels = parseStatus(faultStatus, 4)
	}

	return record, nil
}

func getFaultMessage(faultCode byte, faultStatusLS uint32, faultStatusMS uint32) string {
	switch faultCode {
	case 1:
		if faultStatusLS == 0 && faultStatusMS == 0 {
			return "24Vdc Fault: CMU"
		}
		return "24Vdc Fault: HDSP:" + getHDSPSlots(faultStatusLS, faultStatusMS)
	case 2:
		return "12Vdc Fault"
	case 3:
		return "Conflict Fault"
	case 4:
		return "Serial Bus #1 Error"
	case 5:
		return "Serial Bus #3 Error - HDSP:" + getHDSPSlots(faultStatusLS, faultStatusMS)
	case 6:
		return "CU Frame-62 Latched Flash (LFSA)"
	case 7:
		return "CU Frame-62 Non-Latched Flash (NFSA)"
	case 8:
		switch faultStatusLS {
		case 737:
			return "Diagnostic Fault (Logic Data error)"
		case 184:
			return "Diagnostic Fault (Datalog EEprom error)"
		case 147:
			return "Diagnostic Fault (Logic Comm error)"
		case 122:
			return "Diagnostic Fault (trap error)"
		default:
			return "Diagnostic Fault (not specified)"
		}
	case 9:
		return "Multiple Indication Fault"
	case 10:
		return "Lack of Signal Fault"
	case 11:
		return "Clearance (Short Yellow) Fault"
	case 12:
		return "Clearance (Skipped Yellow) Fault"
	case 13:
		return "Yellow + Red Clearance Fault"
	case 14:
		return "Field Output Check Fault"
	case 15:
		return "Data Key Absent"
	case 16:
		return "Data Key FCS Error"
	case 17:
		return "Data Key Invalid Parameter Error"
	case 18:
		return "Local Flash"
	case 19:
		return "Circuit Breaker Trip"
	case 22:
		return "HDSP Diagnostic Error - HDSP:" + getHDSPSlots(faultStatusLS, faultStatusMS)
	case 23:
		return "FYA Flash Rate Fault"
	case 24, 133:
		return "48Vdc Fault"
	case 129:
		return "Recurrent Pulse Conflict Fault"
	case 130:
		return "Recurrent Pulse Lack of Signal Fault"
	case 131:
		return "Recurrent Pulse Multiple Fault"
	case 132:
		return "Configuration Change Fault"
	case 134:
		return "CU Watchdog Fault"
	case 135:
		return "Conflict Fault, FYA Yellow Trap"
	default:
		return fmt.Sprintf("Undefined Fault Type Error (%d)", faultCode)
	}
}

// getHDSPSlots converts fault status to HDSP slot numbers
func getHDSPSlots(faultStatusLS, faultStatusMS uint32) string {
	var slots []string
	mask := uint32(0x3)
	for i := 0; i < 8; i++ {
		if faultStatusLS&mask != 0 {
			slots = append(slots, fmt.Sprintf("%d", i+1))
		}
		mask <<= 2
	}
	mask = 0x3
	for i := 8; i < 16; i++ {
		if faultStatusMS&mask != 0 {
			slots = append(slots, fmt.Sprintf("%d", i+1))
		}
		mask <<= 2
	}
	if len(slots) == 0 {
		return ""
	}
	return strings.Join(slots, ",")
}

// parseACLineVoltage processes the mains voltage
func parseACLineVoltage(volt int, header *helper.HeaderRecord) string {
	if header.Volt220 && volt > ScalePoint220 {
		volt = (volt-ScalePoint220)*2 + ScalePoint220
	}
	if header.MainsDC {
		volt /= 4
	}
	return fmt.Sprintf("%d Vrms", volt)
}

// strTernary returns a string based on a boolean condition
func strTernary(condition bool, trueStr, falseStr string) string {
	if condition {
		return trueStr
	}
	return falseStr
}

// parseVoltages parses the raw voltage bytes into a slice of int32
// It handles voltage scaling for 220V and DC voltages
func parseVoltages(rawBytes []byte, header *helper.HeaderRecord) []int32 {
	voltages := make([]int32, len(rawBytes))
	for i := range len(rawBytes) {
		voltages[i] = func() int32 {
			if header.Volt220 && rawBytes[i] > ScalePoint220 {
				return (int32(rawBytes[i])-ScalePoint220)*2 + ScalePoint220
			}
			if header.VoltDC {
				return int32(rawBytes[i]) / 4
			}
			return int32(rawBytes[i])
		}()
	}
	return voltages
}

// parseCurrents extracts RMS currents with scaling
func parseCurrents(rawBytes []byte) []int32 {
	currents := make([]int32, len(rawBytes))
	for i := range len(rawBytes) {
		currents[i] = func() int32 {
			if rawBytes[i] > ScalePoint2212 {
				return (int32(rawBytes[i]))*16 - 1800
			}
			return int32(rawBytes[i])
		}()
	}
	return currents
}
