package edicmu2212

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestLogPreviousFail(t *testing.T) {
	// Create a test byte message with header and one record
	byteMsg := make([]byte, HeaderLength+1+PFCMU2LogLength+1) // 7 (header) + 1 (number of records) + 273 (record) + 1 (checksum)

	// Initialize all bytes to 0 first
	for i := range byteMsg {
		byteMsg[i] = 0x00
	}

	// Set header bytes
	byteMsg[0] = 0x01 // Message type
	byteMsg[1] = 0x02 // Device address
	byteMsg[2] = 0x03 // Command code
	byteMsg[3] = 0x00 // Response status
	byteMsg[4] = 0x00 // Reserved
	byteMsg[5] = 0x00 // Reserved
	byteMsg[6] = 0x00 // Reserved
	byteMsg[7] = 0x01 // Number of records

	// Set record bytes (starting at offset 8)
	offset := HeaderLength + 1 // Start after header

	// Set fault code (byte 0)
	byteMsg[offset] = 3 // Conflict Fault

	// Set fault status (bytes 1-4)
	byteMsg[offset+1] = 0x01 // First bit set in first byte
	byteMsg[offset+2] = 0x00
	byteMsg[offset+3] = 0x00
	byteMsg[offset+4] = 0x00

	// Set channel status (bytes 5-28)
	// Red status (bytes 5-8)
	byteMsg[offset+5] = 0x01
	byteMsg[offset+6] = 0x00
	byteMsg[offset+7] = 0x00
	byteMsg[offset+8] = 0x00

	// Yellow status (bytes 9-12)
	byteMsg[offset+9] = 0x01
	byteMsg[offset+10] = 0x00
	byteMsg[offset+11] = 0x00
	byteMsg[offset+12] = 0x00

	// Green status (bytes 13-16)
	byteMsg[offset+13] = 0x01
	byteMsg[offset+14] = 0x00
	byteMsg[offset+15] = 0x00
	byteMsg[offset+16] = 0x00

	// Set cabinet status (byte 29)
	byteMsg[offset+29] = 0x33 // MCCoilBit | MCSecondaryBit | FTRCoilBit | FrontDoorBit

	// Set AC line voltage (byte 31)
	byteMsg[offset+31] = 120

	// Set channel voltages (bytes 48-143)
	// Red channel voltages (bytes 48-79)
	for i := 48; i < 80; i++ {
		byteMsg[offset+i] = 120
	}

	// Yellow channel voltages (bytes 80-111)
	for i := 80; i < 112; i++ {
		byteMsg[offset+i] = 120
	}

	// Green channel voltages (bytes 112-143)
	for i := 112; i < 144; i++ {
		byteMsg[offset+i] = 120
	}

	// Set channel currents (bytes 144-239)
	// Red channel currents (bytes 144-175)
	for i := 144; i < 176; i++ {
		byteMsg[offset+i] = 100
	}

	// Yellow channel currents (bytes 176-207)
	for i := 176; i < 208; i++ {
		byteMsg[offset+i] = 100
	}

	// Green channel currents (bytes 208-239)
	for i := 208; i < 240; i++ {
		byteMsg[offset+i] = 100
	}

	// Set datetime (bytes 240-245)
	byteMsg[offset+240] = 0x30 // second
	byteMsg[offset+241] = 0x45 // minute
	byteMsg[offset+242] = 0x12 // hour
	byteMsg[offset+243] = 0x15 // day
	byteMsg[offset+244] = 0x03 // month
	byteMsg[offset+245] = 0x24 // year

	// Set 24Vdc input (bytes 246-247)
	byteMsg[offset+246] = 120
	byteMsg[offset+247] = 0

	// Set temperature (byte 248)
	byteMsg[offset+248] = 60 // 20 degrees (60-40)

	// Set current status fields (bytes 249-272)
	// Red current status (bytes 249-252)
	for i := 249; i < 253; i++ {
		byteMsg[offset+i] = 0x01
	}

	// Yellow current status (bytes 253-256)
	for i := 253; i < 257; i++ {
		byteMsg[offset+i] = 0x01
	}

	// Green current status (bytes 257-260)
	for i := 257; i < 261; i++ {
		byteMsg[offset+i] = 0x01
	}

	// Set field check status fields (bytes 261-272)
	// Red field check status (bytes 261-264)
	for i := 261; i < 265; i++ {
		byteMsg[offset+i] = 0x01
	}

	// Yellow field check status (bytes 265-268)
	for i := 265; i < 269; i++ {
		byteMsg[offset+i] = 0x01
	}

	// Green field check status (bytes 269-272)
	for i := 269; i < 273; i++ {
		byteMsg[offset+i] = 0x01
	}

	// Calculate and set checksum
	byteMsg[len(byteMsg)-1] = computeChecksum(byteMsg)

	// Create test header
	header := &helper.HeaderRecord{
		Volt220:     false,
		MainsDC:     false,
		CommVersion: helper.ConvertByteToDecimalFormat(0x35),
		VoltDC:      false,
		Model:       helper.CMU2212_lv,
	}

	// Create test HTTP header
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}

	// Create a test message for Issue J+ (CommVersion > 0x39)
	byteMsgJ := make([]byte, 8+PFCMU2LogLengthJ+1)   // 8 (header) + 274 (record) + 1 (checksum)
	copy(byteMsgJ, byteMsg[:8])                      // Copy header
	byteMsgJ[7] = 0x01                               // Number of records
	copy(byteMsgJ[8:], byteMsg[8:8+PFCMU2LogLength]) // Copy record
	byteMsgJ[8+PFCMU2LogLength] = 120                // Set 48Vdc value
	byteMsgJ[len(byteMsgJ)-1] = computeChecksum(byteMsgJ)

	// Create a test message with multiple records
	byteMsgMulti := make([]byte, 8+PFCMU2LogLength*2+1)                  // 8 (header) + 273*2 (records) + 1 (checksum)
	copy(byteMsgMulti, byteMsg[:8])                                      // Copy header
	byteMsgMulti[7] = 0x02                                               // Two records
	copy(byteMsgMulti[8:], byteMsg[8:8+PFCMU2LogLength])                 // Copy first record
	copy(byteMsgMulti[8+PFCMU2LogLength:], byteMsg[8:8+PFCMU2LogLength]) // Copy second record
	byteMsgMulti[len(byteMsgMulti)-1] = computeChecksum(byteMsgMulti)

	tests := []struct {
		name       string
		byteMsg    []byte
		header     *helper.HeaderRecord
		httpHeader *pubsubdata.HeaderDetails
		want       *helper.LogPreviousFailRecords
		wantErr    bool
	}{
		{
			name:       "valid message",
			byteMsg:    byteMsg,
			header:     header,
			httpHeader: httpHeader,
			want: &helper.LogPreviousFailRecords{
				DeviceModel: header.Model.String(),
				RawMessage:  byteMsg,
				Records: []helper.LogPreviousFailRecord{
					{
						Fault:            "Conflict Fault",
						Temperature:      20,
						AcLine:           "120 Vrms",
						RedEnable:        "Active",
						MCCoilEE:         "Active",
						SpecialFunction1: "Auto",
						SpecialFunction2: "Open",
						T48VDCSignalBus:  "0.0 Vrms",
						T24VDCInput:      "30.0 Vrms",
						T12VDCInput:      "0.0 Vrms",
					},
				},
			},
			wantErr: false,
		},
		{
			name:       "valid message Issue J+",
			byteMsg:    byteMsgJ,
			header:     &helper.HeaderRecord{Volt220: false, MainsDC: false, CommVersion: helper.ConvertByteToDecimalFormat(0x40), VoltDC: true, Model: helper.CMU2212_lv},
			httpHeader: httpHeader,
			want: &helper.LogPreviousFailRecords{
				DeviceModel: header.Model.String(),
				RawMessage:  byteMsgJ,
				Records: []helper.LogPreviousFailRecord{
					{
						Fault:            "Conflict Fault",
						Temperature:      20,
						AcLine:           "120 Vrms",
						RedEnable:        "Active",
						MCCoilEE:         "Active",
						SpecialFunction1: "Auto",
						SpecialFunction2: "Open",
						T48VDCSignalBus:  "30.0 Vrms",
						T24VDCInput:      "30.0 Vrms",
						T12VDCInput:      "0.0 Vrms",
					},
				},
			},
			wantErr: false,
		},
		{
			name:       "valid message multiple records",
			byteMsg:    byteMsgMulti,
			header:     header,
			httpHeader: httpHeader,
			want: &helper.LogPreviousFailRecords{
				DeviceModel: header.Model.String(),
				RawMessage:  byteMsgMulti,
				Records: []helper.LogPreviousFailRecord{
					{
						Fault:            "Conflict Fault",
						Temperature:      20,
						AcLine:           "120 Vrms",
						RedEnable:        "Active",
						MCCoilEE:         "Active",
						SpecialFunction1: "Auto",
						SpecialFunction2: "Open",
						T48VDCSignalBus:  "0.0 Vrms",
						T24VDCInput:      "30.0 Vrms",
						T12VDCInput:      "0.0 Vrms",
					},
					{
						Fault:            "Conflict Fault",
						Temperature:      20,
						AcLine:           "120 Vrms",
						RedEnable:        "Active",
						MCCoilEE:         "Active",
						SpecialFunction1: "Auto",
						SpecialFunction2: "Open",
						T48VDCSignalBus:  "0.0 Vrms",
						T24VDCInput:      "30.0 Vrms",
						T12VDCInput:      "0.0 Vrms",
					},
				},
			},
			wantErr: false,
		},
		{
			name:       "invalid message length",
			byteMsg:    byteMsg[:len(byteMsg)-1], // Remove last byte
			header:     header,
			httpHeader: httpHeader,
			want:       nil,
			wantErr:    true,
		},
		{
			name:       "invalid message length Issue J+",
			byteMsg:    byteMsgJ[:len(byteMsgJ)-1], // Remove last byte
			header:     &helper.HeaderRecord{Volt220: false, MainsDC: false, CommVersion: helper.ConvertByteToDecimalFormat(0x40), VoltDC: true, Model: helper.CMU2212_lv},
			httpHeader: httpHeader,
			want:       nil,
			wantErr:    true,
		},
		{
			name: "invalid record parsing",
			byteMsg: func() []byte {
				msg := make([]byte, len(byteMsg))
				copy(msg, byteMsg)
				msg[HeaderLength+1+244] = 0x13 // Invalid month (13)
				// Recalculate checksum
				msg[len(msg)-1] = computeChecksum(msg)
				return msg
			}(),
			header:     header,
			httpHeader: httpHeader,
			want:       nil,
			wantErr:    true,
		},
		{
			name: "invalid byte length with valid checksum",
			byteMsg: func() []byte {
				// Create a message with header and number of records, but missing record bytes
				msg := make([]byte, HeaderLength+1+1) // 7 (header) + 1 (num records) + 1 (checksum)
				msg[0] = 0x01                         // Message type
				msg[1] = 0x02                         // Device address
				msg[2] = 0x03                         // Command code
				msg[3] = 0x00                         // Response status
				msg[4] = 0x00                         // Reserved
				msg[5] = 0x00                         // Reserved
				msg[6] = 0x00                         // Reserved
				msg[7] = 0x01                         // Number of records (should require a full record, but we omit it)
				// Compute checksum for this short message
				msg[len(msg)-1] = computeChecksum(msg)
				return msg
			}(),
			header:     header,
			httpHeader: httpHeader,
			want:       nil,
			wantErr:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			device := EDICMU2212{}
			got, err := device.LogPreviousFail(tt.httpHeader, tt.byteMsg, tt.header)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.want.DeviceModel, got.DeviceModel)
			assert.Equal(t, tt.want.RawMessage, got.RawMessage)
			assert.Equal(t, len(tt.want.Records), len(got.Records))
			for i := range tt.want.Records {
				assert.Equal(t, tt.want.Records[i].Fault, got.Records[i].Fault)
				assert.Equal(t, tt.want.Records[i].Temperature, got.Records[i].Temperature)
				assert.Equal(t, tt.want.Records[i].AcLine, got.Records[i].AcLine)
				assert.Equal(t, tt.want.Records[i].RedEnable, got.Records[i].RedEnable)
				assert.Equal(t, tt.want.Records[i].MCCoilEE, got.Records[i].MCCoilEE)
				assert.Equal(t, tt.want.Records[i].SpecialFunction1, got.Records[i].SpecialFunction1)
				assert.Equal(t, tt.want.Records[i].SpecialFunction2, got.Records[i].SpecialFunction2)
				assert.Equal(t, tt.want.Records[i].T48VDCSignalBus, got.Records[i].T48VDCSignalBus)
				assert.Equal(t, tt.want.Records[i].T24VDCInput, got.Records[i].T24VDCInput)
				assert.Equal(t, tt.want.Records[i].T12VDCInput, got.Records[i].T12VDCInput)
			}
		})
	}
}

func TestStrTernary(t *testing.T) {
	tests := []struct {
		name      string
		condition bool
		trueStr   string
		falseStr  string
		want      string
	}{
		{
			name:      "true condition returns true string",
			condition: true,
			trueStr:   "Active",
			falseStr:  "Off",
			want:      "Active",
		},
		{
			name:      "false condition returns false string",
			condition: false,
			trueStr:   "Active",
			falseStr:  "Off",
			want:      "Off",
		},
		{
			name:      "empty strings",
			condition: true,
			trueStr:   "",
			falseStr:  "",
			want:      "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := strTernary(tt.condition, tt.trueStr, tt.falseStr)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestParseCurrents(t *testing.T) {
	tests := []struct {
		name     string
		rawBytes []byte
		want     []int32
	}{
		{
			name:     "empty input",
			rawBytes: []byte{},
			want:     []int32{},
		},
		{
			name:     "values below scale point",
			rawBytes: []byte{100, 110, 112},
			want:     []int32{100, 110, 112},
		},
		{
			name:     "values above scale point",
			rawBytes: []byte{113, 120, 130},
			want:     []int32{8, 120, 280}, // (113*16)-1800=8, (120*16)-1800=120, (130*16)-1800=280
		},
		{
			name:     "mixed values",
			rawBytes: []byte{100, 113, 120, 112},
			want:     []int32{100, 8, 120, 112},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := parseCurrents(tt.rawBytes)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestGetHDSPSlots(t *testing.T) {
	tests := []struct {
		name          string
		faultStatusLS uint32
		faultStatusMS uint32
		want          string
	}{
		{
			name:          "no slots active",
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "",
		},
		{
			name:          "first slot active",
			faultStatusLS: 0x3,
			faultStatusMS: 0,
			want:          "1",
		},
		{
			name:          "multiple slots in LS",
			faultStatusLS: 0x3 | 0xC, // slots 1 and 2
			faultStatusMS: 0,
			want:          "1,2",
		},
		{
			name:          "multiple slots in MS",
			faultStatusLS: 0,
			faultStatusMS: 0x3 | 0xC, // slots 9 and 10
			want:          "9,10",
		},
		{
			name:          "slots in both LS and MS",
			faultStatusLS: 0x3, // slot 1
			faultStatusMS: 0x3, // slot 9
			want:          "1,9",
		},
		{
			name:          "all slots active",
			faultStatusLS: 0xFFFF,
			faultStatusMS: 0xFFFF,
			want:          "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getHDSPSlots(tt.faultStatusLS, tt.faultStatusMS)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestParseACLineVoltage(t *testing.T) {
	tests := []struct {
		name   string
		volt   int
		header *helper.HeaderRecord
		want   string
	}{
		{
			name:   "standard voltage",
			volt:   120,
			header: &helper.HeaderRecord{Volt220: false, MainsDC: false},
			want:   "120 Vrms",
		},
		{
			name:   "220V scaling below threshold",
			volt:   200,
			header: &helper.HeaderRecord{Volt220: true, MainsDC: false},
			want:   "200 Vrms",
		},
		{
			name:   "220V scaling above threshold",
			volt:   250,
			header: &helper.HeaderRecord{Volt220: true, MainsDC: false},
			want:   "260 Vrms", // (250-240)*2 + 240 = 260
		},
		{
			name:   "DC voltage",
			volt:   120,
			header: &helper.HeaderRecord{Volt220: false, MainsDC: true},
			want:   "30 Vrms", // 120/4 = 30
		},
		{
			name:   "220V DC voltage",
			volt:   250,
			header: &helper.HeaderRecord{Volt220: true, MainsDC: true},
			want:   "65 Vrms", // ((250-240)*2 + 240)/4 = 65
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := parseACLineVoltage(tt.volt, tt.header)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestParseVoltages(t *testing.T) {
	tests := []struct {
		name     string
		rawBytes []byte
		header   *helper.HeaderRecord
		want     []int32
	}{
		{
			name:     "empty input",
			rawBytes: []byte{},
			header:   &helper.HeaderRecord{Volt220: false, VoltDC: false},
			want:     []int32{},
		},
		{
			name:     "standard voltages",
			rawBytes: []byte{120, 130, 140},
			header:   &helper.HeaderRecord{Volt220: false, VoltDC: false},
			want:     []int32{120, 130, 140},
		},
		{
			name:     "220V scaling below threshold",
			rawBytes: []byte{200, 210, 220},
			header:   &helper.HeaderRecord{Volt220: true, VoltDC: false},
			want:     []int32{200, 210, 220},
		},
		{
			name:     "220V scaling above threshold",
			rawBytes: []byte{245, 246, 247},
			header:   &helper.HeaderRecord{Volt220: true, VoltDC: false},
			want:     []int32{250, 252, 254}, // (245-240)*2 + 240 = 250, etc.
		},
		{
			name:     "DC voltages",
			rawBytes: []byte{120, 130, 140},
			header:   &helper.HeaderRecord{Volt220: false, VoltDC: true},
			want:     []int32{30, 32, 35}, // 120/4 = 30, etc.
		},
		{
			name:     "220V DC voltages",
			rawBytes: []byte{245, 246, 247},
			header:   &helper.HeaderRecord{Volt220: true, VoltDC: true},
			want:     []int32{250, 252, 254}, // Updated to match implementation: (245-240)*2 + 240 = 250, etc.
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := parseVoltages(tt.rawBytes, tt.header)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestGetFaultMessage(t *testing.T) {
	tests := []struct {
		name          string
		faultCode     byte
		faultStatusLS uint32
		faultStatusMS uint32
		want          string
	}{
		{
			name:          "24Vdc Fault: CMU",
			faultCode:     1,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "24Vdc Fault: CMU",
		},
		{
			name:          "24Vdc Fault: HDSP",
			faultCode:     1,
			faultStatusLS: 0x3,
			faultStatusMS: 0,
			want:          "24Vdc Fault: HDSP:1",
		},
		{
			name:          "12Vdc Fault",
			faultCode:     2,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "12Vdc Fault",
		},
		{
			name:          "Conflict Fault",
			faultCode:     3,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Conflict Fault",
		},
		{
			name:          "Serial Bus #1 Error",
			faultCode:     4,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Serial Bus #1 Error",
		},
		{
			name:          "Serial Bus #3 Error with HDSP",
			faultCode:     5,
			faultStatusLS: 0x3,
			faultStatusMS: 0,
			want:          "Serial Bus #3 Error - HDSP:1",
		},
		{
			name:          "Diagnostic Fault - Logic Data error",
			faultCode:     8,
			faultStatusLS: 737,
			faultStatusMS: 0,
			want:          "Diagnostic Fault (Logic Data error)",
		},
		{
			name:          "Diagnostic Fault - Datalog EEprom error",
			faultCode:     8,
			faultStatusLS: 184,
			faultStatusMS: 0,
			want:          "Diagnostic Fault (Datalog EEprom error)",
		},
		{
			name:          "Diagnostic Fault - Logic Comm error",
			faultCode:     8,
			faultStatusLS: 147,
			faultStatusMS: 0,
			want:          "Diagnostic Fault (Logic Comm error)",
		},
		{
			name:          "Diagnostic Fault - trap error",
			faultCode:     8,
			faultStatusLS: 122,
			faultStatusMS: 0,
			want:          "Diagnostic Fault (trap error)",
		},
		{
			name:          "Diagnostic Fault - not specified",
			faultCode:     8,
			faultStatusLS: 100,
			faultStatusMS: 0,
			want:          "Diagnostic Fault (not specified)",
		},
		{
			name:          "Multiple Indication Fault",
			faultCode:     9,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Multiple Indication Fault",
		},
		{
			name:          "Lack of Signal Fault",
			faultCode:     10,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Lack of Signal Fault",
		},
		{
			name:          "Clearance (Short Yellow) Fault",
			faultCode:     11,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Clearance (Short Yellow) Fault",
		},
		{
			name:          "Clearance (Skipped Yellow) Fault",
			faultCode:     12,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Clearance (Skipped Yellow) Fault",
		},
		{
			name:          "Yellow + Red Clearance Fault",
			faultCode:     13,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Yellow + Red Clearance Fault",
		},
		{
			name:          "Field Output Check Fault",
			faultCode:     14,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Field Output Check Fault",
		},
		{
			name:          "Data Key Absent",
			faultCode:     15,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Data Key Absent",
		},
		{
			name:          "Data Key FCS Error",
			faultCode:     16,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Data Key FCS Error",
		},
		{
			name:          "Data Key Invalid Parameter Error",
			faultCode:     17,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Data Key Invalid Parameter Error",
		},
		{
			name:          "Local Flash",
			faultCode:     18,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Local Flash",
		},
		{
			name:          "Circuit Breaker Trip",
			faultCode:     19,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Circuit Breaker Trip",
		},
		{
			name:          "HDSP Diagnostic Error",
			faultCode:     22,
			faultStatusLS: 0x3,
			faultStatusMS: 0,
			want:          "HDSP Diagnostic Error - HDSP:1",
		},
		{
			name:          "FYA Flash Rate Fault",
			faultCode:     23,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "FYA Flash Rate Fault",
		},
		{
			name:          "48Vdc Fault",
			faultCode:     24,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "48Vdc Fault",
		},
		{
			name:          "Recurrent Pulse Conflict Fault",
			faultCode:     129,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Recurrent Pulse Conflict Fault",
		},
		{
			name:          "Recurrent Pulse Lack of Signal Fault",
			faultCode:     130,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Recurrent Pulse Lack of Signal Fault",
		},
		{
			name:          "Recurrent Pulse Multiple Fault",
			faultCode:     131,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Recurrent Pulse Multiple Fault",
		},
		{
			name:          "Configuration Change Fault",
			faultCode:     132,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Configuration Change Fault",
		},
		{
			name:          "48Vdc Fault (alt code)",
			faultCode:     133,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "48Vdc Fault",
		},
		{
			name:          "CU Watchdog Fault",
			faultCode:     134,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "CU Watchdog Fault",
		},
		{
			name:          "Conflict Fault, FYA Yellow Trap",
			faultCode:     135,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Conflict Fault, FYA Yellow Trap",
		},
		{
			name:          "Undefined Fault Type",
			faultCode:     255,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "Undefined Fault Type Error (255)",
		},
		{
			name:          "CU Frame-62 Latched Flash (LFSA)",
			faultCode:     6,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "CU Frame-62 Latched Flash (LFSA)",
		},
		{
			name:          "CU Frame-62 Non-Latched Flash (NFSA)",
			faultCode:     7,
			faultStatusLS: 0,
			faultStatusMS: 0,
			want:          "CU Frame-62 Non-Latched Flash (NFSA)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getFaultMessage(tt.faultCode, tt.faultStatusLS, tt.faultStatusMS)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestParsePreviousFailRecord(t *testing.T) {
	// Create a test byte message that matches the layout described in the comments
	// We'll create a minimal valid message with one record
	byteMsg := make([]byte, PFCMU2LogLength)

	// Set fault code (byte 0)
	byteMsg[0] = 3 // Conflict Fault

	// Set fault status (bytes 1-4)
	byteMsg[1] = 0x01 // First bit set in first byte
	byteMsg[2] = 0x00
	byteMsg[3] = 0x00
	byteMsg[4] = 0x00

	// Set channel status (bytes 5-28)
	// Red status (bytes 5-8)
	byteMsg[5] = 0x01
	byteMsg[6] = 0x00
	byteMsg[7] = 0x00
	byteMsg[8] = 0x00

	// Yellow status (bytes 9-12)
	byteMsg[9] = 0x01
	byteMsg[10] = 0x00
	byteMsg[11] = 0x00
	byteMsg[12] = 0x00

	// Green status (bytes 13-16)
	byteMsg[13] = 0x01
	byteMsg[14] = 0x00
	byteMsg[15] = 0x00
	byteMsg[16] = 0x00

	// Set cabinet status (byte 29)
	byteMsg[29] = 0x33 // MCCoilBit | MCSecondaryBit | FTRCoilBit | FrontDoorBit

	// Set AC line voltage (byte 31)
	byteMsg[31] = 120

	// Set channel voltages (bytes 48-143)
	// Red channel voltages (bytes 48-79)
	for i := 48; i < 80; i++ {
		byteMsg[i] = 120
	}

	// Yellow channel voltages (bytes 80-111)
	for i := 80; i < 112; i++ {
		byteMsg[i] = 120
	}

	// Green channel voltages (bytes 112-143)
	for i := 112; i < 144; i++ {
		byteMsg[i] = 120
	}

	// Set channel currents (bytes 144-239)
	// Red channel currents (bytes 144-175)
	for i := 144; i < 176; i++ {
		byteMsg[i] = 100
	}

	// Yellow channel currents (bytes 176-207)
	for i := 176; i < 208; i++ {
		byteMsg[i] = 100
	}

	// Green channel currents (bytes 208-239)
	for i := 208; i < 240; i++ {
		byteMsg[i] = 100
	}

	// Set datetime (bytes 240-245)
	byteMsg[240] = 0x30 // second
	byteMsg[241] = 0x45 // minute
	byteMsg[242] = 0x12 // hour
	byteMsg[243] = 0x15 // day
	byteMsg[244] = 0x03 // month
	byteMsg[245] = 0x24 // year

	// Set 24Vdc input (bytes 246-247)
	byteMsg[246] = 120
	byteMsg[247] = 0

	// Set temperature (byte 248)
	byteMsg[248] = 60 // 20 degrees (60-40)

	// Set current status fields (bytes 249-272)
	// Red current status (bytes 249-252)
	for i := 249; i < 253; i++ {
		byteMsg[i] = 0x01
	}

	// Yellow current status (bytes 253-256)
	for i := 253; i < 257; i++ {
		byteMsg[i] = 0x01
	}

	// Green current status (bytes 257-260)
	for i := 257; i < 261; i++ {
		byteMsg[i] = 0x01
	}

	// Set field check status fields (bytes 261-272)
	// Red field check status (bytes 261-264)
	for i := 261; i < 265; i++ {
		byteMsg[i] = 0x01
	}

	// Yellow field check status (bytes 265-268)
	for i := 265; i < 269; i++ {
		byteMsg[i] = 0x01
	}

	// Green field check status (bytes 269-272)
	for i := 269; i < 273; i++ {
		byteMsg[i] = 0x01
	}

	// Calculate and set checksum (last byte)
	var sum int32
	for i := 0; i < len(byteMsg)-1; i++ {
		sum += int32(byteMsg[i])
	}
	leastSignificant := sum & 0xff
	byteMsg[len(byteMsg)-1] = byte(^leastSignificant) // One's complement of least significant byte

	// Create test header
	header := &helper.HeaderRecord{
		Volt220:     false,
		MainsDC:     false,
		CommVersion: helper.ConvertByteToDecimalFormat(0x35),
		VoltDC:      false,
	}

	// Create test HTTP header
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "America/New_York",
	}

	tests := []struct {
		name       string
		byteMsg    []byte
		offset     int
		header     *helper.HeaderRecord
		httpHeader *pubsubdata.HeaderDetails
		want       *helper.LogPreviousFailRecord
		wantErr    bool
		wantCheck  func(t *testing.T, rec *helper.LogPreviousFailRecord)
	}{
		{
			name:       "valid record",
			byteMsg:    byteMsg,
			offset:     0,
			header:     header,
			httpHeader: httpHeader,
			wantErr:    false,
			wantCheck: func(t *testing.T, rec *helper.LogPreviousFailRecord) {
				assert.Equal(t, "Conflict Fault", rec.Fault)
				assert.Equal(t, int64(20), rec.Temperature)
				assert.Equal(t, "120 Vrms", rec.AcLine)
				assert.Equal(t, "Active", rec.RedEnable)
				assert.Equal(t, "Active", rec.MCCoilEE)
				assert.Equal(t, "Auto", rec.SpecialFunction1)
				assert.Equal(t, "Open", rec.SpecialFunction2)
			},
		},
		{
			name: "all status fields set, faultCode 3 (conflict)",
			byteMsg: func() []byte {
				msg := make([]byte, PFCMU2LogLength)
				msg[0] = 3 // faultCode == 3
				for i := 1; i < 29; i++ {
					msg[i] = 0xFF
				} // all status bytes nonzero
				msg[29] = 0x80 // cabinet status
				msg[31] = 120  // AC line voltage
				msg[248] = 60  // temperature
				msg[240] = 0x30
				msg[241] = 0x45
				msg[242] = 0x12
				msg[243] = 0x15
				msg[244] = 0x03
				msg[245] = 0x24
				return msg
			}(),
			offset:     0,
			header:     header,
			httpHeader: httpHeader,
			wantErr:    false,
			wantCheck: func(t *testing.T, rec *helper.LogPreviousFailRecord) {
				assert.Equal(t, 32, len(rec.FaultStatus)) // 4 bytes, 8 bits per byte
				assert.Equal(t, 32, len(rec.ChannelRedStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenStatus))
				assert.Equal(t, 32, len(rec.ChannelRedFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelRedRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelRedCurrentStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowCurrentStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenCurrentStatus))
				assert.Equal(t, 32, len(rec.NextConflictingChannels)) // only for faultCode==3
			},
		},
		{
			name: "all status fields zero, faultCode 2",
			byteMsg: func() []byte {
				msg := make([]byte, PFCMU2LogLength)
				msg[0] = 2 // faultCode != 3
				for i := 1; i < 29; i++ {
					msg[i] = 0x00
				} // all status bytes zero
				msg[29] = 0x80 // cabinet status
				msg[31] = 120  // AC line voltage
				msg[248] = 60  // temperature
				msg[240] = 0x30
				msg[241] = 0x45
				msg[242] = 0x12
				msg[243] = 0x15
				msg[244] = 0x03
				msg[245] = 0x24 // datetime
				return msg
			}(),
			offset:     0,
			header:     header,
			httpHeader: httpHeader,
			wantErr:    false,
			wantCheck: func(t *testing.T, rec *helper.LogPreviousFailRecord) {
				assert.Equal(t, 32, len(rec.FaultStatus))
				assert.Equal(t, 32, len(rec.ChannelRedStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenStatus))
				assert.Equal(t, 32, len(rec.ChannelRedFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelRedRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelRedCurrentStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowCurrentStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenCurrentStatus))
				assert.Nil(t, rec.NextConflictingChannels)
			},
		},
		{
			name: "mixed status fields, faultCode 3",
			byteMsg: func() []byte {
				msg := make([]byte, PFCMU2LogLength)
				msg[0] = 3
				for i := 1; i < 29; i++ {
					msg[i] = byte(i)
				}
				msg[29] = 0x33
				msg[31] = 120
				msg[248] = 60
				msg[240] = 0x30
				msg[241] = 0x45
				msg[242] = 0x12
				msg[243] = 0x15
				msg[244] = 0x03
				msg[245] = 0x24
				return msg
			}(),
			offset:     0,
			header:     header,
			httpHeader: httpHeader,
			wantErr:    false,
			wantCheck: func(t *testing.T, rec *helper.LogPreviousFailRecord) {
				assert.Equal(t, 32, len(rec.FaultStatus))
				assert.Equal(t, 32, len(rec.ChannelRedStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenStatus))
				assert.Equal(t, 32, len(rec.ChannelRedFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenFieldCheckStatus))
				assert.Equal(t, 32, len(rec.ChannelRedRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenRecurrentPulseStatus))
				assert.Equal(t, 32, len(rec.ChannelRedCurrentStatus))
				assert.Equal(t, 32, len(rec.ChannelYellowCurrentStatus))
				assert.Equal(t, 32, len(rec.ChannelGreenCurrentStatus))
				assert.Equal(t, 32, len(rec.NextConflictingChannels))
			},
		},
		{
			name: "48Vdc signal bus for Issue J+ with DC voltage",
			byteMsg: func() []byte {
				msg := make([]byte, PFCMU2LogLengthJ) // Use J+ length
				msg[0] = 3
				msg[29] = 0x33
				msg[31] = 120
				msg[248] = 60
				msg[273] = 120 // 48Vdc value (will be divided by 4)
				msg[240] = 0x30
				msg[241] = 0x45
				msg[242] = 0x12
				msg[243] = 0x15
				msg[244] = 0x03
				msg[245] = 0x24
				return msg
			}(),
			offset: 0,
			header: &helper.HeaderRecord{
				Volt220:     false,
				MainsDC:     false,
				CommVersion: helper.ConvertByteToDecimalFormat(0x40), // > 0x39 for Issue J+
				VoltDC:      true,
			},
			httpHeader: httpHeader,
			wantErr:    false,
			wantCheck: func(t *testing.T, rec *helper.LogPreviousFailRecord) {
				assert.Equal(t, "30.0 Vrms", rec.T48VDCSignalBus)
			},
		},
		{
			name: "48Vdc signal bus for Issue J+ without DC voltage",
			byteMsg: func() []byte {
				msg := make([]byte, PFCMU2LogLengthJ)
				msg[0] = 3
				msg[29] = 0x33
				msg[31] = 120
				msg[248] = 60
				msg[273] = 120
				msg[240] = 0x30
				msg[241] = 0x45
				msg[242] = 0x12
				msg[243] = 0x15
				msg[244] = 0x03
				msg[245] = 0x24
				return msg
			}(),
			offset: 0,
			header: &helper.HeaderRecord{
				Volt220:     false,
				MainsDC:     false,
				CommVersion: helper.ConvertByteToDecimalFormat(0x40),
				VoltDC:      false,
			},
			httpHeader: httpHeader,
			wantErr:    false,
			wantCheck: func(t *testing.T, rec *helper.LogPreviousFailRecord) {
				assert.Equal(t, "0.0 Vrms", rec.T48VDCSignalBus)
			},
		},
		{
			name: "invalid datetime",
			byteMsg: func() []byte {
				msg := make([]byte, PFCMU2LogLength)
				msg[0] = 3
				msg[29] = 0x33
				msg[31] = 120
				msg[248] = 60
				msg[240] = 0x30
				msg[241] = 0x45
				msg[242] = 0x12
				msg[243] = 0x15
				msg[244] = 0x13 // Invalid month (13)
				msg[245] = 0x24
				return msg
			}(),
			offset:     0,
			header:     header,
			httpHeader: httpHeader,
			wantErr:    true,
		},
		{
			name: "220V DC voltage scaling",
			byteMsg: func() []byte {
				msg := make([]byte, PFCMU2LogLength)
				msg[0] = 3
				msg[29] = 0x33
				msg[31] = 250 // Above ScalePoint220
				msg[248] = 60
				msg[240] = 0x30
				msg[241] = 0x45
				msg[242] = 0x12
				msg[243] = 0x15
				msg[244] = 0x03
				msg[245] = 0x24
				return msg
			}(),
			offset: 0,
			header: &helper.HeaderRecord{
				Volt220:     true,
				MainsDC:     true,
				CommVersion: helper.ConvertByteToDecimalFormat(0x35),
				VoltDC:      true,
			},
			httpHeader: httpHeader,
			wantErr:    false,
			wantCheck: func(t *testing.T, rec *helper.LogPreviousFailRecord) {
				assert.Equal(t, "65 Vrms", rec.AcLine) // ((250-240)*2 + 240)/4 = 65
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parsePreviousFailRecord(tt.byteMsg, tt.offset, tt.httpHeader, tt.header)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			if tt.wantCheck != nil {
				tt.wantCheck(t, got)
			}
		})
	}
}
