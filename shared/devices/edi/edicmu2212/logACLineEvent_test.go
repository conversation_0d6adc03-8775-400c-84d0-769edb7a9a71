package edicmu2212

import (
	"errors"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestLogACLineEvent(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	header := &helper.HeaderRecord{
		Model:            helper.CMUip2212_hv,
		FirmwareVersion:  helper.ConvertByteToDecimalFormat(0x39),
		FirmwareRevision: helper.ConvertByteToDecimalFormat(0x50),
		CommVersion:      helper.ConvertByteToDecimalFormat(0x01),
		MainsDC:          false,
		PowerDownLevel:   25,
	}

	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		wantRecords    int
		wantErr        error
		validateRecord func(t *testing.T, record helper.LogACLineEventRecord)
	}{
		{
			name:        "valid single record",
			byteMsg:     buildValidACLineEventMsg(1),
			httpHeader:  httpHeader,
			header:      header,
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogACLineEventRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogACLineEvent() record DateTime is zero")
				}
				if record.EventType == "" {
					t.Error("LogACLineEvent() record EventType is empty")
				}
				if record.LineVoltageRms == 0 {
					t.Error("LogACLineEvent() record LineVoltageRms is zero")
				}
			},
		},
		{
			name:        "invalid message length",
			byteMsg:     []byte{0x01, 0x02}, // Too short
			httpHeader:  httpHeader,
			header:      header,
			wantRecords: 0,
			wantErr:     helper.ErrMsgByteLen,
			validateRecord: func(t *testing.T, record helper.LogACLineEventRecord) {
				// No validation needed for error case
			},
		},
		{
			name:        "invalid checksum",
			byteMsg:     buildInvalidChecksumACLineEventMsg(1),
			httpHeader:  httpHeader,
			header:      header,
			wantRecords: 0,
			wantErr:     helper.ErrMsgByteChecksum,
			validateRecord: func(t *testing.T, record helper.LogACLineEventRecord) {
				// No validation needed for error case
			},
		},
		{
			name:        "different timezone",
			byteMsg:     buildValidACLineEventMsg(1),
			httpHeader:  &pubsubdata.HeaderDetails{GatewayTimezone: "America/New_York"},
			header:      header,
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogACLineEventRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogACLineEvent() record DateTime is zero")
				}
			},
		},
		{
			name:        "invalid timezone",
			byteMsg:     buildValidACLineEventMsg(1),
			httpHeader:  &pubsubdata.HeaderDetails{GatewayTimezone: "Invalid/Timezone"},
			header:      header,
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogACLineEventRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogACLineEvent() record DateTime is zero")
				}
			},
		},
		{
			name:       "DC voltage type",
			byteMsg:    buildValidACLineEventMsg(1),
			httpHeader: httpHeader,
			header: &helper.HeaderRecord{
				Model:            helper.CMUip2212_hv,
				FirmwareVersion:  helper.ConvertByteToDecimalFormat(0x39),
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x50),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x01),
				MainsDC:          true,
				PowerDownLevel:   25,
			},
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogACLineEventRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogACLineEvent() record DateTime is zero")
				}
			},
		},
		{
			name:       "AC voltage type",
			byteMsg:    buildValidACLineEventMsg(1),
			httpHeader: httpHeader,
			header: &helper.HeaderRecord{
				Model:            helper.CMUip2212_hv,
				FirmwareVersion:  helper.ConvertByteToDecimalFormat(0x39),
				FirmwareRevision: helper.ConvertByteToDecimalFormat(0x50),
				CommVersion:      helper.ConvertByteToDecimalFormat(0x01),
				MainsDC:          false,
				PowerDownLevel:   25,
			},
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogACLineEventRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogACLineEvent() record DateTime is zero")
				}
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			t.Parallel()
			got, err := device.LogACLineEvent(test.httpHeader, test.byteMsg, test.header)
			if err != nil && test.wantErr == nil {
				t.Errorf("LogACLineEvent() unexpected error = %v", err)
				return
			}
			if err == nil && test.wantErr != nil {
				t.Errorf("LogACLineEvent() expected error = %v, got nil", test.wantErr)
				return
			}
			if err != nil && test.wantErr != nil {
				if !errors.Is(err, test.wantErr) {
					t.Errorf("LogACLineEvent() error = %v, wantErr %v", err, test.wantErr)
				}
				return
			}
			if got == nil && test.wantRecords > 0 {
				t.Error("LogACLineEvent() returned nil records when records were expected")
				return
			}
			if got != nil && len(got.Records) != test.wantRecords {
				t.Errorf("LogACLineEvent() got %d records, want %d", len(got.Records), test.wantRecords)
				return
			}
			if got != nil {
				for _, record := range got.Records {
					test.validateRecord(t, record)
				}
				// Check voltage type for DC/AC test cases
				if test.name == "DC voltage type" && got.VoltageType != VoltageTypeDC {
					t.Errorf("LogACLineEvent() VoltageType = %v, want %v", got.VoltageType, VoltageTypeDC)
				}
				if test.name == "AC voltage type" && got.VoltageType != VoltageTypeAC {
					t.Errorf("LogACLineEvent() VoltageType = %v, want %v", got.VoltageType, VoltageTypeAC)
				}
				// Check DeviceModel and RawMessage
				if got.DeviceModel != test.header.Model.String() {
					t.Errorf("LogACLineEvent() DeviceModel = %v, want %v", got.DeviceModel, test.header.Model.String())
				}
				if got.RawMessage == nil {
					t.Error("LogACLineEvent() RawMessage is nil")
				} else if len(got.RawMessage) != len(test.byteMsg) {
					t.Errorf("LogACLineEvent() RawMessage length = %v, want %v", len(got.RawMessage), len(test.byteMsg))
				} else {
					for i := range test.byteMsg {
						if got.RawMessage[i] != test.byteMsg[i] {
							t.Errorf("LogACLineEvent() RawMessage[%d] = %v, want %v", i, got.RawMessage[i], test.byteMsg[i])
							break
						}
					}
				}
			}
		})
	}
}

func TestLogACLineEvent_ErrorBranches(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{Model: helper.CMUip2212_hv, MainsDC: false, PowerDownLevel: 25}

	t.Run("too short", func(t *testing.T) {
		t.Parallel()
		_, err := device.LogACLineEvent(httpHeader, []byte{0x01, 0x02}, header)
		if err == nil {
			t.Error("expected error for too short message")
		}
	})

	t.Run("invalid length", func(t *testing.T) {
		t.Parallel()
		msg := buildValidACLineEventMsg(1)
		msg = msg[:len(msg)-2] // truncate to make length invalid
		_, err := device.LogACLineEvent(httpHeader, msg, header)
		if err == nil {
			t.Error("expected error for invalid length")
		}
	})

	t.Run("invalid checksum", func(t *testing.T) {
		t.Parallel()
		msg := buildValidACLineEventMsg(1)
		msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1]) + 1 // add 1 to make invalid checksum
		_, err := device.LogACLineEvent(httpHeader, msg, header)
		if err == nil {
			t.Error("expected error for invalid checksum")
		}
	})

	t.Run("invalid BCD time", func(t *testing.T) {
		t.Parallel()
		msg := buildValidACLineEventMsg(1)
		msg[HeaderLength+1+6] = 0x13 // invalid month
		msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1])
		_, err := device.LogACLineEvent(httpHeader, msg, header)
		if err == nil {
			t.Error("expected error for invalid BCD time")
		}
	})

	t.Run("zero records", func(t *testing.T) {
		t.Parallel()
		msg := buildValidACLineEventMsg(0)
		_, err := device.LogACLineEvent(httpHeader, msg, header)
		if err != nil {
			t.Errorf("unexpected error for zero records: %v", err)
		}
	})
}

func TestLogACLineEvent_ZeroRecords(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{Model: helper.CMUip2212_hv, MainsDC: false, PowerDownLevel: 25}
	msg := buildValidACLineEventMsg(0)
	result, err := device.LogACLineEvent(httpHeader, msg, header)
	if err != nil {
		t.Errorf("unexpected error for zero records: %v", err)
	}
	if result == nil {
		t.Error("expected non-nil result for zero records")
	}
	if len(result.Records) != 0 {
		t.Errorf("expected zero records, got %d", len(result.Records))
	}
}

func TestLogACLineEvent_TruncatedMessage(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{Model: helper.CMUip2212_hv, MainsDC: false, PowerDownLevel: 25}
	msg := buildValidACLineEventMsg(1)
	msg = msg[:HeaderLength+1+LogACLineEventRecordSize-1] // truncate to just before full record
	_, err := device.LogACLineEvent(httpHeader, msg, header)
	if err == nil {
		t.Error("expected error for truncated message")
	}
}

func buildValidACLineEventMsg(numRecords int) []byte {
	totalLength := HeaderLength + (numRecords * LogACLineEventRecordSize) + HeaderOffset
	msg := make([]byte, totalLength)

	// Set header bytes (all zeros for test)
	for i := 0; i < HeaderLength; i++ {
		msg[i] = 0x00
	}

	// Set number of records
	msg[HeaderLength] = byte(numRecords)

	// Set current time in BCD format for each record
	now := time.Now()
	startingByte := HeaderLength + 1

	for i := 0; i < numRecords; i++ {
		// Set event type (AC Power Up)
		msg[startingByte] = 0x44
		// Set line voltage (220V)
		msg[startingByte+1] = 220

		// Convert time to BCD format
		msg[startingByte+2] = byte(now.Second()/10<<4 | now.Second()%10)   // Seconds
		msg[startingByte+3] = byte(now.Minute()/10<<4 | now.Minute()%10)   // Minutes
		msg[startingByte+4] = byte(now.Hour()/10<<4 | now.Hour()%10)       // Hours
		msg[startingByte+5] = byte(now.Day()/10<<4 | now.Day()%10)         // Day
		msg[startingByte+6] = byte(now.Month()/10<<4 | now.Month()%10)     // Month
		msg[startingByte+7] = byte((now.Year()%100)/10<<4 | now.Year()%10) // Year

		startingByte += LogACLineEventRecordSize
	}

	// Add checksum
	msg[totalLength-1] = calculateChecksum(msg[:totalLength-1])

	return msg
}

func buildInvalidChecksumACLineEventMsg(numRecords int) []byte {
	msg := buildValidACLineEventMsg(numRecords)
	msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1]) + 1 // Set invalid checksum
	return msg
}
