package edicmu2212

import (
	"bytes"
	"fmt"
	"strings"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

type EDICMU2212 struct{}

const (

	// Common constants
	HeaderLength = 7
	HeaderOffset = 2
	PermsOffset  = 2 - 1

	// LogMonitorReset constants
	LogMonitorResetRecordSize = 7

	// LogACLineEvent constants
	LogACLineEventRecordSize = 10
	VoltageTypeAC            = 1
	VoltageTypeDC            = 2

	// ConfigurationChangeLog constants
	CFCMU2rmsLogLength     = 256
	CFCMU2rmsLogLength8FYA = 281
	CFCMU2rmsLogLengthFYA  = 274
	LOSOffset              = 64 - 1
	DarkMapOffset          = 68 - 1
	MultipleOffset         = 84 - 1
	ClearanceOffset        = 96 - 1
	YDOffset               = 104 - 1
	FCOffset               = 216 - 1
	VCoffset               = 230 - 1
	HDSPEnableOffset       = 242 - 1
	MinFlashOffset         = 228 - 1
	V12EnableOffset        = 229 - 1
	V48EnableOffset        = 229 - 1
	HDFU2EnableOffset      = 229 - 1
	MiscOffset             = 246 - 1
	DatakeyCRCOffset       = 247 - 1
	EdiCRCOffset           = 255 - 1
	CFYA_OLPOffset         = 257 - 1
	CFYA_GaOffset          = 263 - 1
	CFYA_OPPOffset         = 269 - 1
	CFYA_OLPOffset2        = 257 - 1
	CFYA_GaOffset2         = 265 - 1
	CFYA_OPPOffset2        = 273 - 1
	CSUROffset             = 108 - 1
	CSUYOffset             = 112 - 1
	CSUGOffset             = 116 - 1
	CSURThreshOffset       = 120 - 1
	CSUYThreshOffset       = 152 - 1
	CSUGThreshOffset       = 184 - 1
	ScalePoint             = 120

	// MonitorIDandName constants
	K2018IDNameLength        = 40
	MonitorIDlsByteIndicator = 5
	MonitorIDmsByteIndicator = 6

	// RMSStatus constants
	RmsStatusLength = 286

	// Fault related offsets
	FaultByteOffset = 8 - 1

	// Channel status offsets for channels 1-16
	RedStatusLSB1Offset    = 13 - 1
	RedStatusMSB1Offset    = 14 - 1
	YellowStatusLSB1Offset = 17 - 1
	YellowStatusMSB1Offset = 18 - 1
	GreenStatusLSB1Offset  = 21 - 1
	GreenStatusMSB1Offset  = 22 - 1

	// Channel status offsets for channels 17-32
	RedStatusLSB2Offset    = 15 - 1
	RedStatusMSB2Offset    = 16 - 1
	YellowStatusLSB2Offset = 19 - 1
	YellowStatusMSB2Offset = 20 - 1
	GreenStatusLSB2Offset  = 23 - 1
	GreenStatusMSB2Offset  = 24 - 1

	// CabStatusOffset
	CabStatus1Offset = 37 - 1
	CabStatus2Offset = 38 - 1

	// CMUMainsOffset
	CmuMainsOffset  = 39 - 1
	HDSPMainsOffset = 40 - 1

	// VoltageOffsets
	RedRmsOffset    = 56 - 1
	YellowRmsOffset = 88 - 1
	GreenRmsOffset  = 120 - 1

	// RMS Monitor time offsets
	MonitorTimeSecOffset  = 248 - 1
	MonitorTimeMinOffset  = 249 - 1
	MonitorTimeHourOffset = 250 - 1
	MonitorTimeDayOffset  = 251 - 1
	MonitorTimeMonOffset  = 252 - 1
	MonitorTimeYearOffset = 253 - 1

	// Config Monitor time offsets
	ConfigTimeSecOffset  = 249 - 1
	ConfigTimeMinOffset  = 250 - 1
	ConfigTimeHourOffset = 251 - 1
	ConfigTimeDayOffset  = 252 - 1
	ConfigTimeMonOffset  = 253 - 1
	ConfigTimeYearOffset = 254 - 1

	// Channel counts
	ChannelsPerGroup = 16

	// Fault codes
	FaultCodeNoFault                    = 0x00
	FaultCode24VDC                      = 0x01
	FaultCode12VDC                      = 0x02
	FaultCodeConflict                   = 0x03
	FaultCodeSerialBus1Error            = 0x04
	FaultCodeSerialBus3Error            = 0x05
	FaultCodeCUFrame62LatchedFlash      = 0x06
	FaultCodeCUFrame62NonLatchedFlash   = 0x07
	FaultCodeDiagnostic                 = 0x08
	FaultCodeMultipleIndication         = 0x09
	FaultCodeLackOfSignal               = 0x0A
	FaultCodeClearanceShortYellow       = 0x0B
	FaultCodeClearanceSkippedYellow     = 0x0C
	FaultCodeYellowRedClearance         = 0x0D
	FaultCodeFieldOutputCheck           = 0x0E
	FaultCodeDataKeyAbsent              = 0x0F
	FaultCodeDataKeyFCSError            = 0x10
	FaultCodeDataKeyInvalidParameter    = 0x11
	FaultCodeLocalFlash                 = 0x12
	FaultCodeCircuitBreakerTrip         = 0x13
	FaultCodeDCMainLowVoltage           = 0x14
	FaultCodeNResetActive               = 0x15
	FaultCodeHDSPDiagnosticError        = 0x16
	FaultCodeFYAFlashRate               = 0x17
	FaultCode48VDC                      = 0x18
	FaultCodeRecurrentPulseConflict     = 0x81
	FaultCodeRecurrentPulseLackOfSignal = 0x82
	FaultCodeRecurrentPulseMultiple     = 0x83
	FaultCodeConfigurationChange        = 0x84
	FaultCode48VDC2                     = 0x85
	FaultCodeCUWatchdog                 = 0x86

	// RMS Engine Data constants
	RmsEngineDataLength     = 20
	RmsEngineVersionLength  = 10
	RmsEngineRevisionLength = 10
	EngineVersionOffset     = 8
	EngineRevisionOffset    = 18
)

var helperParsePermissives = helper.ParsePermissives

func (device EDICMU2212) LogMonitorReset(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogMonitorResetRecords, err error) {
	if len(byteMsg) < HeaderLength {
		return nil, fmt.Errorf("%w byte length less then %d", helper.ErrMsgByteLen, HeaderLength)
	}

	numberOfRecords := int(byteMsg[HeaderLength])
	startingByte := (HeaderLength + 1)
	length := HeaderLength + (numberOfRecords * LogMonitorResetRecordSize) + HeaderOffset
	allRecords = &helper.LogMonitorResetRecords{
		DeviceModel: header.Model.String(),
		RawMessage:  byteMsg,
	}

	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	for range numberOfRecords {
		record := helper.LogMonitorResetRecord{}
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
			byteMsg[startingByte+4],
			byteMsg[startingByte+3],
			byteMsg[startingByte+5],
			byteMsg[startingByte+2],
			byteMsg[startingByte+1],
			byteMsg[startingByte+0],
			httpHeader.GatewayTimezone,
		)
		if err != nil {
			return nil, err
		}
		allRecords.Records = append(allRecords.Records, record)
		startingByte += LogMonitorResetRecordSize
	}

	return allRecords, nil
}

func (device EDICMU2212) LogACLineEvent(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogACLineEventRecords, err error) {
	if len(byteMsg) < HeaderLength {
		return nil, fmt.Errorf("%w byte length less then %d", helper.ErrMsgByteLen, HeaderLength)
	}

	numberOfRecords := int(byteMsg[HeaderLength])
	startingByte := (HeaderLength + 1)
	length := HeaderLength + (numberOfRecords * LogACLineEventRecordSize) + HeaderOffset
	allRecords = &helper.LogACLineEventRecords{
		DeviceModel: header.Model.String(),
		RawMessage:  byteMsg,
		VoltageType: func() int64 {
			if header.MainsDC {
				return VoltageTypeDC
			}
			return VoltageTypeAC
		}(),
	}

	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	for range numberOfRecords {
		record := helper.LogACLineEventRecord{}
		lineVoltage := int64(byteMsg[startingByte+1])
		record.EventType = helper.GetACLineEventType(helper.MonitorModel(header.Model), header.MainsDC, lineVoltage, header.PowerDownLevel, byteMsg[startingByte+0])
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
			byteMsg[startingByte+6],
			byteMsg[startingByte+5],
			byteMsg[startingByte+7],
			byteMsg[startingByte+4],
			byteMsg[startingByte+3],
			byteMsg[startingByte+2],
			httpHeader.GatewayTimezone,
		)
		if err != nil {
			return nil, err
		}
		record.LineVoltageRms = lineVoltage
		allRecords.Records = append(allRecords.Records, record)
		startingByte += LogACLineEventRecordSize
	}

	return allRecords, nil
}

func (device EDICMU2212) MonitorIDandName(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (monitor *helper.MonitorNameAndId, err error) {
	length := HeaderLength + K2018IDNameLength + 1

	monitor = &helper.MonitorNameAndId{
		DeviceModel: header.Model.String(),
	}

	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	monitor.MonitorId = int64(helper.ConvertLSandMStoUnint16(byteMsg[MonitorIDlsByteIndicator], byteMsg[MonitorIDmsByteIndicator]))
	monitor.MonitorName = helper.GetMonitorName(byteMsg[HeaderLength : HeaderLength+K2018IDNameLength])

	return monitor, nil
}

func (device EDICMU2212) RMSStatus(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsStatusRecord *helper.RmsStatusRecord, err error) {
	// process the response
	rmsStatusRecord = &helper.RmsStatusRecord{
		DeviceModel: header.Model.String(),
	}

	// hydrate IsFaulted
	fault := byteMsg[FaultByteOffset]
	if fault == FaultCodeNoFault {
		rmsStatusRecord.IsFaulted = false
		rmsStatusRecord.Fault = "No Fault"
	} else {
		rmsStatusRecord.IsFaulted = true

		// hydrate the fault reason
		rmsStatusRecord.Fault = "+24VDC Low Fault (VDC Fail)"

		switch fault {
		case FaultCode24VDC:
			rmsStatusRecord.Fault = "24VDC Fault"
		case FaultCode12VDC:
			rmsStatusRecord.Fault = "12VDC Fault"
		case FaultCodeConflict:
			rmsStatusRecord.Fault = "Conflict Fault"
		case FaultCodeSerialBus1Error:
			rmsStatusRecord.Fault = "Serial Bus #1 Error"
		case FaultCodeSerialBus3Error:
			rmsStatusRecord.Fault = "Serial Bus #3 Error"
		case FaultCodeCUFrame62LatchedFlash:
			rmsStatusRecord.Fault = "CU Frame-62 Latched Flash (LFSA)"
		case FaultCodeCUFrame62NonLatchedFlash:
			rmsStatusRecord.Fault = "CU Frame-62 Non-Latched Flash (NFSA)"
		case FaultCodeDiagnostic:
			rmsStatusRecord.Fault = "Diagnostic Fault"
		case FaultCodeMultipleIndication:
			rmsStatusRecord.Fault = "Multiple Indication Fault"
		case FaultCodeLackOfSignal:
			rmsStatusRecord.Fault = "Lack of Signal Fault"
		case FaultCodeClearanceShortYellow:
			rmsStatusRecord.Fault = "Clearance (Short Yellow) Fault"
		case FaultCodeClearanceSkippedYellow:
			rmsStatusRecord.Fault = "Clearance (Skipped Yellow) Fault"
		case FaultCodeYellowRedClearance:
			rmsStatusRecord.Fault = "Yellow + Red Clearance Fault"
		case FaultCodeFieldOutputCheck:
			rmsStatusRecord.Fault = "Field Output Check Fault"
		case FaultCodeDataKeyAbsent:
			rmsStatusRecord.Fault = "Data Key Absent"
		case FaultCodeDataKeyFCSError:
			rmsStatusRecord.Fault = "Data Key FCS Error"
		case FaultCodeDataKeyInvalidParameter:
			rmsStatusRecord.Fault = "Data Key Invalid Parameter Error"
		case FaultCodeLocalFlash:
			rmsStatusRecord.Fault = "Local Flash"
		case FaultCodeCircuitBreakerTrip:
			rmsStatusRecord.Fault = "Circuit Breaker Trip"
		case FaultCodeDCMainLowVoltage:
			rmsStatusRecord.Fault = "DC Main Low Voltage"
		case FaultCodeNResetActive:
			rmsStatusRecord.Fault = "nReset is Active (ATC Power Fail)"
		case FaultCodeHDSPDiagnosticError:
			rmsStatusRecord.Fault = "HDSP Diagnostic Error"
		case FaultCodeFYAFlashRate:
			rmsStatusRecord.Fault = "FYA Flash Rate Fault"
		case FaultCode48VDC:
			rmsStatusRecord.Fault = "48VDC Fault"
		case FaultCodeRecurrentPulseConflict:
			rmsStatusRecord.Fault = "Recurrent Pulse Conflict Fault"
		case FaultCodeRecurrentPulseLackOfSignal:
			rmsStatusRecord.Fault = "Recurrent Pulse Lack of Signal Fault"
		case FaultCodeRecurrentPulseMultiple:
			rmsStatusRecord.Fault = "Recurrent Pulse Multiple Indication Fault"
		case FaultCodeConfigurationChange:
			rmsStatusRecord.Fault = "Configuration Change Fault"
		case FaultCode48VDC2:
			rmsStatusRecord.Fault = "48VDC Fault"
		case FaultCodeCUWatchdog:
			rmsStatusRecord.Fault = "CU Watchdog Fault"
		default:
			rmsStatusRecord.Fault = "undefined fault type error"
		}
	}

	// hydrate the Channel Statuses

	// channels 1-16
	redStatus := uint32(byteMsg[RedStatusLSB1Offset])
	redStatus = redStatus*256 + uint32(byteMsg[RedStatusMSB1Offset])
	yellowStatus := uint32(byteMsg[YellowStatusLSB1Offset])
	yellowStatus = yellowStatus*256 + uint32(byteMsg[YellowStatusMSB1Offset])
	greenStatus := uint32(byteMsg[GreenStatusLSB1Offset])
	greenStatus = greenStatus*256 + uint32(byteMsg[GreenStatusMSB1Offset])

	for i := 0; i < ChannelsPerGroup; i++ {
		if helper.IsBitSet(greenStatus, i) {
			rmsStatusRecord.ChannelGreenStatus = append(rmsStatusRecord.ChannelGreenStatus, true)
		} else {
			rmsStatusRecord.ChannelGreenStatus = append(rmsStatusRecord.ChannelGreenStatus, false)
		}

		if helper.IsBitSet(yellowStatus, i) {
			rmsStatusRecord.ChannelYellowStatus = append(rmsStatusRecord.ChannelYellowStatus, true)
		} else {
			rmsStatusRecord.ChannelYellowStatus = append(rmsStatusRecord.ChannelYellowStatus, false)
		}

		if helper.IsBitSet(redStatus, i) {
			rmsStatusRecord.ChannelRedStatus = append(rmsStatusRecord.ChannelRedStatus, true)
		} else {
			rmsStatusRecord.ChannelRedStatus = append(rmsStatusRecord.ChannelRedStatus, false)
		}
	}

	// channels 17-32
	redStatus = uint32(byteMsg[RedStatusLSB2Offset])
	redStatus = redStatus*256 + uint32(byteMsg[RedStatusMSB2Offset])
	yellowStatus = uint32(byteMsg[YellowStatusLSB2Offset])
	yellowStatus = yellowStatus*256 + uint32(byteMsg[YellowStatusMSB2Offset])
	greenStatus = uint32(byteMsg[GreenStatusLSB2Offset])
	greenStatus = greenStatus*256 + uint32(byteMsg[GreenStatusMSB2Offset])

	for i := 0; i < ChannelsPerGroup; i++ {
		if helper.IsBitSet(greenStatus, i) {
			rmsStatusRecord.ChannelGreenStatus = append(rmsStatusRecord.ChannelGreenStatus, true)
		} else {
			rmsStatusRecord.ChannelGreenStatus = append(rmsStatusRecord.ChannelGreenStatus, false)
		}

		if helper.IsBitSet(yellowStatus, i) {
			rmsStatusRecord.ChannelYellowStatus = append(rmsStatusRecord.ChannelYellowStatus, true)
		} else {
			rmsStatusRecord.ChannelYellowStatus = append(rmsStatusRecord.ChannelYellowStatus, false)
		}

		if helper.IsBitSet(redStatus, i) {
			rmsStatusRecord.ChannelRedStatus = append(rmsStatusRecord.ChannelRedStatus, true)
		} else {
			rmsStatusRecord.ChannelRedStatus = append(rmsStatusRecord.ChannelRedStatus, false)
		}
	}

	rmsStatusRecord.MonitorTime, err = helper.ConvertBCDBytesToDateTimeII(
		byteMsg[MonitorTimeMonOffset],
		byteMsg[MonitorTimeDayOffset],
		byteMsg[MonitorTimeYearOffset],
		byteMsg[MonitorTimeHourOffset],
		byteMsg[MonitorTimeMinOffset],
		byteMsg[MonitorTimeSecOffset],
		httpHeader.GatewayTimezone,
	)
	if err != nil {
		return nil, err
	}

	rmsStatusRecord.VoltagesRed = formatVoltages(byteMsg[RedRmsOffset:RedRmsOffset+32], header)
	rmsStatusRecord.VoltagesYellow = formatVoltages(byteMsg[YellowRmsOffset:YellowRmsOffset+32], header)
	rmsStatusRecord.VoltagesGreen = formatVoltages(byteMsg[GreenRmsOffset:GreenRmsOffset+32], header)

	return rmsStatusRecord, nil
}

func (device EDICMU2212) RMSEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsEngineDetail *helper.RmsEngineRecord, err error) {
	rmsEngineDetail = &helper.RmsEngineRecord{
		DeviceModel: header.Model.String(),
	}

	if !helper.VerifyByteLen(byteMsg, RmsEngineDataLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), RmsEngineDataLength)
	}

	err = helper.ValidateChecksum(byteMsg[0:RmsEngineVersionLength])
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	err = helper.ValidateChecksum(byteMsg[RmsEngineRevisionLength:RmsEngineDataLength])
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	versionHeader := byteMsg[0:HeaderLength]
	revisionHeader := byteMsg[RmsEngineRevisionLength : RmsEngineRevisionLength+HeaderLength]
	if !bytes.Equal(versionHeader, revisionHeader) {
		return nil, fmt.Errorf("%w : %v", helper.ErrRMSEngineDataHeaderMismatch, err)
	}

	rmsEngineDetail.EngineVersion = helper.ConvertByteToString(byteMsg[EngineVersionOffset])
	rmsEngineDetail.EngineRevision = helper.ConvertByteToDecimalFormat(byteMsg[EngineRevisionOffset])

	return rmsEngineDetail, nil
}

// getFYAOffsets determines the FYA offsets based on firmware version
func getFYAOffsets(firmwareRevision int64) (recordSize int, olpOffset, gaOffset, oppOffset int) {
	// Default for firmware < 1.5
	recordSize = CFCMU2rmsLogLength

	if firmwareRevision > 0x20 {
		// For firmware >= 2.0 (8xFYA support)
		recordSize = CFCMU2rmsLogLength8FYA
		olpOffset = CFYA_OLPOffset2
		gaOffset = CFYA_GaOffset2
		oppOffset = CFYA_OPPOffset2
	} else if firmwareRevision > 0x15 {
		// For firmware >= 1.5 (FYA support)
		recordSize = CFCMU2rmsLogLengthFYA
		olpOffset = CFYA_OLPOffset
		gaOffset = CFYA_GaOffset
		oppOffset = CFYA_OPPOffset
	}

	return recordSize, olpOffset, gaOffset, oppOffset
}

// getFYACount determines the number of FYA pairs based on firmware version
func getFYACount(firmwareRevision int64) int64 {
	if firmwareRevision > 0x19 {
		return 8 // 8 FYA pairs for firmware >= 2.0
	}
	return 6 // 6 FYA pairs for firmware >= 1.5
}

// parseFYAFlags extracts FYA flags from a FYA data string
func parseFYAFlags(fyaData []string) (redYellowEnable string, flashRateFault bool, yellowTrapDetection, flashRateDetection bool) {
	if len(fyaData) == 0 {
		return "", false, false, false
	}

	// Parse flag values from the first FYA entry
	parts := strings.Split(fyaData[0], ", ")
	flags := map[string]string{}
	for _, part := range parts {
		kv := strings.SplitN(part, ":", 2)
		if len(kv) == 2 {
			key := strings.TrimSpace(kv[0])
			val := strings.TrimSpace(kv[1])
			flags[key] = val
		}
	}

	redYellowEnable = flags["RY"]
	flashRateFault = false
	if flags["FlashRate"] == "true" {
		flashRateFault = true
	}
	yellowTrapDetection = (flags["YTrap"] == "true")
	flashRateDetection = true

	return redYellowEnable, flashRateFault, yellowTrapDetection, flashRateDetection
}

// parseConfigRecord parses a single configuration record
func parseConfigRecord(byteMsg []byte, offset int64, fyaOLPOffset, fyaGaOffset, fyaOPPOffset int64,
	fyaCount int64, firmwareRevision int64, timezone string,
) (helper.ConfigurationChangeLogRecord, error) {
	record := helper.ConfigurationChangeLogRecord{}

	// Parse timestamp (date and time) in UTC
	var err error
	record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
		byteMsg[offset+int64(ConfigTimeMonOffset)],
		byteMsg[offset+int64(ConfigTimeDayOffset)],
		byteMsg[offset+int64(ConfigTimeYearOffset)],
		byteMsg[offset+int64(ConfigTimeHourOffset)],
		byteMsg[offset+int64(ConfigTimeMinOffset)],
		byteMsg[offset+int64(ConfigTimeSecOffset)],
		timezone,
	)
	if err != nil {
		return record, err
	}

	// Parse Permissives (channel configurations)
	permissives, err := helperParsePermissives(byteMsg[offset+int64(PermsOffset):offset+int64(PermsOffset)+62], 32)
	if err != nil {
		return record, err
	}
	record.Ch01Permissives = permissives[0]
	record.Ch02Permissives = permissives[1]
	record.Ch03Permissives = permissives[2]
	record.Ch04Permissives = permissives[3]
	record.Ch05Permissives = permissives[4]
	record.Ch06Permissives = permissives[5]
	record.Ch07Permissives = permissives[6]
	record.Ch08Permissives = permissives[7]
	record.Ch09Permissives = permissives[8]
	record.Ch10Permissives = permissives[9]
	record.Ch11Permissives = permissives[10]
	record.Ch12Permissives = permissives[11]
	record.Ch13Permissives = permissives[12]
	record.Ch14Permissives = permissives[13]
	record.Ch15Permissives = permissives[14]
	record.Ch16Permissives = permissives[15]
	record.Ch17Permissives = permissives[16]
	record.Ch18Permissives = permissives[17]
	record.Ch19Permissives = permissives[18]
	record.Ch20Permissives = permissives[19]
	record.Ch21Permissives = permissives[20]
	record.Ch22Permissives = permissives[21]
	record.Ch23Permissives = permissives[22]
	record.Ch24Permissives = permissives[23]
	record.Ch25Permissives = permissives[24]
	record.Ch26Permissives = permissives[25]
	record.Ch27Permissives = permissives[26]
	record.Ch28Permissives = permissives[27]
	record.Ch29Permissives = permissives[28]
	record.Ch30Permissives = permissives[29]
	record.Ch31Permissives = permissives[30]

	// Parse Lack of Signal (LOS) data
	record.RedFailEnable = parseBitmap(byteMsg[offset+int64(LOSOffset):offset+int64(LOSOffset)+4], 32)

	// Parse Dark Channel Maps.

	base := offset + int64(DarkMapOffset)

	// each map is 4 bytes → 32 bits
	record.DarkChannelX01 = parseBitmap(
		byteMsg[base+0:base+4],
		32,
	)
	record.DarkChannelX02 = parseBitmap(
		byteMsg[base+4:base+8],
		32,
	)
	record.DarkChannelX03 = parseBitmap(
		byteMsg[base+8:base+12],
		32,
	)
	record.DarkChannelX04 = parseBitmap(
		byteMsg[base+12:base+16],
		32,
	)

	// Parse Multiple Enable data (GY, YR, GR)
	record.GreenYellowDualEnable = parseBitmap(byteMsg[offset+int64(MultipleOffset):offset+int64(MultipleOffset)+4], 32)
	record.YellowRedDualEnable = parseBitmap(byteMsg[offset+int64(MultipleOffset)+4:offset+int64(MultipleOffset)+8], 32)
	record.GreenRedDualEnable = parseBitmap(byteMsg[offset+int64(MultipleOffset)+8:offset+int64(MultipleOffset)+12], 32)

	// Parse Clearance data
	// Note: In the VB code and EtlTranslators, for these fields a set bit (1) means "disabled" (X),
	// and an unset bit (0) means "enabled" (.). We invert the values to match the field names.
	yellowClearanceDisable := parseBitmap(byteMsg[offset+int64(ClearanceOffset):offset+int64(ClearanceOffset)+4], 32)
	yellowRedClearanceDisable := parseBitmap(byteMsg[offset+int64(ClearanceOffset)+4:offset+int64(ClearanceOffset)+8], 32)

	record.MinimumYellowClearanceEnable = make([]bool, len(yellowClearanceDisable))
	record.MinimumYellowRedClearanceEnable = make([]bool, len(yellowRedClearanceDisable))

	for i, disabled := range yellowClearanceDisable {
		record.MinimumYellowClearanceEnable[i] = !disabled // Invert the boolean value
	}

	for i, disabled := range yellowRedClearanceDisable {
		record.MinimumYellowRedClearanceEnable[i] = !disabled // Invert the boolean value
	}

	// Parse Yellow Disable data
	// Note: In the VB code, this is called "YellowDisable" where true means yellow is disabled.
	// We invert the values to store in YellowEnable where true means yellow is enabled.
	yellowDisable := parseBitmap(byteMsg[offset+int64(YDOffset):offset+int64(YDOffset)+4], 32)
	record.YellowEnable = make([]bool, len(yellowDisable))
	for i, disabled := range yellowDisable {
		record.YellowEnable[i] = !disabled // Invert the boolean value
	}

	// Current Sense data
	record.CurrentSenseRedEnabled, record.CurrentSenseYellowEnabled, record.CurrentSenseGreenEnabled,
		record.CurrentSenseRedThreshold, record.CurrentSenseYellowThreshold, record.CurrentSenseGreenThreshold, _ = ParseCurrentSenseData(byteMsg, offset)

	// Parse Field Check Enable data
	record.FieldCheckEnableRed = parseBitmap(byteMsg[offset+int64(FCOffset):offset+int64(FCOffset)+4], 32)
	record.FieldCheckEnableYellow = parseBitmap(byteMsg[offset+int64(FCOffset)+4:offset+int64(FCOffset)+8], 32)
	record.FieldCheckEnableGreen = parseBitmap(byteMsg[offset+int64(FCOffset)+8:offset+int64(FCOffset)+12], 32)

	// Virtual Channel assignments
	record.RedVirtualChannel, record.YellowVirtualChannel, record.GreenVirtualChannel = parseVirtualChannels(byteMsg[offset+int64(VCoffset) : offset+int64(VCoffset)+12])

	// Unit Data
	// Parse Min Flash setting
	minFlash := int(byteMsg[offset+int64(MinFlashOffset)])
	record.MinimumFlashTime = fmt.Sprintf("%d", minFlash)

	// Parse 12V and 48V enable settings
	if byteMsg[offset+int64(V12EnableOffset)]&0x01 != 0 {
		record.X24VoltInhibit = true
	}

	if byteMsg[offset+int64(V48EnableOffset)]&0x04 != 0 {
		record.WatchdogEnableSwitch = true
	}

	// Parse HDFU #2 enable
	if byteMsg[offset+int64(HDFU2EnableOffset)]&0x02 != 0 {
		record.CvmLatchEnable = true
	}

	// HDSP Enable (unitdata(1,2,3) not used)
	record.WalkEnableTs1 = len(parseBitmap(byteMsg[offset+int64(HDSPEnableOffset):offset+int64(HDSPEnableOffset)+4], 32)) > 0

	// Parse Misc settings
	miscByte := byteMsg[offset+int64(MiscOffset)]

	// CMU SB #1 Address from bits 1-2 of Misc byte
	cmuAddress := (miscByte & 0x06) >> 1
	switch cmuAddress {
	case 0:
		record.RedFaultTiming = "15"
	case 1:
		record.RedFaultTiming = "16"
	case 2:
		record.RedFaultTiming = "17"
	case 3:
		record.RedFaultTiming = "18"
	}

	// RP Detect from bit 0 of Misc byte
	if (miscByte & 0x01) != 0 {
		record.RecurrentPulse = false
	} else {
		record.RecurrentPulse = true
	}

	// Parse Datakey CRC
	datakeyCRC := int(helper.ConvertLSandMStoUnint16(
		byteMsg[offset+int64(DatakeyCRCOffset)],
		byteMsg[offset+int64(DatakeyCRCOffset)+1],
	))
	record.CheckValue = fmt.Sprintf("%d", datakeyCRC)

	// Parse EDI CRC
	ediCRC := int(helper.ConvertLSandMStoUnint16(
		byteMsg[offset+int64(EdiCRCOffset)],
		byteMsg[offset+int64(EdiCRCOffset)+1],
	))
	record.ChangeSource = fmt.Sprintf("%d", ediCRC)

	// Parse FYA Pair data if firmware supports it
	if firmwareRevision > 0x14 {
		// Make sure we have enough data for FYA
		if offset+int64(fyaOLPOffset)+fyaCount <= int64(len(byteMsg)) &&
			offset+int64(fyaGaOffset)+fyaCount <= int64(len(byteMsg)) &&
			offset+int64(fyaOPPOffset)+fyaCount <= int64(len(byteMsg)) {

			fyaData := parseFYAPairs(
				byteMsg[offset+int64(fyaOLPOffset):offset+int64(fyaOLPOffset)+fyaCount],
				byteMsg[offset+int64(fyaGaOffset):offset+int64(fyaGaOffset)+fyaCount],
				byteMsg[offset+int64(fyaOPPOffset):offset+int64(fyaOPPOffset)+fyaCount],
				fyaCount,
			)
			record.FlashingYellowArrows = fyaData

			// Parse FYA flags
			record.FyaRedAndYellowEnable, record.FYAFlashRateFault,
				record.FyaYellowTrapDetection, record.FyaFlashRateDetection = parseFYAFlags(fyaData)
		}
	}

	return record, nil
}

// parseBitmap extracts a bitmap of boolean values from the byte array
func parseBitmap(data []byte, size int) []bool {
	if len(data) < (size+7)/8 {
		return nil
	}

	result := make([]bool, size)
	for i := 0; i < size; i++ {
		byteIndex := i / 8
		bitIndex := i % 8
		if byteIndex < len(data) {
			result[i] = (data[byteIndex] & (1 << bitIndex)) != 0
		}
	}

	return result
}

// parseVirtualChannels extracts virtual channel assignments
// Use the VirtualSetting type from the helper package

// parseVirtualChannels parses the virtual channel data and returns structured settings for each color.
func parseVirtualChannels(data []byte) ([]helper.VirtualSetting, []helper.VirtualSetting, []helper.VirtualSetting) {
	if len(data) < 12 {
		return nil, nil, nil
	}

	redSettings := make([]helper.VirtualSetting, 4)
	yellowSettings := make([]helper.VirtualSetting, 4)
	greenSettings := make([]helper.VirtualSetting, 4)

	for i := 0; i < 4; i++ { // 4 virtual channels
		for j := 0; j < 3; j++ { // 3 colors (R,Y,G)
			index := i*3 + j
			param := data[index]

			var colorName string
			switch j {
			case 0:
				colorName = "Red"
			case 1:
				colorName = "Yellow"
			case 2:
				colorName = "Green"
			}

			setting := helper.VirtualSetting{
				Color:   colorName,
				Enabled: param != 0,
			}

			if param != 0 {
				setting.SourceChannel = int(param & 0x1F)

				switch param & 0x60 {
				case 0x20:
					setting.SourceColor = "Red"
				case 0x40:
					setting.SourceColor = "Yellow"
				case 0x60:
					setting.SourceColor = "Green"
				default:
					setting.SourceColor = "Unknown"
				}
			}

			// Assign to the appropriate array based on color
			switch j {
			case 0:
				redSettings[i] = setting
			case 1:
				yellowSettings[i] = setting
			case 2:
				greenSettings[i] = setting
			}
		}
	}

	return redSettings, yellowSettings, greenSettings
}

// parseFYAPairs extracts Flashing Yellow Arrow configuration
func parseFYAPairs(olpData, gaData, oppData []byte, count int64) []string {
	if len(olpData) < int(count) || len(gaData) < int(count) || len(oppData) < int(count) {
		return nil
	}

	result := make([]string, int(count))
	for i := int64(0); i < count; i++ {
		// OLP data
		olp := olpData[i]
		enabled := (olp & 0x20) != 0
		olpChannel := (olp & 0x1F) + 1
		// Flash rate detection status
		hasFlashRateDetect := (olp & 0x40) != 0

		// GA data
		ga := gaData[i]
		ltChannel := ga & 0x3F
		// Red and yellow enable status
		hasRedYellowEnable := (ga & 0x40) != 0

		// OPP data
		opp := oppData[i]
		oppChannel := opp & 0x3F
		// Yellow trap detection status
		hasYtrapDetect := (opp & 0x40) != 0

		// Format the result
		status := "Disabled"
		if enabled {
			status = "Enabled"
		}

		ltChannelStr := "--"
		if ltChannel > 0 {
			ltChannelStr = fmt.Sprintf("%d", ltChannel)
		}

		oppChannelStr := "--"
		if oppChannel > 0 {
			oppChannelStr = fmt.Sprintf("%d", oppChannel)
		}

		// Include all the status flags in the result
		result[int(i)] = fmt.Sprintf("FYA%d: %s, OLP Ch:%d, LT Ch:%s, OPP Ch:%s, FlashRate:%t, RY:%t, YTrap:%t",
			i+1, status, olpChannel, ltChannelStr, oppChannelStr,
			hasFlashRateDetect, hasRedYellowEnable, hasYtrapDetect)
	}

	return result
}

func ParseCurrentSenseData(byteMsg []byte, offset int64) ([]bool, []bool, []bool, []int, []int, []int, error) {
	// Check if we have enough data
	minLength := offset + int64(CSUGThreshOffset) + 32 // Need at least up to the last Green threshold
	if int64(len(byteMsg)) < minLength {
		return nil, nil, nil, nil, nil, nil, fmt.Errorf("byte message too short: got %d, need at least %d", len(byteMsg), minLength)
	}

	// Parse the Current Sense Unit parameters (enabled flags)
	// Red parameters
	param1MS := uint16(byteMsg[offset+int64(CSUROffset)+3]) << 8
	param1MS |= uint16(byteMsg[offset+int64(CSUROffset)+2])
	param1 := uint16(byteMsg[offset+int64(CSUROffset)+1]) << 8
	param1 |= uint16(byteMsg[offset+int64(CSUROffset)+0])

	// Yellow parameters
	param2MS := uint16(byteMsg[offset+int64(CSUYOffset)+3]) << 8
	param2MS |= uint16(byteMsg[offset+int64(CSUYOffset)+2])
	param2 := uint16(byteMsg[offset+int64(CSUYOffset)+1]) << 8
	param2 |= uint16(byteMsg[offset+int64(CSUYOffset)+0])

	// Green parameters
	param3MS := uint16(byteMsg[offset+int64(CSUGOffset)+3]) << 8
	param3MS |= uint16(byteMsg[offset+int64(CSUGOffset)+2])
	param3 := uint16(byteMsg[offset+int64(CSUGOffset)+1]) << 8
	param3 |= uint16(byteMsg[offset+int64(CSUGOffset)+0])

	// Initialize result slices
	redEnabled := make([]bool, 32)
	yellowEnabled := make([]bool, 32)
	greenEnabled := make([]bool, 32)
	redThreshold := make([]int, 32)
	yellowThreshold := make([]int, 32)
	greenThreshold := make([]int, 32)

	// Process each channel
	for i := 0; i < 32; i++ {
		// Determine which mask and parameters to use
		var mask uint16 = 1 << (i % 16)
		var redParam, yellowParam, greenParam uint16

		if i < 16 {
			redParam = param1
			yellowParam = param2
			greenParam = param3
		} else {
			redParam = param1MS
			yellowParam = param2MS
			greenParam = param3MS
		}

		// Red channel data
		redEnabled[i] = (redParam & mask) != 0

		// Red threshold
		redThresh := int(byteMsg[offset+int64(CSURThreshOffset)+int64(i)])
		if redThresh > ScalePoint {
			redThresh = (redThresh * 16) - 1800 // Unscale it
		}
		redThreshold[i] = redThresh

		// Yellow channel data
		yellowEnabled[i] = (yellowParam & mask) != 0

		// Yellow threshold
		yellowThresh := int(byteMsg[offset+int64(CSUYThreshOffset)+int64(i)])
		if yellowThresh > ScalePoint {
			yellowThresh = (yellowThresh * 16) - 1800 // Unscale it
		}
		yellowThreshold[i] = yellowThresh

		// Green channel data
		greenEnabled[i] = (greenParam & mask) != 0

		// Green threshold
		greenThresh := int(byteMsg[offset+int64(CSUGThreshOffset)+int64(i)])
		if greenThresh > ScalePoint {
			greenThresh = (greenThresh * 16) - 1800 // Unscale it
		}
		greenThreshold[i] = greenThresh
	}

	return redEnabled, yellowEnabled, greenEnabled, redThreshold, yellowThreshold, greenThreshold, nil
}

func (device EDICMU2212) LogConfiguration(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.ConfigurationChangeLogRecords, err error) {
	firmwareRevision := helper.ConvertBCDStringToInt(header.FirmwareRevision)
	// Determine record size and FYA offsets based on firmware version
	recordSize, fyaOLPOffset, fyaGaOffset, fyaOPPOffset := getFYAOffsets(firmwareRevision)

	// Get number of records and calculate expected length
	numberOfRecords := int(byteMsg[HeaderLength])
	startingByte := int64(HeaderLength + 1)
	length := HeaderLength + (numberOfRecords * recordSize) + HeaderOffset

	// Initialize return structure
	allRecords = &helper.ConfigurationChangeLogRecords{
		DeviceModel: header.Model.String(),
		RawMessage:  byteMsg,
		Record:      make([]helper.ConfigurationChangeLogRecord, 0, numberOfRecords),
	}

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	// Get FYA count based on firmware version
	fyaCount := getFYACount(firmwareRevision)

	// Process each record
	for i := 0; i < numberOfRecords; i++ {
		offset := startingByte + int64(i*recordSize)

		// Parse the record
		record, err := parseConfigRecord(
			byteMsg,
			offset,
			int64(fyaOLPOffset),
			int64(fyaGaOffset),
			int64(fyaOPPOffset),
			fyaCount,
			firmwareRevision,
			httpHeader.GatewayTimezone,
		)
		if err != nil {
			return nil, err
		}

		// Add the record to our collection
		allRecords.Record = append(allRecords.Record, record)
	}

	return allRecords, nil
}

func formatVoltages(rawVoltages []byte, header *helper.HeaderRecord) []int64 {
	size := len(rawVoltages)
	voltages := make([]int64, size)
	for idx, volt := range rawVoltages {
		voltages[size-1-idx] = int64(adjustVoltage(int32(volt), header))
	}
	return voltages
}

// adjustVoltage applies voltage adjustments based on header details
func adjustVoltage(volt int32, headerDetail *helper.HeaderRecord) int32 {
	if headerDetail.Volt220 {
		volt *= 2
	}
	if headerDetail.VoltDC {
		volt /= 4
	}
	return volt
}
