package edicmu2212

import (
	"errors"
	"testing"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestRMSEngineData(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{}
	header := &helper.HeaderRecord{Model: helper.CMU2212_lv}

	tests := []struct {
		name         string
		byteMsg      []byte
		wantVersion  string
		wantRevision string
		wantErr      error
	}{
		{
			name:         "valid engine data",
			byteMsg:      buildValidEngineDataMsg(1, 2),
			wantVersion:  helper.ConvertByteToString(1),
			wantRevision: helper.ConvertByteToDecimalFormat(2),
			wantErr:      nil,
		},
		{
			name:         "invalid message length",
			byteMsg:      []byte{0x01, 0x02}, // Too short
			wantVersion:  helper.ConvertByteToString(0),
			wantRevision: helper.ConvertByteToDecimalFormat(0),
			wantErr:      helper.ErrMsgByteLen,
		},
		{
			name:         "zero version and revision",
			byteMsg:      buildValidEngineDataMsg(0, 0),
			wantVersion:  helper.ConvertByteToString(0),
			wantRevision: helper.ConvertByteToDecimalFormat(0),
			wantErr:      nil,
		},
		{
			name:         "max version and revision",
			byteMsg:      buildValidEngineDataMsg(255, 255),
			wantVersion:  helper.ConvertByteToString(255),
			wantRevision: helper.ConvertByteToDecimalFormat(255),
			wantErr:      nil,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			t.Parallel()
			got, err := device.RMSEngineData(httpHeader, test.byteMsg, header)
			if err != nil && test.wantErr == nil {
				t.Errorf("RMSEngineData() unexpected error = %v", err)
				return
			}
			if err == nil && test.wantErr != nil {
				t.Errorf("RMSEngineData() expected error = %v, got nil", test.wantErr)
				return
			}
			if err != nil && test.wantErr != nil {
				if !errors.Is(err, test.wantErr) {
					t.Errorf("RMSEngineData() error = %v, wantErr %v", err, test.wantErr)
				}
				return
			}
			if got.EngineVersion != test.wantVersion {
				t.Errorf("RMSEngineData() version = %v, want %v", got.EngineVersion, test.wantVersion)
			}
			if got.EngineRevision != test.wantRevision {
				t.Errorf("RMSEngineData() revision = %v, want %v", got.EngineRevision, test.wantRevision)
			}
			if got.DeviceModel != header.Model.String() {
				t.Errorf("RMSEngineData() deviceModel = %v, want %v", got.DeviceModel, header.Model.String())
			}
		})
	}
}

func TestRMSEngineData_ErrorBranches(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{}
	header := &helper.HeaderRecord{}

	t.Run("too short", func(t *testing.T) {
		t.Parallel()
		_, err := device.RMSEngineData(httpHeader, []byte{0x01, 0x02}, header)
		if err == nil {
			t.Error("expected error for too short message")
		}
	})

	t.Run("invalid version checksum", func(t *testing.T) {
		t.Parallel()
		msg := buildValidEngineDataMsg(1, 2)
		msg[RmsEngineVersionLength-1] = calculateChecksum(msg[:RmsEngineVersionLength-1]) + 1 // add 1 to make invalid
		_, err := device.RMSEngineData(httpHeader, msg, header)
		if err == nil {
			t.Error("expected error for invalid version checksum")
		}
	})

	t.Run("invalid revision checksum", func(t *testing.T) {
		t.Parallel()
		msg := buildValidEngineDataMsg(1, 2)
		msg[RmsEngineDataLength-1] = calculateChecksum(msg[:RmsEngineDataLength-1]) + 1 // add 1 to make invalid
		_, err := device.RMSEngineData(httpHeader, msg, header)
		if err == nil {
			t.Error("expected error for invalid revision checksum")
		}
	})

	t.Run("header mismatch", func(t *testing.T) {
		t.Parallel()
		msg := buildValidEngineDataMsg(1, 2)
		msg[RmsEngineRevisionLength+0] = 0xBB // mismatch header
		msg[RmsEngineDataLength-1] = calculateChecksum(msg[RmsEngineRevisionLength : RmsEngineDataLength-1])
		_, err := device.RMSEngineData(httpHeader, msg, header)
		if err == nil {
			t.Error("expected error for header mismatch")
		}
	})
}

// buildValidEngineDataMsg creates a valid engine data message with the specified version and revision
func buildValidEngineDataMsg(version, revision byte) []byte {
	msg := make([]byte, RmsEngineDataLength)
	msg[EngineVersionOffset] = version
	msg[EngineRevisionOffset] = revision
	// Set headers for version and revision to be equal (required by implementation)
	for i := 0; i < HeaderLength; i++ {
		msg[i] = 0xAA // arbitrary header value
		msg[RmsEngineRevisionLength+i] = 0xAA
	}
	// Set valid checksums for version and revision segments
	// Version checksum
	msg[RmsEngineVersionLength-1] = calculateChecksum(msg[:RmsEngineVersionLength-1])
	// Revision checksum
	msg[RmsEngineDataLength-1] = calculateChecksum(msg[RmsEngineRevisionLength : RmsEngineDataLength-1])
	return msg
}
