package edicmu2212

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
)

func TestAdjustVoltage(t *testing.T) {
	t.Run("No_Adjustments", func(t *testing.T) {
		volt := int32(100)
		headerDetail := &helper.HeaderRecord{
			Volt220: false,
			VoltDC:  false,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(100), result)
	})

	t.Run("Volt220_Only", func(t *testing.T) {
		volt := int32(100)
		headerDetail := &helper.HeaderRecord{
			Volt220: true,
			VoltDC:  false,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(200), result) // 100 * 2
	})

	t.Run("VoltDC_Only", func(t *testing.T) {
		volt := int32(100)
		headerDetail := &helper.HeaderRecord{
			Volt220: false,
			VoltDC:  true,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(25), result) // 100 / 4
	})

	t.Run("Both_Volt220_And_VoltDC", func(t *testing.T) {
		volt := int32(100)
		headerDetail := &helper.HeaderRecord{
			Volt220: true,
			VoltDC:  true,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(50), result) // (100 * 2) / 4 = 50
	})

	t.Run("Zero_Voltage", func(t *testing.T) {
		volt := int32(0)
		headerDetail := &helper.HeaderRecord{
			Volt220: true,
			VoltDC:  true,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(0), result) // (0 * 2) / 4 = 0
	})

	t.Run("Negative_Voltage", func(t *testing.T) {
		volt := int32(-100)
		headerDetail := &helper.HeaderRecord{
			Volt220: true,
			VoltDC:  true,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(-50), result) // (-100 * 2) / 4 = -50
	})

	t.Run("Large_Voltage_Value", func(t *testing.T) {
		volt := int32(1000000)
		headerDetail := &helper.HeaderRecord{
			Volt220: false,
			VoltDC:  true,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(250000), result) // 1000000 / 4
	})

	t.Run("Division_With_Remainder", func(t *testing.T) {
		volt := int32(101) // Not divisible by 4
		headerDetail := &helper.HeaderRecord{
			Volt220: false,
			VoltDC:  true,
		}

		result := adjustVoltage(volt, headerDetail)

		assert.Equal(t, int32(25), result) // 101 / 4 = 25 (integer division)
	})

	t.Run("Nil_HeaderDetail", func(t *testing.T) {
		volt := int32(100)

		// This will panic with nil headerDetail, so we expect a panic
		assert.Panics(t, func() {
			adjustVoltage(volt, nil)
		})
	})
}
