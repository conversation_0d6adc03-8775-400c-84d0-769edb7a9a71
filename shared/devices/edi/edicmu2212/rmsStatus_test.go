package edicmu2212

import (
	"errors"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestRMSStatus(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	header := &helper.HeaderRecord{Model: helper.CMU2212_lv}

	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		wantStatus     *helper.RmsStatusRecord
		wantErr        error
		validateStatus func(t *testing.T, status *helper.RmsStatusRecord)
	}{
		{
			name:       "valid status no fault",
			byteMsg:    buildValidRMSStatusMsg(false),
			httpHeader: httpHeader,
			header:     header,
			wantStatus: &helper.RmsStatusRecord{
				DeviceModel: header.Model.String(),
				IsFaulted:   false,
				Fault:       "No Fault",
			},
			wantErr: nil,
			validateStatus: func(t *testing.T, status *helper.RmsStatusRecord) {
				if status.MonitorTime.IsZero() {
					t.Error("RMSStatus() MonitorTime is zero")
				}
				if len(status.ChannelGreenStatus) != 32 {
					t.Errorf("RMSStatus() got %d green status channels, want 32", len(status.ChannelGreenStatus))
				}
				if len(status.ChannelYellowStatus) != 32 {
					t.Errorf("RMSStatus() got %d yellow status channels, want 32", len(status.ChannelYellowStatus))
				}
				if len(status.ChannelRedStatus) != 32 {
					t.Errorf("RMSStatus() got %d red status channels, want 32", len(status.ChannelRedStatus))
				}
			},
		},
		{
			name:       "valid status with fault",
			byteMsg:    buildValidRMSStatusMsg(true),
			httpHeader: httpHeader,
			header:     header,
			wantStatus: &helper.RmsStatusRecord{
				DeviceModel: header.Model.String(),
				IsFaulted:   true,
				Fault:       "24VDC Fault",
			},
			wantErr: nil,
			validateStatus: func(t *testing.T, status *helper.RmsStatusRecord) {
				if status.MonitorTime.IsZero() {
					t.Error("RMSStatus() MonitorTime is zero")
				}
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			t.Parallel()
			got, err := device.RMSStatus(test.httpHeader, test.byteMsg, test.header)
			if err != nil && test.wantErr == nil {
				t.Errorf("RMSStatus() unexpected error = %v", err)
				return
			}
			if err == nil && test.wantErr != nil {
				t.Errorf("RMSStatus() expected error = %v, got nil", test.wantErr)
				return
			}
			if err != nil && test.wantErr != nil {
				if !errors.Is(err, test.wantErr) {
					t.Errorf("RMSStatus() error = %v, wantErr %v", err, test.wantErr)
				}
				return
			}
			if test.wantStatus != nil {
				if got == nil {
					t.Error("RMSStatus() returned nil status when status was expected")
					return
				}
				if got.DeviceModel != test.wantStatus.DeviceModel {
					t.Errorf("RMSStatus() DeviceModel = %v, want %v", got.DeviceModel, test.wantStatus.DeviceModel)
				}
				if got.IsFaulted != test.wantStatus.IsFaulted {
					t.Errorf("RMSStatus() IsFaulted = %v, want %v", got.IsFaulted, test.wantStatus.IsFaulted)
				}
				if got.Fault != test.wantStatus.Fault {
					t.Errorf("RMSStatus() Fault = %v, want %v", got.Fault, test.wantStatus.Fault)
				}
				if test.validateStatus != nil {
					test.validateStatus(t, got)
				}
			}
		})
	}
}

func buildValidRMSStatusMsg(hasFault bool) []byte {
	msg := make([]byte, RmsStatusLength)

	// Set header bytes (all zeros for test)
	for i := 0; i < HeaderLength; i++ {
		msg[i] = 0x00
	}

	// Set fault status
	if hasFault {
		msg[FaultByteOffset] = FaultCode24VDC
	} else {
		msg[FaultByteOffset] = FaultCodeNoFault
	}

	// Set channel statuses (all off)
	msg[RedStatusLSB1Offset] = 0x00
	msg[RedStatusMSB1Offset] = 0x00
	msg[YellowStatusLSB1Offset] = 0x00
	msg[YellowStatusMSB1Offset] = 0x00
	msg[GreenStatusLSB1Offset] = 0x00
	msg[GreenStatusMSB1Offset] = 0x00

	msg[RedStatusLSB2Offset] = 0x00
	msg[RedStatusMSB2Offset] = 0x00
	msg[YellowStatusLSB2Offset] = 0x00
	msg[YellowStatusMSB2Offset] = 0x00
	msg[GreenStatusLSB2Offset] = 0x00
	msg[GreenStatusMSB2Offset] = 0x00

	// Set monitor time
	now := time.Now()
	msg[MonitorTimeSecOffset] = byte(now.Second()/10<<4 | now.Second()%10)
	msg[MonitorTimeMinOffset] = byte(now.Minute()/10<<4 | now.Minute()%10)
	msg[MonitorTimeHourOffset] = byte(now.Hour()/10<<4 | now.Hour()%10)
	msg[MonitorTimeDayOffset] = byte(now.Day()/10<<4 | now.Day()%10)
	msg[MonitorTimeMonOffset] = byte(now.Month()/10<<4 | now.Month()%10)
	msg[MonitorTimeYearOffset] = byte((now.Year()%100)/10<<4 | now.Year()%10)

	// Add checksum
	msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1])

	return msg
}

func TestRMSStatus_AllFaultCodes(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{}
	faultCodes := []struct {
		code     byte
		expected string
	}{
		{FaultCodeNoFault, "No Fault"},
		{FaultCode24VDC, "24VDC Fault"},
		{FaultCode12VDC, "12VDC Fault"},
		{FaultCodeConflict, "Conflict Fault"},
		{FaultCodeSerialBus1Error, "Serial Bus #1 Error"},
		{FaultCodeSerialBus3Error, "Serial Bus #3 Error"},
		{FaultCodeCUFrame62LatchedFlash, "CU Frame-62 Latched Flash (LFSA)"},
		{FaultCodeCUFrame62NonLatchedFlash, "CU Frame-62 Non-Latched Flash (NFSA)"},
		{FaultCodeDiagnostic, "Diagnostic Fault"},
		{FaultCodeMultipleIndication, "Multiple Indication Fault"},
		{FaultCodeLackOfSignal, "Lack of Signal Fault"},
		{FaultCodeClearanceShortYellow, "Clearance (Short Yellow) Fault"},
		{FaultCodeClearanceSkippedYellow, "Clearance (Skipped Yellow) Fault"},
		{FaultCodeYellowRedClearance, "Yellow + Red Clearance Fault"},
		{FaultCodeFieldOutputCheck, "Field Output Check Fault"},
		{FaultCodeDataKeyAbsent, "Data Key Absent"},
		{FaultCodeDataKeyFCSError, "Data Key FCS Error"},
		{FaultCodeDataKeyInvalidParameter, "Data Key Invalid Parameter Error"},
		{FaultCodeLocalFlash, "Local Flash"},
		{FaultCodeCircuitBreakerTrip, "Circuit Breaker Trip"},
		{FaultCodeDCMainLowVoltage, "DC Main Low Voltage"},
		{FaultCodeNResetActive, "nReset is Active (ATC Power Fail)"},
		{FaultCodeHDSPDiagnosticError, "HDSP Diagnostic Error"},
		{FaultCodeFYAFlashRate, "FYA Flash Rate Fault"},
		{FaultCode48VDC, "48VDC Fault"},
		{FaultCodeRecurrentPulseConflict, "Recurrent Pulse Conflict Fault"},
		{FaultCodeRecurrentPulseLackOfSignal, "Recurrent Pulse Lack of Signal Fault"},
		{FaultCodeRecurrentPulseMultiple, "Recurrent Pulse Multiple Indication Fault"},
		{FaultCodeConfigurationChange, "Configuration Change Fault"},
		{FaultCode48VDC2, "48VDC Fault"},
		{FaultCodeCUWatchdog, "CU Watchdog Fault"},
		{0xFF, "undefined fault type error"}, // default case
	}
	for _, fc := range faultCodes {
		msg := buildValidRMSStatusMsg(false)
		msg[FaultByteOffset] = fc.code
		msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1])
		status, err := device.RMSStatus(httpHeader, msg, header)
		if err != nil {
			t.Errorf("RMSStatus() unexpected error for code 0x%X: %v", fc.code, err)
			continue
		}
		if status.Fault != fc.expected {
			t.Errorf("RMSStatus() Fault for code 0x%X = %q, want %q", fc.code, status.Fault, fc.expected)
		}
	}
}

func TestRMSStatus_ChannelStatus(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{}
	msg := buildValidRMSStatusMsg(false)
	// Set all bits for channels 1-16 ON for green, yellow, red
	msg[RedStatusLSB1Offset] = 0xFF
	msg[RedStatusMSB1Offset] = 0xFF
	msg[YellowStatusLSB1Offset] = 0xFF
	msg[YellowStatusMSB1Offset] = 0xFF
	msg[GreenStatusLSB1Offset] = 0xFF
	msg[GreenStatusMSB1Offset] = 0xFF
	// Set all bits for channels 17-32 ON for green, yellow, red
	msg[RedStatusLSB2Offset] = 0xFF
	msg[RedStatusMSB2Offset] = 0xFF
	msg[YellowStatusLSB2Offset] = 0xFF
	msg[YellowStatusMSB2Offset] = 0xFF
	msg[GreenStatusLSB2Offset] = 0xFF
	msg[GreenStatusMSB2Offset] = 0xFF
	msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1])
	status, err := device.RMSStatus(httpHeader, msg, header)
	if err != nil {
		t.Fatalf("RMSStatus() unexpected error: %v", err)
	}
	for i := 0; i < 32; i++ {
		if !status.ChannelGreenStatus[i] {
			t.Errorf("ChannelGreenStatus[%d] = false, want true", i)
		}
		if !status.ChannelYellowStatus[i] {
			t.Errorf("ChannelYellowStatus[%d] = false, want true", i)
		}
		if !status.ChannelRedStatus[i] {
			t.Errorf("ChannelRedStatus[%d] = false, want true", i)
		}
	}
}

func TestRMSStatus_ErrorBranches(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{}

	t.Run("invalid BCD time", func(t *testing.T) {
		t.Parallel()
		msg := buildValidRMSStatusMsg(false)
		msg[MonitorTimeMonOffset] = 0x13 // invalid month
		msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1])
		_, err := device.RMSStatus(httpHeader, msg, header)
		if err == nil {
			t.Error("expected error for invalid BCD time")
		}
	})
}
