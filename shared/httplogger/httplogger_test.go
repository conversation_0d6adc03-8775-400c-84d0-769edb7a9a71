package httplogger

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"go.uber.org/zap/zaptest/observer"
	"synapse-its.com/shared/logger"
)

// findHTTPReqField extracts the "logging.googleapis.com/httpRequest" field from a logged entry.
func findHTTPReqField(fields []zap.Field) (map[string]interface{}, bool) {
	for _, field := range fields {
		if field.Key == "logging.googleapis.com/httpRequest" {
			if m, ok := field.Interface.(map[string]interface{}); ok {
				return m, true
			}
		}
	}
	return nil, false
}

func TestLoggingMiddleware(t *testing.T) {
	// Set up zap observer and override the global logger.
	core, observed := observer.New(zapcore.DebugLevel)
	logger.Logger = zap.New(core)

	// Create a dummy handler that returns a specific status code and writes a body.
	dummyHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusCreated) // 201
		w.Write([]byte("hello world"))
	})

	// Wrap the dummy handler with the LoggingMiddleware.
	wrappedHandler := LoggingMiddleware(dummyHandler)

	// Create a test request.
	req := httptest.NewRequest("GET", "http://example.com/test", nil)
	req.Header.Set("User-Agent", "TestAgent")
	req.Header.Set("Referer", "http://referrer.com")
	// Set a dummy remote address.
	req.RemoteAddr = "*********:1234"

	// Create a ResponseRecorder.
	rr := httptest.NewRecorder()

	// Call the middleware-wrapped handler.
	start := time.Now()
	wrappedHandler.ServeHTTP(rr, req)
	_ = time.Since(start) // We measure latency here, but we won't enforce a strict comparison.

	// Verify the response.
	assert.Equal(t, http.StatusCreated, rr.Code, "Status code should be 201 Created")
	body := rr.Body.String()
	assert.Equal(t, "hello world", body, "Response body should match expected value")

	// Ensure a log entry was captured.
	logs := observed.All()
	require.Len(t, logs, 1, "Expected exactly 1 log entry")
	entry := logs[0]

	// Verify the log message.
	expectedMsg := "HTTP GET /test processed"
	assert.Equal(t, expectedMsg, entry.Entry.Message, "Log message should match expected format")

	// Verify the structured httpRequest field.
	httpReqField, found := findHTTPReqField(entry.Context)
	require.True(t, found, "Expected logging.googleapis.com/httpRequest field in log context")

	// Check some of the expected values.
	method, ok := httpReqField["requestMethod"].(string)
	assert.True(t, ok, "requestMethod should be a string")
	assert.Equal(t, "GET", method, "Request method should be GET")

	url, ok := httpReqField["requestUrl"].(string)
	assert.True(t, ok, "requestUrl should be a string")
	assert.Equal(t, "http://example.com/test", url, "Request URL should match")

	userAgent, ok := httpReqField["userAgent"].(string)
	assert.True(t, ok, "userAgent should be a string")
	assert.Equal(t, "TestAgent", userAgent, "User agent should match")

	remoteIP, ok := httpReqField["remoteIp"].(string)
	assert.True(t, ok, "remoteIp should be a string")
	assert.Contains(t, remoteIP, "*********", "Remote IP should contain expected address")

	referer, ok := httpReqField["referer"].(string)
	assert.True(t, ok, "referer should be a string")
	assert.Equal(t, "http://referrer.com", referer, "Referer should match")

	status, ok := httpReqField["status"].(int)
	assert.True(t, ok, "status should be an int")
	assert.Equal(t, http.StatusCreated, status, "Status should be 201 Created")

	size, ok := httpReqField["responseSize"].(int)
	assert.True(t, ok, "responseSize should be an int")
	assert.Equal(t, len("hello world"), size, "Response size should match body length")

	// Instead of comparing logged latency with a measured value,
	// simply check that it is a positive float64.
	loggedLatency, ok := httpReqField["latency"].(float64)
	assert.True(t, ok, "latency field should be a float64")
	assert.Positive(t, loggedLatency, "Logged latency should be positive")
}

func TestLoggingResponseWriter_DefaultStatusCode(t *testing.T) {
	// Set up zap observer and override the global logger.
	core, observed := observer.New(zapcore.DebugLevel)
	logger.Logger = zap.New(core)

	// Create a dummy handler that writes to response but doesn't call WriteHeader explicitly
	dummyHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Write without calling WriteHeader - this should trigger the default status code
		w.Write([]byte("default status"))
	})

	// Wrap the dummy handler with the LoggingMiddleware.
	wrappedHandler := LoggingMiddleware(dummyHandler)

	// Create a test request.
	req := httptest.NewRequest("POST", "http://example.com/default", nil)

	// Create a ResponseRecorder.
	rr := httptest.NewRecorder()

	// Call the middleware-wrapped handler.
	wrappedHandler.ServeHTTP(rr, req)

	// Verify the response - should default to 200 OK
	assert.Equal(t, http.StatusOK, rr.Code, "Status code should default to 200 OK")
	body := rr.Body.String()
	assert.Equal(t, "default status", body, "Response body should match expected value")

	// Ensure a log entry was captured.
	logs := observed.All()
	require.Len(t, logs, 1, "Expected exactly 1 log entry")
	entry := logs[0]

	// Verify the structured httpRequest field.
	httpReqField, found := findHTTPReqField(entry.Context)
	require.True(t, found, "Expected logging.googleapis.com/httpRequest field in log context")

	// Check that the status is the default 200
	status, ok := httpReqField["status"].(int)
	assert.True(t, ok, "status should be an int")
	assert.Equal(t, http.StatusOK, status, "Status should default to 200 OK")

	// Check that the response size is correct
	expectedSize := len("default status")
	size, ok := httpReqField["responseSize"].(int)
	assert.True(t, ok, "responseSize should be an int")
	assert.Equal(t, expectedSize, size, "Response size should match body length")
}
