package pubsubdata

import (
	"reflect"
	"testing"

	test "synapse-its.com/shared/util/test"
)

func TestMyStructJSONMarshalable_ByType(t *testing.T) {
	// Because we validate these types here to see if they json.<PERSON>() without
	// error, then when we call json.Marshal() in our code, we do not have to
	// check for an error, making our test coverage simpler to manage.
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(HeaderDetails{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(PubsubMessageWrapper{}))
}

func TestTopicSubscriptionMapping(t *testing.T) {
	// Create a set of valid topics for quick lookup
	validTopics := make(map[string]bool)
	for _, topic := range Topics {
		validTopics[topic] = true
	}

	// Track which topics have subscriptions
	topicsWithSubscriptions := make(map[string]bool)

	// Validate each subscription points to a valid topic
	for subscription, topic := range PubsubSubscriptions {
		// Check if the topic exists in the Topics slice
		if !validTopics[topic] {
			t.<PERSON><PERSON>rf("Subscription '%s' points to invalid topic '%s'", subscription, topic)
		}

		// Mark this topic as having at least one subscription
		topicsWithSubscriptions[topic] = true
	}

	// Validate every topic has at least one subscription
	for _, topic := range Topics {
		if !topicsWithSubscriptions[topic] {
			t.Errorf("Topic '%s' has no subscriptions mapped to it", topic)
		}
	}

	// Additional validation: ensure no duplicate subscriptions
	subscriptionSet := make(map[string]bool)
	for subscription := range PubsubSubscriptions {
		if subscriptionSet[subscription] {
			t.Errorf("Duplicate subscription found: '%s'", subscription)
		}
		subscriptionSet[subscription] = true
	}
}
