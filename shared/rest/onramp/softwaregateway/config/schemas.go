package config

import (
	"github.com/google/uuid"
)

// Structure of SoftwareGateway (simplified for config endpoints)
type SoftwareGateway struct {
	Id          uuid.UUID `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	Config      string    `json:"config" db:"config"`
}

// The request body for updating a software gateway config
type UpdateSoftwareGatewayConfigRequest struct {
	Config string `json:"config" validate:"required"`
}

// SoftwareGatewayConfigResponse represents the API response model
type SoftwareGatewayConfigResponse struct {
	Id          uuid.UUID `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	Config      string    `json:"config" db:"config"`
}

// GatewayConfigurationParameters represents the configuration parameters
type GatewayConfigurationParameters struct {
	LogLevel                                      string `json:"log_level"`
	LogFilename                                   string `json:"log_filename"`
	LogMaxBackups                                 int    `json:"log_max_backups"`
	ApplicationVersion                            string `json:"application_version"`
	LogMaxAgeInDays                               int    `json:"log_max_age_in_days"`
	LogCompressBackups                            bool   `json:"log_compress_backups"`
	LogFileMaxSizeMb                              int    `json:"log_file_max_size_mb"`
	RestApiDeviceEndpoint                         string `json:"rest_api_device_endpoint"`
	SendGatewayLogsToCloud                        bool   `json:"send_gateway_logs_to_cloud"`
	EdiDevicePersistConnection                    bool   `json:"edi_device_persist_connection"`
	EdiDeviceProcessingRetries                    int    `json:"edi_device_processing_retries"`
	RecordHttpRequestsToFolder                    bool   `json:"record_http_requests_to_folder"`
	DeviceStateSendFrequencySeconds               int    `json:"device_state_send_frequency_seconds"`
	ConfigChangeCheckFrequencySeconds             int    `json:"config_change_check_frequency_seconds"`
	SendGatewayPerformanceStatsToCloud            bool   `json:"send_gateway_performance_stats_to_cloud"`
	SoftwareUpdateCheckFrequencySeconds           int    `json:"software_update_check_frequency_seconds"`
	ChannelStateSendFrequencyMilliseconds         int    `json:"channel_state_send_frequency_milliseconds"`
	GatewayPerformanceStatsOutputFrequencySeconds int    `json:"gateway_performance_stats_output_frequency_seconds"`
	WsActive                                      bool   `json:"ws_active"`
	WsPort                                        string `json:"ws_port"`
	WsEndpoint                                    string `json:"ws_endpoint"`
	WsMaxConnections                              int    `json:"ws_max_connections"`
	WsSendFrequencyMilliseconds                   int    `json:"ws_send_frequency_milliseconds"`
	WsHeartbeatSendFrequencyMilliseconds          int    `json:"ws_heartbeat_send_frequency_milliseconds"`
	ThresholdDeviceErrorSeconds                   int    `json:"threshold_device_error_seconds"`
}

// Mapping SoftwareGateway model to SoftwareGatewayConfigResponse
func (s *SoftwareGateway) ToResponse() SoftwareGatewayConfigResponse {
	return SoftwareGatewayConfigResponse{
		Id:          s.Id,
		Name:        s.Name,
		Description: s.Description,
		Config:      s.Config,
	}
}
