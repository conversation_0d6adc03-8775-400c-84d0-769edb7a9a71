package config

import "errors"

var (
	ErrDatabaseOperation             = errors.New("database operation failed")
	ErrSoftwareGatewayConfigNotFound = errors.New("software gateway not found")
	ErrInvalidIdentifier             = errors.New("invalid software gateway id")
	ErrInvalidConfigFormat           = errors.New("invalid config json format or gateway config param")
	ErrInvalidRequestBody            = errors.New("invalid request body")
	ErrUnexpectedFields              = errors.New("unexpected fields in request body")
)
