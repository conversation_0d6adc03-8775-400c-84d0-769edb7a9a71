package config

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	GetConnections              func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	GetSoftwareGatewayConfig    func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID) (*SoftwareGateway, error)
	UpdateSoftwareGatewayConfig func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID, req *UpdateSoftwareGatewayConfigRequest) (*SoftwareGateway, error)
}

// This handler will get a software gateway by its identifier
func GetByIdentifierHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate softwareGatewayId
		vars := mux.Vars(r)
		softwareGatewayIdStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(softwareGatewayIdStr)
		if err != nil {
			logger.Errorf("invalid software gateway id: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the software gateway by its identifier using dependency injection
		softwareGateway, err := deps.GetSoftwareGatewayConfig(pg, softwareGatewayId)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				logger.Errorf("Software gateway not found: %s", softwareGatewayId)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error getting software gateway config: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the software gateway
		response.CreateSuccessResponse(softwareGateway.ToResponse(), w)
	}
}

// This handler will update a software gateway by its identifier
func UpdateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate softwareGatewayId
		vars := mux.Vars(r)
		softwareGatewayIdStr := vars["identifier"]
		softwareGatewayId, err := uuid.Parse(softwareGatewayIdStr)
		if err != nil {
			logger.Errorf("invalid software gateway id: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body with validation
		requestBody, err := parseUpdateRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Update the software gateway using dependency injection
		softwareGateway, err := deps.UpdateSoftwareGatewayConfig(pg, softwareGatewayId, requestBody)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayConfigNotFound) {
				logger.Errorf("Software gateway not found: %s", softwareGatewayId)
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("Error updating software gateway config: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the updated software gateway
		response.CreateSuccessResponse(softwareGateway.ToResponse(), w)
	}
}

// GetSoftwareGateway retrieves a software gateway by identifier
var getSoftwareGatewayByIdentifier = func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID) (*SoftwareGateway, error) {
	query := `
		SELECT 
			Id,
			Name,
			Description,
			Config
		FROM {{SoftwareGateway}}
		WHERE Id = $1 AND IsDeleted = false`

	var gateway SoftwareGateway
	err := pg.QueryRowStruct(&gateway, query, softwareGatewayId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to get software gateway by identifier: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &gateway, nil
}

// UpdateSoftwareGateway updates the software gateway config
var updateSoftwareGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId uuid.UUID, req *UpdateSoftwareGatewayConfigRequest) (*SoftwareGateway, error) {
	now := time.Now().UTC()
	query := `
		UPDATE {{SoftwareGateway}} 
		SET 
			Config = $1, 
			UpdatedAt = $2 
		WHERE 
			Id = $3
			AND IsDeleted = false 
		RETURNING Id, Name, Description, Config`

	var gateway SoftwareGateway
	err := pg.QueryRowStruct(&gateway, query, req.Config, now, softwareGatewayId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSoftwareGatewayConfigNotFound
		}
		logger.Errorf("Failed to update software gateway config: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}
	return &gateway, nil
}

// Parses and validates the update software gateway request body
func parseUpdateRequest(r *http.Request) (*UpdateSoftwareGatewayConfigRequest, error) {
	var req UpdateSoftwareGatewayConfigRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unexpected fields

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse update request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRequestBody
	}

	var config GatewayConfigurationParameters
	if err := json.Unmarshal([]byte(req.Config), &config); err != nil {
		logger.Infof("failed to parse config json: %v", err)
		return &req, ErrInvalidConfigFormat
	}

	return &req, nil
}

// Handler is the production-ready HTTP handler using default dependencies.
var (
	GetByIdentifierHandler = GetByIdentifierHandlerWithDeps(HandlerDeps{
		GetConnections:           connect.GetConnections,
		GetSoftwareGatewayConfig: getSoftwareGatewayByIdentifier,
	})
	UpdateHandler = UpdateHandlerWithDeps(HandlerDeps{
		GetConnections:              connect.GetConnections,
		UpdateSoftwareGatewayConfig: updateSoftwareGatewayConfig,
	})
)
