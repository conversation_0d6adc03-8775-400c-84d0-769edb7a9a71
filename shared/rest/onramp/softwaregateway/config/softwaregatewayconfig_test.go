package config

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func Test_SoftwareGateway_ToResponse(t *testing.T) {
	t.Parallel()

	testId := uuid.New()
	config := `{"log_level": "error", "log_filename": "test.log"}`

	gateway := SoftwareGateway{
		Id:          testId,
		Name:        "Test Gateway",
		Description: "Test Gateway Description",
		Config:      config,
	}

	response := gateway.ToResponse()

	assert.Equal(t, gateway.Id, response.Id)
	assert.Equal(t, gateway.Name, response.Name)
	assert.Equal(t, gateway.Description, response.Description)
	assert.Equal(t, gateway.Config, response.Config)
}

func Test_parseUpdateRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		requestBody interface{}
		expectedErr error
		wantErr     bool
	}{
		{
			name: "valid request",
			requestBody: UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "error", "log_filename": "test.log"}`,
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name: "invalid request with unexpected fields",
			requestBody: map[string]interface{}{
				"config":     "valid config",
				"unexpected": "field",
			},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
		},
		{
			name: "invalid JSON config",
			requestBody: UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "error", "log_filename": "test.log"`,
			},
			expectedErr: ErrInvalidConfigFormat,
			wantErr:     true,
		},
		{
			name:        "empty request body",
			requestBody: nil,
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request with test body
			var req *http.Request
			if tt.requestBody != nil {
				jsonBody, err := json.Marshal(tt.requestBody)
				assert.NoError(t, err)
				req = httptest.NewRequest("PATCH", "/api/softwaregateway/test/config", strings.NewReader(string(jsonBody)))
			} else {
				req = httptest.NewRequest("PATCH", "/api/softwaregateway/test/config", nil)
			}
			req.Header.Set("Content-Type", "application/json")

			// Call the function under test
			result, err := parseUpdateRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.requestBody.(UpdateSoftwareGatewayConfigRequest).Config, result.Config)
			}
		})
	}
}

func Test_getSoftwareGatewayByIdentifier(t *testing.T) {
	t.Parallel()

	testId := uuid.New()

	tests := []struct {
		name              string
		softwareGatewayId uuid.UUID
		mockSetup         func(*mocks.FakeDBExecutor)
		expectedResult    *SoftwareGateway
		expectedError     error
	}{
		{
			name:              "successful retrieval",
			softwareGatewayId: testId,
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					Config:      `{"log_level": "error"}`,
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Populate the destination with expected data
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}
			},
			expectedResult: &SoftwareGateway{
				Id:          testId,
				Name:        "Test Gateway",
				Description: "Test Gateway Description",
				Config:      `{"log_level": "error"}`,
			},
			expectedError: nil,
		},
		{
			name:              "not found",
			softwareGatewayId: testId,
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedResult: nil,
			expectedError:  ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:              "database error",
			softwareGatewayId: testId,
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrConnDone
				}
			},
			expectedResult: nil,
			expectedError:  fmt.Errorf("%w: %v", ErrDatabaseOperation, sql.ErrConnDone),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock database executor
			mockDB := &mocks.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Call the function under test
			result, err := getSoftwareGatewayByIdentifier(mockDB, tt.softwareGatewayId)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Id, result.Id)
				assert.Equal(t, tt.expectedResult.Name, result.Name)
				assert.Equal(t, tt.expectedResult.Description, result.Description)
				assert.Equal(t, tt.expectedResult.Config, result.Config)
			}

			// Verify the function was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

func Test_updateSoftwareGatewayConfig(t *testing.T) {
	t.Parallel()

	testId := uuid.New()

	tests := []struct {
		name              string
		softwareGatewayId uuid.UUID
		request           *UpdateSoftwareGatewayConfigRequest
		mockSetup         func(*mocks.FakeDBExecutor)
		expectedResult    *SoftwareGateway
		expectedError     error
	}{
		{
			name:              "successful update",
			softwareGatewayId: testId,
			request: &UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					Config:      `{"log_level": "info"}`,
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify we received the expected arguments
					assert.Equal(t, 3, len(args))
					assert.Equal(t, `{"log_level": "info"}`, args[0])
					assert.IsType(t, time.Time{}, args[1])
					assert.Equal(t, testId, args[2])

					// Populate the destination with expected data
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}
			},
			expectedResult: &SoftwareGateway{
				Id:          testId,
				Name:        "Test Gateway",
				Description: "Test Gateway Description",
				Config:      `{"log_level": "info"}`,
			},
			expectedError: nil,
		},
		{
			name:              "not found",
			softwareGatewayId: testId,
			request: &UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Verify we received the expected arguments
					assert.Equal(t, 3, len(args))
					assert.Equal(t, `{"log_level": "info"}`, args[0])
					assert.IsType(t, time.Time{}, args[1])
					assert.Equal(t, testId, args[2])

					return sql.ErrNoRows
				}
			},
			expectedResult: nil,
			expectedError:  ErrSoftwareGatewayConfigNotFound,
		},
		{
			name:              "database error",
			softwareGatewayId: testId,
			request: &UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrConnDone
				}
			},
			expectedResult: nil,
			expectedError:  fmt.Errorf("%w: %v", ErrDatabaseOperation, sql.ErrConnDone),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock database executor
			mockDB := &mocks.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			// Call the function under test
			result, err := updateSoftwareGatewayConfig(mockDB, tt.softwareGatewayId, tt.request)

			// Assert results
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Id, result.Id)
				assert.Equal(t, tt.expectedResult.Name, result.Name)
				assert.Equal(t, tt.expectedResult.Description, result.Description)
				assert.Equal(t, tt.expectedResult.Config, result.Config)
			}

			// Verify the function was called
			assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
		})
	}
}

func Test_GetByIdentifierHandlerWithDeps(t *testing.T) {
	t.Parallel()

	testId := uuid.New()

	tests := []struct {
		name               string
		softwareGatewayId  string
		mockSetup          func(*mocks.FakeDBExecutor)
		getConnections     func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		expectedStatusCode int
		expectedResponse   interface{}
	}{
		{
			name:              "successful get",
			softwareGatewayId: testId.String(),
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					Config:      `{"log_level": "error"}`,
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}
			},
			expectedStatusCode: http.StatusOK,
			expectedResponse: SoftwareGatewayConfigResponse{
				Id:          testId,
				Name:        "Test Gateway",
				Description: "Test Gateway Description",
				Config:      `{"log_level": "error"}`,
			},
		},
		{
			name:              "getconnections error",
			softwareGatewayId: testId.String(),
			mockSetup:         func(mock *mocks.FakeDBExecutor) {},
			getConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection error")
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse:   nil,
		},
		{
			name:               "invalid uuid",
			softwareGatewayId:  "invalid-uuid",
			mockSetup:          func(mock *mocks.FakeDBExecutor) {},
			expectedStatusCode: http.StatusBadRequest,
			expectedResponse:   nil,
		},
		{
			name:              "not found",
			softwareGatewayId: uuid.New().String(),
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedStatusCode: http.StatusNotFound,
			expectedResponse:   nil,
		},
		{
			name:              "database error",
			softwareGatewayId: uuid.New().String(),
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrConnDone
				}
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectedResponse:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &mocks.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			mockConnections := &connect.Connections{
				Postgres: mockDB,
			}

			getConns := tt.getConnections
			if getConns == nil {
				getConns = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return mockConnections, nil
				}
			}

			handler := GetByIdentifierHandlerWithDeps(HandlerDeps{
				GetConnections:           getConns,
				GetSoftwareGatewayConfig: getSoftwareGatewayByIdentifier,
			})

			req := httptest.NewRequest("GET", "/api/softwaregateway/"+tt.softwareGatewayId+"/config", nil)
			req = mux.SetURLVars(req, map[string]string{
				"identifier": tt.softwareGatewayId,
			})

			w := httptest.NewRecorder()
			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatusCode, w.Code)
			if tt.name == "getconnections error" {
				assert.Equal(t, 0, mockDB.QueryRowStructCallCount)
			} else if tt.softwareGatewayId != "invalid-uuid" {
				assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
			}
		})
	}
}

func Test_UpdateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	testId := uuid.New()

	tests := []struct {
		name               string
		softwareGatewayId  string
		requestBody        interface{}
		mockSetup          func(*mocks.FakeDBExecutor)
		getConnections     func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		expectedStatusCode int
	}{
		{
			name:              "successful update",
			softwareGatewayId: testId.String(),
			requestBody: UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				expectedGateway := SoftwareGateway{
					Id:          testId,
					Name:        "Test Gateway",
					Description: "Test Gateway Description",
					Config:      `{"log_level": "info"}`,
				}

				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					assert.Equal(t, 3, len(args))
					assert.Equal(t, `{"log_level": "info"}`, args[0])
					assert.IsType(t, time.Time{}, args[1])
					assert.Equal(t, testId, args[2])

					if gateway, ok := dest.(*SoftwareGateway); ok {
						*gateway = expectedGateway
					}
					return nil
				}
			},
			expectedStatusCode: http.StatusOK,
		},
		{
			name:              "getconnections error",
			softwareGatewayId: testId.String(),
			requestBody: UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup: func(mock *mocks.FakeDBExecutor) {},
			getConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, fmt.Errorf("connection error")
			},
			expectedStatusCode: http.StatusInternalServerError,
		},
		{
			name:              "invalid uuid",
			softwareGatewayId: "invalid-uuid",
			requestBody: UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup:          func(mock *mocks.FakeDBExecutor) {},
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:              "invalid request body",
			softwareGatewayId: uuid.New().String(),
			requestBody: map[string]interface{}{
				"unexpected": "field",
			},
			mockSetup:          func(mock *mocks.FakeDBExecutor) {},
			expectedStatusCode: http.StatusBadRequest,
		},
		{
			name:              "not found",
			softwareGatewayId: uuid.New().String(),
			requestBody: UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedStatusCode: http.StatusNotFound,
		},
		{
			name:              "database error",
			softwareGatewayId: uuid.New().String(),
			requestBody: UpdateSoftwareGatewayConfigRequest{
				Config: `{"log_level": "info"}`,
			},
			mockSetup: func(mock *mocks.FakeDBExecutor) {
				mock.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrConnDone
				}
			},
			expectedStatusCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB := &mocks.FakeDBExecutor{}
			tt.mockSetup(mockDB)

			mockConnections := &connect.Connections{
				Postgres: mockDB,
			}

			getConns := tt.getConnections
			if getConns == nil {
				getConns = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return mockConnections, nil
				}
			}

			handler := UpdateHandlerWithDeps(HandlerDeps{
				GetConnections:              getConns,
				UpdateSoftwareGatewayConfig: updateSoftwareGatewayConfig,
			})

			var req *http.Request
			if tt.requestBody != nil {
				jsonBody, err := json.Marshal(tt.requestBody)
				assert.NoError(t, err)
				req = httptest.NewRequest("PATCH", "/api/softwaregateway/"+tt.softwareGatewayId+"/config", strings.NewReader(string(jsonBody)))
			} else {
				req = httptest.NewRequest("PATCH", "/api/softwaregateway/"+tt.softwareGatewayId+"/config", nil)
			}
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"identifier": tt.softwareGatewayId,
			})

			w := httptest.NewRecorder()
			handler.ServeHTTP(w, req)
			assert.Equal(t, tt.expectedStatusCode, w.Code)
			if tt.name == "getconnections error" {
				assert.Equal(t, 0, mockDB.QueryRowStructCallCount)
			} else if tt.softwareGatewayId != "invalid-uuid" && tt.requestBody != nil {
				if _, ok := tt.requestBody.(UpdateSoftwareGatewayConfigRequest); ok {
					assert.Equal(t, 1, mockDB.QueryRowStructCallCount)
				}
			}
		})
	}
}

func Test_GetByIdentifierHandler(t *testing.T) {
	t.Parallel()

	// Test that the production handler is properly configured
	handler := GetByIdentifierHandler
	assert.NotNil(t, handler)
}

func Test_UpdateHandler(t *testing.T) {
	t.Parallel()

	// Test that the production handler is properly configured
	handler := UpdateHandler
	assert.NotNil(t, handler)
}
