package softwaregateway

import "errors"

var (
	ErrDatabaseOperation       = errors.New("database operation failed")
	ErrSoftwareGatewayNotFound = errors.New("softwaregateway not found")
	ErrOrganizationNotFound    = errors.New("organizationid not found")
	ErrInvalidIdentifier       = errors.New("invalid identifier")
	ErrInvalidDescription      = errors.New("description cannot be empty")
	ErrInvalidOrganizationId   = errors.New("organization ID must be greater than 0")
	ErrInvalidRequestBody      = errors.New("invalid request body")
	ErrUnexpectedFields        = errors.New("unexpected fields in request body")
)
