package softwaregateway

import (
	"time"
)

// Structure of SoftwareGateway
type SoftwareGateway struct {
	Id                        int        `json:"-" db:"id"`
	OrganizationId            int        `json:"organizationid" db:"organizationid"`
	SoftwareGatewayIdentifier string     `json:"softwaregatewayidentifier" db:"softwaregatewayidentifier"`
	APIKey                    string     `json:"apikey" db:"apikey"`
	Token                     string     `json:"-" db:"token"`
	DateLastCheckedInUTC      time.Time  `json:"datelastcheckedinutc" db:"datelastcheckedinutc"`
	PushConfigOnNextCheck     bool       `json:"pushconfigonnextcheck" db:"pushconfigonnextcheck"`
	IsEnabled                 bool       `json:"isenabled" db:"isenabled"`
	Description               string     `json:"description" db:"description"`
	CreatedAt                 time.Time  `json:"createdat" db:"createdat"`
	UpdatedAt                 time.Time  `json:"updatedat" db:"updatedat"`
	DeletedAt                 *time.Time `json:"-" db:"deletedat"`
	IsDeleted                 bool       `json:"-" db:"isdeleted"`
}

// The request body for creating a new software gateway
type CreateAndUpdateSoftwareGatewayRequest struct {
	Description    string `json:"description" validate:"required,min=1"`
	OrganizationId int    `json:"organizationid" validate:"required,gt=0"`
	IsEnabled      bool   `json:"isenabled"`
}

// SoftwareGatewayResponse represents the API response model
type SoftwareGatewayResponse struct {
	Id                        int        `json:"id"`
	OrganizationId            int        `json:"organizationid"`
	SoftwareGatewayIdentifier string     `json:"softwaregatewayidentifier"`
	APIKey                    string     `json:"apikey"`
	Token                     string     `json:"token"`
	DateLastCheckedInUTC      time.Time  `json:"datelastcheckedinutc"`
	PushConfigOnNextCheck     bool       `json:"pushconfigonnextcheck"`
	IsEnabled                 bool       `json:"isenabled"`
	Description               string     `json:"description"`
	CreatedAt                 time.Time  `json:"createdat"`
	UpdatedAt                 time.Time  `json:"updatedat"`
	DeletedAt                 *time.Time `json:"-" db:"deletedat"`
	IsDeleted                 bool       `json:"-" db:"isdeleted"`
}

// Converts a SoftwareGateway model to a SoftwareGatewayResponse
func (s *SoftwareGateway) ToResponse() SoftwareGatewayResponse {
	return SoftwareGatewayResponse{
		Id:                        s.Id,
		OrganizationId:            s.OrganizationId,
		SoftwareGatewayIdentifier: s.SoftwareGatewayIdentifier,
		APIKey:                    s.APIKey,
		DateLastCheckedInUTC:      s.DateLastCheckedInUTC,
		PushConfigOnNextCheck:     s.PushConfigOnNextCheck,
		IsEnabled:                 s.IsEnabled,
		Description:               s.Description,
		CreatedAt:                 s.CreatedAt,
		UpdatedAt:                 s.UpdatedAt,
	}
}
