package invites

import (
	"context"
	"errors"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
)

// Test_ListUserInvitesForOrganizationHandler_Integration tests the ListUserInvitesForOrganizationHandler with integration scenarios
func Test_ListUserInvitesForOrganizationHandler_Integration(t *testing.T) {
	t.<PERSON>llel()

	orgID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		orgID          string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "successful_organization_invites_retrieval",
			method:         "GET",
			orgID:          orgID,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_organization_id_format",
			method:         "GET",
			orgID:          "invalid-uuid-format",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:   "database_connection_failure",
			method: "GET",
			orgID:  orgID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("database connection failed")
				},
			},
			expectedStatus: 500,
		},
		{
			name:   "get_organization_name_error",
			method: "GET",
			orgID:  orgID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetInvitesForOrganization: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*[]UserInvite, error) {
					return &[]UserInvite{}, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "", errors.New("database error getting organization name")
				},
			},
			expectedStatus: 500,
		},
		{
			name:   "get_invites_for_organization_error",
			method: "GET",
			orgID:  orgID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "", nil
				},
				GetInvitesForOrganization: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (*[]UserInvite, error) {
					return nil, errors.New("database error getting invites for organization")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgID+"/invites", nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
			})

			w := httptest.NewRecorder()

			handler := ListUserInvitesForOrganizationHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ListUserInvitesForOrganizationHandlerWithDeps tests the ListUserInvitesForOrganizationHandlerWithDeps function
func Test_ListUserInvitesForOrganizationHandlerWithDeps(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		orgID          string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "success",
			method:         "GET",
			orgID:          orgID,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_org_id",
			method:         "GET",
			orgID:          "invalid-uuid",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgID+"/invites", nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
			})

			w := httptest.NewRecorder()

			handler := ListUserInvitesForOrganizationHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
