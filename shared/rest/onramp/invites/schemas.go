package invites

import (
	"time"

	"github.com/google/uuid"
)

// CreateInviteRequest represents the API request for creating invites
type CreateInviteRequest struct {
	Email            string    `json:"email" validate:"required,email"`
	InviterID        uuid.UUID `json:"inviterid" validate:"required"`
	Message          *string   `json:"message"`
	OrganizationRole uuid.UUID `json:"organizationrole" validate:"required"`
	ExpiredDays      *int      `json:"expireddays"`
}

// UserInvite represents the database model
type UserInvite struct {
	ID                     uuid.UUID  `json:"id" db:"id"`
	OrganizationIdentifier uuid.UUID  `json:"organizationidentifier" db:"organizationidentifier"`
	TokenHash              string     `json:"tokenhash" db:"tokenhash"`
	Email                  string     `json:"email" db:"email"`
	InviterID              uuid.UUID  `json:"inviterid" db:"inviterid"`
	CustomRoleID           uuid.UUID  `json:"customroleid" db:"customroleid"`
	Status                 string     `json:"status" db:"status"`
	Message                *string    `json:"message" db:"message"`
	RequireSSO             bool       `json:"requiresso" db:"requiresso"`
	RetryCount             int        `json:"retrycount" db:"retrycount"`
	Retried                *time.Time `json:"retried" db:"retried"`
	Expired                *time.Time `json:"expired" db:"expired"`
	Created                time.Time  `json:"created" db:"created"`
	Sent                   *time.Time `json:"sent" db:"sent"`
	Updated                time.Time  `json:"updated" db:"updated"`
}

// EmailTemplateData represents the data for email template rendering
type EmailTemplateData struct {
	AppName          string
	Message          string
	InviteLink       string
	OrganizationName string
}

// Struct for PubSub email message
type PubSubEmailMessage struct {
	Type    string `json:"type"`
	To      string `json:"to"`
	Message string `json:"message"`
}

// The request for revoking invites
type RevokeInviteRequest struct {
	Actor string `json:"actor" validate:"required"`
}

// The API request for resending invites
type ResendInviteRequest struct {
	Actor       string  `json:"actor" validate:"required"`
	Message     *string `json:"message"`
	ExpiredDays *int    `json:"expireddays"`
}

// InviteResponse represents the response for invite operations
type InviteResponse struct {
	ID                     uuid.UUID  `json:"id"`
	OrganizationIdentifier uuid.UUID  `json:"organizationidentifier"`
	Email                  string     `json:"email"`
	InviterID              uuid.UUID  `json:"inviterid"`
	CustomRoleID           uuid.UUID  `json:"customroleid"`
	Status                 string     `json:"status"`
	Message                *string    `json:"message"`
	RequireSSO             bool       `json:"requiresso"`
	RetryCount             int        `json:"retrycount"`
	Retried                *time.Time `json:"retried"`
	Expired                *time.Time `json:"expired"`
	Created                time.Time  `json:"created"`
	Sent                   *time.Time `json:"sent"`
	Updated                time.Time  `json:"updated"`
}
