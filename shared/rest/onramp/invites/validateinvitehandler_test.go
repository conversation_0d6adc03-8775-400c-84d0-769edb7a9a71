package invites

import (
	"context"
	"errors"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test_ValidateInviteHandlerWithDeps tests the ValidateInviteHandlerWithDeps function
func Test_ValidateInviteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	validToken := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

	tests := []struct {
		name           string
		method         string
		orgIDInput     string
		token          string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "missing_token",
			method:         "GET",
			token:          "",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:       "invalid_token_format",
			method:     "GET",
			orgIDInput: "123e4567-e89b-12d3-a456-************",
			token:      "invalid-token",
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					return nil, ErrInvalidInviteToken
				},
			},
			expectedStatus: 401,
		},
		{
			name:       "database_connection_failure",
			method:     "GET",
			orgIDInput: "123e4567-e89b-12d3-a456-************",
			token:      validToken,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("database connection failed")
				},
			},
			expectedStatus: 500,
		},
		{
			name:       "organization_id_mismatch",
			method:     "GET",
			orgIDInput: "123e4567-e89b-12d3-a456-************",
			token:      validToken,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					// Return invite with different organization ID than the request
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: uuid.New(), // Different from the request orgID
						TokenHash:              "test-hash",
						Email:                  "<EMAIL>",
						InviterID:              uuid.New(),
						CustomRoleID:           uuid.New(),
						Status:                 StatusPending,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
			},
			expectedStatus: 401,
		},
		{
			name:           "invalid_organization_id",
			method:         "GET",
			orgIDInput:     "invalid-uuid-format",
			token:          validToken,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			orgID := tt.orgIDInput
			req := httptest.NewRequest(tt.method, "/api/organization/"+orgID+"/invites/validate?token="+tt.token, nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": orgID,
			})

			w := httptest.NewRecorder()

			handler := ValidateInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ValidateInviteHandler_Integration tests the ValidateInviteHandler with integration scenarios
func Test_ValidateInviteHandler_Integration(t *testing.T) {
	t.Parallel()

	validToken := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
	orgID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		orgIDInput     string
		token          string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:       "successful_invite_validation",
			method:     "GET",
			orgIDInput: orgID,
			token:      validToken,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: orgID,
						TokenHash:              "test-hash",
						Email:                  "<EMAIL>",
						InviterID:              orgID,
						CustomRoleID:           uuid.New(),
						Status:                 StatusPending,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
			},
			expectedStatus: 200,
		},
		{
			name:           "missing_token",
			method:         "GET",
			token:          "",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:   "invalid_token_format",
			method: "GET",
			token:  "invalid-token",
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					return nil, ErrInvalidInviteToken
				},
			},
			expectedStatus: 401,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			orgID := "123e4567-e89b-12d3-a456-************"
			req := httptest.NewRequest(tt.method, "/api/organization/"+orgID+"/invites/validate?token="+tt.token, nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": orgID,
			})

			w := httptest.NewRecorder()

			handler := ValidateInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
