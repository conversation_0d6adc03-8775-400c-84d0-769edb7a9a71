package invites

import (
	"context"
	"errors"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test_ListUserInvitesForUserHandlerWithDeps tests the ListUserInvitesForUserHandlerWithDeps function
func Test_ListUserInvitesForUserHandlerWithDeps(t *testing.T) {
	t.Parallel()

	userID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		userID         string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "success",
			method:         "GET",
			userID:         userID,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_user_id",
			method:         "GET",
			userID:         "invalid-uuid",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:   "connections_error",
			method: "GET",
			userID: userID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("connection error")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/user/"+tt.userID+"/invites", nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.userID,
			})

			w := httptest.NewRecorder()

			handler := ListUserInvitesForUserHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ListUserInvitesForUserHandler_Integration tests the ListUserInvitesForUserHandler with integration scenarios
func Test_ListUserInvitesForUserHandler_Integration(t *testing.T) {
	t.Parallel()

	userID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		userID         string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "successful_user_invites_retrieval",
			method:         "GET",
			userID:         userID,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_user_id_format",
			method:         "GET",
			userID:         "invalid-uuid-format",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:   "database_connection_failure",
			method: "GET",
			userID: userID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("database connection failed")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/user/"+tt.userID+"/invites", nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.userID,
			})

			w := httptest.NewRecorder()

			handler := ListUserInvitesForUserHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ListUserInvitesForUserHandler_ErrorScenarios tests specific error scenarios for ListUserInvitesForUserHandler
func Test_ListUserInvitesForUserHandler_ErrorScenarios(t *testing.T) {
	t.Parallel()

	userID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name: "get_invites_for_user_error",
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				GetInvitesForUser: func(pg connect.DatabaseExecutor, userID uuid.UUID) (*[]UserInvite, error) {
					return nil, errors.New("failed to get invites for user")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest("GET", "/api/user/"+userID+"/invites", nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId": userID,
			})

			w := httptest.NewRecorder()

			handler := ListUserInvitesForUserHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
