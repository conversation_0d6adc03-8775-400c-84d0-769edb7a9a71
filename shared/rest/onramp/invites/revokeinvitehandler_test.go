package invites

import (
	"context"
	"errors"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
)

// Test_RevokeInviteHandlerWithDeps tests the RevokeInviteHandlerWithDeps function
func Test_RevokeInviteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		orgID          string
		inviteID       string
		body           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "success",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{"actor": "test-actor"}`,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_invite_id",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       "invalid-uuid",
			body:           `{"actor": "test-actor"}`,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:           "invalid_json",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{invalid json}`,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgID+"/invites/"+tt.inviteID+"/revoke", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
				"inviteId":       tt.inviteID,
			})

			w := httptest.NewRecorder()

			handler := RevokeInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_RevokeInviteHandler_Integration tests the RevokeInviteHandler with integration scenarios
func Test_RevokeInviteHandler_Integration(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		orgID          string
		inviteID       string
		body           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "successful_invite_revocation",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{"actor": "test-actor"}`,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_invite_id_format",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       "invalid-uuid-format",
			body:           `{"actor": "test-actor"}`,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:           "invalid_json_format",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{invalid json}`,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:     "database_connection_failure",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("database connection failed")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "bigquery_batcher_failure",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, errors.New("bigquery batcher failed")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "invalid_organization_id",
			method:   "PUT",
			orgID:    "invalid-uuid",
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
			},
			expectedStatus: 400,
		},
		{
			name:     "invite_not_found",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return nil, ErrInviteNotFound
				},
			},
			expectedStatus: 404,
		},
		{
			name:     "get_invite_by_id_database_error",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return nil, errors.New("database error getting invite")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "invite_organization_mismatch",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					// Return invite with different organization ID than the request orgID
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: uuid.New(), // Different from orgID
						Status:                 StatusPending,
					}, nil
				},
			},
			expectedStatus: 400,
		},
		{
			name:     "update_invite_status_error",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgUUID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgUUID, // Match the orgID
						Status:                 StatusPending,
					}, nil
				},
				UpdateInviteStatus: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error {
					return errors.New("database error updating invite status")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "log_invite_event_error",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgUUID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgUUID, // Match the orgID
						Status:                 StatusPending,
					}, nil
				},
				UpdateInviteStatus: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return errors.New("failed to log invite event")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgID+"/invites/"+tt.inviteID+"/revoke", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
				"inviteId":       tt.inviteID,
			})

			w := httptest.NewRecorder()

			handler := RevokeInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
