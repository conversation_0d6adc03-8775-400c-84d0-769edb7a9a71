package invites

import (
	"context"
	"errors"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
)

// Test_RedeemInviteHandlerWithDeps tests the RedeemInviteHandlerWithDeps function
func Test_RedeemInviteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	userID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		userID         string
		inviteID       string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "success",
			method:         "PUT",
			userID:         userID,
			inviteID:       inviteID,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_invite_id",
			method:         "PUT",
			userID:         userID,
			inviteID:       "invalid-uuid",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/user/"+tt.userID+"/invites/"+tt.inviteID+"/redeem", nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId":   tt.userID,
				"inviteId": tt.inviteID,
			})

			w := httptest.NewRecorder()

			handler := RedeemInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_RedeemInviteHandler_Integration tests the RedeemInviteHandler with integration scenarios
func Test_RedeemInviteHandler_Integration(t *testing.T) {
	t.Parallel()

	userID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		userID         string
		inviteID       string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "successful_invite_redemption",
			method:         "PUT",
			userID:         userID,
			inviteID:       inviteID,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_invite_id_format",
			method:         "PUT",
			userID:         userID,
			inviteID:       "invalid-uuid-format",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:           "invalid_user_id_format",
			method:         "PUT",
			userID:         "invalid-uuid-format",
			inviteID:       inviteID,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:     "database_connection_failure",
			method:   "PUT",
			userID:   userID,
			inviteID: inviteID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("database connection failed")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "bigquery_batcher_failure",
			method:   "PUT",
			userID:   userID,
			inviteID: inviteID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, errors.New("bigquery batcher failed")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "invite_not_found",
			method:   "PUT",
			userID:   userID,
			inviteID: inviteID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return nil, ErrInviteNotFound
				},
			},
			expectedStatus: 404,
		},
		{
			name:     "get_invite_by_id_database_error",
			method:   "PUT",
			userID:   userID,
			inviteID: inviteID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return nil, errors.New("database error getting invite")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "invite_status_not_pending",
			method:   "PUT",
			userID:   userID,
			inviteID: inviteID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return &UserInvite{
						ID:     inviteID,
						Status: StatusRedeemed, // Not pending
					}, nil
				},
			},
			expectedStatus: 400,
		},
		{
			name:     "update_invite_status_error",
			method:   "PUT",
			userID:   userID,
			inviteID: inviteID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return &UserInvite{
						ID:     inviteID,
						Status: StatusPending,
					}, nil
				},
				UpdateInviteStatus: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error {
					return errors.New("database error updating invite status")
				},
			},
			expectedStatus: 500,
		},
		{
			name:     "log_invite_event_error",
			method:   "PUT",
			userID:   userID,
			inviteID: inviteID,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return &UserInvite{
						ID:     inviteID,
						Status: StatusPending,
					}, nil
				},
				UpdateInviteStatus: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, status string, now *time.Time) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return errors.New("failed to log invite event")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/user/"+tt.userID+"/invites/"+tt.inviteID+"/redeem", nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId":   tt.userID,
				"inviteId": tt.inviteID,
			})

			w := httptest.NewRecorder()

			handler := RedeemInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
