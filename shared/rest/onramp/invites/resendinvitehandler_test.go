package invites

import (
	"context"
	"errors"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/bqbatcher"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/pubsub"
)

// Test_ResendInviteHandlerWithDeps tests the ResendInviteHandlerWithDeps function
func Test_ResendInviteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		orgID          string
		inviteID       string
		body           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "success",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{"actor": "test-actor"}`,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_json",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{invalid json}`,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgID+"/invites/"+tt.inviteID+"/resend", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
				"inviteId":       tt.inviteID,
			})

			w := httptest.NewRecorder()

			handler := ResendInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ResendInviteHandlerWithDeps_additional_scenarios tests additional scenarios for ResendInviteHandlerWithDeps
func Test_ResendInviteHandlerWithDeps_additional_scenarios(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		body           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:   "bq_batch_error",
			method: "PUT",
			body:   `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, errors.New("bq batch error")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/organization/"+orgID+"/invites/"+inviteID+"/resend", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": orgID,
				"inviteId":       inviteID,
			})

			w := httptest.NewRecorder()

			handler := ResendInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ResendInviteHandler_Integration tests the ResendInviteHandler with integration scenarios
func Test_ResendInviteHandler_Integration(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		method         string
		orgID          string
		inviteID       string
		body           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:           "successful_invite_resend",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{"actor": "test-actor"}`,
			deps:           setupTestDeps(),
			expectedStatus: 200,
		},
		{
			name:           "invalid_json_format",
			method:         "PUT",
			orgID:          orgID,
			inviteID:       inviteID,
			body:           `{invalid json}`,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:     "database_connection_failure",
			method:   "PUT",
			orgID:    orgID,
			inviteID: inviteID,
			body:     `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return nil, errors.New("database connection failed")
				},
			},
			expectedStatus: 500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/organization/"+tt.orgID+"/invites/"+tt.inviteID+"/resend", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
				"inviteId":       tt.inviteID,
			})

			w := httptest.NewRecorder()

			handler := ResendInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ResendInviteHandler_ErrorScenarios tests specific error scenarios for ResendInviteHandler
func Test_ResendInviteHandler_ErrorScenarios(t *testing.T) {
	t.Parallel()

	orgID := "123e4567-e89b-12d3-a456-************"
	inviteID := "123e4567-e89b-12d3-a456-************"

	tests := []struct {
		name           string
		body           string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name: "bq_batch_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, errors.New("bq batch error")
				},
			},
			expectedStatus: 500,
		},
		{
			name: "invalid_organization_id",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
			},
			expectedStatus: 400,
		},
		{
			name: "invalid_invite_id",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
			},
			expectedStatus: 400,
		},
		{
			name: "get_organization_name_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "", errors.New("database error getting organization name")
				},
			},
			expectedStatus: 500,
		},
		{
			name: "invite_not_found",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return nil, ErrInviteNotFound
				},
			},
			expectedStatus: 404,
		},
		{
			name: "get_invite_by_id_database_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					return nil, errors.New("database error getting invite")
				},
			},
			expectedStatus: 500,
		},
		{
			name: "invite_organization_mismatch",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					// Return invite with different organization ID than the request orgID
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: uuid.New(), // Different from the request orgID
						Status:                 StatusPending,
					}, nil
				},
			},
			expectedStatus: 400,
		},
		{
			name: "generate_token_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "", errors.New("failed to generate token")
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "update_invite_token_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return errors.New("database error updating invite token")
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "log_invite_event_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return errors.New("failed to log invite event")
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "err_log_invite_event_with_custom_message",
			body: `{"actor": "test-actor", "message": "Custom resend message"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return nil, nil
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return errors.New("failed to log invite event")
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "success_with_custom_message",
			body: `{"actor": "test-actor", "message": "Custom resend message"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message PubSubEmailMessage) error {
					return nil
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return nil
				},
			},
			expectedStatus: 200,
		},
		{
			name: "render_email_template_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "", errors.New("failed to render email template")
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message PubSubEmailMessage) error {
					return nil
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "publish_email_notification_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message PubSubEmailMessage) error {
					return errors.New("failed to publish email notification")
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return nil
				},
			},
			expectedStatus: 500,
		},
		{
			name: "check_cooldown_period_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message PubSubEmailMessage) error {
					return nil
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return ErrResendCooldown
				},
			},
			expectedStatus: 425, // Too Early status for cooldown
		},
		{
			name: "check_cooldown_period_general_error",
			body: `{"actor": "test-actor"}`,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
						Pubsub:   pubsub.NewFakePubsubClient(),
					}, nil
				},
				GetBQBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return bqbatcher.FakeBatch(ctx)
				},
				GetOrganizationName: func(pg connect.DatabaseExecutor, orgID uuid.UUID) (string, error) {
					return "Test Organization", nil
				},
				GetInviteByID: func(pg connect.DatabaseExecutor, inviteID uuid.UUID) (*UserInvite, error) {
					orgID, _ := uuid.Parse(orgID)
					return &UserInvite{
						ID:                     inviteID,
						OrganizationIdentifier: orgID,
						Status:                 StatusPending,
						RetryCount:             1,
					}, nil
				},
				GenerateToken: func(length uint) (string, error) {
					return "test-token", nil
				},
				HashString: func(input string) string {
					return "hashed-" + input
				},
				UpdateInviteToken: func(pg connect.DatabaseExecutor, inviteID uuid.UUID, tokenHash string, retryCount int, req *ResendInviteRequest) error {
					return nil
				},
				LogInviteEvent: func(batcher bqbatch.Batcher, eventType string, invite *UserInvite, actor string) error {
					return nil
				},
				RenderEmailTemplate: func(data EmailTemplateData) (string, error) {
					return "<html>Test Email</html>", nil
				},
				PublishEmailNotification: func(ctx context.Context, pubsubClient connect.PsClient, topicName string, message PubSubEmailMessage) error {
					return nil
				},
				CheckCooldownPeriod: func(invite *UserInvite) error {
					return errors.New("general cooldown check error")
				},
			},
			expectedStatus: 500, // Internal error for general cooldown error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Use invalid organization ID for the invalid_organization_id test case
			testOrgID := orgID
			if tt.name == "invalid_organization_id" {
				testOrgID = "invalid-uuid-format"
			}

			// Use invalid invite ID for the invalid_invite_id test case
			testInviteID := inviteID
			if tt.name == "invalid_invite_id" {
				testInviteID = "invalid-uuid-format"
			}

			req := httptest.NewRequest("PUT", "/api/organization/"+testOrgID+"/invites/"+testInviteID+"/resend", strings.NewReader(tt.body))
			req.Header.Set("Content-Type", "application/json")
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": testOrgID,
				"inviteId":       testInviteID,
			})

			w := httptest.NewRecorder()

			handler := ResendInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}
