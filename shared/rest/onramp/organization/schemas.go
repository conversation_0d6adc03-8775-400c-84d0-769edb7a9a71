package organization

import (
	"time"

	"github.com/google/uuid"
)

// Structure of Organization
type Organization struct {
	Id                uuid.UUID `json:"id" db:"id"`
	Name              string    `json:"name" db:"name"`
	Description       string    `json:"description" db:"description"`
	OrgTypeIdentifier string    `json:"orgtypeidentifier" db:"orgtypeidentifier"`
	CreatedAt         time.Time `json:"createdat" db:"createdat"`
	UpdatedAt         time.Time `json:"updatedat" db:"updatedat"`
	IsDeleted         bool      `json:"-" db:"isdeleted"`
}

// The request body for creating a new organization
type CreateAndUpdateOrganizationRequest struct {
	Name              string `json:"name" validate:"required,min=1"`
	Description       string `json:"description" validate:"required,min=1"`
	OrgTypeIdentifier string `json:"orgtypeidentifier" validate:"required,min=1"`
}

// The request body for updating an organization (OrgTypeIdentifier cannot be changed)
type UpdateOrganizationRequest struct {
	Name        string `json:"name" validate:"required,min=1"`
	Description string `json:"description" validate:"required,min=1"`
}

// The data model for the API response
type OrganizationResponse struct {
	Id                uuid.UUID `json:"id"`
	Name              string    `json:"name"`
	Description       string    `json:"description"`
	OrgTypeIdentifier string    `json:"orgtypeidentifier"`
	CreatedAt         time.Time `json:"createdat"`
	UpdatedAt         time.Time `json:"updatedat"`
}

// Converts an Organization model to an OrganizationResponse
func (o *Organization) ToResponse() OrganizationResponse {
	return OrganizationResponse{
		Id:                o.Id,
		Name:              o.Name,
		Description:       o.Description,
		OrgTypeIdentifier: o.OrgTypeIdentifier,
		CreatedAt:         o.CreatedAt,
		UpdatedAt:         o.UpdatedAt,
	}
}
