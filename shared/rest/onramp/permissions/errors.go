package permissions

import "errors"

var (
	ErrInvalidOrganizationId             = errors.New("invalid organization ID")
	ErrDatabaseOperation                 = errors.New("database operation failed")
	ErrInvalidPermissionId               = errors.New("invalid permission ID")
	ErrInvalidRoleId                     = errors.New("invalid role ID")
	ErrUnexpectedFields                  = errors.New("request contains unexpected fields")
	ErrInvalidRequestBody                = errors.New("invalid request body")
	ErrPermissionRoleCombinationNotFound = errors.New("permission-role combination not found")
)
