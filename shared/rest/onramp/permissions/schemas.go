package permissions

// PermissionValue represents a single permission with its current and default values
type PermissionValue struct {
	Name         string  `json:"name"`
	MachineName  string  `json:"machine_name"`
	Weight       float64 `json:"weight"`
	DefaultValue bool    `json:"default_value"`
	Value        bool    `json:"value"`
	Inherited    bool    `json:"inherited"`
}

// RolePermissions represents a role with its permissions
type RolePermissions struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Permissions []PermissionValue `json:"permissions"`
}

// PermissionGroupResponse represents a permission group with its roles and permissions
type PermissionGroupResponse struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Scope       string            `json:"scope"`
	Weight      float64           `json:"weight"`
	Roles       []RolePermissions `json:"roles"`
}

// PermissionsResponse is the top-level response structure
type PermissionsResponse struct {
	Permissions []PermissionGroupResponse `json:"permissions"`
}

// UpdatePermissionRequest represents the request body for updating a permission
type UpdatePermissionRequest struct {
	Value bool `json:"value" validate:"required"`
}

// PermissionMatrixRow represents a single row from the permissions matrix query
type PermissionMatrixRow struct {
	PgIdentifier    string  `db:"pg_identifier"`
	PgScope         string  `db:"pg_scope"`
	PgWeight        float64 `db:"pg_weight"`
	PgName          string  `db:"pg_name"`
	PgDescription   string  `db:"pg_description"`
	RoleID          string  `db:"role_id"`
	RoleName        string  `db:"role_name"`
	RoleDescription string  `db:"role_description"`
	PermIdentifier  string  `db:"perm_identifier"`
	PermWeight      float64 `db:"perm_weight"`
	PermName        string  `db:"perm_name"`
	PermDescription string  `db:"perm_description"`
	DefaultValue    bool    `db:"default_value"`
	CurrentValue    bool    `db:"current_value"`
	Inherited       bool    `db:"inherited"`
}
