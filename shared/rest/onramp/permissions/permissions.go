package permissions

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps defines dependencies for handlers to enable dependency injection
type HandlerDeps struct {
	GetConnections       func(context.Context, ...bool) (*connect.Connections, error)
	GetPermissionsMatrix func(connect.DatabaseExecutor, uuid.UUID) (*PermissionsResponse, error)
	UpdatePermission     func(connect.DatabaseExecutor, uuid.UUID, string, uuid.UUID, bool) error
}

// GetPermissionsHandlerWithDeps returns a handler for getting permissions matrix with dependency injection
func GetPermissionsHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate organization ID from path
		orgId, err := validateOrganizationId(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get permissions matrix for organization
		permissions, err := deps.GetPermissionsMatrix(pg, orgId)
		if err != nil {
			logger.Errorf("failed to get permissions for organization %s: %v", orgId, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(permissions, w)
	}
}

// UpdatePermissionHandlerWithDeps returns a handler for updating permissions with dependency injection
func UpdatePermissionHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Validate path parameters
		orgId, err := validateOrganizationId(r)
		if err != nil {
			logger.Errorf("invalid organization ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		permissionId, err := validatePermissionId(r)
		if err != nil {
			logger.Errorf("invalid permission ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		roleId, err := validateRoleId(r)
		if err != nil {
			logger.Errorf("invalid role ID: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse and validate request body
		req, err := parseUpdatePermissionRequest(r)
		if err != nil {
			logger.Errorf("invalid request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Update permission (this will also validate that the permission-role combination exists)
		err = deps.UpdatePermission(pg, orgId, permissionId, roleId, req.Value)
		if err != nil {
			if errors.Is(err, ErrPermissionRoleCombinationNotFound) {
				logger.Errorf("permission-role combination not found: %v", err)
				response.CreateNotFoundResponse(w)
				return
			}
			logger.Errorf("failed to update permission: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return success response
		response.CreateSuccessResponse(nil, w)
	}
}

// validateOrganizationId validates and extracts the organization ID from the request
func validateOrganizationId(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	orgIdStr := vars["organizationId"]

	if strings.TrimSpace(orgIdStr) == "" {
		return uuid.Nil, ErrInvalidOrganizationId
	}

	orgId, err := uuid.Parse(orgIdStr)
	if err != nil {
		return uuid.Nil, ErrInvalidOrganizationId
	}

	return orgId, nil
}

// validatePermissionId validates and extracts the permission ID from the request
func validatePermissionId(r *http.Request) (string, error) {
	vars := mux.Vars(r)
	permissionId := vars["permissionId"]

	if strings.TrimSpace(permissionId) == "" {
		return "", ErrInvalidPermissionId
	}

	// Validate permission identifier format (alphanumeric and underscores only)
	if !isValidPermissionIdentifier(permissionId) {
		return "", ErrInvalidPermissionId
	}

	return permissionId, nil
}

// isValidPermissionIdentifier validates the format of a permission identifier
func isValidPermissionIdentifier(permissionId string) bool {
	// Permission identifiers should be lowercase letters, numbers, and underscores only
	// and should not be empty or too long
	if len(permissionId) == 0 || len(permissionId) > 100 {
		return false
	}

	for _, char := range permissionId {
		if !((char >= 'a' && char <= 'z') || (char >= '0' && char <= '9') || char == '_') {
			return false
		}
	}
	return true
}

// validateRoleId validates and extracts the role ID from the request
func validateRoleId(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	roleIdStr := vars["roleId"]

	if strings.TrimSpace(roleIdStr) == "" {
		return uuid.Nil, ErrInvalidRoleId
	}

	roleId, err := uuid.Parse(roleIdStr)
	if err != nil {
		return uuid.Nil, ErrInvalidRoleId
	}

	return roleId, nil
}

// parseUpdatePermissionRequest parses and validates the update permission request body
func parseUpdatePermissionRequest(r *http.Request) (*UpdatePermissionRequest, error) {
	var req UpdatePermissionRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unexpected fields

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse update permission request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return nil, ErrUnexpectedFields
		}
		return nil, ErrInvalidRequestBody
	}

	return &req, nil
}

// getPermissionsMatrix retrieves the complete permissions matrix for an organization
var getPermissionsMatrix = func(pg connect.DatabaseExecutor, orgId uuid.UUID) (*PermissionsResponse, error) {
	query := `
		SELECT 
			pg.Identifier as pg_identifier,
			pg.Scope as pg_scope,
			pg.Weight as pg_weight,
			pg.Name as pg_name,
			pg.Description as pg_description,
			cr.Id as role_id,
			cr.Name as role_name,
			cr.Description as role_description,
			p.Identifier as perm_identifier,
			p.Weight as perm_weight,
			p.Name as perm_name,
			p.Description as perm_description,
			trp.DefaultValue as default_value,
			COALESCE(crp.Value, trp.DefaultValue) as current_value,
			CASE WHEN crp.Value IS NULL OR crp.Value = trp.DefaultValue THEN true ELSE false END as inherited
		FROM {{CustomRole}} cr 
		JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
		JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
		JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
		JOIN {{PermissionGroup}} pg ON p.PermissionGroupIdentifier = pg.Identifier
		LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND p.Identifier = crp.PermissionIdentifier
		WHERE cr.OrganizationId = $1 AND cr.IsDeleted = false AND trp.IsDeleted = false
		ORDER BY pg.Weight, p.Weight, cr.Name`

	var rows []PermissionMatrixRow
	err := pg.QueryGenericSlice(&rows, query, orgId)
	if err != nil {
		logger.Errorf("failed to query permissions matrix: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return processPermissionRows(rows)
}

// processPermissionRows processes the query results and builds the hierarchical response structure
func processPermissionRows(rows []PermissionMatrixRow) (*PermissionsResponse, error) {
	groupMap := make(map[string]*PermissionGroupResponse)
	roleMap := make(map[string]map[string]*RolePermissions)

	for _, row := range rows {
		// Parse role ID
		roleId, err := uuid.Parse(row.RoleID)
		if err != nil {
			logger.Errorf("failed to parse role ID: %v", err)
			continue
		}

		// Initialize group if not exists
		if _, exists := groupMap[row.PgIdentifier]; !exists {
			groupMap[row.PgIdentifier] = &PermissionGroupResponse{
				Name:        row.PgName,
				Description: row.PgDescription,
				Scope:       row.PgScope,
				Weight:      row.PgWeight,
				Roles:       []RolePermissions{},
			}
			roleMap[row.PgIdentifier] = make(map[string]*RolePermissions)
		}

		// Initialize role if not exists
		roleKey := roleId.String()
		if _, exists := roleMap[row.PgIdentifier][roleKey]; !exists {
			roleMap[row.PgIdentifier][roleKey] = &RolePermissions{
				Name:        row.RoleName,
				Description: row.RoleDescription,
				Permissions: []PermissionValue{},
			}
		}

		// Add permission to role
		permission := PermissionValue{
			Name:         row.PermName,
			MachineName:  row.PermIdentifier,
			Weight:       row.PermWeight,
			DefaultValue: row.DefaultValue,
			Value:        row.CurrentValue,
			Inherited:    row.Inherited,
		}

		roleMap[row.PgIdentifier][roleKey].Permissions = append(
			roleMap[row.PgIdentifier][roleKey].Permissions,
			permission,
		)
	}

	return buildResponse(groupMap, roleMap), nil
}

// buildResponse converts the maps to the final response structure with proper sorting
func buildResponse(groupMap map[string]*PermissionGroupResponse, roleMap map[string]map[string]*RolePermissions) *PermissionsResponse {
	var groups []PermissionGroupResponse

	// Sort groups by weight
	var groupKeys []string
	for key := range groupMap {
		groupKeys = append(groupKeys, key)
	}
	sort.Slice(groupKeys, func(i, j int) bool {
		return groupMap[groupKeys[i]].Weight < groupMap[groupKeys[j]].Weight
	})

	for _, groupKey := range groupKeys {
		group := groupMap[groupKey]

		// Sort roles within group
		var roles []RolePermissions
		for _, role := range roleMap[groupKey] {
			// Sort permissions within role by weight
			sort.Slice(role.Permissions, func(i, j int) bool {
				return role.Permissions[i].Weight < role.Permissions[j].Weight
			})
			roles = append(roles, *role)
		}

		// Sort roles by name for consistency
		sort.Slice(roles, func(i, j int) bool {
			return roles[i].Name < roles[j].Name
		})

		group.Roles = roles
		groups = append(groups, *group)
	}

	return &PermissionsResponse{Permissions: groups}
}

// updatePermission updates a permission value for a role
var updatePermission = func(pg connect.DatabaseExecutor, orgId uuid.UUID, permissionId string, roleId uuid.UUID, value bool) error {
	// First, get the default value for this permission-role combination to validate it exists
	defaultQuery := `
		SELECT trp.DefaultValue
		FROM {{CustomRole}} cr
		JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
		JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier
		WHERE cr.Id = $1 AND cr.OrganizationId = $2 AND trp.PermissionIdentifier = $3 AND cr.IsDeleted = false AND trp.IsDeleted = false`

	row, err := pg.QueryRow(defaultQuery, roleId, orgId, permissionId)
	if err != nil {
		logger.Errorf("failed to query default value: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	defaultValueInterface, exists := row["defaultvalue"]
	if !exists {
		return ErrPermissionRoleCombinationNotFound
	}

	// Validate the default value type (we don't need to use it, but we should validate it's a bool)
	_, ok := defaultValueInterface.(bool)
	if !ok {
		logger.Errorf("failed to convert default value to bool")
		return fmt.Errorf("%w: invalid default value type", ErrDatabaseOperation)
	}

	now := time.Now().UTC()

	// Always upsert the custom role permission record
	upsertQuery := `
		INSERT INTO {{CustomRolePermission}} (CustomRoleId, PermissionIdentifier, Value, IsDeleted, CreatedAt, UpdatedAt)
		VALUES ($1, $2, $3, false, $4, $4)
		ON CONFLICT (CustomRoleId, PermissionIdentifier)
		DO UPDATE SET Value = $3, UpdatedAt = $4, IsDeleted = false`

	_, err = pg.Exec(upsertQuery, roleId, permissionId, value, now)
	if err != nil {
		logger.Errorf("failed to upsert custom role permission: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return nil
}

// Production handlers with default dependencies
var (
	GetPermissionsHandler = GetPermissionsHandlerWithDeps(HandlerDeps{
		GetConnections:       connect.GetConnections,
		GetPermissionsMatrix: getPermissionsMatrix,
	})

	UpdatePermissionHandler = UpdatePermissionHandlerWithDeps(HandlerDeps{
		GetConnections:   connect.GetConnections,
		UpdatePermission: updatePermission,
	})
)
