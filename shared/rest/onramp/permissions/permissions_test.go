package permissions

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// --- validateOrganizationId ---
func Test_validateOrganizationId(t *testing.T) {
	t.<PERSON>llel()
	tests := []struct {
		name     string
		vars     map[string]string
		expected uuid.UUID
		wantErr  error
	}{
		{
			name:     "valid uuid",
			vars:     map[string]string{"organizationId": uuid.New().String()},
			expected: uuid.Nil, // Will be set by the function
			wantErr:  nil,
		},
		{
			name:     "empty orgId",
			vars:     map[string]string{"organizationId": ""},
			expected: uuid.Nil,
			wantErr:  ErrInvalidOrganizationId,
		},
		{
			name:     "invalid uuid",
			vars:     map[string]string{"organizationId": "not-a-uuid"},
			expected: uuid.Nil,
			wantErr:  ErrInvalidOrganizationId,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request with URL vars
			r := httptest.NewRequest("GET", "/", nil)
			r = mux.SetURLVars(r, tt.vars)

			// Execute test
			result, err := validateOrganizationId(r)

			// Assert results
			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, result)
			}
		})
	}
}

// --- validatePermissionId ---
func Test_validatePermissionId(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name     string
		vars     map[string]string
		expected string
		wantErr  error
	}{
		{
			name:     "valid",
			vars:     map[string]string{"permissionId": "perm_1"},
			expected: "perm_1",
			wantErr:  nil,
		},
		{
			name:     "empty",
			vars:     map[string]string{"permissionId": ""},
			expected: "",
			wantErr:  ErrInvalidPermissionId,
		},
		{
			name:     "invalid format",
			vars:     map[string]string{"permissionId": "bad!id"},
			expected: "",
			wantErr:  ErrInvalidPermissionId,
		},
		{
			name:     "too long",
			vars:     map[string]string{"permissionId": string(bytes.Repeat([]byte{'a'}, 101))},
			expected: "",
			wantErr:  ErrInvalidPermissionId,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request with URL vars
			r := httptest.NewRequest("GET", "/", nil)
			r = mux.SetURLVars(r, tt.vars)

			// Execute test
			got, err := validatePermissionId(r)

			// Assert results
			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.Equal(t, tt.expected, got)
				assert.NoError(t, err)
			}
		})
	}
}

// --- isValidPermissionIdentifier ---
func Test_isValidPermissionIdentifier(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name     string
		id       string
		expected bool
	}{
		{
			name:     "valid",
			id:       "perm_1",
			expected: true,
		},
		{
			name:     "empty",
			id:       "",
			expected: false,
		},
		{
			name:     "too long",
			id:       string(bytes.Repeat([]byte{'a'}, 101)),
			expected: false,
		},
		{
			name:     "invalid char",
			id:       "bad!id",
			expected: false,
		},
		{
			name:     "upper case",
			id:       "PERM",
			expected: false,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			result := isValidPermissionIdentifier(tt.id)

			// Assert results
			assert.Equal(t, tt.expected, result)
		})
	}
}

// --- validateRoleId ---
func Test_validateRoleId(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name     string
		vars     map[string]string
		expected uuid.UUID
		wantErr  error
	}{
		{
			name:     "valid",
			vars:     map[string]string{"roleId": uuid.New().String()},
			expected: uuid.Nil, // Will be set by the function
			wantErr:  nil,
		},
		{
			name:     "empty",
			vars:     map[string]string{"roleId": ""},
			expected: uuid.Nil,
			wantErr:  ErrInvalidRoleId,
		},
		{
			name:     "invalid",
			vars:     map[string]string{"roleId": "bad"},
			expected: uuid.Nil,
			wantErr:  ErrInvalidRoleId,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request with URL vars
			r := httptest.NewRequest("GET", "/", nil)
			r = mux.SetURLVars(r, tt.vars)

			// Execute test
			result, err := validateRoleId(r)

			// Assert results
			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, result)
			}
		})
	}
}

// --- parseUpdatePermissionRequest ---
func Test_parseUpdatePermissionRequest(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name     string
		body     string
		expected *UpdatePermissionRequest
		wantErr  error
	}{
		{
			name:     "valid",
			body:     `{"value":true}`,
			expected: &UpdatePermissionRequest{Value: true},
			wantErr:  nil,
		},
		{
			name:     "unknown field",
			body:     `{"value":true,"foo":1}`,
			expected: nil,
			wantErr:  ErrUnexpectedFields,
		},
		{
			name:     "invalid json",
			body:     `{`,
			expected: nil,
			wantErr:  ErrInvalidRequestBody,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request with body
			r := httptest.NewRequest("POST", "/", bytes.NewBufferString(tt.body))

			// Execute test
			got, err := parseUpdatePermissionRequest(r)

			// Assert results
			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.Equal(t, tt.expected, got)
				assert.NoError(t, err)
			}
		})
	}
}

// --- processPermissionResults ---
func Test_processPermissionRows(t *testing.T) {
	t.Parallel()
	roleId := uuid.New()

	tests := []struct {
		name           string
		results        []PermissionMatrixRow
		expectedGroups int
		expectedRoles  int
		expectedPerms  int
		checkNames     bool
		groupName      string
		roleName       string
		permName       string
	}{
		{
			name: "single result",
			results: []PermissionMatrixRow{
				{
					PgIdentifier:    "group1",
					PgScope:         "org",
					PgWeight:        1,
					PgName:          "Group 1",
					PgDescription:   "desc",
					RoleID:          roleId.String(),
					RoleName:        "Role1",
					RoleDescription: "desc",
					PermIdentifier:  "perm_1",
					PermWeight:      1,
					PermName:        "Perm1",
					DefaultValue:    true,
					CurrentValue:    false,
					Inherited:       false,
				},
			},
			expectedGroups: 1,
			expectedRoles:  1,
			expectedPerms:  1,
			checkNames:     true,
			groupName:      "Group 1",
			roleName:       "Role1",
			permName:       "Perm1",
		},
		{
			name:           "empty results",
			results:        []PermissionMatrixRow{},
			expectedGroups: 0,
			expectedRoles:  0,
			expectedPerms:  0,
			checkNames:     false,
		},
		{
			name: "invalid role id",
			results: []PermissionMatrixRow{
				{
					PgIdentifier:    "group1",
					PgScope:         "org",
					PgWeight:        1,
					PgName:          "Group 1",
					PgDescription:   "desc",
					RoleID:          "invalid-uuid",
					RoleName:        "Role1",
					RoleDescription: "desc",
					PermIdentifier:  "perm_1",
					PermWeight:      1,
					PermName:        "Perm1",
					DefaultValue:    true,
					CurrentValue:    false,
					Inherited:       false,
				},
			},
			expectedGroups: 0, // Invalid role ID should be skipped
			expectedRoles:  0,
			expectedPerms:  0,
			checkNames:     false,
		},
		{
			name: "multiple results with different roles",
			results: []PermissionMatrixRow{
				{
					PgIdentifier:    "group1",
					PgScope:         "org",
					PgWeight:        1,
					PgName:          "Group 1",
					PgDescription:   "desc",
					RoleID:          uuid.New().String(),
					RoleName:        "Role1",
					RoleDescription: "desc",
					PermIdentifier:  "perm_1",
					PermWeight:      1,
					PermName:        "Perm1",
					DefaultValue:    true,
					CurrentValue:    false,
					Inherited:       false,
				},
				{
					PgIdentifier:    "group1",
					PgScope:         "org",
					PgWeight:        1,
					PgName:          "Group 1",
					PgDescription:   "desc",
					RoleID:          uuid.New().String(),
					RoleName:        "Role2",
					RoleDescription: "desc2",
					PermIdentifier:  "perm_2",
					PermWeight:      2,
					PermName:        "Perm2",
					DefaultValue:    false,
					CurrentValue:    true,
					Inherited:       true,
				},
			},
			expectedGroups: 1,
			expectedRoles:  2,
			expectedPerms:  1, // Each role has 1 permission
			checkNames:     true,
			groupName:      "Group 1",
			roleName:       "Role1", // First role name
			permName:       "Perm1", // First permission name
		},
		{
			name: "permissions sorted by weight",
			results: []PermissionMatrixRow{
				{
					PgIdentifier:    "group1",
					PgScope:         "org",
					PgWeight:        1,
					PgName:          "Group 1",
					PgDescription:   "desc",
					RoleID:          roleId.String(), // Use the same role ID for both permissions
					RoleName:        "Role1",
					RoleDescription: "desc",
					PermIdentifier:  "perm_2",
					PermWeight:      2,
					PermName:        "Perm2",
					DefaultValue:    true,
					CurrentValue:    false,
					Inherited:       false,
				},
				{
					PgIdentifier:    "group1",
					PgScope:         "org",
					PgWeight:        1,
					PgName:          "Group 1",
					PgDescription:   "desc",
					RoleID:          roleId.String(), // Use the same role ID for both permissions
					RoleName:        "Role1",
					RoleDescription: "desc",
					PermIdentifier:  "perm_1",
					PermWeight:      1,
					PermName:        "Perm1",
					DefaultValue:    false,
					CurrentValue:    true,
					Inherited:       true,
				},
			},
			expectedGroups: 1,
			expectedRoles:  1,
			expectedPerms:  2, // Same role has 2 permissions
			checkNames:     true,
			groupName:      "Group 1",
			roleName:       "Role1",
			permName:       "Perm1", // First permission name (sorted by weight, perm_1 comes first)
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			resp, err := processPermissionRows(tt.results)

			// Assert results
			assert.NoError(t, err)
			assert.Len(t, resp.Permissions, tt.expectedGroups)

			if tt.checkNames && tt.expectedGroups > 0 {
				assert.Equal(t, tt.groupName, resp.Permissions[0].Name)
				assert.Len(t, resp.Permissions[0].Roles, tt.expectedRoles)
				if tt.expectedRoles > 0 {
					assert.Equal(t, tt.roleName, resp.Permissions[0].Roles[0].Name)
					assert.Len(t, resp.Permissions[0].Roles[0].Permissions, tt.expectedPerms)
					if tt.expectedPerms > 0 {
						assert.Equal(t, tt.permName, resp.Permissions[0].Roles[0].Permissions[0].Name)
					}
				}
			}
		})
	}
}

// --- buildResponse ---
func Test_buildResponse(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		groupMap      map[string]*PermissionGroupResponse
		roleMap       map[string]map[string]*RolePermissions
		expectedLen   int
		checkSorting  bool
		expectedNames []string
	}{
		{
			name: "single group and role",
			groupMap: map[string]*PermissionGroupResponse{
				"g": {Name: "G", Weight: 1, Roles: []RolePermissions{}},
			},
			roleMap: map[string]map[string]*RolePermissions{
				"g": {"r": {Name: "R", Permissions: []PermissionValue{}}},
			},
			expectedLen:   1,
			checkSorting:  false,
			expectedNames: []string{"G"},
		},
		{
			name:         "empty maps",
			groupMap:     map[string]*PermissionGroupResponse{},
			roleMap:      map[string]map[string]*RolePermissions{},
			expectedLen:  0,
			checkSorting: false,
		},
		{
			name: "multiple groups and roles",
			groupMap: map[string]*PermissionGroupResponse{
				"g1": {Name: "G1", Weight: 2, Roles: []RolePermissions{}},
				"g2": {Name: "G2", Weight: 1, Roles: []RolePermissions{}},
			},
			roleMap: map[string]map[string]*RolePermissions{
				"g1": {"r1": {Name: "R1", Permissions: []PermissionValue{}}},
				"g2": {"r2": {Name: "R2", Permissions: []PermissionValue{}}},
			},
			expectedLen:   2,
			checkSorting:  true,
			expectedNames: []string{"G2", "G1"}, // Should be sorted by weight
		},
		{
			name: "three groups with different weights",
			groupMap: map[string]*PermissionGroupResponse{
				"g1": {Name: "G1", Weight: 3, Roles: []RolePermissions{}},
				"g2": {Name: "G2", Weight: 1, Roles: []RolePermissions{}},
				"g3": {Name: "G3", Weight: 2, Roles: []RolePermissions{}},
			},
			roleMap: map[string]map[string]*RolePermissions{
				"g1": {"r1": {Name: "R1", Permissions: []PermissionValue{}}},
				"g2": {"r2": {Name: "R2", Permissions: []PermissionValue{}}},
				"g3": {"r3": {Name: "R3", Permissions: []PermissionValue{}}},
			},
			expectedLen:   3,
			checkSorting:  true,
			expectedNames: []string{"G2", "G3", "G1"}, // Should be sorted by weight
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			resp := buildResponse(tt.groupMap, tt.roleMap)

			// Assert results
			assert.Len(t, resp.Permissions, tt.expectedLen)

			if tt.checkSorting && tt.expectedLen > 0 {
				for i, expectedName := range tt.expectedNames {
					assert.Equal(t, expectedName, resp.Permissions[i].Name)
				}
			}
		})
	}
}

// --- HandlerDeps: GetPermissionsHandlerWithDeps ---
func Test_GetPermissionsHandlerWithDeps(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()
	fakePerms := &PermissionsResponse{Permissions: []PermissionGroupResponse{}}
	fakeConns := &connect.Connections{Postgres: &mocks.FakeDBExecutor{}}
	called := struct{ getConns, getMatrix bool }{}
	deps := HandlerDeps{
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			called.getConns = true
			return fakeConns, nil
		},
		GetPermissionsMatrix: func(db connect.DatabaseExecutor, id uuid.UUID) (*PermissionsResponse, error) {
			called.getMatrix = true
			assert.Equal(t, orgId, id)
			return fakePerms, nil
		},
	}
	w := httptest.NewRecorder()
	r := httptest.NewRequest("GET", "/", nil)
	r = mux.SetURLVars(r, map[string]string{"organizationId": orgId.String()})
	GetPermissionsHandlerWithDeps(deps)(w, r)
	assert.True(t, called.getConns)
	assert.True(t, called.getMatrix)
	assert.Equal(t, http.StatusOK, w.Code)
}

// --- HandlerDeps: UpdatePermissionHandlerWithDeps ---
func Test_UpdatePermissionHandlerWithDeps(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()
	roleId := uuid.New()
	permId := "perm_1"
	fakeConns := &connect.Connections{Postgres: &mocks.FakeDBExecutor{}}
	called := struct{ getConns, update bool }{}
	deps := HandlerDeps{
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			called.getConns = true
			return fakeConns, nil
		},
		UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
			called.update = true
			assert.Equal(t, orgId, o)
			assert.Equal(t, permId, p)
			assert.Equal(t, roleId, r)
			assert.True(t, v)
			return nil
		},
	}
	w := httptest.NewRecorder()
	body, _ := json.Marshal(UpdatePermissionRequest{Value: true})
	r := httptest.NewRequest("POST", "/", bytes.NewBuffer(body))
	r = mux.SetURLVars(r, map[string]string{
		"organizationId": orgId.String(),
		"permissionId":   permId,
		"roleId":         roleId.String(),
	})
	UpdatePermissionHandlerWithDeps(deps)(w, r)
	assert.True(t, called.getConns)
	assert.True(t, called.update)
	assert.Equal(t, http.StatusOK, w.Code)
}

// --- Error path tests for GetPermissionsHandlerWithDeps ---
func Test_GetPermissionsHandlerWithDeps_Errors(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()
	fakeConns := &connect.Connections{Postgres: &mocks.FakeDBExecutor{}}

	tests := []struct {
		name         string
		setupDepsFn  func() HandlerDeps
		urlVars      map[string]string
		expectedCode int
	}{
		{
			name: "get connections error",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
					GetPermissionsMatrix: func(db connect.DatabaseExecutor, id uuid.UUID) (*PermissionsResponse, error) {
						return nil, nil
					},
				}
			},
			urlVars:      map[string]string{"organizationId": orgId.String()},
			expectedCode: http.StatusInternalServerError,
		},
		{
			name: "invalid organization id",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					GetPermissionsMatrix: func(db connect.DatabaseExecutor, id uuid.UUID) (*PermissionsResponse, error) {
						return nil, nil
					},
				}
			},
			urlVars:      map[string]string{"organizationId": "invalid"},
			expectedCode: http.StatusBadRequest,
		},
		{
			name: "get permissions matrix error",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					GetPermissionsMatrix: func(db connect.DatabaseExecutor, id uuid.UUID) (*PermissionsResponse, error) {
						return nil, errors.New("matrix failed")
					},
				}
			},
			urlVars:      map[string]string{"organizationId": orgId.String()},
			expectedCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup dependencies
			deps := tt.setupDepsFn()

			// Create request
			w := httptest.NewRecorder()
			r := httptest.NewRequest("GET", "/", nil)
			r = mux.SetURLVars(r, tt.urlVars)

			// Execute test
			GetPermissionsHandlerWithDeps(deps)(w, r)

			// Assert results
			assert.Equal(t, tt.expectedCode, w.Code)
		})
	}
}

// --- Error path tests for UpdatePermissionHandlerWithDeps ---
func Test_UpdatePermissionHandlerWithDeps_Errors(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()
	roleId := uuid.New()
	permId := "perm_1"
	fakeConns := &connect.Connections{Postgres: &mocks.FakeDBExecutor{}}

	tests := []struct {
		name         string
		setupDepsFn  func() HandlerDeps
		requestBody  string
		urlVars      map[string]string
		expectedCode int
	}{
		{
			name: "get connections error",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
					UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
						return nil
					},
				}
			},
			requestBody:  `{"value":true}`,
			urlVars:      map[string]string{"organizationId": orgId.String(), "permissionId": permId, "roleId": roleId.String()},
			expectedCode: http.StatusInternalServerError,
		},
		{
			name: "invalid organization id",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
						return nil
					},
				}
			},
			requestBody:  `{"value":true}`,
			urlVars:      map[string]string{"organizationId": "invalid", "permissionId": permId, "roleId": roleId.String()},
			expectedCode: http.StatusBadRequest,
		},
		{
			name: "invalid permission id",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
						return nil
					},
				}
			},
			requestBody:  `{"value":true}`,
			urlVars:      map[string]string{"organizationId": orgId.String(), "permissionId": "bad!id", "roleId": roleId.String()},
			expectedCode: http.StatusBadRequest,
		},
		{
			name: "invalid role id",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
						return nil
					},
				}
			},
			requestBody:  `{"value":true}`,
			urlVars:      map[string]string{"organizationId": orgId.String(), "permissionId": permId, "roleId": "invalid"},
			expectedCode: http.StatusBadRequest,
		},
		{
			name: "invalid request body",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
						return nil
					},
				}
			},
			requestBody:  "invalid json",
			urlVars:      map[string]string{"organizationId": orgId.String(), "permissionId": permId, "roleId": roleId.String()},
			expectedCode: http.StatusBadRequest,
		},
		{
			name: "permission role combination not found",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
						return ErrPermissionRoleCombinationNotFound
					},
				}
			},
			requestBody:  `{"value":true}`,
			urlVars:      map[string]string{"organizationId": orgId.String(), "permissionId": permId, "roleId": roleId.String()},
			expectedCode: http.StatusNotFound,
		},
		{
			name: "update permission error",
			setupDepsFn: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
						return fakeConns, nil
					},
					UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
						return errors.New("update failed")
					},
				}
			},
			requestBody:  `{"value":true}`,
			urlVars:      map[string]string{"organizationId": orgId.String(), "permissionId": permId, "roleId": roleId.String()},
			expectedCode: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup dependencies
			deps := tt.setupDepsFn()

			// Create request
			w := httptest.NewRecorder()
			r := httptest.NewRequest("POST", "/", bytes.NewBufferString(tt.requestBody))
			r = mux.SetURLVars(r, tt.urlVars)

			// Execute test
			UpdatePermissionHandlerWithDeps(deps)(w, r)

			// Assert results
			assert.Equal(t, tt.expectedCode, w.Code)
		})
	}
}

// --- Production handler tests ---
func Test_GetPermissionsHandler(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()
	w := httptest.NewRecorder()
	r := httptest.NewRequest("GET", "/", nil)
	r = mux.SetURLVars(r, map[string]string{"organizationId": orgId.String()})

	// Test that the handler calls the correct dependencies
	// We can't easily mock the internal functions, so we test the integration
	// by ensuring the handler doesn't panic and returns appropriate status
	GetPermissionsHandler(w, r)
	// Should return 500 since we don't have real connections in test
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func Test_UpdatePermissionHandler(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()
	roleId := uuid.New()
	permId := "perm_1"
	w := httptest.NewRecorder()
	body, _ := json.Marshal(UpdatePermissionRequest{Value: true})
	r := httptest.NewRequest("POST", "/", bytes.NewBuffer(body))
	r = mux.SetURLVars(r, map[string]string{
		"organizationId": orgId.String(),
		"permissionId":   permId,
		"roleId":         roleId.String(),
	})

	// Test that the handler calls the correct dependencies
	// We can't easily mock the internal functions, so we test the integration
	// by ensuring the handler doesn't panic and returns appropriate status
	UpdatePermissionHandler(w, r)
	// Should return 500 since we don't have real connections in test
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

// --- Additional edge case tests ---

// --- updatePermission function tests ---
func Test_updatePermission(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()
	roleId := uuid.New()
	permId := "perm_1"

	tests := []struct {
		name          string
		setupMockFn   func() *mocks.FakeDBExecutor
		value         bool
		expectedError error
	}{
		{
			name: "successful upsert",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"defaultvalue": false}, nil
				}
				fakeDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeResult{}, nil
				}
				return fakeDB
			},
			value:         true,
			expectedError: nil,
		},
		{
			name: "successful upsert with same value as default",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"defaultvalue": true}, nil
				}
				fakeDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeResult{}, nil
				}
				return fakeDB
			},
			value:         true,
			expectedError: nil,
		},
		{
			name: "query default value error",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("db error")
				}
				return fakeDB
			},
			value:         true,
			expectedError: ErrDatabaseOperation,
		},
		{
			name: "permission role combination not found",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{}, nil
				}
				return fakeDB
			},
			value:         true,
			expectedError: ErrPermissionRoleCombinationNotFound,
		},
		{
			name: "invalid default value type",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"defaultvalue": "not a bool"}, nil
				}
				return fakeDB
			},
			value:         true,
			expectedError: ErrDatabaseOperation,
		},
		{
			name: "exec error during upsert",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"defaultvalue": false}, nil
				}
				fakeDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("exec failed")
				}
				return fakeDB
			},
			value:         true,
			expectedError: ErrDatabaseOperation,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			fakeDB := tt.setupMockFn()

			// Execute test
			err := updatePermission(fakeDB, orgId, permId, roleId, tt.value)

			// Assert results
			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// --- getPermissionsMatrix function tests ---
func Test_getPermissionsMatrix(t *testing.T) {
	t.Parallel()
	orgId := uuid.New()

	tests := []struct {
		name          string
		setupMockFn   func() *mocks.FakeDBExecutor
		expectedError error
		expectedLen   int
	}{
		{
			name: "successful query",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryGenericSliceFunc = func(slice interface{}, query string, args ...interface{}) error {
					assert.Equal(t, orgId, args[0])
					rows := slice.(*[]PermissionMatrixRow)
					*rows = []PermissionMatrixRow{
						{
							PgIdentifier:    "group1",
							PgScope:         "org",
							PgWeight:        1,
							PgName:          "Group 1",
							PgDescription:   "desc",
							RoleID:          uuid.New().String(),
							RoleName:        "Role1",
							RoleDescription: "desc",
							PermIdentifier:  "perm_1",
							PermWeight:      1,
							PermName:        "Perm1",
							DefaultValue:    true,
							CurrentValue:    false,
							Inherited:       false,
						},
					}
					return nil
				}
				return fakeDB
			},
			expectedError: nil,
			expectedLen:   1,
		},
		{
			name: "database error",
			setupMockFn: func() *mocks.FakeDBExecutor {
				fakeDB := &mocks.FakeDBExecutor{}
				fakeDB.QueryGenericSliceFunc = func(slice interface{}, query string, args ...interface{}) error {
					return errors.New("db error")
				}
				return fakeDB
			},
			expectedError: ErrDatabaseOperation,
			expectedLen:   0,
		},
	}

	for _, tt := range tests {
		tt := tt // capture
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			fakeDB := tt.setupMockFn()

			// Execute test
			result, err := getPermissionsMatrix(fakeDB, orgId)

			// Assert results
			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, result.Permissions, tt.expectedLen)
			}
		})
	}
}

// --- Helper struct for testing ---
type fakeResult struct{}

func (fr fakeResult) LastInsertId() (int64, error) { return 0, nil }
func (fr fakeResult) RowsAffected() (int64, error) { return 1, nil }

// --- Additional helper struct for testing ---
type fakeResultWithError struct{}

func (fr fakeResultWithError) LastInsertId() (int64, error) { return 0, nil }
func (fr fakeResultWithError) RowsAffected() (int64, error) {
	return 0, errors.New("rows affected error")
}

// --- HandlerDeps struct tests ---
func Test_HandlerDeps_Structure(t *testing.T) {
	t.Parallel()

	// Test that HandlerDeps can be created with all required fields
	deps := HandlerDeps{
		GetConnections: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
			return &connect.Connections{}, nil
		},
		GetPermissionsMatrix: func(db connect.DatabaseExecutor, id uuid.UUID) (*PermissionsResponse, error) {
			return &PermissionsResponse{}, nil
		},
		UpdatePermission: func(db connect.DatabaseExecutor, o uuid.UUID, p string, r uuid.UUID, v bool) error {
			return nil
		},
	}

	// Test that the functions can be called
	ctx := context.Background()
	conns, err := deps.GetConnections(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, conns)

	fakeDB := &mocks.FakeDBExecutor{}
	orgId := uuid.New()
	perms, err := deps.GetPermissionsMatrix(fakeDB, orgId)
	assert.NoError(t, err)
	assert.NotNil(t, perms)

	roleId := uuid.New()
	err = deps.UpdatePermission(fakeDB, orgId, "perm_1", roleId, true)
	assert.NoError(t, err)
}

// --- Additional validation tests ---
func Test_isValidPermissionIdentifier_EdgeCases(t *testing.T) {
	t.Parallel()
	tests := []struct {
		name string
		id   string
		ok   bool
	}{
		{"valid with numbers", "perm_123", true},
		{"valid with underscores", "perm_name_123", true},
		{"single character", "a", true},
		{"max length", string(bytes.Repeat([]byte{'a'}, 100)), true},
		{"exactly max length", string(bytes.Repeat([]byte{'a'}, 100)), true},
		{"one over max length", string(bytes.Repeat([]byte{'a'}, 101)), false},
		{"mixed case", "Perm_123", false},
		{"special chars", "perm@123", false},
		{"spaces", "perm 123", false},
		{"hyphens", "perm-123", false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tt.ok, isValidPermissionIdentifier(tt.id))
		})
	}
}
