package helper

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
)

var randRead = rand.Read

// Validate identifier
func ValidateIdentifier(identifier string) error {
	if strings.TrimSpace(identifier) == "" {
		return ErrInvalidIdentifier
	}
	return nil
}

// Validate OrganizationID
func ValidateOrganizationID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	orgIDStr := vars["organizationId"]
	if strings.TrimSpace(orgIDStr) == "" {
		return uuid.Nil, ErrInvalidOrganizationID
	}

	// Check if the organizationId is a valid UUID
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidOrganizationID
	}
	return orgID, nil
}

// Validate InviteID
func ValidateInviteID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	inviteIDStr := vars["inviteId"]
	if strings.TrimSpace(inviteIDStr) == "" {
		return uuid.Nil, ErrInvalidInviteID
	}

	// Check if the inviteID is a valid UUID
	inviteID, err := uuid.Parse(inviteIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidInviteID
	}
	return inviteID, nil
}

// ValidateUserID validates the user ID from the request
func ValidateUserID(r *http.Request) (uuid.UUID, error) {
	vars := mux.Vars(r)
	userIDStr := vars["userId"]
	if strings.TrimSpace(userIDStr) == "" {
		return uuid.Nil, ErrInvalidUserID
	}

	// Check if the userID is a valid UUID
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return uuid.Nil, ErrInvalidUserID
	}

	return userID, nil
}

// GenerateRandomTokenHex returns a hex-encoded random token of the specified length.
// It errors if length is negative or if the random source fails.
func GenerateRandomTokenHex(length uint) (string, error) {
	// 2 hex characters per byte
	sz := length / 2
	b := make([]byte, sz)
	if _, err := randRead(b); err != nil {
		return "", err
	}
	return hex.EncodeToString(b), nil
}
