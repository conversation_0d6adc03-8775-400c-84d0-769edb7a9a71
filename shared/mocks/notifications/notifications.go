package notifications

import (
	"context"

	"github.com/stretchr/testify/mock"
)

// MockNotificationService is a mock implementation of the NotificationService interface
type MockNotificationService struct {
	mock.Mock
}

// SendSMS mocks the SendSMS method
func (m *MockNotificationService) SendSMS(ctx context.Context, toPhone string, messageBody string) error {
	args := m.Called(ctx, toPhone, messageBody)
	return args.Error(0)
}

// MockTwilioClient is a mock implementation for testing Twilio client functionality
type MockTwilioClient struct {
	mock.Mock
}

// NewMockTwilioClient creates a new mock Twilio client
func NewMockTwilioClient() *MockTwilioClient {
	return &MockTwilioClient{}
}
