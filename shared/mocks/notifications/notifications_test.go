package notifications

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func Test_MockNotificationService_SendSMS(t *testing.T) {
	tests := []struct {
		name        string
		toPhone     string
		messageBody string
		setupMock   func(*MockNotificationService)
		wantErr     bool
		expectedErr error
	}{
		{
			name:        "success - sends SMS without error",
			toPhone:     "+1234567890",
			messageBody: "Test message",
			setupMock: func(m *MockNotificationService) {
				m.On("SendSMS", context.Background(), "+1234567890", "Test message").Return(nil)
			},
			wantErr: false,
		},
		{
			name:        "failure - SMS sending fails",
			toPhone:     "+1234567890",
			messageBody: "Test message",
			setupMock: func(m *MockNotificationService) {
				m.On("SendSMS", context.Background(), "+1234567890", "Test message").Return(errors.New("failed to send SMS"))
			},
			wantErr:     true,
			expectedErr: errors.New("failed to send SMS"),
		},
		{
			name:        "failure - invalid phone number",
			toPhone:     "invalid",
			messageBody: "Test message",
			setupMock: func(m *MockNotificationService) {
				m.On("SendSMS", context.Background(), "invalid", "Test message").Return(errors.New("invalid phone number"))
			},
			wantErr:     true,
			expectedErr: errors.New("invalid phone number"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock service
			mockService := new(MockNotificationService)

			// Setup mock expectations
			if tt.setupMock != nil {
				tt.setupMock(mockService)
			}

			// Execute test
			err := mockService.SendSMS(context.Background(), tt.toPhone, tt.messageBody)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "expected error but got none")
				assert.Equal(t, tt.expectedErr.Error(), err.Error(), "error message mismatch")
			} else {
				assert.NoError(t, err, "unexpected error")
			}

			// Verify all expectations were met
			mockService.AssertExpectations(t)
		})
	}
}

func Test_NewMockTwilioClient(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "success - creates new mock Twilio client",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute test
			client := NewMockTwilioClient()

			// Assert results
			assert.NotNil(t, client, "expected non-nil client")
			assert.IsType(t, &MockTwilioClient{}, client, "expected MockTwilioClient type")
		})
	}
}
