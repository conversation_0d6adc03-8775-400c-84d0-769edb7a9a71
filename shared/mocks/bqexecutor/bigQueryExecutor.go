package bqexecutor

import (
	"context"

	"cloud.google.com/go/bigquery"
	"github.com/stretchr/testify/mock"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// MockBQClient implements connect.BQClient using testify/mock
type MockBQClient struct {
	mock.Mock
}

func (m *MockBQClient) Query(q string) connect.BQQuery {
	args := m.Called(q)
	return args.Get(0).(connect.BQQuery)
}

func (m *MockBQClient) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockBQClient) Dataset(id string) *bigquery.Dataset {
	args := m.Called(id)
	if args.Get(0) == nil {
		return nil
	}
	return args.Get(0).(*bigquery.Dataset)
}

// MockBQQuery implements connect.BQQuery using testify/mock
type MockBQQuery struct {
	mock.Mock
}

func (m *MockBQQuery) SetParameters(params []bigquery.QueryParameter) {
	m.Called(params)
}

func (m *MockBQQuery) SetUseStandardSQL(use bool) {
	m.Called(use)
}

func (m *MockBQQuery) Read(ctx context.Context) (connect.BQRowIterator, error) {
	args := m.Called(ctx)
	return args.Get(0).(connect.BQRowIterator), args.Error(1)
}

// MockBQRowIterator implements connect.BQRowIterator using testify/mock
type MockBQRowIterator struct {
	mock.Mock
}

func (m *MockBQRowIterator) Next(dst interface{}) error {
	args := m.Called(dst)
	return args.Error(0)
}

func (m *MockBQRowIterator) Schema() bigquery.Schema {
	args := m.Called()
	return args.Get(0).(bigquery.Schema)
}

// FakeBigQueryExecutor is a mock implementation of the BigQueryExecutorInterface
type FakeBigQueryExecutor struct {
	dbexecutor.FakeDBExecutor
	mockClient *MockBQClient
	Config     connect.DatabaseConfig
	Ctx        context.Context
}

// NewFakeBigQueryExecutor creates a new fake executor that returns nil for Dataset() calls
// to signal test mode to the production code
func NewFakeBigQueryExecutor() *FakeBigQueryExecutor {
	mockClient := &MockBQClient{}

	// Set up the mock to return nil for Dataset() calls to signal test mode
	// Use .Maybe() to allow flexibility for tests that don't use all methods
	mockClient.On("Dataset", mock.Anything).Return(nil).Maybe()
	mockClient.On("Close").Return(nil).Maybe()

	// Set up basic query mocking if needed (all optional)
	mockQuery := &MockBQQuery{}
	mockQuery.On("SetParameters", mock.Anything).Return().Maybe()
	mockQuery.On("SetUseStandardSQL", mock.Anything).Return().Maybe()

	mockIter := &MockBQRowIterator{}
	mockIter.On("Schema").Return(bigquery.Schema{}).Maybe()
	mockIter.On("Next", mock.Anything).Return(nil).Maybe()

	mockQuery.On("Read", mock.Anything).Return(mockIter, nil).Maybe()
	mockClient.On("Query", mock.Anything).Return(mockQuery).Maybe()

	return &FakeBigQueryExecutor{
		mockClient: mockClient,
		Config: connect.DatabaseConfig{
			DBName:      "test_dataset",
			Environment: "test",
			Namespace:   "TEST",
		},
		Ctx: context.Background(),
	}
}

// Get the BigQuery client.
func (bq *FakeBigQueryExecutor) GetClient() connect.BQClient {
	return bq.mockClient
}

// Get the BigQuery config.
func (bq *FakeBigQueryExecutor) GetConfig() connect.DatabaseConfig {
	return bq.Config
}

// Get the context.
func (bq *FakeBigQueryExecutor) GetContext() context.Context {
	return bq.Ctx
}

// GetMockClient returns the underlying mock client for test customization
func (bq *FakeBigQueryExecutor) GetMockClient() *MockBQClient {
	return bq.mockClient
}
