package bqexecutor

import (
	"testing"

	"cloud.google.com/go/bigquery"
	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/connect"
)

// TestFakeBigQueryExecutor_Getters covers all getter methods on FakeBigQueryExecutor.
func TestFakeBigQueryExecutor_Getters(t *testing.T) {
	// Create executor using the constructor
	exec := NewFakeBigQueryExecutor()

	// Verify each getter returns valid values
	assert.NotNil(t, exec.GetClient(), "GetClient should return a valid client")
	assert.Equal(t, "test_dataset", exec.GetConfig().DBName, "GetConfig should return the test config")
	assert.Equal(t, "test", exec.GetConfig().Environment, "GetConfig should return test environment")
	assert.Equal(t, "TEST", exec.GetConfig().Namespace, "GetConfig should return test namespace")
	assert.NotNil(t, exec.GetContext(), "GetContext should return a valid context")
}

// TestFakeBigQueryExecutor_MockCustomization tests that we can customize the mock behavior
func TestFakeBigQueryExecutor_MockCustomization(t *testing.T) {
	exec := NewFakeBigQueryExecutor()
	mockClient := exec.GetMockClient()

	// Test default behavior - Dataset returns nil (test mode)
	client := exec.GetClient()
	dataset := client.Dataset("test")
	assert.Nil(t, dataset, "Dataset should return nil in test mode by default")

	// Test that we can verify mock calls
	mockClient.AssertCalled(t, "Dataset", "test")
}

// TestFakeBigQueryExecutor_MockInterfaces tests that the mocks implement the correct interfaces
func TestFakeBigQueryExecutor_MockInterfaces(t *testing.T) {
	exec := NewFakeBigQueryExecutor()

	// Test that the types implement the expected interfaces
	mockClient := exec.GetMockClient()
	assert.Implements(t, (*connect.BQClient)(nil), mockClient, "MockBQClient should implement BQClient interface")

	// Test creating other mock types
	mockQuery := &MockBQQuery{}
	mockIter := &MockBQRowIterator{}

	assert.Implements(t, (*connect.BQQuery)(nil), mockQuery, "MockBQQuery should implement BQQuery interface")
	assert.Implements(t, (*connect.BQRowIterator)(nil), mockIter, "MockBQRowIterator should implement BQRowIterator interface")
}

// TestFakeBigQueryExecutor_CustomMockSetup demonstrates how to customize mock behavior
func TestFakeBigQueryExecutor_CustomMockSetup(t *testing.T) {
	exec := NewFakeBigQueryExecutor()
	mockClient := exec.GetMockClient()

	// Reset the mock to customize behavior
	mockClient.ExpectedCalls = nil
	mockClient.Calls = nil

	// Set up custom mock behavior for specific test scenarios
	mockClient.On("Dataset", "custom_dataset").Return(&bigquery.Dataset{})
	mockClient.On("Close").Return(nil)

	// Test the custom behavior
	client := exec.GetClient()
	dataset := client.Dataset("custom_dataset")
	assert.NotNil(t, dataset, "Dataset should return a dataset for custom_dataset")

	err := client.Close()
	assert.NoError(t, err, "Close should succeed")

	// Verify all expectations were met
	mockClient.AssertExpectations(t)
}
