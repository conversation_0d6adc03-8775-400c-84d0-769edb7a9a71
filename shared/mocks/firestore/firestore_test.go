package firestore

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/api/iterator"
)

func TestSeedAndGet(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed a document
	initial := map[string]interface{}{"foo": "bar"}
	fs.SeedDoc("colA", "doc1", initial)

	// Get via DocumentRef.Get
	ref := fs.Collection("colA").Doc("doc1")
	snap, err := ref.Get(ctx)
	assert.NoError(err)
	assert.True(snap.Exists(), "seeded doc should exist")
	data := snap.Data()
	assert.Equal("bar", data["foo"], "field foo should be 'bar'")
}

func TestGetNonexistentDoc(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// No seeding
	ref := fs.Collection("colB").Doc("missing")
	snap, err := ref.Get(ctx)
	assert.NoError(err)
	assert.False(snap.Exists(), "nonexistent doc should not exist")
	assert.Nil(snap.Data(), "data of nonexistent doc should be nil")
}

func TestSetAndOverwrite(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	ref := fs.Collection("colC").Doc("docX")

	// First Set
	_, err := ref.Set(ctx, map[string]interface{}{"n": 1})
	assert.NoError(err)
	snap, _ := ref.Get(ctx)
	assert.True(snap.Exists())
	assert.Equal(1, snap.Data()["n"])

	// Overwrite with new data
	_, err = ref.Set(ctx, map[string]interface{}{"n": 2, "extra": "yes"})
	assert.NoError(err)
	snap, _ = ref.Get(ctx)
	assert.True(snap.Exists())
	assert.Equal(2, snap.Data()["n"])
	assert.Equal("yes", snap.Data()["extra"])
}

func TestSet_WrongType(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed an initial document so we can verify it isn't overwritten
	initial := map[string]interface{}{"keep": "value"}
	fs.SeedDoc("colZ", "docZ", initial)

	// Attempt to Set with the wrong type (not a map[string]interface{})
	wr, err := fs.Collection("colZ").Doc("docZ").Set(ctx, "not-a-map")
	assert.NoError(err, "should not return an error on wrong type")
	assert.Nil(wr, "write result should be nil when data is wrong type")

	// Verify the original document is still intact
	snap, err := fs.Collection("colZ").Doc("docZ").Get(ctx)
	assert.NoError(err)
	assert.True(snap.Exists(), "original doc should still exist")
	data := snap.Data()
	assert.Equal("value", data["keep"], "original field must be unchanged")
}

func TestBulkWriterDeleteFlush(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed two docs
	fs.SeedDoc("colD", "a", map[string]interface{}{"v": "1"})
	fs.SeedDoc("colD", "b", map[string]interface{}{"v": "2"})

	// Queue delete of "a"
	bw := fs.BulkWriter(ctx)
	err := bw.Delete(fs.Collection("colD").Doc("a"))
	assert.NoError(err)

	// Before Flush, both should still exist
	aSnap, _ := fs.Collection("colD").Doc("a").Get(ctx)
	bSnap, _ := fs.Collection("colD").Doc("b").Get(ctx)
	assert.True(aSnap.Exists(), "a should exist before flush")
	assert.True(bSnap.Exists(), "b should exist before flush")

	// Flush and then "a" is gone, "b" remains
	err = bw.Flush()
	assert.NoError(err)
	aSnap, _ = fs.Collection("colD").Doc("a").Get(ctx)
	bSnap, _ = fs.Collection("colD").Doc("b").Get(ctx)
	assert.False(aSnap.Exists(), "a should be deleted after flush")
	assert.True(bSnap.Exists(), "b should remain")
}

func TestGetAllDocsShallowCopy(t *testing.T) {
	t.Parallel()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	fs.SeedDoc("colE", "x", map[string]interface{}{"k": "v"})
	fs.SeedDoc("colE", "y", map[string]interface{}{"k": "w"})

	docs := fs.GetAllDocs("colE")
	// Original store has two docs
	assert.Len(docs, 2)
	assert.Equal("v", docs["x"]["k"])
	assert.Equal("w", docs["y"]["k"])

	// Modify returned map and ensure original not affected
	delete(docs, "x")
	assert.Len(docs, 1)
	orig := fs.GetAllDocs("colE")
	assert.Len(orig, 2, "original store must remain unchanged")
}

func TestCollectionDocCreatesCollection(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Collection not pre-existing -> calling Doc should create it
	ref := fs.Collection("newCol").Doc("d1")
	snap, err := ref.Get(ctx)
	assert.NoError(err)
	assert.False(snap.Exists(), "new doc in new collection should not exist")

	// Now set and get
	_, err = ref.Set(ctx, map[string]interface{}{"a": 1})
	assert.NoError(err)
	snap, err = ref.Get(ctx)
	assert.NoError(err)
	assert.True(snap.Exists())
	assert.Equal(1, snap.Data()["a"])
}

func TestBulkWriterFlushWithoutOps(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()

	bw := fs.BulkWriter(ctx)
	err := bw.Flush()
	assert.NoError(t, err, "Flush with no ops should not error")
}

func TestCloseNoOp(t *testing.T) {
	t.Parallel()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Close should always return nil
	err := fs.Close()
	assert.NoError(err)
}

func TestEncryptedCreds(t *testing.T) {
	// Can't use t.Parallel() due to env variables
	const expected = "mock-encrypted-creds"
	t.Setenv("FIRESTORE_AUTH_ENCRYPTED", expected)

	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	assert.Equal(expected, fs.EncryptedCreds(), "EncryptedCreds should match the env value")
}

func TestIteratorOverSeededDocs(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	fs.SeedDoc("colF", "one", map[string]any{"v": 1})
	fs.SeedDoc("colF", "two", map[string]any{"v": 2})

	iter := fs.Collection("colF").Documents(ctx)

	var seenIDs []string
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		assert.NoError(err, "Next() should not return error before done")
		assert.True(doc.Exists())
		assert.NotEmpty(doc.ID(), "Doc ID should not be empty")
		assert.Contains([]string{"one", "two"}, doc.ID())
		seenIDs = append(seenIDs, doc.ID())

		data := doc.Data()
		assert.Contains(data, "v")
	}
	assert.Len(seenIDs, 2, "should iterate over both docs")

	// Call Stop (no-op) and ensure it doesn't panic
	iter.Stop()
	iter.Stop() // Should also be safe
}

func TestIteratorOnEmptyCollection(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	iter := fs.Collection("colEmpty").Documents(ctx)

	doc, err := iter.Next()
	assert.Nil(doc)
	assert.ErrorIs(err, iterator.Done, "Next on empty collection must return Done")

	// Ensure Stop doesn't panic
	iter.Stop()
}

func TestGetWithErrorSimulation(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Set up error simulation
	expectedErr := fmt.Errorf("simulated get error")
	fs.GetError = expectedErr

	ref := fs.Collection("colG").Doc("docG")
	snap, err := ref.Get(ctx)
	assert.ErrorIs(err, expectedErr)
	assert.Nil(snap)

	// Clear error and test normal operation
	fs.GetError = nil
	snap, err = ref.Get(ctx)
	assert.NoError(err)
	assert.False(snap.Exists())
}

func TestSetWithErrorSimulation(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Set up error simulation
	expectedErr := fmt.Errorf("simulated set error")
	fs.SetError = expectedErr

	ref := fs.Collection("colH").Doc("docH")
	wr, err := ref.Set(ctx, map[string]interface{}{"test": "data"})
	assert.ErrorIs(err, expectedErr)
	assert.Nil(wr)

	// Clear error and test normal operation
	fs.SetError = nil
	wr, err = ref.Set(ctx, map[string]interface{}{"test": "data"})
	assert.NoError(err)
	assert.NotNil(wr)
}

func TestNextWithErrorSimulation(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed a document
	fs.SeedDoc("colI", "docI", map[string]interface{}{"v": 1})

	// Set up error simulation
	expectedErr := fmt.Errorf("simulated next error")
	fs.NextError = expectedErr

	iter := fs.Collection("colI").Documents(ctx)
	doc, err := iter.Next()
	assert.ErrorIs(err, expectedErr)
	assert.Nil(doc)

	// Clear error and test normal operation
	fs.NextError = nil
	iter = fs.Collection("colI").Documents(ctx)
	doc, err = iter.Next()
	assert.NoError(err)
	assert.NotNil(doc)
	assert.True(doc.Exists())
}

func TestStopMethod(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()

	// Test Stop on empty collection iterator
	iter := fs.Collection("colJ").Documents(ctx)
	iter.Stop()
	// Should not panic

	// Test Stop on populated collection iterator
	fs.SeedDoc("colJ", "docJ", map[string]interface{}{"v": 1})
	iter = fs.Collection("colJ").Documents(ctx)
	iter.Stop()
	// Should not panic

	// Test multiple Stop calls
	iter.Stop()
	iter.Stop()
	// Should not panic
}

func TestDataToMethod(t *testing.T) {
	t.Parallel()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Test DataTo on existing document
	testData := map[string]interface{}{
		"string": "value",
		"int":    42,
		"float":  3.14,
		"bool":   true,
		"array":  []interface{}{1, 2, 3},
		"nested": map[string]interface{}{"key": "value"},
	}
	fs.SeedDoc("colK", "docK", testData)

	ref := fs.Collection("colK").Doc("docK")
	snap, err := ref.Get(context.Background())
	assert.NoError(err)
	assert.True(snap.Exists())

	// Test successful DataTo
	var result map[string]interface{}
	err = snap.DataTo(&result)
	assert.NoError(err)

	// Compare values in a type-agnostic way
	assert.Equal(result["string"], testData["string"])
	assert.Equal(result["bool"], testData["bool"])
	assert.InDelta(result["float"].(float64), testData["float"].(float64), 0.0001)
	assert.InDelta(result["int"].(float64), float64(testData["int"].(int)), 0.0001)
	// Compare array values
	arr := result["array"].([]interface{})
	for i, v := range arr {
		assert.InDelta(v.(float64), float64(testData["array"].([]interface{})[i].(int)), 0.0001)
	}
	// Compare nested map
	nested := result["nested"].(map[string]interface{})
	assert.Equal(nested["key"], testData["nested"].(map[string]interface{})["key"])

	// Test DataTo on non-existent document
	ref = fs.Collection("colK").Doc("nonexistent")
	snap, err = ref.Get(context.Background())
	assert.NoError(err)
	assert.False(snap.Exists())

	err = snap.DataTo(&result)
	assert.Error(err)
	assert.Contains(err.Error(), "document does not exist")
}

func TestDeleteWithErrorSimulation(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed a document
	fs.SeedDoc("colL", "docL", map[string]interface{}{"v": 1})

	// Set up error simulation
	expectedErr := fmt.Errorf("simulated delete error")
	fs.DeleteError = expectedErr

	bw := fs.BulkWriter(ctx)
	err := bw.Delete(fs.Collection("colL").Doc("docL"))
	assert.ErrorIs(err, expectedErr)

	// Clear error and test normal operation
	fs.DeleteError = nil
	bw = fs.BulkWriter(ctx)
	err = bw.Delete(fs.Collection("colL").Doc("docL"))
	assert.NoError(err)

	// Flush to actually delete
	err = bw.Flush()
	assert.NoError(err)

	// Verify document is deleted
	snap, err := fs.Collection("colL").Doc("docL").Get(ctx)
	assert.NoError(err)
	assert.False(snap.Exists())
}

func TestFlushWithErrorSimulation(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed documents
	fs.SeedDoc("colM", "docM1", map[string]interface{}{"v": 1})
	fs.SeedDoc("colM", "docM2", map[string]interface{}{"v": 2})

	// Test FlushError
	expectedErr := fmt.Errorf("simulated flush error")
	fs.FlushError = expectedErr

	bw := fs.BulkWriter(ctx)
	bw.Delete(fs.Collection("colM").Doc("docM1"))
	err := bw.Flush()
	assert.ErrorIs(err, expectedErr)

	// Clear FlushError and test FinalFlushError
	fs.FlushError = nil
	finalFlushErr := fmt.Errorf("simulated final flush error")
	fs.FinalFlushError = finalFlushErr

	bw = fs.BulkWriter(ctx)
	bw.Delete(fs.Collection("colM").Doc("docM2"))
	err = bw.Flush()
	assert.ErrorIs(err, finalFlushErr)

	// Test that FinalFlushError is cleared after first use
	err = bw.Flush()
	assert.NoError(err, "FinalFlushError should be cleared after first use")
}

func TestFlushWithNoOperations(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Test Flush with no operations and FinalFlushError set
	fs.FinalFlushError = fmt.Errorf("should not trigger on empty ops")

	bw := fs.BulkWriter(ctx)
	err := bw.Flush()
	assert.NoError(err, "Flush with no ops should not trigger FinalFlushError")
}

func TestBulkWriterMultipleOperations(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed multiple documents
	fs.SeedDoc("colN", "doc1", map[string]interface{}{"v": 1})
	fs.SeedDoc("colN", "doc2", map[string]interface{}{"v": 2})
	fs.SeedDoc("colN", "doc3", map[string]interface{}{"v": 3})

	// Queue multiple deletes
	bw := fs.BulkWriter(ctx)
	bw.Delete(fs.Collection("colN").Doc("doc1"))
	bw.Delete(fs.Collection("colN").Doc("doc2"))

	// Verify documents still exist before flush
	snap1, _ := fs.Collection("colN").Doc("doc1").Get(ctx)
	snap2, _ := fs.Collection("colN").Doc("doc2").Get(ctx)
	snap3, _ := fs.Collection("colN").Doc("doc3").Get(ctx)
	assert.True(snap1.Exists())
	assert.True(snap2.Exists())
	assert.True(snap3.Exists())

	// Flush and verify only queued documents are deleted
	err := bw.Flush()
	assert.NoError(err)

	snap1, _ = fs.Collection("colN").Doc("doc1").Get(ctx)
	snap2, _ = fs.Collection("colN").Doc("doc2").Get(ctx)
	snap3, _ = fs.Collection("colN").Doc("doc3").Get(ctx)
	assert.False(snap1.Exists())
	assert.False(snap2.Exists())
	assert.True(snap3.Exists(), "doc3 should remain untouched")
}

func TestDataToWithJSONError(t *testing.T) {
	t.Parallel()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Create a document with data that can't be marshaled to JSON
	// This is tricky to do with standard types, but we can test the error path
	// by creating a circular reference or using a channel
	fs.SeedDoc("colO", "docO", map[string]interface{}{"normal": "data"})

	ref := fs.Collection("colO").Doc("docO")
	snap, err := ref.Get(context.Background())
	assert.NoError(err)
	assert.True(snap.Exists())

	// Test DataTo with invalid target type (should still work with valid data)
	var result map[string]interface{}
	err = snap.DataTo(&result)
	assert.NoError(err)
	assert.Equal("data", result["normal"])
}

func TestIteratorWithMultipleDocuments(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed multiple documents
	fs.SeedDoc("colP", "doc1", map[string]interface{}{"id": 1})
	fs.SeedDoc("colP", "doc2", map[string]interface{}{"id": 2})
	fs.SeedDoc("colP", "doc3", map[string]interface{}{"id": 3})

	iter := fs.Collection("colP").Documents(ctx)

	var seenIDs []int
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		assert.NoError(err)
		assert.True(doc.Exists())
		data := doc.Data()
		// Handle both int and float64 types that JSON unmarshaling might produce
		switch v := data["id"].(type) {
		case int:
			seenIDs = append(seenIDs, v)
		case float64:
			seenIDs = append(seenIDs, int(v))
		default:
			t.Errorf("unexpected type for id: %T", v)
		}
	}

	assert.Len(seenIDs, 3)
	assert.Contains(seenIDs, 1)
	assert.Contains(seenIDs, 2)
	assert.Contains(seenIDs, 3)

	// Test Stop after iteration
	iter.Stop()
}

func TestDataToWithMarshalError(t *testing.T) {
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Create a document with a value that cannot be marshaled to JSON
	badData := map[string]interface{}{"bad": make(chan int)}
	fs.SeedDoc("colQ", "docQ", badData)

	ref := fs.Collection("colQ").Doc("docQ")
	snap, err := ref.Get(context.Background())
	assert.NoError(err)
	assert.True(snap.Exists())

	var result map[string]interface{}
	err = snap.DataTo(&result)
	assert.Error(err)
	assert.Contains(err.Error(), "json: unsupported type")
}
