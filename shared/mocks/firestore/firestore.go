// Package mocks provides a FakeFirestoreClient implementation of
// connect.FirestoreClientInterface for unit tests.
// It simulates Firestore in-memory, allowing you to seed initial data,
// perform Get/Set on documents, and batch deletes via a BulkWriter.
//
// Features:
//   - SeedDoc    – pre-populate a collection/document before calling the code
//   - GetAllDocs – inspect full collection state after tests run
//   - Firestore operations: Collection, Doc, Get, Set
//   - BulkWriter: Delete and Flush operations
//
// Usage example:
//
//	import (
//	    "context"
//	    "testing"
//	    "synapse-its.com/shared/connect"
//	    "synapse-its.com/shared/mocks"
//	)
//
//	func TestSyncFirebaseDocuments(t *testing.T) {
//	    ctx := context.Background()
//
//	    // 1) Create fake client & seed initial _state document
//	    fakeFS := mocks.NewFakeFirestoreClient()
//	    fakeFS.SeedDoc("orgA", "_state", map[string]any{
//	        "dev1": map[string]any{"status": "ok"},
//	        "dev2": map[string]any{"status": "error"},
//	    })
//
//	    // 2) Build connect.Connections with fake Firestore
//	    conns := &connect.Connections{
//	        Firestore: fakeFS,
//	        // other dependencies as needed...
//	    }
//
//	    // 3) Invoke code under test
//	    if err := SyncFirebaseDocuments(conns, ctx); err != nil {
//	        t.Fatalf("unexpected error: %v", err)
//	    }
//
//	    // 4) Inspect updated state
//	    docs := fakeFS.GetAllDocs("orgA")
//	    state := docs["_state"].(map[string]any)
//	    if state["dev1"].(map[string]any)["status"] != "ok" {
//	        t.Error("expected dev1 to remain ok")
//	    }
//	}
//
// FakeFirestoreClient is safe for concurrent use in tests.
// Each call to NewFakeFirestoreClient gives a fresh, isolated store.
package firestore

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"

	"google.golang.org/api/iterator"
	"synapse-its.com/shared/connect"
)

// FakeFirestoreClient is an in-memory implementation of
// connect.FirestoreClientInterface. It maps collection -> document ID -> data.
type FakeFirestoreClient struct {
	mu             sync.RWMutex
	store          map[string]map[string]map[string]any
	encryptedCreds string
	// Global error to simulate an error during iteration
	NextError       error // simulate error on Next
	GetError        error // simulate error on Get
	SetError        error // simulate error on Set
	DeleteError     error // simulate error on Delete
	FlushError      error // simulate error on Flush
	FinalFlushError error // simulate error on final Flush
}

// NewFakeFirestoreClient returns a fresh FakeFirestoreClient.
// Use SeedDoc to pre-populate collections before your test.
func NewFakeFirestoreClient() *FakeFirestoreClient {
	return &FakeFirestoreClient{
		store:          make(map[string]map[string]map[string]any),
		encryptedCreds: os.Getenv("FIRESTORE_AUTH_ENCRYPTED"),
	}
}

// SeedDoc pre-populates a single document in a collection.
// After seeding, Get or BulkWriter operations will see this document.
func (f *FakeFirestoreClient) SeedDoc(collection, docID string, data map[string]any) {
	f.mu.Lock()
	defer f.mu.Unlock()
	if f.store[collection] == nil {
		f.store[collection] = make(map[string]map[string]any)
	}
	f.store[collection][docID] = data
}

// GetAllDocs returns a snapshot of all documents in a collection.
// The returned map is a shallow copy of the internal store at call time.
func (f *FakeFirestoreClient) GetAllDocs(collection string) map[string]map[string]any {
	f.mu.RLock()
	defer f.mu.RUnlock()

	result := make(map[string]map[string]any, len(f.store[collection]))
	for id, data := range f.store[collection] {
		result[id] = data
	}
	return result
}

// Collection returns a fake CollectionRefInterface for the named collection.
func (f *FakeFirestoreClient) Collection(name string) connect.CollectionRefInterface {
	f.mu.Lock()
	defer f.mu.Unlock()
	if f.store[name] == nil {
		f.store[name] = make(map[string]map[string]any)
	}
	return &FakeCollectionRef{client: f, col: name}
}

// BulkWriter returns a fake BulkWriterInterface for batch deletes.
func (f *FakeFirestoreClient) BulkWriter(ctx context.Context) connect.BulkWriterInterface {
	return &FakeBulkWriter{client: f, ops: make([]func(), 0)}
}

// Close is a no-op for the fake client.
func (f *FakeFirestoreClient) Close() error {
	return nil
}

// EncryptedCreds returns the firestore environmental config.
func (f *FakeFirestoreClient) EncryptedCreds() string {
	return f.encryptedCreds
}

// FakeCollectionRef implements connect.CollectionRefInterface.
// Use Doc(id) to obtain a FakeDocumentRef.
type FakeCollectionRef struct {
	client *FakeFirestoreClient
	col    string
}

func (c *FakeCollectionRef) Doc(id string) connect.DocumentRefInterface {
	return &FakeDocumentRef{client: c.client, col: c.col, id: id}
}

// Documents returns a connect.DocumentIteratorInterface that iterates
// over all documents in the collection.
// If the collection is empty, Next() will immediately return iterator.Done.
func (c *FakeCollectionRef) Documents(ctx context.Context) connect.DocumentIteratorInterface {
	c.client.mu.RLock()
	defer c.client.mu.RUnlock()

	docs := make([]connect.DocumentSnapshotInterface, 0, len(c.client.store[c.col]))
	for id, data := range c.client.store[c.col] {
		docs = append(docs, &FakeDocumentSnapshot{
			data:   data,
			exists: true,
			id:     id,
		})
	}

	return &FakeDocumentIterator{
		client: c.client,
		docs:   docs,
		idx:    0,
	}
}

// FakeDocumentRef implements connect.DocumentRefInterface.
type FakeDocumentRef struct {
	client *FakeFirestoreClient
	col    string
	id     string
}

// Get returns a FakeDocumentSnapshotInterface. If the doc does not exist,
// snapshot.Exists() will be false.
func (d *FakeDocumentRef) Get(ctx context.Context) (connect.DocumentSnapshotInterface, error) {
	if d.client.GetError != nil {
		return nil, d.client.GetError
	}
	d.client.mu.RLock()
	defer d.client.mu.RUnlock()

	data, ok := d.client.store[d.col][d.id]
	if !ok {
		return &FakeDocumentSnapshot{exists: false}, nil
	}
	return &FakeDocumentSnapshot{data: data, exists: true}, nil
}

// Set writes data to the in-memory store, replacing any existing doc.
func (d *FakeDocumentRef) Set(ctx context.Context, data any) (*connect.WriteResult, error) {
	if d.client.SetError != nil {
		return nil, d.client.SetError
	}
	m, ok := data.(map[string]any)
	if !ok {
		return nil, nil // tests should pass correct type
	}
	d.client.mu.Lock()
	defer d.client.mu.Unlock()
	d.client.store[d.col][d.id] = m
	return &connect.WriteResult{}, nil
}

// FakeDocumentIterator implements connect.DocumentIteratorInterface.
// When iteration completes, Next returns iterator.Done.
type FakeDocumentIterator struct {
	client *FakeFirestoreClient
	docs   []connect.DocumentSnapshotInterface
	idx    int
}

// Next returns the next document in the iteration. If all documents have
// been returned, it returns (nil, iterator.Done). No other errors are returned.
func (it *FakeDocumentIterator) Next() (connect.DocumentSnapshotInterface, error) {
	if it.client.NextError != nil {
		return nil, it.client.NextError
	}
	if it.idx >= len(it.docs) {
		return nil, iterator.Done
	}
	doc := it.docs[it.idx]
	it.idx++
	return doc, nil
}

// No-op just to match signature
func (f *FakeDocumentIterator) Stop() {}

// FakeDocumentSnapshot implements connect.DocumentSnapshotInterface.
type FakeDocumentSnapshot struct {
	data   map[string]any
	exists bool
	id     string
}

func (s *FakeDocumentSnapshot) Data() map[string]any {
	if !s.exists {
		return nil
	}
	return s.data
}
func (s *FakeDocumentSnapshot) Exists() bool { return s.exists }
func (s *FakeDocumentSnapshot) ID() string   { return s.id }

// Add DataTo method to satisfy connect.DocumentSnapshotInterface
func (s *FakeDocumentSnapshot) DataTo(v any) error {
	if !s.exists {
		return fmt.Errorf("document does not exist")
	}
	b, err := json.Marshal(s.data)
	if err != nil {
		return err
	}
	return json.Unmarshal(b, v)
}

// FakeBulkWriter implements connect.BulkWriterInterface.
// Use Delete() to queue removals, then Flush() to apply.
type FakeBulkWriter struct {
	client *FakeFirestoreClient
	ops    []func()
}

// Delete queues a document delete operation. It does not modify state until Flush().
func (w *FakeBulkWriter) Delete(ref connect.DocumentRefInterface) error {
	if w.client.DeleteError != nil {
		return w.client.DeleteError
	}
	dr := ref.(*FakeDocumentRef)
	w.ops = append(w.ops, func() {
		w.client.mu.Lock()
		defer w.client.mu.Unlock()
		delete(w.client.store[dr.col], dr.id)
	})
	return nil
}

// Flush executes all queued delete operations.
func (w *FakeBulkWriter) Flush() error {
	if w.client.FlushError != nil {
		return w.client.FlushError
	}
	if w.client.FinalFlushError != nil && len(w.ops) > 0 {
		err := w.client.FinalFlushError
		w.client.FinalFlushError = nil // Only error once
		return err
	}
	for _, op := range w.ops {
		op()
	}
	w.ops = w.ops[:0]
	return nil
}
