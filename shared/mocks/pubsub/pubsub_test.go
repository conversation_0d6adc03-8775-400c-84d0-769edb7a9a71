package pubsub

import (
	"context"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/connect"
)

func TestFakePubsubClient_Topic_CreateTopic_GetMessages(t *testing.T) {
	ctx := context.Background()
	client := NewFakePubsubClient()

	// Initially, no topics exist.
	topic1 := client.Topic("foo")
	assert.NotNil(t, topic1, "Topic should not be nil")
	ft := topic1.(*FakePubsubTopic)
	assert.Empty(t, ft.GetMessages(), "GetMessages on new topic should be empty")

	// Publish a message and verify it's stored.
	msg := &pubsub.Message{ID: "msg1", Data: []byte("hello")}
	_, _ = topic1.Publish(ctx, msg).Get(ctx)
	all := ft.GetMessages()
	assert.Len(t, all, 1, "One message should be stored")
	assert.Equal(t, "msg1", all[0].ID)
	assert.Equal(t, []byte("hello"), all[0].Data)

	// Calling Topic with the same name should return the same instance.
	topic1b := client.Topic("foo")
	ftb := topic1b.(*FakePubsubTopic)
	assert.Equal(t, ft, ftb, "Topic(\"foo\") should return the same FakePubsubTopic")

	// CreateTopic should also return the same underlying topic.
	topic1c, err := client.CreateTopic(ctx, "foo")
	assert.NoError(t, err)
	assert.Equal(t, ft, topic1c.(*FakePubsubTopic), "CreateTopic should return existing topic")

	// Create a second topic and ensure independence.
	topic2 := client.Topic("bar")
	ft2 := topic2.(*FakePubsubTopic)
	assert.NotEqual(t, ft, ft2, "Different topic names yield different topics")
}

func TestFakePublishResult_Get(t *testing.T) {
	// Case where Err is nil: returns messageID
	r := &FakePublishResult{messageID: "fake-id", Err: nil}
	id, err := r.Get(context.Background())
	assert.NoError(t, err)
	assert.Equal(t, "fake-id", id)

	// Case where Err is non‐nil: returns the error
	expectedErr := errors.New("publish failure")
	r2 := &FakePublishResult{messageID: "fake-id", Err: expectedErr}
	id2, err2 := r2.Get(context.Background())
	assert.Empty(t, id2)
	assert.Equal(t, expectedErr, err2)
}

func TestFakePubsubSubscription_Receive_NoError(t *testing.T) {
	ctx := context.Background()
	client := NewFakePubsubClient()

	// Create topic and publish two messages.
	topic := client.Topic("topic1").(*FakePubsubTopic)
	msg1 := &pubsub.Message{ID: "m1", Data: []byte("one")}
	msg2 := &pubsub.Message{ID: "m2", Data: []byte("two")}
	_, _ = topic.Publish(ctx, msg1).Get(ctx)
	_, _ = topic.Publish(ctx, msg2).Get(ctx)

	// Create a subscription bound to that topic.
	sub, err := client.CreateSubscription(ctx, "sub1", connect.SubscriptionConfig{Topic: topic})
	assert.NoError(t, err)
	fs := sub.(*FakePubsubSubscription)

	// Collect messages delivered via Receive.
	received := make([]string, 0, 2)
	err = fs.Receive(ctx, func(_ context.Context, m *pubsub.Message) {
		received = append(received, m.ID)
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"m1", "m2"}, received)
}

func TestFakePubsubSubscription_Receive_WithReceiveError(t *testing.T) {
	ctx := context.Background()
	client := NewFakePubsubClient()
	client.ReceiveError = errors.New("receive failure")

	// Create a “bare” subscription (no topic messages needed).
	fs := &FakePubsubSubscription{client: client, name: "subX"}
	err := fs.Receive(ctx, func(_ context.Context, m *pubsub.Message) {
		t.Fatal("callback should not be called when ReceiveError is set")
	})
	assert.EqualError(t, err, "receive failure")
}

func TestFakePubsubSubscription_Exists_Close(t *testing.T) {
	fs := &FakePubsubSubscription{}
	ok, err := fs.Exists(context.Background())
	assert.True(t, ok, "Exists should return true by default")
	assert.NoError(t, err)

	assert.NoError(t, fs.Close())
}

func TestFakePubsubClient_Subscription_Defaults(t *testing.T) {
	client := NewFakePubsubClient()

	// Calling Subscription on a name that doesn't exist creates a new one.
	sub1 := client.Subscription("subs1").(*FakePubsubSubscription)
	assert.NotNil(t, sub1, "Subscription should create a new instance")

	// Subsequent calls get the same instance.
	sub1b := client.Subscription("subs1").(*FakePubsubSubscription)
	assert.Equal(t, sub1, sub1b, "Subscription with same name returns same instance")

	// The topic field is nil by default.
	assert.Nil(t, sub1.topic, "Default subscription.topic should be nil")

	// Do not call Receive here (topic is nil, and Receive would panic).
}

func TestFakePubsubClient_CreateSubscription_BindsToTopic(t *testing.T) {
	ctx := context.Background()
	client := NewFakePubsubClient()

	// Create a topic first.
	topic := client.Topic("t1").(*FakePubsubTopic)
	_, _ = topic.Publish(ctx, &pubsub.Message{ID: "id1"}).Get(ctx)

	// Now create a subscription bound to that topic.
	sub, err := client.CreateSubscription(ctx, "subs2", connect.SubscriptionConfig{Topic: topic})
	assert.NoError(t, err)
	fs := sub.(*FakePubsubSubscription)

	// Publishing a new message to the same topic should be delivered by the subscription.
	_, _ = topic.Publish(ctx, &pubsub.Message{ID: "id2"}).Get(ctx)

	collected := []string{}
	err = fs.Receive(ctx, func(_ context.Context, m *pubsub.Message) {
		collected = append(collected, m.ID)
	})
	assert.NoError(t, err)
	assert.ElementsMatch(t, []string{"id1", "id2"}, collected)
}

func TestFakePubsubTopic_Exists(t *testing.T) {
	topic := &FakePubsubTopic{name: "exists"}
	ok, err := topic.Exists(context.Background())
	assert.True(t, ok, "Exists should always return true")
	assert.NoError(t, err)
}

func TestFakePubsubClient_Close(t *testing.T) {
	client := NewFakePubsubClient()
	assert.NoError(t, client.Close(), "Close should always return nil")
}

func TestTopic_WithNilTopicsMap(t *testing.T) {
	// Create a non‐nil FakePubsubClient whose `topics` field is set to nil.
	fc := &FakePubsubClient{
		topics:        nil,
		subscriptions: make(map[string]*FakePubsubSubscription),
	}

	// Now call Topic and recover from the panic.
	defer func() {
		r := recover()
		if r == nil {
			t.Fatal("expected panic when assigning into nil map, but did not get one")
		}
		// If we get here, it means we did enter the fc.topics==nil block.
	}()

	_ = fc.Topic("someName")
}

func TestTopic_NilReceiverReturnsNil(t *testing.T) {
	var fc *FakePubsubClient = nil

	topic := fc.Topic("whatever")
	assert.Nil(t, topic, "nil client returns nil topic")
}

func TestFakePubsubSubscription_ID(t *testing.T) {
	// Test with a subscription that has a name
	sub := &FakePubsubSubscription{name: "test-subscription"}
	assert.Equal(t, "test-subscription", sub.ID(), "ID should return the subscription name")

	// Test with empty name
	subEmpty := &FakePubsubSubscription{name: ""}
	assert.Equal(t, "", subEmpty.ID(), "ID should return empty string for empty name")

	// Test with a subscription created through the client
	client := NewFakePubsubClient()
	subFromClient := client.Subscription("client-sub").(*FakePubsubSubscription)
	assert.Equal(t, "client-sub", subFromClient.ID(), "ID should return the name used to create the subscription")
}

func TestFakePubsubSubscription_SetReceiveSettings_GetReceiveSettings(t *testing.T) {
	sub := &FakePubsubSubscription{name: "test-sub"}

	// Test initial state - should have default settings
	initialSettings := sub.GetReceiveSettings()
	assert.Equal(t, pubsub.ReceiveSettings{}, initialSettings, "Initial settings should be default")

	// Test setting receive settings
	settings := &pubsub.ReceiveSettings{
		MaxExtension:           10 * time.Second,
		MaxOutstandingMessages: 100,
		MaxOutstandingBytes:    1000000,
		NumGoroutines:          5,
	}
	sub.SetReceiveSettings(settings)

	// Verify the settings were set correctly
	retrievedSettings := sub.GetReceiveSettings()
	assert.Equal(t, *settings, retrievedSettings, "Retrieved settings should match what was set")
	assert.Equal(t, 10*time.Second, retrievedSettings.MaxExtension)
	assert.Equal(t, int(100), retrievedSettings.MaxOutstandingMessages)
	assert.Equal(t, int(1000000), retrievedSettings.MaxOutstandingBytes)
	assert.Equal(t, int(5), retrievedSettings.NumGoroutines)

	// Test setting nil settings (should not change anything)
	sub.SetReceiveSettings(nil)
	settingsAfterNil := sub.GetReceiveSettings()
	assert.Equal(t, *settings, settingsAfterNil, "Settings should remain unchanged when setting nil")

	// Test updating settings
	newSettings := &pubsub.ReceiveSettings{
		MaxExtension:           20 * time.Second,
		MaxOutstandingMessages: 200,
		MaxOutstandingBytes:    2000000,
		NumGoroutines:          10,
	}
	sub.SetReceiveSettings(newSettings)
	updatedSettings := sub.GetReceiveSettings()
	assert.Equal(t, *newSettings, updatedSettings, "Settings should be updated correctly")
	assert.Equal(t, 20*time.Second, updatedSettings.MaxExtension)
	assert.Equal(t, int(200), updatedSettings.MaxOutstandingMessages)
	assert.Equal(t, int(2000000), updatedSettings.MaxOutstandingBytes)
	assert.Equal(t, int(10), updatedSettings.NumGoroutines)
}

func TestFakePubsubSubscription_ReceiveSettings_Isolation(t *testing.T) {
	// Test that different subscriptions have isolated settings
	sub1 := &FakePubsubSubscription{name: "sub1"}
	sub2 := &FakePubsubSubscription{name: "sub2"}

	settings1 := &pubsub.ReceiveSettings{MaxExtension: 10 * time.Second}
	settings2 := &pubsub.ReceiveSettings{MaxExtension: 20 * time.Second}

	sub1.SetReceiveSettings(settings1)
	sub2.SetReceiveSettings(settings2)

	assert.Equal(t, 10*time.Second, sub1.GetReceiveSettings().MaxExtension)
	assert.Equal(t, 20*time.Second, sub2.GetReceiveSettings().MaxExtension)
	assert.NotEqual(t, sub1.GetReceiveSettings(), sub2.GetReceiveSettings(), "Different subscriptions should have different settings")
}
