package bqbatcher

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
)

func TestFakeBatcherWithOptions_Defaults(t *testing.T) {
	// Build a FakeBatcher with no options
	b := FakeBatcherWithOptions()

	// By default, RegisterFn, AddFn, and ShutdownFn are non-nil,
	// but LoadBatchFn is nil. So we only test the methods whose Fn is set.

	// 1) Register should return nil (default)
	err := b.Register(struct{}{}, "table", bqbatch.QueueConfig{MaxSize: 1, FlushInterval: 0})
	assert.NoError(t, err, "expected default Register to return nil")

	// 2) Add should return nil (default)
	err = b.Add("row")
	assert.NoError(t, err, "expected default Add to return nil")

	// 3) Shutdown should return nil (default)
	err = b.Shutdown()
	assert.NoError(t, err, "expected default Shutdown to return nil")

	// 4) LoadBatch would panic if called now, because LoadBatchFn is nil.
	// We do not call it here.
}

func TestFakeBatcherWithOptions_ShutdownError(t *testing.T) {
	// Override only ShutdownFn
	sentinel := errors.New("shutdown-failed")
	b := FakeBatcherWithOptions(WithBatchShutdownError(sentinel))

	// Register/Add still use defaults
	assert.NoError(t, b.Register(struct{}{}, "table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Add(42))

	// Shutdown should now return the sentinel error
	err := b.Shutdown()
	assert.Equal(t, sentinel, err, "expected Shutdown to return sentinel error")
}

func TestFakeBatcherWithCustomOption_RegisterError(t *testing.T) {
	// Override RegisterFn
	sentinel := errors.New("register-failed")
	customOpt := func(f *FakeBatcher) {
		f.RegisterFn = func(rowExample interface{}, table string, cfg bqbatch.QueueConfig) error {
			return sentinel
		}
	}
	b := FakeBatcherWithOptions(customOpt)

	// Register returns sentinel
	err := b.Register("row", "table", bqbatch.QueueConfig{})
	assert.Equal(t, sentinel, err, "expected custom RegisterFn to return sentinel")

	// Other methods still use default (nil)
	assert.NoError(t, b.Add("row"))
	assert.NoError(t, b.Shutdown())
}

func TestFakeBatcherWithCustomOption_AddError(t *testing.T) {
	// Override AddFn
	sentinel := errors.New("add-failed")
	customOpt := func(f *FakeBatcher) {
		f.AddFn = func(row interface{}) error {
			return sentinel
		}
	}
	b := FakeBatcherWithOptions(customOpt)

	// Add returns sentinel
	err := b.Add("row")
	assert.Equal(t, sentinel, err, "expected custom AddFn to return sentinel")

	// Other methods still use default (nil)
	assert.NoError(t, b.Register(struct{}{}, "table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Shutdown())
}

func TestFakeBatcherWithCustomOption_LoadBatchError(t *testing.T) {
	// Override LoadBatchFn
	sentinel := errors.New("loadbatch-failed")
	customOpt := func(f *FakeBatcher) {
		f.LoadBatchFn = func(table string, rawData []byte) error {
			return sentinel
		}
	}
	b := FakeBatcherWithOptions(customOpt)

	// LoadBatch returns sentinel
	err := b.LoadBatch("table", []byte("raw data"))
	assert.Equal(t, sentinel, err, "expected custom LoadBatchFn to return sentinel")

	// Other methods still use default (nil)
	assert.NoError(t, b.Register(struct{}{}, "table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Add("row"))
	assert.NoError(t, b.Shutdown())
}

func TestFakeBatch_Defaults(t *testing.T) {
	ctx := context.Background()
	batcherIface, err := FakeBatch(ctx)
	assert.NoError(t, err, "FakeBatch should not return an error")

	// Underlying type should be *FakeBatcher
	fb, ok := batcherIface.(*FakeBatcher)
	assert.True(t, ok, "FakeBatch should return *FakeBatcher")

	// All four Fn fields (RegisterFn, AddFn, LoadBatchFn, ShutdownFn) are non-nil
	assert.NotNil(t, fb.RegisterFn, "RegisterFn must be non-nil")
	assert.NotNil(t, fb.AddFn, "AddFn must be non-nil")
	assert.NotNil(t, fb.LoadBatchFn, "LoadBatchFn must be non-nil")
	assert.NotNil(t, fb.ShutdownFn, "ShutdownFn must be non-nil")

	// Verify Register returns nil
	assert.NoError(t, batcherIface.Register(struct{}{}, "any", bqbatch.QueueConfig{}))

	// Verify Add returns nil
	assert.NoError(t, batcherIface.Add("anything"))

	// Verify LoadBatch returns nil
	assert.NoError(t, batcherIface.LoadBatch("any", []byte("test data")))

	// Verify Shutdown returns nil
	assert.NoError(t, batcherIface.Shutdown())
}

// Test multiple options applied together
func TestFakeBatcherWithOptions_MultipleOptions(t *testing.T) {
	registerErr := errors.New("register-error")
	addErr := errors.New("add-error")
	shutdownErr := errors.New("shutdown-error")

	b := FakeBatcherWithOptions(
		func(f *FakeBatcher) {
			f.RegisterFn = func(rowExample interface{}, table string, cfg bqbatch.QueueConfig) error {
				return registerErr
			}
		},
		func(f *FakeBatcher) {
			f.AddFn = func(row interface{}) error {
				return addErr
			}
		},
		WithBatchShutdownError(shutdownErr),
	)

	// Each method should return its respective error
	err := b.Register(struct{}{}, "table", bqbatch.QueueConfig{})
	assert.Equal(t, registerErr, err)

	err = b.Add("row")
	assert.Equal(t, addErr, err)

	err = b.Shutdown()
	assert.Equal(t, shutdownErr, err)
}

// Test FakeBatcher with different data types
func TestFakeBatcherWithOptions_DifferentDataTypes(t *testing.T) {
	var capturedRows []interface{}
	var capturedTables []string

	b := FakeBatcherWithOptions(
		func(f *FakeBatcher) {
			f.RegisterFn = func(rowExample interface{}, table string, cfg bqbatch.QueueConfig) error {
				capturedTables = append(capturedTables, table)
				return nil
			}
		},
		func(f *FakeBatcher) {
			f.AddFn = func(row interface{}) error {
				capturedRows = append(capturedRows, row)
				return nil
			}
		},
	)

	// Test with different data types
	testStruct := struct{ Name string }{Name: "test"}
	testMap := map[string]int{"count": 42}
	testSlice := []string{"a", "b", "c"}

	// Register different types
	assert.NoError(t, b.Register(testStruct, "struct_table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Register(testMap, "map_table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Register(testSlice, "slice_table", bqbatch.QueueConfig{}))

	// Add different types
	assert.NoError(t, b.Add(testStruct))
	assert.NoError(t, b.Add(testMap))
	assert.NoError(t, b.Add(testSlice))
	assert.NoError(t, b.Add("string"))
	assert.NoError(t, b.Add(123))

	// Verify captured data
	assert.Equal(t, []string{"struct_table", "map_table", "slice_table"}, capturedTables)
	assert.Len(t, capturedRows, 5)
	assert.Equal(t, testStruct, capturedRows[0])
	assert.Equal(t, testMap, capturedRows[1])
	assert.Equal(t, testSlice, capturedRows[2])
	assert.Equal(t, "string", capturedRows[3])
	assert.Equal(t, 123, capturedRows[4])
}

// Test FakeBatcher LoadBatch with different FailedBatch configurations
func TestFakeBatcherWithOptions_LoadBatchVariations(t *testing.T) {
	type capturedBatch struct {
		table   string
		rawData []byte
	}
	var capturedBatches []capturedBatch

	b := FakeBatcherWithOptions(
		func(f *FakeBatcher) {
			f.LoadBatchFn = func(table string, rawData []byte) error {
				capturedBatches = append(capturedBatches, capturedBatch{table: table, rawData: rawData})
				return nil
			}
		},
	)

	// Test with different table and data configurations
	table1 := "table1"
	data1 := []byte(`{"id": "test1", "value": 100}`)
	table2 := "table2"
	data2 := []byte(`{"id": "test2", "value": 200}`)

	assert.NoError(t, b.LoadBatch(table1, data1))
	assert.NoError(t, b.LoadBatch(table2, data2))

	// Verify captured batches
	assert.Len(t, capturedBatches, 2)
	assert.Equal(t, "table1", capturedBatches[0].table)
	assert.Equal(t, data1, capturedBatches[0].rawData)
	assert.Equal(t, "table2", capturedBatches[1].table)
	assert.Equal(t, data2, capturedBatches[1].rawData)
}

// Test nil context handling in FakeBatch
func TestFakeBatch_NilContext(t *testing.T) {
	// FakeBatch should work even with nil context since it doesn't use it
	batcherIface, err := FakeBatch(nil)
	assert.NoError(t, err, "FakeBatch should work with nil context")
	assert.NotNil(t, batcherIface, "should return valid batcher")

	// Should still work normally
	assert.NoError(t, batcherIface.Register(struct{}{}, "table", bqbatch.QueueConfig{}))
	assert.NoError(t, batcherIface.Add("data"))
	assert.NoError(t, batcherIface.Shutdown())
}
