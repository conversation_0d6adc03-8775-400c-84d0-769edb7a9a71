package bqbatcher

import (
	"context"

	"synapse-its.com/shared/bqbatch"
)

// WithBatchShutdownError makes Shutdown() return the given error.
func WithBatchShutdownError(err error) FakeBatchOption {
	return func(f *FakeBatcher) {
		f.ShutdownFn = func() error { return err }
	}
}

// FakeBatcherWithOptions builds a *FakeBatcher with sane defaults, applies any opts,
// and then returns it as a bqbatch.Batcher.
func FakeBatcherWithOptions(opts ...FakeBatchOption) bqbatch.Batcher {
	f := &FakeBatcher{
		RegisterFn: func(_ interface{}, _ string, _ bqbatch.QueueConfig) error { return nil },
		AddFn:      func(_ interface{}) error { return nil },
		ShutdownFn: func() error { return nil },
	}
	for _, opt := range opts {
		opt(f)
	}
	return f
}

func FakeBatch(ctx context.Context) (bqbatch.Batcher, error) {
	return &FakeBatcher{
		RegisterFn: func(rowExample interface{}, table string, cfg bqbatch.QueueConfig) error {
			return nil
		},
		AddFn: func(row interface{}) error {
			return nil
		},
		LoadBatchFn: func(table string, rawData []byte) error {
			return nil
		},
		ShutdownFn: func() error {
			return nil
		},
	}, nil
}

// fakeBatch implements bqbatch.Batcher for testing.
type FakeBatcher struct {
	RegisterFn  func(rowExample interface{}, table string, cfg bqbatch.QueueConfig) error
	AddFn       func(row interface{}) error
	LoadBatchFn func(table string, rawData []byte) error
	ShutdownFn  func() error
}

// Register ties a struct type to a table name and queue config.
func (f *FakeBatcher) Register(rowExample interface{}, table string, cfg bqbatch.QueueConfig) error {
	return f.RegisterFn(rowExample, table, cfg)
}

// Add enqueues a row into the fake batcher.
func (f *FakeBatcher) Add(row interface{}) error {
	return f.AddFn(row)
}

// LoadBatch loads raw JSONL data directly into BigQuery for the specified table.
func (f *FakeBatcher) LoadBatch(table string, rawData []byte) error {
	return f.LoadBatchFn(table, rawData)
}

// Shutdown flushes all queues (no-op by default) and stops accepting new rows.
func (f *FakeBatcher) Shutdown() error {
	return f.ShutdownFn()
}

// FakeBatchOption lets callers patch one of the FakeBatcher hooks.
type FakeBatchOption func(*FakeBatcher)
