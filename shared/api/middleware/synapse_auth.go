package middleware

import (
	"net/http"
	"os"
	"strings"

	"synapse-its.com/shared/api/response"
)

// SynapseAuth is a middleware that validates the x-api-key header against the SYNAPSE_API_KEY environment variable
func SynapseAuth(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		apiKey := r.Header.Get("x-api-key")
		expectedKey := os.Getenv("SYNAPSE_API_KEY")

		if apiKey == "" || expectedKey == "" || !strings.EqualFold(apiKey, expectedKey) {
			response.CreateUnauthorizedResponse(w)
			return
		}

		next.ServeHTTP(w, r)
	})
}
