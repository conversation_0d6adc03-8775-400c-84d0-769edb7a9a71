package middleware

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSynapseAuth(t *testing.T) {
	tests := []struct {
		name          string
		apiKey        string
		envKey        string
		wantNext      bool
		wantStatus    int
		wantBodyCheck func(t *testing.T, body map[string]interface{})
	}{
		{
			name:       "valid api key",
			api<PERSON><PERSON>:     "test-key-123",
			envKey:     "test-key-123",
			wantNext:   true,
			wantStatus: http.StatusOK,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				assert.Equal(t, "pong", body["msg"], "expected next handler body 'pong'")
			},
		},
		{
			name:       "case insensitive match",
			apiKey:     "TEST-KEY-123",
			envKey:     "test-key-123",
			wantNext:   true,
			wantStatus: http.StatusOK,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				assert.Equal(t, "pong", body["msg"], "expected next handler body 'pong'")
			},
		},
		{
			name:       "missing api key header",
			api<PERSON>ey:     "",
			env<PERSON><PERSON>:     "test-key-123",
			wantNext:   false,
			wantStatus: http.StatusUnauthorized,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				assert.Equal(t, "error", body["status"], "expected status 'error'")
				assert.Equal(t, "Unauthorized", body["message"], "expected message 'Unauthorized'")
				code, ok := body["code"].(float64)
				assert.True(t, ok, "expected code to be a number")
				assert.Equal(t, float64(http.StatusUnauthorized), code, "expected code to be StatusUnauthorized")
				assert.Nil(t, body["data"], "expected data to be nil")
			},
		},
		{
			name:       "missing env key",
			apiKey:     "test-key-123",
			envKey:     "",
			wantNext:   false,
			wantStatus: http.StatusUnauthorized,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				assert.Equal(t, "error", body["status"], "expected status 'error'")
				assert.Equal(t, "Unauthorized", body["message"], "expected message 'Unauthorized'")
				code, ok := body["code"].(float64)
				assert.True(t, ok, "expected code to be a number")
				assert.Equal(t, float64(http.StatusUnauthorized), code, "expected code to be StatusUnauthorized")
				assert.Nil(t, body["data"], "expected data to be nil")
			},
		},
		{
			name:       "mismatched keys",
			apiKey:     "test-key-123",
			envKey:     "different-key",
			wantNext:   false,
			wantStatus: http.StatusUnauthorized,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				assert.Equal(t, "error", body["status"], "expected status 'error'")
				assert.Equal(t, "Unauthorized", body["message"], "expected message 'Unauthorized'")
				code, ok := body["code"].(float64)
				assert.True(t, ok, "expected code to be a number")
				assert.Equal(t, float64(http.StatusUnauthorized), code, "expected code to be StatusUnauthorized")
				assert.Nil(t, body["data"], "expected data to be nil")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable for test
			if tt.envKey != "" {
				t.Setenv("SYNAPSE_API_KEY", tt.envKey)
			}

			// build handler chain
			next := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// simple JSON response
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				json.NewEncoder(w).Encode(map[string]interface{}{"msg": "pong"})
			})
			handler := SynapseAuth(next)

			req := httptest.NewRequest("GET", "/test", nil)
			if tt.apiKey != "" {
				req.Header.Set("x-api-key", tt.apiKey)
			}
			rr := httptest.NewRecorder()

			handler.ServeHTTP(rr, req)

			// did we hit next?
			nextCalled := rr.Code == http.StatusOK && tt.wantNext
			assert.Equal(t, tt.wantNext, nextCalled, "next handler called status mismatch")

			// status check
			assert.Equal(t, tt.wantStatus, rr.Code, "status code mismatch")

			// decode body
			var body map[string]interface{}
			err := json.NewDecoder(rr.Body).Decode(&body)
			assert.NoError(t, err, "failed to decode response body")

			tt.wantBodyCheck(t, body)
		})
	}
}
