package authorizer

import (
	"fmt"
	"slices"
	"strings"

	connect "synapse-its.com/shared/connect"
)

// =============================================================================
// DEVICE ACCESS RESOLUTION METHODS
// =============================================================================

// GetAuthorizedDevices returns a list of device IDs that the user has access to based on their permissions
// This function queries the database to resolve the actual devices based on the user's scope permissions
// It supports organization-level, device group-level, and location group-level permissions
func (up *UserPermissions) GetAuthorizedDevices(pg connect.DatabaseExecutor, requiredPermissions ...string) ([]string, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Check if user has any of the required permissions
	if !up.hasAnyPermissionFromList(requiredPermissions) {
		return []string{}, nil // No permissions, return empty list
	}

	// Get devices from all permission scopes in a single query
	deviceIds, err := up.getDevicesForPermissionScopes(pg, up.Permissions, requiredPermissions)
	if err != nil {
		return nil, fmt.Errorf("failed to get devices for permission scopes: %w", err)
	}

	// The new function already returns unique results due to DISTINCT in queries
	return deviceIds, nil
}

// GetAuthorizedDevicesByOrganization returns devices the user can access within a specific organization
// This is useful when needing to filter devices by organization while respecting user permissions
func (up *UserPermissions) GetAuthorizedDevicesByOrganization(pg connect.DatabaseExecutor, organizationID string, requiredPermissions ...string) ([]string, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Filter permissions to only those within the specified organization
	var orgPermissions []Permission
	for _, permission := range up.Permissions {
		if permission.OrganizationID == organizationID {
			orgPermissions = append(orgPermissions, permission)
		}
	}

	// Get devices from permissions within the specified organization in a single query
	deviceIds, err := up.getDevicesForPermissionScopes(pg, orgPermissions, requiredPermissions)
	if err != nil {
		return nil, fmt.Errorf("failed to get devices for permission scopes in org %s: %w", organizationID, err)
	}

	// The new function already returns unique results due to DISTINCT in queries
	return deviceIds, nil
}

// CanAccessDevice checks if the user can access a specific device with the given permissions
// This function retrieves device information and checks if any of the user's permissions grant access
func (up *UserPermissions) CanAccessDevice(pg connect.DatabaseExecutor, deviceID string, requiredPermissions ...string) (bool, error) {
	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Get the device's organization and group memberships
	deviceInfo, err := getDeviceInfo(pg, deviceID)
	if err != nil {
		return false, fmt.Errorf("failed to get device info: %w", err)
	}

	// If no device is found, return false
	if deviceInfo == nil {
		return false, nil
	}

	// Check each permission scope
	for _, permission := range up.Permissions {
		// Skip if user doesn't have any required permissions in this scope
		if !up.hasPermissionsInScope(permission, requiredPermissions) {
			continue
		}

		switch permission.Scope {
		case "org":
			// Organization-level permissions grant access to all devices in the organization
			if permission.ScopeID == deviceInfo.OrganizationID {
				return true, nil
			}

		case "device_group":
			// Device group permissions grant access to devices in that specific group
			for _, groupID := range deviceInfo.DeviceGroupIDs {
				if permission.ScopeID == groupID {
					return true, nil
				}
			}

		case "location_group":
			// Location group permissions grant access to devices in that location group
			for _, locationGroupID := range deviceInfo.LocationGroupIDs {
				if permission.ScopeID == locationGroupID {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

// CanAccessDeviceByOrigID checks if the user can access a specific device using its OrigID with the given permissions
//
// DEPRECATED: This function is deprecated and should not be used in new code.
// Use CanAccessDevice(deviceId string, ...) instead which uses the device's UUID.
// This function exists for backward compatibility with legacy code that uses OrigID.
//
// This function retrieves device information by OrigID and checks if any of the user's permissions grant access
func (up *UserPermissions) CanAccessDeviceByOrigID(pg connect.DatabaseExecutor, origID int64, requiredPermissions ...string) (string, error) {
	if pg == nil {
		return "", fmt.Errorf("pg is nil")
	}

	if len(requiredPermissions) == 0 {
		requiredPermissions = []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"}
	}

	// Get the device's organization and group memberships using OrigID
	deviceInfo, err := getDeviceInfoByOrigID(pg, origID)
	if err != nil {
		return "", fmt.Errorf("failed to get device info by origID: %w", err)
	}

	// If no device is found, return empty string
	if deviceInfo == nil {
		return "", nil
	}

	// Check each permission scope
	for _, permission := range up.Permissions {
		// Skip if user doesn't have any required permissions in this scope
		if !up.hasPermissionsInScope(permission, requiredPermissions) {
			continue
		}

		switch permission.Scope {
		case "org":
			// Organization-level permissions grant access to all devices in the organization
			if permission.ScopeID == deviceInfo.OrganizationID {
				return deviceInfo.DeviceID, nil
			}

		case "device_group":
			// Device group permissions grant access to devices in that specific group
			if slices.Contains(deviceInfo.DeviceGroupIDs, permission.ScopeID) {
				return deviceInfo.DeviceID, nil
			}

		case "location_group":
			// Location group permissions grant access to devices in that location group
			if slices.Contains(deviceInfo.LocationGroupIDs, permission.ScopeID) {
				return deviceInfo.DeviceID, nil
			}
		}
	}

	return "", nil
}

// =============================================================================
// DATABASE QUERY METHODS
// =============================================================================

// getDevicesForPermissionScopes gets device IDs for multiple permission scopes in a single JOIN query
// This is the optimized method that handles all permission types (org, device_group, location_group) in one database call
// It builds a single query with LEFT JOINs and OR conditions to efficiently retrieve all accessible devices
func (up *UserPermissions) getDevicesForPermissionScopes(pg connect.DatabaseExecutor, permissions []Permission, requiredPermissions []string) ([]string, error) {
	if len(permissions) == 0 {
		return []string{}, nil
	}

	// Filter permissions that have the required permissions and group by scope
	var orgScopes []string
	var deviceGroupScopes []string
	var locationGroupScopes []string

	for _, permission := range permissions {
		if !up.hasPermissionsInScope(permission, requiredPermissions) {
			continue
		}

		switch permission.Scope {
		case "org":
			orgScopes = append(orgScopes, permission.ScopeID)
		case "device_group":
			deviceGroupScopes = append(deviceGroupScopes, permission.ScopeID)
		case "location_group":
			locationGroupScopes = append(locationGroupScopes, permission.ScopeID)
		}
	}

	// If no valid scopes, return empty
	if len(orgScopes) == 0 && len(deviceGroupScopes) == 0 && len(locationGroupScopes) == 0 {
		return []string{}, nil
	}

	// Build WHERE conditions for each scope type
	var whereConditions []string
	var args []interface{}
	argIndex := 1

	// Add organization-level condition
	if len(orgScopes) > 0 {
		placeholders := make([]string, len(orgScopes))
		for i, orgID := range orgScopes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, orgID)
			argIndex++
		}
		whereConditions = append(whereConditions, fmt.Sprintf("sg.OrganizationId IN (%s)", strings.Join(placeholders, ",")))
	}

	// Add device group-level condition
	if len(deviceGroupScopes) > 0 {
		placeholders := make([]string, len(deviceGroupScopes))
		for i, groupID := range deviceGroupScopes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, groupID)
			argIndex++
		}
		whereConditions = append(whereConditions, fmt.Sprintf("dgd.DeviceGroupId IN (%s)", strings.Join(placeholders, ",")))
	}

	if len(locationGroupScopes) > 0 {
		placeholders := make([]string, len(locationGroupScopes))
		for i, locationGroupId := range locationGroupScopes {
			placeholders[i] = fmt.Sprintf("$%d", argIndex)
			args = append(args, locationGroupId)
			argIndex++
		}
		whereConditions = append(whereConditions, fmt.Sprintf("lgl.LocationGroupId IN (%s)", strings.Join(placeholders, ",")))
	}

	// Build a query with LEFT JOINs to handle all permission types
	query := fmt.Sprintf(`
		SELECT DISTINCT d.Id::text as device_id
		FROM {{Device}} d
		JOIN {{SoftwareGateway}} sg ON d.SoftwareGatewayId = sg.Id
		LEFT JOIN {{DeviceGroupDevices}} dgd ON d.Id = dgd.DeviceID
		LEFT JOIN {{Location}} l ON d.LocationId = l.Id
		LEFT JOIN {{LocationGroupLocations}} lgl ON l.Id = lgl.LocationId
		WHERE NOT d.IsDeleted
		AND (%s)`,
		strings.Join(whereConditions, " OR "))

	var deviceIds []struct {
		DeviceID string `db:"device_id"`
	}
	err := pg.QueryGenericSlice(&deviceIds, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query devices for multiple permission scopes: %w", err)
	}

	var deviceIDs []string
	for _, device := range deviceIds {
		deviceIDs = append(deviceIDs, device.DeviceID)
	}

	return deviceIDs, nil
}
