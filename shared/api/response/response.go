package response

import (
	"encoding/json"
	"net/http"
)

func CreateResponse(header map[string]string, body map[string]interface{}, httpStatus int, w http.ResponseWriter) {
	for key, value := range header {
		w.Header().Set(key, value)
	}
	w.<PERSON><PERSON><PERSON>eader(httpStatus)
	json.NewEncoder(w).Encode(body)
}

func CreateSuccessResponse(T any, w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "success",
		"data":    T,
		"message": "Request Succeeded",
		"code":    http.StatusOK,
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, http.StatusOK, w)
}

func CreateUnauthorizedResponse(w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "error",
		"data":    nil,
		"message": "Unauthorized",
		"code":    http.StatusUnauthorized,
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, http.StatusUnauthorized, w)
}

func CreateInternalErrorResponse(w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "error",
		"data":    nil,
		"message": "Internal Server Error",
		"code":    http.StatusInternalServerError,
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, http.StatusInternalServerError, w)
}

func CreateForbiddenResponse(w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "forbidden",
		"data":    nil,
		"message": "Forbidden",
		"code":    http.StatusForbidden,
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, http.StatusForbidden, w)
}

func CreateAuthSuccessResponse(T any, w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "success",
		"data":    T,
		"message": "Request Succeeded",
		"code":    http.StatusOK,
	}

	header := map[string]string{
		"Content-Type":                 "application/json",
		"Access-Control-Allow-Origin":  "*",
		"Access-Control-Allow-Headers": "Content-Type,Authorization,X-Api-Key",
		"Access-Control-Allow-Methods": "POST, OPTIONS",
	}

	CreateResponse(header, response, http.StatusOK, w)
}

func CreateBadRequestResponse(w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "error",
		"data":    nil,
		"message": "Bad Request",
		"code":    http.StatusBadRequest,
	}
	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, http.StatusBadRequest, w)
}

func CreateMethodNotAllowedResponse(w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "error",
		"data":    nil,
		"message": "Method Not Allowed",
		"code":    http.StatusMethodNotAllowed,
	}
	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, http.StatusMethodNotAllowed, w)
}

func CreateNotFoundResponse(w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "error",
		"data":    nil,
		"message": "Not Found",
		"code":    http.StatusNotFound,
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, http.StatusNotFound, w)
}

func CreateCustomErrorResponse(message string, data interface{}, statusCode int, w http.ResponseWriter) {
	response := map[string]interface{}{
		"status":  "error",
		"data":    data,
		"message": message,
		"code":    statusCode,
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	CreateResponse(header, response, statusCode, w)
}
