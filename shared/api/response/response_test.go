package response

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCreateResponse(t *testing.T) {
	t.Parallel()
	// Create a ResponseRecorder to capture the response.
	rr := httptest.NewRecorder()

	// Define expected header, body, and status.
	expectedHeader := map[string]string{"Content-Type": "application/json"}
	body := map[string]interface{}{"key": "value"}
	status := http.StatusOK

	// Call the function under test.
	CreateResponse(expectedHeader, body, status, rr)

	// Check if the status code is as expected.
	if rr.Code != status {
		t.Errorf("expected status code %d, got %d", status, rr.Code)
	}

	// Check if the Content-Type header is correctly set.
	if contentType := rr.Header().Get("Content-Type"); contentType != "application/json" {
		t.Errorf("expected Content-Type header 'application/json', got '%s'", contentType)
	}

	// Because json.Encoder.Encode adds a newline, trim the result for string comparison.
	resultBody := strings.TrimSpace(rr.Body.String())
	expectedJSON := `{"key":"value"}`

	// Direct string comparison.
	if resultBody != expectedJSON {
		t.Errorf("expected body %q, got %q", expectedJSON, resultBody)
	}
}

func TestCreateSuccessResponse(t *testing.T) {
	t.Parallel()
	recorder := httptest.NewRecorder()

	// Create mock data
	data := map[string]string{"key": "value"}

	// Call createSuccessResponse with test data.
	CreateSuccessResponse(data, recorder)

	// Verify the header is set.
	if ct := recorder.Header().Get("Content-Type"); ct != "application/json" {
		t.Errorf("Expected Content-Type 'application/json', got %q", ct)
	}

	// Verify the HTTP status code.
	if recorder.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, recorder.Code)
	}

	// Decode the JSON response.
	var response map[string]interface{}
	if err := json.NewDecoder(recorder.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode JSON: %v", err)
	}

	// Check the response fields.
	if status, ok := response["status"].(string); !ok || status != "success" {
		t.Errorf("Expected status 'success', got %v", response["status"])
	}
	if message, ok := response["message"].(string); !ok || message != "Request Succeeded" {
		t.Errorf("Expected message 'Request Succeeded', got %v", response["message"])
	}
	if code, ok := response["code"].(float64); !ok || int(code) != http.StatusOK {
		t.Errorf("Expected code %d, got %v", http.StatusOK, response["code"])
	}
	// Validate that the data field matches what we passed.
	if dataResp, ok := response["data"].(map[string]interface{}); !ok {
		t.Errorf("Expected data to be a map, got %T", response["data"])
	} else {
		if val, ok := dataResp["key"].(string); !ok || val != "value" {
			t.Errorf("Expected data['key'] to be 'value', got %v", dataResp["key"])
		}
	}
}

func TestCreateUnauthorizedResponse(t *testing.T) {
	t.Parallel()
	recorder := httptest.NewRecorder()

	// Call createUnauthorizedResponse.
	CreateUnauthorizedResponse(recorder)

	// Verify header and status code.
	if ct := recorder.Header().Get("Content-Type"); ct != "application/json" {
		t.Errorf("Expected Content-Type 'application/json', got %q", ct)
	}
	if recorder.Code != http.StatusUnauthorized {
		t.Errorf("Expected status code %d, got %d", http.StatusUnauthorized, recorder.Code)
	}

	// Decode the JSON response.
	var response map[string]interface{}
	if err := json.NewDecoder(recorder.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode JSON: %v", err)
	}

	// Check the response fields.
	if status, ok := response["status"].(string); !ok || status != "error" {
		t.Errorf("Expected status 'error', got %v", response["status"])
	}
	if message, ok := response["message"].(string); !ok || message != "Unauthorized" {
		t.Errorf("Expected message 'Unauthorized', got %v", response["message"])
	}
	if code, ok := response["code"].(float64); !ok || int(code) != http.StatusUnauthorized {
		t.Errorf("Expected code %d, got %v", http.StatusUnauthorized, response["code"])
	}
	if response["data"] != nil {
		t.Errorf("Expected data to be nil, got %v", response["data"])
	}
}

func TestCreateInternalErrorResponse(t *testing.T) {
	t.Parallel()
	recorder := httptest.NewRecorder()

	// Call createInternalErrorResponse.
	CreateInternalErrorResponse(recorder)

	// Verify header is set.
	if ct := recorder.Header().Get("Content-Type"); ct != "application/json" {
		t.Errorf("Expected Content-Type 'application/json', got %q", ct)
	}

	// Verify status as InternalServerError
	if recorder.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP status code %d, got %d", http.StatusInternalServerError, recorder.Code)
	}

	// Decode the JSON response.
	var response map[string]interface{}
	if err := json.NewDecoder(recorder.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode JSON: %v", err)
	}

	// Check the response fields.
	if status, ok := response["status"].(string); !ok || status != "error" {
		t.Errorf("Expected status 'error', got %v", response["status"])
	}
	if message, ok := response["message"].(string); !ok || message != "Internal Server Error" {
		t.Errorf("Expected message 'Internal Server Error', got %v", response["message"])
	}
	// The JSON field "code" is set to http.StatusInternalServerError (500).
	if code, ok := response["code"].(float64); !ok || int(code) != http.StatusInternalServerError {
		t.Errorf("Expected JSON code %d, got %v", http.StatusInternalServerError, response["code"])
	}
	if response["data"] != nil {
		t.Errorf("Expected data to be nil, got %v", response["data"])
	}
}

func TestCreateAuthSuccessResponse(t *testing.T) {
	t.Parallel()
	recorder := httptest.NewRecorder()

	// Create mock data
	data := map[string]string{"key": "value"}

	// Call createInternalErrorResponse.
	CreateAuthSuccessResponse(data, recorder)

	// Verify header and status code.
	if ct := recorder.Header().Get("Content-Type"); ct != "application/json" {
		t.Errorf("Expected Content-Type 'application/json', got %s", ct)
	}
	if ct := recorder.Header().Get("Access-Control-Allow-Origin"); ct != "*" {
		t.Errorf("Expected Access-Control-Allow-Origin '*', got %s", ct)
	}
	if ct := recorder.Header().Get("Access-Control-Allow-Headers"); ct != "Content-Type,Authorization,X-Api-Key" {
		t.Errorf("Expected Access-Control-Allow-Headers 'Content-Type,Authorization,X-Api-Key', got %s", ct)
	}
	if ct := recorder.Header().Get("Access-Control-Allow-Methods"); ct != "POST, OPTIONS" {
		t.Errorf("Expected Access-Control-Allow-Methods 'POST, OPTIONS', got %s", ct)
	}
	if recorder.Code != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, recorder.Code)
	}

	// Decode the JSON response.
	var response map[string]interface{}
	if err := json.NewDecoder(recorder.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode JSON: %v", err)
	}

	// Check the response fields.
	if status, ok := response["status"].(string); !ok || status != "success" {
		t.Errorf("Expected status 'success', got %v", response["status"])
	}
	if message, ok := response["message"].(string); !ok || message != "Request Succeeded" {
		t.Errorf("Expected message 'Request Succeeded', got %v", response["message"])
	}
	if code, ok := response["code"].(float64); !ok || int(code) != http.StatusOK {
		t.Errorf("Expected code %d, got %v", http.StatusOK, response["code"])
	}
	// Validate that the data field matches what we passed.
	if dataResp, ok := response["data"].(map[string]interface{}); !ok {
		t.Errorf("Expected data to be a map, got %T", response["data"])
	} else {
		if val, ok := dataResp["key"].(string); !ok || val != "value" {
			t.Errorf("Expected data['key'] to be 'value', got %v", dataResp["key"])
		}
	}
}

func TestCreateForbiddenResponse(t *testing.T) {
	t.Parallel()
	// Arrange
	recorder := httptest.NewRecorder()

	// Act
	CreateForbiddenResponse(recorder)

	// Assert: status code
	if recorder.Code != http.StatusForbidden {
		t.Errorf("expected status code %d, got %d", http.StatusForbidden, recorder.Code)
	}

	// Assert: Content-Type header
	if ct := recorder.Header().Get("Content-Type"); ct != "application/json" {
		t.Errorf("expected Content-Type 'application/json', got %q", ct)
	}

	// Decode response body
	var resp map[string]interface{}
	if err := json.NewDecoder(recorder.Body).Decode(&resp); err != nil {
		t.Fatalf("failed to decode JSON body: %v", err)
	}

	// Assert: status field
	if status, ok := resp["status"].(string); !ok || status != "forbidden" {
		t.Errorf("expected status 'forbidden', got %v", resp["status"])
	}

	// Assert: message field
	if msg, ok := resp["message"].(string); !ok || msg != "Forbidden" {
		t.Errorf("expected message 'Forbidden', got %v", resp["message"])
	}

	// Assert: code field
	if code, ok := resp["code"].(float64); !ok || int(code) != http.StatusForbidden {
		t.Errorf("expected code %d, got %v", http.StatusForbidden, resp["code"])
	}

	// Assert: data field is nil
	if data, exists := resp["data"]; exists && data != nil {
		t.Errorf("expected data to be nil, got %v", data)
	}
}

func TestCreateBadRequestResponse(t *testing.T) {
	t.Parallel()
	// Arrange
	recorder := httptest.NewRecorder()

	// Act
	CreateBadRequestResponse(recorder)

	// Assert
	assert.Equal(t, http.StatusBadRequest, recorder.Code, "status code should be Bad Request")
	assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"), "Content-Type should be application/json")

	// Decode response body
	var resp map[string]interface{}
	err := json.NewDecoder(recorder.Body).Decode(&resp)
	assert.NoError(t, err, "should decode JSON without error")

	// Assert response fields
	assert.Equal(t, "error", resp["status"], "status should be error")
	assert.Equal(t, "Bad Request", resp["message"], "message should be Bad Request")
	assert.Equal(t, float64(http.StatusBadRequest), resp["code"], "code should be Bad Request")
	assert.Nil(t, resp["data"], "data should be nil")
}

func TestCreateMethodNotAllowedResponse(t *testing.T) {
	t.Parallel()
	// Arrange
	recorder := httptest.NewRecorder()

	// Act
	CreateMethodNotAllowedResponse(recorder)

	// Assert
	assert.Equal(t, http.StatusMethodNotAllowed, recorder.Code, "status code should be Method Not Allowed")
	assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"), "Content-Type should be application/json")

	// Decode response body
	var resp map[string]interface{}
	err := json.NewDecoder(recorder.Body).Decode(&resp)
	assert.NoError(t, err, "should decode JSON without error")

	// Assert response fields
	assert.Equal(t, "error", resp["status"], "status should be error")
	assert.Equal(t, "Method Not Allowed", resp["message"], "message should be Method Not Allowed")
	assert.Equal(t, float64(http.StatusMethodNotAllowed), resp["code"], "code should be Method Not Allowed")
	assert.Nil(t, resp["data"], "data should be nil")
}

func Test_CreateNotFoundResponse(t *testing.T) {
	t.Parallel()
	// Create a ResponseRecorder to capture the response
	rr := httptest.NewRecorder()

	// Call the function under test
	CreateNotFoundResponse(rr)

	// Check the status code is 404 Not Found
	assert.Equal(t, http.StatusNotFound, rr.Code, "expected status code %d, got %d", http.StatusNotFound, rr.Code)

	// Check if the Content-Type header is correctly set
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"),
		"expected Content-Type header 'application/json', got '%s'", rr.Header().Get("Content-Type"))

	// Decode the JSON response
	var response map[string]interface{}
	err := json.NewDecoder(rr.Body).Decode(&response)
	assert.NoError(t, err, "Failed to decode JSON response")

	// Check the response fields
	assert.Equal(t, "error", response["status"], "Expected status 'error', got %v", response["status"])
	assert.Equal(t, "Not Found", response["message"], "Expected message 'Not Found', got %v", response["message"])
	assert.Equal(t, float64(http.StatusNotFound), response["code"], "Expected code %d, got %v", http.StatusNotFound, response["code"])
	assert.Nil(t, response["data"], "Expected data to be nil, got %v", response["data"])
}

func TestCreateCustomErrorResponse(t *testing.T) {
	t.Parallel()
	// Create a ResponseRecorder to capture the response
	rr := httptest.NewRecorder()

	// Test data
	message := "Custom error occurred"
	data := map[string]string{"error_field": "error_value"}
	statusCode := http.StatusTeapot // 418

	// Call the function under test
	CreateCustomErrorResponse(message, data, statusCode, rr)

	// Check the status code
	assert.Equal(t, statusCode, rr.Code, "expected status code %d, got %d", statusCode, rr.Code)

	// Check if the Content-Type header is correctly set
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"),
		"expected Content-Type header 'application/json', got '%s'", rr.Header().Get("Content-Type"))

	// Decode the JSON response
	var response map[string]interface{}
	err := json.NewDecoder(rr.Body).Decode(&response)
	assert.NoError(t, err, "Failed to decode JSON response")

	// Check the response fields
	assert.Equal(t, "error", response["status"], "Expected status 'error', got %v", response["status"])
	assert.Equal(t, message, response["message"], "Expected message '%s', got %v", message, response["message"])
	assert.Equal(t, float64(statusCode), response["code"], "Expected code %d, got %v", statusCode, response["code"])

	// Check the data field
	dataResp, ok := response["data"].(map[string]interface{})
	assert.True(t, ok, "Expected data to be a map, got %T", response["data"])
	assert.Equal(t, "error_value", dataResp["error_field"], "Expected data field to match")
}

func TestCreateCustomErrorResponse_WithNilData(t *testing.T) {
	t.Parallel()
	// Create a ResponseRecorder to capture the response
	rr := httptest.NewRecorder()

	// Test data
	message := "Error with nil data"
	statusCode := http.StatusBadGateway // 502

	// Call the function under test with nil data
	CreateCustomErrorResponse(message, nil, statusCode, rr)

	// Check the status code
	assert.Equal(t, statusCode, rr.Code, "expected status code %d, got %d", statusCode, rr.Code)

	// Decode the JSON response
	var response map[string]interface{}
	err := json.NewDecoder(rr.Body).Decode(&response)
	assert.NoError(t, err, "Failed to decode JSON response")

	// Check the response fields
	assert.Equal(t, "error", response["status"], "Expected status 'error', got %v", response["status"])
	assert.Equal(t, message, response["message"], "Expected message '%s', got %v", message, response["message"])
	assert.Equal(t, float64(statusCode), response["code"], "Expected code %d, got %v", statusCode, response["code"])
	assert.Nil(t, response["data"], "Expected data to be nil, got %v", response["data"])
}
