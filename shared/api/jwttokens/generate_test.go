package jwttokens

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"errors"
	"io"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGenerateJwtKeys(t *testing.T) {
	// Test basic key generation
	privBase64, pubBase64, err := generateJwtKeys()

	// Should not return an error
	assert.NoError(t, err)

	// Should return non-empty strings
	assert.NotEmpty(t, privBase64, "private key should not be empty")
	assert.NotEmpty(t, pubBase64, "public key should not be empty")

	// Should be valid base64 strings
	_, err = base64.StdEncoding.DecodeString(privBase64)
	assert.NoError(t, err, "private key should be valid base64")

	_, err = base64.StdEncoding.DecodeString(pubBase64)
	assert.NoError(t, err, "public key should be valid base64")
}

func TestGenerateJwtKeys_PrivateKeyFormat(t *testing.T) {
	privBase64, _, err := generateJwtKeys()
	require.NoError(t, err)

	// Decode base64
	privPEM, err := base64.StdEncoding.DecodeString(privBase64)
	require.NoError(t, err)

	// Should be valid PEM format
	block, _ := pem.Decode(privPEM)
	require.NotNil(t, block, "private key should be valid PEM")
	assert.Equal(t, "RSA PRIVATE KEY", block.Type, "private key should be RSA type")

	// Should be parseable as RSA private key
	privKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	require.NoError(t, err, "should parse as PKCS1 private key")

	// Should be 2048 bits
	assert.Equal(t, 2048, privKey.N.BitLen(), "private key should be 2048 bits")

	// Should have valid RSA components
	assert.NotNil(t, privKey.D, "private key should have D component")
	assert.NotNil(t, privKey.Primes, "private key should have prime factors")
	assert.Len(t, privKey.Primes, 2, "private key should have 2 prime factors")
}

func TestGenerateJwtKeys_PublicKeyFormat(t *testing.T) {
	_, pubBase64, err := generateJwtKeys()
	require.NoError(t, err)

	// Decode base64
	pubPEM, err := base64.StdEncoding.DecodeString(pubBase64)
	require.NoError(t, err)

	// Should be valid PEM format
	block, _ := pem.Decode(pubPEM)
	require.NotNil(t, block, "public key should be valid PEM")
	assert.Equal(t, "PUBLIC KEY", block.Type, "public key should be PUBLIC KEY type")

	// Should be parseable as public key
	pubKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	require.NoError(t, err, "should parse as PKIX public key")

	// Should be RSA public key
	rsaPubKey, ok := pubKey.(*rsa.PublicKey)
	require.True(t, ok, "public key should be RSA type")

	// Should be 2048 bits
	assert.Equal(t, 2048, rsaPubKey.N.BitLen(), "public key should be 2048 bits")

	// Should have valid RSA components
	assert.NotNil(t, rsaPubKey.N, "public key should have N component")
	assert.NotNil(t, rsaPubKey.E, "public key should have E component")
}

func TestGenerateJwtKeys_KeyPairCompatibility(t *testing.T) {
	privBase64, pubBase64, err := generateJwtKeys()
	require.NoError(t, err)

	// Decode and parse private key
	privPEM, err := base64.StdEncoding.DecodeString(privBase64)
	require.NoError(t, err)
	privBlock, _ := pem.Decode(privPEM)
	require.NotNil(t, privBlock)
	privKey, err := x509.ParsePKCS1PrivateKey(privBlock.Bytes)
	require.NoError(t, err)

	// Decode and parse public key
	pubPEM, err := base64.StdEncoding.DecodeString(pubBase64)
	require.NoError(t, err)
	pubBlock, _ := pem.Decode(pubPEM)
	require.NotNil(t, pubBlock)
	pubKeyInterface, err := x509.ParsePKIXPublicKey(pubBlock.Bytes)
	require.NoError(t, err)
	pubKey, ok := pubKeyInterface.(*rsa.PublicKey)
	require.True(t, ok)

	// The public key from the private key should match the generated public key
	assert.Equal(t, privKey.PublicKey.N, pubKey.N, "public key N should match private key's public key N")
	assert.Equal(t, privKey.PublicKey.E, pubKey.E, "public key E should match private key's public key E")
}

func TestGenerateJwtKeys_UniqueKeys(t *testing.T) {
	// Generate multiple key pairs
	priv1, pub1, err1 := generateJwtKeys()
	priv2, pub2, err2 := generateJwtKeys()

	// Both should succeed
	assert.NoError(t, err1)
	assert.NoError(t, err2)

	// Keys should be different each time
	assert.NotEqual(t, priv1, priv2, "private keys should be unique")
	assert.NotEqual(t, pub1, pub2, "public keys should be unique")
}

func TestGenerateJwtKeys_PEMStructure(t *testing.T) {
	privBase64, pubBase64, err := generateJwtKeys()
	require.NoError(t, err)

	// Decode base64 to get PEM
	privPEM, err := base64.StdEncoding.DecodeString(privBase64)
	require.NoError(t, err)
	pubPEM, err := base64.StdEncoding.DecodeString(pubBase64)
	require.NoError(t, err)

	// Check PEM structure
	privPEMStr := string(privPEM)
	pubPEMStr := string(pubPEM)

	// Private key PEM structure
	assert.True(t, strings.HasPrefix(privPEMStr, "-----BEGIN RSA PRIVATE KEY-----\n"),
		"private key should start with proper PEM header")
	assert.True(t, strings.HasSuffix(privPEMStr, "-----END RSA PRIVATE KEY-----\n"),
		"private key should end with proper PEM footer")

	// Public key PEM structure
	assert.True(t, strings.HasPrefix(pubPEMStr, "-----BEGIN PUBLIC KEY-----\n"),
		"public key should start with proper PEM header")
	assert.True(t, strings.HasSuffix(pubPEMStr, "-----END PUBLIC KEY-----\n"),
		"public key should end with proper PEM footer")
}

func TestGenerateJwtKeys_Base64Encoding(t *testing.T) {
	privBase64, pubBase64, err := generateJwtKeys()
	require.NoError(t, err)

	// Should be valid base64 (no padding issues, valid characters)
	_, err = base64.StdEncoding.DecodeString(privBase64)
	assert.NoError(t, err, "private key should be valid standard base64")

	_, err = base64.StdEncoding.DecodeString(pubBase64)
	assert.NoError(t, err, "public key should be valid standard base64")

	// Should not contain invalid base64 characters
	validBase64Chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="
	for _, char := range privBase64 {
		assert.Contains(t, validBase64Chars, string(char),
			"private key base64 should only contain valid characters")
	}

	for _, char := range pubBase64 {
		assert.Contains(t, validBase64Chars, string(char),
			"public key base64 should only contain valid characters")
	}
}

func TestGenerateJwtKeys_KeySizes(t *testing.T) {
	privBase64, pubBase64, err := generateJwtKeys()
	require.NoError(t, err)

	// Private key should be significantly larger than public key
	assert.Greater(t, len(privBase64), len(pubBase64),
		"private key should be larger than public key")

	// Base64 encoded 2048-bit RSA private key should be around 1700+ characters
	assert.Greater(t, len(privBase64), 1600,
		"private key base64 should be reasonably sized for 2048-bit key")

	// Base64 encoded 2048-bit RSA public key should be around 400+ characters
	assert.Greater(t, len(pubBase64), 300,
		"public key base64 should be reasonably sized for 2048-bit key")
}

// Benchmark the key generation performance
func BenchmarkGenerateJwtKeys(b *testing.B) {
	for i := 0; i < b.N; i++ {
		_, _, err := generateJwtKeys()
		if err != nil {
			b.Fatal(err)
		}
	}
}

func TestGenerateJwtKeys_ErrorHandling(t *testing.T) {
	// This test verifies that the function handles errors appropriately
	// The actual error conditions are very rare in practice, but we can
	// verify the function structure and error return paths

	// Test multiple iterations to ensure consistency
	for i := 0; i < 5; i++ {
		privBase64, pubBase64, err := generateJwtKeys()

		// Should never return an error under normal conditions
		assert.NoError(t, err, "generateJwtKeys should not error under normal conditions")
		assert.NotEmpty(t, privBase64, "private key should not be empty")
		assert.NotEmpty(t, pubBase64, "public key should not be empty")

		// Verify the keys are properly formatted
		_, err = base64.StdEncoding.DecodeString(privBase64)
		assert.NoError(t, err, "private key should be valid base64")

		_, err = base64.StdEncoding.DecodeString(pubBase64)
		assert.NoError(t, err, "public key should be valid base64")
	}
}

func TestGenerateJwtKeys_ConcurrentSafety(t *testing.T) {
	// Test that the function is safe for concurrent use
	const numGoroutines = 10
	results := make(chan struct {
		priv, pub string
		err       error
	}, numGoroutines)

	// Launch multiple goroutines
	for i := 0; i < numGoroutines; i++ {
		go func() {
			priv, pub, err := generateJwtKeys()
			results <- struct {
				priv, pub string
				err       error
			}{priv, pub, err}
		}()
	}

	// Collect results
	keys := make(map[string]bool)
	for i := 0; i < numGoroutines; i++ {
		result := <-results
		assert.NoError(t, result.err, "concurrent key generation should not error")
		assert.NotEmpty(t, result.priv, "private key should not be empty")
		assert.NotEmpty(t, result.pub, "public key should not be empty")

		// Ensure all keys are unique
		keyPair := result.priv + ":" + result.pub
		assert.False(t, keys[keyPair], "key pairs should be unique")
		keys[keyPair] = true
	}
}

func TestGenerateJwtKeys_MemoryUsage(t *testing.T) {
	// Test that the function doesn't leak memory or create excessive allocations
	var privBase64, pubBase64 string
	var err error

	// Generate a key pair
	privBase64, pubBase64, err = generateJwtKeys()
	require.NoError(t, err)

	// Verify reasonable memory usage (base64 encoded keys shouldn't be too large)
	assert.Less(t, len(privBase64), 10000, "private key base64 should be reasonably sized")
	assert.Less(t, len(pubBase64), 2000, "public key base64 should be reasonably sized")

	// Verify the keys are not empty after generation
	assert.Greater(t, len(privBase64), 1000, "private key should be substantial")
	assert.Greater(t, len(pubBase64), 200, "public key should be substantial")
}

// Mock implementations for testing error conditions

// FailingKeyGenerator always returns an error
type FailingKeyGenerator struct{}

func (f FailingKeyGenerator) GenerateKey(random io.Reader, bits int) (*rsa.PrivateKey, error) {
	return nil, errors.New("mock key generation failure")
}

// FailingPublicKeyMarshaler always returns an error
type FailingPublicKeyMarshaler struct{}

func (f FailingPublicKeyMarshaler) MarshalPKIXPublicKey(pub interface{}) ([]byte, error) {
	return nil, errors.New("mock public key marshaling failure")
}

func TestGenerateJwtKeys_KeyGenerationError(t *testing.T) {
	// Test the error path when key generation fails
	failingKeyGen := FailingKeyGenerator{}
	defaultMarshaler := DefaultPublicKeyMarshaler{}

	privBase64, pubBase64, err := generateJwtKeysWithDeps(failingKeyGen, defaultMarshaler)

	// Should return an error
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to generate private key")
	assert.Contains(t, err.Error(), "mock key generation failure")

	// Should return empty strings
	assert.Empty(t, privBase64)
	assert.Empty(t, pubBase64)
}

func TestGenerateJwtKeys_PublicKeyMarshalingError(t *testing.T) {
	// Test the error path when public key marshaling fails
	defaultKeyGen := DefaultKeyGenerator{}
	failingMarshaler := FailingPublicKeyMarshaler{}

	privBase64, pubBase64, err := generateJwtKeysWithDeps(defaultKeyGen, failingMarshaler)

	// Should return an error
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to marshal public key")
	assert.Contains(t, err.Error(), "mock public key marshaling failure")

	// Should return empty strings
	assert.Empty(t, privBase64)
	assert.Empty(t, pubBase64)
}

func TestGenerateJwtKeysWithDeps_Success(t *testing.T) {
	// Test the success path with dependency injection
	defaultKeyGen := DefaultKeyGenerator{}
	defaultMarshaler := DefaultPublicKeyMarshaler{}

	privBase64, pubBase64, err := generateJwtKeysWithDeps(defaultKeyGen, defaultMarshaler)

	// Should not return an error
	assert.NoError(t, err)
	assert.NotEmpty(t, privBase64)
	assert.NotEmpty(t, pubBase64)

	// Should produce the same result as the original function
	privBase64Orig, pubBase64Orig, errOrig := generateJwtKeys()
	assert.NoError(t, errOrig)

	// Keys should be different (random generation) but same format
	assert.NotEqual(t, privBase64, privBase64Orig, "keys should be randomly generated")
	assert.NotEqual(t, pubBase64, pubBase64Orig, "keys should be randomly generated")

	// But both should be valid
	_, err = base64.StdEncoding.DecodeString(privBase64)
	assert.NoError(t, err)
	_, err = base64.StdEncoding.DecodeString(pubBase64)
	assert.NoError(t, err)
}

func TestDefaultKeyGenerator(t *testing.T) {
	// Test the DefaultKeyGenerator implementation
	keyGen := DefaultKeyGenerator{}

	key, err := keyGen.GenerateKey(rand.Reader, 2048)
	assert.NoError(t, err)
	assert.NotNil(t, key)
	assert.Equal(t, 2048, key.N.BitLen())
}

func TestDefaultPublicKeyMarshaler(t *testing.T) {
	// Test the DefaultPublicKeyMarshaler implementation
	marshaler := DefaultPublicKeyMarshaler{}

	// Generate a test key
	keyGen := DefaultKeyGenerator{}
	key, err := keyGen.GenerateKey(rand.Reader, 2048)
	require.NoError(t, err)

	// Test marshaling
	pubDER, err := marshaler.MarshalPKIXPublicKey(&key.PublicKey)
	assert.NoError(t, err)
	assert.NotEmpty(t, pubDER)

	// Should be parseable back
	parsedPub, err := x509.ParsePKIXPublicKey(pubDER)
	assert.NoError(t, err)
	assert.NotNil(t, parsedPub)
}
