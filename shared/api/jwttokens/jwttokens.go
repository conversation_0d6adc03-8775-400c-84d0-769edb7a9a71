package jwttokens

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
	helper "synapse-its.com/shared/api/helper"
	security "synapse-its.com/shared/api/security"
)

// CreateJwtTokenUsingDuration creates a signed jwt token with the desired duration
func CreateJwtTokenUsingDuration(username string, duration time.Duration, userPermissions UserPermissions) (signedJwtToken string, tokenExpiration time.Time, err error) {
	var permission string

	expiration := time.Now().Add(duration).UTC()

	if permission, err = createUserPermissions(userPermissions); err != nil {
		return "", expiration, err
	}

	claims := &JwtToken{}
	claims.AuthId = username
	claims.ExpiresAt = expiration
	claims.IssuedAt = time.Now().UTC()
	claims.JwtId = helper.CreateGuid()
	claims.Permissions = permission

	theToken, err := signJwtToken(*claims)
	if err != nil {
		return "", expiration, fmt.Errorf("error - signing token")
	}

	return theToken, expiration, nil
}

// CreateJwtToken creates a signed jwt token.  Refer to CreateJwtTokenUsingDuration to have flexibility in hours, sec, etc for the units.  Note:  This will eventually be deprecated
func CreateJwtToken(username string, tokenexpirationinhours int, userPermissions UserPermissions) (signedJwtToken string, tokenExpiration time.Time, err error) {
	return CreateJwtTokenUsingDuration(username, (time.Duration(tokenexpirationinhours) * time.Hour), userPermissions)
}

// signJwtToken takes in a JwtToken struct and outputs a signed jwt string
func signJwtToken(jwtTokenToBeSigned JwtToken) (theToken string, err error) {
	privateKey, _, err := security.GetJWTAsymmetric()
	if err != nil {
		return "", err
	}
	privKey, err := security.ParseRSAPrivateKeyFromPEM(string(privateKey))
	if err != nil {
		return "", err
	}
	// convert token struct to map[string]interface{}
	jsonBytes, _ := json.Marshal(jwtTokenToBeSigned)

	var mapIt map[string]interface{}
	err = json.Unmarshal(jsonBytes, &mapIt)
	if err != nil {
		return "", err
	}
	claims := jwt.MapClaims(mapIt)
	// token := jwt.NewWithClaims(jwt.SigningMethodRS512, claims)		// 4096 len rsa cert
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims) // 2048 len rsa cert
	theToken, err = token.SignedString(privKey)
	if err != nil {
		return "", err
	}
	return theToken, nil
}

// ValidateJJwtTokenWithPublicKey validates the jstTokenToBeValidate with the public key passed in.
func ValidateJJwtTokenWithPublicKey(jwtTokenToBeValidated string, publicKey string) (jwtToken *JwtToken, userPermissions *UserPermissions, err error) {
	pubKey, err := security.ParseRSAPublicKeyFromPEM(string(publicKey))
	if err != nil {
		return nil, nil, err
	}

	// checking authenticity of the jwt token
	token, err := jwt.Parse(jwtTokenToBeValidated, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return pubKey, nil
	})
	if err != nil {
		return nil, nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		// convert the two time components
		layout := time.RFC3339Nano
		exp, err := time.Parse(layout, claims["expires_at"].(string))
		if err != nil {
			return nil, nil, fmt.Errorf("error - cannot convert expires_at to time.Time (%s)", claims["expires_at"].(string))
		}
		iss, err := time.Parse(layout, claims["issued_at"].(string))
		if err != nil {
			return nil, nil, fmt.Errorf("error - cannot convert issued_at to time.Time (%s)", claims["issued_at"].(string))
		}

		tokenBits := &JwtToken{}
		tokenBits.AuthId = claims["auth_id"].(string)
		tokenBits.IssuedBy = claims["issued_by"].(string)
		tokenBits.ExpiresAt = exp
		tokenBits.IssuedAt = iss
		tokenBits.JwtId = claims["jwt_id"].(string)
		tokenBits.Permissions = claims["permissions"].(string)

		isExpired := tokenBits.ExpiresAt.Before(time.Now().UTC())
		if isExpired {
			return nil, nil, fmt.Errorf("error - token expired - must re-authenticate")
		} else {
			if userPerms, err := getUserPermissions(tokenBits.Permissions); err != nil {
				return nil, nil, fmt.Errorf("error - extracting user permissions")
			} else {
				return tokenBits, userPerms, nil // the token is good
			}
		}
	} else {
		return nil, nil, fmt.Errorf("error - invalid token")
	}
}

// ValidateJJwtToken validates the jwttoken received is valid, if err != nil, it's valid and you can use jtToken and user permissions.
var ValidateJJwtToken = func(jwtTokenToBeValidated string) (jwtToken *JwtToken, userPermissions *UserPermissions, err error) {
	_, publicKey, err := security.GetJWTAsymmetric()
	if err != nil {
		return nil, nil, err
	}

	return ValidateJJwtTokenWithPublicKey(jwtTokenToBeValidated, publicKey)
}

// CreateUserPermissions takes the UserPermissions and returns the zipped base 64 encoded version of it for inclusion in the JWT token.
func createUserPermissions(userPermissions UserPermissions) (base64zippedPermissions string, err error) {
	// convert this to json, zip it, base 64 encode it, and return the string
	jsonStr, _ := json.Marshal(userPermissions)

	zipped, err := helper.CompressBytes(jsonStr)
	if err != nil {
		return "", err
	}
	finalPermissions := base64.StdEncoding.EncodeToString(zipped)

	return finalPermissions, nil
}

// getUserPermissions takes a permissions string from a JwtToken and returns the user permissions
func getUserPermissions(permissions string) (userPermissions *UserPermissions, err error) {
	userPerms := &UserPermissions{}

	data, err := base64.StdEncoding.DecodeString(permissions)
	if err != nil {
		return userPerms, err
	}

	uncompressed, err := helper.DecompressBytes(data)
	if err != nil {
		return userPerms, err
	}

	err = json.Unmarshal(uncompressed, userPerms)
	if err != nil {
		return userPerms, err
	}

	return userPerms, nil
}
