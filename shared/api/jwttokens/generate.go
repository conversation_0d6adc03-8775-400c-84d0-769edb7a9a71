package jwttokens

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"io"
)

// KeyGenerator interface for dependency injection in tests
type KeyGenerator interface {
	GenerateKey(random io.Reader, bits int) (*rsa.PrivateKey, error)
}

// PublicKeyMarshaler interface for dependency injection in tests
type PublicKeyMarshaler interface {
	MarshalPKIXPublicKey(pub interface{}) ([]byte, error)
}

// DefaultKeyGenerator implements KeyGenerator using the standard library
type DefaultKeyGenerator struct{}

func (d DefaultKeyGenerator) GenerateKey(random io.Reader, bits int) (*rsa.PrivateKey, error) {
	return rsa.GenerateKey(random, bits)
}

// DefaultPublicKeyMarshaler implements PublicKeyMarshaler using the standard library
type DefaultPublicKeyMarshaler struct{}

func (d DefaultPublicKeyMarshaler) MarshalPKIXPublicKey(pub interface{}) ([]byte, error) {
	return x509.MarshalPKIXPublicKey(pub)
}

// generateJwtKeysWithDeps allows dependency injection for testing
func generateJwtKeysWithDeps(keyGen KeyGenerator, pubMarshaler PublicKeyMarshaler) (string, string, error) {
	// Generate an RSA private key.
	priv, err := keyGen.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate private key: %v", err)
	}

	// Marshal the private key to PKCS#1 DER format.
	privDER := x509.MarshalPKCS1PrivateKey(priv)
	// Create a PEM block for the private key.
	privBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privDER,
	}
	// PEM encode and then Base64 encode the private key.
	privPEM := pem.EncodeToMemory(privBlock)
	privBase64 := base64.StdEncoding.EncodeToString(privPEM)

	// Marshal the public key to PKIX, ASN.1 DER format.
	pubDER, err := pubMarshaler.MarshalPKIXPublicKey(&priv.PublicKey)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal public key: %v", err)
	}
	// Create a PEM block for the public key.
	pubBlock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pubDER,
	}
	// PEM encode and then Base64 encode the public key.
	pubPEM := pem.EncodeToMemory(pubBlock)
	pubBase64 := base64.StdEncoding.EncodeToString(pubPEM)

	return privBase64, pubBase64, nil
}

// generateKeys creates a new RSA key pair, PEM-encodes them, and returns their Base64 strings.
// Use it for generating the public and private keys for the JWT_PRIVATE_KEY and JWT_PUBLIC_KEY env variables
func generateJwtKeys() (string, string, error) {
	return generateJwtKeysWithDeps(DefaultKeyGenerator{}, DefaultPublicKeyMarshaler{})
}
