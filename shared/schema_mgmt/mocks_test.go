package schema_mgmt

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMockDirEntry(t *testing.T) {
	t.Run("Name", func(t *testing.T) {
		mock := mockDirEntry{name: "test-directory"}
		
		result := mock.Name()
		
		assert.Equal(t, "test-directory", result)
	})

	t.Run("IsDir", func(t *testing.T) {
		mock := mockDirEntry{name: "test-directory"}
		
		result := mock.IsDir()
		
		assert.True(t, result)
	})

	t.Run("Type", func(t *testing.T) {
		mock := mockDirEntry{name: "test-directory"}
		
		result := mock.Type()
		
		assert.Equal(t, os.ModeDir, result)
	})

	t.Run("Info", func(t *testing.T) {
		mock := mockDirEntry{name: "test-directory"}
		
		info, err := mock.Info()
		
		assert.Nil(t, info)
		assert.Nil(t, err)
	})

	t.Run("Different_Names", func(t *testing.T) {
		testCases := []string{
			"dir1",
			"dir2",
			"some-long-directory-name",
			"",
			"dir with spaces",
			"dir/with/path",
		}

		for _, name := range testCases {
			t.Run("Name_"+name, func(t *testing.T) {
				mock := mockDirEntry{name: name}
				
				result := mock.Name()
				
				assert.Equal(t, name, result)
			})
		}
	})

	t.Run("Multiple_Instances", func(t *testing.T) {
		mock1 := mockDirEntry{name: "dir1"}
		mock2 := mockDirEntry{name: "dir2"}

		assert.Equal(t, "dir1", mock1.Name())
		assert.Equal(t, "dir2", mock2.Name())
		
		assert.True(t, mock1.IsDir())
		assert.True(t, mock2.IsDir())
		
		assert.Equal(t, os.ModeDir, mock1.Type())
		assert.Equal(t, os.ModeDir, mock2.Type())
		
		info1, err1 := mock1.Info()
		info2, err2 := mock2.Info()
		
		assert.Nil(t, info1)
		assert.Nil(t, err1)
		assert.Nil(t, info2)
		assert.Nil(t, err2)
	})

	t.Run("Interface_Compliance", func(t *testing.T) {
		var _ os.DirEntry = mockDirEntry{}
	})
} 