syntax = "proto3";

package gateway.v1;

import "gateway/v1/deviceType.proto";

option go_package = "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1";

// DeviceData represents the overall body payload.
message DeviceData {

  // Repeated list of all device entries.
  repeated DeviceEntry messages = 1;
}

// Defines an individual message from a device.
message DeviceEntry {

  // Unique identifier for the device.
  string device_id = 1;
  
  // The payload message.
  // For most message-types, this field is a byte array (typically containing byte data directly from the device).
  // For message type "Mac Address", the content is a UTF-8 string.
  bytes message = 2;
  
  // Type of device
  DeviceType device_type = 3;
}
