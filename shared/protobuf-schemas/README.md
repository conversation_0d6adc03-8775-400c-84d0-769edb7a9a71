# Protobuf Schemas

This repository contains Protocol Buffer (`.proto`) files and generated Go and JavaScript code for various services and APIs.

## Quick Start

### Option 1: Development Container (Recommended)

For the best development experience, use the included VS Code Development Container:

1. Install [VS Code](https://code.visualstudio.com/) and [Docker Desktop](https://www.docker.com/products/docker-desktop/)
2. Install the [Dev Containers extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers)
3. Clone this repository and open it in VS Code
4. When prompted, click "Reopen in Container"
5. All tools will be pre-installed and ready to use

See [.devcontainer/README.md](.devcontainer/README.md) for detailed setup instructions.

### Option 2: Local Development

If you prefer to develop locally, see the [scripts/README.md](scripts/README.md) for setup instructions.

## Project Structure

```
├── models/                          # Custom protobuf definitions
│   ├── gateway/v1/                  # Gateway service definitions
│   ├── rushhour/v1/                 # Rushhour service definitions
│   └── monitors-firmware/           # Monitor firmware definitions
├── monf-protobufs-messages/         # Subtree of external protobufs
├── protomessages/                   # Generated code
│   ├── go/                          # Go language files
│   │   ├── gateway/v1/              # Generated Go for gateway
│   │   ├── rushhour/v1/             # Generated Go for rushhour
│   │   └── monf/                    # Generated Go for monf-protobufs
│   └── js/                          # JavaScript files
│       ├── gateway/v1/              # Generated JS for gateway
│       ├── rushhour/v1/             # Generated JS for rushhour
│       └── monf/                    # Generated JS for monf-protobufs
├── scripts/                         # Generation scripts
├── .devcontainer/                   # VS Code dev container config
└── Dockerfile                       # Production Docker image
```

## Usage

### Generate Protobuf Code

```bash
# Generate all protobuf files (Go + JavaScript)
./scripts/gen-proto.ps1

# Rebuild Docker image if needed
./scripts/gen-proto.ps1 -BuildImage
```

### Managing monf-protobufs-messages Subtree

The `monf-protobufs-messages/` directory is managed as a Git subtree from the external repository. This allows us to include external protobuf definitions while maintaining a clean history.

#### Updating the Subtree (Most Common)

To get the latest changes from the external repository:

If you haven't added the remote yet:

```bash
git remote add monf-protobufs-messages https://bitbucket.org/synapse-its/monf-protobufs-messages.git
```

Pull the latest changes:

```bash
git subtree pull --prefix=monf-protobufs-messages monf-protobufs-messages develop --squash
```

_Note: Replace `develop` with the desired branch if needed._

After updating, regenerate the protobuf code:

```bash
./scripts/gen-proto.ps1
```

### Development

- **Go**: Use the generated Go files in `protomessages/go/`
- **JavaScript**: Use the generated JavaScript files in `protomessages/js/`
- **Protocol Buffers**: Edit `.proto` files in `models/` and `monf-protobufs-messages/`

## Documentation

- [Scripts Documentation](scripts/README.md) - Detailed setup and usage instructions
- [Dev Container Documentation](.devcontainer/README.md) - Development container setup
- [Protocol Buffers Guide](https://developers.google.com/protocol-buffers) - Official documentation

## Contributing

1. Make changes to `.proto` files in the `models/` directory
2. Run the generation script to update the generated code
3. Commit both the `.proto` changes and the generated code
4. Submit a pull request

## Notes

- The `monf-protobufs-messages/` directory is managed as a Git subtree and should not be modified directly
- Generated code is committed to the repository for easy consumption by other projects
- The development container provides a consistent environment across all developers
