// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: gateway/v1/deviceType.proto

package gatewayv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Enum for device types.
type DeviceType int32

const (
	DeviceType_UNKNOWN_DEVICE DeviceType = 0
	DeviceType_EDI_LEGACY     DeviceType = 1
	DeviceType_EDI_NEXT_GEN   DeviceType = 2
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		0: "UNKNOWN_DEVICE",
		1: "EDI_LEGACY",
		2: "EDI_NEXT_GEN",
	}
	DeviceType_value = map[string]int32{
		"UNKNOWN_DEVICE": 0,
		"EDI_LEGACY":     1,
		"EDI_NEXT_GEN":   2,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_gateway_v1_deviceType_proto_enumTypes[0].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file_gateway_v1_deviceType_proto_enumTypes[0]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_gateway_v1_deviceType_proto_rawDescGZIP(), []int{0}
}

var File_gateway_v1_deviceType_proto protoreflect.FileDescriptor

const file_gateway_v1_deviceType_proto_rawDesc = "" +
	"\n" +
	"\x1bgateway/v1/deviceType.proto\x12\n" +
	"gateway.v1*B\n" +
	"\n" +
	"DeviceType\x12\x12\n" +
	"\x0eUNKNOWN_DEVICE\x10\x00\x12\x0e\n" +
	"\n" +
	"EDI_LEGACY\x10\x01\x12\x10\n" +
	"\fEDI_NEXT_GEN\x10\x02BOZMbitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1b\x06proto3"

var (
	file_gateway_v1_deviceType_proto_rawDescOnce sync.Once
	file_gateway_v1_deviceType_proto_rawDescData []byte
)

func file_gateway_v1_deviceType_proto_rawDescGZIP() []byte {
	file_gateway_v1_deviceType_proto_rawDescOnce.Do(func() {
		file_gateway_v1_deviceType_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gateway_v1_deviceType_proto_rawDesc), len(file_gateway_v1_deviceType_proto_rawDesc)))
	})
	return file_gateway_v1_deviceType_proto_rawDescData
}

var file_gateway_v1_deviceType_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_gateway_v1_deviceType_proto_goTypes = []any{
	(DeviceType)(0), // 0: gateway.v1.DeviceType
}
var file_gateway_v1_deviceType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_gateway_v1_deviceType_proto_init() }
func file_gateway_v1_deviceType_proto_init() {
	if File_gateway_v1_deviceType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gateway_v1_deviceType_proto_rawDesc), len(file_gateway_v1_deviceType_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gateway_v1_deviceType_proto_goTypes,
		DependencyIndexes: file_gateway_v1_deviceType_proto_depIdxs,
		EnumInfos:         file_gateway_v1_deviceType_proto_enumTypes,
	}.Build()
	File_gateway_v1_deviceType_proto = out.File
	file_gateway_v1_deviceType_proto_goTypes = nil
	file_gateway_v1_deviceType_proto_depIdxs = nil
}
