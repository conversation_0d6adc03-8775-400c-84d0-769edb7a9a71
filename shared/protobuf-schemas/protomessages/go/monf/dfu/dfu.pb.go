//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  DFU
//Messages and Enums for firmware update commands and responses.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: dfu.proto

package dfu

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EProcessorType - Possible processor types
type EProcessorType int32

const (
	EProcessorType_PROCESSOR_UNSPECIFIED EProcessorType = 0
	EProcessorType_PROCESSOR_MAIN        EProcessorType = 1
	EProcessorType_PROCESSOR_ISOLATED    EProcessorType = 2
	EProcessorType_PROCESSOR_DISPLAY     EProcessorType = 3
	EProcessorType_PROCESSOR_COMMS       EProcessorType = 4
	EProcessorType_PROCESSOR_BLE         EProcessorType = 5
	EProcessorType_PROCESSOR_PACKAGE     EProcessorType = 6 // For overall update package version
)

// Enum value maps for EProcessorType.
var (
	EProcessorType_name = map[int32]string{
		0: "PROCESSOR_UNSPECIFIED",
		1: "PROCESSOR_MAIN",
		2: "PROCESSOR_ISOLATED",
		3: "PROCESSOR_DISPLAY",
		4: "PROCESSOR_COMMS",
		5: "PROCESSOR_BLE",
		6: "PROCESSOR_PACKAGE",
	}
	EProcessorType_value = map[string]int32{
		"PROCESSOR_UNSPECIFIED": 0,
		"PROCESSOR_MAIN":        1,
		"PROCESSOR_ISOLATED":    2,
		"PROCESSOR_DISPLAY":     3,
		"PROCESSOR_COMMS":       4,
		"PROCESSOR_BLE":         5,
		"PROCESSOR_PACKAGE":     6,
	}
)

func (x EProcessorType) Enum() *EProcessorType {
	p := new(EProcessorType)
	*p = x
	return p
}

func (x EProcessorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EProcessorType) Descriptor() protoreflect.EnumDescriptor {
	return file_dfu_proto_enumTypes[0].Descriptor()
}

func (EProcessorType) Type() protoreflect.EnumType {
	return &file_dfu_proto_enumTypes[0]
}

func (x EProcessorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EProcessorType.Descriptor instead.
func (EProcessorType) EnumDescriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{0}
}

// ENUM EImageType - A designator for the type of image being updated or reported
type EImageType int32

const (
	EImageType_IMAGE_UNSPECIFIED       EImageType = 0
	EImageType_IMAGE_MCUBOOT           EImageType = 1
	EImageType_IMAGE_APPLICATION       EImageType = 2 // Including Non-Secure portion of Zephyr Application code
	EImageType_IMAGE_SECURE_CODE       EImageType = 3 // Secure Application code, if separate from Application code
	EImageType_IMAGE_PROTECTED_STORAGE EImageType = 4 // The updateable Protected Storage area
	EImageType_IMAGE_BOOT_SECURE_DATA  EImageType = 5 // The Secure MCUboot data (eg, keys, certs)
)

// Enum value maps for EImageType.
var (
	EImageType_name = map[int32]string{
		0: "IMAGE_UNSPECIFIED",
		1: "IMAGE_MCUBOOT",
		2: "IMAGE_APPLICATION",
		3: "IMAGE_SECURE_CODE",
		4: "IMAGE_PROTECTED_STORAGE",
		5: "IMAGE_BOOT_SECURE_DATA",
	}
	EImageType_value = map[string]int32{
		"IMAGE_UNSPECIFIED":       0,
		"IMAGE_MCUBOOT":           1,
		"IMAGE_APPLICATION":       2,
		"IMAGE_SECURE_CODE":       3,
		"IMAGE_PROTECTED_STORAGE": 4,
		"IMAGE_BOOT_SECURE_DATA":  5,
	}
)

func (x EImageType) Enum() *EImageType {
	p := new(EImageType)
	*p = x
	return p
}

func (x EImageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EImageType) Descriptor() protoreflect.EnumDescriptor {
	return file_dfu_proto_enumTypes[1].Descriptor()
}

func (EImageType) Type() protoreflect.EnumType {
	return &file_dfu_proto_enumTypes[1]
}

func (x EImageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EImageType.Descriptor instead.
func (EImageType) EnumDescriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{1}
}

// ENUM EFirmwareUpdateStatus values give overall status on a firmware update package's progress .
type EFirmwareUpdateStatus int32

const (
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_UNSPECIFIED EFirmwareUpdateStatus = 0
	// The process is done and the update has succeeded for all images.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_SUCCESS EFirmwareUpdateStatus = 1
	// An update is being prepared by downloading an update package.
	// Initiated by CmdFirmwareUpdateManifest.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_DOWNLOADING EFirmwareUpdateStatus = 2
	// An update is in progress, with the images being copied to the secondary image
	// locations on the target MCUs.
	// Set after the last image in the manifest has been downloaded;
	// will advance to PENDING when done.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_COPYING EFirmwareUpdateStatus = 3
	// An update is pending, waiting for a reboot or CmdRebootCommsMcu to start the update process
	// that will be managed by MCUboot.
	// Set after the last image in the manifest has been copied.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_PENDING EFirmwareUpdateStatus = 4
	// An update failed for at least one of the images; see the images' EManifestEntryStatus for the reason.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_FAILED EFirmwareUpdateStatus = 5
	// An update could not be performed at this time, eg, due to power failure.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_NOT_POSSIBLE_NOW EFirmwareUpdateStatus = 6
	// Update failed before it was started; eg, invalid CPU number, bad version string.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_FAILED_INVALID_PARAM EFirmwareUpdateStatus = 7
	// Update rejected for this product; not one of the supported models.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_FAILED_NO_SUPPORTED_MODEL EFirmwareUpdateStatus = 8
	// Update timed out before it was completed.
	EFirmwareUpdateStatus_FW_UPDATE_STATUS_FAILED_TIMEOUT EFirmwareUpdateStatus = 9
)

// Enum value maps for EFirmwareUpdateStatus.
var (
	EFirmwareUpdateStatus_name = map[int32]string{
		0: "FW_UPDATE_STATUS_UNSPECIFIED",
		1: "FW_UPDATE_STATUS_SUCCESS",
		2: "FW_UPDATE_STATUS_DOWNLOADING",
		3: "FW_UPDATE_STATUS_COPYING",
		4: "FW_UPDATE_STATUS_PENDING",
		5: "FW_UPDATE_STATUS_FAILED",
		6: "FW_UPDATE_STATUS_NOT_POSSIBLE_NOW",
		7: "FW_UPDATE_STATUS_FAILED_INVALID_PARAM",
		8: "FW_UPDATE_STATUS_FAILED_NO_SUPPORTED_MODEL",
		9: "FW_UPDATE_STATUS_FAILED_TIMEOUT",
	}
	EFirmwareUpdateStatus_value = map[string]int32{
		"FW_UPDATE_STATUS_UNSPECIFIED":               0,
		"FW_UPDATE_STATUS_SUCCESS":                   1,
		"FW_UPDATE_STATUS_DOWNLOADING":               2,
		"FW_UPDATE_STATUS_COPYING":                   3,
		"FW_UPDATE_STATUS_PENDING":                   4,
		"FW_UPDATE_STATUS_FAILED":                    5,
		"FW_UPDATE_STATUS_NOT_POSSIBLE_NOW":          6,
		"FW_UPDATE_STATUS_FAILED_INVALID_PARAM":      7,
		"FW_UPDATE_STATUS_FAILED_NO_SUPPORTED_MODEL": 8,
		"FW_UPDATE_STATUS_FAILED_TIMEOUT":            9,
	}
)

func (x EFirmwareUpdateStatus) Enum() *EFirmwareUpdateStatus {
	p := new(EFirmwareUpdateStatus)
	*p = x
	return p
}

func (x EFirmwareUpdateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EFirmwareUpdateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_dfu_proto_enumTypes[2].Descriptor()
}

func (EFirmwareUpdateStatus) Type() protoreflect.EnumType {
	return &file_dfu_proto_enumTypes[2]
}

func (x EFirmwareUpdateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EFirmwareUpdateStatus.Descriptor instead.
func (EFirmwareUpdateStatus) EnumDescriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{2}
}

//	ENUM EFirmwareVersionsManifest values select a manifest for returning version info.
//
// See cmd_resp_dfu.CmdManifestVersions for usage info.
type EFirmwareVersionsManifest int32

const (
	EFirmwareVersionsManifest_FW_MANIFEST_UNSPECIFIED EFirmwareVersionsManifest = 0
	EFirmwareVersionsManifest_FW_MANIFEST_PRESENT     EFirmwareVersionsManifest = 1
	EFirmwareVersionsManifest_FW_MANIFEST_UPDATE      EFirmwareVersionsManifest = 2
	EFirmwareVersionsManifest_FW_MANIFEST_NONE        EFirmwareVersionsManifest = 3
)

// Enum value maps for EFirmwareVersionsManifest.
var (
	EFirmwareVersionsManifest_name = map[int32]string{
		0: "FW_MANIFEST_UNSPECIFIED",
		1: "FW_MANIFEST_PRESENT",
		2: "FW_MANIFEST_UPDATE",
		3: "FW_MANIFEST_NONE",
	}
	EFirmwareVersionsManifest_value = map[string]int32{
		"FW_MANIFEST_UNSPECIFIED": 0,
		"FW_MANIFEST_PRESENT":     1,
		"FW_MANIFEST_UPDATE":      2,
		"FW_MANIFEST_NONE":        3,
	}
)

func (x EFirmwareVersionsManifest) Enum() *EFirmwareVersionsManifest {
	p := new(EFirmwareVersionsManifest)
	*p = x
	return p
}

func (x EFirmwareVersionsManifest) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EFirmwareVersionsManifest) Descriptor() protoreflect.EnumDescriptor {
	return file_dfu_proto_enumTypes[3].Descriptor()
}

func (EFirmwareVersionsManifest) Type() protoreflect.EnumType {
	return &file_dfu_proto_enumTypes[3]
}

func (x EFirmwareVersionsManifest) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EFirmwareVersionsManifest.Descriptor instead.
func (EFirmwareVersionsManifest) EnumDescriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{3}
}

//	ENUM EManifestEntryStatus values give the expected or actual status
//
// for one image entry, depending upon whether the App or Device is
// providing the Status value.
// The Happy Path flow for an update is
// NEEDS_UPDATE -> DOWNLOADED -> PENDING_RESTART -> SUCCESS,
// but that flow could terminate in any of the FAILED statuses.
type EManifestEntryStatus int32

const (
	EManifestEntryStatus_FW_IMAGE_STATUS_UNSPECIFIED EManifestEntryStatus = 0
	// Set by the App to indicate that this image entry needs to be updated,
	// and consequently its image should be downloaded next.
	// The Device will report this status until it can report FW_IMAGE_STATUS_DOWNLOADED.
	EManifestEntryStatus_FW_IMAGE_STATUS_NEEDS_UPDATE EManifestEntryStatus = 1
	// Set by the App to indicate that this image entry does not need to be updated,
	// and consequently there will be no image downloaded for it.
	// Its entry is provided for informational purposes, and for completeness.
	EManifestEntryStatus_FW_IMAGE_STATUS_NO_UPDATE EManifestEntryStatus = 2
	// Set by the Device once the update image has been received successfully, but
	// before it has been installed; this should be followed by the PENDING_RESTART
	// status after copying it to its secondary image location.
	EManifestEntryStatus_FW_IMAGE_STATUS_DOWNLOADED EManifestEntryStatus = 3
	// Set by the Device once the update image has been installed successfully.
	// This is also the final state after a successful update.
	EManifestEntryStatus_FW_IMAGE_STATUS_SUCCESS EManifestEntryStatus = 4
	// Set by the Device if the update image failed to be installed.
	EManifestEntryStatus_FW_IMAGE_STATUS_FAILED EManifestEntryStatus = 5
	// Update failed before it was started; eg, incorrect Model number, bad version string.
	EManifestEntryStatus_FW_IMAGE_STATUS_FAILED_INVALID_PARAM EManifestEntryStatus = 6
	// Update failed because an image was not valid; eg, bad hash, bad size.
	EManifestEntryStatus_FW_IMAGE_STATUS_FAILED_IMAGE_INVALID EManifestEntryStatus = 7
	// Update failed because an image version was not acceptable for upgrading.
	EManifestEntryStatus_FW_IMAGE_STATUS_FAILED_IMAGE_VERSION_INCOMPATIBLE EManifestEntryStatus = 8
	// Set by the Device after the update image has been installed (copied into its secondary
	// image location) and is waiting for the restart that will kick off updating of this image
	// to the primary location and executing it from there;
	// thus this is an "update pending" status.
	EManifestEntryStatus_FW_IMAGE_STATUS_PENDING_RESTART EManifestEntryStatus = 9
)

// Enum value maps for EManifestEntryStatus.
var (
	EManifestEntryStatus_name = map[int32]string{
		0: "FW_IMAGE_STATUS_UNSPECIFIED",
		1: "FW_IMAGE_STATUS_NEEDS_UPDATE",
		2: "FW_IMAGE_STATUS_NO_UPDATE",
		3: "FW_IMAGE_STATUS_DOWNLOADED",
		4: "FW_IMAGE_STATUS_SUCCESS",
		5: "FW_IMAGE_STATUS_FAILED",
		6: "FW_IMAGE_STATUS_FAILED_INVALID_PARAM",
		7: "FW_IMAGE_STATUS_FAILED_IMAGE_INVALID",
		8: "FW_IMAGE_STATUS_FAILED_IMAGE_VERSION_INCOMPATIBLE",
		9: "FW_IMAGE_STATUS_PENDING_RESTART",
	}
	EManifestEntryStatus_value = map[string]int32{
		"FW_IMAGE_STATUS_UNSPECIFIED":                       0,
		"FW_IMAGE_STATUS_NEEDS_UPDATE":                      1,
		"FW_IMAGE_STATUS_NO_UPDATE":                         2,
		"FW_IMAGE_STATUS_DOWNLOADED":                        3,
		"FW_IMAGE_STATUS_SUCCESS":                           4,
		"FW_IMAGE_STATUS_FAILED":                            5,
		"FW_IMAGE_STATUS_FAILED_INVALID_PARAM":              6,
		"FW_IMAGE_STATUS_FAILED_IMAGE_INVALID":              7,
		"FW_IMAGE_STATUS_FAILED_IMAGE_VERSION_INCOMPATIBLE": 8,
		"FW_IMAGE_STATUS_PENDING_RESTART":                   9,
	}
)

func (x EManifestEntryStatus) Enum() *EManifestEntryStatus {
	p := new(EManifestEntryStatus)
	*p = x
	return p
}

func (x EManifestEntryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EManifestEntryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_dfu_proto_enumTypes[4].Descriptor()
}

func (EManifestEntryStatus) Type() protoreflect.EnumType {
	return &file_dfu_proto_enumTypes[4]
}

func (x EManifestEntryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EManifestEntryStatus.Descriptor instead.
func (EManifestEntryStatus) EnumDescriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{4}
}

// ENUM EDfuResultCode values are the extra DFU-specific result_code values returned
// in the responses for some of the DFU commands.
type EDfuResultCode int32

const (
	EDfuResultCode_RESULT_UNSPECIFIED EDfuResultCode = 0
	// The normal result code for a successful operation
	EDfuResultCode_RESULT_SUCCESS EDfuResultCode = 1
	// The operation failed due to an invalid parameter in the command
	EDfuResultCode_RESULT_INVALID_PARAM EDfuResultCode = 2
	// The operation could not be executed at this time, eg, due to power failure
	EDfuResultCode_RESULT_NOT_POSSIBLE_NOW EDfuResultCode = 3
	// The command was not accepted by a device (check Update Manifest Status for more information)
	EDfuResultCode_RESULT_COMMAND_NOT_ACCEPTED EDfuResultCode = 4
	// The command failed due to some device-side issue (eg, file system failure)
	EDfuResultCode_RESULT_FAILED_AT_DEVICE EDfuResultCode = 5
	// Command could not be executed because a resource was not available or out of memory
	EDfuResultCode_RESULT_FAILED_UNAVAILABLE EDfuResultCode = 6
	// Command could not be executed because a parameter was missing or empty
	EDfuResultCode_RESULT_FAILED_MISSING_PARAM EDfuResultCode = 7
	// Command could not be executed because it is out of order - ie, a precursor step was not completed
	EDfuResultCode_RESULT_FAILED_OUT_OF_ORDER EDfuResultCode = 8
	// Command failed because of a file I/O error
	EDfuResultCode_RESULT_FAILED_FILE_IO_ERROR EDfuResultCode = 9
	// Command failed because of a comm error - eg, sending or receiving from another processor
	EDfuResultCode_RESULT_FAILED_COMM_ERROR EDfuResultCode = 10
	// Command could not be executed because no update was in progress
	EDfuResultCode_RESULT_FAILED_NO_UPDATE_IN_PROGRESS EDfuResultCode = 11
	// Command failed in one of its processing steps - eg, hash processing
	EDfuResultCode_RESULT_FAILED_PROCESSING_ERROR EDfuResultCode = 12
)

// Enum value maps for EDfuResultCode.
var (
	EDfuResultCode_name = map[int32]string{
		0:  "RESULT_UNSPECIFIED",
		1:  "RESULT_SUCCESS",
		2:  "RESULT_INVALID_PARAM",
		3:  "RESULT_NOT_POSSIBLE_NOW",
		4:  "RESULT_COMMAND_NOT_ACCEPTED",
		5:  "RESULT_FAILED_AT_DEVICE",
		6:  "RESULT_FAILED_UNAVAILABLE",
		7:  "RESULT_FAILED_MISSING_PARAM",
		8:  "RESULT_FAILED_OUT_OF_ORDER",
		9:  "RESULT_FAILED_FILE_IO_ERROR",
		10: "RESULT_FAILED_COMM_ERROR",
		11: "RESULT_FAILED_NO_UPDATE_IN_PROGRESS",
		12: "RESULT_FAILED_PROCESSING_ERROR",
	}
	EDfuResultCode_value = map[string]int32{
		"RESULT_UNSPECIFIED":                  0,
		"RESULT_SUCCESS":                      1,
		"RESULT_INVALID_PARAM":                2,
		"RESULT_NOT_POSSIBLE_NOW":             3,
		"RESULT_COMMAND_NOT_ACCEPTED":         4,
		"RESULT_FAILED_AT_DEVICE":             5,
		"RESULT_FAILED_UNAVAILABLE":           6,
		"RESULT_FAILED_MISSING_PARAM":         7,
		"RESULT_FAILED_OUT_OF_ORDER":          8,
		"RESULT_FAILED_FILE_IO_ERROR":         9,
		"RESULT_FAILED_COMM_ERROR":            10,
		"RESULT_FAILED_NO_UPDATE_IN_PROGRESS": 11,
		"RESULT_FAILED_PROCESSING_ERROR":      12,
	}
)

func (x EDfuResultCode) Enum() *EDfuResultCode {
	p := new(EDfuResultCode)
	*p = x
	return p
}

func (x EDfuResultCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EDfuResultCode) Descriptor() protoreflect.EnumDescriptor {
	return file_dfu_proto_enumTypes[5].Descriptor()
}

func (EDfuResultCode) Type() protoreflect.EnumType {
	return &file_dfu_proto_enumTypes[5]
}

func (x EDfuResultCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EDfuResultCode.Descriptor instead.
func (EDfuResultCode) EnumDescriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{5}
}

//	ENUM EMonitorModelNumber values are for the Model or Part Number of the Monitor
//
// Products, such as "MMU2-16LEip-RM-SF". While these are given by a 30-character
// string in the Serial protocol (field "Model Number" in "Read Factory Settings"),
// for simplicity, efficiency, and to avoid text matching issues, the Model Numbers
// will be captured here by a set of enums.
// As new products are supported, this list of enums will be extended to cover them.
type EMonitorModelNumber int32

const (
	EMonitorModelNumber_MODEL_NUMBER_UNSPECIFIED EMonitorModelNumber = 0
	// MMU2-16LEip-RM-SF - A 16 channel TS2 monitor with FYA support that is powered
	// by 120VAC with an Ethernet port and LCD displays. Rack mounted.
	EMonitorModelNumber_MODEL_NUMBER_MMU2_16LEIP_RM_SF EMonitorModelNumber = 1
	// DK-Nucleo-MMU - For MMU/CMU development and testing, the "model" built from the
	// H573 DK board and H563 Nucleo board.
	EMonitorModelNumber_MODEL_NUMBER_DK_NUCLEO_MMU EMonitorModelNumber = 2
)

// Enum value maps for EMonitorModelNumber.
var (
	EMonitorModelNumber_name = map[int32]string{
		0: "MODEL_NUMBER_UNSPECIFIED",
		1: "MODEL_NUMBER_MMU2_16LEIP_RM_SF",
		2: "MODEL_NUMBER_DK_NUCLEO_MMU",
	}
	EMonitorModelNumber_value = map[string]int32{
		"MODEL_NUMBER_UNSPECIFIED":       0,
		"MODEL_NUMBER_MMU2_16LEIP_RM_SF": 1,
		"MODEL_NUMBER_DK_NUCLEO_MMU":     2,
	}
)

func (x EMonitorModelNumber) Enum() *EMonitorModelNumber {
	p := new(EMonitorModelNumber)
	*p = x
	return p
}

func (x EMonitorModelNumber) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMonitorModelNumber) Descriptor() protoreflect.EnumDescriptor {
	return file_dfu_proto_enumTypes[6].Descriptor()
}

func (EMonitorModelNumber) Type() protoreflect.EnumType {
	return &file_dfu_proto_enumTypes[6]
}

func (x EMonitorModelNumber) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EMonitorModelNumber.Descriptor instead.
func (EMonitorModelNumber) EnumDescriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{6}
}

// DfuManifestEntry - One entry in the Firmware Update Manifest message, describing one image file.
type DfuManifestEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Image Type, target, and version
	ImageVersion *FirmwareImageVersion `protobuf:"bytes,1,opt,name=image_version,json=imageVersion,proto3" json:"image_version,omitempty"`
	// Name should contain the image type and be unique for each version
	Filename string `protobuf:"bytes,2,opt,name=filename,proto3" json:"filename,omitempty"` // Max string length set in dfu.options
	// SHA256 hash of the file
	Hash []byte `protobuf:"bytes,4,opt,name=hash,proto3" json:"hash,omitempty"` // Max length set in cmd_resp_dfu.options
	// Size of the file in bytes
	SizeBytes uint32 `protobuf:"varint,5,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	// Indicates the update status of this entry.
	EntryStatus   EManifestEntryStatus `protobuf:"varint,6,opt,name=entry_status,json=entryStatus,proto3,enum=dfu.EManifestEntryStatus" json:"entry_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DfuManifestEntry) Reset() {
	*x = DfuManifestEntry{}
	mi := &file_dfu_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DfuManifestEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DfuManifestEntry) ProtoMessage() {}

func (x *DfuManifestEntry) ProtoReflect() protoreflect.Message {
	mi := &file_dfu_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DfuManifestEntry.ProtoReflect.Descriptor instead.
func (*DfuManifestEntry) Descriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{0}
}

func (x *DfuManifestEntry) GetImageVersion() *FirmwareImageVersion {
	if x != nil {
		return x.ImageVersion
	}
	return nil
}

func (x *DfuManifestEntry) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *DfuManifestEntry) GetHash() []byte {
	if x != nil {
		return x.Hash
	}
	return nil
}

func (x *DfuManifestEntry) GetSizeBytes() uint32 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

func (x *DfuManifestEntry) GetEntryStatus() EManifestEntryStatus {
	if x != nil {
		return x.EntryStatus
	}
	return EManifestEntryStatus_FW_IMAGE_STATUS_UNSPECIFIED
}

// DfuManifestStatusEntry - One entry as reported in the RespManifestVersions message, describing one image's
// version information and its update status.
// This is a shortened variation of DfuManifestEntry, suitable for reading back image statuses.
type DfuManifestStatusEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Image Type, target, and version
	ImageVersion *FirmwareImageVersion `protobuf:"bytes,1,opt,name=image_version,json=imageVersion,proto3" json:"image_version,omitempty"`
	// Indicates the update status of this entry.
	EntryStatus   EManifestEntryStatus `protobuf:"varint,2,opt,name=entry_status,json=entryStatus,proto3,enum=dfu.EManifestEntryStatus" json:"entry_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DfuManifestStatusEntry) Reset() {
	*x = DfuManifestStatusEntry{}
	mi := &file_dfu_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DfuManifestStatusEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DfuManifestStatusEntry) ProtoMessage() {}

func (x *DfuManifestStatusEntry) ProtoReflect() protoreflect.Message {
	mi := &file_dfu_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DfuManifestStatusEntry.ProtoReflect.Descriptor instead.
func (*DfuManifestStatusEntry) Descriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{1}
}

func (x *DfuManifestStatusEntry) GetImageVersion() *FirmwareImageVersion {
	if x != nil {
		return x.ImageVersion
	}
	return nil
}

func (x *DfuManifestStatusEntry) GetEntryStatus() EManifestEntryStatus {
	if x != nil {
		return x.EntryStatus
	}
	return EManifestEntryStatus_FW_IMAGE_STATUS_UNSPECIFIED
}

// FirmwareImageVersion - A firmware image type, target MCU, and version.
type FirmwareImageVersion struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// MCU for which this file is intended
	TargetMcu EProcessorType `protobuf:"varint,1,opt,name=target_mcu,json=targetMcu,proto3,enum=dfu.EProcessorType" json:"target_mcu,omitempty"`
	// Type of image being updated
	ImageType EImageType `protobuf:"varint,2,opt,name=image_type,json=imageType,proto3,enum=dfu.EImageType" json:"image_type,omitempty"`
	// Version in the format <major>.<minor>.<revision> with ASCII numerals for the values, up to 3 digits each.
	// Examples:  "1.0.3"  "1.5.12"  "10.20.1" "1.31.186"
	// Incorrect: "1.8", "4.8.67.24"-(must be 3 values), "1.3.7c"-(values must be numerals),
	//
	//	"2.4567.78"-(more than 3 digit values), "1-3-89"-(separaters must be periods)
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	// version build, for the format <major>.<minor>.<revision>+<Build>
	// ASCII numerals only representing a 32-bit value (up to 10 digits)
	VersionBuild string `protobuf:"bytes,4,opt,name=version_build,json=versionBuild,proto3" json:"version_build,omitempty"`
	// Date of the build, YYYY/MM/DD
	// Month (MM) and Day (DD) are always 2 digits, year is always 4 digits.
	// Separator is required and must be slash '/'
	// Examples:  "2024/07/25" "2024/12/03"
	// Incorrect: "24/07/25" "2024/7/25" "2024-07-25" "07/24/2024"
	BuildDate     string `protobuf:"bytes,5,opt,name=build_date,json=buildDate,proto3" json:"build_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FirmwareImageVersion) Reset() {
	*x = FirmwareImageVersion{}
	mi := &file_dfu_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FirmwareImageVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FirmwareImageVersion) ProtoMessage() {}

func (x *FirmwareImageVersion) ProtoReflect() protoreflect.Message {
	mi := &file_dfu_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FirmwareImageVersion.ProtoReflect.Descriptor instead.
func (*FirmwareImageVersion) Descriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{2}
}

func (x *FirmwareImageVersion) GetTargetMcu() EProcessorType {
	if x != nil {
		return x.TargetMcu
	}
	return EProcessorType_PROCESSOR_UNSPECIFIED
}

func (x *FirmwareImageVersion) GetImageType() EImageType {
	if x != nil {
		return x.ImageType
	}
	return EImageType_IMAGE_UNSPECIFIED
}

func (x *FirmwareImageVersion) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *FirmwareImageVersion) GetVersionBuild() string {
	if x != nil {
		return x.VersionBuild
	}
	return ""
}

func (x *FirmwareImageVersion) GetBuildDate() string {
	if x != nil {
		return x.BuildDate
	}
	return ""
}

//	FirmwareVersionSimple - A firmware target MCU, version, and date.
//
// NOTE: This is used in cmd_resp_config.RespReadMonitorData for the versions
// returned.
type FirmwareVersionSimple struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// MCU for which this file is intended
	TargetMcu EProcessorType `protobuf:"varint,1,opt,name=target_mcu,json=targetMcu,proto3,enum=dfu.EProcessorType" json:"target_mcu,omitempty"`
	// version Major.Minor.Revision
	Version *basic.VersionStrThree `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	// Date of the build, Month/Day/Year
	BuildDate     *basic.DateStr `protobuf:"bytes,5,opt,name=build_date,json=buildDate,proto3" json:"build_date,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FirmwareVersionSimple) Reset() {
	*x = FirmwareVersionSimple{}
	mi := &file_dfu_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FirmwareVersionSimple) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FirmwareVersionSimple) ProtoMessage() {}

func (x *FirmwareVersionSimple) ProtoReflect() protoreflect.Message {
	mi := &file_dfu_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FirmwareVersionSimple.ProtoReflect.Descriptor instead.
func (*FirmwareVersionSimple) Descriptor() ([]byte, []int) {
	return file_dfu_proto_rawDescGZIP(), []int{3}
}

func (x *FirmwareVersionSimple) GetTargetMcu() EProcessorType {
	if x != nil {
		return x.TargetMcu
	}
	return EProcessorType_PROCESSOR_UNSPECIFIED
}

func (x *FirmwareVersionSimple) GetVersion() *basic.VersionStrThree {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *FirmwareVersionSimple) GetBuildDate() *basic.DateStr {
	if x != nil {
		return x.BuildDate
	}
	return nil
}

var File_dfu_proto protoreflect.FileDescriptor

const file_dfu_proto_rawDesc = "" +
	"\n" +
	"\tdfu.proto\x12\x03dfu\x1a\vbasic.proto\"\xdf\x01\n" +
	"\x10DfuManifestEntry\x12>\n" +
	"\rimage_version\x18\x01 \x01(\v2\x19.dfu.FirmwareImageVersionR\fimageVersion\x12\x1a\n" +
	"\bfilename\x18\x02 \x01(\tR\bfilename\x12\x12\n" +
	"\x04hash\x18\x04 \x01(\fR\x04hash\x12\x1d\n" +
	"\n" +
	"size_bytes\x18\x05 \x01(\rR\tsizeBytes\x12<\n" +
	"\fentry_status\x18\x06 \x01(\x0e2\x19.dfu.EManifestEntryStatusR\ventryStatus\"\x96\x01\n" +
	"\x16DfuManifestStatusEntry\x12>\n" +
	"\rimage_version\x18\x01 \x01(\v2\x19.dfu.FirmwareImageVersionR\fimageVersion\x12<\n" +
	"\fentry_status\x18\x02 \x01(\x0e2\x19.dfu.EManifestEntryStatusR\ventryStatus\"\xd8\x01\n" +
	"\x14FirmwareImageVersion\x122\n" +
	"\n" +
	"target_mcu\x18\x01 \x01(\x0e2\x13.dfu.EProcessorTypeR\ttargetMcu\x12.\n" +
	"\n" +
	"image_type\x18\x02 \x01(\x0e2\x0f.dfu.EImageTypeR\timageType\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\x12#\n" +
	"\rversion_build\x18\x04 \x01(\tR\fversionBuild\x12\x1d\n" +
	"\n" +
	"build_date\x18\x05 \x01(\tR\tbuildDate\"\xac\x01\n" +
	"\x15FirmwareVersionSimple\x122\n" +
	"\n" +
	"target_mcu\x18\x01 \x01(\x0e2\x13.dfu.EProcessorTypeR\ttargetMcu\x120\n" +
	"\aversion\x18\x03 \x01(\v2\x16.basic.VersionStrThreeR\aversion\x12-\n" +
	"\n" +
	"build_date\x18\x05 \x01(\v2\x0e.basic.DateStrR\tbuildDate*\xad\x01\n" +
	"\x0eEProcessorType\x12\x19\n" +
	"\x15PROCESSOR_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0ePROCESSOR_MAIN\x10\x01\x12\x16\n" +
	"\x12PROCESSOR_ISOLATED\x10\x02\x12\x15\n" +
	"\x11PROCESSOR_DISPLAY\x10\x03\x12\x13\n" +
	"\x0fPROCESSOR_COMMS\x10\x04\x12\x11\n" +
	"\rPROCESSOR_BLE\x10\x05\x12\x15\n" +
	"\x11PROCESSOR_PACKAGE\x10\x06*\x9d\x01\n" +
	"\n" +
	"EImageType\x12\x15\n" +
	"\x11IMAGE_UNSPECIFIED\x10\x00\x12\x11\n" +
	"\rIMAGE_MCUBOOT\x10\x01\x12\x15\n" +
	"\x11IMAGE_APPLICATION\x10\x02\x12\x15\n" +
	"\x11IMAGE_SECURE_CODE\x10\x03\x12\x1b\n" +
	"\x17IMAGE_PROTECTED_STORAGE\x10\x04\x12\x1a\n" +
	"\x16IMAGE_BOOT_SECURE_DATA\x10\x05*\xf9\x02\n" +
	"\x15EFirmwareUpdateStatus\x12 \n" +
	"\x1cFW_UPDATE_STATUS_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18FW_UPDATE_STATUS_SUCCESS\x10\x01\x12 \n" +
	"\x1cFW_UPDATE_STATUS_DOWNLOADING\x10\x02\x12\x1c\n" +
	"\x18FW_UPDATE_STATUS_COPYING\x10\x03\x12\x1c\n" +
	"\x18FW_UPDATE_STATUS_PENDING\x10\x04\x12\x1b\n" +
	"\x17FW_UPDATE_STATUS_FAILED\x10\x05\x12%\n" +
	"!FW_UPDATE_STATUS_NOT_POSSIBLE_NOW\x10\x06\x12)\n" +
	"%FW_UPDATE_STATUS_FAILED_INVALID_PARAM\x10\a\x12.\n" +
	"*FW_UPDATE_STATUS_FAILED_NO_SUPPORTED_MODEL\x10\b\x12#\n" +
	"\x1fFW_UPDATE_STATUS_FAILED_TIMEOUT\x10\t*\x7f\n" +
	"\x19EFirmwareVersionsManifest\x12\x1b\n" +
	"\x17FW_MANIFEST_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13FW_MANIFEST_PRESENT\x10\x01\x12\x16\n" +
	"\x12FW_MANIFEST_UPDATE\x10\x02\x12\x14\n" +
	"\x10FW_MANIFEST_NONE\x10\x03*\x81\x03\n" +
	"\x14EManifestEntryStatus\x12\x1f\n" +
	"\x1bFW_IMAGE_STATUS_UNSPECIFIED\x10\x00\x12 \n" +
	"\x1cFW_IMAGE_STATUS_NEEDS_UPDATE\x10\x01\x12\x1d\n" +
	"\x19FW_IMAGE_STATUS_NO_UPDATE\x10\x02\x12\x1e\n" +
	"\x1aFW_IMAGE_STATUS_DOWNLOADED\x10\x03\x12\x1b\n" +
	"\x17FW_IMAGE_STATUS_SUCCESS\x10\x04\x12\x1a\n" +
	"\x16FW_IMAGE_STATUS_FAILED\x10\x05\x12(\n" +
	"$FW_IMAGE_STATUS_FAILED_INVALID_PARAM\x10\x06\x12(\n" +
	"$FW_IMAGE_STATUS_FAILED_IMAGE_INVALID\x10\a\x125\n" +
	"1FW_IMAGE_STATUS_FAILED_IMAGE_VERSION_INCOMPATIBLE\x10\b\x12#\n" +
	"\x1fFW_IMAGE_STATUS_PENDING_RESTART\x10\t*\x9d\x03\n" +
	"\x0eEDfuResultCode\x12\x16\n" +
	"\x12RESULT_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eRESULT_SUCCESS\x10\x01\x12\x18\n" +
	"\x14RESULT_INVALID_PARAM\x10\x02\x12\x1b\n" +
	"\x17RESULT_NOT_POSSIBLE_NOW\x10\x03\x12\x1f\n" +
	"\x1bRESULT_COMMAND_NOT_ACCEPTED\x10\x04\x12\x1b\n" +
	"\x17RESULT_FAILED_AT_DEVICE\x10\x05\x12\x1d\n" +
	"\x19RESULT_FAILED_UNAVAILABLE\x10\x06\x12\x1f\n" +
	"\x1bRESULT_FAILED_MISSING_PARAM\x10\a\x12\x1e\n" +
	"\x1aRESULT_FAILED_OUT_OF_ORDER\x10\b\x12\x1f\n" +
	"\x1bRESULT_FAILED_FILE_IO_ERROR\x10\t\x12\x1c\n" +
	"\x18RESULT_FAILED_COMM_ERROR\x10\n" +
	"\x12'\n" +
	"#RESULT_FAILED_NO_UPDATE_IN_PROGRESS\x10\v\x12\"\n" +
	"\x1eRESULT_FAILED_PROCESSING_ERROR\x10\f*w\n" +
	"\x13EMonitorModelNumber\x12\x1c\n" +
	"\x18MODEL_NUMBER_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1eMODEL_NUMBER_MMU2_16LEIP_RM_SF\x10\x01\x12\x1e\n" +
	"\x1aMODEL_NUMBER_DK_NUCLEO_MMU\x10\x02b\x06proto3"

var (
	file_dfu_proto_rawDescOnce sync.Once
	file_dfu_proto_rawDescData []byte
)

func file_dfu_proto_rawDescGZIP() []byte {
	file_dfu_proto_rawDescOnce.Do(func() {
		file_dfu_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dfu_proto_rawDesc), len(file_dfu_proto_rawDesc)))
	})
	return file_dfu_proto_rawDescData
}

var file_dfu_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_dfu_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_dfu_proto_goTypes = []any{
	(EProcessorType)(0),            // 0: dfu.EProcessorType
	(EImageType)(0),                // 1: dfu.EImageType
	(EFirmwareUpdateStatus)(0),     // 2: dfu.EFirmwareUpdateStatus
	(EFirmwareVersionsManifest)(0), // 3: dfu.EFirmwareVersionsManifest
	(EManifestEntryStatus)(0),      // 4: dfu.EManifestEntryStatus
	(EDfuResultCode)(0),            // 5: dfu.EDfuResultCode
	(EMonitorModelNumber)(0),       // 6: dfu.EMonitorModelNumber
	(*DfuManifestEntry)(nil),       // 7: dfu.DfuManifestEntry
	(*DfuManifestStatusEntry)(nil), // 8: dfu.DfuManifestStatusEntry
	(*FirmwareImageVersion)(nil),   // 9: dfu.FirmwareImageVersion
	(*FirmwareVersionSimple)(nil),  // 10: dfu.FirmwareVersionSimple
	(*basic.VersionStrThree)(nil),  // 11: basic.VersionStrThree
	(*basic.DateStr)(nil),          // 12: basic.DateStr
}
var file_dfu_proto_depIdxs = []int32{
	9,  // 0: dfu.DfuManifestEntry.image_version:type_name -> dfu.FirmwareImageVersion
	4,  // 1: dfu.DfuManifestEntry.entry_status:type_name -> dfu.EManifestEntryStatus
	9,  // 2: dfu.DfuManifestStatusEntry.image_version:type_name -> dfu.FirmwareImageVersion
	4,  // 3: dfu.DfuManifestStatusEntry.entry_status:type_name -> dfu.EManifestEntryStatus
	0,  // 4: dfu.FirmwareImageVersion.target_mcu:type_name -> dfu.EProcessorType
	1,  // 5: dfu.FirmwareImageVersion.image_type:type_name -> dfu.EImageType
	0,  // 6: dfu.FirmwareVersionSimple.target_mcu:type_name -> dfu.EProcessorType
	11, // 7: dfu.FirmwareVersionSimple.version:type_name -> basic.VersionStrThree
	12, // 8: dfu.FirmwareVersionSimple.build_date:type_name -> basic.DateStr
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_dfu_proto_init() }
func file_dfu_proto_init() {
	if File_dfu_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dfu_proto_rawDesc), len(file_dfu_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dfu_proto_goTypes,
		DependencyIndexes: file_dfu_proto_depIdxs,
		EnumInfos:         file_dfu_proto_enumTypes,
		MessageInfos:      file_dfu_proto_msgTypes,
	}.Build()
	File_dfu_proto = out.File
	file_dfu_proto_goTypes = nil
	file_dfu_proto_depIdxs = nil
}
