//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  MON_FAULTS
//Enumerations and definitions for monitor faults.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: mon_faults.proto

package mon_faults

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//	ENUM EFaultCode defines possible fault sources.  This is a bitmap in the firmware that is converted
//
// to an enumerated type as only one fault code may be sent at a time, with the exception of a Diagnostic
// Fault.  Therefore, a field of this type is usually followed by a field for the Diagnostic fault.
// Some values only apply to certain monitors.
type EFaultCode int32

const (
	EFaultCode_FAULT_CODE_UNSPECIFIED               EFaultCode = 0
	EFaultCode_FAULT_CODE_SERIAL_PORT1              EFaultCode = 1
	EFaultCode_FAULT_CODE_MONITOR_MAINS             EFaultCode = 3
	EFaultCode_FAULT_CODE_FLASH                     EFaultCode = 5
	EFaultCode_FAULT_CODE_CONTROLLER_VOLTAGE        EFaultCode = 9
	EFaultCode_FAULT_CODE_24V_MONITOR_1             EFaultCode = 10
	EFaultCode_FAULT_CODE_24V_MONITOR_2             EFaultCode = 11
	EFaultCode_FAULT_CODE_CONFLICT                  EFaultCode = 12
	EFaultCode_FAULT_CODE_MULTIPLE_INDICATION       EFaultCode = 14
	EFaultCode_FAULT_CODE_EXTERNAL_WATCHDOG         EFaultCode = 15
	EFaultCode_FAULT_CODE_LACK_OF_SIGNAL            EFaultCode = 16
	EFaultCode_FAULT_CODE_MINIMUM_Y_CLEARANCE       EFaultCode = 17 // Yellow
	EFaultCode_FAULT_CODE_SKIPPED_Y_CLEARANCE       EFaultCode = 18 // Yellow
	EFaultCode_FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE EFaultCode = 19 // Yellow and Red
	EFaultCode_FAULT_CODE_FIELD_CHECK               EFaultCode = 20
	EFaultCode_FAULT_CODE_FLASHING_Y_ARROW          EFaultCode = 21 // Yellow
	EFaultCode_FAULT_CODE_DATAKEY_DATA              EFaultCode = 23
	EFaultCode_FAULT_CODE_DATAKEY_ABSENT            EFaultCode = 24
	EFaultCode_FAULT_CODE_MULTIPLE_FAULTS           EFaultCode = 100 // More than one fault, other than a diag fault is set in mains
)

// Enum value maps for EFaultCode.
var (
	EFaultCode_name = map[int32]string{
		0:   "FAULT_CODE_UNSPECIFIED",
		1:   "FAULT_CODE_SERIAL_PORT1",
		3:   "FAULT_CODE_MONITOR_MAINS",
		5:   "FAULT_CODE_FLASH",
		9:   "FAULT_CODE_CONTROLLER_VOLTAGE",
		10:  "FAULT_CODE_24V_MONITOR_1",
		11:  "FAULT_CODE_24V_MONITOR_2",
		12:  "FAULT_CODE_CONFLICT",
		14:  "FAULT_CODE_MULTIPLE_INDICATION",
		15:  "FAULT_CODE_EXTERNAL_WATCHDOG",
		16:  "FAULT_CODE_LACK_OF_SIGNAL",
		17:  "FAULT_CODE_MINIMUM_Y_CLEARANCE",
		18:  "FAULT_CODE_SKIPPED_Y_CLEARANCE",
		19:  "FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE",
		20:  "FAULT_CODE_FIELD_CHECK",
		21:  "FAULT_CODE_FLASHING_Y_ARROW",
		23:  "FAULT_CODE_DATAKEY_DATA",
		24:  "FAULT_CODE_DATAKEY_ABSENT",
		100: "FAULT_CODE_MULTIPLE_FAULTS",
	}
	EFaultCode_value = map[string]int32{
		"FAULT_CODE_UNSPECIFIED":               0,
		"FAULT_CODE_SERIAL_PORT1":              1,
		"FAULT_CODE_MONITOR_MAINS":             3,
		"FAULT_CODE_FLASH":                     5,
		"FAULT_CODE_CONTROLLER_VOLTAGE":        9,
		"FAULT_CODE_24V_MONITOR_1":             10,
		"FAULT_CODE_24V_MONITOR_2":             11,
		"FAULT_CODE_CONFLICT":                  12,
		"FAULT_CODE_MULTIPLE_INDICATION":       14,
		"FAULT_CODE_EXTERNAL_WATCHDOG":         15,
		"FAULT_CODE_LACK_OF_SIGNAL":            16,
		"FAULT_CODE_MINIMUM_Y_CLEARANCE":       17,
		"FAULT_CODE_SKIPPED_Y_CLEARANCE":       18,
		"FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE": 19,
		"FAULT_CODE_FIELD_CHECK":               20,
		"FAULT_CODE_FLASHING_Y_ARROW":          21,
		"FAULT_CODE_DATAKEY_DATA":              23,
		"FAULT_CODE_DATAKEY_ABSENT":            24,
		"FAULT_CODE_MULTIPLE_FAULTS":           100,
	}
)

func (x EFaultCode) Enum() *EFaultCode {
	p := new(EFaultCode)
	*p = x
	return p
}

func (x EFaultCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EFaultCode) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_faults_proto_enumTypes[0].Descriptor()
}

func (EFaultCode) Type() protoreflect.EnumType {
	return &file_mon_faults_proto_enumTypes[0]
}

func (x EFaultCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EFaultCode.Descriptor instead.
func (EFaultCode) EnumDescriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{0}
}

// ENUM SubFaultSerialPort provides additional information for FAULT_CODE_SERIAL_PORT1.
type ESubFaultSerialPort int32

const (
	ESubFaultSerialPort_SUBFLT_SERIAL_UNSPECIFIED ESubFaultSerialPort = 0
	ESubFaultSerialPort_SUBFLT_SERIAL_TIMEOUT     ESubFaultSerialPort = 1
	ESubFaultSerialPort_SUBFLT_SERIAL_QUALITY     ESubFaultSerialPort = 2
	ESubFaultSerialPort_SUBFLT_SERIAL_RESET       ESubFaultSerialPort = 3
)

// Enum value maps for ESubFaultSerialPort.
var (
	ESubFaultSerialPort_name = map[int32]string{
		0: "SUBFLT_SERIAL_UNSPECIFIED",
		1: "SUBFLT_SERIAL_TIMEOUT",
		2: "SUBFLT_SERIAL_QUALITY",
		3: "SUBFLT_SERIAL_RESET",
	}
	ESubFaultSerialPort_value = map[string]int32{
		"SUBFLT_SERIAL_UNSPECIFIED": 0,
		"SUBFLT_SERIAL_TIMEOUT":     1,
		"SUBFLT_SERIAL_QUALITY":     2,
		"SUBFLT_SERIAL_RESET":       3,
	}
)

func (x ESubFaultSerialPort) Enum() *ESubFaultSerialPort {
	p := new(ESubFaultSerialPort)
	*p = x
	return p
}

func (x ESubFaultSerialPort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESubFaultSerialPort) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_faults_proto_enumTypes[1].Descriptor()
}

func (ESubFaultSerialPort) Type() protoreflect.EnumType {
	return &file_mon_faults_proto_enumTypes[1]
}

func (x ESubFaultSerialPort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ESubFaultSerialPort.Descriptor instead.
func (ESubFaultSerialPort) EnumDescriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{1}
}

// ENUM ESubFaultMultipleIndication provides additional information for FAULT_CODE_MULTIPLE_INDICATION.
type ESubFaultMultipleIndication int32

const (
	ESubFaultMultipleIndication_SUBFLT_MULTIND_UNSPECIFIED      ESubFaultMultipleIndication = 0
	ESubFaultMultipleIndication_SUBFLT_MULTIND_GREEN_YELLOW     ESubFaultMultipleIndication = 1
	ESubFaultMultipleIndication_SUBFLT_MULTIND_GREEN_RED        ESubFaultMultipleIndication = 2
	ESubFaultMultipleIndication_SUBFLT_MULTIND_YELLOW_RED       ESubFaultMultipleIndication = 3
	ESubFaultMultipleIndication_SUBFLT_MULTIND_MULTIPLE         ESubFaultMultipleIndication = 4
	ESubFaultMultipleIndication_SUBFLT_MULTIND_FLASHING_Y_ARROW ESubFaultMultipleIndication = 5
)

// Enum value maps for ESubFaultMultipleIndication.
var (
	ESubFaultMultipleIndication_name = map[int32]string{
		0: "SUBFLT_MULTIND_UNSPECIFIED",
		1: "SUBFLT_MULTIND_GREEN_YELLOW",
		2: "SUBFLT_MULTIND_GREEN_RED",
		3: "SUBFLT_MULTIND_YELLOW_RED",
		4: "SUBFLT_MULTIND_MULTIPLE",
		5: "SUBFLT_MULTIND_FLASHING_Y_ARROW",
	}
	ESubFaultMultipleIndication_value = map[string]int32{
		"SUBFLT_MULTIND_UNSPECIFIED":      0,
		"SUBFLT_MULTIND_GREEN_YELLOW":     1,
		"SUBFLT_MULTIND_GREEN_RED":        2,
		"SUBFLT_MULTIND_YELLOW_RED":       3,
		"SUBFLT_MULTIND_MULTIPLE":         4,
		"SUBFLT_MULTIND_FLASHING_Y_ARROW": 5,
	}
)

func (x ESubFaultMultipleIndication) Enum() *ESubFaultMultipleIndication {
	p := new(ESubFaultMultipleIndication)
	*p = x
	return p
}

func (x ESubFaultMultipleIndication) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESubFaultMultipleIndication) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_faults_proto_enumTypes[2].Descriptor()
}

func (ESubFaultMultipleIndication) Type() protoreflect.EnumType {
	return &file_mon_faults_proto_enumTypes[2]
}

func (x ESubFaultMultipleIndication) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ESubFaultMultipleIndication.Descriptor instead.
func (ESubFaultMultipleIndication) EnumDescriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{2}
}

// ENUM ESubFaultLackOfSignal provides additional information for FAULT_CODE_LACK_OF_SIGNAL.
type ESubFaultLackOfSignal int32

const (
	ESubFaultLackOfSignal_SUBFLT_LACKOFSIG_UNSPECIFIED      ESubFaultLackOfSignal = 0
	ESubFaultLackOfSignal_SUBFLT_LACKOFSIG_NORMAL           ESubFaultLackOfSignal = 1
	ESubFaultLackOfSignal_SUBFLT_LACKOFSIG_FLASHING_Y_ARROW ESubFaultLackOfSignal = 2
)

// Enum value maps for ESubFaultLackOfSignal.
var (
	ESubFaultLackOfSignal_name = map[int32]string{
		0: "SUBFLT_LACKOFSIG_UNSPECIFIED",
		1: "SUBFLT_LACKOFSIG_NORMAL",
		2: "SUBFLT_LACKOFSIG_FLASHING_Y_ARROW",
	}
	ESubFaultLackOfSignal_value = map[string]int32{
		"SUBFLT_LACKOFSIG_UNSPECIFIED":      0,
		"SUBFLT_LACKOFSIG_NORMAL":           1,
		"SUBFLT_LACKOFSIG_FLASHING_Y_ARROW": 2,
	}
)

func (x ESubFaultLackOfSignal) Enum() *ESubFaultLackOfSignal {
	p := new(ESubFaultLackOfSignal)
	*p = x
	return p
}

func (x ESubFaultLackOfSignal) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESubFaultLackOfSignal) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_faults_proto_enumTypes[3].Descriptor()
}

func (ESubFaultLackOfSignal) Type() protoreflect.EnumType {
	return &file_mon_faults_proto_enumTypes[3]
}

func (x ESubFaultLackOfSignal) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ESubFaultLackOfSignal.Descriptor instead.
func (ESubFaultLackOfSignal) EnumDescriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{3}
}

// ENUM ESubFaultSkippedYellow provides additional information for FAULT_CODE_SKIPPED_Y_CLEARANCE.
type ESubFaultSkippedYellow int32

const (
	ESubFaultSkippedYellow_SUBFLT_SKIPPEDY_UNSPECIFIED    ESubFaultSkippedYellow = 0
	ESubFaultSkippedYellow_SUBFLT_SKIPPEDY_STANDARD       ESubFaultSkippedYellow = 1
	ESubFaultSkippedYellow_SUBFLT_SKIPPEDY_FYA_FLASHING_Y ESubFaultSkippedYellow = 2 // Flashing Yellow Arrow, Flashing Yellow
	ESubFaultSkippedYellow_SUBFLT_SKIPPEDY_FYA_GREEN      ESubFaultSkippedYellow = 3 // Flashing Yellow Arrow
)

// Enum value maps for ESubFaultSkippedYellow.
var (
	ESubFaultSkippedYellow_name = map[int32]string{
		0: "SUBFLT_SKIPPEDY_UNSPECIFIED",
		1: "SUBFLT_SKIPPEDY_STANDARD",
		2: "SUBFLT_SKIPPEDY_FYA_FLASHING_Y",
		3: "SUBFLT_SKIPPEDY_FYA_GREEN",
	}
	ESubFaultSkippedYellow_value = map[string]int32{
		"SUBFLT_SKIPPEDY_UNSPECIFIED":    0,
		"SUBFLT_SKIPPEDY_STANDARD":       1,
		"SUBFLT_SKIPPEDY_FYA_FLASHING_Y": 2,
		"SUBFLT_SKIPPEDY_FYA_GREEN":      3,
	}
)

func (x ESubFaultSkippedYellow) Enum() *ESubFaultSkippedYellow {
	p := new(ESubFaultSkippedYellow)
	*p = x
	return p
}

func (x ESubFaultSkippedYellow) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESubFaultSkippedYellow) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_faults_proto_enumTypes[4].Descriptor()
}

func (ESubFaultSkippedYellow) Type() protoreflect.EnumType {
	return &file_mon_faults_proto_enumTypes[4]
}

func (x ESubFaultSkippedYellow) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ESubFaultSkippedYellow.Descriptor instead.
func (ESubFaultSkippedYellow) EnumDescriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{4}
}

// ENUM ESubFaultDataKey provides additional information for FAULT_CODE_DATAKEY_DATA.
type ESubFaultDataKey int32

const (
	ESubFaultDataKey_SUBFLT_DATAKEY_UNSPECIFIED   ESubFaultDataKey = 0
	ESubFaultDataKey_SUBFLT_DATAKEY_CRC_ERROR     ESubFaultDataKey = 1
	ESubFaultDataKey_SUBFLT_DATAKEY_FORMAT_ERROR  ESubFaultDataKey = 2
	ESubFaultDataKey_SUBFLT_DATAKEY_DATA_ERROR    ESubFaultDataKey = 3
	ESubFaultDataKey_SUBFLT_DATAKEY_RAM_CRC_ERROR ESubFaultDataKey = 4
)

// Enum value maps for ESubFaultDataKey.
var (
	ESubFaultDataKey_name = map[int32]string{
		0: "SUBFLT_DATAKEY_UNSPECIFIED",
		1: "SUBFLT_DATAKEY_CRC_ERROR",
		2: "SUBFLT_DATAKEY_FORMAT_ERROR",
		3: "SUBFLT_DATAKEY_DATA_ERROR",
		4: "SUBFLT_DATAKEY_RAM_CRC_ERROR",
	}
	ESubFaultDataKey_value = map[string]int32{
		"SUBFLT_DATAKEY_UNSPECIFIED":   0,
		"SUBFLT_DATAKEY_CRC_ERROR":     1,
		"SUBFLT_DATAKEY_FORMAT_ERROR":  2,
		"SUBFLT_DATAKEY_DATA_ERROR":    3,
		"SUBFLT_DATAKEY_RAM_CRC_ERROR": 4,
	}
)

func (x ESubFaultDataKey) Enum() *ESubFaultDataKey {
	p := new(ESubFaultDataKey)
	*p = x
	return p
}

func (x ESubFaultDataKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESubFaultDataKey) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_faults_proto_enumTypes[5].Descriptor()
}

func (ESubFaultDataKey) Type() protoreflect.EnumType {
	return &file_mon_faults_proto_enumTypes[5]
}

func (x ESubFaultDataKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ESubFaultDataKey.Descriptor instead.
func (ESubFaultDataKey) EnumDescriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{5}
}

// ENUM ESubFaultDiagnostic provides additional information for diagnostic errors.
type ESubFaultDiagnostic int32

const (
	ESubFaultDiagnostic_SUBFLT_DIAG_UNSPECIFIED ESubFaultDiagnostic = 0
	ESubFaultDiagnostic_SUBFLT_DIAG_1           ESubFaultDiagnostic = 1 // TODO rename these for the actual errors
	ESubFaultDiagnostic_SUBFLT_DIAG_2           ESubFaultDiagnostic = 2
	ESubFaultDiagnostic_SUBFLT_DIAG_3           ESubFaultDiagnostic = 3
	ESubFaultDiagnostic_SUBFLT_DIAG_4           ESubFaultDiagnostic = 4
)

// Enum value maps for ESubFaultDiagnostic.
var (
	ESubFaultDiagnostic_name = map[int32]string{
		0: "SUBFLT_DIAG_UNSPECIFIED",
		1: "SUBFLT_DIAG_1",
		2: "SUBFLT_DIAG_2",
		3: "SUBFLT_DIAG_3",
		4: "SUBFLT_DIAG_4",
	}
	ESubFaultDiagnostic_value = map[string]int32{
		"SUBFLT_DIAG_UNSPECIFIED": 0,
		"SUBFLT_DIAG_1":           1,
		"SUBFLT_DIAG_2":           2,
		"SUBFLT_DIAG_3":           3,
		"SUBFLT_DIAG_4":           4,
	}
)

func (x ESubFaultDiagnostic) Enum() *ESubFaultDiagnostic {
	p := new(ESubFaultDiagnostic)
	*p = x
	return p
}

func (x ESubFaultDiagnostic) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ESubFaultDiagnostic) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_faults_proto_enumTypes[6].Descriptor()
}

func (ESubFaultDiagnostic) Type() protoreflect.EnumType {
	return &file_mon_faults_proto_enumTypes[6]
}

func (x ESubFaultDiagnostic) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ESubFaultDiagnostic.Descriptor instead.
func (ESubFaultDiagnostic) EnumDescriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{6}
}

// MmuSubFaultTypeValue defines a message with the possible fault subcodes.
type MmuSubFaultTypeValue struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to SubcodeType:
	//
	//	*MmuSubFaultTypeValue_SerialPort
	//	*MmuSubFaultTypeValue_MultipleIndication
	//	*MmuSubFaultTypeValue_LackOfSignal
	//	*MmuSubFaultTypeValue_SkippedYellow
	//	*MmuSubFaultTypeValue_DataKey
	SubcodeType   isMmuSubFaultTypeValue_SubcodeType `protobuf_oneof:"subcode_type"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MmuSubFaultTypeValue) Reset() {
	*x = MmuSubFaultTypeValue{}
	mi := &file_mon_faults_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MmuSubFaultTypeValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MmuSubFaultTypeValue) ProtoMessage() {}

func (x *MmuSubFaultTypeValue) ProtoReflect() protoreflect.Message {
	mi := &file_mon_faults_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MmuSubFaultTypeValue.ProtoReflect.Descriptor instead.
func (*MmuSubFaultTypeValue) Descriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{0}
}

func (x *MmuSubFaultTypeValue) GetSubcodeType() isMmuSubFaultTypeValue_SubcodeType {
	if x != nil {
		return x.SubcodeType
	}
	return nil
}

func (x *MmuSubFaultTypeValue) GetSerialPort() ESubFaultSerialPort {
	if x != nil {
		if x, ok := x.SubcodeType.(*MmuSubFaultTypeValue_SerialPort); ok {
			return x.SerialPort
		}
	}
	return ESubFaultSerialPort_SUBFLT_SERIAL_UNSPECIFIED
}

func (x *MmuSubFaultTypeValue) GetMultipleIndication() ESubFaultMultipleIndication {
	if x != nil {
		if x, ok := x.SubcodeType.(*MmuSubFaultTypeValue_MultipleIndication); ok {
			return x.MultipleIndication
		}
	}
	return ESubFaultMultipleIndication_SUBFLT_MULTIND_UNSPECIFIED
}

func (x *MmuSubFaultTypeValue) GetLackOfSignal() ESubFaultLackOfSignal {
	if x != nil {
		if x, ok := x.SubcodeType.(*MmuSubFaultTypeValue_LackOfSignal); ok {
			return x.LackOfSignal
		}
	}
	return ESubFaultLackOfSignal_SUBFLT_LACKOFSIG_UNSPECIFIED
}

func (x *MmuSubFaultTypeValue) GetSkippedYellow() ESubFaultSkippedYellow {
	if x != nil {
		if x, ok := x.SubcodeType.(*MmuSubFaultTypeValue_SkippedYellow); ok {
			return x.SkippedYellow
		}
	}
	return ESubFaultSkippedYellow_SUBFLT_SKIPPEDY_UNSPECIFIED
}

func (x *MmuSubFaultTypeValue) GetDataKey() ESubFaultDataKey {
	if x != nil {
		if x, ok := x.SubcodeType.(*MmuSubFaultTypeValue_DataKey); ok {
			return x.DataKey
		}
	}
	return ESubFaultDataKey_SUBFLT_DATAKEY_UNSPECIFIED
}

type isMmuSubFaultTypeValue_SubcodeType interface {
	isMmuSubFaultTypeValue_SubcodeType()
}

type MmuSubFaultTypeValue_SerialPort struct {
	SerialPort ESubFaultSerialPort `protobuf:"varint,1,opt,name=serial_port,json=serialPort,proto3,enum=mon_faults.ESubFaultSerialPort,oneof"`
}

type MmuSubFaultTypeValue_MultipleIndication struct {
	MultipleIndication ESubFaultMultipleIndication `protobuf:"varint,2,opt,name=multiple_indication,json=multipleIndication,proto3,enum=mon_faults.ESubFaultMultipleIndication,oneof"`
}

type MmuSubFaultTypeValue_LackOfSignal struct {
	LackOfSignal ESubFaultLackOfSignal `protobuf:"varint,3,opt,name=lack_of_signal,json=lackOfSignal,proto3,enum=mon_faults.ESubFaultLackOfSignal,oneof"`
}

type MmuSubFaultTypeValue_SkippedYellow struct {
	SkippedYellow ESubFaultSkippedYellow `protobuf:"varint,4,opt,name=skipped_yellow,json=skippedYellow,proto3,enum=mon_faults.ESubFaultSkippedYellow,oneof"`
}

type MmuSubFaultTypeValue_DataKey struct {
	DataKey ESubFaultDataKey `protobuf:"varint,5,opt,name=data_key,json=dataKey,proto3,enum=mon_faults.ESubFaultDataKey,oneof"`
}

func (*MmuSubFaultTypeValue_SerialPort) isMmuSubFaultTypeValue_SubcodeType() {}

func (*MmuSubFaultTypeValue_MultipleIndication) isMmuSubFaultTypeValue_SubcodeType() {}

func (*MmuSubFaultTypeValue_LackOfSignal) isMmuSubFaultTypeValue_SubcodeType() {}

func (*MmuSubFaultTypeValue_SkippedYellow) isMmuSubFaultTypeValue_SubcodeType() {}

func (*MmuSubFaultTypeValue_DataKey) isMmuSubFaultTypeValue_SubcodeType() {}

//	MmuMonitoredControlStatesBitmap gives the states of various control states.
//
// This matches the bitmap in a Fault Sequence Log Entry field "Controls"
type MmuMonitoredControlStatesBitmap struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	StartDelayRelay  bool                   `protobuf:"varint,4,opt,name=start_delay_relay,json=startDelayRelay,proto3" json:"start_delay_relay,omitempty"`    // True if the start delay relay is energized
	ResetEvent       bool                   `protobuf:"varint,5,opt,name=reset_event,json=resetEvent,proto3" json:"reset_event,omitempty"`                     // True if reset bit is set in SDLC frame (bit 61 frame 129)
	StartupFlashCall bool                   `protobuf:"varint,6,opt,name=startup_flash_call,json=startupFlashCall,proto3" json:"startup_flash_call,omitempty"` // True if startup flash bit is set in SDLC frame (bit 80 frame 129)
	AcLineValid      bool                   `protobuf:"varint,7,opt,name=ac_line_valid,json=acLineValid,proto3" json:"ac_line_valid,omitempty"`                // True if the input AC power (mains) line is valid
	FaultRelay       bool                   `protobuf:"varint,8,opt,name=fault_relay,json=faultRelay,proto3" json:"fault_relay,omitempty"`                     // True if the fault relay is energized
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MmuMonitoredControlStatesBitmap) Reset() {
	*x = MmuMonitoredControlStatesBitmap{}
	mi := &file_mon_faults_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MmuMonitoredControlStatesBitmap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MmuMonitoredControlStatesBitmap) ProtoMessage() {}

func (x *MmuMonitoredControlStatesBitmap) ProtoReflect() protoreflect.Message {
	mi := &file_mon_faults_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MmuMonitoredControlStatesBitmap.ProtoReflect.Descriptor instead.
func (*MmuMonitoredControlStatesBitmap) Descriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{1}
}

func (x *MmuMonitoredControlStatesBitmap) GetStartDelayRelay() bool {
	if x != nil {
		return x.StartDelayRelay
	}
	return false
}

func (x *MmuMonitoredControlStatesBitmap) GetResetEvent() bool {
	if x != nil {
		return x.ResetEvent
	}
	return false
}

func (x *MmuMonitoredControlStatesBitmap) GetStartupFlashCall() bool {
	if x != nil {
		return x.StartupFlashCall
	}
	return false
}

func (x *MmuMonitoredControlStatesBitmap) GetAcLineValid() bool {
	if x != nil {
		return x.AcLineValid
	}
	return false
}

func (x *MmuMonitoredControlStatesBitmap) GetFaultRelay() bool {
	if x != nil {
		return x.FaultRelay
	}
	return false
}

//	MmuMonitoredInputsStatusBitmap gives the status on monitored inputs. This matches the bitmap in
//
// a Fault Sequence Log Entry field "Inputs"
type MmuMonitoredInputsStatusBitmap struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Monitor_24VInhibit bool                   `protobuf:"varint,1,opt,name=monitor_24v_inhibit,json=monitor24vInhibit,proto3" json:"monitor_24v_inhibit,omitempty"` // TRUE means 24V is NOT being monitored
	// for the remaining fields, TRUE means status "OK"
	Monitor_24V_1       bool `protobuf:"varint,2,opt,name=monitor_24v_1,json=monitor24v1,proto3" json:"monitor_24v_1,omitempty"`
	Monitor_24V_2       bool `protobuf:"varint,3,opt,name=monitor_24v_2,json=monitor24v2,proto3" json:"monitor_24v_2,omitempty"`
	ControllerVoltage   bool `protobuf:"varint,4,opt,name=controller_voltage,json=controllerVoltage,proto3" json:"controller_voltage,omitempty"`
	TypeSelect          bool `protobuf:"varint,5,opt,name=type_select,json=typeSelect,proto3" json:"type_select,omitempty"`
	RedEnable           bool `protobuf:"varint,6,opt,name=red_enable,json=redEnable,proto3" json:"red_enable,omitempty"`
	ExternalReset       bool `protobuf:"varint,7,opt,name=external_reset,json=externalReset,proto3" json:"external_reset,omitempty"`
	Port1Disable        bool `protobuf:"varint,8,opt,name=port1_disable,json=port1Disable,proto3" json:"port1_disable,omitempty"`
	ProgramCardInserted bool `protobuf:"varint,9,opt,name=program_card_inserted,json=programCardInserted,proto3" json:"program_card_inserted,omitempty"`
	LocalFlash          bool `protobuf:"varint,10,opt,name=local_flash,json=localFlash,proto3" json:"local_flash,omitempty"`
	ExternalWatchdog    bool `protobuf:"varint,11,opt,name=external_watchdog,json=externalWatchdog,proto3" json:"external_watchdog,omitempty"`
	Alarm               bool `protobuf:"varint,12,opt,name=alarm,proto3" json:"alarm,omitempty"` // True if an alarm is active
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MmuMonitoredInputsStatusBitmap) Reset() {
	*x = MmuMonitoredInputsStatusBitmap{}
	mi := &file_mon_faults_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MmuMonitoredInputsStatusBitmap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MmuMonitoredInputsStatusBitmap) ProtoMessage() {}

func (x *MmuMonitoredInputsStatusBitmap) ProtoReflect() protoreflect.Message {
	mi := &file_mon_faults_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MmuMonitoredInputsStatusBitmap.ProtoReflect.Descriptor instead.
func (*MmuMonitoredInputsStatusBitmap) Descriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{2}
}

func (x *MmuMonitoredInputsStatusBitmap) GetMonitor_24VInhibit() bool {
	if x != nil {
		return x.Monitor_24VInhibit
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetMonitor_24V_1() bool {
	if x != nil {
		return x.Monitor_24V_1
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetMonitor_24V_2() bool {
	if x != nil {
		return x.Monitor_24V_2
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetControllerVoltage() bool {
	if x != nil {
		return x.ControllerVoltage
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetTypeSelect() bool {
	if x != nil {
		return x.TypeSelect
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetRedEnable() bool {
	if x != nil {
		return x.RedEnable
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetExternalReset() bool {
	if x != nil {
		return x.ExternalReset
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetPort1Disable() bool {
	if x != nil {
		return x.Port1Disable
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetProgramCardInserted() bool {
	if x != nil {
		return x.ProgramCardInserted
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetLocalFlash() bool {
	if x != nil {
		return x.LocalFlash
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetExternalWatchdog() bool {
	if x != nil {
		return x.ExternalWatchdog
	}
	return false
}

func (x *MmuMonitoredInputsStatusBitmap) GetAlarm() bool {
	if x != nil {
		return x.Alarm
	}
	return false
}

// FaultIndicationChVoltCurrent represents one set of voltage and current measurements for an indicator channel.
type FaultIndicationChVoltCurrent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The channel number
	Channel uint32 `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"`
	// Indicator voltage in Volts
	VoltageV float32 `protobuf:"fixed32,2,opt,name=voltage_v,json=voltageV,proto3" json:"voltage_v,omitempty"`
	// Indicator current in Amps, if provided.
	// In an MMU, this is not built-in, but can be provided by an LSU (Load Sensing Unit).
	CurrentA      *float32 `protobuf:"fixed32,3,opt,name=current_a,json=currentA,proto3,oneof" json:"current_a,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaultIndicationChVoltCurrent) Reset() {
	*x = FaultIndicationChVoltCurrent{}
	mi := &file_mon_faults_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultIndicationChVoltCurrent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultIndicationChVoltCurrent) ProtoMessage() {}

func (x *FaultIndicationChVoltCurrent) ProtoReflect() protoreflect.Message {
	mi := &file_mon_faults_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultIndicationChVoltCurrent.ProtoReflect.Descriptor instead.
func (*FaultIndicationChVoltCurrent) Descriptor() ([]byte, []int) {
	return file_mon_faults_proto_rawDescGZIP(), []int{3}
}

func (x *FaultIndicationChVoltCurrent) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *FaultIndicationChVoltCurrent) GetVoltageV() float32 {
	if x != nil {
		return x.VoltageV
	}
	return 0
}

func (x *FaultIndicationChVoltCurrent) GetCurrentA() float32 {
	if x != nil && x.CurrentA != nil {
		return *x.CurrentA
	}
	return 0
}

var File_mon_faults_proto protoreflect.FileDescriptor

const file_mon_faults_proto_rawDesc = "" +
	"\n" +
	"\x10mon_faults.proto\x12\n" +
	"mon_faults\"\x99\x03\n" +
	"\x14MmuSubFaultTypeValue\x12B\n" +
	"\vserial_port\x18\x01 \x01(\x0e2\x1f.mon_faults.ESubFaultSerialPortH\x00R\n" +
	"serialPort\x12Z\n" +
	"\x13multiple_indication\x18\x02 \x01(\x0e2'.mon_faults.ESubFaultMultipleIndicationH\x00R\x12multipleIndication\x12I\n" +
	"\x0elack_of_signal\x18\x03 \x01(\x0e2!.mon_faults.ESubFaultLackOfSignalH\x00R\flackOfSignal\x12K\n" +
	"\x0eskipped_yellow\x18\x04 \x01(\x0e2\".mon_faults.ESubFaultSkippedYellowH\x00R\rskippedYellow\x129\n" +
	"\bdata_key\x18\x05 \x01(\x0e2\x1c.mon_faults.ESubFaultDataKeyH\x00R\adataKeyB\x0e\n" +
	"\fsubcode_type\"\xe7\x01\n" +
	"\x1fMmuMonitoredControlStatesBitmap\x12*\n" +
	"\x11start_delay_relay\x18\x04 \x01(\bR\x0fstartDelayRelay\x12\x1f\n" +
	"\vreset_event\x18\x05 \x01(\bR\n" +
	"resetEvent\x12,\n" +
	"\x12startup_flash_call\x18\x06 \x01(\bR\x10startupFlashCall\x12\"\n" +
	"\rac_line_valid\x18\a \x01(\bR\vacLineValid\x12\x1f\n" +
	"\vfault_relay\x18\b \x01(\bR\n" +
	"faultRelayJ\x04\b\x01\x10\x04\"\xeb\x03\n" +
	"\x1eMmuMonitoredInputsStatusBitmap\x12.\n" +
	"\x13monitor_24v_inhibit\x18\x01 \x01(\bR\x11monitor24vInhibit\x12\"\n" +
	"\rmonitor_24v_1\x18\x02 \x01(\bR\vmonitor24v1\x12\"\n" +
	"\rmonitor_24v_2\x18\x03 \x01(\bR\vmonitor24v2\x12-\n" +
	"\x12controller_voltage\x18\x04 \x01(\bR\x11controllerVoltage\x12\x1f\n" +
	"\vtype_select\x18\x05 \x01(\bR\n" +
	"typeSelect\x12\x1d\n" +
	"\n" +
	"red_enable\x18\x06 \x01(\bR\tredEnable\x12%\n" +
	"\x0eexternal_reset\x18\a \x01(\bR\rexternalReset\x12#\n" +
	"\rport1_disable\x18\b \x01(\bR\fport1Disable\x122\n" +
	"\x15program_card_inserted\x18\t \x01(\bR\x13programCardInserted\x12\x1f\n" +
	"\vlocal_flash\x18\n" +
	" \x01(\bR\n" +
	"localFlash\x12+\n" +
	"\x11external_watchdog\x18\v \x01(\bR\x10externalWatchdog\x12\x14\n" +
	"\x05alarm\x18\f \x01(\bR\x05alarm\"\x85\x01\n" +
	"\x1cFaultIndicationChVoltCurrent\x12\x18\n" +
	"\achannel\x18\x01 \x01(\rR\achannel\x12\x1b\n" +
	"\tvoltage_v\x18\x02 \x01(\x02R\bvoltageV\x12 \n" +
	"\tcurrent_a\x18\x03 \x01(\x02H\x00R\bcurrentA\x88\x01\x01B\f\n" +
	"\n" +
	"_current_a*\xe1\x04\n" +
	"\n" +
	"EFaultCode\x12\x1a\n" +
	"\x16FAULT_CODE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17FAULT_CODE_SERIAL_PORT1\x10\x01\x12\x1c\n" +
	"\x18FAULT_CODE_MONITOR_MAINS\x10\x03\x12\x14\n" +
	"\x10FAULT_CODE_FLASH\x10\x05\x12!\n" +
	"\x1dFAULT_CODE_CONTROLLER_VOLTAGE\x10\t\x12\x1c\n" +
	"\x18FAULT_CODE_24V_MONITOR_1\x10\n" +
	"\x12\x1c\n" +
	"\x18FAULT_CODE_24V_MONITOR_2\x10\v\x12\x17\n" +
	"\x13FAULT_CODE_CONFLICT\x10\f\x12\"\n" +
	"\x1eFAULT_CODE_MULTIPLE_INDICATION\x10\x0e\x12 \n" +
	"\x1cFAULT_CODE_EXTERNAL_WATCHDOG\x10\x0f\x12\x1d\n" +
	"\x19FAULT_CODE_LACK_OF_SIGNAL\x10\x10\x12\"\n" +
	"\x1eFAULT_CODE_MINIMUM_Y_CLEARANCE\x10\x11\x12\"\n" +
	"\x1eFAULT_CODE_SKIPPED_Y_CLEARANCE\x10\x12\x12(\n" +
	"$FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE\x10\x13\x12\x1a\n" +
	"\x16FAULT_CODE_FIELD_CHECK\x10\x14\x12\x1f\n" +
	"\x1bFAULT_CODE_FLASHING_Y_ARROW\x10\x15\x12\x1b\n" +
	"\x17FAULT_CODE_DATAKEY_DATA\x10\x17\x12\x1d\n" +
	"\x19FAULT_CODE_DATAKEY_ABSENT\x10\x18\x12\x1e\n" +
	"\x1aFAULT_CODE_MULTIPLE_FAULTS\x10d*\x83\x01\n" +
	"\x13ESubFaultSerialPort\x12\x1d\n" +
	"\x19SUBFLT_SERIAL_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15SUBFLT_SERIAL_TIMEOUT\x10\x01\x12\x19\n" +
	"\x15SUBFLT_SERIAL_QUALITY\x10\x02\x12\x17\n" +
	"\x13SUBFLT_SERIAL_RESET\x10\x03*\xdd\x01\n" +
	"\x1bESubFaultMultipleIndication\x12\x1e\n" +
	"\x1aSUBFLT_MULTIND_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bSUBFLT_MULTIND_GREEN_YELLOW\x10\x01\x12\x1c\n" +
	"\x18SUBFLT_MULTIND_GREEN_RED\x10\x02\x12\x1d\n" +
	"\x19SUBFLT_MULTIND_YELLOW_RED\x10\x03\x12\x1b\n" +
	"\x17SUBFLT_MULTIND_MULTIPLE\x10\x04\x12#\n" +
	"\x1fSUBFLT_MULTIND_FLASHING_Y_ARROW\x10\x05*}\n" +
	"\x15ESubFaultLackOfSignal\x12 \n" +
	"\x1cSUBFLT_LACKOFSIG_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17SUBFLT_LACKOFSIG_NORMAL\x10\x01\x12%\n" +
	"!SUBFLT_LACKOFSIG_FLASHING_Y_ARROW\x10\x02*\x9a\x01\n" +
	"\x16ESubFaultSkippedYellow\x12\x1f\n" +
	"\x1bSUBFLT_SKIPPEDY_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18SUBFLT_SKIPPEDY_STANDARD\x10\x01\x12\"\n" +
	"\x1eSUBFLT_SKIPPEDY_FYA_FLASHING_Y\x10\x02\x12\x1d\n" +
	"\x19SUBFLT_SKIPPEDY_FYA_GREEN\x10\x03*\xb2\x01\n" +
	"\x10ESubFaultDataKey\x12\x1e\n" +
	"\x1aSUBFLT_DATAKEY_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18SUBFLT_DATAKEY_CRC_ERROR\x10\x01\x12\x1f\n" +
	"\x1bSUBFLT_DATAKEY_FORMAT_ERROR\x10\x02\x12\x1d\n" +
	"\x19SUBFLT_DATAKEY_DATA_ERROR\x10\x03\x12 \n" +
	"\x1cSUBFLT_DATAKEY_RAM_CRC_ERROR\x10\x04*~\n" +
	"\x13ESubFaultDiagnostic\x12\x1b\n" +
	"\x17SUBFLT_DIAG_UNSPECIFIED\x10\x00\x12\x11\n" +
	"\rSUBFLT_DIAG_1\x10\x01\x12\x11\n" +
	"\rSUBFLT_DIAG_2\x10\x02\x12\x11\n" +
	"\rSUBFLT_DIAG_3\x10\x03\x12\x11\n" +
	"\rSUBFLT_DIAG_4\x10\x04b\x06proto3"

var (
	file_mon_faults_proto_rawDescOnce sync.Once
	file_mon_faults_proto_rawDescData []byte
)

func file_mon_faults_proto_rawDescGZIP() []byte {
	file_mon_faults_proto_rawDescOnce.Do(func() {
		file_mon_faults_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_mon_faults_proto_rawDesc), len(file_mon_faults_proto_rawDesc)))
	})
	return file_mon_faults_proto_rawDescData
}

var file_mon_faults_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_mon_faults_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_mon_faults_proto_goTypes = []any{
	(EFaultCode)(0),                         // 0: mon_faults.EFaultCode
	(ESubFaultSerialPort)(0),                // 1: mon_faults.ESubFaultSerialPort
	(ESubFaultMultipleIndication)(0),        // 2: mon_faults.ESubFaultMultipleIndication
	(ESubFaultLackOfSignal)(0),              // 3: mon_faults.ESubFaultLackOfSignal
	(ESubFaultSkippedYellow)(0),             // 4: mon_faults.ESubFaultSkippedYellow
	(ESubFaultDataKey)(0),                   // 5: mon_faults.ESubFaultDataKey
	(ESubFaultDiagnostic)(0),                // 6: mon_faults.ESubFaultDiagnostic
	(*MmuSubFaultTypeValue)(nil),            // 7: mon_faults.MmuSubFaultTypeValue
	(*MmuMonitoredControlStatesBitmap)(nil), // 8: mon_faults.MmuMonitoredControlStatesBitmap
	(*MmuMonitoredInputsStatusBitmap)(nil),  // 9: mon_faults.MmuMonitoredInputsStatusBitmap
	(*FaultIndicationChVoltCurrent)(nil),    // 10: mon_faults.FaultIndicationChVoltCurrent
}
var file_mon_faults_proto_depIdxs = []int32{
	1, // 0: mon_faults.MmuSubFaultTypeValue.serial_port:type_name -> mon_faults.ESubFaultSerialPort
	2, // 1: mon_faults.MmuSubFaultTypeValue.multiple_indication:type_name -> mon_faults.ESubFaultMultipleIndication
	3, // 2: mon_faults.MmuSubFaultTypeValue.lack_of_signal:type_name -> mon_faults.ESubFaultLackOfSignal
	4, // 3: mon_faults.MmuSubFaultTypeValue.skipped_yellow:type_name -> mon_faults.ESubFaultSkippedYellow
	5, // 4: mon_faults.MmuSubFaultTypeValue.data_key:type_name -> mon_faults.ESubFaultDataKey
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_mon_faults_proto_init() }
func file_mon_faults_proto_init() {
	if File_mon_faults_proto != nil {
		return
	}
	file_mon_faults_proto_msgTypes[0].OneofWrappers = []any{
		(*MmuSubFaultTypeValue_SerialPort)(nil),
		(*MmuSubFaultTypeValue_MultipleIndication)(nil),
		(*MmuSubFaultTypeValue_LackOfSignal)(nil),
		(*MmuSubFaultTypeValue_SkippedYellow)(nil),
		(*MmuSubFaultTypeValue_DataKey)(nil),
	}
	file_mon_faults_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_mon_faults_proto_rawDesc), len(file_mon_faults_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mon_faults_proto_goTypes,
		DependencyIndexes: file_mon_faults_proto_depIdxs,
		EnumInfos:         file_mon_faults_proto_enumTypes,
		MessageInfos:      file_mon_faults_proto_msgTypes,
	}.Build()
	File_mon_faults_proto = out.File
	file_mon_faults_proto_goTypes = nil
	file_mon_faults_proto_depIdxs = nil
}
