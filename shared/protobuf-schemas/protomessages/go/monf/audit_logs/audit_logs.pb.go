//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  AUDIT_LOGS
//Log entry message format for the communications processor logs.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: audit_logs.proto

package audit_logs

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EAuditLogType is used to specify a log for requests and reads.
type EAuditLogType int32

const (
	EAuditLogType_AUD_LOG_UNSPECIFIED   EAuditLogType = 0
	EAuditLogType_AUD_LOG_ACCESS        EAuditLogType = 1
	EAuditLogType_AUD_LOG_NETWORK       EAuditLogType = 2
	EAuditLogType_AUD_LOG_CONFIGURATION EAuditLogType = 3
	EAuditLogType_AUD_LOG_BLUETOOTH     EAuditLogType = 4
	EAuditLogType_AUD_LOG_SYSTEM        EAuditLogType = 5
)

// Enum value maps for EAuditLogType.
var (
	EAuditLogType_name = map[int32]string{
		0: "AUD_LOG_UNSPECIFIED",
		1: "AUD_LOG_ACCESS",
		2: "AUD_LOG_NETWORK",
		3: "AUD_LOG_CONFIGURATION",
		4: "AUD_LOG_BLUETOOTH",
		5: "AUD_LOG_SYSTEM",
	}
	EAuditLogType_value = map[string]int32{
		"AUD_LOG_UNSPECIFIED":   0,
		"AUD_LOG_ACCESS":        1,
		"AUD_LOG_NETWORK":       2,
		"AUD_LOG_CONFIGURATION": 3,
		"AUD_LOG_BLUETOOTH":     4,
		"AUD_LOG_SYSTEM":        5,
	}
)

func (x EAuditLogType) Enum() *EAuditLogType {
	p := new(EAuditLogType)
	*p = x
	return p
}

func (x EAuditLogType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAuditLogType) Descriptor() protoreflect.EnumDescriptor {
	return file_audit_logs_proto_enumTypes[0].Descriptor()
}

func (EAuditLogType) Type() protoreflect.EnumType {
	return &file_audit_logs_proto_enumTypes[0]
}

func (x EAuditLogType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EAuditLogType.Descriptor instead.
func (EAuditLogType) EnumDescriptor() ([]byte, []int) {
	return file_audit_logs_proto_rawDescGZIP(), []int{0}
}

//	AuditLogEntryComms represents one entry of an audit log (any) on the Comms processor.
//
// This message format is used both to store entries in the log files on the Comms processer
// file system, and when they are read out over the websocket interface.
type AuditLogEntryComms struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Log entry timestamp
	EntryTimestamp *basic.LocalDateTime `protobuf:"bytes,1,opt,name=entry_timestamp,json=entryTimestamp,proto3" json:"entry_timestamp,omitempty"`
	// The entry text, max length in audit_logs.options
	EntryText string `protobuf:"bytes,2,opt,name=entry_text,json=entryText,proto3" json:"entry_text,omitempty"`
	// The entry property (value accompanying the text)
	//
	// Types that are valid to be assigned to EntryProperty:
	//
	//	*AuditLogEntryComms_IntegerVal
	//	*AuditLogEntryComms_FloatVal
	//	*AuditLogEntryComms_StringText
	EntryProperty isAuditLogEntryComms_EntryProperty `protobuf_oneof:"entry_property"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuditLogEntryComms) Reset() {
	*x = AuditLogEntryComms{}
	mi := &file_audit_logs_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditLogEntryComms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLogEntryComms) ProtoMessage() {}

func (x *AuditLogEntryComms) ProtoReflect() protoreflect.Message {
	mi := &file_audit_logs_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLogEntryComms.ProtoReflect.Descriptor instead.
func (*AuditLogEntryComms) Descriptor() ([]byte, []int) {
	return file_audit_logs_proto_rawDescGZIP(), []int{0}
}

func (x *AuditLogEntryComms) GetEntryTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.EntryTimestamp
	}
	return nil
}

func (x *AuditLogEntryComms) GetEntryText() string {
	if x != nil {
		return x.EntryText
	}
	return ""
}

func (x *AuditLogEntryComms) GetEntryProperty() isAuditLogEntryComms_EntryProperty {
	if x != nil {
		return x.EntryProperty
	}
	return nil
}

func (x *AuditLogEntryComms) GetIntegerVal() int64 {
	if x != nil {
		if x, ok := x.EntryProperty.(*AuditLogEntryComms_IntegerVal); ok {
			return x.IntegerVal
		}
	}
	return 0
}

func (x *AuditLogEntryComms) GetFloatVal() float32 {
	if x != nil {
		if x, ok := x.EntryProperty.(*AuditLogEntryComms_FloatVal); ok {
			return x.FloatVal
		}
	}
	return 0
}

func (x *AuditLogEntryComms) GetStringText() string {
	if x != nil {
		if x, ok := x.EntryProperty.(*AuditLogEntryComms_StringText); ok {
			return x.StringText
		}
	}
	return ""
}

type isAuditLogEntryComms_EntryProperty interface {
	isAuditLogEntryComms_EntryProperty()
}

type AuditLogEntryComms_IntegerVal struct {
	IntegerVal int64 `protobuf:"varint,3,opt,name=integer_val,json=integerVal,proto3,oneof"` // a signed integer value up to 64 bits (may be used for UNsigned 32-bit values)
}

type AuditLogEntryComms_FloatVal struct {
	FloatVal float32 `protobuf:"fixed32,4,opt,name=float_val,json=floatVal,proto3,oneof"` // floating point value
}

type AuditLogEntryComms_StringText struct {
	StringText string `protobuf:"bytes,5,opt,name=string_text,json=stringText,proto3,oneof"` // a text string, max length in audit_logs.options
}

func (*AuditLogEntryComms_IntegerVal) isAuditLogEntryComms_EntryProperty() {}

func (*AuditLogEntryComms_FloatVal) isAuditLogEntryComms_EntryProperty() {}

func (*AuditLogEntryComms_StringText) isAuditLogEntryComms_EntryProperty() {}

//	AuditLogMultipleEntriesComms encapsulates repeated audit log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
//
// NOTE: The log entries are NOT stored in this format as a repeated field in the log files.
// Instead, the are separated by a short binary header.
// This message is ONLY used when log entries are read out as part of a command-response.
type AuditLogMultipleEntriesComms struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*AuditLogEntryComms  `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in audit_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuditLogMultipleEntriesComms) Reset() {
	*x = AuditLogMultipleEntriesComms{}
	mi := &file_audit_logs_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditLogMultipleEntriesComms) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLogMultipleEntriesComms) ProtoMessage() {}

func (x *AuditLogMultipleEntriesComms) ProtoReflect() protoreflect.Message {
	mi := &file_audit_logs_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLogMultipleEntriesComms.ProtoReflect.Descriptor instead.
func (*AuditLogMultipleEntriesComms) Descriptor() ([]byte, []int) {
	return file_audit_logs_proto_rawDescGZIP(), []int{1}
}

func (x *AuditLogMultipleEntriesComms) GetLogEntry() []*AuditLogEntryComms {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	AuditLogEntryCount is used to return the present total count of entries for a log,
//
// along with the length limit on the log.
type AuditLogEntryCount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is the log to which the count applies
	Log EAuditLogType `protobuf:"varint,1,opt,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"`
	// Total number of present log entries.
	Entries uint32 `protobuf:"varint,2,opt,name=entries,proto3" json:"entries,omitempty"`
	// Log length limit in total entries.  If 0, the log has no entry limit and is only limited by
	// file system space.
	MaxEntries uint32 `protobuf:"varint,3,opt,name=max_entries,json=maxEntries,proto3" json:"max_entries,omitempty"`
	// log length minimum increment
	EntriesPerFile uint32 `protobuf:"varint,4,opt,name=entries_per_file,json=entriesPerFile,proto3" json:"entries_per_file,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AuditLogEntryCount) Reset() {
	*x = AuditLogEntryCount{}
	mi := &file_audit_logs_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuditLogEntryCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLogEntryCount) ProtoMessage() {}

func (x *AuditLogEntryCount) ProtoReflect() protoreflect.Message {
	mi := &file_audit_logs_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLogEntryCount.ProtoReflect.Descriptor instead.
func (*AuditLogEntryCount) Descriptor() ([]byte, []int) {
	return file_audit_logs_proto_rawDescGZIP(), []int{2}
}

func (x *AuditLogEntryCount) GetLog() EAuditLogType {
	if x != nil {
		return x.Log
	}
	return EAuditLogType_AUD_LOG_UNSPECIFIED
}

func (x *AuditLogEntryCount) GetEntries() uint32 {
	if x != nil {
		return x.Entries
	}
	return 0
}

func (x *AuditLogEntryCount) GetMaxEntries() uint32 {
	if x != nil {
		return x.MaxEntries
	}
	return 0
}

func (x *AuditLogEntryCount) GetEntriesPerFile() uint32 {
	if x != nil {
		return x.EntriesPerFile
	}
	return 0
}

var File_audit_logs_proto protoreflect.FileDescriptor

const file_audit_logs_proto_rawDesc = "" +
	"\n" +
	"\x10audit_logs.proto\x12\n" +
	"audit_logs\x1a\vbasic.proto\"\xe9\x01\n" +
	"\x12AuditLogEntryComms\x12=\n" +
	"\x0fentry_timestamp\x18\x01 \x01(\v2\x14.basic.LocalDateTimeR\x0eentryTimestamp\x12\x1d\n" +
	"\n" +
	"entry_text\x18\x02 \x01(\tR\tentryText\x12!\n" +
	"\vinteger_val\x18\x03 \x01(\x03H\x00R\n" +
	"integerVal\x12\x1d\n" +
	"\tfloat_val\x18\x04 \x01(\x02H\x00R\bfloatVal\x12!\n" +
	"\vstring_text\x18\x05 \x01(\tH\x00R\n" +
	"stringTextB\x10\n" +
	"\x0eentry_property\"[\n" +
	"\x1cAuditLogMultipleEntriesComms\x12;\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x1e.audit_logs.AuditLogEntryCommsR\blogEntry\"\xa6\x01\n" +
	"\x12AuditLogEntryCount\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\x12\x18\n" +
	"\aentries\x18\x02 \x01(\rR\aentries\x12\x1f\n" +
	"\vmax_entries\x18\x03 \x01(\rR\n" +
	"maxEntries\x12(\n" +
	"\x10entries_per_file\x18\x04 \x01(\rR\x0eentriesPerFile*\x97\x01\n" +
	"\rEAuditLogType\x12\x17\n" +
	"\x13AUD_LOG_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eAUD_LOG_ACCESS\x10\x01\x12\x13\n" +
	"\x0fAUD_LOG_NETWORK\x10\x02\x12\x19\n" +
	"\x15AUD_LOG_CONFIGURATION\x10\x03\x12\x15\n" +
	"\x11AUD_LOG_BLUETOOTH\x10\x04\x12\x12\n" +
	"\x0eAUD_LOG_SYSTEM\x10\x05b\x06proto3"

var (
	file_audit_logs_proto_rawDescOnce sync.Once
	file_audit_logs_proto_rawDescData []byte
)

func file_audit_logs_proto_rawDescGZIP() []byte {
	file_audit_logs_proto_rawDescOnce.Do(func() {
		file_audit_logs_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_audit_logs_proto_rawDesc), len(file_audit_logs_proto_rawDesc)))
	})
	return file_audit_logs_proto_rawDescData
}

var file_audit_logs_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_audit_logs_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_audit_logs_proto_goTypes = []any{
	(EAuditLogType)(0),                   // 0: audit_logs.EAuditLogType
	(*AuditLogEntryComms)(nil),           // 1: audit_logs.AuditLogEntryComms
	(*AuditLogMultipleEntriesComms)(nil), // 2: audit_logs.AuditLogMultipleEntriesComms
	(*AuditLogEntryCount)(nil),           // 3: audit_logs.AuditLogEntryCount
	(*basic.LocalDateTime)(nil),          // 4: basic.LocalDateTime
}
var file_audit_logs_proto_depIdxs = []int32{
	4, // 0: audit_logs.AuditLogEntryComms.entry_timestamp:type_name -> basic.LocalDateTime
	1, // 1: audit_logs.AuditLogMultipleEntriesComms.log_entry:type_name -> audit_logs.AuditLogEntryComms
	0, // 2: audit_logs.AuditLogEntryCount.log:type_name -> audit_logs.EAuditLogType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_audit_logs_proto_init() }
func file_audit_logs_proto_init() {
	if File_audit_logs_proto != nil {
		return
	}
	file_audit_logs_proto_msgTypes[0].OneofWrappers = []any{
		(*AuditLogEntryComms_IntegerVal)(nil),
		(*AuditLogEntryComms_FloatVal)(nil),
		(*AuditLogEntryComms_StringText)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_audit_logs_proto_rawDesc), len(file_audit_logs_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_audit_logs_proto_goTypes,
		DependencyIndexes: file_audit_logs_proto_depIdxs,
		EnumInfos:         file_audit_logs_proto_enumTypes,
		MessageInfos:      file_audit_logs_proto_msgTypes,
	}.Build()
	File_audit_logs_proto = out.File
	file_audit_logs_proto_goTypes = nil
	file_audit_logs_proto_depIdxs = nil
}
