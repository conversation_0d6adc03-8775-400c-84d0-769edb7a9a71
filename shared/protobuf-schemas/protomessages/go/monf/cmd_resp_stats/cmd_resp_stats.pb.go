//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  CMD_RESP_STATS
//Commands and responses for retrieving operational statistics.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: cmd_resp_stats.proto

package cmd_resp_stats

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	mon_logs "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs"
	settings "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//	ENUM EDisplayButtonEvent defines the values of the display button events;
//
// one of Pressed, Idle, or else a Duration (in 0.1 second increments) that the button was "pressed".
type EDisplayButtonEvent int32

const (
	// 0 = Button currently is pressed
	EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED EDisplayButtonEvent = 0
	// 255 = Idle value (button not pressed)
	EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_IDLE EDisplayButtonEvent = 255 // Higher values are reserved for future use
)

// Enum value maps for EDisplayButtonEvent.
var (
	EDisplayButtonEvent_name = map[int32]string{
		0:   "DISPLAY_BUTTON_EVENT_PRESSED",
		255: "DISPLAY_BUTTON_EVENT_IDLE",
	}
	EDisplayButtonEvent_value = map[string]int32{
		"DISPLAY_BUTTON_EVENT_PRESSED": 0,
		"DISPLAY_BUTTON_EVENT_IDLE":    255,
	}
)

func (x EDisplayButtonEvent) Enum() *EDisplayButtonEvent {
	p := new(EDisplayButtonEvent)
	*p = x
	return p
}

func (x EDisplayButtonEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EDisplayButtonEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_cmd_resp_stats_proto_enumTypes[0].Descriptor()
}

func (EDisplayButtonEvent) Type() protoreflect.EnumType {
	return &file_cmd_resp_stats_proto_enumTypes[0]
}

func (x EDisplayButtonEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EDisplayButtonEvent.Descriptor instead.
func (EDisplayButtonEvent) EnumDescriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{0}
}

// CmdReadPort1Statistics is used to retrieve statistics on the controller port1 interface
type CmdReadPort1Statistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadPort1Statistics) Reset() {
	*x = CmdReadPort1Statistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadPort1Statistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadPort1Statistics) ProtoMessage() {}

func (x *CmdReadPort1Statistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadPort1Statistics.ProtoReflect.Descriptor instead.
func (*CmdReadPort1Statistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{0}
}

func (x *CmdReadPort1Statistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadPort1Statistics returns statistics on the controller port1 interface.
//
// The data comes from the Main processor response to the command 0x10 Retrieve Port 1 Statistics
type RespReadPort1Statistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Frame reception counters
	Frame_1RxCount uint32 `protobuf:"varint,1,opt,name=frame_1_rx_count,json=frame1RxCount,proto3" json:"frame_1_rx_count,omitempty"`
	Frame_3RxCount uint32 `protobuf:"varint,2,opt,name=frame_3_rx_count,json=frame3RxCount,proto3" json:"frame_3_rx_count,omitempty"`
	Frame_9RxCount uint32 `protobuf:"varint,3,opt,name=frame_9_rx_count,json=frame9RxCount,proto3" json:"frame_9_rx_count,omitempty"`
	// Frames where the length was less than expected.
	ShortFrameErrorCount uint32 `protobuf:"varint,4,opt,name=short_frame_error_count,json=shortFrameErrorCount,proto3" json:"short_frame_error_count,omitempty"`
	// The control byte value was unrecognized
	ControlByteErrorCount uint32 `protobuf:"varint,5,opt,name=control_byte_error_count,json=controlByteErrorCount,proto3" json:"control_byte_error_count,omitempty"`
	// frame CRC errors
	CrcErrorCount uint32 `protobuf:"varint,6,opt,name=crc_error_count,json=crcErrorCount,proto3" json:"crc_error_count,omitempty"`
	// Bus went idle without frame ending byte
	IdleStateErrorCount uint32 `protobuf:"varint,7,opt,name=idle_state_error_count,json=idleStateErrorCount,proto3" json:"idle_state_error_count,omitempty"`
	// Bus data started without frame start byte
	FramingErrorCount uint32 `protobuf:"varint,8,opt,name=framing_error_count,json=framingErrorCount,proto3" json:"framing_error_count,omitempty"`
	// Frames where the length was greater than expected.
	LongFrameErrorCount uint32 `protobuf:"varint,9,opt,name=long_frame_error_count,json=longFrameErrorCount,proto3" json:"long_frame_error_count,omitempty"`
	// Controller 100 ms communication timeout
	FrameTimeoutErrorCount uint32 `protobuf:"varint,10,opt,name=frame_timeout_error_count,json=frameTimeoutErrorCount,proto3" json:"frame_timeout_error_count,omitempty"`
	// Count of all unknown frames since last cleared
	UnknownFrameErrorCount uint32 `protobuf:"varint,11,opt,name=unknown_frame_error_count,json=unknownFrameErrorCount,proto3" json:"unknown_frame_error_count,omitempty"`
	// Most recent unknown frame number
	UnknownFrameNumber uint32 `protobuf:"varint,12,opt,name=unknown_frame_number,json=unknownFrameNumber,proto3" json:"unknown_frame_number,omitempty"`
	// timestamp when these statistics were last cleared
	LastClearTimestamp *basic.LocalDateTime `protobuf:"bytes,13,opt,name=last_clear_timestamp,json=lastClearTimestamp,proto3" json:"last_clear_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RespReadPort1Statistics) Reset() {
	*x = RespReadPort1Statistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadPort1Statistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadPort1Statistics) ProtoMessage() {}

func (x *RespReadPort1Statistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadPort1Statistics.ProtoReflect.Descriptor instead.
func (*RespReadPort1Statistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{1}
}

func (x *RespReadPort1Statistics) GetFrame_1RxCount() uint32 {
	if x != nil {
		return x.Frame_1RxCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetFrame_3RxCount() uint32 {
	if x != nil {
		return x.Frame_3RxCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetFrame_9RxCount() uint32 {
	if x != nil {
		return x.Frame_9RxCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetShortFrameErrorCount() uint32 {
	if x != nil {
		return x.ShortFrameErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetControlByteErrorCount() uint32 {
	if x != nil {
		return x.ControlByteErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetCrcErrorCount() uint32 {
	if x != nil {
		return x.CrcErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetIdleStateErrorCount() uint32 {
	if x != nil {
		return x.IdleStateErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetFramingErrorCount() uint32 {
	if x != nil {
		return x.FramingErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetLongFrameErrorCount() uint32 {
	if x != nil {
		return x.LongFrameErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetFrameTimeoutErrorCount() uint32 {
	if x != nil {
		return x.FrameTimeoutErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetUnknownFrameErrorCount() uint32 {
	if x != nil {
		return x.UnknownFrameErrorCount
	}
	return 0
}

func (x *RespReadPort1Statistics) GetUnknownFrameNumber() uint32 {
	if x != nil {
		return x.UnknownFrameNumber
	}
	return 0
}

func (x *RespReadPort1Statistics) GetLastClearTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.LastClearTimestamp
	}
	return nil
}

// CmdReadDataKeyStatistics is used to retrieve statistics on the controller port1 interface
type CmdReadDataKeyStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadDataKeyStatistics) Reset() {
	*x = CmdReadDataKeyStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadDataKeyStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadDataKeyStatistics) ProtoMessage() {}

func (x *CmdReadDataKeyStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadDataKeyStatistics.ProtoReflect.Descriptor instead.
func (*CmdReadDataKeyStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{2}
}

func (x *CmdReadDataKeyStatistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadDataKeyStatistics returns statistics on the Data Key or Program Card, depending on which is used
//
// in the monitor.
// The data comes from the Main processor response to the command 0x13 Retrieve Program Card / Data Key Statistics
type RespReadDataKeyStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is the source of the data
	Source settings.EConfigDataLocation `protobuf:"varint,1,opt,name=source,proto3,enum=settings.EConfigDataLocation" json:"source,omitempty"`
	// total count of reads from the data key / program card
	ReadCount uint32 `protobuf:"varint,2,opt,name=read_count,json=readCount,proto3" json:"read_count,omitempty"`
	// Count of the total seconds the key has been removed, over all key removals
	RemovedSeconds uint32 `protobuf:"varint,3,opt,name=removed_seconds,json=removedSeconds,proto3" json:"removed_seconds,omitempty"`
	// count of CRC errors when reading the data key / program card
	CrcErrorCount uint32 `protobuf:"varint,4,opt,name=crc_error_count,json=crcErrorCount,proto3" json:"crc_error_count,omitempty"`
	// count of faults caused by data key / program card read errors
	FaultsFromBadReadsCount uint32 `protobuf:"varint,5,opt,name=faults_from_bad_reads_count,json=faultsFromBadReadsCount,proto3" json:"faults_from_bad_reads_count,omitempty"`
	// Field of errors that have occurred
	Errors *mon_logs.DataKeyErrorCodeBitmap `protobuf:"bytes,6,opt,name=errors,proto3" json:"errors,omitempty"`
	// timestamp when these statistics were last cleared
	LastClearTimestamp *basic.LocalDateTime `protobuf:"bytes,7,opt,name=last_clear_timestamp,json=lastClearTimestamp,proto3" json:"last_clear_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RespReadDataKeyStatistics) Reset() {
	*x = RespReadDataKeyStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadDataKeyStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadDataKeyStatistics) ProtoMessage() {}

func (x *RespReadDataKeyStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadDataKeyStatistics.ProtoReflect.Descriptor instead.
func (*RespReadDataKeyStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{3}
}

func (x *RespReadDataKeyStatistics) GetSource() settings.EConfigDataLocation {
	if x != nil {
		return x.Source
	}
	return settings.EConfigDataLocation(0)
}

func (x *RespReadDataKeyStatistics) GetReadCount() uint32 {
	if x != nil {
		return x.ReadCount
	}
	return 0
}

func (x *RespReadDataKeyStatistics) GetRemovedSeconds() uint32 {
	if x != nil {
		return x.RemovedSeconds
	}
	return 0
}

func (x *RespReadDataKeyStatistics) GetCrcErrorCount() uint32 {
	if x != nil {
		return x.CrcErrorCount
	}
	return 0
}

func (x *RespReadDataKeyStatistics) GetFaultsFromBadReadsCount() uint32 {
	if x != nil {
		return x.FaultsFromBadReadsCount
	}
	return 0
}

func (x *RespReadDataKeyStatistics) GetErrors() *mon_logs.DataKeyErrorCodeBitmap {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *RespReadDataKeyStatistics) GetLastClearTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.LastClearTimestamp
	}
	return nil
}

//	CmdReadMainToIsolatedCommStatistics is used to retrieve statistics on the serial interface
//
// between the Main and Isolated microcontrollers
type CmdReadMainToIsolatedCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadMainToIsolatedCommStatistics) Reset() {
	*x = CmdReadMainToIsolatedCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadMainToIsolatedCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadMainToIsolatedCommStatistics) ProtoMessage() {}

func (x *CmdReadMainToIsolatedCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadMainToIsolatedCommStatistics.ProtoReflect.Descriptor instead.
func (*CmdReadMainToIsolatedCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{4}
}

func (x *CmdReadMainToIsolatedCommStatistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadMainToIsolatedCommStatistics returns statistics on the inter-MCU communication bus between the Main
//
// and Isolated MCUs.
// The data comes from the Main processor response to the command
// 0x14 Retrieve Main to Isolated processor Communication Statistics
type RespReadMainToIsolatedCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// total count of commands messages sent from the Mains MCU to the Isolated
	SentCount uint32 `protobuf:"varint,1,opt,name=sent_count,json=sentCount,proto3" json:"sent_count,omitempty"`
	// total count of response messages received by the Mains MCU from the Isolated
	ReceivedCount uint32 `protobuf:"varint,2,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	// total count of unexpected messages (responses) received by the Mains MCU from the Isolated
	UnexpectedReceivedCount uint32 `protobuf:"varint,3,opt,name=unexpected_received_count,json=unexpectedReceivedCount,proto3" json:"unexpected_received_count,omitempty"`
	// total count of messages (responses) received by the Mains MCU from the Isolated
	// with checksum errors
	ChecksumErrorsCount uint32 `protobuf:"varint,4,opt,name=checksum_errors_count,json=checksumErrorsCount,proto3" json:"checksum_errors_count,omitempty"`
	// total count of messages (responses) received by the Mains MCU from the Isolated
	// with unexpected data length
	DataLengthErrorsCount uint32 `protobuf:"varint,5,opt,name=data_length_errors_count,json=dataLengthErrorsCount,proto3" json:"data_length_errors_count,omitempty"`
	// total count of timeouts on response messages expected by the Mains MCU from the Isolated
	TimeoutErrorsCount uint32 `protobuf:"varint,6,opt,name=timeout_errors_count,json=timeoutErrorsCount,proto3" json:"timeout_errors_count,omitempty"`
	// total count of times the Main MCU reset its UART due to communication errors
	UartResetCount uint32 `protobuf:"varint,7,opt,name=uart_reset_count,json=uartResetCount,proto3" json:"uart_reset_count,omitempty"`
	// total count of messages (responses) received by the Main MCU with framing errors
	BadFrameCount uint32 `protobuf:"varint,8,opt,name=bad_frame_count,json=badFrameCount,proto3" json:"bad_frame_count,omitempty"`
	// timestamp when these statistics were last cleared
	LastClearTimestamp *basic.LocalDateTime `protobuf:"bytes,9,opt,name=last_clear_timestamp,json=lastClearTimestamp,proto3" json:"last_clear_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RespReadMainToIsolatedCommStatistics) Reset() {
	*x = RespReadMainToIsolatedCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadMainToIsolatedCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadMainToIsolatedCommStatistics) ProtoMessage() {}

func (x *RespReadMainToIsolatedCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadMainToIsolatedCommStatistics.ProtoReflect.Descriptor instead.
func (*RespReadMainToIsolatedCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{5}
}

func (x *RespReadMainToIsolatedCommStatistics) GetSentCount() uint32 {
	if x != nil {
		return x.SentCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetReceivedCount() uint32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetUnexpectedReceivedCount() uint32 {
	if x != nil {
		return x.UnexpectedReceivedCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetChecksumErrorsCount() uint32 {
	if x != nil {
		return x.ChecksumErrorsCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetDataLengthErrorsCount() uint32 {
	if x != nil {
		return x.DataLengthErrorsCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetTimeoutErrorsCount() uint32 {
	if x != nil {
		return x.TimeoutErrorsCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetUartResetCount() uint32 {
	if x != nil {
		return x.UartResetCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetBadFrameCount() uint32 {
	if x != nil {
		return x.BadFrameCount
	}
	return 0
}

func (x *RespReadMainToIsolatedCommStatistics) GetLastClearTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.LastClearTimestamp
	}
	return nil
}

//	CmdReadMainToDisplayCommStatistics is used to retrieve statistics on the serial interface
//
// between the Main and Display microcontrollers
type CmdReadMainToDisplayCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadMainToDisplayCommStatistics) Reset() {
	*x = CmdReadMainToDisplayCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadMainToDisplayCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadMainToDisplayCommStatistics) ProtoMessage() {}

func (x *CmdReadMainToDisplayCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadMainToDisplayCommStatistics.ProtoReflect.Descriptor instead.
func (*CmdReadMainToDisplayCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{6}
}

func (x *CmdReadMainToDisplayCommStatistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadMainToDisplayCommStatistics returns statistics on the inter-MCU communication bus between the Main
//
// and Display MCUs.
// The data comes from the Main processor response to the command
// 0x1A Retrieve Main to Display processor Communication Statistics
type RespReadMainToDisplayCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// total count of commands messages sent from the Mains MCU to the Display
	SentCount uint32 `protobuf:"varint,1,opt,name=sent_count,json=sentCount,proto3" json:"sent_count,omitempty"`
	// total count of response messages received by the Mains MCU from the Display
	ReceivedCount uint32 `protobuf:"varint,2,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	// total count of unexpected messages (responses) received by the Mains MCU from the Display
	UnexpectedReceivedCount uint32 `protobuf:"varint,3,opt,name=unexpected_received_count,json=unexpectedReceivedCount,proto3" json:"unexpected_received_count,omitempty"`
	// total count of messages (responses) received by the Mains MCU from the Display
	// with checksum errors
	ChecksumErrorsCount uint32 `protobuf:"varint,4,opt,name=checksum_errors_count,json=checksumErrorsCount,proto3" json:"checksum_errors_count,omitempty"`
	// total count of messages (responses) received by the Mains MCU from the Display
	// with unexpected data length
	DataLengthErrorsCount uint32 `protobuf:"varint,5,opt,name=data_length_errors_count,json=dataLengthErrorsCount,proto3" json:"data_length_errors_count,omitempty"`
	// total count of timeouts on response messages expected by the Mains MCU from the Display
	TimeoutErrorsCount uint32 `protobuf:"varint,6,opt,name=timeout_errors_count,json=timeoutErrorsCount,proto3" json:"timeout_errors_count,omitempty"`
	// total count of times the Main MCU reset its UART due to communication errors
	UartResetCount uint32 `protobuf:"varint,7,opt,name=uart_reset_count,json=uartResetCount,proto3" json:"uart_reset_count,omitempty"`
	// total count of messages (responses) received by the Main MCU with framing errors
	BadFrameCount uint32 `protobuf:"varint,8,opt,name=bad_frame_count,json=badFrameCount,proto3" json:"bad_frame_count,omitempty"`
	// timestamp when these statistics were last cleared
	LastClearTimestamp *basic.LocalDateTime `protobuf:"bytes,9,opt,name=last_clear_timestamp,json=lastClearTimestamp,proto3" json:"last_clear_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RespReadMainToDisplayCommStatistics) Reset() {
	*x = RespReadMainToDisplayCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadMainToDisplayCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadMainToDisplayCommStatistics) ProtoMessage() {}

func (x *RespReadMainToDisplayCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadMainToDisplayCommStatistics.ProtoReflect.Descriptor instead.
func (*RespReadMainToDisplayCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{7}
}

func (x *RespReadMainToDisplayCommStatistics) GetSentCount() uint32 {
	if x != nil {
		return x.SentCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetReceivedCount() uint32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetUnexpectedReceivedCount() uint32 {
	if x != nil {
		return x.UnexpectedReceivedCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetChecksumErrorsCount() uint32 {
	if x != nil {
		return x.ChecksumErrorsCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetDataLengthErrorsCount() uint32 {
	if x != nil {
		return x.DataLengthErrorsCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetTimeoutErrorsCount() uint32 {
	if x != nil {
		return x.TimeoutErrorsCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetUartResetCount() uint32 {
	if x != nil {
		return x.UartResetCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetBadFrameCount() uint32 {
	if x != nil {
		return x.BadFrameCount
	}
	return 0
}

func (x *RespReadMainToDisplayCommStatistics) GetLastClearTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.LastClearTimestamp
	}
	return nil
}

//	CmdReadMainToCommsCommStatistics is used to retrieve statistics on the serial interface
//
// between the Main and Isolated microcontrollers
type CmdReadMainToCommsCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadMainToCommsCommStatistics) Reset() {
	*x = CmdReadMainToCommsCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadMainToCommsCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadMainToCommsCommStatistics) ProtoMessage() {}

func (x *CmdReadMainToCommsCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadMainToCommsCommStatistics.ProtoReflect.Descriptor instead.
func (*CmdReadMainToCommsCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{8}
}

func (x *CmdReadMainToCommsCommStatistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadMainToCommsCommStatistics returns statistics on the inter-MCU communication bus between the Main
//
// and Communications MCUs for the Main MCU side.
// The data comes from the Main processor response to the command
// 0x15 Retrieve Main to Comms processor Comm Statistics
type RespReadMainToCommsCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// total count of response messages sent from the Main MCU to the Communications
	SentCount uint32 `protobuf:"varint,1,opt,name=sent_count,json=sentCount,proto3" json:"sent_count,omitempty"`
	// total count of commands messages received by the Main MCU from the Communications
	ReceivedCount uint32 `protobuf:"varint,2,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	// total count of unknown messages (commands) received by the Main MCU from the Communications
	UnknownReceivedCount uint32 `protobuf:"varint,3,opt,name=unknown_received_count,json=unknownReceivedCount,proto3" json:"unknown_received_count,omitempty"`
	// total count of messages (commands) received by the Main MCU from the Communications
	// with checksum errors
	ChecksumErrorsCount uint32 `protobuf:"varint,4,opt,name=checksum_errors_count,json=checksumErrorsCount,proto3" json:"checksum_errors_count,omitempty"`
	// total count of messages (commands) received by the Main MCU from the Communications
	// with unexpected data length
	DataLengthErrorsCount uint32 `protobuf:"varint,5,opt,name=data_length_errors_count,json=dataLengthErrorsCount,proto3" json:"data_length_errors_count,omitempty"`
	// total count of timeouts on ping messages expected by the Main MCU from the Communications
	PingTimeoutCount uint32 `protobuf:"varint,6,opt,name=ping_timeout_count,json=pingTimeoutCount,proto3" json:"ping_timeout_count,omitempty"`
	// total count of times the Main MCU reset its UART due to communication errors
	UartResetCount uint32 `protobuf:"varint,7,opt,name=uart_reset_count,json=uartResetCount,proto3" json:"uart_reset_count,omitempty"`
	// total count of messages (commands) received by the Main MCU with framing errors
	BadFrameCount uint32 `protobuf:"varint,8,opt,name=bad_frame_count,json=badFrameCount,proto3" json:"bad_frame_count,omitempty"`
	// timestamp when these statistics were last cleared
	LastClearTimestamp *basic.LocalDateTime `protobuf:"bytes,9,opt,name=last_clear_timestamp,json=lastClearTimestamp,proto3" json:"last_clear_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RespReadMainToCommsCommStatistics) Reset() {
	*x = RespReadMainToCommsCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadMainToCommsCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadMainToCommsCommStatistics) ProtoMessage() {}

func (x *RespReadMainToCommsCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadMainToCommsCommStatistics.ProtoReflect.Descriptor instead.
func (*RespReadMainToCommsCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{9}
}

func (x *RespReadMainToCommsCommStatistics) GetSentCount() uint32 {
	if x != nil {
		return x.SentCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetReceivedCount() uint32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetUnknownReceivedCount() uint32 {
	if x != nil {
		return x.UnknownReceivedCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetChecksumErrorsCount() uint32 {
	if x != nil {
		return x.ChecksumErrorsCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetDataLengthErrorsCount() uint32 {
	if x != nil {
		return x.DataLengthErrorsCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetPingTimeoutCount() uint32 {
	if x != nil {
		return x.PingTimeoutCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetUartResetCount() uint32 {
	if x != nil {
		return x.UartResetCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetBadFrameCount() uint32 {
	if x != nil {
		return x.BadFrameCount
	}
	return 0
}

func (x *RespReadMainToCommsCommStatistics) GetLastClearTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.LastClearTimestamp
	}
	return nil
}

//	CmdReadCommsToMainCommStatistics is used to retrieve statistics on the serial interface
//
// between the Main and Isolated microcontrollers
type CmdReadCommsToMainCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadCommsToMainCommStatistics) Reset() {
	*x = CmdReadCommsToMainCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadCommsToMainCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadCommsToMainCommStatistics) ProtoMessage() {}

func (x *CmdReadCommsToMainCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadCommsToMainCommStatistics.ProtoReflect.Descriptor instead.
func (*CmdReadCommsToMainCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{10}
}

func (x *CmdReadCommsToMainCommStatistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadCommsToMainCommStatistics returns statistics on the inter-MCU communication bus between the Main
//
// and Communications MCUs for the Communications MCU side.
// The data comes from the Comms processor
type RespReadCommsToMainCommStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// total count of commands messages sent from the Communications MCU to the Main
	SentCount uint32 `protobuf:"varint,1,opt,name=sent_count,json=sentCount,proto3" json:"sent_count,omitempty"`
	// total count of response messages received by the Communications MCU from the Main
	ReceivedCount uint32 `protobuf:"varint,2,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	// total count of unexpected messages (response) received by the Communications MCU from the Main
	UnexpectedReceivedCount uint32 `protobuf:"varint,3,opt,name=unexpected_received_count,json=unexpectedReceivedCount,proto3" json:"unexpected_received_count,omitempty"`
	// total count of messages (response) received by the Communications MCU from the Main
	// with checksum errors
	ChecksumErrorsCount uint32 `protobuf:"varint,4,opt,name=checksum_errors_count,json=checksumErrorsCount,proto3" json:"checksum_errors_count,omitempty"`
	// total count of messages (response) received by the Communications MCU from the Main
	// with unexpected data length
	DataLengthErrorsCount uint32 `protobuf:"varint,5,opt,name=data_length_errors_count,json=dataLengthErrorsCount,proto3" json:"data_length_errors_count,omitempty"`
	// total count of timeouts on responses expected by the Communications MCU from the Main
	TimeoutErrorsCount uint32 `protobuf:"varint,6,opt,name=timeout_errors_count,json=timeoutErrorsCount,proto3" json:"timeout_errors_count,omitempty"`
	// total count of messages (responses) received by the Comms MCU with framing errors
	BadFrameCount uint32 `protobuf:"varint,7,opt,name=bad_frame_count,json=badFrameCount,proto3" json:"bad_frame_count,omitempty"`
	// timestamp when these statistics were last cleared
	LastClearTimestamp *basic.LocalDateTime `protobuf:"bytes,8,opt,name=last_clear_timestamp,json=lastClearTimestamp,proto3" json:"last_clear_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RespReadCommsToMainCommStatistics) Reset() {
	*x = RespReadCommsToMainCommStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadCommsToMainCommStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadCommsToMainCommStatistics) ProtoMessage() {}

func (x *RespReadCommsToMainCommStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadCommsToMainCommStatistics.ProtoReflect.Descriptor instead.
func (*RespReadCommsToMainCommStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{11}
}

func (x *RespReadCommsToMainCommStatistics) GetSentCount() uint32 {
	if x != nil {
		return x.SentCount
	}
	return 0
}

func (x *RespReadCommsToMainCommStatistics) GetReceivedCount() uint32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

func (x *RespReadCommsToMainCommStatistics) GetUnexpectedReceivedCount() uint32 {
	if x != nil {
		return x.UnexpectedReceivedCount
	}
	return 0
}

func (x *RespReadCommsToMainCommStatistics) GetChecksumErrorsCount() uint32 {
	if x != nil {
		return x.ChecksumErrorsCount
	}
	return 0
}

func (x *RespReadCommsToMainCommStatistics) GetDataLengthErrorsCount() uint32 {
	if x != nil {
		return x.DataLengthErrorsCount
	}
	return 0
}

func (x *RespReadCommsToMainCommStatistics) GetTimeoutErrorsCount() uint32 {
	if x != nil {
		return x.TimeoutErrorsCount
	}
	return 0
}

func (x *RespReadCommsToMainCommStatistics) GetBadFrameCount() uint32 {
	if x != nil {
		return x.BadFrameCount
	}
	return 0
}

func (x *RespReadCommsToMainCommStatistics) GetLastClearTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.LastClearTimestamp
	}
	return nil
}

// CmdReadFlashStatistics is used to retrieve statistics on the Main MCU internal flash memory
type CmdReadFlashStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadFlashStatistics) Reset() {
	*x = CmdReadFlashStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadFlashStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadFlashStatistics) ProtoMessage() {}

func (x *CmdReadFlashStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadFlashStatistics.ProtoReflect.Descriptor instead.
func (*CmdReadFlashStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{12}
}

func (x *CmdReadFlashStatistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadFlashStatistics
//
// The data comes from the Main processor response to the command 0x16 Retrieve Flash Read Statistics
type RespReadFlashStatistics struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	FactoryArea   *settings.FlashAreaStatistics `protobuf:"bytes,1,opt,name=factory_area,json=factoryArea,proto3" json:"factory_area,omitempty"`
	GeneralArea   *settings.FlashAreaStatistics `protobuf:"bytes,2,opt,name=general_area,json=generalArea,proto3" json:"general_area,omitempty"`
	HeaderArea    *settings.FlashAreaStatistics `protobuf:"bytes,3,opt,name=header_area,json=headerArea,proto3" json:"header_area,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadFlashStatistics) Reset() {
	*x = RespReadFlashStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadFlashStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadFlashStatistics) ProtoMessage() {}

func (x *RespReadFlashStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadFlashStatistics.ProtoReflect.Descriptor instead.
func (*RespReadFlashStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{13}
}

func (x *RespReadFlashStatistics) GetFactoryArea() *settings.FlashAreaStatistics {
	if x != nil {
		return x.FactoryArea
	}
	return nil
}

func (x *RespReadFlashStatistics) GetGeneralArea() *settings.FlashAreaStatistics {
	if x != nil {
		return x.GeneralArea
	}
	return nil
}

func (x *RespReadFlashStatistics) GetHeaderArea() *settings.FlashAreaStatistics {
	if x != nil {
		return x.HeaderArea
	}
	return nil
}

// CmdReadWatchdogStatistics is used to retrieve statistics on the Main MCU watchdog counts
type CmdReadWatchdogStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadWatchdogStatistics) Reset() {
	*x = CmdReadWatchdogStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadWatchdogStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadWatchdogStatistics) ProtoMessage() {}

func (x *CmdReadWatchdogStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadWatchdogStatistics.ProtoReflect.Descriptor instead.
func (*CmdReadWatchdogStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{14}
}

func (x *CmdReadWatchdogStatistics) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadWatchdogStatistics - These are the counts of Main MCU task for the last window period
//
// The data comes from the Main processor response to the command 0x17 Retrieve Watchdog Counts
type RespReadWatchdogStatistics struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	MainLoopCount        uint32                 `protobuf:"varint,1,opt,name=main_loop_count,json=mainLoopCount,proto3" json:"main_loop_count,omitempty"`
	Tick_1MsCount        uint32                 `protobuf:"varint,2,opt,name=tick_1ms_count,json=tick1msCount,proto3" json:"tick_1ms_count,omitempty"`
	FaultProcessingCount uint32                 `protobuf:"varint,3,opt,name=fault_processing_count,json=faultProcessingCount,proto3" json:"fault_processing_count,omitempty"`
	AdcProcessingCount   uint32                 `protobuf:"varint,4,opt,name=adc_processing_count,json=adcProcessingCount,proto3" json:"adc_processing_count,omitempty"`
	RmsCalcuationCount   uint32                 `protobuf:"varint,5,opt,name=rms_calcuation_count,json=rmsCalcuationCount,proto3" json:"rms_calcuation_count,omitempty"`
	// communications MCU
	CommMcuRxCount uint32 `protobuf:"varint,6,opt,name=comm_mcu_rx_count,json=commMcuRxCount,proto3" json:"comm_mcu_rx_count,omitempty"`
	// isolated MCU
	IsoMcuTxCount uint32 `protobuf:"varint,7,opt,name=iso_mcu_tx_count,json=isoMcuTxCount,proto3" json:"iso_mcu_tx_count,omitempty"`
	// Program Card or Data Key number of reads
	PcardDkeyReadCount uint32 `protobuf:"varint,8,opt,name=pcard_dkey_read_count,json=pcardDkeyReadCount,proto3" json:"pcard_dkey_read_count,omitempty"`
	// GPIO monitoring
	GetInputsCount          uint32 `protobuf:"varint,9,opt,name=get_inputs_count,json=getInputsCount,proto3" json:"get_inputs_count,omitempty"`
	SetLedsCount            uint32 `protobuf:"varint,10,opt,name=set_leds_count,json=setLedsCount,proto3" json:"set_leds_count,omitempty"`
	DisplayMcuTxCount       uint32 `protobuf:"varint,11,opt,name=display_mcu_tx_count,json=displayMcuTxCount,proto3" json:"display_mcu_tx_count,omitempty"`
	BackgroundChecksumCount uint32 `protobuf:"varint,12,opt,name=background_checksum_count,json=backgroundChecksumCount,proto3" json:"background_checksum_count,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *RespReadWatchdogStatistics) Reset() {
	*x = RespReadWatchdogStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadWatchdogStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadWatchdogStatistics) ProtoMessage() {}

func (x *RespReadWatchdogStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadWatchdogStatistics.ProtoReflect.Descriptor instead.
func (*RespReadWatchdogStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{15}
}

func (x *RespReadWatchdogStatistics) GetMainLoopCount() uint32 {
	if x != nil {
		return x.MainLoopCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetTick_1MsCount() uint32 {
	if x != nil {
		return x.Tick_1MsCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetFaultProcessingCount() uint32 {
	if x != nil {
		return x.FaultProcessingCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetAdcProcessingCount() uint32 {
	if x != nil {
		return x.AdcProcessingCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetRmsCalcuationCount() uint32 {
	if x != nil {
		return x.RmsCalcuationCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetCommMcuRxCount() uint32 {
	if x != nil {
		return x.CommMcuRxCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetIsoMcuTxCount() uint32 {
	if x != nil {
		return x.IsoMcuTxCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetPcardDkeyReadCount() uint32 {
	if x != nil {
		return x.PcardDkeyReadCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetGetInputsCount() uint32 {
	if x != nil {
		return x.GetInputsCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetSetLedsCount() uint32 {
	if x != nil {
		return x.SetLedsCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetDisplayMcuTxCount() uint32 {
	if x != nil {
		return x.DisplayMcuTxCount
	}
	return 0
}

func (x *RespReadWatchdogStatistics) GetBackgroundChecksumCount() uint32 {
	if x != nil {
		return x.BackgroundChecksumCount
	}
	return 0
}

// CmdReadInternalSupplyVoltages is used to check the monitor internal voltage rails
type CmdReadInternalSupplyVoltages struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadInternalSupplyVoltages) Reset() {
	*x = CmdReadInternalSupplyVoltages{}
	mi := &file_cmd_resp_stats_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadInternalSupplyVoltages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadInternalSupplyVoltages) ProtoMessage() {}

func (x *CmdReadInternalSupplyVoltages) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadInternalSupplyVoltages.ProtoReflect.Descriptor instead.
func (*CmdReadInternalSupplyVoltages) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{16}
}

func (x *CmdReadInternalSupplyVoltages) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadInternalSupplyVoltages
//
// The data comes from the Main processor response to the command 0x18 Retrieve Internal Supplies
type RespReadInternalSupplyVoltages struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	AcMainsPresentVolts  float32                `protobuf:"fixed32,1,opt,name=ac_mains_present_volts,json=acMainsPresentVolts,proto3" json:"ac_mains_present_volts,omitempty"`
	MainMcu_5VVolts      *basic.NowMinMaxFloat  `protobuf:"bytes,2,opt,name=main_mcu_5v_volts,json=mainMcu5vVolts,proto3" json:"main_mcu_5v_volts,omitempty"`
	MainMcu_3V3Volts     *basic.NowMinMaxFloat  `protobuf:"bytes,3,opt,name=main_mcu_3v3_volts,json=mainMcu3v3Volts,proto3" json:"main_mcu_3v3_volts,omitempty"`
	MainMcuNeg3V3Volts   *basic.NowMinMaxFloat  `protobuf:"bytes,4,opt,name=main_mcu_neg3v3_volts,json=mainMcuNeg3v3Volts,proto3" json:"main_mcu_neg3v3_volts,omitempty"`
	IsolatedMcu_5VVolts  *basic.NowMinMaxFloat  `protobuf:"bytes,5,opt,name=isolated_mcu_5v_volts,json=isolatedMcu5vVolts,proto3" json:"isolated_mcu_5v_volts,omitempty"`
	IsolatedMcu_3V3Volts *basic.NowMinMaxFloat  `protobuf:"bytes,6,opt,name=isolated_mcu_3v3_volts,json=isolatedMcu3v3Volts,proto3" json:"isolated_mcu_3v3_volts,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RespReadInternalSupplyVoltages) Reset() {
	*x = RespReadInternalSupplyVoltages{}
	mi := &file_cmd_resp_stats_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadInternalSupplyVoltages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadInternalSupplyVoltages) ProtoMessage() {}

func (x *RespReadInternalSupplyVoltages) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadInternalSupplyVoltages.ProtoReflect.Descriptor instead.
func (*RespReadInternalSupplyVoltages) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{17}
}

func (x *RespReadInternalSupplyVoltages) GetAcMainsPresentVolts() float32 {
	if x != nil {
		return x.AcMainsPresentVolts
	}
	return 0
}

func (x *RespReadInternalSupplyVoltages) GetMainMcu_5VVolts() *basic.NowMinMaxFloat {
	if x != nil {
		return x.MainMcu_5VVolts
	}
	return nil
}

func (x *RespReadInternalSupplyVoltages) GetMainMcu_3V3Volts() *basic.NowMinMaxFloat {
	if x != nil {
		return x.MainMcu_3V3Volts
	}
	return nil
}

func (x *RespReadInternalSupplyVoltages) GetMainMcuNeg3V3Volts() *basic.NowMinMaxFloat {
	if x != nil {
		return x.MainMcuNeg3V3Volts
	}
	return nil
}

func (x *RespReadInternalSupplyVoltages) GetIsolatedMcu_5VVolts() *basic.NowMinMaxFloat {
	if x != nil {
		return x.IsolatedMcu_5VVolts
	}
	return nil
}

func (x *RespReadInternalSupplyVoltages) GetIsolatedMcu_3V3Volts() *basic.NowMinMaxFloat {
	if x != nil {
		return x.IsolatedMcu_3V3Volts
	}
	return nil
}

// CmdGetTimeDatesDst is used to retrieve the monitor date / time and daylight savings settings.
type CmdGetTimeDatesDst struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdGetTimeDatesDst) Reset() {
	*x = CmdGetTimeDatesDst{}
	mi := &file_cmd_resp_stats_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdGetTimeDatesDst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdGetTimeDatesDst) ProtoMessage() {}

func (x *CmdGetTimeDatesDst) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdGetTimeDatesDst.ProtoReflect.Descriptor instead.
func (*CmdGetTimeDatesDst) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{18}
}

func (x *CmdGetTimeDatesDst) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespGetTimeDatesDst
//
// The data comes from the Main processor response to the command 0x19 Get Date/Time and Daylight Savings
type RespGetTimeDatesDst struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// date and time of the monitor Main MCU
	PresentDateTime *basic.LocalDateTime `protobuf:"bytes,1,opt,name=present_date_time,json=presentDateTime,proto3" json:"present_date_time,omitempty"`
	// whether daylights savings time is enabled or not
	DstEnabled bool `protobuf:"varint,2,opt,name=dst_enabled,json=dstEnabled,proto3" json:"dst_enabled,omitempty"`
	// the daylight savings settings in use on the monitor (if enabled)
	DstSettings   *basic.DaylightSavingsSettings `protobuf:"bytes,3,opt,name=dst_settings,json=dstSettings,proto3" json:"dst_settings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespGetTimeDatesDst) Reset() {
	*x = RespGetTimeDatesDst{}
	mi := &file_cmd_resp_stats_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespGetTimeDatesDst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespGetTimeDatesDst) ProtoMessage() {}

func (x *RespGetTimeDatesDst) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespGetTimeDatesDst.ProtoReflect.Descriptor instead.
func (*RespGetTimeDatesDst) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{19}
}

func (x *RespGetTimeDatesDst) GetPresentDateTime() *basic.LocalDateTime {
	if x != nil {
		return x.PresentDateTime
	}
	return nil
}

func (x *RespGetTimeDatesDst) GetDstEnabled() bool {
	if x != nil {
		return x.DstEnabled
	}
	return false
}

func (x *RespGetTimeDatesDst) GetDstSettings() *basic.DaylightSavingsSettings {
	if x != nil {
		return x.DstSettings
	}
	return nil
}

// CmdClearStatistics is used to clear monitor statistics
type CmdClearStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The statistics to clear
	StatisticsToClear settings.EMonitorStatistics `protobuf:"varint,1,opt,name=statistics_to_clear,json=statisticsToClear,proto3,enum=settings.EMonitorStatistics" json:"statistics_to_clear,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CmdClearStatistics) Reset() {
	*x = CmdClearStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdClearStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdClearStatistics) ProtoMessage() {}

func (x *CmdClearStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdClearStatistics.ProtoReflect.Descriptor instead.
func (*CmdClearStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{20}
}

func (x *CmdClearStatistics) GetStatisticsToClear() settings.EMonitorStatistics {
	if x != nil {
		return x.StatisticsToClear
	}
	return settings.EMonitorStatistics(0)
}

//	RespClearStatistics
//
// The data comes from the Main processor response(s) to the command(s)
// 0x90 Reset Port 1 Statistics
// 0x93 Reset Reset Data Key Statistics
// 0x94 Reset Main to Isolated processor Communication Statistics
// 0x95 Reset Main to Comms processor Communication Statistics
type RespClearStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The statistics that were cleared
	StatisticsCleared settings.EMonitorStatistics `protobuf:"varint,1,opt,name=statistics_cleared,json=statisticsCleared,proto3,enum=settings.EMonitorStatistics" json:"statistics_cleared,omitempty"`
	// timestamp when the statistics were cleared
	// If the command statistics_to_clear = STATISTICS_ALL, this is the timestamp of the
	// last set of statstics cleared.
	Timestamp     *basic.LocalDateTime `protobuf:"bytes,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespClearStatistics) Reset() {
	*x = RespClearStatistics{}
	mi := &file_cmd_resp_stats_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespClearStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespClearStatistics) ProtoMessage() {}

func (x *RespClearStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespClearStatistics.ProtoReflect.Descriptor instead.
func (*RespClearStatistics) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{21}
}

func (x *RespClearStatistics) GetStatisticsCleared() settings.EMonitorStatistics {
	if x != nil {
		return x.StatisticsCleared
	}
	return settings.EMonitorStatistics(0)
}

func (x *RespClearStatistics) GetTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

//	CmdSetTimeDatesDst is used to set the monitor date / time and daylight savings settings.
//
// This will send command 0x99 Get Date/Time and Daylight Savings to the Main processor
//
// NOTE: If the monitor is in a cabinet that has Port 1 communications, any setting of the current
// date and time will be overwritten within one second when date/time is broadcast on Port 1
// (every one second).
type CmdSetTimeDatesDst struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// date and time to set for the Main MCU
	PresentDateTime *basic.LocalDateTime `protobuf:"bytes,1,opt,name=present_date_time,json=presentDateTime,proto3" json:"present_date_time,omitempty"`
	// whether daylights savings time is enabled or not
	DstEnabled bool `protobuf:"varint,2,opt,name=dst_enabled,json=dstEnabled,proto3" json:"dst_enabled,omitempty"`
	// the daylight savings settings to use on the monitor (if enabled)
	DstSettings   *basic.DaylightSavingsSettings `protobuf:"bytes,3,opt,name=dst_settings,json=dstSettings,proto3" json:"dst_settings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdSetTimeDatesDst) Reset() {
	*x = CmdSetTimeDatesDst{}
	mi := &file_cmd_resp_stats_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdSetTimeDatesDst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdSetTimeDatesDst) ProtoMessage() {}

func (x *CmdSetTimeDatesDst) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdSetTimeDatesDst.ProtoReflect.Descriptor instead.
func (*CmdSetTimeDatesDst) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{22}
}

func (x *CmdSetTimeDatesDst) GetPresentDateTime() *basic.LocalDateTime {
	if x != nil {
		return x.PresentDateTime
	}
	return nil
}

func (x *CmdSetTimeDatesDst) GetDstEnabled() bool {
	if x != nil {
		return x.DstEnabled
	}
	return false
}

func (x *CmdSetTimeDatesDst) GetDstSettings() *basic.DaylightSavingsSettings {
	if x != nil {
		return x.DstSettings
	}
	return nil
}

// RespSetTimeDatesDst
type RespSetTimeDatesDst struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The result of the time set on the Main processor
	Result        settings.ETimeSetResult `protobuf:"varint,1,opt,name=result,proto3,enum=settings.ETimeSetResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespSetTimeDatesDst) Reset() {
	*x = RespSetTimeDatesDst{}
	mi := &file_cmd_resp_stats_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespSetTimeDatesDst) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespSetTimeDatesDst) ProtoMessage() {}

func (x *RespSetTimeDatesDst) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespSetTimeDatesDst.ProtoReflect.Descriptor instead.
func (*RespSetTimeDatesDst) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{23}
}

func (x *RespSetTimeDatesDst) GetResult() settings.ETimeSetResult {
	if x != nil {
		return x.Result
	}
	return settings.ETimeSetResult(0)
}

// CmdRemoteDisplayButtonEvent is used to remotely set the state of the display buttons,
// as though they were pressed by a user on the local Display.
// The command is sent on each edge transition of any button (pressed or released).
// The value for each button will be:
// - 0 = Button currently is pressed
// - 1 - 254 = 0.1 to 25.4 second duration that it was pressed before it was released
// - 255 = Idle value (button not pressed)
// Maps to the Serial Command 0x9A Set Display Button Event.
type CmdRemoteDisplayButtonEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// State of the Help button
	Help EDisplayButtonEvent `protobuf:"varint,1,opt,name=help,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"help,omitempty"`
	// State of the Up (Next) button
	Up EDisplayButtonEvent `protobuf:"varint,2,opt,name=up,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"up,omitempty"`
	// State of the Down (Back) button
	Down EDisplayButtonEvent `protobuf:"varint,3,opt,name=down,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"down,omitempty"`
	// State of the Enter (Select) button
	Enter EDisplayButtonEvent `protobuf:"varint,4,opt,name=enter,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"enter,omitempty"`
	// State of the Back (Escape or Exit) button
	Back EDisplayButtonEvent `protobuf:"varint,5,opt,name=back,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"back,omitempty"`
	// State of the Left button
	Left EDisplayButtonEvent `protobuf:"varint,6,opt,name=left,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"left,omitempty"`
	// State of the Right button
	Right EDisplayButtonEvent `protobuf:"varint,7,opt,name=right,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"right,omitempty"`
	// State of the Reset button
	Reset_        EDisplayButtonEvent `protobuf:"varint,8,opt,name=reset,proto3,enum=cmd_resp_stats.EDisplayButtonEvent" json:"reset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRemoteDisplayButtonEvent) Reset() {
	*x = CmdRemoteDisplayButtonEvent{}
	mi := &file_cmd_resp_stats_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRemoteDisplayButtonEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRemoteDisplayButtonEvent) ProtoMessage() {}

func (x *CmdRemoteDisplayButtonEvent) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRemoteDisplayButtonEvent.ProtoReflect.Descriptor instead.
func (*CmdRemoteDisplayButtonEvent) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{24}
}

func (x *CmdRemoteDisplayButtonEvent) GetHelp() EDisplayButtonEvent {
	if x != nil {
		return x.Help
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

func (x *CmdRemoteDisplayButtonEvent) GetUp() EDisplayButtonEvent {
	if x != nil {
		return x.Up
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

func (x *CmdRemoteDisplayButtonEvent) GetDown() EDisplayButtonEvent {
	if x != nil {
		return x.Down
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

func (x *CmdRemoteDisplayButtonEvent) GetEnter() EDisplayButtonEvent {
	if x != nil {
		return x.Enter
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

func (x *CmdRemoteDisplayButtonEvent) GetBack() EDisplayButtonEvent {
	if x != nil {
		return x.Back
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

func (x *CmdRemoteDisplayButtonEvent) GetLeft() EDisplayButtonEvent {
	if x != nil {
		return x.Left
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

func (x *CmdRemoteDisplayButtonEvent) GetRight() EDisplayButtonEvent {
	if x != nil {
		return x.Right
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

func (x *CmdRemoteDisplayButtonEvent) GetReset_() EDisplayButtonEvent {
	if x != nil {
		return x.Reset_
	}
	return EDisplayButtonEvent_DISPLAY_BUTTON_EVENT_PRESSED
}

// RespRemoteDisplayButtonEvent
type RespRemoteDisplayButtonEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The simple result of the Remote Display Button Status command
	Result        settings.EWriteResult `protobuf:"varint,1,opt,name=result,proto3,enum=settings.EWriteResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRemoteDisplayButtonEvent) Reset() {
	*x = RespRemoteDisplayButtonEvent{}
	mi := &file_cmd_resp_stats_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRemoteDisplayButtonEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRemoteDisplayButtonEvent) ProtoMessage() {}

func (x *RespRemoteDisplayButtonEvent) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_stats_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRemoteDisplayButtonEvent.ProtoReflect.Descriptor instead.
func (*RespRemoteDisplayButtonEvent) Descriptor() ([]byte, []int) {
	return file_cmd_resp_stats_proto_rawDescGZIP(), []int{25}
}

func (x *RespRemoteDisplayButtonEvent) GetResult() settings.EWriteResult {
	if x != nil {
		return x.Result
	}
	return settings.EWriteResult(0)
}

var File_cmd_resp_stats_proto protoreflect.FileDescriptor

const file_cmd_resp_stats_proto_rawDesc = "" +
	"\n" +
	"\x14cmd_resp_stats.proto\x12\x0ecmd_resp_stats\x1a\vbasic.proto\x1a\x0emon_logs.proto\x1a\x0esettings.proto\"2\n" +
	"\x16CmdReadPort1Statistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xb6\x05\n" +
	"\x17RespReadPort1Statistics\x12'\n" +
	"\x10frame_1_rx_count\x18\x01 \x01(\rR\rframe1RxCount\x12'\n" +
	"\x10frame_3_rx_count\x18\x02 \x01(\rR\rframe3RxCount\x12'\n" +
	"\x10frame_9_rx_count\x18\x03 \x01(\rR\rframe9RxCount\x125\n" +
	"\x17short_frame_error_count\x18\x04 \x01(\rR\x14shortFrameErrorCount\x127\n" +
	"\x18control_byte_error_count\x18\x05 \x01(\rR\x15controlByteErrorCount\x12&\n" +
	"\x0fcrc_error_count\x18\x06 \x01(\rR\rcrcErrorCount\x123\n" +
	"\x16idle_state_error_count\x18\a \x01(\rR\x13idleStateErrorCount\x12.\n" +
	"\x13framing_error_count\x18\b \x01(\rR\x11framingErrorCount\x123\n" +
	"\x16long_frame_error_count\x18\t \x01(\rR\x13longFrameErrorCount\x129\n" +
	"\x19frame_timeout_error_count\x18\n" +
	" \x01(\rR\x16frameTimeoutErrorCount\x129\n" +
	"\x19unknown_frame_error_count\x18\v \x01(\rR\x16unknownFrameErrorCount\x120\n" +
	"\x14unknown_frame_number\x18\f \x01(\rR\x12unknownFrameNumber\x12F\n" +
	"\x14last_clear_timestamp\x18\r \x01(\v2\x14.basic.LocalDateTimeR\x12lastClearTimestamp\"4\n" +
	"\x18CmdReadDataKeyStatistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\x82\x03\n" +
	"\x19RespReadDataKeyStatistics\x125\n" +
	"\x06source\x18\x01 \x01(\x0e2\x1d.settings.EConfigDataLocationR\x06source\x12\x1d\n" +
	"\n" +
	"read_count\x18\x02 \x01(\rR\treadCount\x12'\n" +
	"\x0fremoved_seconds\x18\x03 \x01(\rR\x0eremovedSeconds\x12&\n" +
	"\x0fcrc_error_count\x18\x04 \x01(\rR\rcrcErrorCount\x12<\n" +
	"\x1bfaults_from_bad_reads_count\x18\x05 \x01(\rR\x17faultsFromBadReadsCount\x128\n" +
	"\x06errors\x18\x06 \x01(\v2 .mon_logs.DataKeyErrorCodeBitmapR\x06errors\x12F\n" +
	"\x14last_clear_timestamp\x18\a \x01(\v2\x14.basic.LocalDateTimeR\x12lastClearTimestamp\"?\n" +
	"#CmdReadMainToIsolatedCommStatistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xe1\x03\n" +
	"$RespReadMainToIsolatedCommStatistics\x12\x1d\n" +
	"\n" +
	"sent_count\x18\x01 \x01(\rR\tsentCount\x12%\n" +
	"\x0ereceived_count\x18\x02 \x01(\rR\rreceivedCount\x12:\n" +
	"\x19unexpected_received_count\x18\x03 \x01(\rR\x17unexpectedReceivedCount\x122\n" +
	"\x15checksum_errors_count\x18\x04 \x01(\rR\x13checksumErrorsCount\x127\n" +
	"\x18data_length_errors_count\x18\x05 \x01(\rR\x15dataLengthErrorsCount\x120\n" +
	"\x14timeout_errors_count\x18\x06 \x01(\rR\x12timeoutErrorsCount\x12(\n" +
	"\x10uart_reset_count\x18\a \x01(\rR\x0euartResetCount\x12&\n" +
	"\x0fbad_frame_count\x18\b \x01(\rR\rbadFrameCount\x12F\n" +
	"\x14last_clear_timestamp\x18\t \x01(\v2\x14.basic.LocalDateTimeR\x12lastClearTimestamp\">\n" +
	"\"CmdReadMainToDisplayCommStatistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xe0\x03\n" +
	"#RespReadMainToDisplayCommStatistics\x12\x1d\n" +
	"\n" +
	"sent_count\x18\x01 \x01(\rR\tsentCount\x12%\n" +
	"\x0ereceived_count\x18\x02 \x01(\rR\rreceivedCount\x12:\n" +
	"\x19unexpected_received_count\x18\x03 \x01(\rR\x17unexpectedReceivedCount\x122\n" +
	"\x15checksum_errors_count\x18\x04 \x01(\rR\x13checksumErrorsCount\x127\n" +
	"\x18data_length_errors_count\x18\x05 \x01(\rR\x15dataLengthErrorsCount\x120\n" +
	"\x14timeout_errors_count\x18\x06 \x01(\rR\x12timeoutErrorsCount\x12(\n" +
	"\x10uart_reset_count\x18\a \x01(\rR\x0euartResetCount\x12&\n" +
	"\x0fbad_frame_count\x18\b \x01(\rR\rbadFrameCount\x12F\n" +
	"\x14last_clear_timestamp\x18\t \x01(\v2\x14.basic.LocalDateTimeR\x12lastClearTimestamp\"<\n" +
	" CmdReadMainToCommsCommStatistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xd4\x03\n" +
	"!RespReadMainToCommsCommStatistics\x12\x1d\n" +
	"\n" +
	"sent_count\x18\x01 \x01(\rR\tsentCount\x12%\n" +
	"\x0ereceived_count\x18\x02 \x01(\rR\rreceivedCount\x124\n" +
	"\x16unknown_received_count\x18\x03 \x01(\rR\x14unknownReceivedCount\x122\n" +
	"\x15checksum_errors_count\x18\x04 \x01(\rR\x13checksumErrorsCount\x127\n" +
	"\x18data_length_errors_count\x18\x05 \x01(\rR\x15dataLengthErrorsCount\x12,\n" +
	"\x12ping_timeout_count\x18\x06 \x01(\rR\x10pingTimeoutCount\x12(\n" +
	"\x10uart_reset_count\x18\a \x01(\rR\x0euartResetCount\x12&\n" +
	"\x0fbad_frame_count\x18\b \x01(\rR\rbadFrameCount\x12F\n" +
	"\x14last_clear_timestamp\x18\t \x01(\v2\x14.basic.LocalDateTimeR\x12lastClearTimestamp\"<\n" +
	" CmdReadCommsToMainCommStatistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xb4\x03\n" +
	"!RespReadCommsToMainCommStatistics\x12\x1d\n" +
	"\n" +
	"sent_count\x18\x01 \x01(\rR\tsentCount\x12%\n" +
	"\x0ereceived_count\x18\x02 \x01(\rR\rreceivedCount\x12:\n" +
	"\x19unexpected_received_count\x18\x03 \x01(\rR\x17unexpectedReceivedCount\x122\n" +
	"\x15checksum_errors_count\x18\x04 \x01(\rR\x13checksumErrorsCount\x127\n" +
	"\x18data_length_errors_count\x18\x05 \x01(\rR\x15dataLengthErrorsCount\x120\n" +
	"\x14timeout_errors_count\x18\x06 \x01(\rR\x12timeoutErrorsCount\x12&\n" +
	"\x0fbad_frame_count\x18\a \x01(\rR\rbadFrameCount\x12F\n" +
	"\x14last_clear_timestamp\x18\b \x01(\v2\x14.basic.LocalDateTimeR\x12lastClearTimestamp\"2\n" +
	"\x16CmdReadFlashStatistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xdd\x01\n" +
	"\x17RespReadFlashStatistics\x12@\n" +
	"\ffactory_area\x18\x01 \x01(\v2\x1d.settings.FlashAreaStatisticsR\vfactoryArea\x12@\n" +
	"\fgeneral_area\x18\x02 \x01(\v2\x1d.settings.FlashAreaStatisticsR\vgeneralArea\x12>\n" +
	"\vheader_area\x18\x03 \x01(\v2\x1d.settings.FlashAreaStatisticsR\n" +
	"headerArea\"5\n" +
	"\x19CmdReadWatchdogStatistics\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xc8\x04\n" +
	"\x1aRespReadWatchdogStatistics\x12&\n" +
	"\x0fmain_loop_count\x18\x01 \x01(\rR\rmainLoopCount\x12$\n" +
	"\x0etick_1ms_count\x18\x02 \x01(\rR\ftick1msCount\x124\n" +
	"\x16fault_processing_count\x18\x03 \x01(\rR\x14faultProcessingCount\x120\n" +
	"\x14adc_processing_count\x18\x04 \x01(\rR\x12adcProcessingCount\x120\n" +
	"\x14rms_calcuation_count\x18\x05 \x01(\rR\x12rmsCalcuationCount\x12)\n" +
	"\x11comm_mcu_rx_count\x18\x06 \x01(\rR\x0ecommMcuRxCount\x12'\n" +
	"\x10iso_mcu_tx_count\x18\a \x01(\rR\risoMcuTxCount\x121\n" +
	"\x15pcard_dkey_read_count\x18\b \x01(\rR\x12pcardDkeyReadCount\x12(\n" +
	"\x10get_inputs_count\x18\t \x01(\rR\x0egetInputsCount\x12$\n" +
	"\x0eset_leds_count\x18\n" +
	" \x01(\rR\fsetLedsCount\x12/\n" +
	"\x14display_mcu_tx_count\x18\v \x01(\rR\x11displayMcuTxCount\x12:\n" +
	"\x19background_checksum_count\x18\f \x01(\rR\x17backgroundChecksumCount\"9\n" +
	"\x1dCmdReadInternalSupplyVoltages\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xbb\x03\n" +
	"\x1eRespReadInternalSupplyVoltages\x123\n" +
	"\x16ac_mains_present_volts\x18\x01 \x01(\x02R\x13acMainsPresentVolts\x12@\n" +
	"\x11main_mcu_5v_volts\x18\x02 \x01(\v2\x15.basic.NowMinMaxFloatR\x0emainMcu5vVolts\x12B\n" +
	"\x12main_mcu_3v3_volts\x18\x03 \x01(\v2\x15.basic.NowMinMaxFloatR\x0fmainMcu3v3Volts\x12H\n" +
	"\x15main_mcu_neg3v3_volts\x18\x04 \x01(\v2\x15.basic.NowMinMaxFloatR\x12mainMcuNeg3v3Volts\x12H\n" +
	"\x15isolated_mcu_5v_volts\x18\x05 \x01(\v2\x15.basic.NowMinMaxFloatR\x12isolatedMcu5vVolts\x12J\n" +
	"\x16isolated_mcu_3v3_volts\x18\x06 \x01(\v2\x15.basic.NowMinMaxFloatR\x13isolatedMcu3v3Volts\".\n" +
	"\x12CmdGetTimeDatesDst\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xbb\x01\n" +
	"\x13RespGetTimeDatesDst\x12@\n" +
	"\x11present_date_time\x18\x01 \x01(\v2\x14.basic.LocalDateTimeR\x0fpresentDateTime\x12\x1f\n" +
	"\vdst_enabled\x18\x02 \x01(\bR\n" +
	"dstEnabled\x12A\n" +
	"\fdst_settings\x18\x03 \x01(\v2\x1e.basic.DaylightSavingsSettingsR\vdstSettings\"b\n" +
	"\x12CmdClearStatistics\x12L\n" +
	"\x13statistics_to_clear\x18\x01 \x01(\x0e2\x1c.settings.EMonitorStatisticsR\x11statisticsToClear\"\x96\x01\n" +
	"\x13RespClearStatistics\x12K\n" +
	"\x12statistics_cleared\x18\x01 \x01(\x0e2\x1c.settings.EMonitorStatisticsR\x11statisticsCleared\x122\n" +
	"\ttimestamp\x18\x02 \x01(\v2\x14.basic.LocalDateTimeR\ttimestamp\"\xba\x01\n" +
	"\x12CmdSetTimeDatesDst\x12@\n" +
	"\x11present_date_time\x18\x01 \x01(\v2\x14.basic.LocalDateTimeR\x0fpresentDateTime\x12\x1f\n" +
	"\vdst_enabled\x18\x02 \x01(\bR\n" +
	"dstEnabled\x12A\n" +
	"\fdst_settings\x18\x03 \x01(\v2\x1e.basic.DaylightSavingsSettingsR\vdstSettings\"G\n" +
	"\x13RespSetTimeDatesDst\x120\n" +
	"\x06result\x18\x01 \x01(\x0e2\x18.settings.ETimeSetResultR\x06result\"\xe7\x03\n" +
	"\x1bCmdRemoteDisplayButtonEvent\x127\n" +
	"\x04help\x18\x01 \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x04help\x123\n" +
	"\x02up\x18\x02 \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x02up\x127\n" +
	"\x04down\x18\x03 \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x04down\x129\n" +
	"\x05enter\x18\x04 \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x05enter\x127\n" +
	"\x04back\x18\x05 \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x04back\x127\n" +
	"\x04left\x18\x06 \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x04left\x129\n" +
	"\x05right\x18\a \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x05right\x129\n" +
	"\x05reset\x18\b \x01(\x0e2#.cmd_resp_stats.EDisplayButtonEventR\x05reset\"N\n" +
	"\x1cRespRemoteDisplayButtonEvent\x12.\n" +
	"\x06result\x18\x01 \x01(\x0e2\x16.settings.EWriteResultR\x06result*W\n" +
	"\x13EDisplayButtonEvent\x12 \n" +
	"\x1cDISPLAY_BUTTON_EVENT_PRESSED\x10\x00\x12\x1e\n" +
	"\x19DISPLAY_BUTTON_EVENT_IDLE\x10\xff\x01b\x06proto3"

var (
	file_cmd_resp_stats_proto_rawDescOnce sync.Once
	file_cmd_resp_stats_proto_rawDescData []byte
)

func file_cmd_resp_stats_proto_rawDescGZIP() []byte {
	file_cmd_resp_stats_proto_rawDescOnce.Do(func() {
		file_cmd_resp_stats_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cmd_resp_stats_proto_rawDesc), len(file_cmd_resp_stats_proto_rawDesc)))
	})
	return file_cmd_resp_stats_proto_rawDescData
}

var file_cmd_resp_stats_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cmd_resp_stats_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_cmd_resp_stats_proto_goTypes = []any{
	(EDisplayButtonEvent)(0),                     // 0: cmd_resp_stats.EDisplayButtonEvent
	(*CmdReadPort1Statistics)(nil),               // 1: cmd_resp_stats.CmdReadPort1Statistics
	(*RespReadPort1Statistics)(nil),              // 2: cmd_resp_stats.RespReadPort1Statistics
	(*CmdReadDataKeyStatistics)(nil),             // 3: cmd_resp_stats.CmdReadDataKeyStatistics
	(*RespReadDataKeyStatistics)(nil),            // 4: cmd_resp_stats.RespReadDataKeyStatistics
	(*CmdReadMainToIsolatedCommStatistics)(nil),  // 5: cmd_resp_stats.CmdReadMainToIsolatedCommStatistics
	(*RespReadMainToIsolatedCommStatistics)(nil), // 6: cmd_resp_stats.RespReadMainToIsolatedCommStatistics
	(*CmdReadMainToDisplayCommStatistics)(nil),   // 7: cmd_resp_stats.CmdReadMainToDisplayCommStatistics
	(*RespReadMainToDisplayCommStatistics)(nil),  // 8: cmd_resp_stats.RespReadMainToDisplayCommStatistics
	(*CmdReadMainToCommsCommStatistics)(nil),     // 9: cmd_resp_stats.CmdReadMainToCommsCommStatistics
	(*RespReadMainToCommsCommStatistics)(nil),    // 10: cmd_resp_stats.RespReadMainToCommsCommStatistics
	(*CmdReadCommsToMainCommStatistics)(nil),     // 11: cmd_resp_stats.CmdReadCommsToMainCommStatistics
	(*RespReadCommsToMainCommStatistics)(nil),    // 12: cmd_resp_stats.RespReadCommsToMainCommStatistics
	(*CmdReadFlashStatistics)(nil),               // 13: cmd_resp_stats.CmdReadFlashStatistics
	(*RespReadFlashStatistics)(nil),              // 14: cmd_resp_stats.RespReadFlashStatistics
	(*CmdReadWatchdogStatistics)(nil),            // 15: cmd_resp_stats.CmdReadWatchdogStatistics
	(*RespReadWatchdogStatistics)(nil),           // 16: cmd_resp_stats.RespReadWatchdogStatistics
	(*CmdReadInternalSupplyVoltages)(nil),        // 17: cmd_resp_stats.CmdReadInternalSupplyVoltages
	(*RespReadInternalSupplyVoltages)(nil),       // 18: cmd_resp_stats.RespReadInternalSupplyVoltages
	(*CmdGetTimeDatesDst)(nil),                   // 19: cmd_resp_stats.CmdGetTimeDatesDst
	(*RespGetTimeDatesDst)(nil),                  // 20: cmd_resp_stats.RespGetTimeDatesDst
	(*CmdClearStatistics)(nil),                   // 21: cmd_resp_stats.CmdClearStatistics
	(*RespClearStatistics)(nil),                  // 22: cmd_resp_stats.RespClearStatistics
	(*CmdSetTimeDatesDst)(nil),                   // 23: cmd_resp_stats.CmdSetTimeDatesDst
	(*RespSetTimeDatesDst)(nil),                  // 24: cmd_resp_stats.RespSetTimeDatesDst
	(*CmdRemoteDisplayButtonEvent)(nil),          // 25: cmd_resp_stats.CmdRemoteDisplayButtonEvent
	(*RespRemoteDisplayButtonEvent)(nil),         // 26: cmd_resp_stats.RespRemoteDisplayButtonEvent
	(*basic.LocalDateTime)(nil),                  // 27: basic.LocalDateTime
	(settings.EConfigDataLocation)(0),            // 28: settings.EConfigDataLocation
	(*mon_logs.DataKeyErrorCodeBitmap)(nil),      // 29: mon_logs.DataKeyErrorCodeBitmap
	(*settings.FlashAreaStatistics)(nil),         // 30: settings.FlashAreaStatistics
	(*basic.NowMinMaxFloat)(nil),                 // 31: basic.NowMinMaxFloat
	(*basic.DaylightSavingsSettings)(nil),        // 32: basic.DaylightSavingsSettings
	(settings.EMonitorStatistics)(0),             // 33: settings.EMonitorStatistics
	(settings.ETimeSetResult)(0),                 // 34: settings.ETimeSetResult
	(settings.EWriteResult)(0),                   // 35: settings.EWriteResult
}
var file_cmd_resp_stats_proto_depIdxs = []int32{
	27, // 0: cmd_resp_stats.RespReadPort1Statistics.last_clear_timestamp:type_name -> basic.LocalDateTime
	28, // 1: cmd_resp_stats.RespReadDataKeyStatistics.source:type_name -> settings.EConfigDataLocation
	29, // 2: cmd_resp_stats.RespReadDataKeyStatistics.errors:type_name -> mon_logs.DataKeyErrorCodeBitmap
	27, // 3: cmd_resp_stats.RespReadDataKeyStatistics.last_clear_timestamp:type_name -> basic.LocalDateTime
	27, // 4: cmd_resp_stats.RespReadMainToIsolatedCommStatistics.last_clear_timestamp:type_name -> basic.LocalDateTime
	27, // 5: cmd_resp_stats.RespReadMainToDisplayCommStatistics.last_clear_timestamp:type_name -> basic.LocalDateTime
	27, // 6: cmd_resp_stats.RespReadMainToCommsCommStatistics.last_clear_timestamp:type_name -> basic.LocalDateTime
	27, // 7: cmd_resp_stats.RespReadCommsToMainCommStatistics.last_clear_timestamp:type_name -> basic.LocalDateTime
	30, // 8: cmd_resp_stats.RespReadFlashStatistics.factory_area:type_name -> settings.FlashAreaStatistics
	30, // 9: cmd_resp_stats.RespReadFlashStatistics.general_area:type_name -> settings.FlashAreaStatistics
	30, // 10: cmd_resp_stats.RespReadFlashStatistics.header_area:type_name -> settings.FlashAreaStatistics
	31, // 11: cmd_resp_stats.RespReadInternalSupplyVoltages.main_mcu_5v_volts:type_name -> basic.NowMinMaxFloat
	31, // 12: cmd_resp_stats.RespReadInternalSupplyVoltages.main_mcu_3v3_volts:type_name -> basic.NowMinMaxFloat
	31, // 13: cmd_resp_stats.RespReadInternalSupplyVoltages.main_mcu_neg3v3_volts:type_name -> basic.NowMinMaxFloat
	31, // 14: cmd_resp_stats.RespReadInternalSupplyVoltages.isolated_mcu_5v_volts:type_name -> basic.NowMinMaxFloat
	31, // 15: cmd_resp_stats.RespReadInternalSupplyVoltages.isolated_mcu_3v3_volts:type_name -> basic.NowMinMaxFloat
	27, // 16: cmd_resp_stats.RespGetTimeDatesDst.present_date_time:type_name -> basic.LocalDateTime
	32, // 17: cmd_resp_stats.RespGetTimeDatesDst.dst_settings:type_name -> basic.DaylightSavingsSettings
	33, // 18: cmd_resp_stats.CmdClearStatistics.statistics_to_clear:type_name -> settings.EMonitorStatistics
	33, // 19: cmd_resp_stats.RespClearStatistics.statistics_cleared:type_name -> settings.EMonitorStatistics
	27, // 20: cmd_resp_stats.RespClearStatistics.timestamp:type_name -> basic.LocalDateTime
	27, // 21: cmd_resp_stats.CmdSetTimeDatesDst.present_date_time:type_name -> basic.LocalDateTime
	32, // 22: cmd_resp_stats.CmdSetTimeDatesDst.dst_settings:type_name -> basic.DaylightSavingsSettings
	34, // 23: cmd_resp_stats.RespSetTimeDatesDst.result:type_name -> settings.ETimeSetResult
	0,  // 24: cmd_resp_stats.CmdRemoteDisplayButtonEvent.help:type_name -> cmd_resp_stats.EDisplayButtonEvent
	0,  // 25: cmd_resp_stats.CmdRemoteDisplayButtonEvent.up:type_name -> cmd_resp_stats.EDisplayButtonEvent
	0,  // 26: cmd_resp_stats.CmdRemoteDisplayButtonEvent.down:type_name -> cmd_resp_stats.EDisplayButtonEvent
	0,  // 27: cmd_resp_stats.CmdRemoteDisplayButtonEvent.enter:type_name -> cmd_resp_stats.EDisplayButtonEvent
	0,  // 28: cmd_resp_stats.CmdRemoteDisplayButtonEvent.back:type_name -> cmd_resp_stats.EDisplayButtonEvent
	0,  // 29: cmd_resp_stats.CmdRemoteDisplayButtonEvent.left:type_name -> cmd_resp_stats.EDisplayButtonEvent
	0,  // 30: cmd_resp_stats.CmdRemoteDisplayButtonEvent.right:type_name -> cmd_resp_stats.EDisplayButtonEvent
	0,  // 31: cmd_resp_stats.CmdRemoteDisplayButtonEvent.reset:type_name -> cmd_resp_stats.EDisplayButtonEvent
	35, // 32: cmd_resp_stats.RespRemoteDisplayButtonEvent.result:type_name -> settings.EWriteResult
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_cmd_resp_stats_proto_init() }
func file_cmd_resp_stats_proto_init() {
	if File_cmd_resp_stats_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cmd_resp_stats_proto_rawDesc), len(file_cmd_resp_stats_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_resp_stats_proto_goTypes,
		DependencyIndexes: file_cmd_resp_stats_proto_depIdxs,
		EnumInfos:         file_cmd_resp_stats_proto_enumTypes,
		MessageInfos:      file_cmd_resp_stats_proto_msgTypes,
	}.Build()
	File_cmd_resp_stats_proto = out.File
	file_cmd_resp_stats_proto_goTypes = nil
	file_cmd_resp_stats_proto_depIdxs = nil
}
