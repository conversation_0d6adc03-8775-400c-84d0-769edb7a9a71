//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  CMD_RESP_LOGS
//Command (from app) and response (from monitor) message formats for logging related commands.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: cmd_resp_logs.proto

package cmd_resp_logs

import (
	audit_logs "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/audit_logs"
	mon_logs "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CmdRequestLogCounts is used to request the number of entries in monitor logs
type CmdRequestLogCounts struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The monitor log type(s) to request.  May be MON_LOG_ALL to get entry counts for all logs
	Log           []mon_logs.EMonitorLogType `protobuf:"varint,1,rep,packed,name=log,proto3,enum=mon_logs.EMonitorLogType" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRequestLogCounts) Reset() {
	*x = CmdRequestLogCounts{}
	mi := &file_cmd_resp_logs_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRequestLogCounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRequestLogCounts) ProtoMessage() {}

func (x *CmdRequestLogCounts) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRequestLogCounts.ProtoReflect.Descriptor instead.
func (*CmdRequestLogCounts) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{0}
}

func (x *CmdRequestLogCounts) GetLog() []mon_logs.EMonitorLogType {
	if x != nil {
		return x.Log
	}
	return nil
}

//	RespRequestLogCounts returns the entry counts for the requested logs. The format and size of
//
// a single log entry in the monitor log storage is also returned.
type RespRequestLogCounts struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A field repeat for each log in the command request.  If the command request was for
	// MON_LOG_ALL, then there is a repeated field for every available log.
	//
	// NOTE: for MON_LOG_FAULT_MEASUREMENT, MON_LOG_FAULT_SEQUENCE, & MON_LOG_FAULT_FACTS the entry
	//
	//	count is the maximum number of entries that may be returned for any particular Fault ID.
	Log           []*mon_logs.LogEntryCount `protobuf:"bytes,1,rep,name=log,proto3" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRequestLogCounts) Reset() {
	*x = RespRequestLogCounts{}
	mi := &file_cmd_resp_logs_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRequestLogCounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRequestLogCounts) ProtoMessage() {}

func (x *RespRequestLogCounts) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRequestLogCounts.ProtoReflect.Descriptor instead.
func (*RespRequestLogCounts) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{1}
}

func (x *RespRequestLogCounts) GetLog() []*mon_logs.LogEntryCount {
	if x != nil {
		return x.Log
	}
	return nil
}

// CmdRequestLogClear is used to clear one or more monitor logs
type CmdRequestLogClear struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The monitor log type(s) to clear.  May be MON_LOG_ALL to clear all logs.
	//
	// NOTE: Clearing the MON_LOG_FAULT_HEADER log will also clear the MON_LOG_FAULT_MEASUREMENT,
	//
	//	MON_LOG_FAULT_SEQUENCE, and MON_LOG_FAULT_FACTS logs.  Attempting to clear the
	//	MON_LOG_FAULT_MEASUREMENT, MON_LOG_FAULT_SEQUENCE, or MON_LOG_FAULT_FACTS logs will
	//	have no effect and return an error.
	Log           []mon_logs.EMonitorLogType `protobuf:"varint,1,rep,packed,name=log,proto3,enum=mon_logs.EMonitorLogType" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRequestLogClear) Reset() {
	*x = CmdRequestLogClear{}
	mi := &file_cmd_resp_logs_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRequestLogClear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRequestLogClear) ProtoMessage() {}

func (x *CmdRequestLogClear) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRequestLogClear.ProtoReflect.Descriptor instead.
func (*CmdRequestLogClear) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{2}
}

func (x *CmdRequestLogClear) GetLog() []mon_logs.EMonitorLogType {
	if x != nil {
		return x.Log
	}
	return nil
}

//	RespRequestLogClear returns the cleared entry counts for the cleared logs. The format and size
//
// values are not used in this response. (Always 0)
type RespRequestLogClear struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A field repeat for each log in the command request.  If the command request was for
	// MON_LOG_ALL, then there is a repeated field for every available log.
	Log           []*mon_logs.LogEntryCount `protobuf:"bytes,1,rep,name=log,proto3" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRequestLogClear) Reset() {
	*x = RespRequestLogClear{}
	mi := &file_cmd_resp_logs_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRequestLogClear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRequestLogClear) ProtoMessage() {}

func (x *RespRequestLogClear) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRequestLogClear.ProtoReflect.Descriptor instead.
func (*RespRequestLogClear) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{3}
}

func (x *RespRequestLogClear) GetLog() []*mon_logs.LogEntryCount {
	if x != nil {
		return x.Log
	}
	return nil
}

//	CmdRequestLogEntries is used to request entries from a monitor log
//
// NOTE: large log requests may be returned in multiple response messages.
type CmdRequestLogEntries struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The monitor log to request entries from.  Entries may only be request from one log at a time.
	// A request for MON_LOG_ALL is illegal and will return an error in the response.
	Log mon_logs.EMonitorLogType `protobuf:"varint,1,opt,name=log,proto3,enum=mon_logs.EMonitorLogType" json:"log,omitempty"`
	// For log types MON_LOG_FAULT_MEASUREMENT, MON_LOG_FAULT_SEQUENCE, or MON_LOG_FAULT_FACTS
	// the associated fault_id is required.
	FaultId *uint32 `protobuf:"varint,2,opt,name=fault_id,json=faultId,proto3,oneof" json:"fault_id,omitempty"`
	// The log entry ID at which to start retrieving entries. The first/oldest entry is value '1'. HOWEVER,
	// if the log has become full and starts deleting older entries, they are not renumbered.  Thus, entry
	// 1 (and up to the oldest undeleted entry) may not exist.
	StartingEntryId uint32 `protobuf:"varint,3,opt,name=starting_entry_id,json=startingEntryId,proto3" json:"starting_entry_id,omitempty"`
	// The number of entries to return, beginning with the starting entry ID
	EntryReturnCount uint32 `protobuf:"varint,4,opt,name=entry_return_count,json=entryReturnCount,proto3" json:"entry_return_count,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CmdRequestLogEntries) Reset() {
	*x = CmdRequestLogEntries{}
	mi := &file_cmd_resp_logs_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRequestLogEntries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRequestLogEntries) ProtoMessage() {}

func (x *CmdRequestLogEntries) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRequestLogEntries.ProtoReflect.Descriptor instead.
func (*CmdRequestLogEntries) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{4}
}

func (x *CmdRequestLogEntries) GetLog() mon_logs.EMonitorLogType {
	if x != nil {
		return x.Log
	}
	return mon_logs.EMonitorLogType(0)
}

func (x *CmdRequestLogEntries) GetFaultId() uint32 {
	if x != nil && x.FaultId != nil {
		return *x.FaultId
	}
	return 0
}

func (x *CmdRequestLogEntries) GetStartingEntryId() uint32 {
	if x != nil {
		return x.StartingEntryId
	}
	return 0
}

func (x *CmdRequestLogEntries) GetEntryReturnCount() uint32 {
	if x != nil {
		return x.EntryReturnCount
	}
	return 0
}

//	RespRequestLogEntries returns the requested log entries.
//
// NOTE: Large logs and/or large entry count requests may be returned in more than 1 response message.
// All response messages to a single CmdRequestLogEntries will have the same request_id.
// Fewer entries than requested in CmdRequestLogEntries.entry_return_count may be returned if
// more entries were requested than exist in the log.
type RespRequestLogEntries struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The monitor log from which the entries were requested.
	Log mon_logs.EMonitorLogType `protobuf:"varint,1,opt,name=log,proto3,enum=mon_logs.EMonitorLogType" json:"log,omitempty"`
	// The total number of messages that comprise the response.  If all the requested entries fit in single message
	// this value will be 1
	TotalMessageCount uint32 `protobuf:"varint,2,opt,name=total_message_count,json=totalMessageCount,proto3" json:"total_message_count,omitempty"`
	// The message sequence count.  This is the number of the response message out of the total_message_count
	// The first response message will have value 1 and the last message value will equal total_message_count
	MessageSequenceCount uint32 `protobuf:"varint,3,opt,name=message_sequence_count,json=messageSequenceCount,proto3" json:"message_sequence_count,omitempty"`
	// The log entries.  Each of these messages contains a repeated field of the log entry message.
	// The total number of messages for a response and the number of entries in each message are dependant on the
	// size of the requested log entry, and the number of requested entries.
	// A request for a handful of entries from a log with large entries, like the config log, may be returned in
	// several response messages.  However, a request for dozens of entries from a small log, like the clock log,
	// may all be returned in a single response message.
	//
	// Types that are valid to be assigned to Entries:
	//
	//	*RespRequestLogEntries_PowerLog
	//	*RespRequestLogEntries_ResetLog
	//	*RespRequestLogEntries_ClockLog
	//	*RespRequestLogEntries_ConfigLog
	//	*RespRequestLogEntries_Port1Log
	//	*RespRequestLogEntries_FaultHdrLog
	//	*RespRequestLogEntries_FaultMsrLog
	//	*RespRequestLogEntries_FaultSeqLog
	//	*RespRequestLogEntries_FaultFactsLog
	//	*RespRequestLogEntries_AlarmLog
	Entries       isRespRequestLogEntries_Entries `protobuf_oneof:"entries"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRequestLogEntries) Reset() {
	*x = RespRequestLogEntries{}
	mi := &file_cmd_resp_logs_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRequestLogEntries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRequestLogEntries) ProtoMessage() {}

func (x *RespRequestLogEntries) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRequestLogEntries.ProtoReflect.Descriptor instead.
func (*RespRequestLogEntries) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{5}
}

func (x *RespRequestLogEntries) GetLog() mon_logs.EMonitorLogType {
	if x != nil {
		return x.Log
	}
	return mon_logs.EMonitorLogType(0)
}

func (x *RespRequestLogEntries) GetTotalMessageCount() uint32 {
	if x != nil {
		return x.TotalMessageCount
	}
	return 0
}

func (x *RespRequestLogEntries) GetMessageSequenceCount() uint32 {
	if x != nil {
		return x.MessageSequenceCount
	}
	return 0
}

func (x *RespRequestLogEntries) GetEntries() isRespRequestLogEntries_Entries {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *RespRequestLogEntries) GetPowerLog() *mon_logs.PowerLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_PowerLog); ok {
			return x.PowerLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetResetLog() *mon_logs.ResetLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_ResetLog); ok {
			return x.ResetLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetClockLog() *mon_logs.ClockLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_ClockLog); ok {
			return x.ClockLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetConfigLog() *mon_logs.ConfigLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_ConfigLog); ok {
			return x.ConfigLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetPort1Log() *mon_logs.Port1LogLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_Port1Log); ok {
			return x.Port1Log
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetFaultHdrLog() *mon_logs.FaultHeaderLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_FaultHdrLog); ok {
			return x.FaultHdrLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetFaultMsrLog() *mon_logs.FaultMeasurementLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_FaultMsrLog); ok {
			return x.FaultMsrLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetFaultSeqLog() *mon_logs.FaultSequenceLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_FaultSeqLog); ok {
			return x.FaultSeqLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetFaultFactsLog() *mon_logs.FaultFactsLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_FaultFactsLog); ok {
			return x.FaultFactsLog
		}
	}
	return nil
}

func (x *RespRequestLogEntries) GetAlarmLog() *mon_logs.AlarmLogMultipleEntriesMmu {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestLogEntries_AlarmLog); ok {
			return x.AlarmLog
		}
	}
	return nil
}

type isRespRequestLogEntries_Entries interface {
	isRespRequestLogEntries_Entries()
}

type RespRequestLogEntries_PowerLog struct {
	PowerLog *mon_logs.PowerLogMultipleEntriesMmu `protobuf:"bytes,4,opt,name=power_log,json=powerLog,proto3,oneof"`
}

type RespRequestLogEntries_ResetLog struct {
	ResetLog *mon_logs.ResetLogMultipleEntriesMmu `protobuf:"bytes,5,opt,name=reset_log,json=resetLog,proto3,oneof"`
}

type RespRequestLogEntries_ClockLog struct {
	ClockLog *mon_logs.ClockLogMultipleEntriesMmu `protobuf:"bytes,6,opt,name=clock_log,json=clockLog,proto3,oneof"`
}

type RespRequestLogEntries_ConfigLog struct {
	ConfigLog *mon_logs.ConfigLogMultipleEntriesMmu `protobuf:"bytes,7,opt,name=config_log,json=configLog,proto3,oneof"`
}

type RespRequestLogEntries_Port1Log struct {
	Port1Log *mon_logs.Port1LogLogMultipleEntriesMmu `protobuf:"bytes,8,opt,name=port1_log,json=port1Log,proto3,oneof"`
}

type RespRequestLogEntries_FaultHdrLog struct {
	FaultHdrLog *mon_logs.FaultHeaderLogMultipleEntriesMmu `protobuf:"bytes,9,opt,name=fault_hdr_log,json=faultHdrLog,proto3,oneof"`
}

type RespRequestLogEntries_FaultMsrLog struct {
	FaultMsrLog *mon_logs.FaultMeasurementLogMultipleEntriesMmu `protobuf:"bytes,10,opt,name=fault_msr_log,json=faultMsrLog,proto3,oneof"`
}

type RespRequestLogEntries_FaultSeqLog struct {
	FaultSeqLog *mon_logs.FaultSequenceLogMultipleEntriesMmu `protobuf:"bytes,11,opt,name=fault_seq_log,json=faultSeqLog,proto3,oneof"`
}

type RespRequestLogEntries_FaultFactsLog struct {
	FaultFactsLog *mon_logs.FaultFactsLogMultipleEntriesMmu `protobuf:"bytes,12,opt,name=fault_facts_log,json=faultFactsLog,proto3,oneof"`
}

type RespRequestLogEntries_AlarmLog struct {
	AlarmLog *mon_logs.AlarmLogMultipleEntriesMmu `protobuf:"bytes,13,opt,name=alarm_log,json=alarmLog,proto3,oneof"`
}

func (*RespRequestLogEntries_PowerLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_ResetLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_ClockLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_ConfigLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_Port1Log) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_FaultHdrLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_FaultMsrLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_FaultSeqLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_FaultFactsLog) isRespRequestLogEntries_Entries() {}

func (*RespRequestLogEntries_AlarmLog) isRespRequestLogEntries_Entries() {}

//	CmdRequestAuditLogCounts is used to request the number of entries in audit logs, as well as
//
// the log max length setting.
type CmdRequestAuditLogCounts struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The audit log type(s) to request.
	Log           []audit_logs.EAuditLogType `protobuf:"varint,1,rep,packed,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRequestAuditLogCounts) Reset() {
	*x = CmdRequestAuditLogCounts{}
	mi := &file_cmd_resp_logs_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRequestAuditLogCounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRequestAuditLogCounts) ProtoMessage() {}

func (x *CmdRequestAuditLogCounts) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRequestAuditLogCounts.ProtoReflect.Descriptor instead.
func (*CmdRequestAuditLogCounts) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{6}
}

func (x *CmdRequestAuditLogCounts) GetLog() []audit_logs.EAuditLogType {
	if x != nil {
		return x.Log
	}
	return nil
}

// RespRequestAuditLogCounts returns the entry counts for the requested logs.
type RespRequestAuditLogCounts struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// If true, then the audit logs have reached the maximum allowed file system usage for logfiles.
	// Linear logs will be replacing the oldest entries with any new entries, and circular logs will
	// be limited to their current size.
	LogsSpaceLimited bool `protobuf:"varint,2,opt,name=logs_space_limited,json=logsSpaceLimited,proto3" json:"logs_space_limited,omitempty"`
	// A field repeat for each log in the command request.
	Log           []*audit_logs.AuditLogEntryCount `protobuf:"bytes,1,rep,name=log,proto3" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRequestAuditLogCounts) Reset() {
	*x = RespRequestAuditLogCounts{}
	mi := &file_cmd_resp_logs_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRequestAuditLogCounts) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRequestAuditLogCounts) ProtoMessage() {}

func (x *RespRequestAuditLogCounts) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRequestAuditLogCounts.ProtoReflect.Descriptor instead.
func (*RespRequestAuditLogCounts) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{7}
}

func (x *RespRequestAuditLogCounts) GetLogsSpaceLimited() bool {
	if x != nil {
		return x.LogsSpaceLimited
	}
	return false
}

func (x *RespRequestAuditLogCounts) GetLog() []*audit_logs.AuditLogEntryCount {
	if x != nil {
		return x.Log
	}
	return nil
}

// CmdRequestAuditLogClear is used to clear one or more audit logs
type CmdRequestAuditLogClear struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The audit log type(s) to clear.
	Log           []audit_logs.EAuditLogType `protobuf:"varint,1,rep,packed,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRequestAuditLogClear) Reset() {
	*x = CmdRequestAuditLogClear{}
	mi := &file_cmd_resp_logs_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRequestAuditLogClear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRequestAuditLogClear) ProtoMessage() {}

func (x *CmdRequestAuditLogClear) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRequestAuditLogClear.ProtoReflect.Descriptor instead.
func (*CmdRequestAuditLogClear) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{8}
}

func (x *CmdRequestAuditLogClear) GetLog() []audit_logs.EAuditLogType {
	if x != nil {
		return x.Log
	}
	return nil
}

//	RespRequestAuditLogClear returns the list of cleared logs.
//
// Use CmdRequestAuditLogCounts to confirm the cleared logs
type RespRequestAuditLogClear struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A field repeat for each log in the command request.
	Log           []audit_logs.EAuditLogType `protobuf:"varint,1,rep,packed,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"` // Max repeat count set in cmd_resp_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRequestAuditLogClear) Reset() {
	*x = RespRequestAuditLogClear{}
	mi := &file_cmd_resp_logs_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRequestAuditLogClear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRequestAuditLogClear) ProtoMessage() {}

func (x *RespRequestAuditLogClear) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRequestAuditLogClear.ProtoReflect.Descriptor instead.
func (*RespRequestAuditLogClear) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{9}
}

func (x *RespRequestAuditLogClear) GetLog() []audit_logs.EAuditLogType {
	if x != nil {
		return x.Log
	}
	return nil
}

//	CmdRequestAuditLogReset is used to clear one audit log and change the log maximum length setting.
//
// Use CmdRequestAuditLogCounts to confirm the reset log
type CmdRequestAuditLogReset struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The audit log type to reset.
	Log audit_logs.EAuditLogType `protobuf:"varint,1,opt,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"`
	// The maximum number of entries to limit the log to.  If set to `0` then the log length is limited only
	// by file system space.
	// NOTE: The firmware has a minimum length increment (entries per file), and the requested max_entries
	//
	//	log length will be rounded up to the nearest multiple of entries_per_file.
	MaxEntries    uint32 `protobuf:"varint,2,opt,name=max_entries,json=maxEntries,proto3" json:"max_entries,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRequestAuditLogReset) Reset() {
	*x = CmdRequestAuditLogReset{}
	mi := &file_cmd_resp_logs_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRequestAuditLogReset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRequestAuditLogReset) ProtoMessage() {}

func (x *CmdRequestAuditLogReset) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRequestAuditLogReset.ProtoReflect.Descriptor instead.
func (*CmdRequestAuditLogReset) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{10}
}

func (x *CmdRequestAuditLogReset) GetLog() audit_logs.EAuditLogType {
	if x != nil {
		return x.Log
	}
	return audit_logs.EAuditLogType(0)
}

func (x *CmdRequestAuditLogReset) GetMaxEntries() uint32 {
	if x != nil {
		return x.MaxEntries
	}
	return 0
}

// RespRequestAuditLogReset returns the reset log.
type RespRequestAuditLogReset struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Echoes back the reset log.
	Log           audit_logs.EAuditLogType `protobuf:"varint,1,opt,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRequestAuditLogReset) Reset() {
	*x = RespRequestAuditLogReset{}
	mi := &file_cmd_resp_logs_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRequestAuditLogReset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRequestAuditLogReset) ProtoMessage() {}

func (x *RespRequestAuditLogReset) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRequestAuditLogReset.ProtoReflect.Descriptor instead.
func (*RespRequestAuditLogReset) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{11}
}

func (x *RespRequestAuditLogReset) GetLog() audit_logs.EAuditLogType {
	if x != nil {
		return x.Log
	}
	return audit_logs.EAuditLogType(0)
}

//	CmdRequestAuditLogEntries is used to request entries from an audit log
//
// NOTE: large log requests may be returned in multiple response messages.
type CmdRequestAuditLogEntries struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The audit log to request entries from.  Entries may only be request from one log at a time.
	Log audit_logs.EAuditLogType `protobuf:"varint,1,opt,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"`
	// The log entry index at which to start retrieving entries.  The first entry is value '1'.
	StartingEntryIndex uint32 `protobuf:"varint,2,opt,name=starting_entry_index,json=startingEntryIndex,proto3" json:"starting_entry_index,omitempty"`
	// NOTE: Requesting a large number of entries (2000+ although poor network conditions may reduce that)
	// may cause a timeout in the MMU due to the network interface getting backed up.  A combination
	// of the network throughput between the MMU and PC App, and how quickly the PC App is able to
	// receive and process responses contribute to this.  If the PC App is getting
	// WrapperResponse.code = RESP_TIMEOUT errors during log reads, it should use more log read commands
	// requesting fewer entries per command to complete a log read.
	EntryReturnCount uint32 `protobuf:"varint,3,opt,name=entry_return_count,json=entryReturnCount,proto3" json:"entry_return_count,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CmdRequestAuditLogEntries) Reset() {
	*x = CmdRequestAuditLogEntries{}
	mi := &file_cmd_resp_logs_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRequestAuditLogEntries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRequestAuditLogEntries) ProtoMessage() {}

func (x *CmdRequestAuditLogEntries) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRequestAuditLogEntries.ProtoReflect.Descriptor instead.
func (*CmdRequestAuditLogEntries) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{12}
}

func (x *CmdRequestAuditLogEntries) GetLog() audit_logs.EAuditLogType {
	if x != nil {
		return x.Log
	}
	return audit_logs.EAuditLogType(0)
}

func (x *CmdRequestAuditLogEntries) GetStartingEntryIndex() uint32 {
	if x != nil {
		return x.StartingEntryIndex
	}
	return 0
}

func (x *CmdRequestAuditLogEntries) GetEntryReturnCount() uint32 {
	if x != nil {
		return x.EntryReturnCount
	}
	return 0
}

//	RespRequestAuditLogEntries returns the requested log entries.
//
// NOTE: Large logs and/or large entry count requests may be returned in more than 1 response message.
// All response messages to a single CmdRequestAuditLogEntries will have the same wrapper request_id.
// Fewer entries than requested in CmdRequestAuditLogEntries.entry_return_count may be returned if
// more entries were requested than exist in the log.
type RespRequestAuditLogEntries struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The audit log from which the entries were requested.
	Log audit_logs.EAuditLogType `protobuf:"varint,1,opt,name=log,proto3,enum=audit_logs.EAuditLogType" json:"log,omitempty"`
	// The total number of messages that comprise the response.  If all the requested entries fit in single message
	// this value will be 1
	TotalMessageCount uint32 `protobuf:"varint,2,opt,name=total_message_count,json=totalMessageCount,proto3" json:"total_message_count,omitempty"`
	// The message sequence count.  This is the number of the response message out of the total_message_count
	// The first response message will have value 1 and the last message value will equal total_message_count
	MessageSequenceCount uint32 `protobuf:"varint,3,opt,name=message_sequence_count,json=messageSequenceCount,proto3" json:"message_sequence_count,omitempty"`
	// The log entries.  Each of these messages contains a repeated field of the log entry message.
	// The total number of messages for a response and the number of entries in each message are dependant on the
	// size of the requested log entry, and the number of requested entries.
	//
	// Types that are valid to be assigned to Entries:
	//
	//	*RespRequestAuditLogEntries_Format1
	Entries       isRespRequestAuditLogEntries_Entries `protobuf_oneof:"entries"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRequestAuditLogEntries) Reset() {
	*x = RespRequestAuditLogEntries{}
	mi := &file_cmd_resp_logs_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRequestAuditLogEntries) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRequestAuditLogEntries) ProtoMessage() {}

func (x *RespRequestAuditLogEntries) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_logs_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRequestAuditLogEntries.ProtoReflect.Descriptor instead.
func (*RespRequestAuditLogEntries) Descriptor() ([]byte, []int) {
	return file_cmd_resp_logs_proto_rawDescGZIP(), []int{13}
}

func (x *RespRequestAuditLogEntries) GetLog() audit_logs.EAuditLogType {
	if x != nil {
		return x.Log
	}
	return audit_logs.EAuditLogType(0)
}

func (x *RespRequestAuditLogEntries) GetTotalMessageCount() uint32 {
	if x != nil {
		return x.TotalMessageCount
	}
	return 0
}

func (x *RespRequestAuditLogEntries) GetMessageSequenceCount() uint32 {
	if x != nil {
		return x.MessageSequenceCount
	}
	return 0
}

func (x *RespRequestAuditLogEntries) GetEntries() isRespRequestAuditLogEntries_Entries {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *RespRequestAuditLogEntries) GetFormat1() *audit_logs.AuditLogMultipleEntriesComms {
	if x != nil {
		if x, ok := x.Entries.(*RespRequestAuditLogEntries_Format1); ok {
			return x.Format1
		}
	}
	return nil
}

type isRespRequestAuditLogEntries_Entries interface {
	isRespRequestAuditLogEntries_Entries()
}

type RespRequestAuditLogEntries_Format1 struct {
	// in case there is ever another log format.
	Format1 *audit_logs.AuditLogMultipleEntriesComms `protobuf:"bytes,4,opt,name=format1,proto3,oneof"`
}

func (*RespRequestAuditLogEntries_Format1) isRespRequestAuditLogEntries_Entries() {}

var File_cmd_resp_logs_proto protoreflect.FileDescriptor

const file_cmd_resp_logs_proto_rawDesc = "" +
	"\n" +
	"\x13cmd_resp_logs.proto\x12\rcmd_resp_logs\x1a\x0emon_logs.proto\x1a\x10audit_logs.proto\"B\n" +
	"\x13CmdRequestLogCounts\x12+\n" +
	"\x03log\x18\x01 \x03(\x0e2\x19.mon_logs.EMonitorLogTypeR\x03log\"A\n" +
	"\x14RespRequestLogCounts\x12)\n" +
	"\x03log\x18\x01 \x03(\v2\x17.mon_logs.LogEntryCountR\x03log\"A\n" +
	"\x12CmdRequestLogClear\x12+\n" +
	"\x03log\x18\x01 \x03(\x0e2\x19.mon_logs.EMonitorLogTypeR\x03log\"@\n" +
	"\x13RespRequestLogClear\x12)\n" +
	"\x03log\x18\x01 \x03(\v2\x17.mon_logs.LogEntryCountR\x03log\"\xca\x01\n" +
	"\x14CmdRequestLogEntries\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.mon_logs.EMonitorLogTypeR\x03log\x12\x1e\n" +
	"\bfault_id\x18\x02 \x01(\rH\x00R\afaultId\x88\x01\x01\x12*\n" +
	"\x11starting_entry_id\x18\x03 \x01(\rR\x0fstartingEntryId\x12,\n" +
	"\x12entry_return_count\x18\x04 \x01(\rR\x10entryReturnCountB\v\n" +
	"\t_fault_id\"\xab\a\n" +
	"\x15RespRequestLogEntries\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.mon_logs.EMonitorLogTypeR\x03log\x12.\n" +
	"\x13total_message_count\x18\x02 \x01(\rR\x11totalMessageCount\x124\n" +
	"\x16message_sequence_count\x18\x03 \x01(\rR\x14messageSequenceCount\x12C\n" +
	"\tpower_log\x18\x04 \x01(\v2$.mon_logs.PowerLogMultipleEntriesMmuH\x00R\bpowerLog\x12C\n" +
	"\treset_log\x18\x05 \x01(\v2$.mon_logs.ResetLogMultipleEntriesMmuH\x00R\bresetLog\x12C\n" +
	"\tclock_log\x18\x06 \x01(\v2$.mon_logs.ClockLogMultipleEntriesMmuH\x00R\bclockLog\x12F\n" +
	"\n" +
	"config_log\x18\a \x01(\v2%.mon_logs.ConfigLogMultipleEntriesMmuH\x00R\tconfigLog\x12F\n" +
	"\tport1_log\x18\b \x01(\v2'.mon_logs.Port1LogLogMultipleEntriesMmuH\x00R\bport1Log\x12P\n" +
	"\rfault_hdr_log\x18\t \x01(\v2*.mon_logs.FaultHeaderLogMultipleEntriesMmuH\x00R\vfaultHdrLog\x12U\n" +
	"\rfault_msr_log\x18\n" +
	" \x01(\v2/.mon_logs.FaultMeasurementLogMultipleEntriesMmuH\x00R\vfaultMsrLog\x12R\n" +
	"\rfault_seq_log\x18\v \x01(\v2,.mon_logs.FaultSequenceLogMultipleEntriesMmuH\x00R\vfaultSeqLog\x12S\n" +
	"\x0ffault_facts_log\x18\f \x01(\v2).mon_logs.FaultFactsLogMultipleEntriesMmuH\x00R\rfaultFactsLog\x12C\n" +
	"\talarm_log\x18\r \x01(\v2$.mon_logs.AlarmLogMultipleEntriesMmuH\x00R\balarmLogB\t\n" +
	"\aentries\"G\n" +
	"\x18CmdRequestAuditLogCounts\x12+\n" +
	"\x03log\x18\x01 \x03(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\"{\n" +
	"\x19RespRequestAuditLogCounts\x12,\n" +
	"\x12logs_space_limited\x18\x02 \x01(\bR\x10logsSpaceLimited\x120\n" +
	"\x03log\x18\x01 \x03(\v2\x1e.audit_logs.AuditLogEntryCountR\x03log\"F\n" +
	"\x17CmdRequestAuditLogClear\x12+\n" +
	"\x03log\x18\x01 \x03(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\"G\n" +
	"\x18RespRequestAuditLogClear\x12+\n" +
	"\x03log\x18\x01 \x03(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\"g\n" +
	"\x17CmdRequestAuditLogReset\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\x12\x1f\n" +
	"\vmax_entries\x18\x02 \x01(\rR\n" +
	"maxEntries\"G\n" +
	"\x18RespRequestAuditLogReset\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\"\xa8\x01\n" +
	"\x19CmdRequestAuditLogEntries\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\x120\n" +
	"\x14starting_entry_index\x18\x02 \x01(\rR\x12startingEntryIndex\x12,\n" +
	"\x12entry_return_count\x18\x03 \x01(\rR\x10entryReturnCount\"\x80\x02\n" +
	"\x1aRespRequestAuditLogEntries\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.audit_logs.EAuditLogTypeR\x03log\x12.\n" +
	"\x13total_message_count\x18\x02 \x01(\rR\x11totalMessageCount\x124\n" +
	"\x16message_sequence_count\x18\x03 \x01(\rR\x14messageSequenceCount\x12D\n" +
	"\aformat1\x18\x04 \x01(\v2(.audit_logs.AuditLogMultipleEntriesCommsH\x00R\aformat1B\t\n" +
	"\aentriesb\x06proto3"

var (
	file_cmd_resp_logs_proto_rawDescOnce sync.Once
	file_cmd_resp_logs_proto_rawDescData []byte
)

func file_cmd_resp_logs_proto_rawDescGZIP() []byte {
	file_cmd_resp_logs_proto_rawDescOnce.Do(func() {
		file_cmd_resp_logs_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cmd_resp_logs_proto_rawDesc), len(file_cmd_resp_logs_proto_rawDesc)))
	})
	return file_cmd_resp_logs_proto_rawDescData
}

var file_cmd_resp_logs_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_cmd_resp_logs_proto_goTypes = []any{
	(*CmdRequestLogCounts)(nil),                            // 0: cmd_resp_logs.CmdRequestLogCounts
	(*RespRequestLogCounts)(nil),                           // 1: cmd_resp_logs.RespRequestLogCounts
	(*CmdRequestLogClear)(nil),                             // 2: cmd_resp_logs.CmdRequestLogClear
	(*RespRequestLogClear)(nil),                            // 3: cmd_resp_logs.RespRequestLogClear
	(*CmdRequestLogEntries)(nil),                           // 4: cmd_resp_logs.CmdRequestLogEntries
	(*RespRequestLogEntries)(nil),                          // 5: cmd_resp_logs.RespRequestLogEntries
	(*CmdRequestAuditLogCounts)(nil),                       // 6: cmd_resp_logs.CmdRequestAuditLogCounts
	(*RespRequestAuditLogCounts)(nil),                      // 7: cmd_resp_logs.RespRequestAuditLogCounts
	(*CmdRequestAuditLogClear)(nil),                        // 8: cmd_resp_logs.CmdRequestAuditLogClear
	(*RespRequestAuditLogClear)(nil),                       // 9: cmd_resp_logs.RespRequestAuditLogClear
	(*CmdRequestAuditLogReset)(nil),                        // 10: cmd_resp_logs.CmdRequestAuditLogReset
	(*RespRequestAuditLogReset)(nil),                       // 11: cmd_resp_logs.RespRequestAuditLogReset
	(*CmdRequestAuditLogEntries)(nil),                      // 12: cmd_resp_logs.CmdRequestAuditLogEntries
	(*RespRequestAuditLogEntries)(nil),                     // 13: cmd_resp_logs.RespRequestAuditLogEntries
	(mon_logs.EMonitorLogType)(0),                          // 14: mon_logs.EMonitorLogType
	(*mon_logs.LogEntryCount)(nil),                         // 15: mon_logs.LogEntryCount
	(*mon_logs.PowerLogMultipleEntriesMmu)(nil),            // 16: mon_logs.PowerLogMultipleEntriesMmu
	(*mon_logs.ResetLogMultipleEntriesMmu)(nil),            // 17: mon_logs.ResetLogMultipleEntriesMmu
	(*mon_logs.ClockLogMultipleEntriesMmu)(nil),            // 18: mon_logs.ClockLogMultipleEntriesMmu
	(*mon_logs.ConfigLogMultipleEntriesMmu)(nil),           // 19: mon_logs.ConfigLogMultipleEntriesMmu
	(*mon_logs.Port1LogLogMultipleEntriesMmu)(nil),         // 20: mon_logs.Port1LogLogMultipleEntriesMmu
	(*mon_logs.FaultHeaderLogMultipleEntriesMmu)(nil),      // 21: mon_logs.FaultHeaderLogMultipleEntriesMmu
	(*mon_logs.FaultMeasurementLogMultipleEntriesMmu)(nil), // 22: mon_logs.FaultMeasurementLogMultipleEntriesMmu
	(*mon_logs.FaultSequenceLogMultipleEntriesMmu)(nil),    // 23: mon_logs.FaultSequenceLogMultipleEntriesMmu
	(*mon_logs.FaultFactsLogMultipleEntriesMmu)(nil),       // 24: mon_logs.FaultFactsLogMultipleEntriesMmu
	(*mon_logs.AlarmLogMultipleEntriesMmu)(nil),            // 25: mon_logs.AlarmLogMultipleEntriesMmu
	(audit_logs.EAuditLogType)(0),                          // 26: audit_logs.EAuditLogType
	(*audit_logs.AuditLogEntryCount)(nil),                  // 27: audit_logs.AuditLogEntryCount
	(*audit_logs.AuditLogMultipleEntriesComms)(nil),        // 28: audit_logs.AuditLogMultipleEntriesComms
}
var file_cmd_resp_logs_proto_depIdxs = []int32{
	14, // 0: cmd_resp_logs.CmdRequestLogCounts.log:type_name -> mon_logs.EMonitorLogType
	15, // 1: cmd_resp_logs.RespRequestLogCounts.log:type_name -> mon_logs.LogEntryCount
	14, // 2: cmd_resp_logs.CmdRequestLogClear.log:type_name -> mon_logs.EMonitorLogType
	15, // 3: cmd_resp_logs.RespRequestLogClear.log:type_name -> mon_logs.LogEntryCount
	14, // 4: cmd_resp_logs.CmdRequestLogEntries.log:type_name -> mon_logs.EMonitorLogType
	14, // 5: cmd_resp_logs.RespRequestLogEntries.log:type_name -> mon_logs.EMonitorLogType
	16, // 6: cmd_resp_logs.RespRequestLogEntries.power_log:type_name -> mon_logs.PowerLogMultipleEntriesMmu
	17, // 7: cmd_resp_logs.RespRequestLogEntries.reset_log:type_name -> mon_logs.ResetLogMultipleEntriesMmu
	18, // 8: cmd_resp_logs.RespRequestLogEntries.clock_log:type_name -> mon_logs.ClockLogMultipleEntriesMmu
	19, // 9: cmd_resp_logs.RespRequestLogEntries.config_log:type_name -> mon_logs.ConfigLogMultipleEntriesMmu
	20, // 10: cmd_resp_logs.RespRequestLogEntries.port1_log:type_name -> mon_logs.Port1LogLogMultipleEntriesMmu
	21, // 11: cmd_resp_logs.RespRequestLogEntries.fault_hdr_log:type_name -> mon_logs.FaultHeaderLogMultipleEntriesMmu
	22, // 12: cmd_resp_logs.RespRequestLogEntries.fault_msr_log:type_name -> mon_logs.FaultMeasurementLogMultipleEntriesMmu
	23, // 13: cmd_resp_logs.RespRequestLogEntries.fault_seq_log:type_name -> mon_logs.FaultSequenceLogMultipleEntriesMmu
	24, // 14: cmd_resp_logs.RespRequestLogEntries.fault_facts_log:type_name -> mon_logs.FaultFactsLogMultipleEntriesMmu
	25, // 15: cmd_resp_logs.RespRequestLogEntries.alarm_log:type_name -> mon_logs.AlarmLogMultipleEntriesMmu
	26, // 16: cmd_resp_logs.CmdRequestAuditLogCounts.log:type_name -> audit_logs.EAuditLogType
	27, // 17: cmd_resp_logs.RespRequestAuditLogCounts.log:type_name -> audit_logs.AuditLogEntryCount
	26, // 18: cmd_resp_logs.CmdRequestAuditLogClear.log:type_name -> audit_logs.EAuditLogType
	26, // 19: cmd_resp_logs.RespRequestAuditLogClear.log:type_name -> audit_logs.EAuditLogType
	26, // 20: cmd_resp_logs.CmdRequestAuditLogReset.log:type_name -> audit_logs.EAuditLogType
	26, // 21: cmd_resp_logs.RespRequestAuditLogReset.log:type_name -> audit_logs.EAuditLogType
	26, // 22: cmd_resp_logs.CmdRequestAuditLogEntries.log:type_name -> audit_logs.EAuditLogType
	26, // 23: cmd_resp_logs.RespRequestAuditLogEntries.log:type_name -> audit_logs.EAuditLogType
	28, // 24: cmd_resp_logs.RespRequestAuditLogEntries.format1:type_name -> audit_logs.AuditLogMultipleEntriesComms
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_cmd_resp_logs_proto_init() }
func file_cmd_resp_logs_proto_init() {
	if File_cmd_resp_logs_proto != nil {
		return
	}
	file_cmd_resp_logs_proto_msgTypes[4].OneofWrappers = []any{}
	file_cmd_resp_logs_proto_msgTypes[5].OneofWrappers = []any{
		(*RespRequestLogEntries_PowerLog)(nil),
		(*RespRequestLogEntries_ResetLog)(nil),
		(*RespRequestLogEntries_ClockLog)(nil),
		(*RespRequestLogEntries_ConfigLog)(nil),
		(*RespRequestLogEntries_Port1Log)(nil),
		(*RespRequestLogEntries_FaultHdrLog)(nil),
		(*RespRequestLogEntries_FaultMsrLog)(nil),
		(*RespRequestLogEntries_FaultSeqLog)(nil),
		(*RespRequestLogEntries_FaultFactsLog)(nil),
		(*RespRequestLogEntries_AlarmLog)(nil),
	}
	file_cmd_resp_logs_proto_msgTypes[13].OneofWrappers = []any{
		(*RespRequestAuditLogEntries_Format1)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cmd_resp_logs_proto_rawDesc), len(file_cmd_resp_logs_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_resp_logs_proto_goTypes,
		DependencyIndexes: file_cmd_resp_logs_proto_depIdxs,
		MessageInfos:      file_cmd_resp_logs_proto_msgTypes,
	}.Build()
	File_cmd_resp_logs_proto = out.File
	file_cmd_resp_logs_proto_goTypes = nil
	file_cmd_resp_logs_proto_depIdxs = nil
}
