//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  CMD_RESP_DFU
//Commands and responses for updating firmware in the monitor

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: cmd_resp_dfu.proto

package cmd_resp_dfu

import (
	dfu "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//	CmdManifestVersions - requests the firmware images and versions in the manifests on the system,
//
// along with the update status.
type CmdManifestVersions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The choice of manifest to return the versions from.
	// FW_MANIFEST_PRESENT = FW versions on the unit after an update is complete.  During an update,
	//
	//	these are the versions before the update started, and may not be accurate
	//	while the update is still in process.
	//
	// FW_MANIFEST_UPDATE =  During an update, this returns the versions that the firmware is being
	//
	//	updated to.  After an update is completed successfully, this will not
	//	return any entries.
	//
	// FW_MANIFEST_NONE =    No manifest entries are returned; used to just get fw_update_status
	ManifestType  dfu.EFirmwareVersionsManifest `protobuf:"varint,1,opt,name=manifest_type,json=manifestType,proto3,enum=dfu.EFirmwareVersionsManifest" json:"manifest_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdManifestVersions) Reset() {
	*x = CmdManifestVersions{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdManifestVersions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdManifestVersions) ProtoMessage() {}

func (x *CmdManifestVersions) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdManifestVersions.ProtoReflect.Descriptor instead.
func (*CmdManifestVersions) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{0}
}

func (x *CmdManifestVersions) GetManifestType() dfu.EFirmwareVersionsManifest {
	if x != nil {
		return x.ManifestType
	}
	return dfu.EFirmwareVersionsManifest(0)
}

// RespManifestVersions - gives the firmware image version information from the selected manifest.
// If the manifest_type is FW_MANIFEST_NONE, then only the manifest_type, fw_update_status,
// and this_model_number fields will contain useful information;
// the other (repeated or string) fields will be empty.
type RespManifestVersions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Echoes the choice of the manifest requested in the command
	ManifestType dfu.EFirmwareVersionsManifest `protobuf:"varint,1,opt,name=manifest_type,json=manifestType,proto3,enum=dfu.EFirmwareVersionsManifest" json:"manifest_type,omitempty"`
	// Report the version information and update status for each image in the requested manifest.
	ImageStatuses []*dfu.DfuManifestStatusEntry `protobuf:"bytes,2,rep,name=image_statuses,json=imageStatuses,proto3" json:"image_statuses,omitempty"` // Max repeat count set in cmd_resp_dfu.options
	// Overall Status of any firmware update in progress.
	// This may be the only field returned when the manifest choice is FW_MANIFEST_NONE
	FwUpdateStatus dfu.EFirmwareUpdateStatus `protobuf:"varint,3,opt,name=fw_update_status,json=fwUpdateStatus,proto3,enum=dfu.EFirmwareUpdateStatus" json:"fw_update_status,omitempty"`
	// Enum for the specific Model Number of this monitor
	ThisModelNumber dfu.EMonitorModelNumber `protobuf:"varint,4,opt,name=this_model_number,json=thisModelNumber,proto3,enum=dfu.EMonitorModelNumber" json:"this_model_number,omitempty"`
	// List of the Enums for the Model Numbers that this manifest applies to.
	// This may be empty when reporting the FW_MANIFEST_PRESENT.
	SupportedModelNumbers []dfu.EMonitorModelNumber `protobuf:"varint,5,rep,packed,name=supported_model_numbers,json=supportedModelNumbers,proto3,enum=dfu.EMonitorModelNumber" json:"supported_model_numbers,omitempty"`
	// Version (Major.Minor.Revision) for the overall package of firmware
	// This may differ from one or more of the image versions, unless all the images in
	// the package have one unified version number.
	// Version in the format <major>.<minor>.<revision> with ASCII numerals for the values, up to 3 digits each.
	PackageVersion string `protobuf:"bytes,6,opt,name=package_version,json=packageVersion,proto3" json:"package_version,omitempty"`
	// Date the package was assembled, YYYY/MM/DD
	// Month (MM) and Day (DD) are always 2 digits, year is always 4 digits.
	// Separator is required and must be slash '/'
	PackageBuildDate string `protobuf:"bytes,7,opt,name=package_build_date,json=packageBuildDate,proto3" json:"package_build_date,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RespManifestVersions) Reset() {
	*x = RespManifestVersions{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespManifestVersions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespManifestVersions) ProtoMessage() {}

func (x *RespManifestVersions) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespManifestVersions.ProtoReflect.Descriptor instead.
func (*RespManifestVersions) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{1}
}

func (x *RespManifestVersions) GetManifestType() dfu.EFirmwareVersionsManifest {
	if x != nil {
		return x.ManifestType
	}
	return dfu.EFirmwareVersionsManifest(0)
}

func (x *RespManifestVersions) GetImageStatuses() []*dfu.DfuManifestStatusEntry {
	if x != nil {
		return x.ImageStatuses
	}
	return nil
}

func (x *RespManifestVersions) GetFwUpdateStatus() dfu.EFirmwareUpdateStatus {
	if x != nil {
		return x.FwUpdateStatus
	}
	return dfu.EFirmwareUpdateStatus(0)
}

func (x *RespManifestVersions) GetThisModelNumber() dfu.EMonitorModelNumber {
	if x != nil {
		return x.ThisModelNumber
	}
	return dfu.EMonitorModelNumber(0)
}

func (x *RespManifestVersions) GetSupportedModelNumbers() []dfu.EMonitorModelNumber {
	if x != nil {
		return x.SupportedModelNumbers
	}
	return nil
}

func (x *RespManifestVersions) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *RespManifestVersions) GetPackageBuildDate() string {
	if x != nil {
		return x.PackageBuildDate
	}
	return ""
}

// CmdRebootCommsMcu - reboot the comms processor
type CmdRebootCommsMcu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRebootCommsMcu) Reset() {
	*x = CmdRebootCommsMcu{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRebootCommsMcu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRebootCommsMcu) ProtoMessage() {}

func (x *CmdRebootCommsMcu) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRebootCommsMcu.ProtoReflect.Descriptor instead.
func (*CmdRebootCommsMcu) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{2}
}

func (x *CmdRebootCommsMcu) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

// RespRebootCommsMcu - Confirms the reboot.
type RespRebootCommsMcu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// gives the milliseconds period until the reboot occurs
	RebootInMs uint32 `protobuf:"varint,1,opt,name=reboot_in_ms,json=rebootInMs,proto3" json:"reboot_in_ms,omitempty"`
	// Additional result code with more detail than errors returned in the wrapper field "code"
	ResultCode    dfu.EDfuResultCode `protobuf:"varint,2,opt,name=result_code,json=resultCode,proto3,enum=dfu.EDfuResultCode" json:"result_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRebootCommsMcu) Reset() {
	*x = RespRebootCommsMcu{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRebootCommsMcu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRebootCommsMcu) ProtoMessage() {}

func (x *RespRebootCommsMcu) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRebootCommsMcu.ProtoReflect.Descriptor instead.
func (*RespRebootCommsMcu) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{3}
}

func (x *RespRebootCommsMcu) GetRebootInMs() uint32 {
	if x != nil {
		return x.RebootInMs
	}
	return 0
}

func (x *RespRebootCommsMcu) GetResultCode() dfu.EDfuResultCode {
	if x != nil {
		return x.ResultCode
	}
	return dfu.EDfuResultCode(0)
}

// CmdInitiateFirmwareUpdate - Enables a Firmware Update process to begin
type CmdInitiateFirmwareUpdate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdInitiateFirmwareUpdate) Reset() {
	*x = CmdInitiateFirmwareUpdate{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdInitiateFirmwareUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdInitiateFirmwareUpdate) ProtoMessage() {}

func (x *CmdInitiateFirmwareUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdInitiateFirmwareUpdate.ProtoReflect.Descriptor instead.
func (*CmdInitiateFirmwareUpdate) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{4}
}

func (x *CmdInitiateFirmwareUpdate) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

// RespInitiateFirmwareUpdate - Confirms the update starting.
type RespInitiateFirmwareUpdate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Additional result code with more detail than errors returned in the wrapper field "code"
	ResultCode    dfu.EDfuResultCode `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3,enum=dfu.EDfuResultCode" json:"result_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespInitiateFirmwareUpdate) Reset() {
	*x = RespInitiateFirmwareUpdate{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespInitiateFirmwareUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespInitiateFirmwareUpdate) ProtoMessage() {}

func (x *RespInitiateFirmwareUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespInitiateFirmwareUpdate.ProtoReflect.Descriptor instead.
func (*RespInitiateFirmwareUpdate) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{5}
}

func (x *RespInitiateFirmwareUpdate) GetResultCode() dfu.EDfuResultCode {
	if x != nil {
		return x.ResultCode
	}
	return dfu.EDfuResultCode(0)
}

// CmdFirmwareUpdateManifest - The Firmware Update Manifest message, containing all the image files to be updated.
// The files should be listed in this order
// - Other Processors connected to the Main Processor
// - Main Processor application code
// - (Uncommon) Main Processor MCUboot
// - Protected Storage update for the Comms Processor
// - Comms Processor application code
// - (Uncommon) Comms Processor MCUboot
// The manifest may list all of these, or just one file, or some subset, though
// it is recommended that all the images listed (except MCUboot) are updated together,
// to keep the system in a consistent, validated state.
type CmdFirmwareUpdateManifest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Timeout for the whole update process
	TimeoutSeconds uint32 `protobuf:"varint,1,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	// Entries for each image in the update
	Entries []*dfu.DfuManifestEntry `protobuf:"bytes,2,rep,name=entries,proto3" json:"entries,omitempty"` // Max repeat count set in cmd_resp_dfu.options
	// List of the Enums for the Model Numbers that this manifest applies to.
	// The manifest will be rejected if the monitor's Model Number is not in this list.
	SupportedModelNumbers []dfu.EMonitorModelNumber `protobuf:"varint,3,rep,packed,name=supported_model_numbers,json=supportedModelNumbers,proto3,enum=dfu.EMonitorModelNumber" json:"supported_model_numbers,omitempty"`
	// Version (Major.Minor.Revision) for the overall package of firmware
	// This may differ from one or more of the image versions, unless all the images in
	// the package have one unified version number.
	// Version in the format <major>.<minor>.<revision> with ASCII numerals for the values, up to 3 digits each.
	PackageVersion string `protobuf:"bytes,4,opt,name=package_version,json=packageVersion,proto3" json:"package_version,omitempty"`
	// Date the package was assembled, YYYY/MM/DD
	// Month (MM) and Day (DD) are always 2 digits, year is always 4 digits.
	// Separator is required and must be slash '/'
	PackageBuildDate string `protobuf:"bytes,5,opt,name=package_build_date,json=packageBuildDate,proto3" json:"package_build_date,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CmdFirmwareUpdateManifest) Reset() {
	*x = CmdFirmwareUpdateManifest{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdFirmwareUpdateManifest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdFirmwareUpdateManifest) ProtoMessage() {}

func (x *CmdFirmwareUpdateManifest) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdFirmwareUpdateManifest.ProtoReflect.Descriptor instead.
func (*CmdFirmwareUpdateManifest) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{6}
}

func (x *CmdFirmwareUpdateManifest) GetTimeoutSeconds() uint32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *CmdFirmwareUpdateManifest) GetEntries() []*dfu.DfuManifestEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *CmdFirmwareUpdateManifest) GetSupportedModelNumbers() []dfu.EMonitorModelNumber {
	if x != nil {
		return x.SupportedModelNumbers
	}
	return nil
}

func (x *CmdFirmwareUpdateManifest) GetPackageVersion() string {
	if x != nil {
		return x.PackageVersion
	}
	return ""
}

func (x *CmdFirmwareUpdateManifest) GetPackageBuildDate() string {
	if x != nil {
		return x.PackageBuildDate
	}
	return ""
}

// RespFirmwareUpdateManifest - Confirms the update starting.
type RespFirmwareUpdateManifest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Additional result code with more detail than errors returned in the wrapper field "code"
	ResultCode    dfu.EDfuResultCode `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3,enum=dfu.EDfuResultCode" json:"result_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespFirmwareUpdateManifest) Reset() {
	*x = RespFirmwareUpdateManifest{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespFirmwareUpdateManifest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespFirmwareUpdateManifest) ProtoMessage() {}

func (x *RespFirmwareUpdateManifest) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespFirmwareUpdateManifest.ProtoReflect.Descriptor instead.
func (*RespFirmwareUpdateManifest) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{7}
}

func (x *RespFirmwareUpdateManifest) GetResultCode() dfu.EDfuResultCode {
	if x != nil {
		return x.ResultCode
	}
	return dfu.EDfuResultCode(0)
}

// CmdBeginFirmwareDownload - Start the download of an image file using CmdFirmwareDownloadChunk
type CmdBeginFirmwareDownload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// MCU for which this file is intended
	TargetMcu dfu.EProcessorType `protobuf:"varint,1,opt,name=target_mcu,json=targetMcu,proto3,enum=dfu.EProcessorType" json:"target_mcu,omitempty"`
	// Type of image being updated
	ImageType dfu.EImageType `protobuf:"varint,2,opt,name=image_type,json=imageType,proto3,enum=dfu.EImageType" json:"image_type,omitempty"`
	// Name must be the same as provided in the DfuManifestEntry
	Filename string `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"` // Max string length set in cmd_resp_dfu.options
	// ID that will be used to identify the subsequent download chunk commands
	DownloadRequestId uint32 `protobuf:"varint,4,opt,name=download_request_id,json=downloadRequestId,proto3" json:"download_request_id,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CmdBeginFirmwareDownload) Reset() {
	*x = CmdBeginFirmwareDownload{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdBeginFirmwareDownload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdBeginFirmwareDownload) ProtoMessage() {}

func (x *CmdBeginFirmwareDownload) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdBeginFirmwareDownload.ProtoReflect.Descriptor instead.
func (*CmdBeginFirmwareDownload) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{8}
}

func (x *CmdBeginFirmwareDownload) GetTargetMcu() dfu.EProcessorType {
	if x != nil {
		return x.TargetMcu
	}
	return dfu.EProcessorType(0)
}

func (x *CmdBeginFirmwareDownload) GetImageType() dfu.EImageType {
	if x != nil {
		return x.ImageType
	}
	return dfu.EImageType(0)
}

func (x *CmdBeginFirmwareDownload) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *CmdBeginFirmwareDownload) GetDownloadRequestId() uint32 {
	if x != nil {
		return x.DownloadRequestId
	}
	return 0
}

// RespBeginFirmwareDownload - Confirms the image download start.
type RespBeginFirmwareDownload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Additional result code with more detail than errors returned in the wrapper field "code"
	ResultCode dfu.EDfuResultCode `protobuf:"varint,1,opt,name=result_code,json=resultCode,proto3,enum=dfu.EDfuResultCode" json:"result_code,omitempty"`
	// gives the maximum allowed size in bytes of each image chunk
	// NOTE: This must not exceed the nanopb cmd_resp_dfu.options max size for cmd_resp_dfu.CmdFirmwareDownloadChunk.chunk_data
	MaxChunkSizeBytes uint32 `protobuf:"varint,3,opt,name=max_chunk_size_bytes,json=maxChunkSizeBytes,proto3" json:"max_chunk_size_bytes,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RespBeginFirmwareDownload) Reset() {
	*x = RespBeginFirmwareDownload{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespBeginFirmwareDownload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespBeginFirmwareDownload) ProtoMessage() {}

func (x *RespBeginFirmwareDownload) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespBeginFirmwareDownload.ProtoReflect.Descriptor instead.
func (*RespBeginFirmwareDownload) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{9}
}

func (x *RespBeginFirmwareDownload) GetResultCode() dfu.EDfuResultCode {
	if x != nil {
		return x.ResultCode
	}
	return dfu.EDfuResultCode(0)
}

func (x *RespBeginFirmwareDownload) GetMaxChunkSizeBytes() uint32 {
	if x != nil {
		return x.MaxChunkSizeBytes
	}
	return 0
}

// CmdFirmwareDownloadChunk - send one chunk of the firmware image file
type CmdFirmwareDownloadChunk struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID that was provided in the CmdBeginFirmwareDownload
	DownloadRequestId uint32 `protobuf:"varint,1,opt,name=download_request_id,json=downloadRequestId,proto3" json:"download_request_id,omitempty"`
	// Offset in the file where this chunk should be written
	Offset uint32 `protobuf:"fixed32,2,opt,name=offset,proto3" json:"offset,omitempty"`
	// Size of the chunk, in bytes
	SizeBytes uint32 `protobuf:"varint,3,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	// True if this is the last chunk of the file
	LastChunk bool `protobuf:"varint,4,opt,name=last_chunk,json=lastChunk,proto3" json:"last_chunk,omitempty"`
	// The byte array of chunk data
	ChunkData     []byte `protobuf:"bytes,5,opt,name=chunk_data,json=chunkData,proto3" json:"chunk_data,omitempty"` // Max data length set in cmd_resp_dfu.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdFirmwareDownloadChunk) Reset() {
	*x = CmdFirmwareDownloadChunk{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdFirmwareDownloadChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdFirmwareDownloadChunk) ProtoMessage() {}

func (x *CmdFirmwareDownloadChunk) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdFirmwareDownloadChunk.ProtoReflect.Descriptor instead.
func (*CmdFirmwareDownloadChunk) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{10}
}

func (x *CmdFirmwareDownloadChunk) GetDownloadRequestId() uint32 {
	if x != nil {
		return x.DownloadRequestId
	}
	return 0
}

func (x *CmdFirmwareDownloadChunk) GetOffset() uint32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *CmdFirmwareDownloadChunk) GetSizeBytes() uint32 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

func (x *CmdFirmwareDownloadChunk) GetLastChunk() bool {
	if x != nil {
		return x.LastChunk
	}
	return false
}

func (x *CmdFirmwareDownloadChunk) GetChunkData() []byte {
	if x != nil {
		return x.ChunkData
	}
	return nil
}

//	RespFirmwareDownloadChunk - returns the expected and received offsets.
//
// These will normally be the same.  If the response has a RESP_SEQUENCE_ERR code,
// these will show the disparity in the recevied chunk
type RespFirmwareDownloadChunk struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Offset in the file that was expect for this chunk
	OffsetExpected uint32 `protobuf:"fixed32,1,opt,name=offset_expected,json=offsetExpected,proto3" json:"offset_expected,omitempty"`
	// Offset in the file of this chunk
	OffsetReceived uint32 `protobuf:"fixed32,2,opt,name=offset_received,json=offsetReceived,proto3" json:"offset_received,omitempty"`
	// This will be set to confirm the last chunk of the file was received
	Complete bool `protobuf:"varint,3,opt,name=complete,proto3" json:"complete,omitempty"`
	// Additional result code with more detail than errors returned in the wrapper field "code"
	ResultCode    dfu.EDfuResultCode `protobuf:"varint,4,opt,name=result_code,json=resultCode,proto3,enum=dfu.EDfuResultCode" json:"result_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespFirmwareDownloadChunk) Reset() {
	*x = RespFirmwareDownloadChunk{}
	mi := &file_cmd_resp_dfu_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespFirmwareDownloadChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespFirmwareDownloadChunk) ProtoMessage() {}

func (x *RespFirmwareDownloadChunk) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_dfu_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespFirmwareDownloadChunk.ProtoReflect.Descriptor instead.
func (*RespFirmwareDownloadChunk) Descriptor() ([]byte, []int) {
	return file_cmd_resp_dfu_proto_rawDescGZIP(), []int{11}
}

func (x *RespFirmwareDownloadChunk) GetOffsetExpected() uint32 {
	if x != nil {
		return x.OffsetExpected
	}
	return 0
}

func (x *RespFirmwareDownloadChunk) GetOffsetReceived() uint32 {
	if x != nil {
		return x.OffsetReceived
	}
	return 0
}

func (x *RespFirmwareDownloadChunk) GetComplete() bool {
	if x != nil {
		return x.Complete
	}
	return false
}

func (x *RespFirmwareDownloadChunk) GetResultCode() dfu.EDfuResultCode {
	if x != nil {
		return x.ResultCode
	}
	return dfu.EDfuResultCode(0)
}

var File_cmd_resp_dfu_proto protoreflect.FileDescriptor

const file_cmd_resp_dfu_proto_rawDesc = "" +
	"\n" +
	"\x12cmd_resp_dfu.proto\x12\fcmd_resp_dfu\x1a\tdfu.proto\"Z\n" +
	"\x13CmdManifestVersions\x12C\n" +
	"\rmanifest_type\x18\x01 \x01(\x0e2\x1e.dfu.EFirmwareVersionsManifestR\fmanifestType\"\xd4\x03\n" +
	"\x14RespManifestVersions\x12C\n" +
	"\rmanifest_type\x18\x01 \x01(\x0e2\x1e.dfu.EFirmwareVersionsManifestR\fmanifestType\x12B\n" +
	"\x0eimage_statuses\x18\x02 \x03(\v2\x1b.dfu.DfuManifestStatusEntryR\rimageStatuses\x12D\n" +
	"\x10fw_update_status\x18\x03 \x01(\x0e2\x1a.dfu.EFirmwareUpdateStatusR\x0efwUpdateStatus\x12D\n" +
	"\x11this_model_number\x18\x04 \x01(\x0e2\x18.dfu.EMonitorModelNumberR\x0fthisModelNumber\x12P\n" +
	"\x17supported_model_numbers\x18\x05 \x03(\x0e2\x18.dfu.EMonitorModelNumberR\x15supportedModelNumbers\x12'\n" +
	"\x0fpackage_version\x18\x06 \x01(\tR\x0epackageVersion\x12,\n" +
	"\x12package_build_date\x18\a \x01(\tR\x10packageBuildDate\"-\n" +
	"\x11CmdRebootCommsMcu\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"l\n" +
	"\x12RespRebootCommsMcu\x12 \n" +
	"\freboot_in_ms\x18\x01 \x01(\rR\n" +
	"rebootInMs\x124\n" +
	"\vresult_code\x18\x02 \x01(\x0e2\x13.dfu.EDfuResultCodeR\n" +
	"resultCode\"5\n" +
	"\x19CmdInitiateFirmwareUpdate\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"R\n" +
	"\x1aRespInitiateFirmwareUpdate\x124\n" +
	"\vresult_code\x18\x01 \x01(\x0e2\x13.dfu.EDfuResultCodeR\n" +
	"resultCode\"\x9e\x02\n" +
	"\x19CmdFirmwareUpdateManifest\x12'\n" +
	"\x0ftimeout_seconds\x18\x01 \x01(\rR\x0etimeoutSeconds\x12/\n" +
	"\aentries\x18\x02 \x03(\v2\x15.dfu.DfuManifestEntryR\aentries\x12P\n" +
	"\x17supported_model_numbers\x18\x03 \x03(\x0e2\x18.dfu.EMonitorModelNumberR\x15supportedModelNumbers\x12'\n" +
	"\x0fpackage_version\x18\x04 \x01(\tR\x0epackageVersion\x12,\n" +
	"\x12package_build_date\x18\x05 \x01(\tR\x10packageBuildDate\"R\n" +
	"\x1aRespFirmwareUpdateManifest\x124\n" +
	"\vresult_code\x18\x01 \x01(\x0e2\x13.dfu.EDfuResultCodeR\n" +
	"resultCode\"\xca\x01\n" +
	"\x18CmdBeginFirmwareDownload\x122\n" +
	"\n" +
	"target_mcu\x18\x01 \x01(\x0e2\x13.dfu.EProcessorTypeR\ttargetMcu\x12.\n" +
	"\n" +
	"image_type\x18\x02 \x01(\x0e2\x0f.dfu.EImageTypeR\timageType\x12\x1a\n" +
	"\bfilename\x18\x03 \x01(\tR\bfilename\x12.\n" +
	"\x13download_request_id\x18\x04 \x01(\rR\x11downloadRequestId\"\x82\x01\n" +
	"\x19RespBeginFirmwareDownload\x124\n" +
	"\vresult_code\x18\x01 \x01(\x0e2\x13.dfu.EDfuResultCodeR\n" +
	"resultCode\x12/\n" +
	"\x14max_chunk_size_bytes\x18\x03 \x01(\rR\x11maxChunkSizeBytes\"\xbf\x01\n" +
	"\x18CmdFirmwareDownloadChunk\x12.\n" +
	"\x13download_request_id\x18\x01 \x01(\rR\x11downloadRequestId\x12\x16\n" +
	"\x06offset\x18\x02 \x01(\aR\x06offset\x12\x1d\n" +
	"\n" +
	"size_bytes\x18\x03 \x01(\rR\tsizeBytes\x12\x1d\n" +
	"\n" +
	"last_chunk\x18\x04 \x01(\bR\tlastChunk\x12\x1d\n" +
	"\n" +
	"chunk_data\x18\x05 \x01(\fR\tchunkData\"\xbf\x01\n" +
	"\x19RespFirmwareDownloadChunk\x12'\n" +
	"\x0foffset_expected\x18\x01 \x01(\aR\x0eoffsetExpected\x12'\n" +
	"\x0foffset_received\x18\x02 \x01(\aR\x0eoffsetReceived\x12\x1a\n" +
	"\bcomplete\x18\x03 \x01(\bR\bcomplete\x124\n" +
	"\vresult_code\x18\x04 \x01(\x0e2\x13.dfu.EDfuResultCodeR\n" +
	"resultCodeb\x06proto3"

var (
	file_cmd_resp_dfu_proto_rawDescOnce sync.Once
	file_cmd_resp_dfu_proto_rawDescData []byte
)

func file_cmd_resp_dfu_proto_rawDescGZIP() []byte {
	file_cmd_resp_dfu_proto_rawDescOnce.Do(func() {
		file_cmd_resp_dfu_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cmd_resp_dfu_proto_rawDesc), len(file_cmd_resp_dfu_proto_rawDesc)))
	})
	return file_cmd_resp_dfu_proto_rawDescData
}

var file_cmd_resp_dfu_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_cmd_resp_dfu_proto_goTypes = []any{
	(*CmdManifestVersions)(nil),        // 0: cmd_resp_dfu.CmdManifestVersions
	(*RespManifestVersions)(nil),       // 1: cmd_resp_dfu.RespManifestVersions
	(*CmdRebootCommsMcu)(nil),          // 2: cmd_resp_dfu.CmdRebootCommsMcu
	(*RespRebootCommsMcu)(nil),         // 3: cmd_resp_dfu.RespRebootCommsMcu
	(*CmdInitiateFirmwareUpdate)(nil),  // 4: cmd_resp_dfu.CmdInitiateFirmwareUpdate
	(*RespInitiateFirmwareUpdate)(nil), // 5: cmd_resp_dfu.RespInitiateFirmwareUpdate
	(*CmdFirmwareUpdateManifest)(nil),  // 6: cmd_resp_dfu.CmdFirmwareUpdateManifest
	(*RespFirmwareUpdateManifest)(nil), // 7: cmd_resp_dfu.RespFirmwareUpdateManifest
	(*CmdBeginFirmwareDownload)(nil),   // 8: cmd_resp_dfu.CmdBeginFirmwareDownload
	(*RespBeginFirmwareDownload)(nil),  // 9: cmd_resp_dfu.RespBeginFirmwareDownload
	(*CmdFirmwareDownloadChunk)(nil),   // 10: cmd_resp_dfu.CmdFirmwareDownloadChunk
	(*RespFirmwareDownloadChunk)(nil),  // 11: cmd_resp_dfu.RespFirmwareDownloadChunk
	(dfu.EFirmwareVersionsManifest)(0), // 12: dfu.EFirmwareVersionsManifest
	(*dfu.DfuManifestStatusEntry)(nil), // 13: dfu.DfuManifestStatusEntry
	(dfu.EFirmwareUpdateStatus)(0),     // 14: dfu.EFirmwareUpdateStatus
	(dfu.EMonitorModelNumber)(0),       // 15: dfu.EMonitorModelNumber
	(dfu.EDfuResultCode)(0),            // 16: dfu.EDfuResultCode
	(*dfu.DfuManifestEntry)(nil),       // 17: dfu.DfuManifestEntry
	(dfu.EProcessorType)(0),            // 18: dfu.EProcessorType
	(dfu.EImageType)(0),                // 19: dfu.EImageType
}
var file_cmd_resp_dfu_proto_depIdxs = []int32{
	12, // 0: cmd_resp_dfu.CmdManifestVersions.manifest_type:type_name -> dfu.EFirmwareVersionsManifest
	12, // 1: cmd_resp_dfu.RespManifestVersions.manifest_type:type_name -> dfu.EFirmwareVersionsManifest
	13, // 2: cmd_resp_dfu.RespManifestVersions.image_statuses:type_name -> dfu.DfuManifestStatusEntry
	14, // 3: cmd_resp_dfu.RespManifestVersions.fw_update_status:type_name -> dfu.EFirmwareUpdateStatus
	15, // 4: cmd_resp_dfu.RespManifestVersions.this_model_number:type_name -> dfu.EMonitorModelNumber
	15, // 5: cmd_resp_dfu.RespManifestVersions.supported_model_numbers:type_name -> dfu.EMonitorModelNumber
	16, // 6: cmd_resp_dfu.RespRebootCommsMcu.result_code:type_name -> dfu.EDfuResultCode
	16, // 7: cmd_resp_dfu.RespInitiateFirmwareUpdate.result_code:type_name -> dfu.EDfuResultCode
	17, // 8: cmd_resp_dfu.CmdFirmwareUpdateManifest.entries:type_name -> dfu.DfuManifestEntry
	15, // 9: cmd_resp_dfu.CmdFirmwareUpdateManifest.supported_model_numbers:type_name -> dfu.EMonitorModelNumber
	16, // 10: cmd_resp_dfu.RespFirmwareUpdateManifest.result_code:type_name -> dfu.EDfuResultCode
	18, // 11: cmd_resp_dfu.CmdBeginFirmwareDownload.target_mcu:type_name -> dfu.EProcessorType
	19, // 12: cmd_resp_dfu.CmdBeginFirmwareDownload.image_type:type_name -> dfu.EImageType
	16, // 13: cmd_resp_dfu.RespBeginFirmwareDownload.result_code:type_name -> dfu.EDfuResultCode
	16, // 14: cmd_resp_dfu.RespFirmwareDownloadChunk.result_code:type_name -> dfu.EDfuResultCode
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_cmd_resp_dfu_proto_init() }
func file_cmd_resp_dfu_proto_init() {
	if File_cmd_resp_dfu_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cmd_resp_dfu_proto_rawDesc), len(file_cmd_resp_dfu_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_resp_dfu_proto_goTypes,
		DependencyIndexes: file_cmd_resp_dfu_proto_depIdxs,
		MessageInfos:      file_cmd_resp_dfu_proto_msgTypes,
	}.Build()
	File_cmd_resp_dfu_proto = out.File
	file_cmd_resp_dfu_proto_goTypes = nil
	file_cmd_resp_dfu_proto_depIdxs = nil
}
