//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  REALTIME
//Messages for realtime data based on the SPI data from the Main processor.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: realtime.proto

package realtime

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	mon_faults "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EDisplayChannelsType indicates the channel configuration type of the display
type EDisplayChannelsType int32

const (
	EDisplayChannelsType_DISP_CH_UNSPECIFIED EDisplayChannelsType = 0
	EDisplayChannelsType_DISP_CH_3           EDisplayChannelsType = 1 // 3 channel type
	EDisplayChannelsType_DISP_CH_6           EDisplayChannelsType = 2 // 6 channel type
	EDisplayChannelsType_DISP_CH_12          EDisplayChannelsType = 3 // 12 channel type
	EDisplayChannelsType_DISP_CH_16          EDisplayChannelsType = 4 // 16 channel type
	EDisplayChannelsType_DISP_CH_16X         EDisplayChannelsType = 5 // 16 channel + Walks type
)

// Enum value maps for EDisplayChannelsType.
var (
	EDisplayChannelsType_name = map[int32]string{
		0: "DISP_CH_UNSPECIFIED",
		1: "DISP_CH_3",
		2: "DISP_CH_6",
		3: "DISP_CH_12",
		4: "DISP_CH_16",
		5: "DISP_CH_16X",
	}
	EDisplayChannelsType_value = map[string]int32{
		"DISP_CH_UNSPECIFIED": 0,
		"DISP_CH_3":           1,
		"DISP_CH_6":           2,
		"DISP_CH_12":          3,
		"DISP_CH_16":          4,
		"DISP_CH_16X":         5,
	}
)

func (x EDisplayChannelsType) Enum() *EDisplayChannelsType {
	p := new(EDisplayChannelsType)
	*p = x
	return p
}

func (x EDisplayChannelsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EDisplayChannelsType) Descriptor() protoreflect.EnumDescriptor {
	return file_realtime_proto_enumTypes[0].Descriptor()
}

func (EDisplayChannelsType) Type() protoreflect.EnumType {
	return &file_realtime_proto_enumTypes[0]
}

func (x EDisplayChannelsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EDisplayChannelsType.Descriptor instead.
func (EDisplayChannelsType) EnumDescriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{0}
}

//	ChannelVoltsCurrentsAndTimers provides one channel's voltages and currents.
//
// This is used in the ChannelStatusData message
type ChannelVoltsCurrentsAndTimers struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The channel number
	Channel uint32 `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"`
	// Channel indicator voltages in Volts
	RedVolts    float32 `protobuf:"fixed32,2,opt,name=red_volts,json=redVolts,proto3" json:"red_volts,omitempty"`
	YellowVolts float32 `protobuf:"fixed32,3,opt,name=yellow_volts,json=yellowVolts,proto3" json:"yellow_volts,omitempty"`
	GreenVolts  float32 `protobuf:"fixed32,4,opt,name=green_volts,json=greenVolts,proto3" json:"green_volts,omitempty"`
	WalkVolts   float32 `protobuf:"fixed32,5,opt,name=walk_volts,json=walkVolts,proto3" json:"walk_volts,omitempty"`
	// Channel indicator currents in Amps
	RedAmps    float32 `protobuf:"fixed32,6,opt,name=red_amps,json=redAmps,proto3" json:"red_amps,omitempty"`
	YellowAmps float32 `protobuf:"fixed32,7,opt,name=yellow_amps,json=yellowAmps,proto3" json:"yellow_amps,omitempty"`
	GreenAmps  float32 `protobuf:"fixed32,8,opt,name=green_amps,json=greenAmps,proto3" json:"green_amps,omitempty"`
	WalkAmps   float32 `protobuf:"fixed32,9,opt,name=walk_amps,json=walkAmps,proto3" json:"walk_amps,omitempty"`
	// Channel indicator off times in milliseconds
	RedOffTimeMs    float32 `protobuf:"fixed32,10,opt,name=red_off_time_ms,json=redOffTimeMs,proto3" json:"red_off_time_ms,omitempty"`
	YellowOffTimeMs float32 `protobuf:"fixed32,11,opt,name=yellow_off_time_ms,json=yellowOffTimeMs,proto3" json:"yellow_off_time_ms,omitempty"`
	GreenOffTimeMs  float32 `protobuf:"fixed32,12,opt,name=green_off_time_ms,json=greenOffTimeMs,proto3" json:"green_off_time_ms,omitempty"`
	WalkOffTimeMs   float32 `protobuf:"fixed32,13,opt,name=walk_off_time_ms,json=walkOffTimeMs,proto3" json:"walk_off_time_ms,omitempty"`
	// Channel indicator on times in milliseconds
	RedOnTimeMs    float32 `protobuf:"fixed32,14,opt,name=red_on_time_ms,json=redOnTimeMs,proto3" json:"red_on_time_ms,omitempty"`
	YellowOnTimeMs float32 `protobuf:"fixed32,15,opt,name=yellow_on_time_ms,json=yellowOnTimeMs,proto3" json:"yellow_on_time_ms,omitempty"`
	GreenOnTimeMs  float32 `protobuf:"fixed32,16,opt,name=green_on_time_ms,json=greenOnTimeMs,proto3" json:"green_on_time_ms,omitempty"`
	WalkOnTimeMs   float32 `protobuf:"fixed32,17,opt,name=walk_on_time_ms,json=walkOnTimeMs,proto3" json:"walk_on_time_ms,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ChannelVoltsCurrentsAndTimers) Reset() {
	*x = ChannelVoltsCurrentsAndTimers{}
	mi := &file_realtime_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelVoltsCurrentsAndTimers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelVoltsCurrentsAndTimers) ProtoMessage() {}

func (x *ChannelVoltsCurrentsAndTimers) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelVoltsCurrentsAndTimers.ProtoReflect.Descriptor instead.
func (*ChannelVoltsCurrentsAndTimers) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{0}
}

func (x *ChannelVoltsCurrentsAndTimers) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetRedVolts() float32 {
	if x != nil {
		return x.RedVolts
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetYellowVolts() float32 {
	if x != nil {
		return x.YellowVolts
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetGreenVolts() float32 {
	if x != nil {
		return x.GreenVolts
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetWalkVolts() float32 {
	if x != nil {
		return x.WalkVolts
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetRedAmps() float32 {
	if x != nil {
		return x.RedAmps
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetYellowAmps() float32 {
	if x != nil {
		return x.YellowAmps
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetGreenAmps() float32 {
	if x != nil {
		return x.GreenAmps
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetWalkAmps() float32 {
	if x != nil {
		return x.WalkAmps
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetRedOffTimeMs() float32 {
	if x != nil {
		return x.RedOffTimeMs
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetYellowOffTimeMs() float32 {
	if x != nil {
		return x.YellowOffTimeMs
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetGreenOffTimeMs() float32 {
	if x != nil {
		return x.GreenOffTimeMs
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetWalkOffTimeMs() float32 {
	if x != nil {
		return x.WalkOffTimeMs
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetRedOnTimeMs() float32 {
	if x != nil {
		return x.RedOnTimeMs
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetYellowOnTimeMs() float32 {
	if x != nil {
		return x.YellowOnTimeMs
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetGreenOnTimeMs() float32 {
	if x != nil {
		return x.GreenOnTimeMs
	}
	return 0
}

func (x *ChannelVoltsCurrentsAndTimers) GetWalkOnTimeMs() float32 {
	if x != nil {
		return x.WalkOnTimeMs
	}
	return 0
}

//	ChannelStatusData provides realtime data for all channels in use.
//
// This comes from the Main processor SPI data Channel Data Block and Timers Data Block
type ChannelStatusData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// bitmaps of channel indications on.  LSbit = Ch.1
	BluesOnChmap   uint32 `protobuf:"fixed32,1,opt,name=blues_on_chmap,json=bluesOnChmap,proto3" json:"blues_on_chmap,omitempty"`
	RedsOnChmap    uint32 `protobuf:"fixed32,2,opt,name=reds_on_chmap,json=redsOnChmap,proto3" json:"reds_on_chmap,omitempty"`
	YellowsOnChmap uint32 `protobuf:"fixed32,3,opt,name=yellows_on_chmap,json=yellowsOnChmap,proto3" json:"yellows_on_chmap,omitempty"`
	GreensOnChmap  uint32 `protobuf:"fixed32,4,opt,name=greens_on_chmap,json=greensOnChmap,proto3" json:"greens_on_chmap,omitempty"`
	WalksOnChmap   uint32 `protobuf:"fixed32,5,opt,name=walks_on_chmap,json=walksOnChmap,proto3" json:"walks_on_chmap,omitempty"`
	// bitmaps of indication channels field check mismatch.  LSbit = Ch.1
	RedsFieldCheckChmap    uint32 `protobuf:"fixed32,6,opt,name=reds_field_check_chmap,json=redsFieldCheckChmap,proto3" json:"reds_field_check_chmap,omitempty"`
	YellowsFieldCheckChmap uint32 `protobuf:"fixed32,7,opt,name=yellows_field_check_chmap,json=yellowsFieldCheckChmap,proto3" json:"yellows_field_check_chmap,omitempty"`
	GreensFieldCheckChmap  uint32 `protobuf:"fixed32,8,opt,name=greens_field_check_chmap,json=greensFieldCheckChmap,proto3" json:"greens_field_check_chmap,omitempty"`
	WalksFieldCheckChmap   uint32 `protobuf:"fixed32,9,opt,name=walks_field_check_chmap,json=walksFieldCheckChmap,proto3" json:"walks_field_check_chmap,omitempty"`
	// voltages, currents, and timers for each channel's indicators
	ChannelIndicatorsVIT []*ChannelVoltsCurrentsAndTimers `protobuf:"bytes,10,rep,name=channel_indicators_v_i_t,json=channelIndicatorsVIT,proto3" json:"channel_indicators_v_i_t,omitempty"` // Max repeat count set in realtime.options
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ChannelStatusData) Reset() {
	*x = ChannelStatusData{}
	mi := &file_realtime_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelStatusData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelStatusData) ProtoMessage() {}

func (x *ChannelStatusData) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelStatusData.ProtoReflect.Descriptor instead.
func (*ChannelStatusData) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{1}
}

func (x *ChannelStatusData) GetBluesOnChmap() uint32 {
	if x != nil {
		return x.BluesOnChmap
	}
	return 0
}

func (x *ChannelStatusData) GetRedsOnChmap() uint32 {
	if x != nil {
		return x.RedsOnChmap
	}
	return 0
}

func (x *ChannelStatusData) GetYellowsOnChmap() uint32 {
	if x != nil {
		return x.YellowsOnChmap
	}
	return 0
}

func (x *ChannelStatusData) GetGreensOnChmap() uint32 {
	if x != nil {
		return x.GreensOnChmap
	}
	return 0
}

func (x *ChannelStatusData) GetWalksOnChmap() uint32 {
	if x != nil {
		return x.WalksOnChmap
	}
	return 0
}

func (x *ChannelStatusData) GetRedsFieldCheckChmap() uint32 {
	if x != nil {
		return x.RedsFieldCheckChmap
	}
	return 0
}

func (x *ChannelStatusData) GetYellowsFieldCheckChmap() uint32 {
	if x != nil {
		return x.YellowsFieldCheckChmap
	}
	return 0
}

func (x *ChannelStatusData) GetGreensFieldCheckChmap() uint32 {
	if x != nil {
		return x.GreensFieldCheckChmap
	}
	return 0
}

func (x *ChannelStatusData) GetWalksFieldCheckChmap() uint32 {
	if x != nil {
		return x.WalksFieldCheckChmap
	}
	return 0
}

func (x *ChannelStatusData) GetChannelIndicatorsVIT() []*ChannelVoltsCurrentsAndTimers {
	if x != nil {
		return x.ChannelIndicatorsVIT
	}
	return nil
}

//	DisplayLineControl provides formatting options for a display line of text.  This comes from the
//
// SPI CpsTextLine "Control Byte"
type DisplayLineControl struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// When true, this line was modified since last update.
	Modified bool `protobuf:"varint,1,opt,name=modified,proto3" json:"modified,omitempty"`
	// When true, this line is being displayed.
	InUse         bool `protobuf:"varint,2,opt,name=in_use,json=inUse,proto3" json:"in_use,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DisplayLineControl) Reset() {
	*x = DisplayLineControl{}
	mi := &file_realtime_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DisplayLineControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DisplayLineControl) ProtoMessage() {}

func (x *DisplayLineControl) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DisplayLineControl.ProtoReflect.Descriptor instead.
func (*DisplayLineControl) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{2}
}

func (x *DisplayLineControl) GetModified() bool {
	if x != nil {
		return x.Modified
	}
	return false
}

func (x *DisplayLineControl) GetInUse() bool {
	if x != nil {
		return x.InUse
	}
	return false
}

//	LcdDisplayLine provides a line of text from the monitor's display. This is used in MonitorDisplayData.
//
// This is a separate message as color and other formatting data may be added for each line.
type LcdDisplayLine struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The monitor's display line number
	LineNumber uint32 `protobuf:"varint,1,opt,name=line_number,json=lineNumber,proto3" json:"line_number,omitempty"`
	// formatting information for this display line
	Format *DisplayLineControl `protobuf:"bytes,2,opt,name=format,proto3" json:"format,omitempty"`
	// A string of ASCII characters for the given display line
	// NOTE: This is type "bytes" instead of "string" because the display uses non-printable ASCII char
	//
	//	values for special display characters.  When Flutter/Dart consumes the protobuf as a "string"
	//	it may truncate a text line at one of these characters (esp. with MSB set) during the protobuf
	//	decoding into UTF-8, losing the remainder of the line text.  Using a type of "bytes" prevents
	//	this from happening
	LineText []byte `protobuf:"bytes,3,opt,name=line_text,json=lineText,proto3" json:"line_text,omitempty"` // Max length set in realtime.options
	// An RGB color byte for each character in "line_text"
	// Refer to the Display documentation for the format of each byte.
	LineColors    []byte `protobuf:"bytes,4,opt,name=line_colors,json=lineColors,proto3" json:"line_colors,omitempty"` // Max length set in realtime.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LcdDisplayLine) Reset() {
	*x = LcdDisplayLine{}
	mi := &file_realtime_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LcdDisplayLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LcdDisplayLine) ProtoMessage() {}

func (x *LcdDisplayLine) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LcdDisplayLine.ProtoReflect.Descriptor instead.
func (*LcdDisplayLine) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{3}
}

func (x *LcdDisplayLine) GetLineNumber() uint32 {
	if x != nil {
		return x.LineNumber
	}
	return 0
}

func (x *LcdDisplayLine) GetFormat() *DisplayLineControl {
	if x != nil {
		return x.Format
	}
	return nil
}

func (x *LcdDisplayLine) GetLineText() []byte {
	if x != nil {
		return x.LineText
	}
	return nil
}

func (x *LcdDisplayLine) GetLineColors() []byte {
	if x != nil {
		return x.LineColors
	}
	return nil
}

//	MonitorDisplayData provides monitor LED and LCD display data.
//
// This comes from the Main processor SPI data Display Data Block, CpsDisplayData
type MonitorDisplayData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// True when the field display (non-text portion) of the screen is enabled
	EnableFieldDisplay bool `protobuf:"varint,12,opt,name=enable_field_display,json=enableFieldDisplay,proto3" json:"enable_field_display,omitempty"`
	// Enumerated value with the monitor channels to display
	DisplayChannels EDisplayChannelsType `protobuf:"varint,13,opt,name=display_channels,json=displayChannels,proto3,enum=realtime.EDisplayChannelsType" json:"display_channels,omitempty"`
	// Bitmaps of channel LEDs on.  LSbit = Ch.1
	//
	// Note that the LED and Field Check bits for Display Data are not updated when in a Fault state,
	// but remain what they were at the time of the fault.
	// The following are bitfields for the channel maps with LSB = Ch.1
	RedFieldLedsOnChmap    uint32 `protobuf:"fixed32,1,opt,name=red_field_leds_on_chmap,json=redFieldLedsOnChmap,proto3" json:"red_field_leds_on_chmap,omitempty"`
	YellowFieldLedsOnChmap uint32 `protobuf:"fixed32,2,opt,name=yellow_field_leds_on_chmap,json=yellowFieldLedsOnChmap,proto3" json:"yellow_field_leds_on_chmap,omitempty"`
	GreenFieldLedsOnChmap  uint32 `protobuf:"fixed32,3,opt,name=green_field_leds_on_chmap,json=greenFieldLedsOnChmap,proto3" json:"green_field_leds_on_chmap,omitempty"`
	WalkFieldLedsOnChmap   uint32 `protobuf:"fixed32,4,opt,name=walk_field_leds_on_chmap,json=walkFieldLedsOnChmap,proto3" json:"walk_field_leds_on_chmap,omitempty"`
	BlueFaultLedsOnChmap   uint32 `protobuf:"fixed32,5,opt,name=blue_fault_leds_on_chmap,json=blueFaultLedsOnChmap,proto3" json:"blue_fault_leds_on_chmap,omitempty"`
	// The Field Check States show what the traffic controller intended for each channel.
	// The field_leds_on values above show what the monitor actually detected;
	// any discrepancies between the intended and detected states are signficant.
	RedFcsChmap    uint32 `protobuf:"fixed32,6,opt,name=red_fcs_chmap,json=redFcsChmap,proto3" json:"red_fcs_chmap,omitempty"`
	YellowFcsChmap uint32 `protobuf:"fixed32,7,opt,name=yellow_fcs_chmap,json=yellowFcsChmap,proto3" json:"yellow_fcs_chmap,omitempty"`
	GreenFcsChmap  uint32 `protobuf:"fixed32,8,opt,name=green_fcs_chmap,json=greenFcsChmap,proto3" json:"green_fcs_chmap,omitempty"`
	WalkFcsChmap   uint32 `protobuf:"fixed32,9,opt,name=walk_fcs_chmap,json=walkFcsChmap,proto3" json:"walk_fcs_chmap,omitempty"`
	// The total number of text lines on the LCD display
	// Note that this may change in different display modes.
	LineCount uint32 `protobuf:"varint,10,opt,name=line_count,json=lineCount,proto3" json:"line_count,omitempty"`
	// LCD display lines
	DisplayLines  []*LcdDisplayLine `protobuf:"bytes,11,rep,name=display_lines,json=displayLines,proto3" json:"display_lines,omitempty"` // Max repeat count set in realtime.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MonitorDisplayData) Reset() {
	*x = MonitorDisplayData{}
	mi := &file_realtime_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MonitorDisplayData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorDisplayData) ProtoMessage() {}

func (x *MonitorDisplayData) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorDisplayData.ProtoReflect.Descriptor instead.
func (*MonitorDisplayData) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{4}
}

func (x *MonitorDisplayData) GetEnableFieldDisplay() bool {
	if x != nil {
		return x.EnableFieldDisplay
	}
	return false
}

func (x *MonitorDisplayData) GetDisplayChannels() EDisplayChannelsType {
	if x != nil {
		return x.DisplayChannels
	}
	return EDisplayChannelsType_DISP_CH_UNSPECIFIED
}

func (x *MonitorDisplayData) GetRedFieldLedsOnChmap() uint32 {
	if x != nil {
		return x.RedFieldLedsOnChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetYellowFieldLedsOnChmap() uint32 {
	if x != nil {
		return x.YellowFieldLedsOnChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetGreenFieldLedsOnChmap() uint32 {
	if x != nil {
		return x.GreenFieldLedsOnChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetWalkFieldLedsOnChmap() uint32 {
	if x != nil {
		return x.WalkFieldLedsOnChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetBlueFaultLedsOnChmap() uint32 {
	if x != nil {
		return x.BlueFaultLedsOnChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetRedFcsChmap() uint32 {
	if x != nil {
		return x.RedFcsChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetYellowFcsChmap() uint32 {
	if x != nil {
		return x.YellowFcsChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetGreenFcsChmap() uint32 {
	if x != nil {
		return x.GreenFcsChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetWalkFcsChmap() uint32 {
	if x != nil {
		return x.WalkFcsChmap
	}
	return 0
}

func (x *MonitorDisplayData) GetLineCount() uint32 {
	if x != nil {
		return x.LineCount
	}
	return 0
}

func (x *MonitorDisplayData) GetDisplayLines() []*LcdDisplayLine {
	if x != nil {
		return x.DisplayLines
	}
	return nil
}

// PanelLedsBitmap provides panel LED states for the MonitorPresentStatus message.
type PanelLedsBitmap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Power         bool                   `protobuf:"varint,1,opt,name=power,proto3" json:"power,omitempty"`
	Fault         bool                   `protobuf:"varint,2,opt,name=fault,proto3" json:"fault,omitempty"`
	Diag          bool                   `protobuf:"varint,3,opt,name=diag,proto3" json:"diag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PanelLedsBitmap) Reset() {
	*x = PanelLedsBitmap{}
	mi := &file_realtime_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PanelLedsBitmap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PanelLedsBitmap) ProtoMessage() {}

func (x *PanelLedsBitmap) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PanelLedsBitmap.ProtoReflect.Descriptor instead.
func (*PanelLedsBitmap) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{5}
}

func (x *PanelLedsBitmap) GetPower() bool {
	if x != nil {
		return x.Power
	}
	return false
}

func (x *PanelLedsBitmap) GetFault() bool {
	if x != nil {
		return x.Fault
	}
	return false
}

func (x *PanelLedsBitmap) GetDiag() bool {
	if x != nil {
		return x.Diag
	}
	return false
}

//	MmuMonitoredInputsVoltages provides the voltages on the monitored inputs for the MMU.
//
// This is used in the MonitorPresentStatus message
type MmuMonitoredInputsVoltages struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// values are voltages
	Input1_24VInhibitVolts      float32 `protobuf:"fixed32,1,opt,name=input1_24v_inhibit_volts,json=input124vInhibitVolts,proto3" json:"input1_24v_inhibit_volts,omitempty"`
	Input2_24VMonitor_1Volts    float32 `protobuf:"fixed32,2,opt,name=input2_24v_monitor_1_volts,json=input224vMonitor1Volts,proto3" json:"input2_24v_monitor_1_volts,omitempty"`
	Input3_24VMonitor_2Volts    float32 `protobuf:"fixed32,3,opt,name=input3_24v_monitor_2_volts,json=input324vMonitor2Volts,proto3" json:"input3_24v_monitor_2_volts,omitempty"`
	Input4ControllerVoltage     float32 `protobuf:"fixed32,4,opt,name=input4_controller_voltage,json=input4ControllerVoltage,proto3" json:"input4_controller_voltage,omitempty"`
	Input5TypeSelectVolts       float32 `protobuf:"fixed32,5,opt,name=input5_type_select_volts,json=input5TypeSelectVolts,proto3" json:"input5_type_select_volts,omitempty"`
	Input6RedEnableVolts        float32 `protobuf:"fixed32,6,opt,name=input6_red_enable_volts,json=input6RedEnableVolts,proto3" json:"input6_red_enable_volts,omitempty"`
	Input7ExternalResetVolts    float32 `protobuf:"fixed32,7,opt,name=input7_external_reset_volts,json=input7ExternalResetVolts,proto3" json:"input7_external_reset_volts,omitempty"`
	Input8Port1DisableVolts     float32 `protobuf:"fixed32,8,opt,name=input8_port1_disable_volts,json=input8Port1DisableVolts,proto3" json:"input8_port1_disable_volts,omitempty"`
	Input9ExternalWatchdogVolts float32 `protobuf:"fixed32,9,opt,name=input9_external_watchdog_volts,json=input9ExternalWatchdogVolts,proto3" json:"input9_external_watchdog_volts,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *MmuMonitoredInputsVoltages) Reset() {
	*x = MmuMonitoredInputsVoltages{}
	mi := &file_realtime_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MmuMonitoredInputsVoltages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MmuMonitoredInputsVoltages) ProtoMessage() {}

func (x *MmuMonitoredInputsVoltages) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MmuMonitoredInputsVoltages.ProtoReflect.Descriptor instead.
func (*MmuMonitoredInputsVoltages) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{6}
}

func (x *MmuMonitoredInputsVoltages) GetInput1_24VInhibitVolts() float32 {
	if x != nil {
		return x.Input1_24VInhibitVolts
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput2_24VMonitor_1Volts() float32 {
	if x != nil {
		return x.Input2_24VMonitor_1Volts
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput3_24VMonitor_2Volts() float32 {
	if x != nil {
		return x.Input3_24VMonitor_2Volts
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput4ControllerVoltage() float32 {
	if x != nil {
		return x.Input4ControllerVoltage
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput5TypeSelectVolts() float32 {
	if x != nil {
		return x.Input5TypeSelectVolts
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput6RedEnableVolts() float32 {
	if x != nil {
		return x.Input6RedEnableVolts
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput7ExternalResetVolts() float32 {
	if x != nil {
		return x.Input7ExternalResetVolts
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput8Port1DisableVolts() float32 {
	if x != nil {
		return x.Input8Port1DisableVolts
	}
	return 0
}

func (x *MmuMonitoredInputsVoltages) GetInput9ExternalWatchdogVolts() float32 {
	if x != nil {
		return x.Input9ExternalWatchdogVolts
	}
	return 0
}

//	MonitorPresentStatus provides general monitor status information.
//
// This comes from the Main processor SPI data Current Status Block
type MonitorPresentStatus struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The most recent fault(s)
	// The "fault_code" field indicates the present monitor fault, if any.
	// It is also possible that a diagnostic fault may also occur along with a monitor fault.
	// If a diagnostic fault has occurred (on its own or in additon to a monitor fault), then
	// the "diagnostic_fault" field will be 'true', and the "diagnostic_code" field indicates the
	// fault type.
	FaultCode       mon_faults.EFaultCode `protobuf:"varint,1,opt,name=fault_code,json=faultCode,proto3,enum=mon_faults.EFaultCode" json:"fault_code,omitempty"`
	DiagnosticFault bool                  `protobuf:"varint,2,opt,name=diagnostic_fault,json=diagnosticFault,proto3" json:"diagnostic_fault,omitempty"`
	// Subcode for fault
	FaultSubcode *mon_faults.MmuSubFaultTypeValue `protobuf:"bytes,28,opt,name=fault_subcode,json=faultSubcode,proto3" json:"fault_subcode,omitempty"`
	// Code for a diagnostic fault.
	DiagnosticCode mon_faults.ESubFaultDiagnostic `protobuf:"varint,8,opt,name=diagnostic_code,json=diagnosticCode,proto3,enum=mon_faults.ESubFaultDiagnostic" json:"diagnostic_code,omitempty"`
	// bitmap of channels involved in the fault.  LSbit = Ch.1.  May be all 0s for a diagnostic fault.
	FaultChannelsChmap uint32 `protobuf:"fixed32,9,opt,name=fault_channels_chmap,json=faultChannelsChmap,proto3" json:"fault_channels_chmap,omitempty"`
	// Incremented for each new fault and matches the Fault Log entry for the fault
	LastFaultId uint32 `protobuf:"varint,10,opt,name=last_fault_id,json=lastFaultId,proto3" json:"last_fault_id,omitempty"`
	// The status of the monitor panel LEDs.
	Leds *PanelLedsBitmap `protobuf:"bytes,11,opt,name=leds,proto3" json:"leds,omitempty"`
	// Mains AC voltage or DC supply voltage in Volts.
	//
	// Types that are valid to be assigned to SupplyVoltage:
	//
	//	*MonitorPresentStatus_AcMainsVoltage
	//	*MonitorPresentStatus_DcSupplyVoltage
	SupplyVoltage isMonitorPresentStatus_SupplyVoltage `protobuf_oneof:"supply_voltage"`
	// Mains AC supply frequency in Hz or DC supply voltage ripple in mV, depending on model options.
	//
	// Types that are valid to be assigned to SupplyCharacteristic:
	//
	//	*MonitorPresentStatus_AcFrequencyHz
	//	*MonitorPresentStatus_DcRippleMv
	SupplyCharacteristic isMonitorPresentStatus_SupplyCharacteristic `protobuf_oneof:"supply_characteristic"`
	// Present monitor unit temperature in degrees F.
	TemperatureDegf float32 `protobuf:"fixed32,16,opt,name=temperature_degf,json=temperatureDegf,proto3" json:"temperature_degf,omitempty"`
	// minimum flash time remaining in seconds.
	MinFlashRemainingS uint32 `protobuf:"varint,17,opt,name=min_flash_remaining_s,json=minFlashRemainingS,proto3" json:"min_flash_remaining_s,omitempty"`
	// Timeout count for controller, no message frame received in over 100 ms since midnight or last clear of statistics
	ControllerTimeoutCount uint32 `protobuf:"varint,18,opt,name=controller_timeout_count,json=controllerTimeoutCount,proto3" json:"controller_timeout_count,omitempty"`
	// Controller time now
	ControllerDateTime *basic.LocalDateTime `protobuf:"bytes,20,opt,name=controller_date_time,json=controllerDateTime,proto3" json:"controller_date_time,omitempty"`
	// Monitor RTC time now
	RtcDateTime *basic.LocalDateTime `protobuf:"bytes,21,opt,name=rtc_date_time,json=rtcDateTime,proto3" json:"rtc_date_time,omitempty"`
	// (Uptime) Total run time for the monitor since being shipped, in seconds
	TotalRunTime uint32 `protobuf:"varint,22,opt,name=total_run_time,json=totalRunTime,proto3" json:"total_run_time,omitempty"`
	// Total time the monitor has been in flash since being shipped, in seconds
	TotalFlashTime uint32 `protobuf:"varint,23,opt,name=total_flash_time,json=totalFlashTime,proto3" json:"total_flash_time,omitempty"`
	// Total time the monitor has been in a fault state since being shipped, in seconds
	TotalFaultTime uint32 `protobuf:"varint,24,opt,name=total_fault_time,json=totalFaultTime,proto3" json:"total_fault_time,omitempty"`
	// The most recent configuration log entry ID
	LastConfigId uint32 `protobuf:"varint,25,opt,name=last_config_id,json=lastConfigId,proto3" json:"last_config_id,omitempty"`
	// States of monitored inputs
	//
	// Types that are valid to be assigned to MonitoredInputStates:
	//
	//	*MonitorPresentStatus_MmuStates
	MonitoredInputStates isMonitorPresentStatus_MonitoredInputStates `protobuf_oneof:"monitored_input_states"`
	// monitored input voltages
	//
	// Types that are valid to be assigned to MonitoredInputVolts:
	//
	//	*MonitorPresentStatus_MmuVoltages
	MonitoredInputVolts isMonitorPresentStatus_MonitoredInputVolts `protobuf_oneof:"monitored_input_volts"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MonitorPresentStatus) Reset() {
	*x = MonitorPresentStatus{}
	mi := &file_realtime_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MonitorPresentStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorPresentStatus) ProtoMessage() {}

func (x *MonitorPresentStatus) ProtoReflect() protoreflect.Message {
	mi := &file_realtime_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorPresentStatus.ProtoReflect.Descriptor instead.
func (*MonitorPresentStatus) Descriptor() ([]byte, []int) {
	return file_realtime_proto_rawDescGZIP(), []int{7}
}

func (x *MonitorPresentStatus) GetFaultCode() mon_faults.EFaultCode {
	if x != nil {
		return x.FaultCode
	}
	return mon_faults.EFaultCode(0)
}

func (x *MonitorPresentStatus) GetDiagnosticFault() bool {
	if x != nil {
		return x.DiagnosticFault
	}
	return false
}

func (x *MonitorPresentStatus) GetFaultSubcode() *mon_faults.MmuSubFaultTypeValue {
	if x != nil {
		return x.FaultSubcode
	}
	return nil
}

func (x *MonitorPresentStatus) GetDiagnosticCode() mon_faults.ESubFaultDiagnostic {
	if x != nil {
		return x.DiagnosticCode
	}
	return mon_faults.ESubFaultDiagnostic(0)
}

func (x *MonitorPresentStatus) GetFaultChannelsChmap() uint32 {
	if x != nil {
		return x.FaultChannelsChmap
	}
	return 0
}

func (x *MonitorPresentStatus) GetLastFaultId() uint32 {
	if x != nil {
		return x.LastFaultId
	}
	return 0
}

func (x *MonitorPresentStatus) GetLeds() *PanelLedsBitmap {
	if x != nil {
		return x.Leds
	}
	return nil
}

func (x *MonitorPresentStatus) GetSupplyVoltage() isMonitorPresentStatus_SupplyVoltage {
	if x != nil {
		return x.SupplyVoltage
	}
	return nil
}

func (x *MonitorPresentStatus) GetAcMainsVoltage() float32 {
	if x != nil {
		if x, ok := x.SupplyVoltage.(*MonitorPresentStatus_AcMainsVoltage); ok {
			return x.AcMainsVoltage
		}
	}
	return 0
}

func (x *MonitorPresentStatus) GetDcSupplyVoltage() float32 {
	if x != nil {
		if x, ok := x.SupplyVoltage.(*MonitorPresentStatus_DcSupplyVoltage); ok {
			return x.DcSupplyVoltage
		}
	}
	return 0
}

func (x *MonitorPresentStatus) GetSupplyCharacteristic() isMonitorPresentStatus_SupplyCharacteristic {
	if x != nil {
		return x.SupplyCharacteristic
	}
	return nil
}

func (x *MonitorPresentStatus) GetAcFrequencyHz() float32 {
	if x != nil {
		if x, ok := x.SupplyCharacteristic.(*MonitorPresentStatus_AcFrequencyHz); ok {
			return x.AcFrequencyHz
		}
	}
	return 0
}

func (x *MonitorPresentStatus) GetDcRippleMv() float32 {
	if x != nil {
		if x, ok := x.SupplyCharacteristic.(*MonitorPresentStatus_DcRippleMv); ok {
			return x.DcRippleMv
		}
	}
	return 0
}

func (x *MonitorPresentStatus) GetTemperatureDegf() float32 {
	if x != nil {
		return x.TemperatureDegf
	}
	return 0
}

func (x *MonitorPresentStatus) GetMinFlashRemainingS() uint32 {
	if x != nil {
		return x.MinFlashRemainingS
	}
	return 0
}

func (x *MonitorPresentStatus) GetControllerTimeoutCount() uint32 {
	if x != nil {
		return x.ControllerTimeoutCount
	}
	return 0
}

func (x *MonitorPresentStatus) GetControllerDateTime() *basic.LocalDateTime {
	if x != nil {
		return x.ControllerDateTime
	}
	return nil
}

func (x *MonitorPresentStatus) GetRtcDateTime() *basic.LocalDateTime {
	if x != nil {
		return x.RtcDateTime
	}
	return nil
}

func (x *MonitorPresentStatus) GetTotalRunTime() uint32 {
	if x != nil {
		return x.TotalRunTime
	}
	return 0
}

func (x *MonitorPresentStatus) GetTotalFlashTime() uint32 {
	if x != nil {
		return x.TotalFlashTime
	}
	return 0
}

func (x *MonitorPresentStatus) GetTotalFaultTime() uint32 {
	if x != nil {
		return x.TotalFaultTime
	}
	return 0
}

func (x *MonitorPresentStatus) GetLastConfigId() uint32 {
	if x != nil {
		return x.LastConfigId
	}
	return 0
}

func (x *MonitorPresentStatus) GetMonitoredInputStates() isMonitorPresentStatus_MonitoredInputStates {
	if x != nil {
		return x.MonitoredInputStates
	}
	return nil
}

func (x *MonitorPresentStatus) GetMmuStates() *mon_faults.MmuMonitoredInputsStatusBitmap {
	if x != nil {
		if x, ok := x.MonitoredInputStates.(*MonitorPresentStatus_MmuStates); ok {
			return x.MmuStates
		}
	}
	return nil
}

func (x *MonitorPresentStatus) GetMonitoredInputVolts() isMonitorPresentStatus_MonitoredInputVolts {
	if x != nil {
		return x.MonitoredInputVolts
	}
	return nil
}

func (x *MonitorPresentStatus) GetMmuVoltages() *MmuMonitoredInputsVoltages {
	if x != nil {
		if x, ok := x.MonitoredInputVolts.(*MonitorPresentStatus_MmuVoltages); ok {
			return x.MmuVoltages
		}
	}
	return nil
}

type isMonitorPresentStatus_SupplyVoltage interface {
	isMonitorPresentStatus_SupplyVoltage()
}

type MonitorPresentStatus_AcMainsVoltage struct {
	AcMainsVoltage float32 `protobuf:"fixed32,12,opt,name=ac_mains_voltage,json=acMainsVoltage,proto3,oneof"`
}

type MonitorPresentStatus_DcSupplyVoltage struct {
	DcSupplyVoltage float32 `protobuf:"fixed32,13,opt,name=dc_supply_voltage,json=dcSupplyVoltage,proto3,oneof"`
}

func (*MonitorPresentStatus_AcMainsVoltage) isMonitorPresentStatus_SupplyVoltage() {}

func (*MonitorPresentStatus_DcSupplyVoltage) isMonitorPresentStatus_SupplyVoltage() {}

type isMonitorPresentStatus_SupplyCharacteristic interface {
	isMonitorPresentStatus_SupplyCharacteristic()
}

type MonitorPresentStatus_AcFrequencyHz struct {
	AcFrequencyHz float32 `protobuf:"fixed32,14,opt,name=ac_frequency_hz,json=acFrequencyHz,proto3,oneof"`
}

type MonitorPresentStatus_DcRippleMv struct {
	DcRippleMv float32 `protobuf:"fixed32,15,opt,name=dc_ripple_mv,json=dcRippleMv,proto3,oneof"`
}

func (*MonitorPresentStatus_AcFrequencyHz) isMonitorPresentStatus_SupplyCharacteristic() {}

func (*MonitorPresentStatus_DcRippleMv) isMonitorPresentStatus_SupplyCharacteristic() {}

type isMonitorPresentStatus_MonitoredInputStates interface {
	isMonitorPresentStatus_MonitoredInputStates()
}

type MonitorPresentStatus_MmuStates struct {
	MmuStates *mon_faults.MmuMonitoredInputsStatusBitmap `protobuf:"bytes,26,opt,name=mmu_states,json=mmuStates,proto3,oneof"` //  CMU
}

func (*MonitorPresentStatus_MmuStates) isMonitorPresentStatus_MonitoredInputStates() {}

type isMonitorPresentStatus_MonitoredInputVolts interface {
	isMonitorPresentStatus_MonitoredInputVolts()
}

type MonitorPresentStatus_MmuVoltages struct {
	MmuVoltages *MmuMonitoredInputsVoltages `protobuf:"bytes,27,opt,name=mmu_voltages,json=mmuVoltages,proto3,oneof"` // TODO add CMU voltages
}

func (*MonitorPresentStatus_MmuVoltages) isMonitorPresentStatus_MonitoredInputVolts() {}

var File_realtime_proto protoreflect.FileDescriptor

const file_realtime_proto_rawDesc = "" +
	"\n" +
	"\x0erealtime.proto\x12\brealtime\x1a\vbasic.proto\x1a\x10mon_faults.proto\"\xf9\x04\n" +
	"\x1dChannelVoltsCurrentsAndTimers\x12\x18\n" +
	"\achannel\x18\x01 \x01(\rR\achannel\x12\x1b\n" +
	"\tred_volts\x18\x02 \x01(\x02R\bredVolts\x12!\n" +
	"\fyellow_volts\x18\x03 \x01(\x02R\vyellowVolts\x12\x1f\n" +
	"\vgreen_volts\x18\x04 \x01(\x02R\n" +
	"greenVolts\x12\x1d\n" +
	"\n" +
	"walk_volts\x18\x05 \x01(\x02R\twalkVolts\x12\x19\n" +
	"\bred_amps\x18\x06 \x01(\x02R\aredAmps\x12\x1f\n" +
	"\vyellow_amps\x18\a \x01(\x02R\n" +
	"yellowAmps\x12\x1d\n" +
	"\n" +
	"green_amps\x18\b \x01(\x02R\tgreenAmps\x12\x1b\n" +
	"\twalk_amps\x18\t \x01(\x02R\bwalkAmps\x12%\n" +
	"\x0fred_off_time_ms\x18\n" +
	" \x01(\x02R\fredOffTimeMs\x12+\n" +
	"\x12yellow_off_time_ms\x18\v \x01(\x02R\x0fyellowOffTimeMs\x12)\n" +
	"\x11green_off_time_ms\x18\f \x01(\x02R\x0egreenOffTimeMs\x12'\n" +
	"\x10walk_off_time_ms\x18\r \x01(\x02R\rwalkOffTimeMs\x12#\n" +
	"\x0ered_on_time_ms\x18\x0e \x01(\x02R\vredOnTimeMs\x12)\n" +
	"\x11yellow_on_time_ms\x18\x0f \x01(\x02R\x0eyellowOnTimeMs\x12'\n" +
	"\x10green_on_time_ms\x18\x10 \x01(\x02R\rgreenOnTimeMs\x12%\n" +
	"\x0fwalk_on_time_ms\x18\x11 \x01(\x02R\fwalkOnTimeMs\"\x96\x04\n" +
	"\x11ChannelStatusData\x12$\n" +
	"\x0eblues_on_chmap\x18\x01 \x01(\aR\fbluesOnChmap\x12\"\n" +
	"\rreds_on_chmap\x18\x02 \x01(\aR\vredsOnChmap\x12(\n" +
	"\x10yellows_on_chmap\x18\x03 \x01(\aR\x0eyellowsOnChmap\x12&\n" +
	"\x0fgreens_on_chmap\x18\x04 \x01(\aR\rgreensOnChmap\x12$\n" +
	"\x0ewalks_on_chmap\x18\x05 \x01(\aR\fwalksOnChmap\x123\n" +
	"\x16reds_field_check_chmap\x18\x06 \x01(\aR\x13redsFieldCheckChmap\x129\n" +
	"\x19yellows_field_check_chmap\x18\a \x01(\aR\x16yellowsFieldCheckChmap\x127\n" +
	"\x18greens_field_check_chmap\x18\b \x01(\aR\x15greensFieldCheckChmap\x125\n" +
	"\x17walks_field_check_chmap\x18\t \x01(\aR\x14walksFieldCheckChmap\x12_\n" +
	"\x18channel_indicators_v_i_t\x18\n" +
	" \x03(\v2'.realtime.ChannelVoltsCurrentsAndTimersR\x14channelIndicatorsVIT\"G\n" +
	"\x12DisplayLineControl\x12\x1a\n" +
	"\bmodified\x18\x01 \x01(\bR\bmodified\x12\x15\n" +
	"\x06in_use\x18\x02 \x01(\bR\x05inUse\"\xa5\x01\n" +
	"\x0eLcdDisplayLine\x12\x1f\n" +
	"\vline_number\x18\x01 \x01(\rR\n" +
	"lineNumber\x124\n" +
	"\x06format\x18\x02 \x01(\v2\x1c.realtime.DisplayLineControlR\x06format\x12\x1b\n" +
	"\tline_text\x18\x03 \x01(\fR\blineText\x12\x1f\n" +
	"\vline_colors\x18\x04 \x01(\fR\n" +
	"lineColors\"\xa7\x05\n" +
	"\x12MonitorDisplayData\x120\n" +
	"\x14enable_field_display\x18\f \x01(\bR\x12enableFieldDisplay\x12I\n" +
	"\x10display_channels\x18\r \x01(\x0e2\x1e.realtime.EDisplayChannelsTypeR\x0fdisplayChannels\x124\n" +
	"\x17red_field_leds_on_chmap\x18\x01 \x01(\aR\x13redFieldLedsOnChmap\x12:\n" +
	"\x1ayellow_field_leds_on_chmap\x18\x02 \x01(\aR\x16yellowFieldLedsOnChmap\x128\n" +
	"\x19green_field_leds_on_chmap\x18\x03 \x01(\aR\x15greenFieldLedsOnChmap\x126\n" +
	"\x18walk_field_leds_on_chmap\x18\x04 \x01(\aR\x14walkFieldLedsOnChmap\x126\n" +
	"\x18blue_fault_leds_on_chmap\x18\x05 \x01(\aR\x14blueFaultLedsOnChmap\x12\"\n" +
	"\rred_fcs_chmap\x18\x06 \x01(\aR\vredFcsChmap\x12(\n" +
	"\x10yellow_fcs_chmap\x18\a \x01(\aR\x0eyellowFcsChmap\x12&\n" +
	"\x0fgreen_fcs_chmap\x18\b \x01(\aR\rgreenFcsChmap\x12$\n" +
	"\x0ewalk_fcs_chmap\x18\t \x01(\aR\fwalkFcsChmap\x12\x1d\n" +
	"\n" +
	"line_count\x18\n" +
	" \x01(\rR\tlineCount\x12=\n" +
	"\rdisplay_lines\x18\v \x03(\v2\x18.realtime.LcdDisplayLineR\fdisplayLines\"Q\n" +
	"\x0fPanelLedsBitmap\x12\x14\n" +
	"\x05power\x18\x01 \x01(\bR\x05power\x12\x14\n" +
	"\x05fault\x18\x02 \x01(\bR\x05fault\x12\x12\n" +
	"\x04diag\x18\x03 \x01(\bR\x04diag\"\xba\x04\n" +
	"\x1aMmuMonitoredInputsVoltages\x127\n" +
	"\x18input1_24v_inhibit_volts\x18\x01 \x01(\x02R\x15input124vInhibitVolts\x12:\n" +
	"\x1ainput2_24v_monitor_1_volts\x18\x02 \x01(\x02R\x16input224vMonitor1Volts\x12:\n" +
	"\x1ainput3_24v_monitor_2_volts\x18\x03 \x01(\x02R\x16input324vMonitor2Volts\x12:\n" +
	"\x19input4_controller_voltage\x18\x04 \x01(\x02R\x17input4ControllerVoltage\x127\n" +
	"\x18input5_type_select_volts\x18\x05 \x01(\x02R\x15input5TypeSelectVolts\x125\n" +
	"\x17input6_red_enable_volts\x18\x06 \x01(\x02R\x14input6RedEnableVolts\x12=\n" +
	"\x1binput7_external_reset_volts\x18\a \x01(\x02R\x18input7ExternalResetVolts\x12;\n" +
	"\x1ainput8_port1_disable_volts\x18\b \x01(\x02R\x17input8Port1DisableVolts\x12C\n" +
	"\x1einput9_external_watchdog_volts\x18\t \x01(\x02R\x1binput9ExternalWatchdogVolts\"\xf2\t\n" +
	"\x14MonitorPresentStatus\x125\n" +
	"\n" +
	"fault_code\x18\x01 \x01(\x0e2\x16.mon_faults.EFaultCodeR\tfaultCode\x12)\n" +
	"\x10diagnostic_fault\x18\x02 \x01(\bR\x0fdiagnosticFault\x12E\n" +
	"\rfault_subcode\x18\x1c \x01(\v2 .mon_faults.MmuSubFaultTypeValueR\ffaultSubcode\x12H\n" +
	"\x0fdiagnostic_code\x18\b \x01(\x0e2\x1f.mon_faults.ESubFaultDiagnosticR\x0ediagnosticCode\x120\n" +
	"\x14fault_channels_chmap\x18\t \x01(\aR\x12faultChannelsChmap\x12\"\n" +
	"\rlast_fault_id\x18\n" +
	" \x01(\rR\vlastFaultId\x12-\n" +
	"\x04leds\x18\v \x01(\v2\x19.realtime.PanelLedsBitmapR\x04leds\x12*\n" +
	"\x10ac_mains_voltage\x18\f \x01(\x02H\x00R\x0eacMainsVoltage\x12,\n" +
	"\x11dc_supply_voltage\x18\r \x01(\x02H\x00R\x0fdcSupplyVoltage\x12(\n" +
	"\x0fac_frequency_hz\x18\x0e \x01(\x02H\x01R\racFrequencyHz\x12\"\n" +
	"\fdc_ripple_mv\x18\x0f \x01(\x02H\x01R\n" +
	"dcRippleMv\x12)\n" +
	"\x10temperature_degf\x18\x10 \x01(\x02R\x0ftemperatureDegf\x121\n" +
	"\x15min_flash_remaining_s\x18\x11 \x01(\rR\x12minFlashRemainingS\x128\n" +
	"\x18controller_timeout_count\x18\x12 \x01(\rR\x16controllerTimeoutCount\x12F\n" +
	"\x14controller_date_time\x18\x14 \x01(\v2\x14.basic.LocalDateTimeR\x12controllerDateTime\x128\n" +
	"\rrtc_date_time\x18\x15 \x01(\v2\x14.basic.LocalDateTimeR\vrtcDateTime\x12$\n" +
	"\x0etotal_run_time\x18\x16 \x01(\rR\ftotalRunTime\x12(\n" +
	"\x10total_flash_time\x18\x17 \x01(\rR\x0etotalFlashTime\x12(\n" +
	"\x10total_fault_time\x18\x18 \x01(\rR\x0etotalFaultTime\x12$\n" +
	"\x0elast_config_id\x18\x19 \x01(\rR\flastConfigId\x12K\n" +
	"\n" +
	"mmu_states\x18\x1a \x01(\v2*.mon_faults.MmuMonitoredInputsStatusBitmapH\x02R\tmmuStates\x12I\n" +
	"\fmmu_voltages\x18\x1b \x01(\v2$.realtime.MmuMonitoredInputsVoltagesH\x03R\vmmuVoltagesB\x10\n" +
	"\x0esupply_voltageB\x17\n" +
	"\x15supply_characteristicB\x18\n" +
	"\x16monitored_input_statesB\x17\n" +
	"\x15monitored_input_voltsJ\x04\b\x03\x10\bJ\x04\b\x13\x10\x14*~\n" +
	"\x14EDisplayChannelsType\x12\x17\n" +
	"\x13DISP_CH_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tDISP_CH_3\x10\x01\x12\r\n" +
	"\tDISP_CH_6\x10\x02\x12\x0e\n" +
	"\n" +
	"DISP_CH_12\x10\x03\x12\x0e\n" +
	"\n" +
	"DISP_CH_16\x10\x04\x12\x0f\n" +
	"\vDISP_CH_16X\x10\x05b\x06proto3"

var (
	file_realtime_proto_rawDescOnce sync.Once
	file_realtime_proto_rawDescData []byte
)

func file_realtime_proto_rawDescGZIP() []byte {
	file_realtime_proto_rawDescOnce.Do(func() {
		file_realtime_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_realtime_proto_rawDesc), len(file_realtime_proto_rawDesc)))
	})
	return file_realtime_proto_rawDescData
}

var file_realtime_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_realtime_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_realtime_proto_goTypes = []any{
	(EDisplayChannelsType)(0),                         // 0: realtime.EDisplayChannelsType
	(*ChannelVoltsCurrentsAndTimers)(nil),             // 1: realtime.ChannelVoltsCurrentsAndTimers
	(*ChannelStatusData)(nil),                         // 2: realtime.ChannelStatusData
	(*DisplayLineControl)(nil),                        // 3: realtime.DisplayLineControl
	(*LcdDisplayLine)(nil),                            // 4: realtime.LcdDisplayLine
	(*MonitorDisplayData)(nil),                        // 5: realtime.MonitorDisplayData
	(*PanelLedsBitmap)(nil),                           // 6: realtime.PanelLedsBitmap
	(*MmuMonitoredInputsVoltages)(nil),                // 7: realtime.MmuMonitoredInputsVoltages
	(*MonitorPresentStatus)(nil),                      // 8: realtime.MonitorPresentStatus
	(mon_faults.EFaultCode)(0),                        // 9: mon_faults.EFaultCode
	(*mon_faults.MmuSubFaultTypeValue)(nil),           // 10: mon_faults.MmuSubFaultTypeValue
	(mon_faults.ESubFaultDiagnostic)(0),               // 11: mon_faults.ESubFaultDiagnostic
	(*basic.LocalDateTime)(nil),                       // 12: basic.LocalDateTime
	(*mon_faults.MmuMonitoredInputsStatusBitmap)(nil), // 13: mon_faults.MmuMonitoredInputsStatusBitmap
}
var file_realtime_proto_depIdxs = []int32{
	1,  // 0: realtime.ChannelStatusData.channel_indicators_v_i_t:type_name -> realtime.ChannelVoltsCurrentsAndTimers
	3,  // 1: realtime.LcdDisplayLine.format:type_name -> realtime.DisplayLineControl
	0,  // 2: realtime.MonitorDisplayData.display_channels:type_name -> realtime.EDisplayChannelsType
	4,  // 3: realtime.MonitorDisplayData.display_lines:type_name -> realtime.LcdDisplayLine
	9,  // 4: realtime.MonitorPresentStatus.fault_code:type_name -> mon_faults.EFaultCode
	10, // 5: realtime.MonitorPresentStatus.fault_subcode:type_name -> mon_faults.MmuSubFaultTypeValue
	11, // 6: realtime.MonitorPresentStatus.diagnostic_code:type_name -> mon_faults.ESubFaultDiagnostic
	6,  // 7: realtime.MonitorPresentStatus.leds:type_name -> realtime.PanelLedsBitmap
	12, // 8: realtime.MonitorPresentStatus.controller_date_time:type_name -> basic.LocalDateTime
	12, // 9: realtime.MonitorPresentStatus.rtc_date_time:type_name -> basic.LocalDateTime
	13, // 10: realtime.MonitorPresentStatus.mmu_states:type_name -> mon_faults.MmuMonitoredInputsStatusBitmap
	7,  // 11: realtime.MonitorPresentStatus.mmu_voltages:type_name -> realtime.MmuMonitoredInputsVoltages
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_realtime_proto_init() }
func file_realtime_proto_init() {
	if File_realtime_proto != nil {
		return
	}
	file_realtime_proto_msgTypes[7].OneofWrappers = []any{
		(*MonitorPresentStatus_AcMainsVoltage)(nil),
		(*MonitorPresentStatus_DcSupplyVoltage)(nil),
		(*MonitorPresentStatus_AcFrequencyHz)(nil),
		(*MonitorPresentStatus_DcRippleMv)(nil),
		(*MonitorPresentStatus_MmuStates)(nil),
		(*MonitorPresentStatus_MmuVoltages)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_realtime_proto_rawDesc), len(file_realtime_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_realtime_proto_goTypes,
		DependencyIndexes: file_realtime_proto_depIdxs,
		EnumInfos:         file_realtime_proto_enumTypes,
		MessageInfos:      file_realtime_proto_msgTypes,
	}.Build()
	File_realtime_proto = out.File
	file_realtime_proto_goTypes = nil
	file_realtime_proto_depIdxs = nil
}
