//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  CMD_RESP_COMMS
//Command (from app) and response (from monitor) message formats for communications related commands.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: cmd_resp_comms.proto

package cmd_resp_comms

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EAuthenticationResult provides authentication status.
type EAuthenticationResult int32

const (
	EAuthenticationResult_AUTH_UNSPECIFIED    EAuthenticationResult = 0
	EAuthenticationResult_AUTH_SUCCESS        EAuthenticationResult = 1 // Authentication succeeded
	EAuthenticationResult_AUTH_NO_ACCESS      EAuthenticationResult = 2 // No access allowed on this unit* (see note below)
	EAuthenticationResult_AUTH_HAS_CONNECTION EAuthenticationResult = 7 // An App is already connected to this device, new connection refused.
)

// Enum value maps for EAuthenticationResult.
var (
	EAuthenticationResult_name = map[int32]string{
		0: "AUTH_UNSPECIFIED",
		1: "AUTH_SUCCESS",
		2: "AUTH_NO_ACCESS",
		7: "AUTH_HAS_CONNECTION",
	}
	EAuthenticationResult_value = map[string]int32{
		"AUTH_UNSPECIFIED":    0,
		"AUTH_SUCCESS":        1,
		"AUTH_NO_ACCESS":      2,
		"AUTH_HAS_CONNECTION": 7,
	}
)

func (x EAuthenticationResult) Enum() *EAuthenticationResult {
	p := new(EAuthenticationResult)
	*p = x
	return p
}

func (x EAuthenticationResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAuthenticationResult) Descriptor() protoreflect.EnumDescriptor {
	return file_cmd_resp_comms_proto_enumTypes[0].Descriptor()
}

func (EAuthenticationResult) Type() protoreflect.EnumType {
	return &file_cmd_resp_comms_proto_enumTypes[0]
}

func (x EAuthenticationResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EAuthenticationResult.Descriptor instead.
func (EAuthenticationResult) EnumDescriptor() ([]byte, []int) {
	return file_cmd_resp_comms_proto_rawDescGZIP(), []int{0}
}

//	RespAuthStatus is the first message sent after establishing a WebSocket connection, by the MMU
//
// (There is no command that generates it.)  It communicates the connection status to the connecting
// App.  In the case of AUTH_NO_ACCESS and AUTH_HAS_CONNECTION, the MMU will disconnect itself after
// sending this message.
type RespAuthStatus struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The result of the authentication
	Result EAuthenticationResult `protobuf:"varint,1,opt,name=result,proto3,enum=cmd_resp_comms.EAuthenticationResult" json:"result,omitempty"`
	// If the result is "AUTH_HAS_CONNECTION", then this contains the account name from the
	// existing connection's certificate.  Otherwise, this field is empty.
	ExistingConnectionName string `protobuf:"bytes,2,opt,name=existing_connection_name,json=existingConnectionName,proto3" json:"existing_connection_name,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *RespAuthStatus) Reset() {
	*x = RespAuthStatus{}
	mi := &file_cmd_resp_comms_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespAuthStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespAuthStatus) ProtoMessage() {}

func (x *RespAuthStatus) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_comms_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespAuthStatus.ProtoReflect.Descriptor instead.
func (*RespAuthStatus) Descriptor() ([]byte, []int) {
	return file_cmd_resp_comms_proto_rawDescGZIP(), []int{0}
}

func (x *RespAuthStatus) GetResult() EAuthenticationResult {
	if x != nil {
		return x.Result
	}
	return EAuthenticationResult_AUTH_UNSPECIFIED
}

func (x *RespAuthStatus) GetExistingConnectionName() string {
	if x != nil {
		return x.ExistingConnectionName
	}
	return ""
}

// CmdChunkTest is used exclusively for testing interfaces (esp. BLE) with large messages
type CmdChunkTest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []byte                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // Max data length set in cmd_resp_comms.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdChunkTest) Reset() {
	*x = CmdChunkTest{}
	mi := &file_cmd_resp_comms_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdChunkTest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdChunkTest) ProtoMessage() {}

func (x *CmdChunkTest) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_comms_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdChunkTest.ProtoReflect.Descriptor instead.
func (*CmdChunkTest) Descriptor() ([]byte, []int) {
	return file_cmd_resp_comms_proto_rawDescGZIP(), []int{1}
}

func (x *CmdChunkTest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// RespChunkTest returns the contents of the bytes sent in CmdChunkTest.data
type RespChunkTest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DataEcho      []byte                 `protobuf:"bytes,1,opt,name=data_echo,json=dataEcho,proto3" json:"data_echo,omitempty"` // Max data length set in cmd_resp_comms.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespChunkTest) Reset() {
	*x = RespChunkTest{}
	mi := &file_cmd_resp_comms_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespChunkTest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespChunkTest) ProtoMessage() {}

func (x *RespChunkTest) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_comms_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespChunkTest.ProtoReflect.Descriptor instead.
func (*RespChunkTest) Descriptor() ([]byte, []int) {
	return file_cmd_resp_comms_proto_rawDescGZIP(), []int{2}
}

func (x *RespChunkTest) GetDataEcho() []byte {
	if x != nil {
		return x.DataEcho
	}
	return nil
}

var File_cmd_resp_comms_proto protoreflect.FileDescriptor

const file_cmd_resp_comms_proto_rawDesc = "" +
	"\n" +
	"\x14cmd_resp_comms.proto\x12\x0ecmd_resp_comms\"\x89\x01\n" +
	"\x0eRespAuthStatus\x12=\n" +
	"\x06result\x18\x01 \x01(\x0e2%.cmd_resp_comms.EAuthenticationResultR\x06result\x128\n" +
	"\x18existing_connection_name\x18\x02 \x01(\tR\x16existingConnectionName\"\"\n" +
	"\fCmdChunkTest\x12\x12\n" +
	"\x04data\x18\x01 \x01(\fR\x04data\",\n" +
	"\rRespChunkTest\x12\x1b\n" +
	"\tdata_echo\x18\x01 \x01(\fR\bdataEcho*\x84\x01\n" +
	"\x15EAuthenticationResult\x12\x14\n" +
	"\x10AUTH_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fAUTH_SUCCESS\x10\x01\x12\x12\n" +
	"\x0eAUTH_NO_ACCESS\x10\x02\x12\x17\n" +
	"\x13AUTH_HAS_CONNECTION\x10\a\"\x04\b\x03\x10\x03\"\x04\b\x04\x10\x04\"\x04\b\x05\x10\x05\"\x04\b\x06\x10\x06b\x06proto3"

var (
	file_cmd_resp_comms_proto_rawDescOnce sync.Once
	file_cmd_resp_comms_proto_rawDescData []byte
)

func file_cmd_resp_comms_proto_rawDescGZIP() []byte {
	file_cmd_resp_comms_proto_rawDescOnce.Do(func() {
		file_cmd_resp_comms_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cmd_resp_comms_proto_rawDesc), len(file_cmd_resp_comms_proto_rawDesc)))
	})
	return file_cmd_resp_comms_proto_rawDescData
}

var file_cmd_resp_comms_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cmd_resp_comms_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_cmd_resp_comms_proto_goTypes = []any{
	(EAuthenticationResult)(0), // 0: cmd_resp_comms.EAuthenticationResult
	(*RespAuthStatus)(nil),     // 1: cmd_resp_comms.RespAuthStatus
	(*CmdChunkTest)(nil),       // 2: cmd_resp_comms.CmdChunkTest
	(*RespChunkTest)(nil),      // 3: cmd_resp_comms.RespChunkTest
}
var file_cmd_resp_comms_proto_depIdxs = []int32{
	0, // 0: cmd_resp_comms.RespAuthStatus.result:type_name -> cmd_resp_comms.EAuthenticationResult
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_cmd_resp_comms_proto_init() }
func file_cmd_resp_comms_proto_init() {
	if File_cmd_resp_comms_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cmd_resp_comms_proto_rawDesc), len(file_cmd_resp_comms_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_resp_comms_proto_goTypes,
		DependencyIndexes: file_cmd_resp_comms_proto_depIdxs,
		EnumInfos:         file_cmd_resp_comms_proto_enumTypes,
		MessageInfos:      file_cmd_resp_comms_proto_msgTypes,
	}.Build()
	File_cmd_resp_comms_proto = out.File
	file_cmd_resp_comms_proto_goTypes = nil
	file_cmd_resp_comms_proto_depIdxs = nil
}
