//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  BASIC
//General use messages for common complex fields.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: basic.proto

package basic

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EDaysOfTheWeek
type EDaysOfTheWeek int32

const (
	EDaysOfTheWeek_DAY_UNSPECIFIED EDaysOfTheWeek = 0
	EDaysOfTheWeek_DAY_SUNDAY      EDaysOfTheWeek = 1
	EDaysOfTheWeek_DAY_MONDAY      EDaysOfTheWeek = 2
	EDaysOfTheWeek_DAY_TUESDAY     EDaysOfTheWeek = 3
	EDaysOfTheWeek_DAY_WEDNESDAY   EDaysOfTheWeek = 4
	EDaysOfTheWeek_DAY_THURSDAY    EDaysOfTheWeek = 5
	EDaysOfTheWeek_DAY_FRIDAY      EDaysOfTheWeek = 6
	EDaysOfTheWeek_DAY_SATURDAY    EDaysOfTheWeek = 7
)

// Enum value maps for EDaysOfTheWeek.
var (
	EDaysOfTheWeek_name = map[int32]string{
		0: "DAY_UNSPECIFIED",
		1: "DAY_SUNDAY",
		2: "DAY_MONDAY",
		3: "DAY_TUESDAY",
		4: "DAY_WEDNESDAY",
		5: "DAY_THURSDAY",
		6: "DAY_FRIDAY",
		7: "DAY_SATURDAY",
	}
	EDaysOfTheWeek_value = map[string]int32{
		"DAY_UNSPECIFIED": 0,
		"DAY_SUNDAY":      1,
		"DAY_MONDAY":      2,
		"DAY_TUESDAY":     3,
		"DAY_WEDNESDAY":   4,
		"DAY_THURSDAY":    5,
		"DAY_FRIDAY":      6,
		"DAY_SATURDAY":    7,
	}
)

func (x EDaysOfTheWeek) Enum() *EDaysOfTheWeek {
	p := new(EDaysOfTheWeek)
	*p = x
	return p
}

func (x EDaysOfTheWeek) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EDaysOfTheWeek) Descriptor() protoreflect.EnumDescriptor {
	return file_basic_proto_enumTypes[0].Descriptor()
}

func (EDaysOfTheWeek) Type() protoreflect.EnumType {
	return &file_basic_proto_enumTypes[0]
}

func (x EDaysOfTheWeek) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EDaysOfTheWeek.Descriptor instead.
func (EDaysOfTheWeek) EnumDescriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{0}
}

//	LocalDateTime represents the time an event occurred on the device where it occurred, at the
//
// local time of the device.  This format is used to prevent misinterpretations of an epoch or
// similar time format due to timezone and daylight savings settings.
type LocalDateTime struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Year value, e.g. 2024
	Year uint32 `protobuf:"varint,1,opt,name=year,proto3" json:"year,omitempty"`
	// Month of the year, 1-12
	Month uint32 `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	// Day of the month, 1-31
	Day uint32 `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
	// Hour of the day, 0-23
	Hour uint32 `protobuf:"varint,4,opt,name=hour,proto3" json:"hour,omitempty"`
	// Minute of the hour, 0-59
	Minute uint32 `protobuf:"varint,5,opt,name=minute,proto3" json:"minute,omitempty"`
	// Second of the minute, 0-59
	Second        uint32 `protobuf:"varint,6,opt,name=second,proto3" json:"second,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalDateTime) Reset() {
	*x = LocalDateTime{}
	mi := &file_basic_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalDateTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalDateTime) ProtoMessage() {}

func (x *LocalDateTime) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalDateTime.ProtoReflect.Descriptor instead.
func (*LocalDateTime) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{0}
}

func (x *LocalDateTime) GetYear() uint32 {
	if x != nil {
		return x.Year
	}
	return 0
}

func (x *LocalDateTime) GetMonth() uint32 {
	if x != nil {
		return x.Month
	}
	return 0
}

func (x *LocalDateTime) GetDay() uint32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *LocalDateTime) GetHour() uint32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *LocalDateTime) GetMinute() uint32 {
	if x != nil {
		return x.Minute
	}
	return 0
}

func (x *LocalDateTime) GetSecond() uint32 {
	if x != nil {
		return x.Second
	}
	return 0
}

//	VersionStrThree is used for any version information with 3 parts, major.minor.revision,
//
// where the values are ASCII characters
type VersionStrThree struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Major         string                 `protobuf:"bytes,1,opt,name=major,proto3" json:"major,omitempty"`       // Max string length set in basic.options
	Minor         string                 `protobuf:"bytes,2,opt,name=minor,proto3" json:"minor,omitempty"`       // Max string length set in basic.options
	Revision      string                 `protobuf:"bytes,3,opt,name=revision,proto3" json:"revision,omitempty"` // Max string length set in basic.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VersionStrThree) Reset() {
	*x = VersionStrThree{}
	mi := &file_basic_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionStrThree) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionStrThree) ProtoMessage() {}

func (x *VersionStrThree) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionStrThree.ProtoReflect.Descriptor instead.
func (*VersionStrThree) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{1}
}

func (x *VersionStrThree) GetMajor() string {
	if x != nil {
		return x.Major
	}
	return ""
}

func (x *VersionStrThree) GetMinor() string {
	if x != nil {
		return x.Minor
	}
	return ""
}

func (x *VersionStrThree) GetRevision() string {
	if x != nil {
		return x.Revision
	}
	return ""
}

// DateStr is used for a date where where the values are ASCII characters.
type DateStr struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Month         string                 `protobuf:"bytes,1,opt,name=month,proto3" json:"month,omitempty"` // Max string length set in basic.options
	Day           string                 `protobuf:"bytes,2,opt,name=day,proto3" json:"day,omitempty"`     // Max string length set in basic.options
	Year          string                 `protobuf:"bytes,3,opt,name=year,proto3" json:"year,omitempty"`   // Max string length set in basic.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DateStr) Reset() {
	*x = DateStr{}
	mi := &file_basic_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DateStr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateStr) ProtoMessage() {}

func (x *DateStr) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateStr.ProtoReflect.Descriptor instead.
func (*DateStr) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{2}
}

func (x *DateStr) GetMonth() string {
	if x != nil {
		return x.Month
	}
	return ""
}

func (x *DateStr) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *DateStr) GetYear() string {
	if x != nil {
		return x.Year
	}
	return ""
}

// IpAddressV4 provides an IPV4 Ethernet address: octet_1_ms0.octet_2.octet_3.octet_4_lso
type IpAddressV4 struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Most Significant octet, XX.xx.xx.xx
	Octet_1Mso uint32 `protobuf:"varint,1,opt,name=octet_1_mso,json=octet1Mso,proto3" json:"octet_1_mso,omitempty"`
	// Second octet, xx.XX.xx.xx
	Octet_2 uint32 `protobuf:"varint,2,opt,name=octet_2,json=octet2,proto3" json:"octet_2,omitempty"`
	// Third octet, xx.xx.XX.xx
	Octet_3 uint32 `protobuf:"varint,3,opt,name=octet_3,json=octet3,proto3" json:"octet_3,omitempty"`
	// Least Significant octet, xx.xx.xx.XX
	Octet_4Lso    uint32 `protobuf:"varint,4,opt,name=octet_4_lso,json=octet4Lso,proto3" json:"octet_4_lso,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IpAddressV4) Reset() {
	*x = IpAddressV4{}
	mi := &file_basic_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IpAddressV4) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpAddressV4) ProtoMessage() {}

func (x *IpAddressV4) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpAddressV4.ProtoReflect.Descriptor instead.
func (*IpAddressV4) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{3}
}

func (x *IpAddressV4) GetOctet_1Mso() uint32 {
	if x != nil {
		return x.Octet_1Mso
	}
	return 0
}

func (x *IpAddressV4) GetOctet_2() uint32 {
	if x != nil {
		return x.Octet_2
	}
	return 0
}

func (x *IpAddressV4) GetOctet_3() uint32 {
	if x != nil {
		return x.Octet_3
	}
	return 0
}

func (x *IpAddressV4) GetOctet_4Lso() uint32 {
	if x != nil {
		return x.Octet_4Lso
	}
	return 0
}

// NowMinMaxFloat - Present, minimum, and maxiumum floating point values
type NowMinMaxFloat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Present       float32                `protobuf:"fixed32,1,opt,name=present,proto3" json:"present,omitempty"`
	Minimum       float32                `protobuf:"fixed32,2,opt,name=minimum,proto3" json:"minimum,omitempty"`
	Maximum       float32                `protobuf:"fixed32,3,opt,name=maximum,proto3" json:"maximum,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NowMinMaxFloat) Reset() {
	*x = NowMinMaxFloat{}
	mi := &file_basic_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NowMinMaxFloat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NowMinMaxFloat) ProtoMessage() {}

func (x *NowMinMaxFloat) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NowMinMaxFloat.ProtoReflect.Descriptor instead.
func (*NowMinMaxFloat) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{4}
}

func (x *NowMinMaxFloat) GetPresent() float32 {
	if x != nil {
		return x.Present
	}
	return 0
}

func (x *NowMinMaxFloat) GetMinimum() float32 {
	if x != nil {
		return x.Minimum
	}
	return 0
}

func (x *NowMinMaxFloat) GetMaximum() float32 {
	if x != nil {
		return x.Maximum
	}
	return 0
}

//	ModelAndSerialNumber - This is a BASIC message mostly so that the maximum length set for
//
// nanopb on the embedded side can be in one place - the basic.options file, instead of sprinkled
// in several places throughout the protobuf .options files.  This is done to prevent errors caused
// by changing the length in one place and forgetting to update others.
type ModelAndSerialNumber struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// serial number, ASCII string (null terminated)
	Serial string `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"` // Max string length set in basic.options
	// model number, ASCII string (null terminated)
	Model         string `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"` // Max string length set in basic.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelAndSerialNumber) Reset() {
	*x = ModelAndSerialNumber{}
	mi := &file_basic_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelAndSerialNumber) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelAndSerialNumber) ProtoMessage() {}

func (x *ModelAndSerialNumber) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelAndSerialNumber.ProtoReflect.Descriptor instead.
func (*ModelAndSerialNumber) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{5}
}

func (x *ModelAndSerialNumber) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *ModelAndSerialNumber) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

//	MonitorAndUserIds - This is a BASIC message mostly so that the maximum length set for
//
// nanopb on the embedded side can be in one place - the basic.options file, instead of sprinkled
// in several places throughout the protobuf .options files.  This is done to prevent errors caused
// by changing the length in one place and forgetting to update others.
type MonitorAndUserIds struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Monitor ID, ASCII string (null terminated)
	Monitor string `protobuf:"bytes,1,opt,name=monitor,proto3" json:"monitor,omitempty"` // Max string length set in basic.options
	// User ID, ASCII string (null terminated)
	User          string `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"` // Max string length set in basic.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MonitorAndUserIds) Reset() {
	*x = MonitorAndUserIds{}
	mi := &file_basic_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MonitorAndUserIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorAndUserIds) ProtoMessage() {}

func (x *MonitorAndUserIds) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorAndUserIds.ProtoReflect.Descriptor instead.
func (*MonitorAndUserIds) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{6}
}

func (x *MonitorAndUserIds) GetMonitor() string {
	if x != nil {
		return x.Monitor
	}
	return ""
}

func (x *MonitorAndUserIds) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

// DaylightSavingsSettings - DST Start and End setting
type DaylightSavingsSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The number of the week in the month DST starts (1-4)
	StartWeekOfMonth uint32 `protobuf:"varint,1,opt,name=start_week_of_month,json=startWeekOfMonth,proto3" json:"start_week_of_month,omitempty"`
	// The day of the week DST starts
	StartDayOfWeek EDaysOfTheWeek `protobuf:"varint,2,opt,name=start_day_of_week,json=startDayOfWeek,proto3,enum=basic.EDaysOfTheWeek" json:"start_day_of_week,omitempty"`
	// The month in which DST starts (1-12)
	StartMonthOfYear uint32 `protobuf:"varint,3,opt,name=start_month_of_year,json=startMonthOfYear,proto3" json:"start_month_of_year,omitempty"`
	// The number of the week in the month DST ends (1-4)
	EndWeekOfMonth uint32 `protobuf:"varint,4,opt,name=end_week_of_month,json=endWeekOfMonth,proto3" json:"end_week_of_month,omitempty"`
	// The day of the week DST ends
	EndDayOfWeek EDaysOfTheWeek `protobuf:"varint,5,opt,name=end_day_of_week,json=endDayOfWeek,proto3,enum=basic.EDaysOfTheWeek" json:"end_day_of_week,omitempty"`
	// The month in which DST ends (1-12)
	EndMonthOfYear uint32 `protobuf:"varint,6,opt,name=end_month_of_year,json=endMonthOfYear,proto3" json:"end_month_of_year,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DaylightSavingsSettings) Reset() {
	*x = DaylightSavingsSettings{}
	mi := &file_basic_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DaylightSavingsSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaylightSavingsSettings) ProtoMessage() {}

func (x *DaylightSavingsSettings) ProtoReflect() protoreflect.Message {
	mi := &file_basic_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaylightSavingsSettings.ProtoReflect.Descriptor instead.
func (*DaylightSavingsSettings) Descriptor() ([]byte, []int) {
	return file_basic_proto_rawDescGZIP(), []int{7}
}

func (x *DaylightSavingsSettings) GetStartWeekOfMonth() uint32 {
	if x != nil {
		return x.StartWeekOfMonth
	}
	return 0
}

func (x *DaylightSavingsSettings) GetStartDayOfWeek() EDaysOfTheWeek {
	if x != nil {
		return x.StartDayOfWeek
	}
	return EDaysOfTheWeek_DAY_UNSPECIFIED
}

func (x *DaylightSavingsSettings) GetStartMonthOfYear() uint32 {
	if x != nil {
		return x.StartMonthOfYear
	}
	return 0
}

func (x *DaylightSavingsSettings) GetEndWeekOfMonth() uint32 {
	if x != nil {
		return x.EndWeekOfMonth
	}
	return 0
}

func (x *DaylightSavingsSettings) GetEndDayOfWeek() EDaysOfTheWeek {
	if x != nil {
		return x.EndDayOfWeek
	}
	return EDaysOfTheWeek_DAY_UNSPECIFIED
}

func (x *DaylightSavingsSettings) GetEndMonthOfYear() uint32 {
	if x != nil {
		return x.EndMonthOfYear
	}
	return 0
}

var File_basic_proto protoreflect.FileDescriptor

const file_basic_proto_rawDesc = "" +
	"\n" +
	"\vbasic.proto\x12\x05basic\"\x8f\x01\n" +
	"\rLocalDateTime\x12\x12\n" +
	"\x04year\x18\x01 \x01(\rR\x04year\x12\x14\n" +
	"\x05month\x18\x02 \x01(\rR\x05month\x12\x10\n" +
	"\x03day\x18\x03 \x01(\rR\x03day\x12\x12\n" +
	"\x04hour\x18\x04 \x01(\rR\x04hour\x12\x16\n" +
	"\x06minute\x18\x05 \x01(\rR\x06minute\x12\x16\n" +
	"\x06second\x18\x06 \x01(\rR\x06second\"Y\n" +
	"\x0fVersionStrThree\x12\x14\n" +
	"\x05major\x18\x01 \x01(\tR\x05major\x12\x14\n" +
	"\x05minor\x18\x02 \x01(\tR\x05minor\x12\x1a\n" +
	"\brevision\x18\x03 \x01(\tR\brevision\"E\n" +
	"\aDateStr\x12\x14\n" +
	"\x05month\x18\x01 \x01(\tR\x05month\x12\x10\n" +
	"\x03day\x18\x02 \x01(\tR\x03day\x12\x12\n" +
	"\x04year\x18\x03 \x01(\tR\x04year\"\x7f\n" +
	"\vIpAddressV4\x12\x1e\n" +
	"\voctet_1_mso\x18\x01 \x01(\rR\toctet1Mso\x12\x17\n" +
	"\aoctet_2\x18\x02 \x01(\rR\x06octet2\x12\x17\n" +
	"\aoctet_3\x18\x03 \x01(\rR\x06octet3\x12\x1e\n" +
	"\voctet_4_lso\x18\x04 \x01(\rR\toctet4Lso\"^\n" +
	"\x0eNowMinMaxFloat\x12\x18\n" +
	"\apresent\x18\x01 \x01(\x02R\apresent\x12\x18\n" +
	"\aminimum\x18\x02 \x01(\x02R\aminimum\x12\x18\n" +
	"\amaximum\x18\x03 \x01(\x02R\amaximum\"D\n" +
	"\x14ModelAndSerialNumber\x12\x16\n" +
	"\x06serial\x18\x01 \x01(\tR\x06serial\x12\x14\n" +
	"\x05model\x18\x02 \x01(\tR\x05model\"A\n" +
	"\x11MonitorAndUserIds\x12\x18\n" +
	"\amonitor\x18\x01 \x01(\tR\amonitor\x12\x12\n" +
	"\x04user\x18\x02 \x01(\tR\x04user\"\xcd\x02\n" +
	"\x17DaylightSavingsSettings\x12-\n" +
	"\x13start_week_of_month\x18\x01 \x01(\rR\x10startWeekOfMonth\x12@\n" +
	"\x11start_day_of_week\x18\x02 \x01(\x0e2\x15.basic.EDaysOfTheWeekR\x0estartDayOfWeek\x12-\n" +
	"\x13start_month_of_year\x18\x03 \x01(\rR\x10startMonthOfYear\x12)\n" +
	"\x11end_week_of_month\x18\x04 \x01(\rR\x0eendWeekOfMonth\x12<\n" +
	"\x0fend_day_of_week\x18\x05 \x01(\x0e2\x15.basic.EDaysOfTheWeekR\fendDayOfWeek\x12)\n" +
	"\x11end_month_of_year\x18\x06 \x01(\rR\x0eendMonthOfYear*\x9d\x01\n" +
	"\x0eEDaysOfTheWeek\x12\x13\n" +
	"\x0fDAY_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"DAY_SUNDAY\x10\x01\x12\x0e\n" +
	"\n" +
	"DAY_MONDAY\x10\x02\x12\x0f\n" +
	"\vDAY_TUESDAY\x10\x03\x12\x11\n" +
	"\rDAY_WEDNESDAY\x10\x04\x12\x10\n" +
	"\fDAY_THURSDAY\x10\x05\x12\x0e\n" +
	"\n" +
	"DAY_FRIDAY\x10\x06\x12\x10\n" +
	"\fDAY_SATURDAY\x10\ab\x06proto3"

var (
	file_basic_proto_rawDescOnce sync.Once
	file_basic_proto_rawDescData []byte
)

func file_basic_proto_rawDescGZIP() []byte {
	file_basic_proto_rawDescOnce.Do(func() {
		file_basic_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_basic_proto_rawDesc), len(file_basic_proto_rawDesc)))
	})
	return file_basic_proto_rawDescData
}

var file_basic_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_basic_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_basic_proto_goTypes = []any{
	(EDaysOfTheWeek)(0),             // 0: basic.EDaysOfTheWeek
	(*LocalDateTime)(nil),           // 1: basic.LocalDateTime
	(*VersionStrThree)(nil),         // 2: basic.VersionStrThree
	(*DateStr)(nil),                 // 3: basic.DateStr
	(*IpAddressV4)(nil),             // 4: basic.IpAddressV4
	(*NowMinMaxFloat)(nil),          // 5: basic.NowMinMaxFloat
	(*ModelAndSerialNumber)(nil),    // 6: basic.ModelAndSerialNumber
	(*MonitorAndUserIds)(nil),       // 7: basic.MonitorAndUserIds
	(*DaylightSavingsSettings)(nil), // 8: basic.DaylightSavingsSettings
}
var file_basic_proto_depIdxs = []int32{
	0, // 0: basic.DaylightSavingsSettings.start_day_of_week:type_name -> basic.EDaysOfTheWeek
	0, // 1: basic.DaylightSavingsSettings.end_day_of_week:type_name -> basic.EDaysOfTheWeek
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_basic_proto_init() }
func file_basic_proto_init() {
	if File_basic_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_basic_proto_rawDesc), len(file_basic_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_basic_proto_goTypes,
		DependencyIndexes: file_basic_proto_depIdxs,
		EnumInfos:         file_basic_proto_enumTypes,
		MessageInfos:      file_basic_proto_msgTypes,
	}.Build()
	File_basic_proto = out.File
	file_basic_proto_goTypes = nil
	file_basic_proto_depIdxs = nil
}
