//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  MON_LOGS
//Messages for monitor log entries.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: mon_logs.proto

package mon_logs

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	mon_faults "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_faults"
	settings "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//	ENUM EPowerLogEventType defines event types for PowerLogEntry.
//
// Some values only apply to certain monitors.
type EPowerLogEventType int32

const (
	EPowerLogEventType_PWR_LOG_EVENT_UNSPECIFIED             EPowerLogEventType = 0
	EPowerLogEventType_PWR_LOG_EVENT_POWER_UP                EPowerLogEventType = 1
	EPowerLogEventType_PWR_LOG_EVENT_POWER_DOWN              EPowerLogEventType = 2
	EPowerLogEventType_PWR_LOG_EVENT_CONTROLLER_UP           EPowerLogEventType = 3 // CMU only
	EPowerLogEventType_PWR_LOG_EVENT_CONTROLLER_DOWN         EPowerLogEventType = 4 // CMU only
	EPowerLogEventType_PWR_LOG_EVENT_LOW_VOLTAGE             EPowerLogEventType = 5
	EPowerLogEventType_PWR_LOG_EVENT_HIGH_VOLTAGE            EPowerLogEventType = 6
	EPowerLogEventType_PWR_LOG_EVENT_POWER_INTERRUPTED       EPowerLogEventType = 7
	EPowerLogEventType_PWR_LOG_EVENT_POWER_DOWN_TIMEOUT      EPowerLogEventType = 8  // CMU only
	EPowerLogEventType_PWR_LOG_EVENT_NRESET_TIMEOUT          EPowerLogEventType = 9  // CMU only
	EPowerLogEventType_PWR_LOG_EVENT_NRESET_RECOVERY         EPowerLogEventType = 10 // CMU only
	EPowerLogEventType_PWR_LOG_EVENT_HDSP_TIMEOUT            EPowerLogEventType = 11 // CMU only
	EPowerLogEventType_PWR_LOG_EVENT_FREQUENCY_LOW           EPowerLogEventType = 12
	EPowerLogEventType_PWR_LOG_EVENT_FREQUENCY_HIGH          EPowerLogEventType = 13
	EPowerLogEventType_PWR_LOG_EVENT_TIMED                   EPowerLogEventType = 14 // A Timed Event (scheduled)
	EPowerLogEventType_PWR_LOG_EVENT_LOW_VOLTAGE_RECOVERY    EPowerLogEventType = 15
	EPowerLogEventType_PWR_LOG_EVENT_HIGH_VOLTAGE_RECOVERY   EPowerLogEventType = 16
	EPowerLogEventType_PWR_LOG_EVENT_FREQUENCY_LOW_RECOVERY  EPowerLogEventType = 17
	EPowerLogEventType_PWR_LOG_EVENT_FREQUENCY_HIGH_RECOVERY EPowerLogEventType = 18
	EPowerLogEventType_PWR_LOG_EVENT_TEST                    EPowerLogEventType = 19 // A developer test event
)

// Enum value maps for EPowerLogEventType.
var (
	EPowerLogEventType_name = map[int32]string{
		0:  "PWR_LOG_EVENT_UNSPECIFIED",
		1:  "PWR_LOG_EVENT_POWER_UP",
		2:  "PWR_LOG_EVENT_POWER_DOWN",
		3:  "PWR_LOG_EVENT_CONTROLLER_UP",
		4:  "PWR_LOG_EVENT_CONTROLLER_DOWN",
		5:  "PWR_LOG_EVENT_LOW_VOLTAGE",
		6:  "PWR_LOG_EVENT_HIGH_VOLTAGE",
		7:  "PWR_LOG_EVENT_POWER_INTERRUPTED",
		8:  "PWR_LOG_EVENT_POWER_DOWN_TIMEOUT",
		9:  "PWR_LOG_EVENT_NRESET_TIMEOUT",
		10: "PWR_LOG_EVENT_NRESET_RECOVERY",
		11: "PWR_LOG_EVENT_HDSP_TIMEOUT",
		12: "PWR_LOG_EVENT_FREQUENCY_LOW",
		13: "PWR_LOG_EVENT_FREQUENCY_HIGH",
		14: "PWR_LOG_EVENT_TIMED",
		15: "PWR_LOG_EVENT_LOW_VOLTAGE_RECOVERY",
		16: "PWR_LOG_EVENT_HIGH_VOLTAGE_RECOVERY",
		17: "PWR_LOG_EVENT_FREQUENCY_LOW_RECOVERY",
		18: "PWR_LOG_EVENT_FREQUENCY_HIGH_RECOVERY",
		19: "PWR_LOG_EVENT_TEST",
	}
	EPowerLogEventType_value = map[string]int32{
		"PWR_LOG_EVENT_UNSPECIFIED":             0,
		"PWR_LOG_EVENT_POWER_UP":                1,
		"PWR_LOG_EVENT_POWER_DOWN":              2,
		"PWR_LOG_EVENT_CONTROLLER_UP":           3,
		"PWR_LOG_EVENT_CONTROLLER_DOWN":         4,
		"PWR_LOG_EVENT_LOW_VOLTAGE":             5,
		"PWR_LOG_EVENT_HIGH_VOLTAGE":            6,
		"PWR_LOG_EVENT_POWER_INTERRUPTED":       7,
		"PWR_LOG_EVENT_POWER_DOWN_TIMEOUT":      8,
		"PWR_LOG_EVENT_NRESET_TIMEOUT":          9,
		"PWR_LOG_EVENT_NRESET_RECOVERY":         10,
		"PWR_LOG_EVENT_HDSP_TIMEOUT":            11,
		"PWR_LOG_EVENT_FREQUENCY_LOW":           12,
		"PWR_LOG_EVENT_FREQUENCY_HIGH":          13,
		"PWR_LOG_EVENT_TIMED":                   14,
		"PWR_LOG_EVENT_LOW_VOLTAGE_RECOVERY":    15,
		"PWR_LOG_EVENT_HIGH_VOLTAGE_RECOVERY":   16,
		"PWR_LOG_EVENT_FREQUENCY_LOW_RECOVERY":  17,
		"PWR_LOG_EVENT_FREQUENCY_HIGH_RECOVERY": 18,
		"PWR_LOG_EVENT_TEST":                    19,
	}
)

func (x EPowerLogEventType) Enum() *EPowerLogEventType {
	p := new(EPowerLogEventType)
	*p = x
	return p
}

func (x EPowerLogEventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EPowerLogEventType) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[0].Descriptor()
}

func (EPowerLogEventType) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[0]
}

func (x EPowerLogEventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EPowerLogEventType.Descriptor instead.
func (EPowerLogEventType) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{0}
}

// ENUM EPowerLogEventTiming defines log period for PowerLogEntry.
type EPowerLogEventTiming int32

const (
	EPowerLogEventTiming_PWR_LOG_PERIOD_UNSPECIFIED    EPowerLogEventTiming = 0
	EPowerLogEventTiming_PWR_LOG_PERIOD_NO_TIMED_EVENT EPowerLogEventTiming = 1
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_1_HR     EPowerLogEventTiming = 2
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_2_HRS    EPowerLogEventTiming = 3
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_4_HRS    EPowerLogEventTiming = 4
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_8_HRS    EPowerLogEventTiming = 5
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_12_HRS   EPowerLogEventTiming = 6
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_1_DAY    EPowerLogEventTiming = 7
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_2_DAYS   EPowerLogEventTiming = 8
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_7_DAYS   EPowerLogEventTiming = 9
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_14_DAYS  EPowerLogEventTiming = 10
	EPowerLogEventTiming_PWR_LOG_PERIOD_EVERY_1_MONTH  EPowerLogEventTiming = 11
)

// Enum value maps for EPowerLogEventTiming.
var (
	EPowerLogEventTiming_name = map[int32]string{
		0:  "PWR_LOG_PERIOD_UNSPECIFIED",
		1:  "PWR_LOG_PERIOD_NO_TIMED_EVENT",
		2:  "PWR_LOG_PERIOD_EVERY_1_HR",
		3:  "PWR_LOG_PERIOD_EVERY_2_HRS",
		4:  "PWR_LOG_PERIOD_EVERY_4_HRS",
		5:  "PWR_LOG_PERIOD_EVERY_8_HRS",
		6:  "PWR_LOG_PERIOD_EVERY_12_HRS",
		7:  "PWR_LOG_PERIOD_EVERY_1_DAY",
		8:  "PWR_LOG_PERIOD_EVERY_2_DAYS",
		9:  "PWR_LOG_PERIOD_EVERY_7_DAYS",
		10: "PWR_LOG_PERIOD_EVERY_14_DAYS",
		11: "PWR_LOG_PERIOD_EVERY_1_MONTH",
	}
	EPowerLogEventTiming_value = map[string]int32{
		"PWR_LOG_PERIOD_UNSPECIFIED":    0,
		"PWR_LOG_PERIOD_NO_TIMED_EVENT": 1,
		"PWR_LOG_PERIOD_EVERY_1_HR":     2,
		"PWR_LOG_PERIOD_EVERY_2_HRS":    3,
		"PWR_LOG_PERIOD_EVERY_4_HRS":    4,
		"PWR_LOG_PERIOD_EVERY_8_HRS":    5,
		"PWR_LOG_PERIOD_EVERY_12_HRS":   6,
		"PWR_LOG_PERIOD_EVERY_1_DAY":    7,
		"PWR_LOG_PERIOD_EVERY_2_DAYS":   8,
		"PWR_LOG_PERIOD_EVERY_7_DAYS":   9,
		"PWR_LOG_PERIOD_EVERY_14_DAYS":  10,
		"PWR_LOG_PERIOD_EVERY_1_MONTH":  11,
	}
)

func (x EPowerLogEventTiming) Enum() *EPowerLogEventTiming {
	p := new(EPowerLogEventTiming)
	*p = x
	return p
}

func (x EPowerLogEventTiming) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EPowerLogEventTiming) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[1].Descriptor()
}

func (EPowerLogEventTiming) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[1]
}

func (x EPowerLogEventTiming) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EPowerLogEventTiming.Descriptor instead.
func (EPowerLogEventTiming) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{1}
}

//	ENUM EResetLogSource defines sources of a reset for ResetLogEntry.
//
// Some values only apply to certain monitors.
type EResetLogSource int32

const (
	EResetLogSource_RST_LOG_SOURCE_UNSPECIFIED    EResetLogSource = 0
	EResetLogSource_RST_LOG_SOURCE_FRONT_PANEL    EResetLogSource = 1
	EResetLogSource_RST_LOG_SOURCE_EXTERNAL       EResetLogSource = 2
	EResetLogSource_RST_LOG_SOURCE_NON_LATCHED    EResetLogSource = 3
	EResetLogSource_RST_LOG_SOURCE_POWER_CYCLE    EResetLogSource = 4
	EResetLogSource_RST_LOG_SOURCE_ADU            EResetLogSource = 5
	EResetLogSource_RST_LOG_SOURCE_CONFIG_CHANGED EResetLogSource = 6 // Data Key
	EResetLogSource_RST_LOG_SOURCE_REMOTE         EResetLogSource = 7 // Remote UI
	EResetLogSource_RST_LOG_SOURCE_TEST           EResetLogSource = 8 // Test reset through debug command
)

// Enum value maps for EResetLogSource.
var (
	EResetLogSource_name = map[int32]string{
		0: "RST_LOG_SOURCE_UNSPECIFIED",
		1: "RST_LOG_SOURCE_FRONT_PANEL",
		2: "RST_LOG_SOURCE_EXTERNAL",
		3: "RST_LOG_SOURCE_NON_LATCHED",
		4: "RST_LOG_SOURCE_POWER_CYCLE",
		5: "RST_LOG_SOURCE_ADU",
		6: "RST_LOG_SOURCE_CONFIG_CHANGED",
		7: "RST_LOG_SOURCE_REMOTE",
		8: "RST_LOG_SOURCE_TEST",
	}
	EResetLogSource_value = map[string]int32{
		"RST_LOG_SOURCE_UNSPECIFIED":    0,
		"RST_LOG_SOURCE_FRONT_PANEL":    1,
		"RST_LOG_SOURCE_EXTERNAL":       2,
		"RST_LOG_SOURCE_NON_LATCHED":    3,
		"RST_LOG_SOURCE_POWER_CYCLE":    4,
		"RST_LOG_SOURCE_ADU":            5,
		"RST_LOG_SOURCE_CONFIG_CHANGED": 6,
		"RST_LOG_SOURCE_REMOTE":         7,
		"RST_LOG_SOURCE_TEST":           8,
	}
)

func (x EResetLogSource) Enum() *EResetLogSource {
	p := new(EResetLogSource)
	*p = x
	return p
}

func (x EResetLogSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EResetLogSource) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[2].Descriptor()
}

func (EResetLogSource) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[2]
}

func (x EResetLogSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EResetLogSource.Descriptor instead.
func (EResetLogSource) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{2}
}

//	ENUM EClockLogSource defines sources of a clock change for ClockLogEntry.
//
// Some values only apply to certain monitors.
type EClockLogSource int32

const (
	EClockLogSource_CLOCK_LOG_SOURCE_UNSPECIFIED EClockLogSource = 0
	EClockLogSource_CLOCK_LOG_SOURCE_CONTROLLER  EClockLogSource = 1
	EClockLogSource_CLOCK_LOG_SOURCE_ETHERNET    EClockLogSource = 2
	EClockLogSource_CLOCK_LOG_SOURCE_BLUETOOTH   EClockLogSource = 3
	EClockLogSource_CLOCK_LOG_SOURCE_FRONTPANEL  EClockLogSource = 4
)

// Enum value maps for EClockLogSource.
var (
	EClockLogSource_name = map[int32]string{
		0: "CLOCK_LOG_SOURCE_UNSPECIFIED",
		1: "CLOCK_LOG_SOURCE_CONTROLLER",
		2: "CLOCK_LOG_SOURCE_ETHERNET",
		3: "CLOCK_LOG_SOURCE_BLUETOOTH",
		4: "CLOCK_LOG_SOURCE_FRONTPANEL",
	}
	EClockLogSource_value = map[string]int32{
		"CLOCK_LOG_SOURCE_UNSPECIFIED": 0,
		"CLOCK_LOG_SOURCE_CONTROLLER":  1,
		"CLOCK_LOG_SOURCE_ETHERNET":    2,
		"CLOCK_LOG_SOURCE_BLUETOOTH":   3,
		"CLOCK_LOG_SOURCE_FRONTPANEL":  4,
	}
)

func (x EClockLogSource) Enum() *EClockLogSource {
	p := new(EClockLogSource)
	*p = x
	return p
}

func (x EClockLogSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EClockLogSource) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[3].Descriptor()
}

func (EClockLogSource) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[3]
}

func (x EClockLogSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EClockLogSource.Descriptor instead.
func (EClockLogSource) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{3}
}

// ENUM ECaptureChannelInput defines the type of capture channel input sourcefor the FaultFactsLogEntry.
type ECaptureChannelInput int32

const (
	ECaptureChannelInput_CH_INPUT_UNSPECIFIED               ECaptureChannelInput = 0
	ECaptureChannelInput_CH_INPUT_RED_VOLTAGE               ECaptureChannelInput = 1
	ECaptureChannelInput_CH_INPUT_YELLOW_VOLTAGE            ECaptureChannelInput = 2
	ECaptureChannelInput_CH_INPUT_GREEN_VOLTAGE             ECaptureChannelInput = 3
	ECaptureChannelInput_CH_INPUT_WALK_VOLTAGE              ECaptureChannelInput = 4
	ECaptureChannelInput_CH_INPUT_RED_CURRENT               ECaptureChannelInput = 5
	ECaptureChannelInput_CH_INPUT_YELLOW_CURRENT            ECaptureChannelInput = 6
	ECaptureChannelInput_CH_INPUT_GREEN_CURRENT             ECaptureChannelInput = 7
	ECaptureChannelInput_CH_INPUT_WALK_CURRENT              ECaptureChannelInput = 8
	ECaptureChannelInput_CH_INPUT_AC_LINE_VOLTS             ECaptureChannelInput = 9
	ECaptureChannelInput_CH_INPUT_24V_1_VOLTS               ECaptureChannelInput = 10
	ECaptureChannelInput_CH_INPUT_24V_2_VOLTS               ECaptureChannelInput = 11
	ECaptureChannelInput_CH_INPUT_CVM_VOLTS                 ECaptureChannelInput = 12
	ECaptureChannelInput_CH_INPUT_LINE_FREQUENCY            ECaptureChannelInput = 13
	ECaptureChannelInput_CH_INPUT_RED_ENABLE_VOLTS          ECaptureChannelInput = 14
	ECaptureChannelInput_CH_INPUT_EXTERNAL_WATCHDOG_VOLTS   ECaptureChannelInput = 15
	ECaptureChannelInput_CH_INPUT_TYPE_SELECT_VOLTS         ECaptureChannelInput = 16
	ECaptureChannelInput_CH_INPUT_SDLC_DISABLE_VOLTS        ECaptureChannelInput = 17
	ECaptureChannelInput_CH_INPUT_24V_MONITOR_INHIBIT_VOLTS ECaptureChannelInput = 18
	ECaptureChannelInput_CH_INPUT_LOCAL_FLASH_VOLTS         ECaptureChannelInput = 19
	ECaptureChannelInput_CH_INPUT_TEST                      ECaptureChannelInput = 254 // test input source type
)

// Enum value maps for ECaptureChannelInput.
var (
	ECaptureChannelInput_name = map[int32]string{
		0:   "CH_INPUT_UNSPECIFIED",
		1:   "CH_INPUT_RED_VOLTAGE",
		2:   "CH_INPUT_YELLOW_VOLTAGE",
		3:   "CH_INPUT_GREEN_VOLTAGE",
		4:   "CH_INPUT_WALK_VOLTAGE",
		5:   "CH_INPUT_RED_CURRENT",
		6:   "CH_INPUT_YELLOW_CURRENT",
		7:   "CH_INPUT_GREEN_CURRENT",
		8:   "CH_INPUT_WALK_CURRENT",
		9:   "CH_INPUT_AC_LINE_VOLTS",
		10:  "CH_INPUT_24V_1_VOLTS",
		11:  "CH_INPUT_24V_2_VOLTS",
		12:  "CH_INPUT_CVM_VOLTS",
		13:  "CH_INPUT_LINE_FREQUENCY",
		14:  "CH_INPUT_RED_ENABLE_VOLTS",
		15:  "CH_INPUT_EXTERNAL_WATCHDOG_VOLTS",
		16:  "CH_INPUT_TYPE_SELECT_VOLTS",
		17:  "CH_INPUT_SDLC_DISABLE_VOLTS",
		18:  "CH_INPUT_24V_MONITOR_INHIBIT_VOLTS",
		19:  "CH_INPUT_LOCAL_FLASH_VOLTS",
		254: "CH_INPUT_TEST",
	}
	ECaptureChannelInput_value = map[string]int32{
		"CH_INPUT_UNSPECIFIED":               0,
		"CH_INPUT_RED_VOLTAGE":               1,
		"CH_INPUT_YELLOW_VOLTAGE":            2,
		"CH_INPUT_GREEN_VOLTAGE":             3,
		"CH_INPUT_WALK_VOLTAGE":              4,
		"CH_INPUT_RED_CURRENT":               5,
		"CH_INPUT_YELLOW_CURRENT":            6,
		"CH_INPUT_GREEN_CURRENT":             7,
		"CH_INPUT_WALK_CURRENT":              8,
		"CH_INPUT_AC_LINE_VOLTS":             9,
		"CH_INPUT_24V_1_VOLTS":               10,
		"CH_INPUT_24V_2_VOLTS":               11,
		"CH_INPUT_CVM_VOLTS":                 12,
		"CH_INPUT_LINE_FREQUENCY":            13,
		"CH_INPUT_RED_ENABLE_VOLTS":          14,
		"CH_INPUT_EXTERNAL_WATCHDOG_VOLTS":   15,
		"CH_INPUT_TYPE_SELECT_VOLTS":         16,
		"CH_INPUT_SDLC_DISABLE_VOLTS":        17,
		"CH_INPUT_24V_MONITOR_INHIBIT_VOLTS": 18,
		"CH_INPUT_LOCAL_FLASH_VOLTS":         19,
		"CH_INPUT_TEST":                      254,
	}
)

func (x ECaptureChannelInput) Enum() *ECaptureChannelInput {
	p := new(ECaptureChannelInput)
	*p = x
	return p
}

func (x ECaptureChannelInput) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ECaptureChannelInput) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[4].Descriptor()
}

func (ECaptureChannelInput) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[4]
}

func (x ECaptureChannelInput) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ECaptureChannelInput.Descriptor instead.
func (ECaptureChannelInput) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{4}
}

// ENUM EAlarmSeverity defines alarm severities for the AlarmLogEntry.
// Should be kept in sync with the log_alarm_severity_t enums in the Serial Comms Library.
type EAlarmSeverity int32

const (
	EAlarmSeverity_ALARM_SEVERITY_CRITICAL EAlarmSeverity = 0
	EAlarmSeverity_ALARM_SEVERITY_MAJOR    EAlarmSeverity = 1
	EAlarmSeverity_ALARM_SEVERITY_MINOR    EAlarmSeverity = 2
	EAlarmSeverity_ALARM_SEVERITY_INFORM   EAlarmSeverity = 3
	EAlarmSeverity_ALARM_SEVERITY_TEST     EAlarmSeverity = 15
)

// Enum value maps for EAlarmSeverity.
var (
	EAlarmSeverity_name = map[int32]string{
		0:  "ALARM_SEVERITY_CRITICAL",
		1:  "ALARM_SEVERITY_MAJOR",
		2:  "ALARM_SEVERITY_MINOR",
		3:  "ALARM_SEVERITY_INFORM",
		15: "ALARM_SEVERITY_TEST",
	}
	EAlarmSeverity_value = map[string]int32{
		"ALARM_SEVERITY_CRITICAL": 0,
		"ALARM_SEVERITY_MAJOR":    1,
		"ALARM_SEVERITY_MINOR":    2,
		"ALARM_SEVERITY_INFORM":   3,
		"ALARM_SEVERITY_TEST":     15,
	}
)

func (x EAlarmSeverity) Enum() *EAlarmSeverity {
	p := new(EAlarmSeverity)
	*p = x
	return p
}

func (x EAlarmSeverity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAlarmSeverity) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[5].Descriptor()
}

func (EAlarmSeverity) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[5]
}

func (x EAlarmSeverity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EAlarmSeverity.Descriptor instead.
func (EAlarmSeverity) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{5}
}

// ENUM EAlarmSource defines alarm sources for the AlarmLogEntry.
// Should be kept in sync with the log_alarm_source_t enums in the Serial Comms Library.
type EAlarmSource int32

const (
	EAlarmSource_ALARM_SOURCE_MONITOR_OPERATION      EAlarmSource = 0
	EAlarmSource_ALARM_SOURCE_MONITOR_HARDWARE       EAlarmSource = 1
	EAlarmSource_ALARM_SOURCE_OTHER_CABINET_HARDWARE EAlarmSource = 2
	EAlarmSource_ALARM_SOURCE_EXTERNAL_SOURCE        EAlarmSource = 3
	EAlarmSource_ALARM_SOURCE_INTERNAL_COMMUNICATION EAlarmSource = 4
	EAlarmSource_ALARM_SOURCE_EXTERNAL_COMMUNICATION EAlarmSource = 5
	EAlarmSource_ALARM_SOURCE_TEST                   EAlarmSource = 14
	EAlarmSource_ALARM_SOURCE_UNSPECIFIED            EAlarmSource = 15
)

// Enum value maps for EAlarmSource.
var (
	EAlarmSource_name = map[int32]string{
		0:  "ALARM_SOURCE_MONITOR_OPERATION",
		1:  "ALARM_SOURCE_MONITOR_HARDWARE",
		2:  "ALARM_SOURCE_OTHER_CABINET_HARDWARE",
		3:  "ALARM_SOURCE_EXTERNAL_SOURCE",
		4:  "ALARM_SOURCE_INTERNAL_COMMUNICATION",
		5:  "ALARM_SOURCE_EXTERNAL_COMMUNICATION",
		14: "ALARM_SOURCE_TEST",
		15: "ALARM_SOURCE_UNSPECIFIED",
	}
	EAlarmSource_value = map[string]int32{
		"ALARM_SOURCE_MONITOR_OPERATION":      0,
		"ALARM_SOURCE_MONITOR_HARDWARE":       1,
		"ALARM_SOURCE_OTHER_CABINET_HARDWARE": 2,
		"ALARM_SOURCE_EXTERNAL_SOURCE":        3,
		"ALARM_SOURCE_INTERNAL_COMMUNICATION": 4,
		"ALARM_SOURCE_EXTERNAL_COMMUNICATION": 5,
		"ALARM_SOURCE_TEST":                   14,
		"ALARM_SOURCE_UNSPECIFIED":            15,
	}
)

func (x EAlarmSource) Enum() *EAlarmSource {
	p := new(EAlarmSource)
	*p = x
	return p
}

func (x EAlarmSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EAlarmSource) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[6].Descriptor()
}

func (EAlarmSource) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[6]
}

func (x EAlarmSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EAlarmSource.Descriptor instead.
func (EAlarmSource) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{6}
}

// ENUM EMonitorLogType is used to specify a log for requests and results.
type EMonitorLogType int32

const (
	EMonitorLogType_MON_LOG_UNSPECIFIED       EMonitorLogType = 0
	EMonitorLogType_MON_LOG_ALL               EMonitorLogType = 1 // For requesting / specifying all logs
	EMonitorLogType_MON_LOG_POWER             EMonitorLogType = 2
	EMonitorLogType_MON_LOG_RESET             EMonitorLogType = 3
	EMonitorLogType_MON_LOG_CLOCK             EMonitorLogType = 4
	EMonitorLogType_MON_LOG_CONFIGURATION     EMonitorLogType = 5
	EMonitorLogType_MON_LOG_PORT1             EMonitorLogType = 6
	EMonitorLogType_MON_LOG_FAULT_HEADER      EMonitorLogType = 7
	EMonitorLogType_MON_LOG_FAULT_MEASUREMENT EMonitorLogType = 8
	EMonitorLogType_MON_LOG_FAULT_SEQUENCE    EMonitorLogType = 9
	EMonitorLogType_MON_LOG_FAULT_FACTS       EMonitorLogType = 10
	EMonitorLogType_MON_LOG_ALARM             EMonitorLogType = 11
)

// Enum value maps for EMonitorLogType.
var (
	EMonitorLogType_name = map[int32]string{
		0:  "MON_LOG_UNSPECIFIED",
		1:  "MON_LOG_ALL",
		2:  "MON_LOG_POWER",
		3:  "MON_LOG_RESET",
		4:  "MON_LOG_CLOCK",
		5:  "MON_LOG_CONFIGURATION",
		6:  "MON_LOG_PORT1",
		7:  "MON_LOG_FAULT_HEADER",
		8:  "MON_LOG_FAULT_MEASUREMENT",
		9:  "MON_LOG_FAULT_SEQUENCE",
		10: "MON_LOG_FAULT_FACTS",
		11: "MON_LOG_ALARM",
	}
	EMonitorLogType_value = map[string]int32{
		"MON_LOG_UNSPECIFIED":       0,
		"MON_LOG_ALL":               1,
		"MON_LOG_POWER":             2,
		"MON_LOG_RESET":             3,
		"MON_LOG_CLOCK":             4,
		"MON_LOG_CONFIGURATION":     5,
		"MON_LOG_PORT1":             6,
		"MON_LOG_FAULT_HEADER":      7,
		"MON_LOG_FAULT_MEASUREMENT": 8,
		"MON_LOG_FAULT_SEQUENCE":    9,
		"MON_LOG_FAULT_FACTS":       10,
		"MON_LOG_ALARM":             11,
	}
)

func (x EMonitorLogType) Enum() *EMonitorLogType {
	p := new(EMonitorLogType)
	*p = x
	return p
}

func (x EMonitorLogType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMonitorLogType) Descriptor() protoreflect.EnumDescriptor {
	return file_mon_logs_proto_enumTypes[7].Descriptor()
}

func (EMonitorLogType) Type() protoreflect.EnumType {
	return &file_mon_logs_proto_enumTypes[7]
}

func (x EMonitorLogType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EMonitorLogType.Descriptor instead.
func (EMonitorLogType) EnumDescriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{7}
}

//	MmuPowerMonitors are MMU specific voltage monitors that are part of the monitor Power Log.
//
// This is used in PowerLogEntryMmu
type MmuPowerMonitors struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AC Mains voltage when entry was made (present), followed by the minimum and maximum recorded voltages
	AcMainsVolts *basic.NowMinMaxFloat `protobuf:"bytes,1,opt,name=ac_mains_volts,json=acMainsVolts,proto3" json:"ac_mains_volts,omitempty"`
	// DC 24 V monitor 1 voltage when entry was made (present), followed by the minimum and maximum recorded voltages
	Dc_24VoltsMon1 *basic.NowMinMaxFloat `protobuf:"bytes,2,opt,name=dc_24_volts_mon1,json=dc24VoltsMon1,proto3" json:"dc_24_volts_mon1,omitempty"`
	// DC 24 V monitor 2 voltage when entry was made (present), followed by the minimum and maximum recorded voltages
	Dc_24VoltsMon2 *basic.NowMinMaxFloat `protobuf:"bytes,3,opt,name=dc_24_volts_mon2,json=dc24VoltsMon2,proto3" json:"dc_24_volts_mon2,omitempty"`
	// Controller Voltage Monitor voltage when entry was made (present), followed by the minimum and maximum recorded voltages
	CvmVolt *basic.NowMinMaxFloat `protobuf:"bytes,4,opt,name=cvm_volt,json=cvmVolt,proto3" json:"cvm_volt,omitempty"`
	// Line Frequency in Hertz when entry was made (present), followed by the minimum and maximum recorded frequencies
	LineFrequencyHz *basic.NowMinMaxFloat `protobuf:"bytes,5,opt,name=line_frequency_hz,json=lineFrequencyHz,proto3" json:"line_frequency_hz,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MmuPowerMonitors) Reset() {
	*x = MmuPowerMonitors{}
	mi := &file_mon_logs_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MmuPowerMonitors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MmuPowerMonitors) ProtoMessage() {}

func (x *MmuPowerMonitors) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MmuPowerMonitors.ProtoReflect.Descriptor instead.
func (*MmuPowerMonitors) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{0}
}

func (x *MmuPowerMonitors) GetAcMainsVolts() *basic.NowMinMaxFloat {
	if x != nil {
		return x.AcMainsVolts
	}
	return nil
}

func (x *MmuPowerMonitors) GetDc_24VoltsMon1() *basic.NowMinMaxFloat {
	if x != nil {
		return x.Dc_24VoltsMon1
	}
	return nil
}

func (x *MmuPowerMonitors) GetDc_24VoltsMon2() *basic.NowMinMaxFloat {
	if x != nil {
		return x.Dc_24VoltsMon2
	}
	return nil
}

func (x *MmuPowerMonitors) GetCvmVolt() *basic.NowMinMaxFloat {
	if x != nil {
		return x.CvmVolt
	}
	return nil
}

func (x *MmuPowerMonitors) GetLineFrequencyHz() *basic.NowMinMaxFloat {
	if x != nil {
		return x.LineFrequencyHz
	}
	return nil
}

//	PowerLogEntryMmu represents one entry of the monitor Power Log for Mmu.
//
// This comes from the Main processor response to the command 0x01 Retrieve Power Log
type PowerLogEntryMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique per log entry
	EntryId uint32 `protobuf:"varint,1,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// Enum with the log event
	EventType EPowerLogEventType `protobuf:"varint,2,opt,name=event_type,json=eventType,proto3,enum=mon_logs.EPowerLogEventType" json:"event_type,omitempty"`
	// Log entry timestamp
	EntryTimestamp *basic.LocalDateTime `protobuf:"bytes,3,opt,name=entry_timestamp,json=entryTimestamp,proto3" json:"entry_timestamp,omitempty"`
	// monitored voltages
	PowerVoltages *MmuPowerMonitors `protobuf:"bytes,4,opt,name=power_voltages,json=powerVoltages,proto3" json:"power_voltages,omitempty"`
	// Temperature in degrees F when entry was made (present), followed by the minimum and maximum recorded temperatures
	TemperatureDegf *basic.NowMinMaxFloat `protobuf:"bytes,5,opt,name=temperature_degf,json=temperatureDegf,proto3" json:"temperature_degf,omitempty"`
	// The period at which regular log entries are made
	EventTiming EPowerLogEventTiming `protobuf:"varint,8,opt,name=event_timing,json=eventTiming,proto3,enum=mon_logs.EPowerLogEventTiming" json:"event_timing,omitempty"`
	// For a PWR_LOG_EVENT_POWER_INTERRUPTED event, this is the length of the interrupt in milliseconds.
	PowerInterruptTimeMs *uint32 `protobuf:"varint,7,opt,name=power_interrupt_time_ms,json=powerInterruptTimeMs,proto3,oneof" json:"power_interrupt_time_ms,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PowerLogEntryMmu) Reset() {
	*x = PowerLogEntryMmu{}
	mi := &file_mon_logs_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PowerLogEntryMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerLogEntryMmu) ProtoMessage() {}

func (x *PowerLogEntryMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerLogEntryMmu.ProtoReflect.Descriptor instead.
func (*PowerLogEntryMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{1}
}

func (x *PowerLogEntryMmu) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *PowerLogEntryMmu) GetEventType() EPowerLogEventType {
	if x != nil {
		return x.EventType
	}
	return EPowerLogEventType_PWR_LOG_EVENT_UNSPECIFIED
}

func (x *PowerLogEntryMmu) GetEntryTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.EntryTimestamp
	}
	return nil
}

func (x *PowerLogEntryMmu) GetPowerVoltages() *MmuPowerMonitors {
	if x != nil {
		return x.PowerVoltages
	}
	return nil
}

func (x *PowerLogEntryMmu) GetTemperatureDegf() *basic.NowMinMaxFloat {
	if x != nil {
		return x.TemperatureDegf
	}
	return nil
}

func (x *PowerLogEntryMmu) GetEventTiming() EPowerLogEventTiming {
	if x != nil {
		return x.EventTiming
	}
	return EPowerLogEventTiming_PWR_LOG_PERIOD_UNSPECIFIED
}

func (x *PowerLogEntryMmu) GetPowerInterruptTimeMs() uint32 {
	if x != nil && x.PowerInterruptTimeMs != nil {
		return *x.PowerInterruptTimeMs
	}
	return 0
}

//	PowerLogMultipleEntriesMmu encapsulates repeated Power Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type PowerLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*PowerLogEntryMmu    `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PowerLogMultipleEntriesMmu) Reset() {
	*x = PowerLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PowerLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerLogMultipleEntriesMmu) ProtoMessage() {}

func (x *PowerLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*PowerLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{2}
}

func (x *PowerLogMultipleEntriesMmu) GetLogEntry() []*PowerLogEntryMmu {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	ResetLogEntryMmu represents one entry of the monitor Reset Log for Mmu.
//
// This comes from the Main processor response to the command 0x02 Retrieve Reset Log
type ResetLogEntryMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique per log entry
	EntryId uint32 `protobuf:"varint,1,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// Enum with the source of the reset
	ResetSource EResetLogSource `protobuf:"varint,2,opt,name=reset_source,json=resetSource,proto3,enum=mon_logs.EResetLogSource" json:"reset_source,omitempty"`
	// Log entry timestamp
	EntryTimestamp *basic.LocalDateTime `protobuf:"bytes,3,opt,name=entry_timestamp,json=entryTimestamp,proto3" json:"entry_timestamp,omitempty"`
	// Fault that was cleared by the reset
	// A diagnostic fault may also be cleared along with another fault
	FaultCleared      mon_faults.EFaultCode `protobuf:"varint,4,opt,name=fault_cleared,json=faultCleared,proto3,enum=mon_faults.EFaultCode" json:"fault_cleared,omitempty"`
	DiagnosticCleared bool                  `protobuf:"varint,5,opt,name=diagnostic_cleared,json=diagnosticCleared,proto3" json:"diagnostic_cleared,omitempty"`
	// The Fault Log entry for the fault that was cleared
	FaultId       uint32 `protobuf:"varint,6,opt,name=fault_id,json=faultId,proto3" json:"fault_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetLogEntryMmu) Reset() {
	*x = ResetLogEntryMmu{}
	mi := &file_mon_logs_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetLogEntryMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetLogEntryMmu) ProtoMessage() {}

func (x *ResetLogEntryMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetLogEntryMmu.ProtoReflect.Descriptor instead.
func (*ResetLogEntryMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{3}
}

func (x *ResetLogEntryMmu) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *ResetLogEntryMmu) GetResetSource() EResetLogSource {
	if x != nil {
		return x.ResetSource
	}
	return EResetLogSource_RST_LOG_SOURCE_UNSPECIFIED
}

func (x *ResetLogEntryMmu) GetEntryTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.EntryTimestamp
	}
	return nil
}

func (x *ResetLogEntryMmu) GetFaultCleared() mon_faults.EFaultCode {
	if x != nil {
		return x.FaultCleared
	}
	return mon_faults.EFaultCode(0)
}

func (x *ResetLogEntryMmu) GetDiagnosticCleared() bool {
	if x != nil {
		return x.DiagnosticCleared
	}
	return false
}

func (x *ResetLogEntryMmu) GetFaultId() uint32 {
	if x != nil {
		return x.FaultId
	}
	return 0
}

//	ResetLogMultipleEntriesMmu encapsulates repeated Reset Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type ResetLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*ResetLogEntryMmu    `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetLogMultipleEntriesMmu) Reset() {
	*x = ResetLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetLogMultipleEntriesMmu) ProtoMessage() {}

func (x *ResetLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*ResetLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{4}
}

func (x *ResetLogMultipleEntriesMmu) GetLogEntry() []*ResetLogEntryMmu {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	ClockLogEntry represents one entry of the Clock Log.
//
// This comes from the Main processor response to the command 0x04 Retrieve Clock Log
type ClockLogEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique per log entry
	EntryId uint32 `protobuf:"varint,1,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// Date/Time source
	ClockSource EClockLogSource `protobuf:"varint,2,opt,name=clock_source,json=clockSource,proto3,enum=mon_logs.EClockLogSource" json:"clock_source,omitempty"`
	// Previous Date/Time
	PreviousDatetime *basic.LocalDateTime `protobuf:"bytes,3,opt,name=previous_datetime,json=previousDatetime,proto3" json:"previous_datetime,omitempty"`
	// New Date/Time
	NewDatetime *basic.LocalDateTime `protobuf:"bytes,4,opt,name=new_datetime,json=newDatetime,proto3" json:"new_datetime,omitempty"`
	// (Uptime) Total run time for the monitor since being shipped, in seconds
	RunTime       uint32 `protobuf:"varint,5,opt,name=run_time,json=runTime,proto3" json:"run_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClockLogEntry) Reset() {
	*x = ClockLogEntry{}
	mi := &file_mon_logs_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClockLogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockLogEntry) ProtoMessage() {}

func (x *ClockLogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockLogEntry.ProtoReflect.Descriptor instead.
func (*ClockLogEntry) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{5}
}

func (x *ClockLogEntry) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *ClockLogEntry) GetClockSource() EClockLogSource {
	if x != nil {
		return x.ClockSource
	}
	return EClockLogSource_CLOCK_LOG_SOURCE_UNSPECIFIED
}

func (x *ClockLogEntry) GetPreviousDatetime() *basic.LocalDateTime {
	if x != nil {
		return x.PreviousDatetime
	}
	return nil
}

func (x *ClockLogEntry) GetNewDatetime() *basic.LocalDateTime {
	if x != nil {
		return x.NewDatetime
	}
	return nil
}

func (x *ClockLogEntry) GetRunTime() uint32 {
	if x != nil {
		return x.RunTime
	}
	return 0
}

//	ClockLogMultipleEntriesMmu encapsulates repeated Clock Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type ClockLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*ClockLogEntry       `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClockLogMultipleEntriesMmu) Reset() {
	*x = ClockLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClockLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClockLogMultipleEntriesMmu) ProtoMessage() {}

func (x *ClockLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClockLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*ClockLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{6}
}

func (x *ClockLogMultipleEntriesMmu) GetLogEntry() []*ClockLogEntry {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

// HardwareRevisionsMmu contains the hardware revision values for the MMU system.
type HardwareRevisionsMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Main processor hardware revision, value 0-255
	MainHardwareRevision uint32 `protobuf:"varint,1,opt,name=main_hardware_revision,json=mainHardwareRevision,proto3" json:"main_hardware_revision,omitempty"`
	// Isolated processor hardware revision, value 0-255
	IsoHardwareRevision uint32 `protobuf:"varint,2,opt,name=iso_hardware_revision,json=isoHardwareRevision,proto3" json:"iso_hardware_revision,omitempty"`
	// Display processor hardware revision, 0-15
	DisplayHardwareRevision uint32 `protobuf:"varint,3,opt,name=display_hardware_revision,json=displayHardwareRevision,proto3" json:"display_hardware_revision,omitempty"`
	// Display board RMS engine hardware revision, 0-15
	DisplayRmsHardwareRevision uint32 `protobuf:"varint,4,opt,name=display_rms_hardware_revision,json=displayRmsHardwareRevision,proto3" json:"display_rms_hardware_revision,omitempty"`
	// Communications processor hardware revision, 0-255
	CommsHardwareRevision uint32 `protobuf:"varint,5,opt,name=comms_hardware_revision,json=commsHardwareRevision,proto3" json:"comms_hardware_revision,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *HardwareRevisionsMmu) Reset() {
	*x = HardwareRevisionsMmu{}
	mi := &file_mon_logs_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HardwareRevisionsMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HardwareRevisionsMmu) ProtoMessage() {}

func (x *HardwareRevisionsMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HardwareRevisionsMmu.ProtoReflect.Descriptor instead.
func (*HardwareRevisionsMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{7}
}

func (x *HardwareRevisionsMmu) GetMainHardwareRevision() uint32 {
	if x != nil {
		return x.MainHardwareRevision
	}
	return 0
}

func (x *HardwareRevisionsMmu) GetIsoHardwareRevision() uint32 {
	if x != nil {
		return x.IsoHardwareRevision
	}
	return 0
}

func (x *HardwareRevisionsMmu) GetDisplayHardwareRevision() uint32 {
	if x != nil {
		return x.DisplayHardwareRevision
	}
	return 0
}

func (x *HardwareRevisionsMmu) GetDisplayRmsHardwareRevision() uint32 {
	if x != nil {
		return x.DisplayRmsHardwareRevision
	}
	return 0
}

func (x *HardwareRevisionsMmu) GetCommsHardwareRevision() uint32 {
	if x != nil {
		return x.CommsHardwareRevision
	}
	return 0
}

//	ConfigLogEntryMmu represents one entry of the MMU Configuration Log.
//
// This comes from the Main processor response to the command 0x05 Retrieve Configuration Log
type ConfigLogEntryMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique per log entry, over the lifetime of the monitor
	ConfigId uint32 `protobuf:"varint,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	// monitor hardware revisions
	Hardware *HardwareRevisionsMmu `protobuf:"bytes,2,opt,name=hardware,proto3" json:"hardware,omitempty"`
	// CCIT CRC16 with seed value 0xFFFF over 'data_key_data'
	DataKeyCrc uint32 `protobuf:"varint,10,opt,name=data_key_crc,json=dataKeyCrc,proto3" json:"data_key_crc,omitempty"`
	// Raw contents of the entire Data Key
	DataKeyData []byte `protobuf:"bytes,9,opt,name=data_key_data,json=dataKeyData,proto3" json:"data_key_data,omitempty"` // Max data length set in mon_logs.options
	// The Factory options in use for this configuration.
	FactoryOptions *settings.FactoryOptionsMmu `protobuf:"bytes,23,opt,name=factory_options,json=factoryOptions,proto3" json:"factory_options,omitempty"`
	// The PCB options in use for this configuration.
	PcbOptions *settings.PcbOptionsMmu `protobuf:"bytes,22,opt,name=pcb_options,json=pcbOptions,proto3" json:"pcb_options,omitempty"`
	// The Agency options in use for this configuration.
	AgencyOptions *settings.AgencyOptionsMmu `protobuf:"bytes,24,opt,name=agency_options,json=agencyOptions,proto3" json:"agency_options,omitempty"`
	// CCIT CRC16 with seed value 0xFFFF over 'data_key_data', 'pcb_options', 'factory_options', and 'agency_options'
	// The Monitor will compute this value.
	MonitorCrc uint32 `protobuf:"varint,12,opt,name=monitor_crc,json=monitorCrc,proto3" json:"monitor_crc,omitempty"`
	// Log entry timestamp
	EntryTimestamp *basic.LocalDateTime `protobuf:"bytes,4,opt,name=entry_timestamp,json=entryTimestamp,proto3" json:"entry_timestamp,omitempty"`
	// MMU serial number & MMU model number
	Numbers *basic.ModelAndSerialNumber `protobuf:"bytes,5,opt,name=numbers,proto3" json:"numbers,omitempty"`
	// Main processor firmware version and date
	MainMcuFwVersion *basic.VersionStrThree `protobuf:"bytes,6,opt,name=main_mcu_fw_version,json=mainMcuFwVersion,proto3" json:"main_mcu_fw_version,omitempty"`
	MainMcuFwDate    *basic.DateStr         `protobuf:"bytes,13,opt,name=main_mcu_fw_date,json=mainMcuFwDate,proto3" json:"main_mcu_fw_date,omitempty"`
	// Isolated processor firmware version and date
	IsolatedMcuFwVersion *basic.VersionStrThree `protobuf:"bytes,7,opt,name=isolated_mcu_fw_version,json=isolatedMcuFwVersion,proto3" json:"isolated_mcu_fw_version,omitempty"`
	IsolatedMcuFwDate    *basic.DateStr         `protobuf:"bytes,14,opt,name=isolated_mcu_fw_date,json=isolatedMcuFwDate,proto3" json:"isolated_mcu_fw_date,omitempty"`
	// Display processor firmware version and date
	DispMcuFwVersion *basic.VersionStrThree `protobuf:"bytes,15,opt,name=disp_mcu_fw_version,json=dispMcuFwVersion,proto3" json:"disp_mcu_fw_version,omitempty"`
	DispMcuFwDate    *basic.DateStr         `protobuf:"bytes,16,opt,name=disp_mcu_fw_date,json=dispMcuFwDate,proto3" json:"disp_mcu_fw_date,omitempty"`
	// Communication processor firmware version and date
	CommsMcuFwVersion *basic.VersionStrThree `protobuf:"bytes,8,opt,name=comms_mcu_fw_version,json=commsMcuFwVersion,proto3" json:"comms_mcu_fw_version,omitempty"`
	CommsMcuFwDate    *basic.DateStr         `protobuf:"bytes,17,opt,name=comms_mcu_fw_date,json=commsMcuFwDate,proto3" json:"comms_mcu_fw_date,omitempty"`
	// BLE processor firmware version and date
	BleMcuFwVersion *basic.VersionStrThree `protobuf:"bytes,18,opt,name=ble_mcu_fw_version,json=bleMcuFwVersion,proto3" json:"ble_mcu_fw_version,omitempty"`
	BleMcuFwDate    *basic.DateStr         `protobuf:"bytes,19,opt,name=ble_mcu_fw_date,json=bleMcuFwDate,proto3" json:"ble_mcu_fw_date,omitempty"`
	// Firmware package firmware version and date
	PkgMcuFwVersion *basic.VersionStrThree `protobuf:"bytes,20,opt,name=pkg_mcu_fw_version,json=pkgMcuFwVersion,proto3" json:"pkg_mcu_fw_version,omitempty"`
	PkgMcuFwDate    *basic.DateStr         `protobuf:"bytes,21,opt,name=pkg_mcu_fw_date,json=pkgMcuFwDate,proto3" json:"pkg_mcu_fw_date,omitempty"`
	// Data source - program card type
	DataSrc       settings.EConfigDataLocation `protobuf:"varint,25,opt,name=data_src,json=dataSrc,proto3,enum=settings.EConfigDataLocation" json:"data_src,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfigLogEntryMmu) Reset() {
	*x = ConfigLogEntryMmu{}
	mi := &file_mon_logs_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigLogEntryMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigLogEntryMmu) ProtoMessage() {}

func (x *ConfigLogEntryMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigLogEntryMmu.ProtoReflect.Descriptor instead.
func (*ConfigLogEntryMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{8}
}

func (x *ConfigLogEntryMmu) GetConfigId() uint32 {
	if x != nil {
		return x.ConfigId
	}
	return 0
}

func (x *ConfigLogEntryMmu) GetHardware() *HardwareRevisionsMmu {
	if x != nil {
		return x.Hardware
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetDataKeyCrc() uint32 {
	if x != nil {
		return x.DataKeyCrc
	}
	return 0
}

func (x *ConfigLogEntryMmu) GetDataKeyData() []byte {
	if x != nil {
		return x.DataKeyData
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetFactoryOptions() *settings.FactoryOptionsMmu {
	if x != nil {
		return x.FactoryOptions
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetPcbOptions() *settings.PcbOptionsMmu {
	if x != nil {
		return x.PcbOptions
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetAgencyOptions() *settings.AgencyOptionsMmu {
	if x != nil {
		return x.AgencyOptions
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetMonitorCrc() uint32 {
	if x != nil {
		return x.MonitorCrc
	}
	return 0
}

func (x *ConfigLogEntryMmu) GetEntryTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.EntryTimestamp
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetNumbers() *basic.ModelAndSerialNumber {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetMainMcuFwVersion() *basic.VersionStrThree {
	if x != nil {
		return x.MainMcuFwVersion
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetMainMcuFwDate() *basic.DateStr {
	if x != nil {
		return x.MainMcuFwDate
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetIsolatedMcuFwVersion() *basic.VersionStrThree {
	if x != nil {
		return x.IsolatedMcuFwVersion
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetIsolatedMcuFwDate() *basic.DateStr {
	if x != nil {
		return x.IsolatedMcuFwDate
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetDispMcuFwVersion() *basic.VersionStrThree {
	if x != nil {
		return x.DispMcuFwVersion
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetDispMcuFwDate() *basic.DateStr {
	if x != nil {
		return x.DispMcuFwDate
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetCommsMcuFwVersion() *basic.VersionStrThree {
	if x != nil {
		return x.CommsMcuFwVersion
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetCommsMcuFwDate() *basic.DateStr {
	if x != nil {
		return x.CommsMcuFwDate
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetBleMcuFwVersion() *basic.VersionStrThree {
	if x != nil {
		return x.BleMcuFwVersion
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetBleMcuFwDate() *basic.DateStr {
	if x != nil {
		return x.BleMcuFwDate
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetPkgMcuFwVersion() *basic.VersionStrThree {
	if x != nil {
		return x.PkgMcuFwVersion
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetPkgMcuFwDate() *basic.DateStr {
	if x != nil {
		return x.PkgMcuFwDate
	}
	return nil
}

func (x *ConfigLogEntryMmu) GetDataSrc() settings.EConfigDataLocation {
	if x != nil {
		return x.DataSrc
	}
	return settings.EConfigDataLocation(0)
}

//	ConfigLogMultipleEntriesMmu encapsulates repeated Configuration Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type ConfigLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*ConfigLogEntryMmu   `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfigLogMultipleEntriesMmu) Reset() {
	*x = ConfigLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigLogMultipleEntriesMmu) ProtoMessage() {}

func (x *ConfigLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*ConfigLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{9}
}

func (x *ConfigLogMultipleEntriesMmu) GetLogEntry() []*ConfigLogEntryMmu {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	Port1LogEntryMmu represents one entry of the MMu Port 1 Log.
//
// This comes from the Main processor response to the command 0x06 Retrieve Port 1 Log
type Port1LogEntryMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique per log entry
	EntryId uint32 `protobuf:"varint,1,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// Log entry timestamp
	EntryTimestamp *basic.LocalDateTime `protobuf:"bytes,2,opt,name=entry_timestamp,json=entryTimestamp,proto3" json:"entry_timestamp,omitempty"`
	// Timestamp when the counts were cleared (reset)
	TimestampWhenCleared *basic.LocalDateTime `protobuf:"bytes,3,opt,name=timestamp_when_cleared,json=timestampWhenCleared,proto3" json:"timestamp_when_cleared,omitempty"`
	// Count of frame CRC errors.  Maxes out at 65535
	CrcErrorCount uint32 `protobuf:"varint,4,opt,name=crc_error_count,json=crcErrorCount,proto3" json:"crc_error_count,omitempty"`
	// Count of frame idle errors, where the bus went idle with no closing frame byte.  Maxes out at 65535
	IdleErrorCount uint32 `protobuf:"varint,5,opt,name=idle_error_count,json=idleErrorCount,proto3" json:"idle_error_count,omitempty"`
	// Count of frame errors, where bus data was received without a starting frame character.  Maxes out at 65535
	FrameErrorCount uint32 `protobuf:"varint,6,opt,name=frame_error_count,json=frameErrorCount,proto3" json:"frame_error_count,omitempty"`
	// Count of bus timeout errors, where no frame from the controller was received in over 100 ms.
	// Maxes out at 65535
	TimeoutErrorCount uint32 `protobuf:"varint,7,opt,name=timeout_error_count,json=timeoutErrorCount,proto3" json:"timeout_error_count,omitempty"`
	// Count of short frame errors, where the frame length is less than expected. Maxes out at 65535
	ShortErrorCount uint32 `protobuf:"varint,8,opt,name=short_error_count,json=shortErrorCount,proto3" json:"short_error_count,omitempty"`
	// Count of long freame errors, where the frame length is more than expected.  Maxes out at 65535
	LongErrorCount uint32 `protobuf:"varint,9,opt,name=long_error_count,json=longErrorCount,proto3" json:"long_error_count,omitempty"`
	// Count of unknown frame number errors.  Maxes out at 65535
	UnknownErrorCount uint32 `protobuf:"varint,10,opt,name=unknown_error_count,json=unknownErrorCount,proto3" json:"unknown_error_count,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Port1LogEntryMmu) Reset() {
	*x = Port1LogEntryMmu{}
	mi := &file_mon_logs_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Port1LogEntryMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Port1LogEntryMmu) ProtoMessage() {}

func (x *Port1LogEntryMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Port1LogEntryMmu.ProtoReflect.Descriptor instead.
func (*Port1LogEntryMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{10}
}

func (x *Port1LogEntryMmu) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *Port1LogEntryMmu) GetEntryTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.EntryTimestamp
	}
	return nil
}

func (x *Port1LogEntryMmu) GetTimestampWhenCleared() *basic.LocalDateTime {
	if x != nil {
		return x.TimestampWhenCleared
	}
	return nil
}

func (x *Port1LogEntryMmu) GetCrcErrorCount() uint32 {
	if x != nil {
		return x.CrcErrorCount
	}
	return 0
}

func (x *Port1LogEntryMmu) GetIdleErrorCount() uint32 {
	if x != nil {
		return x.IdleErrorCount
	}
	return 0
}

func (x *Port1LogEntryMmu) GetFrameErrorCount() uint32 {
	if x != nil {
		return x.FrameErrorCount
	}
	return 0
}

func (x *Port1LogEntryMmu) GetTimeoutErrorCount() uint32 {
	if x != nil {
		return x.TimeoutErrorCount
	}
	return 0
}

func (x *Port1LogEntryMmu) GetShortErrorCount() uint32 {
	if x != nil {
		return x.ShortErrorCount
	}
	return 0
}

func (x *Port1LogEntryMmu) GetLongErrorCount() uint32 {
	if x != nil {
		return x.LongErrorCount
	}
	return 0
}

func (x *Port1LogEntryMmu) GetUnknownErrorCount() uint32 {
	if x != nil {
		return x.UnknownErrorCount
	}
	return 0
}

//	Port1LogLogMultipleEntriesMmu encapsulates repeated Port 1 Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type Port1LogLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*Port1LogEntryMmu    `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Port1LogLogMultipleEntriesMmu) Reset() {
	*x = Port1LogLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Port1LogLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Port1LogLogMultipleEntriesMmu) ProtoMessage() {}

func (x *Port1LogLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Port1LogLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*Port1LogLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{11}
}

func (x *Port1LogLogMultipleEntriesMmu) GetLogEntry() []*Port1LogEntryMmu {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	FaultHeaderLogEntryMmu represents one entry of the Fault Header Log for the MMU.
//
// This comes from the Main processor response to the command 0x08 Retrieve Fault Header Log
type FaultHeaderLogEntryMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique per log entry
	FaultId uint32 `protobuf:"varint,1,opt,name=fault_id,json=faultId,proto3" json:"fault_id,omitempty"`
	// Log entry timestamp
	EntryTimestamp *basic.LocalDateTime `protobuf:"bytes,2,opt,name=entry_timestamp,json=entryTimestamp,proto3" json:"entry_timestamp,omitempty"`
	// The Configuration Log entry id in effect at the time of the fault
	ConfigIdInUse uint32 `protobuf:"varint,3,opt,name=config_id_in_use,json=configIdInUse,proto3" json:"config_id_in_use,omitempty"`
	// CCIT CRC16 with seed value 0xFFFF over 'data_key_data'
	DataKeyCrc uint32 `protobuf:"varint,4,opt,name=data_key_crc,json=dataKeyCrc,proto3" json:"data_key_crc,omitempty"`
	// MMU serial number & model number
	Numbers *basic.ModelAndSerialNumber `protobuf:"bytes,5,opt,name=numbers,proto3" json:"numbers,omitempty"`
	// Monitor and user ID strings
	Ids *basic.MonitorAndUserIds `protobuf:"bytes,6,opt,name=ids,proto3" json:"ids,omitempty"`
	// Measurement log frequency (resolution) in entries per second
	MeasurementLogFrequency uint32 `protobuf:"varint,7,opt,name=measurement_log_frequency,json=measurementLogFrequency,proto3" json:"measurement_log_frequency,omitempty"`
	// Measurement log count of entries for this fault
	MeasurementLogEntryCount uint32 `protobuf:"varint,8,opt,name=measurement_log_entry_count,json=measurementLogEntryCount,proto3" json:"measurement_log_entry_count,omitempty"`
	// Sequence log frequency (resolution) in entries per second
	SequenceLogFrequency uint32 `protobuf:"varint,9,opt,name=sequence_log_frequency,json=sequenceLogFrequency,proto3" json:"sequence_log_frequency,omitempty"`
	// Sequence log count of entries for this fault
	SequenceLogEntryCount uint32 `protobuf:"varint,10,opt,name=sequence_log_entry_count,json=sequenceLogEntryCount,proto3" json:"sequence_log_entry_count,omitempty"`
	// Fault that occurred
	// A diagnostic fault may also occur along with another fault
	FaultCode       mon_faults.EFaultCode `protobuf:"varint,11,opt,name=fault_code,json=faultCode,proto3,enum=mon_faults.EFaultCode" json:"fault_code,omitempty"`
	DiagnosticFault bool                  `protobuf:"varint,12,opt,name=diagnostic_fault,json=diagnosticFault,proto3" json:"diagnostic_fault,omitempty"`
	// Subcode for fault
	FaultSubcode *mon_faults.MmuSubFaultTypeValue `protobuf:"bytes,13,opt,name=fault_subcode,json=faultSubcode,proto3" json:"fault_subcode,omitempty"`
	// First identified diagnostic fault code (if diagnostic_fault is TRUE)
	DiagnosticCode mon_faults.ESubFaultDiagnostic `protobuf:"varint,18,opt,name=diagnostic_code,json=diagnosticCode,proto3,enum=mon_faults.ESubFaultDiagnostic" json:"diagnostic_code,omitempty"`
	// bitmap of channels involved in the fault.  LSbit = Ch.1
	ChannelsChmap uint32 `protobuf:"fixed32,19,opt,name=channels_chmap,json=channelsChmap,proto3" json:"channels_chmap,omitempty"`
	// bitmap of channel red indications involved in the fault.  LSbit = Ch.1
	RedIndicationsChmap uint32 `protobuf:"fixed32,20,opt,name=red_indications_chmap,json=redIndicationsChmap,proto3" json:"red_indications_chmap,omitempty"`
	// bitmap of channel yellow indications involved in the fault.  LSbit = Ch.1
	YellowIndicationsChmap uint32 `protobuf:"fixed32,21,opt,name=yellow_indications_chmap,json=yellowIndicationsChmap,proto3" json:"yellow_indications_chmap,omitempty"`
	// bitmap of channel green indications involved in the fault.  LSbit = Ch.1
	GreenIndicationsChmap uint32 `protobuf:"fixed32,22,opt,name=green_indications_chmap,json=greenIndicationsChmap,proto3" json:"green_indications_chmap,omitempty"`
	// bitmap of channel walk indications involved in the fault.  LSbit = Ch.1
	WalkIndicationsChmap uint32 `protobuf:"fixed32,23,opt,name=walk_indications_chmap,json=walkIndicationsChmap,proto3" json:"walk_indications_chmap,omitempty"`
	// bitmap of channel red indications on at the time of the fault.  LSbit = Ch.1
	RedsOnChmap uint32 `protobuf:"fixed32,24,opt,name=reds_on_chmap,json=redsOnChmap,proto3" json:"reds_on_chmap,omitempty"`
	// bitmap of channel yellow indications on at the time of the fault.  LSbit = Ch.1
	YellowsOnChmap uint32 `protobuf:"fixed32,25,opt,name=yellows_on_chmap,json=yellowsOnChmap,proto3" json:"yellows_on_chmap,omitempty"`
	// bitmap of channel green indications on at the time of the fault.  LSbit = Ch.1
	GreensOnChmap uint32 `protobuf:"fixed32,26,opt,name=greens_on_chmap,json=greensOnChmap,proto3" json:"greens_on_chmap,omitempty"`
	// bitmap of channel walk indications on at the time of the fault.  LSbit = Ch.1
	WalkOnChmap uint32 `protobuf:"fixed32,27,opt,name=walk_on_chmap,json=walkOnChmap,proto3" json:"walk_on_chmap,omitempty"`
	// Mains supply frequency in Hz
	LineFrequency float32 `protobuf:"fixed32,28,opt,name=line_frequency,json=lineFrequency,proto3" json:"line_frequency,omitempty"`
	// Temperature in degrees F when fault occurred.
	TemperatureDegf float32 `protobuf:"fixed32,29,opt,name=temperature_degf,json=temperatureDegf,proto3" json:"temperature_degf,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *FaultHeaderLogEntryMmu) Reset() {
	*x = FaultHeaderLogEntryMmu{}
	mi := &file_mon_logs_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultHeaderLogEntryMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultHeaderLogEntryMmu) ProtoMessage() {}

func (x *FaultHeaderLogEntryMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultHeaderLogEntryMmu.ProtoReflect.Descriptor instead.
func (*FaultHeaderLogEntryMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{12}
}

func (x *FaultHeaderLogEntryMmu) GetFaultId() uint32 {
	if x != nil {
		return x.FaultId
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetEntryTimestamp() *basic.LocalDateTime {
	if x != nil {
		return x.EntryTimestamp
	}
	return nil
}

func (x *FaultHeaderLogEntryMmu) GetConfigIdInUse() uint32 {
	if x != nil {
		return x.ConfigIdInUse
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetDataKeyCrc() uint32 {
	if x != nil {
		return x.DataKeyCrc
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetNumbers() *basic.ModelAndSerialNumber {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *FaultHeaderLogEntryMmu) GetIds() *basic.MonitorAndUserIds {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *FaultHeaderLogEntryMmu) GetMeasurementLogFrequency() uint32 {
	if x != nil {
		return x.MeasurementLogFrequency
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetMeasurementLogEntryCount() uint32 {
	if x != nil {
		return x.MeasurementLogEntryCount
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetSequenceLogFrequency() uint32 {
	if x != nil {
		return x.SequenceLogFrequency
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetSequenceLogEntryCount() uint32 {
	if x != nil {
		return x.SequenceLogEntryCount
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetFaultCode() mon_faults.EFaultCode {
	if x != nil {
		return x.FaultCode
	}
	return mon_faults.EFaultCode(0)
}

func (x *FaultHeaderLogEntryMmu) GetDiagnosticFault() bool {
	if x != nil {
		return x.DiagnosticFault
	}
	return false
}

func (x *FaultHeaderLogEntryMmu) GetFaultSubcode() *mon_faults.MmuSubFaultTypeValue {
	if x != nil {
		return x.FaultSubcode
	}
	return nil
}

func (x *FaultHeaderLogEntryMmu) GetDiagnosticCode() mon_faults.ESubFaultDiagnostic {
	if x != nil {
		return x.DiagnosticCode
	}
	return mon_faults.ESubFaultDiagnostic(0)
}

func (x *FaultHeaderLogEntryMmu) GetChannelsChmap() uint32 {
	if x != nil {
		return x.ChannelsChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetRedIndicationsChmap() uint32 {
	if x != nil {
		return x.RedIndicationsChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetYellowIndicationsChmap() uint32 {
	if x != nil {
		return x.YellowIndicationsChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetGreenIndicationsChmap() uint32 {
	if x != nil {
		return x.GreenIndicationsChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetWalkIndicationsChmap() uint32 {
	if x != nil {
		return x.WalkIndicationsChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetRedsOnChmap() uint32 {
	if x != nil {
		return x.RedsOnChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetYellowsOnChmap() uint32 {
	if x != nil {
		return x.YellowsOnChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetGreensOnChmap() uint32 {
	if x != nil {
		return x.GreensOnChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetWalkOnChmap() uint32 {
	if x != nil {
		return x.WalkOnChmap
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetLineFrequency() float32 {
	if x != nil {
		return x.LineFrequency
	}
	return 0
}

func (x *FaultHeaderLogEntryMmu) GetTemperatureDegf() float32 {
	if x != nil {
		return x.TemperatureDegf
	}
	return 0
}

//	FaultHeaderLogMultipleEntriesMmu encapsulates repeated Fault Header Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type FaultHeaderLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	LogEntry      []*FaultHeaderLogEntryMmu `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaultHeaderLogMultipleEntriesMmu) Reset() {
	*x = FaultHeaderLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultHeaderLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultHeaderLogMultipleEntriesMmu) ProtoMessage() {}

func (x *FaultHeaderLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultHeaderLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*FaultHeaderLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{13}
}

func (x *FaultHeaderLogMultipleEntriesMmu) GetLogEntry() []*FaultHeaderLogEntryMmu {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	FaultMeasurementLogEntryMmu represents one entry of the Fault Measurement Log for the Mmu.
//
// This comes from the Main processor response to the command 0x09 Retrieve Fault Measurement Log
type FaultMeasurementLogEntryMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The matching fault log entry.
	FaultId uint32 `protobuf:"varint,1,opt,name=fault_id,json=faultId,proto3" json:"fault_id,omitempty"`
	// Each fault_id has its own set of numbered entry_ids.  Only the most recent faults may have measurement entries.
	EntryId uint32 `protobuf:"varint,2,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// Mains AC voltage, converted to float Volts
	MainsAcVolts *float32 `protobuf:"fixed32,3,opt,name=mains_ac_volts,json=mainsAcVolts,proto3,oneof" json:"mains_ac_volts,omitempty"`
	// bitmap of indication channels 'on' status bits using the normal threshold.  LSbit = Ch.1
	RedsOnNormalChmap    uint32 `protobuf:"fixed32,4,opt,name=reds_on_normal_chmap,json=redsOnNormalChmap,proto3" json:"reds_on_normal_chmap,omitempty"`
	YellowsOnNormalChmap uint32 `protobuf:"fixed32,5,opt,name=yellows_on_normal_chmap,json=yellowsOnNormalChmap,proto3" json:"yellows_on_normal_chmap,omitempty"`
	GreensOnNormalChmap  uint32 `protobuf:"fixed32,6,opt,name=greens_on_normal_chmap,json=greensOnNormalChmap,proto3" json:"greens_on_normal_chmap,omitempty"`
	WalksOnNormalChmap   uint32 `protobuf:"fixed32,7,opt,name=walks_on_normal_chmap,json=walksOnNormalChmap,proto3" json:"walks_on_normal_chmap,omitempty"`
	// bitmap of indication channels 'on' status bits using the lack of signal threshold.  LSbit = Ch.1
	RedsOnLackofsignalChmap    uint32 `protobuf:"fixed32,8,opt,name=reds_on_lackofsignal_chmap,json=redsOnLackofsignalChmap,proto3" json:"reds_on_lackofsignal_chmap,omitempty"`
	YellowsOnLackofsignalChmap uint32 `protobuf:"fixed32,9,opt,name=yellows_on_lackofsignal_chmap,json=yellowsOnLackofsignalChmap,proto3" json:"yellows_on_lackofsignal_chmap,omitempty"`
	GreensOnLackofsignalChmap  uint32 `protobuf:"fixed32,10,opt,name=greens_on_lackofsignal_chmap,json=greensOnLackofsignalChmap,proto3" json:"greens_on_lackofsignal_chmap,omitempty"`
	WalksOnLackofsignalChmap   uint32 `protobuf:"fixed32,11,opt,name=walks_on_lackofsignal_chmap,json=walksOnLackofsignalChmap,proto3" json:"walks_on_lackofsignal_chmap,omitempty"`
	// bitmap of indication channels 'on' status bits using the current threshold.  LSbit = Ch.1
	RedsOnCurrentChmap    uint32 `protobuf:"fixed32,12,opt,name=reds_on_current_chmap,json=redsOnCurrentChmap,proto3" json:"reds_on_current_chmap,omitempty"`
	YellowsOnCurrentChmap uint32 `protobuf:"fixed32,13,opt,name=yellows_on_current_chmap,json=yellowsOnCurrentChmap,proto3" json:"yellows_on_current_chmap,omitempty"`
	GreensOnCurrentChmap  uint32 `protobuf:"fixed32,14,opt,name=greens_on_current_chmap,json=greensOnCurrentChmap,proto3" json:"greens_on_current_chmap,omitempty"`
	WalksOnCurrentChmap   uint32 `protobuf:"fixed32,15,opt,name=walks_on_current_chmap,json=walksOnCurrentChmap,proto3" json:"walks_on_current_chmap,omitempty"`
	// bitmap of indication channels field check mistmatch.  LSbit = Ch.1
	RedsFieldCheckChmap    uint32 `protobuf:"fixed32,16,opt,name=reds_field_check_chmap,json=redsFieldCheckChmap,proto3" json:"reds_field_check_chmap,omitempty"`
	YellowsFieldCheckChmap uint32 `protobuf:"fixed32,17,opt,name=yellows_field_check_chmap,json=yellowsFieldCheckChmap,proto3" json:"yellows_field_check_chmap,omitempty"`
	GreensFieldCheckChmap  uint32 `protobuf:"fixed32,18,opt,name=greens_field_check_chmap,json=greensFieldCheckChmap,proto3" json:"greens_field_check_chmap,omitempty"`
	WalksFieldCheckChmap   uint32 `protobuf:"fixed32,19,opt,name=walks_field_check_chmap,json=walksFieldCheckChmap,proto3" json:"walks_field_check_chmap,omitempty"`
	// voltage and current measurements for each indicator per channel.
	// The current is converted from the serial message 15mA per bit units to Amp units.
	RedChannels    []*mon_faults.FaultIndicationChVoltCurrent `protobuf:"bytes,20,rep,name=red_channels,json=redChannels,proto3" json:"red_channels,omitempty"`          // Max repeat count set in mon_logs.options
	YellowChannels []*mon_faults.FaultIndicationChVoltCurrent `protobuf:"bytes,21,rep,name=yellow_channels,json=yellowChannels,proto3" json:"yellow_channels,omitempty"` // Max repeat count set in mon_logs.options
	GreenChannels  []*mon_faults.FaultIndicationChVoltCurrent `protobuf:"bytes,22,rep,name=green_channels,json=greenChannels,proto3" json:"green_channels,omitempty"`    // Max repeat count set in mon_logs.options
	WalkChannels   []*mon_faults.FaultIndicationChVoltCurrent `protobuf:"bytes,23,rep,name=walk_channels,json=walkChannels,proto3" json:"walk_channels,omitempty"`       // Max repeat count set in mon_logs.options
	// 24V monitor inhibit signal voltage, converted to float Volts
	Inhibit_24VMonitorVolts *float32 `protobuf:"fixed32,24,opt,name=inhibit_24v_monitor_volts,json=inhibit24vMonitorVolts,proto3,oneof" json:"inhibit_24v_monitor_volts,omitempty"`
	// 24V monitor 1 signal voltage, converted to float Volts
	Monitor_24V_1Volts *float32 `protobuf:"fixed32,25,opt,name=monitor_24v_1_volts,json=monitor24v1Volts,proto3,oneof" json:"monitor_24v_1_volts,omitempty"`
	// 24V monitor 2 signal voltage, converted to float Volts
	Monitor_24V_2Volts *float32 `protobuf:"fixed32,26,opt,name=monitor_24v_2_volts,json=monitor24v2Volts,proto3,oneof" json:"monitor_24v_2_volts,omitempty"`
	// Controller voltage monitor signal voltage, converted to float Volts
	ControllerMonitorVolts *float32 `protobuf:"fixed32,27,opt,name=controller_monitor_volts,json=controllerMonitorVolts,proto3,oneof" json:"controller_monitor_volts,omitempty"`
	// Type select voltage, converted to float Volts
	TypeSelectVolts *float32 `protobuf:"fixed32,28,opt,name=type_select_volts,json=typeSelectVolts,proto3,oneof" json:"type_select_volts,omitempty"`
	// Local Flash voltage, converted to float Volts
	LocalFlashVolts *float32 `protobuf:"fixed32,29,opt,name=local_flash_volts,json=localFlashVolts,proto3,oneof" json:"local_flash_volts,omitempty"`
	// External reset DC voltage, converted to float Volts
	ExternalResetVolts *float32 `protobuf:"fixed32,30,opt,name=external_reset_volts,json=externalResetVolts,proto3,oneof" json:"external_reset_volts,omitempty"`
	// Port 1 disable voltage, converted to float Volts
	Port1DisableVolts *float32 `protobuf:"fixed32,31,opt,name=port1_disable_volts,json=port1DisableVolts,proto3,oneof" json:"port1_disable_volts,omitempty"`
	// External Watchdog voltage, converted to float Volts
	ExternalWatchdogVolts *float32 `protobuf:"fixed32,32,opt,name=external_watchdog_volts,json=externalWatchdogVolts,proto3,oneof" json:"external_watchdog_volts,omitempty"`
	// Red enable voltage, converted to float Volts
	RedEnabledVolts *float32 `protobuf:"fixed32,33,opt,name=red_enabled_volts,json=redEnabledVolts,proto3,oneof" json:"red_enabled_volts,omitempty"`
	// Monitored input state bitmap
	InputStates   *mon_faults.MmuMonitoredInputsStatusBitmap `protobuf:"bytes,34,opt,name=input_states,json=inputStates,proto3" json:"input_states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaultMeasurementLogEntryMmu) Reset() {
	*x = FaultMeasurementLogEntryMmu{}
	mi := &file_mon_logs_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultMeasurementLogEntryMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultMeasurementLogEntryMmu) ProtoMessage() {}

func (x *FaultMeasurementLogEntryMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultMeasurementLogEntryMmu.ProtoReflect.Descriptor instead.
func (*FaultMeasurementLogEntryMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{14}
}

func (x *FaultMeasurementLogEntryMmu) GetFaultId() uint32 {
	if x != nil {
		return x.FaultId
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetMainsAcVolts() float32 {
	if x != nil && x.MainsAcVolts != nil {
		return *x.MainsAcVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetRedsOnNormalChmap() uint32 {
	if x != nil {
		return x.RedsOnNormalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetYellowsOnNormalChmap() uint32 {
	if x != nil {
		return x.YellowsOnNormalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetGreensOnNormalChmap() uint32 {
	if x != nil {
		return x.GreensOnNormalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetWalksOnNormalChmap() uint32 {
	if x != nil {
		return x.WalksOnNormalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetRedsOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.RedsOnLackofsignalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetYellowsOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.YellowsOnLackofsignalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetGreensOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.GreensOnLackofsignalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetWalksOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.WalksOnLackofsignalChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetRedsOnCurrentChmap() uint32 {
	if x != nil {
		return x.RedsOnCurrentChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetYellowsOnCurrentChmap() uint32 {
	if x != nil {
		return x.YellowsOnCurrentChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetGreensOnCurrentChmap() uint32 {
	if x != nil {
		return x.GreensOnCurrentChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetWalksOnCurrentChmap() uint32 {
	if x != nil {
		return x.WalksOnCurrentChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetRedsFieldCheckChmap() uint32 {
	if x != nil {
		return x.RedsFieldCheckChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetYellowsFieldCheckChmap() uint32 {
	if x != nil {
		return x.YellowsFieldCheckChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetGreensFieldCheckChmap() uint32 {
	if x != nil {
		return x.GreensFieldCheckChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetWalksFieldCheckChmap() uint32 {
	if x != nil {
		return x.WalksFieldCheckChmap
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetRedChannels() []*mon_faults.FaultIndicationChVoltCurrent {
	if x != nil {
		return x.RedChannels
	}
	return nil
}

func (x *FaultMeasurementLogEntryMmu) GetYellowChannels() []*mon_faults.FaultIndicationChVoltCurrent {
	if x != nil {
		return x.YellowChannels
	}
	return nil
}

func (x *FaultMeasurementLogEntryMmu) GetGreenChannels() []*mon_faults.FaultIndicationChVoltCurrent {
	if x != nil {
		return x.GreenChannels
	}
	return nil
}

func (x *FaultMeasurementLogEntryMmu) GetWalkChannels() []*mon_faults.FaultIndicationChVoltCurrent {
	if x != nil {
		return x.WalkChannels
	}
	return nil
}

func (x *FaultMeasurementLogEntryMmu) GetInhibit_24VMonitorVolts() float32 {
	if x != nil && x.Inhibit_24VMonitorVolts != nil {
		return *x.Inhibit_24VMonitorVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetMonitor_24V_1Volts() float32 {
	if x != nil && x.Monitor_24V_1Volts != nil {
		return *x.Monitor_24V_1Volts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetMonitor_24V_2Volts() float32 {
	if x != nil && x.Monitor_24V_2Volts != nil {
		return *x.Monitor_24V_2Volts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetControllerMonitorVolts() float32 {
	if x != nil && x.ControllerMonitorVolts != nil {
		return *x.ControllerMonitorVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetTypeSelectVolts() float32 {
	if x != nil && x.TypeSelectVolts != nil {
		return *x.TypeSelectVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetLocalFlashVolts() float32 {
	if x != nil && x.LocalFlashVolts != nil {
		return *x.LocalFlashVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetExternalResetVolts() float32 {
	if x != nil && x.ExternalResetVolts != nil {
		return *x.ExternalResetVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetPort1DisableVolts() float32 {
	if x != nil && x.Port1DisableVolts != nil {
		return *x.Port1DisableVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetExternalWatchdogVolts() float32 {
	if x != nil && x.ExternalWatchdogVolts != nil {
		return *x.ExternalWatchdogVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetRedEnabledVolts() float32 {
	if x != nil && x.RedEnabledVolts != nil {
		return *x.RedEnabledVolts
	}
	return 0
}

func (x *FaultMeasurementLogEntryMmu) GetInputStates() *mon_faults.MmuMonitoredInputsStatusBitmap {
	if x != nil {
		return x.InputStates
	}
	return nil
}

//	FaultMeasurementLogMultipleEntriesMmu encapsulates repeated Fault Measurement Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type FaultMeasurementLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	LogEntry      []*FaultMeasurementLogEntryMmu `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaultMeasurementLogMultipleEntriesMmu) Reset() {
	*x = FaultMeasurementLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultMeasurementLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultMeasurementLogMultipleEntriesMmu) ProtoMessage() {}

func (x *FaultMeasurementLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultMeasurementLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*FaultMeasurementLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{15}
}

func (x *FaultMeasurementLogMultipleEntriesMmu) GetLogEntry() []*FaultMeasurementLogEntryMmu {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	FaultSequenceLogEntryMmu represents one entry of the Fault Sequence Log for Mmu.
//
// This comes from the Main processor response to the command 0x0A Retrieve Fault Sequence Log
type FaultSequenceLogEntryMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The matching fault log entry.
	FaultId uint32 `protobuf:"varint,1,opt,name=fault_id,json=faultId,proto3" json:"fault_id,omitempty"`
	// Each fault_id has its own set of numbered entry_ids.  Only the most recent faults may have sequence entries.
	EntryId uint32 `protobuf:"varint,2,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// The number of periods with all data at the given states
	PeriodCount uint32 `protobuf:"varint,3,opt,name=period_count,json=periodCount,proto3" json:"period_count,omitempty"`
	// Monitored control states bitmap
	ControlStates *mon_faults.MmuMonitoredControlStatesBitmap `protobuf:"bytes,4,opt,name=control_states,json=controlStates,proto3" json:"control_states,omitempty"`
	// Monitored input state bitmap
	InputStates *mon_faults.MmuMonitoredInputsStatusBitmap `protobuf:"bytes,5,opt,name=input_states,json=inputStates,proto3" json:"input_states,omitempty"`
	// bitmap of indication channels 'on' status bits using the normal threshold.  LSbit = Ch.1
	RedsOnNormalChmap    uint32 `protobuf:"fixed32,6,opt,name=reds_on_normal_chmap,json=redsOnNormalChmap,proto3" json:"reds_on_normal_chmap,omitempty"`
	YellowsOnNormalChmap uint32 `protobuf:"fixed32,7,opt,name=yellows_on_normal_chmap,json=yellowsOnNormalChmap,proto3" json:"yellows_on_normal_chmap,omitempty"`
	GreensOnNormalChmap  uint32 `protobuf:"fixed32,8,opt,name=greens_on_normal_chmap,json=greensOnNormalChmap,proto3" json:"greens_on_normal_chmap,omitempty"`
	WalksOnNormalChmap   uint32 `protobuf:"fixed32,9,opt,name=walks_on_normal_chmap,json=walksOnNormalChmap,proto3" json:"walks_on_normal_chmap,omitempty"`
	// bitmap of indication channels 'on' status bits using the lack of signal threshold.  LSbit = Ch.1
	RedsOnLackofsignalChmap    uint32 `protobuf:"fixed32,10,opt,name=reds_on_lackofsignal_chmap,json=redsOnLackofsignalChmap,proto3" json:"reds_on_lackofsignal_chmap,omitempty"`
	YellowsOnLackofsignalChmap uint32 `protobuf:"fixed32,11,opt,name=yellows_on_lackofsignal_chmap,json=yellowsOnLackofsignalChmap,proto3" json:"yellows_on_lackofsignal_chmap,omitempty"`
	GreensOnLackofsignalChmap  uint32 `protobuf:"fixed32,12,opt,name=greens_on_lackofsignal_chmap,json=greensOnLackofsignalChmap,proto3" json:"greens_on_lackofsignal_chmap,omitempty"`
	WalksOnLackofsignalChmap   uint32 `protobuf:"fixed32,13,opt,name=walks_on_lackofsignal_chmap,json=walksOnLackofsignalChmap,proto3" json:"walks_on_lackofsignal_chmap,omitempty"`
	// bitmap of indication channels 'on' status bits using the current threshold.  LSbit = Ch.1
	RedsOnCurrentChmap    uint32 `protobuf:"fixed32,14,opt,name=reds_on_current_chmap,json=redsOnCurrentChmap,proto3" json:"reds_on_current_chmap,omitempty"`
	YellowsOnCurrentChmap uint32 `protobuf:"fixed32,15,opt,name=yellows_on_current_chmap,json=yellowsOnCurrentChmap,proto3" json:"yellows_on_current_chmap,omitempty"`
	GreensOnCurrentChmap  uint32 `protobuf:"fixed32,16,opt,name=greens_on_current_chmap,json=greensOnCurrentChmap,proto3" json:"greens_on_current_chmap,omitempty"`
	WalksOnCurrentChmap   uint32 `protobuf:"fixed32,17,opt,name=walks_on_current_chmap,json=walksOnCurrentChmap,proto3" json:"walks_on_current_chmap,omitempty"`
	// bitmap of indication channels field check mistmatch.  LSbit = Ch.1
	RedsFieldCheckChmap    uint32 `protobuf:"fixed32,18,opt,name=reds_field_check_chmap,json=redsFieldCheckChmap,proto3" json:"reds_field_check_chmap,omitempty"`
	YellowsFieldCheckChmap uint32 `protobuf:"fixed32,19,opt,name=yellows_field_check_chmap,json=yellowsFieldCheckChmap,proto3" json:"yellows_field_check_chmap,omitempty"`
	GreensFieldCheckChmap  uint32 `protobuf:"fixed32,20,opt,name=greens_field_check_chmap,json=greensFieldCheckChmap,proto3" json:"greens_field_check_chmap,omitempty"`
	WalksFieldCheckChmap   uint32 `protobuf:"fixed32,21,opt,name=walks_field_check_chmap,json=walksFieldCheckChmap,proto3" json:"walks_field_check_chmap,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *FaultSequenceLogEntryMmu) Reset() {
	*x = FaultSequenceLogEntryMmu{}
	mi := &file_mon_logs_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultSequenceLogEntryMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultSequenceLogEntryMmu) ProtoMessage() {}

func (x *FaultSequenceLogEntryMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultSequenceLogEntryMmu.ProtoReflect.Descriptor instead.
func (*FaultSequenceLogEntryMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{16}
}

func (x *FaultSequenceLogEntryMmu) GetFaultId() uint32 {
	if x != nil {
		return x.FaultId
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetPeriodCount() uint32 {
	if x != nil {
		return x.PeriodCount
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetControlStates() *mon_faults.MmuMonitoredControlStatesBitmap {
	if x != nil {
		return x.ControlStates
	}
	return nil
}

func (x *FaultSequenceLogEntryMmu) GetInputStates() *mon_faults.MmuMonitoredInputsStatusBitmap {
	if x != nil {
		return x.InputStates
	}
	return nil
}

func (x *FaultSequenceLogEntryMmu) GetRedsOnNormalChmap() uint32 {
	if x != nil {
		return x.RedsOnNormalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetYellowsOnNormalChmap() uint32 {
	if x != nil {
		return x.YellowsOnNormalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetGreensOnNormalChmap() uint32 {
	if x != nil {
		return x.GreensOnNormalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetWalksOnNormalChmap() uint32 {
	if x != nil {
		return x.WalksOnNormalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetRedsOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.RedsOnLackofsignalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetYellowsOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.YellowsOnLackofsignalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetGreensOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.GreensOnLackofsignalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetWalksOnLackofsignalChmap() uint32 {
	if x != nil {
		return x.WalksOnLackofsignalChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetRedsOnCurrentChmap() uint32 {
	if x != nil {
		return x.RedsOnCurrentChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetYellowsOnCurrentChmap() uint32 {
	if x != nil {
		return x.YellowsOnCurrentChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetGreensOnCurrentChmap() uint32 {
	if x != nil {
		return x.GreensOnCurrentChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetWalksOnCurrentChmap() uint32 {
	if x != nil {
		return x.WalksOnCurrentChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetRedsFieldCheckChmap() uint32 {
	if x != nil {
		return x.RedsFieldCheckChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetYellowsFieldCheckChmap() uint32 {
	if x != nil {
		return x.YellowsFieldCheckChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetGreensFieldCheckChmap() uint32 {
	if x != nil {
		return x.GreensFieldCheckChmap
	}
	return 0
}

func (x *FaultSequenceLogEntryMmu) GetWalksFieldCheckChmap() uint32 {
	if x != nil {
		return x.WalksFieldCheckChmap
	}
	return 0
}

//	FaultSequenceLogMultipleEntriesMmu encapsulates repeated Fault Sequence Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type FaultSequenceLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	LogEntry      []*FaultSequenceLogEntryMmu `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaultSequenceLogMultipleEntriesMmu) Reset() {
	*x = FaultSequenceLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultSequenceLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultSequenceLogMultipleEntriesMmu) ProtoMessage() {}

func (x *FaultSequenceLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultSequenceLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*FaultSequenceLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{17}
}

func (x *FaultSequenceLogMultipleEntriesMmu) GetLogEntry() []*FaultSequenceLogEntryMmu {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	FaultFactsLogEntry represents one entry of the Fault FACTS Log.
//
// This comes from the Main processor response to the command 0x0C Retrieve Fault FACTS Log
type FaultFactsLogEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The matching fault log entry.
	FaultId uint32 `protobuf:"varint,1,opt,name=fault_id,json=faultId,proto3" json:"fault_id,omitempty"`
	// unique per FACTS log entry
	EntryId uint32 `protobuf:"varint,2,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// Date/Time stamp
	Datetime *basic.LocalDateTime `protobuf:"bytes,3,opt,name=datetime,proto3" json:"datetime,omitempty"`
	// ADC Samples of the input.  Each sample is 16 bits, in centiVolt (0.01 V), milliamps, or centihertz units
	Samples_16BCv []byte `protobuf:"bytes,4,opt,name=samples_16b_cv,json=samples16bCv,proto3" json:"samples_16b_cv,omitempty"`
	// The length of time the captured samples span, in milliseconds
	CaptureTimeMs uint32 `protobuf:"varint,5,opt,name=capture_time_ms,json=captureTimeMs,proto3" json:"capture_time_ms,omitempty"`
	// The channel from which the samples are captured.
	CaptureChannel uint32 `protobuf:"varint,6,opt,name=capture_channel,json=captureChannel,proto3" json:"capture_channel,omitempty"`
	// The type of channel signal input source that is captured
	ChannelInput  ECaptureChannelInput `protobuf:"varint,7,opt,name=channel_input,json=channelInput,proto3,enum=mon_logs.ECaptureChannelInput" json:"channel_input,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaultFactsLogEntry) Reset() {
	*x = FaultFactsLogEntry{}
	mi := &file_mon_logs_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultFactsLogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultFactsLogEntry) ProtoMessage() {}

func (x *FaultFactsLogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultFactsLogEntry.ProtoReflect.Descriptor instead.
func (*FaultFactsLogEntry) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{18}
}

func (x *FaultFactsLogEntry) GetFaultId() uint32 {
	if x != nil {
		return x.FaultId
	}
	return 0
}

func (x *FaultFactsLogEntry) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *FaultFactsLogEntry) GetDatetime() *basic.LocalDateTime {
	if x != nil {
		return x.Datetime
	}
	return nil
}

func (x *FaultFactsLogEntry) GetSamples_16BCv() []byte {
	if x != nil {
		return x.Samples_16BCv
	}
	return nil
}

func (x *FaultFactsLogEntry) GetCaptureTimeMs() uint32 {
	if x != nil {
		return x.CaptureTimeMs
	}
	return 0
}

func (x *FaultFactsLogEntry) GetCaptureChannel() uint32 {
	if x != nil {
		return x.CaptureChannel
	}
	return 0
}

func (x *FaultFactsLogEntry) GetChannelInput() ECaptureChannelInput {
	if x != nil {
		return x.ChannelInput
	}
	return ECaptureChannelInput_CH_INPUT_UNSPECIFIED
}

//	FaultFactsLogMultipleEntriesMmu encapsulates repeated Fault Sequence Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type FaultFactsLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*FaultFactsLogEntry  `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaultFactsLogMultipleEntriesMmu) Reset() {
	*x = FaultFactsLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaultFactsLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaultFactsLogMultipleEntriesMmu) ProtoMessage() {}

func (x *FaultFactsLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaultFactsLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*FaultFactsLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{19}
}

func (x *FaultFactsLogMultipleEntriesMmu) GetLogEntry() []*FaultFactsLogEntry {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	AlarmLogEntry represents one entry of the Alarm Log.
//
// This comes from the Main processor response to the command 0x0B Retrieve Alarm Log.
type AlarmLogEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// unique per log entry
	EntryId uint32 `protobuf:"varint,1,opt,name=entry_id,json=entryId,proto3" json:"entry_id,omitempty"`
	// Alarm severity
	Severity EAlarmSeverity `protobuf:"varint,2,opt,name=severity,proto3,enum=mon_logs.EAlarmSeverity" json:"severity,omitempty"`
	// Alarm source
	Source EAlarmSource `protobuf:"varint,3,opt,name=source,proto3,enum=mon_logs.EAlarmSource" json:"source,omitempty"`
	// Date/Time stamp
	Datetime *basic.LocalDateTime `protobuf:"bytes,4,opt,name=datetime,proto3" json:"datetime,omitempty"`
	// Alarm text string
	Text          string `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"` // Max string length set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlarmLogEntry) Reset() {
	*x = AlarmLogEntry{}
	mi := &file_mon_logs_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmLogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmLogEntry) ProtoMessage() {}

func (x *AlarmLogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmLogEntry.ProtoReflect.Descriptor instead.
func (*AlarmLogEntry) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{20}
}

func (x *AlarmLogEntry) GetEntryId() uint32 {
	if x != nil {
		return x.EntryId
	}
	return 0
}

func (x *AlarmLogEntry) GetSeverity() EAlarmSeverity {
	if x != nil {
		return x.Severity
	}
	return EAlarmSeverity_ALARM_SEVERITY_CRITICAL
}

func (x *AlarmLogEntry) GetSource() EAlarmSource {
	if x != nil {
		return x.Source
	}
	return EAlarmSource_ALARM_SOURCE_MONITOR_OPERATION
}

func (x *AlarmLogEntry) GetDatetime() *basic.LocalDateTime {
	if x != nil {
		return x.Datetime
	}
	return nil
}

func (x *AlarmLogEntry) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

//	AlarmLogMultipleEntriesMmu encapsulates repeated Alarm Log entries.
//
// This is done because having repeated oneof{} fields in a messsage is
// not allowed.  This allows having oneof{} in a message that may return
// multiple entries for different logs.
type AlarmLogMultipleEntriesMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogEntry      []*AlarmLogEntry       `protobuf:"bytes,1,rep,name=log_entry,json=logEntry,proto3" json:"log_entry,omitempty"` // Max repeat count set in mon_logs.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlarmLogMultipleEntriesMmu) Reset() {
	*x = AlarmLogMultipleEntriesMmu{}
	mi := &file_mon_logs_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmLogMultipleEntriesMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmLogMultipleEntriesMmu) ProtoMessage() {}

func (x *AlarmLogMultipleEntriesMmu) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmLogMultipleEntriesMmu.ProtoReflect.Descriptor instead.
func (*AlarmLogMultipleEntriesMmu) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{21}
}

func (x *AlarmLogMultipleEntriesMmu) GetLogEntry() []*AlarmLogEntry {
	if x != nil {
		return x.LogEntry
	}
	return nil
}

//	LogEntryCount is used to return the total count of entries for a log.
//
// These come from the Main processor response to the command 0x0F Retrieve Logging System Details
type LogEntryCount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is the log to which the count applies
	Log EMonitorLogType `protobuf:"varint,1,opt,name=log,proto3,enum=mon_logs.EMonitorLogType" json:"log,omitempty"`
	// The number of the first/oldest entry available. (Always > 0)
	StartEntryId uint32 `protobuf:"varint,2,opt,name=start_entry_id,json=startEntryId,proto3" json:"start_entry_id,omitempty"`
	// Total number of log entries.
	Entries uint32 `protobuf:"varint,3,opt,name=entries,proto3" json:"entries,omitempty"`
	// Log entry format - This is the format the log is stored in on the monitor.
	//
	//	This field is not used for clear log responses
	Format        *uint32 `protobuf:"varint,4,opt,name=format,proto3,oneof" json:"format,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEntryCount) Reset() {
	*x = LogEntryCount{}
	mi := &file_mon_logs_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEntryCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEntryCount) ProtoMessage() {}

func (x *LogEntryCount) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEntryCount.ProtoReflect.Descriptor instead.
func (*LogEntryCount) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{22}
}

func (x *LogEntryCount) GetLog() EMonitorLogType {
	if x != nil {
		return x.Log
	}
	return EMonitorLogType_MON_LOG_UNSPECIFIED
}

func (x *LogEntryCount) GetStartEntryId() uint32 {
	if x != nil {
		return x.StartEntryId
	}
	return 0
}

func (x *LogEntryCount) GetEntries() uint32 {
	if x != nil {
		return x.Entries
	}
	return 0
}

func (x *LogEntryCount) GetFormat() uint32 {
	if x != nil && x.Format != nil {
		return *x.Format
	}
	return 0
}

// DataKeyErrorCodeBitmap are possible errors accessing the data key / program card.
type DataKeyErrorCodeBitmap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error_1       bool                   `protobuf:"varint,1,opt,name=error_1,json=error1,proto3" json:"error_1,omitempty"` // bitmap is TBD
	Error_2       bool                   `protobuf:"varint,2,opt,name=error_2,json=error2,proto3" json:"error_2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataKeyErrorCodeBitmap) Reset() {
	*x = DataKeyErrorCodeBitmap{}
	mi := &file_mon_logs_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataKeyErrorCodeBitmap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataKeyErrorCodeBitmap) ProtoMessage() {}

func (x *DataKeyErrorCodeBitmap) ProtoReflect() protoreflect.Message {
	mi := &file_mon_logs_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataKeyErrorCodeBitmap.ProtoReflect.Descriptor instead.
func (*DataKeyErrorCodeBitmap) Descriptor() ([]byte, []int) {
	return file_mon_logs_proto_rawDescGZIP(), []int{23}
}

func (x *DataKeyErrorCodeBitmap) GetError_1() bool {
	if x != nil {
		return x.Error_1
	}
	return false
}

func (x *DataKeyErrorCodeBitmap) GetError_2() bool {
	if x != nil {
		return x.Error_2
	}
	return false
}

var File_mon_logs_proto protoreflect.FileDescriptor

const file_mon_logs_proto_rawDesc = "" +
	"\n" +
	"\x0emon_logs.proto\x12\bmon_logs\x1a\vbasic.proto\x1a\x10mon_faults.proto\x1a\x0esettings.proto\"\xc4\x02\n" +
	"\x10MmuPowerMonitors\x12;\n" +
	"\x0eac_mains_volts\x18\x01 \x01(\v2\x15.basic.NowMinMaxFloatR\facMainsVolts\x12>\n" +
	"\x10dc_24_volts_mon1\x18\x02 \x01(\v2\x15.basic.NowMinMaxFloatR\rdc24VoltsMon1\x12>\n" +
	"\x10dc_24_volts_mon2\x18\x03 \x01(\v2\x15.basic.NowMinMaxFloatR\rdc24VoltsMon2\x120\n" +
	"\bcvm_volt\x18\x04 \x01(\v2\x15.basic.NowMinMaxFloatR\acvmVolt\x12A\n" +
	"\x11line_frequency_hz\x18\x05 \x01(\v2\x15.basic.NowMinMaxFloatR\x0flineFrequencyHz\"\xcf\x03\n" +
	"\x10PowerLogEntryMmu\x12\x19\n" +
	"\bentry_id\x18\x01 \x01(\rR\aentryId\x12;\n" +
	"\n" +
	"event_type\x18\x02 \x01(\x0e2\x1c.mon_logs.EPowerLogEventTypeR\teventType\x12=\n" +
	"\x0fentry_timestamp\x18\x03 \x01(\v2\x14.basic.LocalDateTimeR\x0eentryTimestamp\x12A\n" +
	"\x0epower_voltages\x18\x04 \x01(\v2\x1a.mon_logs.MmuPowerMonitorsR\rpowerVoltages\x12@\n" +
	"\x10temperature_degf\x18\x05 \x01(\v2\x15.basic.NowMinMaxFloatR\x0ftemperatureDegf\x12A\n" +
	"\fevent_timing\x18\b \x01(\x0e2\x1e.mon_logs.EPowerLogEventTimingR\veventTiming\x12:\n" +
	"\x17power_interrupt_time_ms\x18\a \x01(\rH\x00R\x14powerInterruptTimeMs\x88\x01\x01B\x1a\n" +
	"\x18_power_interrupt_time_msJ\x04\b\x06\x10\a\"U\n" +
	"\x1aPowerLogMultipleEntriesMmu\x127\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x1a.mon_logs.PowerLogEntryMmuR\blogEntry\"\xb1\x02\n" +
	"\x10ResetLogEntryMmu\x12\x19\n" +
	"\bentry_id\x18\x01 \x01(\rR\aentryId\x12<\n" +
	"\freset_source\x18\x02 \x01(\x0e2\x19.mon_logs.EResetLogSourceR\vresetSource\x12=\n" +
	"\x0fentry_timestamp\x18\x03 \x01(\v2\x14.basic.LocalDateTimeR\x0eentryTimestamp\x12;\n" +
	"\rfault_cleared\x18\x04 \x01(\x0e2\x16.mon_faults.EFaultCodeR\ffaultCleared\x12-\n" +
	"\x12diagnostic_cleared\x18\x05 \x01(\bR\x11diagnosticCleared\x12\x19\n" +
	"\bfault_id\x18\x06 \x01(\rR\afaultId\"U\n" +
	"\x1aResetLogMultipleEntriesMmu\x127\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x1a.mon_logs.ResetLogEntryMmuR\blogEntry\"\xff\x01\n" +
	"\rClockLogEntry\x12\x19\n" +
	"\bentry_id\x18\x01 \x01(\rR\aentryId\x12<\n" +
	"\fclock_source\x18\x02 \x01(\x0e2\x19.mon_logs.EClockLogSourceR\vclockSource\x12A\n" +
	"\x11previous_datetime\x18\x03 \x01(\v2\x14.basic.LocalDateTimeR\x10previousDatetime\x127\n" +
	"\fnew_datetime\x18\x04 \x01(\v2\x14.basic.LocalDateTimeR\vnewDatetime\x12\x19\n" +
	"\brun_time\x18\x05 \x01(\rR\arunTime\"R\n" +
	"\x1aClockLogMultipleEntriesMmu\x124\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x17.mon_logs.ClockLogEntryR\blogEntry\"\xb7\x02\n" +
	"\x14HardwareRevisionsMmu\x124\n" +
	"\x16main_hardware_revision\x18\x01 \x01(\rR\x14mainHardwareRevision\x122\n" +
	"\x15iso_hardware_revision\x18\x02 \x01(\rR\x13isoHardwareRevision\x12:\n" +
	"\x19display_hardware_revision\x18\x03 \x01(\rR\x17displayHardwareRevision\x12A\n" +
	"\x1ddisplay_rms_hardware_revision\x18\x04 \x01(\rR\x1adisplayRmsHardwareRevision\x126\n" +
	"\x17comms_hardware_revision\x18\x05 \x01(\rR\x15commsHardwareRevision\"\xde\n" +
	"\n" +
	"\x11ConfigLogEntryMmu\x12\x1b\n" +
	"\tconfig_id\x18\x01 \x01(\rR\bconfigId\x12:\n" +
	"\bhardware\x18\x02 \x01(\v2\x1e.mon_logs.HardwareRevisionsMmuR\bhardware\x12 \n" +
	"\fdata_key_crc\x18\n" +
	" \x01(\rR\n" +
	"dataKeyCrc\x12\"\n" +
	"\rdata_key_data\x18\t \x01(\fR\vdataKeyData\x12D\n" +
	"\x0ffactory_options\x18\x17 \x01(\v2\x1b.settings.FactoryOptionsMmuR\x0efactoryOptions\x128\n" +
	"\vpcb_options\x18\x16 \x01(\v2\x17.settings.PcbOptionsMmuR\n" +
	"pcbOptions\x12A\n" +
	"\x0eagency_options\x18\x18 \x01(\v2\x1a.settings.AgencyOptionsMmuR\ragencyOptions\x12\x1f\n" +
	"\vmonitor_crc\x18\f \x01(\rR\n" +
	"monitorCrc\x12=\n" +
	"\x0fentry_timestamp\x18\x04 \x01(\v2\x14.basic.LocalDateTimeR\x0eentryTimestamp\x125\n" +
	"\anumbers\x18\x05 \x01(\v2\x1b.basic.ModelAndSerialNumberR\anumbers\x12E\n" +
	"\x13main_mcu_fw_version\x18\x06 \x01(\v2\x16.basic.VersionStrThreeR\x10mainMcuFwVersion\x127\n" +
	"\x10main_mcu_fw_date\x18\r \x01(\v2\x0e.basic.DateStrR\rmainMcuFwDate\x12M\n" +
	"\x17isolated_mcu_fw_version\x18\a \x01(\v2\x16.basic.VersionStrThreeR\x14isolatedMcuFwVersion\x12?\n" +
	"\x14isolated_mcu_fw_date\x18\x0e \x01(\v2\x0e.basic.DateStrR\x11isolatedMcuFwDate\x12E\n" +
	"\x13disp_mcu_fw_version\x18\x0f \x01(\v2\x16.basic.VersionStrThreeR\x10dispMcuFwVersion\x127\n" +
	"\x10disp_mcu_fw_date\x18\x10 \x01(\v2\x0e.basic.DateStrR\rdispMcuFwDate\x12G\n" +
	"\x14comms_mcu_fw_version\x18\b \x01(\v2\x16.basic.VersionStrThreeR\x11commsMcuFwVersion\x129\n" +
	"\x11comms_mcu_fw_date\x18\x11 \x01(\v2\x0e.basic.DateStrR\x0ecommsMcuFwDate\x12C\n" +
	"\x12ble_mcu_fw_version\x18\x12 \x01(\v2\x16.basic.VersionStrThreeR\x0fbleMcuFwVersion\x125\n" +
	"\x0fble_mcu_fw_date\x18\x13 \x01(\v2\x0e.basic.DateStrR\fbleMcuFwDate\x12C\n" +
	"\x12pkg_mcu_fw_version\x18\x14 \x01(\v2\x16.basic.VersionStrThreeR\x0fpkgMcuFwVersion\x125\n" +
	"\x0fpkg_mcu_fw_date\x18\x15 \x01(\v2\x0e.basic.DateStrR\fpkgMcuFwDate\x128\n" +
	"\bdata_src\x18\x19 \x01(\x0e2\x1d.settings.EConfigDataLocationR\adataSrcJ\x04\b\x03\x10\x04J\x04\b\v\x10\f\"W\n" +
	"\x1bConfigLogMultipleEntriesMmu\x128\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x1b.mon_logs.ConfigLogEntryMmuR\blogEntry\"\xec\x03\n" +
	"\x10Port1LogEntryMmu\x12\x19\n" +
	"\bentry_id\x18\x01 \x01(\rR\aentryId\x12=\n" +
	"\x0fentry_timestamp\x18\x02 \x01(\v2\x14.basic.LocalDateTimeR\x0eentryTimestamp\x12J\n" +
	"\x16timestamp_when_cleared\x18\x03 \x01(\v2\x14.basic.LocalDateTimeR\x14timestampWhenCleared\x12&\n" +
	"\x0fcrc_error_count\x18\x04 \x01(\rR\rcrcErrorCount\x12(\n" +
	"\x10idle_error_count\x18\x05 \x01(\rR\x0eidleErrorCount\x12*\n" +
	"\x11frame_error_count\x18\x06 \x01(\rR\x0fframeErrorCount\x12.\n" +
	"\x13timeout_error_count\x18\a \x01(\rR\x11timeoutErrorCount\x12*\n" +
	"\x11short_error_count\x18\b \x01(\rR\x0fshortErrorCount\x12(\n" +
	"\x10long_error_count\x18\t \x01(\rR\x0elongErrorCount\x12.\n" +
	"\x13unknown_error_count\x18\n" +
	" \x01(\rR\x11unknownErrorCount\"X\n" +
	"\x1dPort1LogLogMultipleEntriesMmu\x127\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x1a.mon_logs.Port1LogEntryMmuR\blogEntry\"\xec\t\n" +
	"\x16FaultHeaderLogEntryMmu\x12\x19\n" +
	"\bfault_id\x18\x01 \x01(\rR\afaultId\x12=\n" +
	"\x0fentry_timestamp\x18\x02 \x01(\v2\x14.basic.LocalDateTimeR\x0eentryTimestamp\x12'\n" +
	"\x10config_id_in_use\x18\x03 \x01(\rR\rconfigIdInUse\x12 \n" +
	"\fdata_key_crc\x18\x04 \x01(\rR\n" +
	"dataKeyCrc\x125\n" +
	"\anumbers\x18\x05 \x01(\v2\x1b.basic.ModelAndSerialNumberR\anumbers\x12*\n" +
	"\x03ids\x18\x06 \x01(\v2\x18.basic.MonitorAndUserIdsR\x03ids\x12:\n" +
	"\x19measurement_log_frequency\x18\a \x01(\rR\x17measurementLogFrequency\x12=\n" +
	"\x1bmeasurement_log_entry_count\x18\b \x01(\rR\x18measurementLogEntryCount\x124\n" +
	"\x16sequence_log_frequency\x18\t \x01(\rR\x14sequenceLogFrequency\x127\n" +
	"\x18sequence_log_entry_count\x18\n" +
	" \x01(\rR\x15sequenceLogEntryCount\x125\n" +
	"\n" +
	"fault_code\x18\v \x01(\x0e2\x16.mon_faults.EFaultCodeR\tfaultCode\x12)\n" +
	"\x10diagnostic_fault\x18\f \x01(\bR\x0fdiagnosticFault\x12E\n" +
	"\rfault_subcode\x18\r \x01(\v2 .mon_faults.MmuSubFaultTypeValueR\ffaultSubcode\x12H\n" +
	"\x0fdiagnostic_code\x18\x12 \x01(\x0e2\x1f.mon_faults.ESubFaultDiagnosticR\x0ediagnosticCode\x12%\n" +
	"\x0echannels_chmap\x18\x13 \x01(\aR\rchannelsChmap\x122\n" +
	"\x15red_indications_chmap\x18\x14 \x01(\aR\x13redIndicationsChmap\x128\n" +
	"\x18yellow_indications_chmap\x18\x15 \x01(\aR\x16yellowIndicationsChmap\x126\n" +
	"\x17green_indications_chmap\x18\x16 \x01(\aR\x15greenIndicationsChmap\x124\n" +
	"\x16walk_indications_chmap\x18\x17 \x01(\aR\x14walkIndicationsChmap\x12\"\n" +
	"\rreds_on_chmap\x18\x18 \x01(\aR\vredsOnChmap\x12(\n" +
	"\x10yellows_on_chmap\x18\x19 \x01(\aR\x0eyellowsOnChmap\x12&\n" +
	"\x0fgreens_on_chmap\x18\x1a \x01(\aR\rgreensOnChmap\x12\"\n" +
	"\rwalk_on_chmap\x18\x1b \x01(\aR\vwalkOnChmap\x12%\n" +
	"\x0eline_frequency\x18\x1c \x01(\x02R\rlineFrequency\x12)\n" +
	"\x10temperature_degf\x18\x1d \x01(\x02R\x0ftemperatureDegf\"a\n" +
	" FaultHeaderLogMultipleEntriesMmu\x12=\n" +
	"\tlog_entry\x18\x01 \x03(\v2 .mon_logs.FaultHeaderLogEntryMmuR\blogEntry\"\xc5\x11\n" +
	"\x1bFaultMeasurementLogEntryMmu\x12\x19\n" +
	"\bfault_id\x18\x01 \x01(\rR\afaultId\x12\x19\n" +
	"\bentry_id\x18\x02 \x01(\rR\aentryId\x12)\n" +
	"\x0emains_ac_volts\x18\x03 \x01(\x02H\x00R\fmainsAcVolts\x88\x01\x01\x12/\n" +
	"\x14reds_on_normal_chmap\x18\x04 \x01(\aR\x11redsOnNormalChmap\x125\n" +
	"\x17yellows_on_normal_chmap\x18\x05 \x01(\aR\x14yellowsOnNormalChmap\x123\n" +
	"\x16greens_on_normal_chmap\x18\x06 \x01(\aR\x13greensOnNormalChmap\x121\n" +
	"\x15walks_on_normal_chmap\x18\a \x01(\aR\x12walksOnNormalChmap\x12;\n" +
	"\x1areds_on_lackofsignal_chmap\x18\b \x01(\aR\x17redsOnLackofsignalChmap\x12A\n" +
	"\x1dyellows_on_lackofsignal_chmap\x18\t \x01(\aR\x1ayellowsOnLackofsignalChmap\x12?\n" +
	"\x1cgreens_on_lackofsignal_chmap\x18\n" +
	" \x01(\aR\x19greensOnLackofsignalChmap\x12=\n" +
	"\x1bwalks_on_lackofsignal_chmap\x18\v \x01(\aR\x18walksOnLackofsignalChmap\x121\n" +
	"\x15reds_on_current_chmap\x18\f \x01(\aR\x12redsOnCurrentChmap\x127\n" +
	"\x18yellows_on_current_chmap\x18\r \x01(\aR\x15yellowsOnCurrentChmap\x125\n" +
	"\x17greens_on_current_chmap\x18\x0e \x01(\aR\x14greensOnCurrentChmap\x123\n" +
	"\x16walks_on_current_chmap\x18\x0f \x01(\aR\x13walksOnCurrentChmap\x123\n" +
	"\x16reds_field_check_chmap\x18\x10 \x01(\aR\x13redsFieldCheckChmap\x129\n" +
	"\x19yellows_field_check_chmap\x18\x11 \x01(\aR\x16yellowsFieldCheckChmap\x127\n" +
	"\x18greens_field_check_chmap\x18\x12 \x01(\aR\x15greensFieldCheckChmap\x125\n" +
	"\x17walks_field_check_chmap\x18\x13 \x01(\aR\x14walksFieldCheckChmap\x12K\n" +
	"\fred_channels\x18\x14 \x03(\v2(.mon_faults.FaultIndicationChVoltCurrentR\vredChannels\x12Q\n" +
	"\x0fyellow_channels\x18\x15 \x03(\v2(.mon_faults.FaultIndicationChVoltCurrentR\x0eyellowChannels\x12O\n" +
	"\x0egreen_channels\x18\x16 \x03(\v2(.mon_faults.FaultIndicationChVoltCurrentR\rgreenChannels\x12M\n" +
	"\rwalk_channels\x18\x17 \x03(\v2(.mon_faults.FaultIndicationChVoltCurrentR\fwalkChannels\x12>\n" +
	"\x19inhibit_24v_monitor_volts\x18\x18 \x01(\x02H\x01R\x16inhibit24vMonitorVolts\x88\x01\x01\x122\n" +
	"\x13monitor_24v_1_volts\x18\x19 \x01(\x02H\x02R\x10monitor24v1Volts\x88\x01\x01\x122\n" +
	"\x13monitor_24v_2_volts\x18\x1a \x01(\x02H\x03R\x10monitor24v2Volts\x88\x01\x01\x12=\n" +
	"\x18controller_monitor_volts\x18\x1b \x01(\x02H\x04R\x16controllerMonitorVolts\x88\x01\x01\x12/\n" +
	"\x11type_select_volts\x18\x1c \x01(\x02H\x05R\x0ftypeSelectVolts\x88\x01\x01\x12/\n" +
	"\x11local_flash_volts\x18\x1d \x01(\x02H\x06R\x0flocalFlashVolts\x88\x01\x01\x125\n" +
	"\x14external_reset_volts\x18\x1e \x01(\x02H\aR\x12externalResetVolts\x88\x01\x01\x123\n" +
	"\x13port1_disable_volts\x18\x1f \x01(\x02H\bR\x11port1DisableVolts\x88\x01\x01\x12;\n" +
	"\x17external_watchdog_volts\x18  \x01(\x02H\tR\x15externalWatchdogVolts\x88\x01\x01\x12/\n" +
	"\x11red_enabled_volts\x18! \x01(\x02H\n" +
	"R\x0fredEnabledVolts\x88\x01\x01\x12M\n" +
	"\finput_states\x18\" \x01(\v2*.mon_faults.MmuMonitoredInputsStatusBitmapR\vinputStatesB\x11\n" +
	"\x0f_mains_ac_voltsB\x1c\n" +
	"\x1a_inhibit_24v_monitor_voltsB\x16\n" +
	"\x14_monitor_24v_1_voltsB\x16\n" +
	"\x14_monitor_24v_2_voltsB\x1b\n" +
	"\x19_controller_monitor_voltsB\x14\n" +
	"\x12_type_select_voltsB\x14\n" +
	"\x12_local_flash_voltsB\x17\n" +
	"\x15_external_reset_voltsB\x16\n" +
	"\x14_port1_disable_voltsB\x1a\n" +
	"\x18_external_watchdog_voltsB\x14\n" +
	"\x12_red_enabled_volts\"k\n" +
	"%FaultMeasurementLogMultipleEntriesMmu\x12B\n" +
	"\tlog_entry\x18\x01 \x03(\v2%.mon_logs.FaultMeasurementLogEntryMmuR\blogEntry\"\x9e\t\n" +
	"\x18FaultSequenceLogEntryMmu\x12\x19\n" +
	"\bfault_id\x18\x01 \x01(\rR\afaultId\x12\x19\n" +
	"\bentry_id\x18\x02 \x01(\rR\aentryId\x12!\n" +
	"\fperiod_count\x18\x03 \x01(\rR\vperiodCount\x12R\n" +
	"\x0econtrol_states\x18\x04 \x01(\v2+.mon_faults.MmuMonitoredControlStatesBitmapR\rcontrolStates\x12M\n" +
	"\finput_states\x18\x05 \x01(\v2*.mon_faults.MmuMonitoredInputsStatusBitmapR\vinputStates\x12/\n" +
	"\x14reds_on_normal_chmap\x18\x06 \x01(\aR\x11redsOnNormalChmap\x125\n" +
	"\x17yellows_on_normal_chmap\x18\a \x01(\aR\x14yellowsOnNormalChmap\x123\n" +
	"\x16greens_on_normal_chmap\x18\b \x01(\aR\x13greensOnNormalChmap\x121\n" +
	"\x15walks_on_normal_chmap\x18\t \x01(\aR\x12walksOnNormalChmap\x12;\n" +
	"\x1areds_on_lackofsignal_chmap\x18\n" +
	" \x01(\aR\x17redsOnLackofsignalChmap\x12A\n" +
	"\x1dyellows_on_lackofsignal_chmap\x18\v \x01(\aR\x1ayellowsOnLackofsignalChmap\x12?\n" +
	"\x1cgreens_on_lackofsignal_chmap\x18\f \x01(\aR\x19greensOnLackofsignalChmap\x12=\n" +
	"\x1bwalks_on_lackofsignal_chmap\x18\r \x01(\aR\x18walksOnLackofsignalChmap\x121\n" +
	"\x15reds_on_current_chmap\x18\x0e \x01(\aR\x12redsOnCurrentChmap\x127\n" +
	"\x18yellows_on_current_chmap\x18\x0f \x01(\aR\x15yellowsOnCurrentChmap\x125\n" +
	"\x17greens_on_current_chmap\x18\x10 \x01(\aR\x14greensOnCurrentChmap\x123\n" +
	"\x16walks_on_current_chmap\x18\x11 \x01(\aR\x13walksOnCurrentChmap\x123\n" +
	"\x16reds_field_check_chmap\x18\x12 \x01(\aR\x13redsFieldCheckChmap\x129\n" +
	"\x19yellows_field_check_chmap\x18\x13 \x01(\aR\x16yellowsFieldCheckChmap\x127\n" +
	"\x18greens_field_check_chmap\x18\x14 \x01(\aR\x15greensFieldCheckChmap\x125\n" +
	"\x17walks_field_check_chmap\x18\x15 \x01(\aR\x14walksFieldCheckChmap\"e\n" +
	"\"FaultSequenceLogMultipleEntriesMmu\x12?\n" +
	"\tlog_entry\x18\x01 \x03(\v2\".mon_logs.FaultSequenceLogEntryMmuR\blogEntry\"\xb8\x02\n" +
	"\x12FaultFactsLogEntry\x12\x19\n" +
	"\bfault_id\x18\x01 \x01(\rR\afaultId\x12\x19\n" +
	"\bentry_id\x18\x02 \x01(\rR\aentryId\x120\n" +
	"\bdatetime\x18\x03 \x01(\v2\x14.basic.LocalDateTimeR\bdatetime\x12$\n" +
	"\x0esamples_16b_cv\x18\x04 \x01(\fR\fsamples16bCv\x12&\n" +
	"\x0fcapture_time_ms\x18\x05 \x01(\rR\rcaptureTimeMs\x12'\n" +
	"\x0fcapture_channel\x18\x06 \x01(\rR\x0ecaptureChannel\x12C\n" +
	"\rchannel_input\x18\a \x01(\x0e2\x1e.mon_logs.ECaptureChannelInputR\fchannelInput\"\\\n" +
	"\x1fFaultFactsLogMultipleEntriesMmu\x129\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x1c.mon_logs.FaultFactsLogEntryR\blogEntry\"\xd6\x01\n" +
	"\rAlarmLogEntry\x12\x19\n" +
	"\bentry_id\x18\x01 \x01(\rR\aentryId\x124\n" +
	"\bseverity\x18\x02 \x01(\x0e2\x18.mon_logs.EAlarmSeverityR\bseverity\x12.\n" +
	"\x06source\x18\x03 \x01(\x0e2\x16.mon_logs.EAlarmSourceR\x06source\x120\n" +
	"\bdatetime\x18\x04 \x01(\v2\x14.basic.LocalDateTimeR\bdatetime\x12\x12\n" +
	"\x04text\x18\x05 \x01(\tR\x04text\"R\n" +
	"\x1aAlarmLogMultipleEntriesMmu\x124\n" +
	"\tlog_entry\x18\x01 \x03(\v2\x17.mon_logs.AlarmLogEntryR\blogEntry\"\xa4\x01\n" +
	"\rLogEntryCount\x12+\n" +
	"\x03log\x18\x01 \x01(\x0e2\x19.mon_logs.EMonitorLogTypeR\x03log\x12$\n" +
	"\x0estart_entry_id\x18\x02 \x01(\rR\fstartEntryId\x12\x18\n" +
	"\aentries\x18\x03 \x01(\rR\aentries\x12\x1b\n" +
	"\x06format\x18\x04 \x01(\rH\x00R\x06format\x88\x01\x01B\t\n" +
	"\a_format\"J\n" +
	"\x16DataKeyErrorCodeBitmap\x12\x17\n" +
	"\aerror_1\x18\x01 \x01(\bR\x06error1\x12\x17\n" +
	"\aerror_2\x18\x02 \x01(\bR\x06error2*\xba\x05\n" +
	"\x12EPowerLogEventType\x12\x1d\n" +
	"\x19PWR_LOG_EVENT_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16PWR_LOG_EVENT_POWER_UP\x10\x01\x12\x1c\n" +
	"\x18PWR_LOG_EVENT_POWER_DOWN\x10\x02\x12\x1f\n" +
	"\x1bPWR_LOG_EVENT_CONTROLLER_UP\x10\x03\x12!\n" +
	"\x1dPWR_LOG_EVENT_CONTROLLER_DOWN\x10\x04\x12\x1d\n" +
	"\x19PWR_LOG_EVENT_LOW_VOLTAGE\x10\x05\x12\x1e\n" +
	"\x1aPWR_LOG_EVENT_HIGH_VOLTAGE\x10\x06\x12#\n" +
	"\x1fPWR_LOG_EVENT_POWER_INTERRUPTED\x10\a\x12$\n" +
	" PWR_LOG_EVENT_POWER_DOWN_TIMEOUT\x10\b\x12 \n" +
	"\x1cPWR_LOG_EVENT_NRESET_TIMEOUT\x10\t\x12!\n" +
	"\x1dPWR_LOG_EVENT_NRESET_RECOVERY\x10\n" +
	"\x12\x1e\n" +
	"\x1aPWR_LOG_EVENT_HDSP_TIMEOUT\x10\v\x12\x1f\n" +
	"\x1bPWR_LOG_EVENT_FREQUENCY_LOW\x10\f\x12 \n" +
	"\x1cPWR_LOG_EVENT_FREQUENCY_HIGH\x10\r\x12\x17\n" +
	"\x13PWR_LOG_EVENT_TIMED\x10\x0e\x12&\n" +
	"\"PWR_LOG_EVENT_LOW_VOLTAGE_RECOVERY\x10\x0f\x12'\n" +
	"#PWR_LOG_EVENT_HIGH_VOLTAGE_RECOVERY\x10\x10\x12(\n" +
	"$PWR_LOG_EVENT_FREQUENCY_LOW_RECOVERY\x10\x11\x12)\n" +
	"%PWR_LOG_EVENT_FREQUENCY_HIGH_RECOVERY\x10\x12\x12\x16\n" +
	"\x12PWR_LOG_EVENT_TEST\x10\x13*\x9f\x03\n" +
	"\x14EPowerLogEventTiming\x12\x1e\n" +
	"\x1aPWR_LOG_PERIOD_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dPWR_LOG_PERIOD_NO_TIMED_EVENT\x10\x01\x12\x1d\n" +
	"\x19PWR_LOG_PERIOD_EVERY_1_HR\x10\x02\x12\x1e\n" +
	"\x1aPWR_LOG_PERIOD_EVERY_2_HRS\x10\x03\x12\x1e\n" +
	"\x1aPWR_LOG_PERIOD_EVERY_4_HRS\x10\x04\x12\x1e\n" +
	"\x1aPWR_LOG_PERIOD_EVERY_8_HRS\x10\x05\x12\x1f\n" +
	"\x1bPWR_LOG_PERIOD_EVERY_12_HRS\x10\x06\x12\x1e\n" +
	"\x1aPWR_LOG_PERIOD_EVERY_1_DAY\x10\a\x12\x1f\n" +
	"\x1bPWR_LOG_PERIOD_EVERY_2_DAYS\x10\b\x12\x1f\n" +
	"\x1bPWR_LOG_PERIOD_EVERY_7_DAYS\x10\t\x12 \n" +
	"\x1cPWR_LOG_PERIOD_EVERY_14_DAYS\x10\n" +
	"\x12 \n" +
	"\x1cPWR_LOG_PERIOD_EVERY_1_MONTH\x10\v*\x9d\x02\n" +
	"\x0fEResetLogSource\x12\x1e\n" +
	"\x1aRST_LOG_SOURCE_UNSPECIFIED\x10\x00\x12\x1e\n" +
	"\x1aRST_LOG_SOURCE_FRONT_PANEL\x10\x01\x12\x1b\n" +
	"\x17RST_LOG_SOURCE_EXTERNAL\x10\x02\x12\x1e\n" +
	"\x1aRST_LOG_SOURCE_NON_LATCHED\x10\x03\x12\x1e\n" +
	"\x1aRST_LOG_SOURCE_POWER_CYCLE\x10\x04\x12\x16\n" +
	"\x12RST_LOG_SOURCE_ADU\x10\x05\x12!\n" +
	"\x1dRST_LOG_SOURCE_CONFIG_CHANGED\x10\x06\x12\x19\n" +
	"\x15RST_LOG_SOURCE_REMOTE\x10\a\x12\x17\n" +
	"\x13RST_LOG_SOURCE_TEST\x10\b*\xb4\x01\n" +
	"\x0fEClockLogSource\x12 \n" +
	"\x1cCLOCK_LOG_SOURCE_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bCLOCK_LOG_SOURCE_CONTROLLER\x10\x01\x12\x1d\n" +
	"\x19CLOCK_LOG_SOURCE_ETHERNET\x10\x02\x12\x1e\n" +
	"\x1aCLOCK_LOG_SOURCE_BLUETOOTH\x10\x03\x12\x1f\n" +
	"\x1bCLOCK_LOG_SOURCE_FRONTPANEL\x10\x04*\xf3\x04\n" +
	"\x14ECaptureChannelInput\x12\x18\n" +
	"\x14CH_INPUT_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14CH_INPUT_RED_VOLTAGE\x10\x01\x12\x1b\n" +
	"\x17CH_INPUT_YELLOW_VOLTAGE\x10\x02\x12\x1a\n" +
	"\x16CH_INPUT_GREEN_VOLTAGE\x10\x03\x12\x19\n" +
	"\x15CH_INPUT_WALK_VOLTAGE\x10\x04\x12\x18\n" +
	"\x14CH_INPUT_RED_CURRENT\x10\x05\x12\x1b\n" +
	"\x17CH_INPUT_YELLOW_CURRENT\x10\x06\x12\x1a\n" +
	"\x16CH_INPUT_GREEN_CURRENT\x10\a\x12\x19\n" +
	"\x15CH_INPUT_WALK_CURRENT\x10\b\x12\x1a\n" +
	"\x16CH_INPUT_AC_LINE_VOLTS\x10\t\x12\x18\n" +
	"\x14CH_INPUT_24V_1_VOLTS\x10\n" +
	"\x12\x18\n" +
	"\x14CH_INPUT_24V_2_VOLTS\x10\v\x12\x16\n" +
	"\x12CH_INPUT_CVM_VOLTS\x10\f\x12\x1b\n" +
	"\x17CH_INPUT_LINE_FREQUENCY\x10\r\x12\x1d\n" +
	"\x19CH_INPUT_RED_ENABLE_VOLTS\x10\x0e\x12$\n" +
	" CH_INPUT_EXTERNAL_WATCHDOG_VOLTS\x10\x0f\x12\x1e\n" +
	"\x1aCH_INPUT_TYPE_SELECT_VOLTS\x10\x10\x12\x1f\n" +
	"\x1bCH_INPUT_SDLC_DISABLE_VOLTS\x10\x11\x12&\n" +
	"\"CH_INPUT_24V_MONITOR_INHIBIT_VOLTS\x10\x12\x12\x1e\n" +
	"\x1aCH_INPUT_LOCAL_FLASH_VOLTS\x10\x13\x12\x12\n" +
	"\rCH_INPUT_TEST\x10\xfe\x01*\x95\x01\n" +
	"\x0eEAlarmSeverity\x12\x1b\n" +
	"\x17ALARM_SEVERITY_CRITICAL\x10\x00\x12\x18\n" +
	"\x14ALARM_SEVERITY_MAJOR\x10\x01\x12\x18\n" +
	"\x14ALARM_SEVERITY_MINOR\x10\x02\x12\x19\n" +
	"\x15ALARM_SEVERITY_INFORM\x10\x03\x12\x17\n" +
	"\x13ALARM_SEVERITY_TEST\x10\x0f*\xa7\x02\n" +
	"\fEAlarmSource\x12\"\n" +
	"\x1eALARM_SOURCE_MONITOR_OPERATION\x10\x00\x12!\n" +
	"\x1dALARM_SOURCE_MONITOR_HARDWARE\x10\x01\x12'\n" +
	"#ALARM_SOURCE_OTHER_CABINET_HARDWARE\x10\x02\x12 \n" +
	"\x1cALARM_SOURCE_EXTERNAL_SOURCE\x10\x03\x12'\n" +
	"#ALARM_SOURCE_INTERNAL_COMMUNICATION\x10\x04\x12'\n" +
	"#ALARM_SOURCE_EXTERNAL_COMMUNICATION\x10\x05\x12\x15\n" +
	"\x11ALARM_SOURCE_TEST\x10\x0e\x12\x1c\n" +
	"\x18ALARM_SOURCE_UNSPECIFIED\x10\x0f*\xa3\x02\n" +
	"\x0fEMonitorLogType\x12\x17\n" +
	"\x13MON_LOG_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vMON_LOG_ALL\x10\x01\x12\x11\n" +
	"\rMON_LOG_POWER\x10\x02\x12\x11\n" +
	"\rMON_LOG_RESET\x10\x03\x12\x11\n" +
	"\rMON_LOG_CLOCK\x10\x04\x12\x19\n" +
	"\x15MON_LOG_CONFIGURATION\x10\x05\x12\x11\n" +
	"\rMON_LOG_PORT1\x10\x06\x12\x18\n" +
	"\x14MON_LOG_FAULT_HEADER\x10\a\x12\x1d\n" +
	"\x19MON_LOG_FAULT_MEASUREMENT\x10\b\x12\x1a\n" +
	"\x16MON_LOG_FAULT_SEQUENCE\x10\t\x12\x17\n" +
	"\x13MON_LOG_FAULT_FACTS\x10\n" +
	"\x12\x11\n" +
	"\rMON_LOG_ALARM\x10\vb\x06proto3"

var (
	file_mon_logs_proto_rawDescOnce sync.Once
	file_mon_logs_proto_rawDescData []byte
)

func file_mon_logs_proto_rawDescGZIP() []byte {
	file_mon_logs_proto_rawDescOnce.Do(func() {
		file_mon_logs_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_mon_logs_proto_rawDesc), len(file_mon_logs_proto_rawDesc)))
	})
	return file_mon_logs_proto_rawDescData
}

var file_mon_logs_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_mon_logs_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_mon_logs_proto_goTypes = []any{
	(EPowerLogEventType)(0),                            // 0: mon_logs.EPowerLogEventType
	(EPowerLogEventTiming)(0),                          // 1: mon_logs.EPowerLogEventTiming
	(EResetLogSource)(0),                               // 2: mon_logs.EResetLogSource
	(EClockLogSource)(0),                               // 3: mon_logs.EClockLogSource
	(ECaptureChannelInput)(0),                          // 4: mon_logs.ECaptureChannelInput
	(EAlarmSeverity)(0),                                // 5: mon_logs.EAlarmSeverity
	(EAlarmSource)(0),                                  // 6: mon_logs.EAlarmSource
	(EMonitorLogType)(0),                               // 7: mon_logs.EMonitorLogType
	(*MmuPowerMonitors)(nil),                           // 8: mon_logs.MmuPowerMonitors
	(*PowerLogEntryMmu)(nil),                           // 9: mon_logs.PowerLogEntryMmu
	(*PowerLogMultipleEntriesMmu)(nil),                 // 10: mon_logs.PowerLogMultipleEntriesMmu
	(*ResetLogEntryMmu)(nil),                           // 11: mon_logs.ResetLogEntryMmu
	(*ResetLogMultipleEntriesMmu)(nil),                 // 12: mon_logs.ResetLogMultipleEntriesMmu
	(*ClockLogEntry)(nil),                              // 13: mon_logs.ClockLogEntry
	(*ClockLogMultipleEntriesMmu)(nil),                 // 14: mon_logs.ClockLogMultipleEntriesMmu
	(*HardwareRevisionsMmu)(nil),                       // 15: mon_logs.HardwareRevisionsMmu
	(*ConfigLogEntryMmu)(nil),                          // 16: mon_logs.ConfigLogEntryMmu
	(*ConfigLogMultipleEntriesMmu)(nil),                // 17: mon_logs.ConfigLogMultipleEntriesMmu
	(*Port1LogEntryMmu)(nil),                           // 18: mon_logs.Port1LogEntryMmu
	(*Port1LogLogMultipleEntriesMmu)(nil),              // 19: mon_logs.Port1LogLogMultipleEntriesMmu
	(*FaultHeaderLogEntryMmu)(nil),                     // 20: mon_logs.FaultHeaderLogEntryMmu
	(*FaultHeaderLogMultipleEntriesMmu)(nil),           // 21: mon_logs.FaultHeaderLogMultipleEntriesMmu
	(*FaultMeasurementLogEntryMmu)(nil),                // 22: mon_logs.FaultMeasurementLogEntryMmu
	(*FaultMeasurementLogMultipleEntriesMmu)(nil),      // 23: mon_logs.FaultMeasurementLogMultipleEntriesMmu
	(*FaultSequenceLogEntryMmu)(nil),                   // 24: mon_logs.FaultSequenceLogEntryMmu
	(*FaultSequenceLogMultipleEntriesMmu)(nil),         // 25: mon_logs.FaultSequenceLogMultipleEntriesMmu
	(*FaultFactsLogEntry)(nil),                         // 26: mon_logs.FaultFactsLogEntry
	(*FaultFactsLogMultipleEntriesMmu)(nil),            // 27: mon_logs.FaultFactsLogMultipleEntriesMmu
	(*AlarmLogEntry)(nil),                              // 28: mon_logs.AlarmLogEntry
	(*AlarmLogMultipleEntriesMmu)(nil),                 // 29: mon_logs.AlarmLogMultipleEntriesMmu
	(*LogEntryCount)(nil),                              // 30: mon_logs.LogEntryCount
	(*DataKeyErrorCodeBitmap)(nil),                     // 31: mon_logs.DataKeyErrorCodeBitmap
	(*basic.NowMinMaxFloat)(nil),                       // 32: basic.NowMinMaxFloat
	(*basic.LocalDateTime)(nil),                        // 33: basic.LocalDateTime
	(mon_faults.EFaultCode)(0),                         // 34: mon_faults.EFaultCode
	(*settings.FactoryOptionsMmu)(nil),                 // 35: settings.FactoryOptionsMmu
	(*settings.PcbOptionsMmu)(nil),                     // 36: settings.PcbOptionsMmu
	(*settings.AgencyOptionsMmu)(nil),                  // 37: settings.AgencyOptionsMmu
	(*basic.ModelAndSerialNumber)(nil),                 // 38: basic.ModelAndSerialNumber
	(*basic.VersionStrThree)(nil),                      // 39: basic.VersionStrThree
	(*basic.DateStr)(nil),                              // 40: basic.DateStr
	(settings.EConfigDataLocation)(0),                  // 41: settings.EConfigDataLocation
	(*basic.MonitorAndUserIds)(nil),                    // 42: basic.MonitorAndUserIds
	(*mon_faults.MmuSubFaultTypeValue)(nil),            // 43: mon_faults.MmuSubFaultTypeValue
	(mon_faults.ESubFaultDiagnostic)(0),                // 44: mon_faults.ESubFaultDiagnostic
	(*mon_faults.FaultIndicationChVoltCurrent)(nil),    // 45: mon_faults.FaultIndicationChVoltCurrent
	(*mon_faults.MmuMonitoredInputsStatusBitmap)(nil),  // 46: mon_faults.MmuMonitoredInputsStatusBitmap
	(*mon_faults.MmuMonitoredControlStatesBitmap)(nil), // 47: mon_faults.MmuMonitoredControlStatesBitmap
}
var file_mon_logs_proto_depIdxs = []int32{
	32, // 0: mon_logs.MmuPowerMonitors.ac_mains_volts:type_name -> basic.NowMinMaxFloat
	32, // 1: mon_logs.MmuPowerMonitors.dc_24_volts_mon1:type_name -> basic.NowMinMaxFloat
	32, // 2: mon_logs.MmuPowerMonitors.dc_24_volts_mon2:type_name -> basic.NowMinMaxFloat
	32, // 3: mon_logs.MmuPowerMonitors.cvm_volt:type_name -> basic.NowMinMaxFloat
	32, // 4: mon_logs.MmuPowerMonitors.line_frequency_hz:type_name -> basic.NowMinMaxFloat
	0,  // 5: mon_logs.PowerLogEntryMmu.event_type:type_name -> mon_logs.EPowerLogEventType
	33, // 6: mon_logs.PowerLogEntryMmu.entry_timestamp:type_name -> basic.LocalDateTime
	8,  // 7: mon_logs.PowerLogEntryMmu.power_voltages:type_name -> mon_logs.MmuPowerMonitors
	32, // 8: mon_logs.PowerLogEntryMmu.temperature_degf:type_name -> basic.NowMinMaxFloat
	1,  // 9: mon_logs.PowerLogEntryMmu.event_timing:type_name -> mon_logs.EPowerLogEventTiming
	9,  // 10: mon_logs.PowerLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.PowerLogEntryMmu
	2,  // 11: mon_logs.ResetLogEntryMmu.reset_source:type_name -> mon_logs.EResetLogSource
	33, // 12: mon_logs.ResetLogEntryMmu.entry_timestamp:type_name -> basic.LocalDateTime
	34, // 13: mon_logs.ResetLogEntryMmu.fault_cleared:type_name -> mon_faults.EFaultCode
	11, // 14: mon_logs.ResetLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.ResetLogEntryMmu
	3,  // 15: mon_logs.ClockLogEntry.clock_source:type_name -> mon_logs.EClockLogSource
	33, // 16: mon_logs.ClockLogEntry.previous_datetime:type_name -> basic.LocalDateTime
	33, // 17: mon_logs.ClockLogEntry.new_datetime:type_name -> basic.LocalDateTime
	13, // 18: mon_logs.ClockLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.ClockLogEntry
	15, // 19: mon_logs.ConfigLogEntryMmu.hardware:type_name -> mon_logs.HardwareRevisionsMmu
	35, // 20: mon_logs.ConfigLogEntryMmu.factory_options:type_name -> settings.FactoryOptionsMmu
	36, // 21: mon_logs.ConfigLogEntryMmu.pcb_options:type_name -> settings.PcbOptionsMmu
	37, // 22: mon_logs.ConfigLogEntryMmu.agency_options:type_name -> settings.AgencyOptionsMmu
	33, // 23: mon_logs.ConfigLogEntryMmu.entry_timestamp:type_name -> basic.LocalDateTime
	38, // 24: mon_logs.ConfigLogEntryMmu.numbers:type_name -> basic.ModelAndSerialNumber
	39, // 25: mon_logs.ConfigLogEntryMmu.main_mcu_fw_version:type_name -> basic.VersionStrThree
	40, // 26: mon_logs.ConfigLogEntryMmu.main_mcu_fw_date:type_name -> basic.DateStr
	39, // 27: mon_logs.ConfigLogEntryMmu.isolated_mcu_fw_version:type_name -> basic.VersionStrThree
	40, // 28: mon_logs.ConfigLogEntryMmu.isolated_mcu_fw_date:type_name -> basic.DateStr
	39, // 29: mon_logs.ConfigLogEntryMmu.disp_mcu_fw_version:type_name -> basic.VersionStrThree
	40, // 30: mon_logs.ConfigLogEntryMmu.disp_mcu_fw_date:type_name -> basic.DateStr
	39, // 31: mon_logs.ConfigLogEntryMmu.comms_mcu_fw_version:type_name -> basic.VersionStrThree
	40, // 32: mon_logs.ConfigLogEntryMmu.comms_mcu_fw_date:type_name -> basic.DateStr
	39, // 33: mon_logs.ConfigLogEntryMmu.ble_mcu_fw_version:type_name -> basic.VersionStrThree
	40, // 34: mon_logs.ConfigLogEntryMmu.ble_mcu_fw_date:type_name -> basic.DateStr
	39, // 35: mon_logs.ConfigLogEntryMmu.pkg_mcu_fw_version:type_name -> basic.VersionStrThree
	40, // 36: mon_logs.ConfigLogEntryMmu.pkg_mcu_fw_date:type_name -> basic.DateStr
	41, // 37: mon_logs.ConfigLogEntryMmu.data_src:type_name -> settings.EConfigDataLocation
	16, // 38: mon_logs.ConfigLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.ConfigLogEntryMmu
	33, // 39: mon_logs.Port1LogEntryMmu.entry_timestamp:type_name -> basic.LocalDateTime
	33, // 40: mon_logs.Port1LogEntryMmu.timestamp_when_cleared:type_name -> basic.LocalDateTime
	18, // 41: mon_logs.Port1LogLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.Port1LogEntryMmu
	33, // 42: mon_logs.FaultHeaderLogEntryMmu.entry_timestamp:type_name -> basic.LocalDateTime
	38, // 43: mon_logs.FaultHeaderLogEntryMmu.numbers:type_name -> basic.ModelAndSerialNumber
	42, // 44: mon_logs.FaultHeaderLogEntryMmu.ids:type_name -> basic.MonitorAndUserIds
	34, // 45: mon_logs.FaultHeaderLogEntryMmu.fault_code:type_name -> mon_faults.EFaultCode
	43, // 46: mon_logs.FaultHeaderLogEntryMmu.fault_subcode:type_name -> mon_faults.MmuSubFaultTypeValue
	44, // 47: mon_logs.FaultHeaderLogEntryMmu.diagnostic_code:type_name -> mon_faults.ESubFaultDiagnostic
	20, // 48: mon_logs.FaultHeaderLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.FaultHeaderLogEntryMmu
	45, // 49: mon_logs.FaultMeasurementLogEntryMmu.red_channels:type_name -> mon_faults.FaultIndicationChVoltCurrent
	45, // 50: mon_logs.FaultMeasurementLogEntryMmu.yellow_channels:type_name -> mon_faults.FaultIndicationChVoltCurrent
	45, // 51: mon_logs.FaultMeasurementLogEntryMmu.green_channels:type_name -> mon_faults.FaultIndicationChVoltCurrent
	45, // 52: mon_logs.FaultMeasurementLogEntryMmu.walk_channels:type_name -> mon_faults.FaultIndicationChVoltCurrent
	46, // 53: mon_logs.FaultMeasurementLogEntryMmu.input_states:type_name -> mon_faults.MmuMonitoredInputsStatusBitmap
	22, // 54: mon_logs.FaultMeasurementLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.FaultMeasurementLogEntryMmu
	47, // 55: mon_logs.FaultSequenceLogEntryMmu.control_states:type_name -> mon_faults.MmuMonitoredControlStatesBitmap
	46, // 56: mon_logs.FaultSequenceLogEntryMmu.input_states:type_name -> mon_faults.MmuMonitoredInputsStatusBitmap
	24, // 57: mon_logs.FaultSequenceLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.FaultSequenceLogEntryMmu
	33, // 58: mon_logs.FaultFactsLogEntry.datetime:type_name -> basic.LocalDateTime
	4,  // 59: mon_logs.FaultFactsLogEntry.channel_input:type_name -> mon_logs.ECaptureChannelInput
	26, // 60: mon_logs.FaultFactsLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.FaultFactsLogEntry
	5,  // 61: mon_logs.AlarmLogEntry.severity:type_name -> mon_logs.EAlarmSeverity
	6,  // 62: mon_logs.AlarmLogEntry.source:type_name -> mon_logs.EAlarmSource
	33, // 63: mon_logs.AlarmLogEntry.datetime:type_name -> basic.LocalDateTime
	28, // 64: mon_logs.AlarmLogMultipleEntriesMmu.log_entry:type_name -> mon_logs.AlarmLogEntry
	7,  // 65: mon_logs.LogEntryCount.log:type_name -> mon_logs.EMonitorLogType
	66, // [66:66] is the sub-list for method output_type
	66, // [66:66] is the sub-list for method input_type
	66, // [66:66] is the sub-list for extension type_name
	66, // [66:66] is the sub-list for extension extendee
	0,  // [0:66] is the sub-list for field type_name
}

func init() { file_mon_logs_proto_init() }
func file_mon_logs_proto_init() {
	if File_mon_logs_proto != nil {
		return
	}
	file_mon_logs_proto_msgTypes[1].OneofWrappers = []any{}
	file_mon_logs_proto_msgTypes[14].OneofWrappers = []any{}
	file_mon_logs_proto_msgTypes[22].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_mon_logs_proto_rawDesc), len(file_mon_logs_proto_rawDesc)),
			NumEnums:      8,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mon_logs_proto_goTypes,
		DependencyIndexes: file_mon_logs_proto_depIdxs,
		EnumInfos:         file_mon_logs_proto_enumTypes,
		MessageInfos:      file_mon_logs_proto_msgTypes,
	}.Build()
	File_mon_logs_proto = out.File
	file_mon_logs_proto_goTypes = nil
	file_mon_logs_proto_depIdxs = nil
}
