//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  WRAPPERS
//Wrapper messages for command messages sent by the app to the monitor unit, and response messages sent
//by the monitor unit back to the app.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: wrappers.proto

package wrappers

import (
	cmd_resp_comms "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_comms"
	cmd_resp_config "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_config"
	cmd_resp_dfu "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_dfu"
	cmd_resp_logs "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_logs"
	cmd_resp_realtime "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_realtime"
	cmd_resp_stats "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/cmd_resp_stats"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EResponseCodes provide error conditions .
type EResponseCodes int32

const (
	EResponseCodes_RESP_UNSPECIFIED       EResponseCodes = 0
	EResponseCodes_RESP_SUCCESS           EResponseCodes = 1  // command succeeded
	EResponseCodes_RESP_PROHIBITED        EResponseCodes = 2  // Authentication permissions do not allow this command
	EResponseCodes_RESP_FAILED            EResponseCodes = 3  // command execution failed (eg, getting data from the Main MCU)
	EResponseCodes_RESP_CMD_UNKNOWN       EResponseCodes = 4  // command message type is unknown (or version is 0)
	EResponseCodes_RESP_BAD_VALUE         EResponseCodes = 5  // a command parameter value is missing or out of range
	EResponseCodes_RESP_BAD_CHANNEL       EResponseCodes = 6  // a command is requesting a non-existant channel
	EResponseCodes_RESP_CMD_ERROR         EResponseCodes = 7  // an error occurred while processing the command
	EResponseCodes_RESP_OPTION_MISMATCH   EResponseCodes = 8  // PCB Option jumpers or other options do not match
	EResponseCodes_RESP_UNAVAILABLE       EResponseCodes = 9  // The resource or destination is unavailable (i.e. Attempt to write data key, but no data key)
	EResponseCodes_RESP_BUSY              EResponseCodes = 10 // The resource is busy and unabled to handle the command (but may be available later)
	EResponseCodes_RESP_TIMEOUT           EResponseCodes = 11 // A timeout has occurred during or before command execution
	EResponseCodes_RESP_SEQUENCE_ERR      EResponseCodes = 12 // Command data was received out of sequence
	EResponseCodes_RESP_NOT_WRITABLE      EResponseCodes = 13 // The device does not permit writing the command data
	EResponseCodes_RESP_DEVICE_NACK       EResponseCodes = 14 // A connecteddevice NACKed the command (bad checksum, internal read/write failed, etc)
	EResponseCodes_RESP_FILE_SYSTEM_ERROR EResponseCodes = 15 // An internal file system error occurred (eg, file i/o failed)
)

// Enum value maps for EResponseCodes.
var (
	EResponseCodes_name = map[int32]string{
		0:  "RESP_UNSPECIFIED",
		1:  "RESP_SUCCESS",
		2:  "RESP_PROHIBITED",
		3:  "RESP_FAILED",
		4:  "RESP_CMD_UNKNOWN",
		5:  "RESP_BAD_VALUE",
		6:  "RESP_BAD_CHANNEL",
		7:  "RESP_CMD_ERROR",
		8:  "RESP_OPTION_MISMATCH",
		9:  "RESP_UNAVAILABLE",
		10: "RESP_BUSY",
		11: "RESP_TIMEOUT",
		12: "RESP_SEQUENCE_ERR",
		13: "RESP_NOT_WRITABLE",
		14: "RESP_DEVICE_NACK",
		15: "RESP_FILE_SYSTEM_ERROR",
	}
	EResponseCodes_value = map[string]int32{
		"RESP_UNSPECIFIED":       0,
		"RESP_SUCCESS":           1,
		"RESP_PROHIBITED":        2,
		"RESP_FAILED":            3,
		"RESP_CMD_UNKNOWN":       4,
		"RESP_BAD_VALUE":         5,
		"RESP_BAD_CHANNEL":       6,
		"RESP_CMD_ERROR":         7,
		"RESP_OPTION_MISMATCH":   8,
		"RESP_UNAVAILABLE":       9,
		"RESP_BUSY":              10,
		"RESP_TIMEOUT":           11,
		"RESP_SEQUENCE_ERR":      12,
		"RESP_NOT_WRITABLE":      13,
		"RESP_DEVICE_NACK":       14,
		"RESP_FILE_SYSTEM_ERROR": 15,
	}
)

func (x EResponseCodes) Enum() *EResponseCodes {
	p := new(EResponseCodes)
	*p = x
	return p
}

func (x EResponseCodes) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EResponseCodes) Descriptor() protoreflect.EnumDescriptor {
	return file_wrappers_proto_enumTypes[0].Descriptor()
}

func (EResponseCodes) Type() protoreflect.EnumType {
	return &file_wrappers_proto_enumTypes[0]
}

func (x EResponseCodes) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EResponseCodes.Descriptor instead.
func (EResponseCodes) EnumDescriptor() ([]byte, []int) {
	return file_wrappers_proto_rawDescGZIP(), []int{0}
}

//	WrapperCommand is the wrapper for all websocket command messages from the application to the
//
// monitor unit.  (Excluding authentication messages over raw TCP)
type WrapperCommand struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The command interface version
	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	// This value is assigned by the app and is echoed back in a response, to match responses
	// to commands.
	// Some log read commands may generate multiple responses, and all the responses will have the
	// same request_id value.
	RequestId uint32 `protobuf:"varint,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// The command with its parameters.  See the command message definition for details on the command.
	//
	// Types that are valid to be assigned to Command:
	//
	//	*WrapperCommand_RequestLogCounts
	//	*WrapperCommand_LogClear
	//	*WrapperCommand_RequestLog
	//	*WrapperCommand_ReqAuditlogCounts
	//	*WrapperCommand_AuditlogClear
	//	*WrapperCommand_AuditLogReset
	//	*WrapperCommand_RequestAuditlog
	//	*WrapperCommand_RdMonitorData
	//	*WrapperCommand_RdNetworkConfigUnit
	//	*WrapperCommand_RdNetworkConfigActive
	//	*WrapperCommand_RdChannelConfig
	//	*WrapperCommand_RdChannelCurrentSense
	//	*WrapperCommand_RdChannelPermissives
	//	*WrapperCommand_RdFyaConfig
	//	*WrapperCommand_RdDataKey
	//	*WrapperCommand_WrDataKey
	//	*WrapperCommand_RdFactorySettings
	//	*WrapperCommand_WrFactorySettings
	//	*WrapperCommand_RdUserSettings
	//	*WrapperCommand_WrUserSettings
	//	*WrapperCommand_RdPort1Disables
	//	*WrapperCommand_WrPort1Disables
	//	*WrapperCommand_WrAgencyOptions
	//	*WrapperCommand_RemoteReset
	//	*WrapperCommand_RdPort1Stats
	//	*WrapperCommand_RdDataKeyStats
	//	*WrapperCommand_RdMainIsoCommsStats
	//	*WrapperCommand_RdMainCommsStats
	//	*WrapperCommand_RdCommsMainStats
	//	*WrapperCommand_RdFlashStats
	//	*WrapperCommand_RdWatchdogStats
	//	*WrapperCommand_RdDateTimeDst
	//	*WrapperCommand_ClearStats
	//	*WrapperCommand_WrDateTimeDst
	//	*WrapperCommand_RemoteDisplayButtonEvent
	//	*WrapperCommand_StartRealtime
	//	*WrapperCommand_StopRealtime
	//	*WrapperCommand_ManifestVersions
	//	*WrapperCommand_RebootCommsMcu
	//	*WrapperCommand_InitiateDfu
	//	*WrapperCommand_SendFwManifest
	//	*WrapperCommand_BeginFirmwareDownload
	//	*WrapperCommand_DownloadFirmwareChunk
	//	*WrapperCommand_TestChunk
	Command       isWrapperCommand_Command `protobuf_oneof:"command"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WrapperCommand) Reset() {
	*x = WrapperCommand{}
	mi := &file_wrappers_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WrapperCommand) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WrapperCommand) ProtoMessage() {}

func (x *WrapperCommand) ProtoReflect() protoreflect.Message {
	mi := &file_wrappers_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WrapperCommand.ProtoReflect.Descriptor instead.
func (*WrapperCommand) Descriptor() ([]byte, []int) {
	return file_wrappers_proto_rawDescGZIP(), []int{0}
}

func (x *WrapperCommand) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *WrapperCommand) GetRequestId() uint32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *WrapperCommand) GetCommand() isWrapperCommand_Command {
	if x != nil {
		return x.Command
	}
	return nil
}

func (x *WrapperCommand) GetRequestLogCounts() *cmd_resp_logs.CmdRequestLogCounts {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RequestLogCounts); ok {
			return x.RequestLogCounts
		}
	}
	return nil
}

func (x *WrapperCommand) GetLogClear() *cmd_resp_logs.CmdRequestLogClear {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_LogClear); ok {
			return x.LogClear
		}
	}
	return nil
}

func (x *WrapperCommand) GetRequestLog() *cmd_resp_logs.CmdRequestLogEntries {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RequestLog); ok {
			return x.RequestLog
		}
	}
	return nil
}

func (x *WrapperCommand) GetReqAuditlogCounts() *cmd_resp_logs.CmdRequestAuditLogCounts {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_ReqAuditlogCounts); ok {
			return x.ReqAuditlogCounts
		}
	}
	return nil
}

func (x *WrapperCommand) GetAuditlogClear() *cmd_resp_logs.CmdRequestAuditLogClear {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_AuditlogClear); ok {
			return x.AuditlogClear
		}
	}
	return nil
}

func (x *WrapperCommand) GetAuditLogReset() *cmd_resp_logs.CmdRequestAuditLogReset {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_AuditLogReset); ok {
			return x.AuditLogReset
		}
	}
	return nil
}

func (x *WrapperCommand) GetRequestAuditlog() *cmd_resp_logs.CmdRequestAuditLogEntries {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RequestAuditlog); ok {
			return x.RequestAuditlog
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdMonitorData() *cmd_resp_config.CmdReadMonitorData {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdMonitorData); ok {
			return x.RdMonitorData
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdNetworkConfigUnit() *cmd_resp_config.CmdReadUnitNetworkConfiguration {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdNetworkConfigUnit); ok {
			return x.RdNetworkConfigUnit
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdNetworkConfigActive() *cmd_resp_config.CmdReadActiveNetworkConfiguration {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdNetworkConfigActive); ok {
			return x.RdNetworkConfigActive
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdChannelConfig() *cmd_resp_config.CmdReadPerChannelConfiguration {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdChannelConfig); ok {
			return x.RdChannelConfig
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdChannelCurrentSense() *cmd_resp_config.CmdReadPerChannelCurrentSenseSettings {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdChannelCurrentSense); ok {
			return x.RdChannelCurrentSense
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdChannelPermissives() *cmd_resp_config.CmdReadPerChannelPermissiveSettings {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdChannelPermissives); ok {
			return x.RdChannelPermissives
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdFyaConfig() *cmd_resp_config.CmdReadFlashingYellowArrowConfiguration {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdFyaConfig); ok {
			return x.RdFyaConfig
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdDataKey() *cmd_resp_config.CmdReadDataKey {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdDataKey); ok {
			return x.RdDataKey
		}
	}
	return nil
}

func (x *WrapperCommand) GetWrDataKey() *cmd_resp_config.CmdWriteDataKey {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_WrDataKey); ok {
			return x.WrDataKey
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdFactorySettings() *cmd_resp_config.CmdReadFactorySettings {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdFactorySettings); ok {
			return x.RdFactorySettings
		}
	}
	return nil
}

func (x *WrapperCommand) GetWrFactorySettings() *cmd_resp_config.CmdWriteFactorySettings {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_WrFactorySettings); ok {
			return x.WrFactorySettings
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdUserSettings() *cmd_resp_config.CmdReadUserSettings {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdUserSettings); ok {
			return x.RdUserSettings
		}
	}
	return nil
}

func (x *WrapperCommand) GetWrUserSettings() *cmd_resp_config.CmdWriteUserSettings {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_WrUserSettings); ok {
			return x.WrUserSettings
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdPort1Disables() *cmd_resp_config.CmdReadPort1DisableOverrides {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdPort1Disables); ok {
			return x.RdPort1Disables
		}
	}
	return nil
}

func (x *WrapperCommand) GetWrPort1Disables() *cmd_resp_config.CmdWritePort1DisableOverrides {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_WrPort1Disables); ok {
			return x.WrPort1Disables
		}
	}
	return nil
}

func (x *WrapperCommand) GetWrAgencyOptions() *cmd_resp_config.CmdWriteAgencyOptions {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_WrAgencyOptions); ok {
			return x.WrAgencyOptions
		}
	}
	return nil
}

func (x *WrapperCommand) GetRemoteReset() *cmd_resp_config.CmdRemoteReset {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RemoteReset); ok {
			return x.RemoteReset
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdPort1Stats() *cmd_resp_stats.CmdReadPort1Statistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdPort1Stats); ok {
			return x.RdPort1Stats
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdDataKeyStats() *cmd_resp_stats.CmdReadDataKeyStatistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdDataKeyStats); ok {
			return x.RdDataKeyStats
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdMainIsoCommsStats() *cmd_resp_stats.CmdReadMainToIsolatedCommStatistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdMainIsoCommsStats); ok {
			return x.RdMainIsoCommsStats
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdMainCommsStats() *cmd_resp_stats.CmdReadMainToCommsCommStatistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdMainCommsStats); ok {
			return x.RdMainCommsStats
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdCommsMainStats() *cmd_resp_stats.CmdReadCommsToMainCommStatistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdCommsMainStats); ok {
			return x.RdCommsMainStats
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdFlashStats() *cmd_resp_stats.CmdReadFlashStatistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdFlashStats); ok {
			return x.RdFlashStats
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdWatchdogStats() *cmd_resp_stats.CmdReadWatchdogStatistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdWatchdogStats); ok {
			return x.RdWatchdogStats
		}
	}
	return nil
}

func (x *WrapperCommand) GetRdDateTimeDst() *cmd_resp_stats.CmdGetTimeDatesDst {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RdDateTimeDst); ok {
			return x.RdDateTimeDst
		}
	}
	return nil
}

func (x *WrapperCommand) GetClearStats() *cmd_resp_stats.CmdClearStatistics {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_ClearStats); ok {
			return x.ClearStats
		}
	}
	return nil
}

func (x *WrapperCommand) GetWrDateTimeDst() *cmd_resp_stats.CmdSetTimeDatesDst {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_WrDateTimeDst); ok {
			return x.WrDateTimeDst
		}
	}
	return nil
}

func (x *WrapperCommand) GetRemoteDisplayButtonEvent() *cmd_resp_stats.CmdRemoteDisplayButtonEvent {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RemoteDisplayButtonEvent); ok {
			return x.RemoteDisplayButtonEvent
		}
	}
	return nil
}

func (x *WrapperCommand) GetStartRealtime() *cmd_resp_realtime.CmdStartRealtimeData {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_StartRealtime); ok {
			return x.StartRealtime
		}
	}
	return nil
}

func (x *WrapperCommand) GetStopRealtime() *cmd_resp_realtime.CmdStopRealtimeData {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_StopRealtime); ok {
			return x.StopRealtime
		}
	}
	return nil
}

func (x *WrapperCommand) GetManifestVersions() *cmd_resp_dfu.CmdManifestVersions {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_ManifestVersions); ok {
			return x.ManifestVersions
		}
	}
	return nil
}

func (x *WrapperCommand) GetRebootCommsMcu() *cmd_resp_dfu.CmdRebootCommsMcu {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_RebootCommsMcu); ok {
			return x.RebootCommsMcu
		}
	}
	return nil
}

func (x *WrapperCommand) GetInitiateDfu() *cmd_resp_dfu.CmdInitiateFirmwareUpdate {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_InitiateDfu); ok {
			return x.InitiateDfu
		}
	}
	return nil
}

func (x *WrapperCommand) GetSendFwManifest() *cmd_resp_dfu.CmdFirmwareUpdateManifest {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_SendFwManifest); ok {
			return x.SendFwManifest
		}
	}
	return nil
}

func (x *WrapperCommand) GetBeginFirmwareDownload() *cmd_resp_dfu.CmdBeginFirmwareDownload {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_BeginFirmwareDownload); ok {
			return x.BeginFirmwareDownload
		}
	}
	return nil
}

func (x *WrapperCommand) GetDownloadFirmwareChunk() *cmd_resp_dfu.CmdFirmwareDownloadChunk {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_DownloadFirmwareChunk); ok {
			return x.DownloadFirmwareChunk
		}
	}
	return nil
}

func (x *WrapperCommand) GetTestChunk() *cmd_resp_comms.CmdChunkTest {
	if x != nil {
		if x, ok := x.Command.(*WrapperCommand_TestChunk); ok {
			return x.TestChunk
		}
	}
	return nil
}

type isWrapperCommand_Command interface {
	isWrapperCommand_Command()
}

type WrapperCommand_RequestLogCounts struct {
	// log request commands
	RequestLogCounts *cmd_resp_logs.CmdRequestLogCounts `protobuf:"bytes,20,opt,name=request_log_counts,json=requestLogCounts,proto3,oneof"`
}

type WrapperCommand_LogClear struct {
	LogClear *cmd_resp_logs.CmdRequestLogClear `protobuf:"bytes,21,opt,name=log_clear,json=logClear,proto3,oneof"`
}

type WrapperCommand_RequestLog struct {
	RequestLog *cmd_resp_logs.CmdRequestLogEntries `protobuf:"bytes,22,opt,name=request_log,json=requestLog,proto3,oneof"`
}

type WrapperCommand_ReqAuditlogCounts struct {
	ReqAuditlogCounts *cmd_resp_logs.CmdRequestAuditLogCounts `protobuf:"bytes,23,opt,name=req_auditlog_counts,json=reqAuditlogCounts,proto3,oneof"`
}

type WrapperCommand_AuditlogClear struct {
	AuditlogClear *cmd_resp_logs.CmdRequestAuditLogClear `protobuf:"bytes,24,opt,name=auditlog_clear,json=auditlogClear,proto3,oneof"`
}

type WrapperCommand_AuditLogReset struct {
	AuditLogReset *cmd_resp_logs.CmdRequestAuditLogReset `protobuf:"bytes,25,opt,name=audit_log_reset,json=auditLogReset,proto3,oneof"`
}

type WrapperCommand_RequestAuditlog struct {
	RequestAuditlog *cmd_resp_logs.CmdRequestAuditLogEntries `protobuf:"bytes,26,opt,name=request_auditlog,json=requestAuditlog,proto3,oneof"`
}

type WrapperCommand_RdMonitorData struct {
	// configuration commands
	RdMonitorData *cmd_resp_config.CmdReadMonitorData `protobuf:"bytes,31,opt,name=rd_monitor_data,json=rdMonitorData,proto3,oneof"`
}

type WrapperCommand_RdNetworkConfigUnit struct {
	RdNetworkConfigUnit *cmd_resp_config.CmdReadUnitNetworkConfiguration `protobuf:"bytes,32,opt,name=rd_network_config_unit,json=rdNetworkConfigUnit,proto3,oneof"`
}

type WrapperCommand_RdNetworkConfigActive struct {
	RdNetworkConfigActive *cmd_resp_config.CmdReadActiveNetworkConfiguration `protobuf:"bytes,33,opt,name=rd_network_config_active,json=rdNetworkConfigActive,proto3,oneof"`
}

type WrapperCommand_RdChannelConfig struct {
	RdChannelConfig *cmd_resp_config.CmdReadPerChannelConfiguration `protobuf:"bytes,34,opt,name=rd_channel_config,json=rdChannelConfig,proto3,oneof"`
}

type WrapperCommand_RdChannelCurrentSense struct {
	RdChannelCurrentSense *cmd_resp_config.CmdReadPerChannelCurrentSenseSettings `protobuf:"bytes,35,opt,name=rd_channel_current_sense,json=rdChannelCurrentSense,proto3,oneof"`
}

type WrapperCommand_RdChannelPermissives struct {
	RdChannelPermissives *cmd_resp_config.CmdReadPerChannelPermissiveSettings `protobuf:"bytes,36,opt,name=rd_channel_permissives,json=rdChannelPermissives,proto3,oneof"`
}

type WrapperCommand_RdFyaConfig struct {
	RdFyaConfig *cmd_resp_config.CmdReadFlashingYellowArrowConfiguration `protobuf:"bytes,37,opt,name=rd_fya_config,json=rdFyaConfig,proto3,oneof"`
}

type WrapperCommand_RdDataKey struct {
	RdDataKey *cmd_resp_config.CmdReadDataKey `protobuf:"bytes,38,opt,name=rd_data_key,json=rdDataKey,proto3,oneof"` // or program card
}

type WrapperCommand_WrDataKey struct {
	WrDataKey *cmd_resp_config.CmdWriteDataKey `protobuf:"bytes,39,opt,name=wr_data_key,json=wrDataKey,proto3,oneof"` // or program card
}

type WrapperCommand_RdFactorySettings struct {
	RdFactorySettings *cmd_resp_config.CmdReadFactorySettings `protobuf:"bytes,40,opt,name=rd_factory_settings,json=rdFactorySettings,proto3,oneof"`
}

type WrapperCommand_WrFactorySettings struct {
	WrFactorySettings *cmd_resp_config.CmdWriteFactorySettings `protobuf:"bytes,41,opt,name=wr_factory_settings,json=wrFactorySettings,proto3,oneof"`
}

type WrapperCommand_RdUserSettings struct {
	RdUserSettings *cmd_resp_config.CmdReadUserSettings `protobuf:"bytes,42,opt,name=rd_user_settings,json=rdUserSettings,proto3,oneof"`
}

type WrapperCommand_WrUserSettings struct {
	WrUserSettings *cmd_resp_config.CmdWriteUserSettings `protobuf:"bytes,43,opt,name=wr_user_settings,json=wrUserSettings,proto3,oneof"`
}

type WrapperCommand_RdPort1Disables struct {
	RdPort1Disables *cmd_resp_config.CmdReadPort1DisableOverrides `protobuf:"bytes,44,opt,name=rd_port1_disables,json=rdPort1Disables,proto3,oneof"`
}

type WrapperCommand_WrPort1Disables struct {
	WrPort1Disables *cmd_resp_config.CmdWritePort1DisableOverrides `protobuf:"bytes,45,opt,name=wr_port1_disables,json=wrPort1Disables,proto3,oneof"`
}

type WrapperCommand_WrAgencyOptions struct {
	WrAgencyOptions *cmd_resp_config.CmdWriteAgencyOptions `protobuf:"bytes,46,opt,name=wr_agency_options,json=wrAgencyOptions,proto3,oneof"`
}

type WrapperCommand_RemoteReset struct {
	RemoteReset *cmd_resp_config.CmdRemoteReset `protobuf:"bytes,47,opt,name=remote_reset,json=remoteReset,proto3,oneof"`
}

type WrapperCommand_RdPort1Stats struct {
	// status and statistics commands
	RdPort1Stats *cmd_resp_stats.CmdReadPort1Statistics `protobuf:"bytes,50,opt,name=rd_port1_stats,json=rdPort1Stats,proto3,oneof"`
}

type WrapperCommand_RdDataKeyStats struct {
	RdDataKeyStats *cmd_resp_stats.CmdReadDataKeyStatistics `protobuf:"bytes,51,opt,name=rd_data_key_stats,json=rdDataKeyStats,proto3,oneof"` // or program card
}

type WrapperCommand_RdMainIsoCommsStats struct {
	RdMainIsoCommsStats *cmd_resp_stats.CmdReadMainToIsolatedCommStatistics `protobuf:"bytes,52,opt,name=rd_main_iso_comms_stats,json=rdMainIsoCommsStats,proto3,oneof"`
}

type WrapperCommand_RdMainCommsStats struct {
	RdMainCommsStats *cmd_resp_stats.CmdReadMainToCommsCommStatistics `protobuf:"bytes,53,opt,name=rd_main_comms_stats,json=rdMainCommsStats,proto3,oneof"`
}

type WrapperCommand_RdCommsMainStats struct {
	RdCommsMainStats *cmd_resp_stats.CmdReadCommsToMainCommStatistics `protobuf:"bytes,54,opt,name=rd_comms_main_stats,json=rdCommsMainStats,proto3,oneof"`
}

type WrapperCommand_RdFlashStats struct {
	RdFlashStats *cmd_resp_stats.CmdReadFlashStatistics `protobuf:"bytes,55,opt,name=rd_flash_stats,json=rdFlashStats,proto3,oneof"`
}

type WrapperCommand_RdWatchdogStats struct {
	RdWatchdogStats *cmd_resp_stats.CmdReadWatchdogStatistics `protobuf:"bytes,56,opt,name=rd_watchdog_stats,json=rdWatchdogStats,proto3,oneof"`
}

type WrapperCommand_RdDateTimeDst struct {
	RdDateTimeDst *cmd_resp_stats.CmdGetTimeDatesDst `protobuf:"bytes,57,opt,name=rd_date_time_dst,json=rdDateTimeDst,proto3,oneof"`
}

type WrapperCommand_ClearStats struct {
	ClearStats *cmd_resp_stats.CmdClearStatistics `protobuf:"bytes,58,opt,name=clear_stats,json=clearStats,proto3,oneof"`
}

type WrapperCommand_WrDateTimeDst struct {
	WrDateTimeDst *cmd_resp_stats.CmdSetTimeDatesDst `protobuf:"bytes,59,opt,name=wr_date_time_dst,json=wrDateTimeDst,proto3,oneof"`
}

type WrapperCommand_RemoteDisplayButtonEvent struct {
	RemoteDisplayButtonEvent *cmd_resp_stats.CmdRemoteDisplayButtonEvent `protobuf:"bytes,65,opt,name=remote_display_button_event,json=remoteDisplayButtonEvent,proto3,oneof"`
}

type WrapperCommand_StartRealtime struct {
	// realtime data commands
	StartRealtime *cmd_resp_realtime.CmdStartRealtimeData `protobuf:"bytes,60,opt,name=start_realtime,json=startRealtime,proto3,oneof"`
}

type WrapperCommand_StopRealtime struct {
	StopRealtime *cmd_resp_realtime.CmdStopRealtimeData `protobuf:"bytes,61,opt,name=stop_realtime,json=stopRealtime,proto3,oneof"` // Note that 65 is used for remote_display_button_event above
}

type WrapperCommand_ManifestVersions struct {
	// DFU commands
	ManifestVersions *cmd_resp_dfu.CmdManifestVersions `protobuf:"bytes,70,opt,name=manifest_versions,json=manifestVersions,proto3,oneof"`
}

type WrapperCommand_RebootCommsMcu struct {
	RebootCommsMcu *cmd_resp_dfu.CmdRebootCommsMcu `protobuf:"bytes,71,opt,name=reboot_comms_mcu,json=rebootCommsMcu,proto3,oneof"`
}

type WrapperCommand_InitiateDfu struct {
	InitiateDfu *cmd_resp_dfu.CmdInitiateFirmwareUpdate `protobuf:"bytes,72,opt,name=initiate_dfu,json=initiateDfu,proto3,oneof"`
}

type WrapperCommand_SendFwManifest struct {
	SendFwManifest *cmd_resp_dfu.CmdFirmwareUpdateManifest `protobuf:"bytes,73,opt,name=send_fw_manifest,json=sendFwManifest,proto3,oneof"`
}

type WrapperCommand_BeginFirmwareDownload struct {
	BeginFirmwareDownload *cmd_resp_dfu.CmdBeginFirmwareDownload `protobuf:"bytes,74,opt,name=begin_firmware_download,json=beginFirmwareDownload,proto3,oneof"`
}

type WrapperCommand_DownloadFirmwareChunk struct {
	DownloadFirmwareChunk *cmd_resp_dfu.CmdFirmwareDownloadChunk `protobuf:"bytes,75,opt,name=download_firmware_chunk,json=downloadFirmwareChunk,proto3,oneof"`
}

type WrapperCommand_TestChunk struct {
	// test commands
	TestChunk *cmd_resp_comms.CmdChunkTest `protobuf:"bytes,90,opt,name=test_chunk,json=testChunk,proto3,oneof"`
}

func (*WrapperCommand_RequestLogCounts) isWrapperCommand_Command() {}

func (*WrapperCommand_LogClear) isWrapperCommand_Command() {}

func (*WrapperCommand_RequestLog) isWrapperCommand_Command() {}

func (*WrapperCommand_ReqAuditlogCounts) isWrapperCommand_Command() {}

func (*WrapperCommand_AuditlogClear) isWrapperCommand_Command() {}

func (*WrapperCommand_AuditLogReset) isWrapperCommand_Command() {}

func (*WrapperCommand_RequestAuditlog) isWrapperCommand_Command() {}

func (*WrapperCommand_RdMonitorData) isWrapperCommand_Command() {}

func (*WrapperCommand_RdNetworkConfigUnit) isWrapperCommand_Command() {}

func (*WrapperCommand_RdNetworkConfigActive) isWrapperCommand_Command() {}

func (*WrapperCommand_RdChannelConfig) isWrapperCommand_Command() {}

func (*WrapperCommand_RdChannelCurrentSense) isWrapperCommand_Command() {}

func (*WrapperCommand_RdChannelPermissives) isWrapperCommand_Command() {}

func (*WrapperCommand_RdFyaConfig) isWrapperCommand_Command() {}

func (*WrapperCommand_RdDataKey) isWrapperCommand_Command() {}

func (*WrapperCommand_WrDataKey) isWrapperCommand_Command() {}

func (*WrapperCommand_RdFactorySettings) isWrapperCommand_Command() {}

func (*WrapperCommand_WrFactorySettings) isWrapperCommand_Command() {}

func (*WrapperCommand_RdUserSettings) isWrapperCommand_Command() {}

func (*WrapperCommand_WrUserSettings) isWrapperCommand_Command() {}

func (*WrapperCommand_RdPort1Disables) isWrapperCommand_Command() {}

func (*WrapperCommand_WrPort1Disables) isWrapperCommand_Command() {}

func (*WrapperCommand_WrAgencyOptions) isWrapperCommand_Command() {}

func (*WrapperCommand_RemoteReset) isWrapperCommand_Command() {}

func (*WrapperCommand_RdPort1Stats) isWrapperCommand_Command() {}

func (*WrapperCommand_RdDataKeyStats) isWrapperCommand_Command() {}

func (*WrapperCommand_RdMainIsoCommsStats) isWrapperCommand_Command() {}

func (*WrapperCommand_RdMainCommsStats) isWrapperCommand_Command() {}

func (*WrapperCommand_RdCommsMainStats) isWrapperCommand_Command() {}

func (*WrapperCommand_RdFlashStats) isWrapperCommand_Command() {}

func (*WrapperCommand_RdWatchdogStats) isWrapperCommand_Command() {}

func (*WrapperCommand_RdDateTimeDst) isWrapperCommand_Command() {}

func (*WrapperCommand_ClearStats) isWrapperCommand_Command() {}

func (*WrapperCommand_WrDateTimeDst) isWrapperCommand_Command() {}

func (*WrapperCommand_RemoteDisplayButtonEvent) isWrapperCommand_Command() {}

func (*WrapperCommand_StartRealtime) isWrapperCommand_Command() {}

func (*WrapperCommand_StopRealtime) isWrapperCommand_Command() {}

func (*WrapperCommand_ManifestVersions) isWrapperCommand_Command() {}

func (*WrapperCommand_RebootCommsMcu) isWrapperCommand_Command() {}

func (*WrapperCommand_InitiateDfu) isWrapperCommand_Command() {}

func (*WrapperCommand_SendFwManifest) isWrapperCommand_Command() {}

func (*WrapperCommand_BeginFirmwareDownload) isWrapperCommand_Command() {}

func (*WrapperCommand_DownloadFirmwareChunk) isWrapperCommand_Command() {}

func (*WrapperCommand_TestChunk) isWrapperCommand_Command() {}

//	WrapperResponse is the wrapper for all websocket reaponse and realtime messages from the
//
// monitor unit to the application.  (Excluding authentication messages over raw TCP)
type WrapperResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The response interface version
	// NOTE: This may not match the WrapperCommand.version!  This is the command and response version structures supported
	//
	//	by this firmware.  Generally, versions will be backwards compatible, but firmware with a response version older
	//	than the command version will not know how to interpret new parameters (fields) in the new command version, and
	//	not return new fields or data in the responses, but should interpret the command and provide the response
	//	according to the version format given here.
	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	// This value matches the request_id value in the command that generated the response.
	// Some log read commands may generate multiple responses, and all the responses will have the
	// same request_id value.
	// For Realtime data messages, the request_id matches the last "start_realtime" command request_id
	// the started or modified the requested realtime data.
	RequestId uint32 `protobuf:"varint,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// The response code returns any errors that occurred processing the command.
	Code EResponseCodes `protobuf:"varint,3,opt,name=code,proto3,enum=wrappers.EResponseCodes" json:"code,omitempty"`
	// An optional string with Plaintext result of the command, for human eyes
	ResultTxt string `protobuf:"bytes,4,opt,name=result_txt,json=resultTxt,proto3" json:"result_txt,omitempty"` // Max string length set in cmd_resp_dfu.options
	// A response to a command with its parameters, or realtime data.  See the response message definition for details on the response.
	//
	// Types that are valid to be assigned to Response:
	//
	//	*WrapperResponse_AuthStatus
	//	*WrapperResponse_RequestLogCounts
	//	*WrapperResponse_LogClear
	//	*WrapperResponse_RequestLog
	//	*WrapperResponse_ReqAuditlogCounts
	//	*WrapperResponse_AuditlogClear
	//	*WrapperResponse_AuditLogReset
	//	*WrapperResponse_RequestAuditlog
	//	*WrapperResponse_RdMonitorData
	//	*WrapperResponse_RdNetworkConfigUnit
	//	*WrapperResponse_RdNetworkConfigActive
	//	*WrapperResponse_RdChannelConfig
	//	*WrapperResponse_RdChannelCurrentSense
	//	*WrapperResponse_RdChannelPermissives
	//	*WrapperResponse_RdFyaConfig
	//	*WrapperResponse_RdDataKey
	//	*WrapperResponse_WrDataKey
	//	*WrapperResponse_RdFactorySettings
	//	*WrapperResponse_WrFactorySettings
	//	*WrapperResponse_RdUserSettings
	//	*WrapperResponse_WrUserSettings
	//	*WrapperResponse_RdPort1Disables
	//	*WrapperResponse_WrPort1Disables
	//	*WrapperResponse_WrAgencyOptions
	//	*WrapperResponse_RemoteReset
	//	*WrapperResponse_RdPort1Stats
	//	*WrapperResponse_RdDataKeyStats
	//	*WrapperResponse_RdMainIsoCommsStats
	//	*WrapperResponse_RdMainCommsStats
	//	*WrapperResponse_RdCommsMainStats
	//	*WrapperResponse_RdFlashStats
	//	*WrapperResponse_RdWatchdogStats
	//	*WrapperResponse_RdDateTimeDst
	//	*WrapperResponse_ClearStats
	//	*WrapperResponse_WrDateTimeDst
	//	*WrapperResponse_RemoteDisplayButtonEvent
	//	*WrapperResponse_StartRealtime
	//	*WrapperResponse_StopRealtime
	//	*WrapperResponse_ManifestVersions
	//	*WrapperResponse_RebootCommsMcu
	//	*WrapperResponse_InitiateDfu
	//	*WrapperResponse_SendFwManifest
	//	*WrapperResponse_BeginFirmwareDownload
	//	*WrapperResponse_DownloadFirmwareChunk
	//	*WrapperResponse_RealtimeData
	//	*WrapperResponse_RealtimeDisplay
	//	*WrapperResponse_TestChunk
	Response      isWrapperResponse_Response `protobuf_oneof:"response"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WrapperResponse) Reset() {
	*x = WrapperResponse{}
	mi := &file_wrappers_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WrapperResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WrapperResponse) ProtoMessage() {}

func (x *WrapperResponse) ProtoReflect() protoreflect.Message {
	mi := &file_wrappers_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WrapperResponse.ProtoReflect.Descriptor instead.
func (*WrapperResponse) Descriptor() ([]byte, []int) {
	return file_wrappers_proto_rawDescGZIP(), []int{1}
}

func (x *WrapperResponse) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *WrapperResponse) GetRequestId() uint32 {
	if x != nil {
		return x.RequestId
	}
	return 0
}

func (x *WrapperResponse) GetCode() EResponseCodes {
	if x != nil {
		return x.Code
	}
	return EResponseCodes_RESP_UNSPECIFIED
}

func (x *WrapperResponse) GetResultTxt() string {
	if x != nil {
		return x.ResultTxt
	}
	return ""
}

func (x *WrapperResponse) GetResponse() isWrapperResponse_Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *WrapperResponse) GetAuthStatus() *cmd_resp_comms.RespAuthStatus {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_AuthStatus); ok {
			return x.AuthStatus
		}
	}
	return nil
}

func (x *WrapperResponse) GetRequestLogCounts() *cmd_resp_logs.RespRequestLogCounts {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RequestLogCounts); ok {
			return x.RequestLogCounts
		}
	}
	return nil
}

func (x *WrapperResponse) GetLogClear() *cmd_resp_logs.RespRequestLogClear {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_LogClear); ok {
			return x.LogClear
		}
	}
	return nil
}

func (x *WrapperResponse) GetRequestLog() *cmd_resp_logs.RespRequestLogEntries {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RequestLog); ok {
			return x.RequestLog
		}
	}
	return nil
}

func (x *WrapperResponse) GetReqAuditlogCounts() *cmd_resp_logs.RespRequestAuditLogCounts {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_ReqAuditlogCounts); ok {
			return x.ReqAuditlogCounts
		}
	}
	return nil
}

func (x *WrapperResponse) GetAuditlogClear() *cmd_resp_logs.RespRequestAuditLogClear {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_AuditlogClear); ok {
			return x.AuditlogClear
		}
	}
	return nil
}

func (x *WrapperResponse) GetAuditLogReset() *cmd_resp_logs.RespRequestAuditLogReset {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_AuditLogReset); ok {
			return x.AuditLogReset
		}
	}
	return nil
}

func (x *WrapperResponse) GetRequestAuditlog() *cmd_resp_logs.RespRequestAuditLogEntries {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RequestAuditlog); ok {
			return x.RequestAuditlog
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdMonitorData() *cmd_resp_config.RespReadMonitorData {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdMonitorData); ok {
			return x.RdMonitorData
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdNetworkConfigUnit() *cmd_resp_config.RespReadUnitNetworkConfiguration {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdNetworkConfigUnit); ok {
			return x.RdNetworkConfigUnit
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdNetworkConfigActive() *cmd_resp_config.RespReadActiveNetworkConfiguration {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdNetworkConfigActive); ok {
			return x.RdNetworkConfigActive
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdChannelConfig() *cmd_resp_config.RespReadPerChannelConfiguration {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdChannelConfig); ok {
			return x.RdChannelConfig
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdChannelCurrentSense() *cmd_resp_config.RespReadPerChannelCurrentSenseSettings {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdChannelCurrentSense); ok {
			return x.RdChannelCurrentSense
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdChannelPermissives() *cmd_resp_config.RespReadPerChannelPermissiveSettings {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdChannelPermissives); ok {
			return x.RdChannelPermissives
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdFyaConfig() *cmd_resp_config.RespReadFlashingYellowArrowConfiguration {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdFyaConfig); ok {
			return x.RdFyaConfig
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdDataKey() *cmd_resp_config.RespReadDataKey {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdDataKey); ok {
			return x.RdDataKey
		}
	}
	return nil
}

func (x *WrapperResponse) GetWrDataKey() *cmd_resp_config.RespWriteDataKey {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_WrDataKey); ok {
			return x.WrDataKey
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdFactorySettings() *cmd_resp_config.RespReadFactorySettings {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdFactorySettings); ok {
			return x.RdFactorySettings
		}
	}
	return nil
}

func (x *WrapperResponse) GetWrFactorySettings() *cmd_resp_config.RespWriteFactorySettings {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_WrFactorySettings); ok {
			return x.WrFactorySettings
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdUserSettings() *cmd_resp_config.RespReadUserSettings {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdUserSettings); ok {
			return x.RdUserSettings
		}
	}
	return nil
}

func (x *WrapperResponse) GetWrUserSettings() *cmd_resp_config.RespWriteUserSettings {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_WrUserSettings); ok {
			return x.WrUserSettings
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdPort1Disables() *cmd_resp_config.RespReadPort1DisableOverrides {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdPort1Disables); ok {
			return x.RdPort1Disables
		}
	}
	return nil
}

func (x *WrapperResponse) GetWrPort1Disables() *cmd_resp_config.RespWritePort1DisableOverrides {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_WrPort1Disables); ok {
			return x.WrPort1Disables
		}
	}
	return nil
}

func (x *WrapperResponse) GetWrAgencyOptions() *cmd_resp_config.RespWriteAgencyOptions {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_WrAgencyOptions); ok {
			return x.WrAgencyOptions
		}
	}
	return nil
}

func (x *WrapperResponse) GetRemoteReset() *cmd_resp_config.RespRemoteReset {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RemoteReset); ok {
			return x.RemoteReset
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdPort1Stats() *cmd_resp_stats.RespReadPort1Statistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdPort1Stats); ok {
			return x.RdPort1Stats
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdDataKeyStats() *cmd_resp_stats.RespReadDataKeyStatistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdDataKeyStats); ok {
			return x.RdDataKeyStats
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdMainIsoCommsStats() *cmd_resp_stats.RespReadMainToIsolatedCommStatistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdMainIsoCommsStats); ok {
			return x.RdMainIsoCommsStats
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdMainCommsStats() *cmd_resp_stats.RespReadMainToCommsCommStatistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdMainCommsStats); ok {
			return x.RdMainCommsStats
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdCommsMainStats() *cmd_resp_stats.RespReadCommsToMainCommStatistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdCommsMainStats); ok {
			return x.RdCommsMainStats
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdFlashStats() *cmd_resp_stats.RespReadFlashStatistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdFlashStats); ok {
			return x.RdFlashStats
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdWatchdogStats() *cmd_resp_stats.RespReadWatchdogStatistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdWatchdogStats); ok {
			return x.RdWatchdogStats
		}
	}
	return nil
}

func (x *WrapperResponse) GetRdDateTimeDst() *cmd_resp_stats.RespGetTimeDatesDst {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RdDateTimeDst); ok {
			return x.RdDateTimeDst
		}
	}
	return nil
}

func (x *WrapperResponse) GetClearStats() *cmd_resp_stats.RespClearStatistics {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_ClearStats); ok {
			return x.ClearStats
		}
	}
	return nil
}

func (x *WrapperResponse) GetWrDateTimeDst() *cmd_resp_stats.RespSetTimeDatesDst {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_WrDateTimeDst); ok {
			return x.WrDateTimeDst
		}
	}
	return nil
}

func (x *WrapperResponse) GetRemoteDisplayButtonEvent() *cmd_resp_stats.RespRemoteDisplayButtonEvent {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RemoteDisplayButtonEvent); ok {
			return x.RemoteDisplayButtonEvent
		}
	}
	return nil
}

func (x *WrapperResponse) GetStartRealtime() *cmd_resp_realtime.RespStartRealtimeData {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_StartRealtime); ok {
			return x.StartRealtime
		}
	}
	return nil
}

func (x *WrapperResponse) GetStopRealtime() *cmd_resp_realtime.RespStopRealtimeData {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_StopRealtime); ok {
			return x.StopRealtime
		}
	}
	return nil
}

func (x *WrapperResponse) GetManifestVersions() *cmd_resp_dfu.RespManifestVersions {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_ManifestVersions); ok {
			return x.ManifestVersions
		}
	}
	return nil
}

func (x *WrapperResponse) GetRebootCommsMcu() *cmd_resp_dfu.RespRebootCommsMcu {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RebootCommsMcu); ok {
			return x.RebootCommsMcu
		}
	}
	return nil
}

func (x *WrapperResponse) GetInitiateDfu() *cmd_resp_dfu.RespInitiateFirmwareUpdate {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_InitiateDfu); ok {
			return x.InitiateDfu
		}
	}
	return nil
}

func (x *WrapperResponse) GetSendFwManifest() *cmd_resp_dfu.RespFirmwareUpdateManifest {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_SendFwManifest); ok {
			return x.SendFwManifest
		}
	}
	return nil
}

func (x *WrapperResponse) GetBeginFirmwareDownload() *cmd_resp_dfu.RespBeginFirmwareDownload {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_BeginFirmwareDownload); ok {
			return x.BeginFirmwareDownload
		}
	}
	return nil
}

func (x *WrapperResponse) GetDownloadFirmwareChunk() *cmd_resp_dfu.RespFirmwareDownloadChunk {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_DownloadFirmwareChunk); ok {
			return x.DownloadFirmwareChunk
		}
	}
	return nil
}

func (x *WrapperResponse) GetRealtimeData() *cmd_resp_realtime.RealtimeData1 {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RealtimeData); ok {
			return x.RealtimeData
		}
	}
	return nil
}

func (x *WrapperResponse) GetRealtimeDisplay() *cmd_resp_realtime.RealtimeDisplay1 {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_RealtimeDisplay); ok {
			return x.RealtimeDisplay
		}
	}
	return nil
}

func (x *WrapperResponse) GetTestChunk() *cmd_resp_comms.RespChunkTest {
	if x != nil {
		if x, ok := x.Response.(*WrapperResponse_TestChunk); ok {
			return x.TestChunk
		}
	}
	return nil
}

type isWrapperResponse_Response interface {
	isWrapperResponse_Response()
}

type WrapperResponse_AuthStatus struct {
	// communications responses
	AuthStatus *cmd_resp_comms.RespAuthStatus `protobuf:"bytes,11,opt,name=auth_status,json=authStatus,proto3,oneof"`
}

type WrapperResponse_RequestLogCounts struct {
	// log request responses
	RequestLogCounts *cmd_resp_logs.RespRequestLogCounts `protobuf:"bytes,20,opt,name=request_log_counts,json=requestLogCounts,proto3,oneof"`
}

type WrapperResponse_LogClear struct {
	LogClear *cmd_resp_logs.RespRequestLogClear `protobuf:"bytes,21,opt,name=log_clear,json=logClear,proto3,oneof"`
}

type WrapperResponse_RequestLog struct {
	RequestLog *cmd_resp_logs.RespRequestLogEntries `protobuf:"bytes,22,opt,name=request_log,json=requestLog,proto3,oneof"`
}

type WrapperResponse_ReqAuditlogCounts struct {
	ReqAuditlogCounts *cmd_resp_logs.RespRequestAuditLogCounts `protobuf:"bytes,23,opt,name=req_auditlog_counts,json=reqAuditlogCounts,proto3,oneof"`
}

type WrapperResponse_AuditlogClear struct {
	AuditlogClear *cmd_resp_logs.RespRequestAuditLogClear `protobuf:"bytes,24,opt,name=auditlog_clear,json=auditlogClear,proto3,oneof"`
}

type WrapperResponse_AuditLogReset struct {
	AuditLogReset *cmd_resp_logs.RespRequestAuditLogReset `protobuf:"bytes,25,opt,name=audit_log_reset,json=auditLogReset,proto3,oneof"`
}

type WrapperResponse_RequestAuditlog struct {
	RequestAuditlog *cmd_resp_logs.RespRequestAuditLogEntries `protobuf:"bytes,26,opt,name=request_auditlog,json=requestAuditlog,proto3,oneof"`
}

type WrapperResponse_RdMonitorData struct {
	// configuration responses
	RdMonitorData *cmd_resp_config.RespReadMonitorData `protobuf:"bytes,31,opt,name=rd_monitor_data,json=rdMonitorData,proto3,oneof"`
}

type WrapperResponse_RdNetworkConfigUnit struct {
	RdNetworkConfigUnit *cmd_resp_config.RespReadUnitNetworkConfiguration `protobuf:"bytes,32,opt,name=rd_network_config_unit,json=rdNetworkConfigUnit,proto3,oneof"`
}

type WrapperResponse_RdNetworkConfigActive struct {
	RdNetworkConfigActive *cmd_resp_config.RespReadActiveNetworkConfiguration `protobuf:"bytes,33,opt,name=rd_network_config_active,json=rdNetworkConfigActive,proto3,oneof"`
}

type WrapperResponse_RdChannelConfig struct {
	RdChannelConfig *cmd_resp_config.RespReadPerChannelConfiguration `protobuf:"bytes,34,opt,name=rd_channel_config,json=rdChannelConfig,proto3,oneof"`
}

type WrapperResponse_RdChannelCurrentSense struct {
	RdChannelCurrentSense *cmd_resp_config.RespReadPerChannelCurrentSenseSettings `protobuf:"bytes,35,opt,name=rd_channel_current_sense,json=rdChannelCurrentSense,proto3,oneof"`
}

type WrapperResponse_RdChannelPermissives struct {
	RdChannelPermissives *cmd_resp_config.RespReadPerChannelPermissiveSettings `protobuf:"bytes,36,opt,name=rd_channel_permissives,json=rdChannelPermissives,proto3,oneof"`
}

type WrapperResponse_RdFyaConfig struct {
	RdFyaConfig *cmd_resp_config.RespReadFlashingYellowArrowConfiguration `protobuf:"bytes,37,opt,name=rd_fya_config,json=rdFyaConfig,proto3,oneof"`
}

type WrapperResponse_RdDataKey struct {
	RdDataKey *cmd_resp_config.RespReadDataKey `protobuf:"bytes,38,opt,name=rd_data_key,json=rdDataKey,proto3,oneof"` // or program card
}

type WrapperResponse_WrDataKey struct {
	WrDataKey *cmd_resp_config.RespWriteDataKey `protobuf:"bytes,39,opt,name=wr_data_key,json=wrDataKey,proto3,oneof"` // or program card
}

type WrapperResponse_RdFactorySettings struct {
	RdFactorySettings *cmd_resp_config.RespReadFactorySettings `protobuf:"bytes,40,opt,name=rd_factory_settings,json=rdFactorySettings,proto3,oneof"`
}

type WrapperResponse_WrFactorySettings struct {
	WrFactorySettings *cmd_resp_config.RespWriteFactorySettings `protobuf:"bytes,41,opt,name=wr_factory_settings,json=wrFactorySettings,proto3,oneof"`
}

type WrapperResponse_RdUserSettings struct {
	RdUserSettings *cmd_resp_config.RespReadUserSettings `protobuf:"bytes,42,opt,name=rd_user_settings,json=rdUserSettings,proto3,oneof"`
}

type WrapperResponse_WrUserSettings struct {
	WrUserSettings *cmd_resp_config.RespWriteUserSettings `protobuf:"bytes,43,opt,name=wr_user_settings,json=wrUserSettings,proto3,oneof"`
}

type WrapperResponse_RdPort1Disables struct {
	RdPort1Disables *cmd_resp_config.RespReadPort1DisableOverrides `protobuf:"bytes,44,opt,name=rd_port1_disables,json=rdPort1Disables,proto3,oneof"`
}

type WrapperResponse_WrPort1Disables struct {
	WrPort1Disables *cmd_resp_config.RespWritePort1DisableOverrides `protobuf:"bytes,45,opt,name=wr_port1_disables,json=wrPort1Disables,proto3,oneof"`
}

type WrapperResponse_WrAgencyOptions struct {
	WrAgencyOptions *cmd_resp_config.RespWriteAgencyOptions `protobuf:"bytes,46,opt,name=wr_agency_options,json=wrAgencyOptions,proto3,oneof"`
}

type WrapperResponse_RemoteReset struct {
	RemoteReset *cmd_resp_config.RespRemoteReset `protobuf:"bytes,47,opt,name=remote_reset,json=remoteReset,proto3,oneof"`
}

type WrapperResponse_RdPort1Stats struct {
	// status and statistics responses
	RdPort1Stats *cmd_resp_stats.RespReadPort1Statistics `protobuf:"bytes,50,opt,name=rd_port1_stats,json=rdPort1Stats,proto3,oneof"`
}

type WrapperResponse_RdDataKeyStats struct {
	RdDataKeyStats *cmd_resp_stats.RespReadDataKeyStatistics `protobuf:"bytes,51,opt,name=rd_data_key_stats,json=rdDataKeyStats,proto3,oneof"` // or program card
}

type WrapperResponse_RdMainIsoCommsStats struct {
	RdMainIsoCommsStats *cmd_resp_stats.RespReadMainToIsolatedCommStatistics `protobuf:"bytes,52,opt,name=rd_main_iso_comms_stats,json=rdMainIsoCommsStats,proto3,oneof"`
}

type WrapperResponse_RdMainCommsStats struct {
	RdMainCommsStats *cmd_resp_stats.RespReadMainToCommsCommStatistics `protobuf:"bytes,53,opt,name=rd_main_comms_stats,json=rdMainCommsStats,proto3,oneof"`
}

type WrapperResponse_RdCommsMainStats struct {
	RdCommsMainStats *cmd_resp_stats.RespReadCommsToMainCommStatistics `protobuf:"bytes,54,opt,name=rd_comms_main_stats,json=rdCommsMainStats,proto3,oneof"`
}

type WrapperResponse_RdFlashStats struct {
	RdFlashStats *cmd_resp_stats.RespReadFlashStatistics `protobuf:"bytes,55,opt,name=rd_flash_stats,json=rdFlashStats,proto3,oneof"`
}

type WrapperResponse_RdWatchdogStats struct {
	RdWatchdogStats *cmd_resp_stats.RespReadWatchdogStatistics `protobuf:"bytes,56,opt,name=rd_watchdog_stats,json=rdWatchdogStats,proto3,oneof"`
}

type WrapperResponse_RdDateTimeDst struct {
	RdDateTimeDst *cmd_resp_stats.RespGetTimeDatesDst `protobuf:"bytes,57,opt,name=rd_date_time_dst,json=rdDateTimeDst,proto3,oneof"`
}

type WrapperResponse_ClearStats struct {
	ClearStats *cmd_resp_stats.RespClearStatistics `protobuf:"bytes,58,opt,name=clear_stats,json=clearStats,proto3,oneof"`
}

type WrapperResponse_WrDateTimeDst struct {
	WrDateTimeDst *cmd_resp_stats.RespSetTimeDatesDst `protobuf:"bytes,59,opt,name=wr_date_time_dst,json=wrDateTimeDst,proto3,oneof"`
}

type WrapperResponse_RemoteDisplayButtonEvent struct {
	RemoteDisplayButtonEvent *cmd_resp_stats.RespRemoteDisplayButtonEvent `protobuf:"bytes,65,opt,name=remote_display_button_event,json=remoteDisplayButtonEvent,proto3,oneof"`
}

type WrapperResponse_StartRealtime struct {
	// realtime data responses
	StartRealtime *cmd_resp_realtime.RespStartRealtimeData `protobuf:"bytes,60,opt,name=start_realtime,json=startRealtime,proto3,oneof"`
}

type WrapperResponse_StopRealtime struct {
	StopRealtime *cmd_resp_realtime.RespStopRealtimeData `protobuf:"bytes,61,opt,name=stop_realtime,json=stopRealtime,proto3,oneof"` // Note that 65 is used for remote_display_button_event
}

type WrapperResponse_ManifestVersions struct {
	// DFU responses
	ManifestVersions *cmd_resp_dfu.RespManifestVersions `protobuf:"bytes,70,opt,name=manifest_versions,json=manifestVersions,proto3,oneof"`
}

type WrapperResponse_RebootCommsMcu struct {
	RebootCommsMcu *cmd_resp_dfu.RespRebootCommsMcu `protobuf:"bytes,71,opt,name=reboot_comms_mcu,json=rebootCommsMcu,proto3,oneof"`
}

type WrapperResponse_InitiateDfu struct {
	InitiateDfu *cmd_resp_dfu.RespInitiateFirmwareUpdate `protobuf:"bytes,72,opt,name=initiate_dfu,json=initiateDfu,proto3,oneof"`
}

type WrapperResponse_SendFwManifest struct {
	SendFwManifest *cmd_resp_dfu.RespFirmwareUpdateManifest `protobuf:"bytes,73,opt,name=send_fw_manifest,json=sendFwManifest,proto3,oneof"`
}

type WrapperResponse_BeginFirmwareDownload struct {
	BeginFirmwareDownload *cmd_resp_dfu.RespBeginFirmwareDownload `protobuf:"bytes,74,opt,name=begin_firmware_download,json=beginFirmwareDownload,proto3,oneof"`
}

type WrapperResponse_DownloadFirmwareChunk struct {
	DownloadFirmwareChunk *cmd_resp_dfu.RespFirmwareDownloadChunk `protobuf:"bytes,75,opt,name=download_firmware_chunk,json=downloadFirmwareChunk,proto3,oneof"`
}

type WrapperResponse_RealtimeData struct {
	// realtime data messages -----------------------------------------------------------------
	RealtimeData *cmd_resp_realtime.RealtimeData1 `protobuf:"bytes,80,opt,name=realtime_data,json=realtimeData,proto3,oneof"`
}

type WrapperResponse_RealtimeDisplay struct {
	RealtimeDisplay *cmd_resp_realtime.RealtimeDisplay1 `protobuf:"bytes,81,opt,name=realtime_display,json=realtimeDisplay,proto3,oneof"`
}

type WrapperResponse_TestChunk struct {
	// test commands
	TestChunk *cmd_resp_comms.RespChunkTest `protobuf:"bytes,90,opt,name=test_chunk,json=testChunk,proto3,oneof"`
}

func (*WrapperResponse_AuthStatus) isWrapperResponse_Response() {}

func (*WrapperResponse_RequestLogCounts) isWrapperResponse_Response() {}

func (*WrapperResponse_LogClear) isWrapperResponse_Response() {}

func (*WrapperResponse_RequestLog) isWrapperResponse_Response() {}

func (*WrapperResponse_ReqAuditlogCounts) isWrapperResponse_Response() {}

func (*WrapperResponse_AuditlogClear) isWrapperResponse_Response() {}

func (*WrapperResponse_AuditLogReset) isWrapperResponse_Response() {}

func (*WrapperResponse_RequestAuditlog) isWrapperResponse_Response() {}

func (*WrapperResponse_RdMonitorData) isWrapperResponse_Response() {}

func (*WrapperResponse_RdNetworkConfigUnit) isWrapperResponse_Response() {}

func (*WrapperResponse_RdNetworkConfigActive) isWrapperResponse_Response() {}

func (*WrapperResponse_RdChannelConfig) isWrapperResponse_Response() {}

func (*WrapperResponse_RdChannelCurrentSense) isWrapperResponse_Response() {}

func (*WrapperResponse_RdChannelPermissives) isWrapperResponse_Response() {}

func (*WrapperResponse_RdFyaConfig) isWrapperResponse_Response() {}

func (*WrapperResponse_RdDataKey) isWrapperResponse_Response() {}

func (*WrapperResponse_WrDataKey) isWrapperResponse_Response() {}

func (*WrapperResponse_RdFactorySettings) isWrapperResponse_Response() {}

func (*WrapperResponse_WrFactorySettings) isWrapperResponse_Response() {}

func (*WrapperResponse_RdUserSettings) isWrapperResponse_Response() {}

func (*WrapperResponse_WrUserSettings) isWrapperResponse_Response() {}

func (*WrapperResponse_RdPort1Disables) isWrapperResponse_Response() {}

func (*WrapperResponse_WrPort1Disables) isWrapperResponse_Response() {}

func (*WrapperResponse_WrAgencyOptions) isWrapperResponse_Response() {}

func (*WrapperResponse_RemoteReset) isWrapperResponse_Response() {}

func (*WrapperResponse_RdPort1Stats) isWrapperResponse_Response() {}

func (*WrapperResponse_RdDataKeyStats) isWrapperResponse_Response() {}

func (*WrapperResponse_RdMainIsoCommsStats) isWrapperResponse_Response() {}

func (*WrapperResponse_RdMainCommsStats) isWrapperResponse_Response() {}

func (*WrapperResponse_RdCommsMainStats) isWrapperResponse_Response() {}

func (*WrapperResponse_RdFlashStats) isWrapperResponse_Response() {}

func (*WrapperResponse_RdWatchdogStats) isWrapperResponse_Response() {}

func (*WrapperResponse_RdDateTimeDst) isWrapperResponse_Response() {}

func (*WrapperResponse_ClearStats) isWrapperResponse_Response() {}

func (*WrapperResponse_WrDateTimeDst) isWrapperResponse_Response() {}

func (*WrapperResponse_RemoteDisplayButtonEvent) isWrapperResponse_Response() {}

func (*WrapperResponse_StartRealtime) isWrapperResponse_Response() {}

func (*WrapperResponse_StopRealtime) isWrapperResponse_Response() {}

func (*WrapperResponse_ManifestVersions) isWrapperResponse_Response() {}

func (*WrapperResponse_RebootCommsMcu) isWrapperResponse_Response() {}

func (*WrapperResponse_InitiateDfu) isWrapperResponse_Response() {}

func (*WrapperResponse_SendFwManifest) isWrapperResponse_Response() {}

func (*WrapperResponse_BeginFirmwareDownload) isWrapperResponse_Response() {}

func (*WrapperResponse_DownloadFirmwareChunk) isWrapperResponse_Response() {}

func (*WrapperResponse_RealtimeData) isWrapperResponse_Response() {}

func (*WrapperResponse_RealtimeDisplay) isWrapperResponse_Response() {}

func (*WrapperResponse_TestChunk) isWrapperResponse_Response() {}

var File_wrappers_proto protoreflect.FileDescriptor

const file_wrappers_proto_rawDesc = "" +
	"\n" +
	"\x0ewrappers.proto\x12\bwrappers\x1a\x14cmd_resp_comms.proto\x1a\x13cmd_resp_logs.proto\x1a\x15cmd_resp_config.proto\x1a\x14cmd_resp_stats.proto\x1a\x12cmd_resp_dfu.proto\x1a\x17cmd_resp_realtime.proto\"\xd0\x1e\n" +
	"\x0eWrapperCommand\x12\x18\n" +
	"\aversion\x18\x01 \x01(\rR\aversion\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\rR\trequestId\x12R\n" +
	"\x12request_log_counts\x18\x14 \x01(\v2\".cmd_resp_logs.CmdRequestLogCountsH\x00R\x10requestLogCounts\x12@\n" +
	"\tlog_clear\x18\x15 \x01(\v2!.cmd_resp_logs.CmdRequestLogClearH\x00R\blogClear\x12F\n" +
	"\vrequest_log\x18\x16 \x01(\v2#.cmd_resp_logs.CmdRequestLogEntriesH\x00R\n" +
	"requestLog\x12Y\n" +
	"\x13req_auditlog_counts\x18\x17 \x01(\v2'.cmd_resp_logs.CmdRequestAuditLogCountsH\x00R\x11reqAuditlogCounts\x12O\n" +
	"\x0eauditlog_clear\x18\x18 \x01(\v2&.cmd_resp_logs.CmdRequestAuditLogClearH\x00R\rauditlogClear\x12P\n" +
	"\x0faudit_log_reset\x18\x19 \x01(\v2&.cmd_resp_logs.CmdRequestAuditLogResetH\x00R\rauditLogReset\x12U\n" +
	"\x10request_auditlog\x18\x1a \x01(\v2(.cmd_resp_logs.CmdRequestAuditLogEntriesH\x00R\x0frequestAuditlog\x12M\n" +
	"\x0frd_monitor_data\x18\x1f \x01(\v2#.cmd_resp_config.CmdReadMonitorDataH\x00R\rrdMonitorData\x12g\n" +
	"\x16rd_network_config_unit\x18  \x01(\v20.cmd_resp_config.CmdReadUnitNetworkConfigurationH\x00R\x13rdNetworkConfigUnit\x12m\n" +
	"\x18rd_network_config_active\x18! \x01(\v22.cmd_resp_config.CmdReadActiveNetworkConfigurationH\x00R\x15rdNetworkConfigActive\x12]\n" +
	"\x11rd_channel_config\x18\" \x01(\v2/.cmd_resp_config.CmdReadPerChannelConfigurationH\x00R\x0frdChannelConfig\x12q\n" +
	"\x18rd_channel_current_sense\x18# \x01(\v26.cmd_resp_config.CmdReadPerChannelCurrentSenseSettingsH\x00R\x15rdChannelCurrentSense\x12l\n" +
	"\x16rd_channel_permissives\x18$ \x01(\v24.cmd_resp_config.CmdReadPerChannelPermissiveSettingsH\x00R\x14rdChannelPermissives\x12^\n" +
	"\rrd_fya_config\x18% \x01(\v28.cmd_resp_config.CmdReadFlashingYellowArrowConfigurationH\x00R\vrdFyaConfig\x12A\n" +
	"\vrd_data_key\x18& \x01(\v2\x1f.cmd_resp_config.CmdReadDataKeyH\x00R\trdDataKey\x12B\n" +
	"\vwr_data_key\x18' \x01(\v2 .cmd_resp_config.CmdWriteDataKeyH\x00R\twrDataKey\x12Y\n" +
	"\x13rd_factory_settings\x18( \x01(\v2'.cmd_resp_config.CmdReadFactorySettingsH\x00R\x11rdFactorySettings\x12Z\n" +
	"\x13wr_factory_settings\x18) \x01(\v2(.cmd_resp_config.CmdWriteFactorySettingsH\x00R\x11wrFactorySettings\x12P\n" +
	"\x10rd_user_settings\x18* \x01(\v2$.cmd_resp_config.CmdReadUserSettingsH\x00R\x0erdUserSettings\x12Q\n" +
	"\x10wr_user_settings\x18+ \x01(\v2%.cmd_resp_config.CmdWriteUserSettingsH\x00R\x0ewrUserSettings\x12[\n" +
	"\x11rd_port1_disables\x18, \x01(\v2-.cmd_resp_config.CmdReadPort1DisableOverridesH\x00R\x0frdPort1Disables\x12\\\n" +
	"\x11wr_port1_disables\x18- \x01(\v2..cmd_resp_config.CmdWritePort1DisableOverridesH\x00R\x0fwrPort1Disables\x12T\n" +
	"\x11wr_agency_options\x18. \x01(\v2&.cmd_resp_config.CmdWriteAgencyOptionsH\x00R\x0fwrAgencyOptions\x12D\n" +
	"\fremote_reset\x18/ \x01(\v2\x1f.cmd_resp_config.CmdRemoteResetH\x00R\vremoteReset\x12N\n" +
	"\x0erd_port1_stats\x182 \x01(\v2&.cmd_resp_stats.CmdReadPort1StatisticsH\x00R\frdPort1Stats\x12U\n" +
	"\x11rd_data_key_stats\x183 \x01(\v2(.cmd_resp_stats.CmdReadDataKeyStatisticsH\x00R\x0erdDataKeyStats\x12k\n" +
	"\x17rd_main_iso_comms_stats\x184 \x01(\v23.cmd_resp_stats.CmdReadMainToIsolatedCommStatisticsH\x00R\x13rdMainIsoCommsStats\x12a\n" +
	"\x13rd_main_comms_stats\x185 \x01(\v20.cmd_resp_stats.CmdReadMainToCommsCommStatisticsH\x00R\x10rdMainCommsStats\x12a\n" +
	"\x13rd_comms_main_stats\x186 \x01(\v20.cmd_resp_stats.CmdReadCommsToMainCommStatisticsH\x00R\x10rdCommsMainStats\x12N\n" +
	"\x0erd_flash_stats\x187 \x01(\v2&.cmd_resp_stats.CmdReadFlashStatisticsH\x00R\frdFlashStats\x12W\n" +
	"\x11rd_watchdog_stats\x188 \x01(\v2).cmd_resp_stats.CmdReadWatchdogStatisticsH\x00R\x0frdWatchdogStats\x12M\n" +
	"\x10rd_date_time_dst\x189 \x01(\v2\".cmd_resp_stats.CmdGetTimeDatesDstH\x00R\rrdDateTimeDst\x12E\n" +
	"\vclear_stats\x18: \x01(\v2\".cmd_resp_stats.CmdClearStatisticsH\x00R\n" +
	"clearStats\x12M\n" +
	"\x10wr_date_time_dst\x18; \x01(\v2\".cmd_resp_stats.CmdSetTimeDatesDstH\x00R\rwrDateTimeDst\x12l\n" +
	"\x1bremote_display_button_event\x18A \x01(\v2+.cmd_resp_stats.CmdRemoteDisplayButtonEventH\x00R\x18remoteDisplayButtonEvent\x12P\n" +
	"\x0estart_realtime\x18< \x01(\v2'.cmd_resp_realtime.CmdStartRealtimeDataH\x00R\rstartRealtime\x12M\n" +
	"\rstop_realtime\x18= \x01(\v2&.cmd_resp_realtime.CmdStopRealtimeDataH\x00R\fstopRealtime\x12P\n" +
	"\x11manifest_versions\x18F \x01(\v2!.cmd_resp_dfu.CmdManifestVersionsH\x00R\x10manifestVersions\x12K\n" +
	"\x10reboot_comms_mcu\x18G \x01(\v2\x1f.cmd_resp_dfu.CmdRebootCommsMcuH\x00R\x0erebootCommsMcu\x12L\n" +
	"\finitiate_dfu\x18H \x01(\v2'.cmd_resp_dfu.CmdInitiateFirmwareUpdateH\x00R\vinitiateDfu\x12S\n" +
	"\x10send_fw_manifest\x18I \x01(\v2'.cmd_resp_dfu.CmdFirmwareUpdateManifestH\x00R\x0esendFwManifest\x12`\n" +
	"\x17begin_firmware_download\x18J \x01(\v2&.cmd_resp_dfu.CmdBeginFirmwareDownloadH\x00R\x15beginFirmwareDownload\x12`\n" +
	"\x17download_firmware_chunk\x18K \x01(\v2&.cmd_resp_dfu.CmdFirmwareDownloadChunkH\x00R\x15downloadFirmwareChunk\x12=\n" +
	"\n" +
	"test_chunk\x18Z \x01(\v2\x1c.cmd_resp_comms.CmdChunkTestH\x00R\ttestChunkB\t\n" +
	"\acommandJ\x04\b\n" +
	"\x10\vJ\x04\b\v\x10\f\"\xa3!\n" +
	"\x0fWrapperResponse\x12\x18\n" +
	"\aversion\x18\x01 \x01(\rR\aversion\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\rR\trequestId\x12,\n" +
	"\x04code\x18\x03 \x01(\x0e2\x18.wrappers.EResponseCodesR\x04code\x12\x1d\n" +
	"\n" +
	"result_txt\x18\x04 \x01(\tR\tresultTxt\x12A\n" +
	"\vauth_status\x18\v \x01(\v2\x1e.cmd_resp_comms.RespAuthStatusH\x00R\n" +
	"authStatus\x12S\n" +
	"\x12request_log_counts\x18\x14 \x01(\v2#.cmd_resp_logs.RespRequestLogCountsH\x00R\x10requestLogCounts\x12A\n" +
	"\tlog_clear\x18\x15 \x01(\v2\".cmd_resp_logs.RespRequestLogClearH\x00R\blogClear\x12G\n" +
	"\vrequest_log\x18\x16 \x01(\v2$.cmd_resp_logs.RespRequestLogEntriesH\x00R\n" +
	"requestLog\x12Z\n" +
	"\x13req_auditlog_counts\x18\x17 \x01(\v2(.cmd_resp_logs.RespRequestAuditLogCountsH\x00R\x11reqAuditlogCounts\x12P\n" +
	"\x0eauditlog_clear\x18\x18 \x01(\v2'.cmd_resp_logs.RespRequestAuditLogClearH\x00R\rauditlogClear\x12Q\n" +
	"\x0faudit_log_reset\x18\x19 \x01(\v2'.cmd_resp_logs.RespRequestAuditLogResetH\x00R\rauditLogReset\x12V\n" +
	"\x10request_auditlog\x18\x1a \x01(\v2).cmd_resp_logs.RespRequestAuditLogEntriesH\x00R\x0frequestAuditlog\x12N\n" +
	"\x0frd_monitor_data\x18\x1f \x01(\v2$.cmd_resp_config.RespReadMonitorDataH\x00R\rrdMonitorData\x12h\n" +
	"\x16rd_network_config_unit\x18  \x01(\v21.cmd_resp_config.RespReadUnitNetworkConfigurationH\x00R\x13rdNetworkConfigUnit\x12n\n" +
	"\x18rd_network_config_active\x18! \x01(\v23.cmd_resp_config.RespReadActiveNetworkConfigurationH\x00R\x15rdNetworkConfigActive\x12^\n" +
	"\x11rd_channel_config\x18\" \x01(\v20.cmd_resp_config.RespReadPerChannelConfigurationH\x00R\x0frdChannelConfig\x12r\n" +
	"\x18rd_channel_current_sense\x18# \x01(\v27.cmd_resp_config.RespReadPerChannelCurrentSenseSettingsH\x00R\x15rdChannelCurrentSense\x12m\n" +
	"\x16rd_channel_permissives\x18$ \x01(\v25.cmd_resp_config.RespReadPerChannelPermissiveSettingsH\x00R\x14rdChannelPermissives\x12_\n" +
	"\rrd_fya_config\x18% \x01(\v29.cmd_resp_config.RespReadFlashingYellowArrowConfigurationH\x00R\vrdFyaConfig\x12B\n" +
	"\vrd_data_key\x18& \x01(\v2 .cmd_resp_config.RespReadDataKeyH\x00R\trdDataKey\x12C\n" +
	"\vwr_data_key\x18' \x01(\v2!.cmd_resp_config.RespWriteDataKeyH\x00R\twrDataKey\x12Z\n" +
	"\x13rd_factory_settings\x18( \x01(\v2(.cmd_resp_config.RespReadFactorySettingsH\x00R\x11rdFactorySettings\x12[\n" +
	"\x13wr_factory_settings\x18) \x01(\v2).cmd_resp_config.RespWriteFactorySettingsH\x00R\x11wrFactorySettings\x12Q\n" +
	"\x10rd_user_settings\x18* \x01(\v2%.cmd_resp_config.RespReadUserSettingsH\x00R\x0erdUserSettings\x12R\n" +
	"\x10wr_user_settings\x18+ \x01(\v2&.cmd_resp_config.RespWriteUserSettingsH\x00R\x0ewrUserSettings\x12\\\n" +
	"\x11rd_port1_disables\x18, \x01(\v2..cmd_resp_config.RespReadPort1DisableOverridesH\x00R\x0frdPort1Disables\x12]\n" +
	"\x11wr_port1_disables\x18- \x01(\v2/.cmd_resp_config.RespWritePort1DisableOverridesH\x00R\x0fwrPort1Disables\x12U\n" +
	"\x11wr_agency_options\x18. \x01(\v2'.cmd_resp_config.RespWriteAgencyOptionsH\x00R\x0fwrAgencyOptions\x12E\n" +
	"\fremote_reset\x18/ \x01(\v2 .cmd_resp_config.RespRemoteResetH\x00R\vremoteReset\x12O\n" +
	"\x0erd_port1_stats\x182 \x01(\v2'.cmd_resp_stats.RespReadPort1StatisticsH\x00R\frdPort1Stats\x12V\n" +
	"\x11rd_data_key_stats\x183 \x01(\v2).cmd_resp_stats.RespReadDataKeyStatisticsH\x00R\x0erdDataKeyStats\x12l\n" +
	"\x17rd_main_iso_comms_stats\x184 \x01(\v24.cmd_resp_stats.RespReadMainToIsolatedCommStatisticsH\x00R\x13rdMainIsoCommsStats\x12b\n" +
	"\x13rd_main_comms_stats\x185 \x01(\v21.cmd_resp_stats.RespReadMainToCommsCommStatisticsH\x00R\x10rdMainCommsStats\x12b\n" +
	"\x13rd_comms_main_stats\x186 \x01(\v21.cmd_resp_stats.RespReadCommsToMainCommStatisticsH\x00R\x10rdCommsMainStats\x12O\n" +
	"\x0erd_flash_stats\x187 \x01(\v2'.cmd_resp_stats.RespReadFlashStatisticsH\x00R\frdFlashStats\x12X\n" +
	"\x11rd_watchdog_stats\x188 \x01(\v2*.cmd_resp_stats.RespReadWatchdogStatisticsH\x00R\x0frdWatchdogStats\x12N\n" +
	"\x10rd_date_time_dst\x189 \x01(\v2#.cmd_resp_stats.RespGetTimeDatesDstH\x00R\rrdDateTimeDst\x12F\n" +
	"\vclear_stats\x18: \x01(\v2#.cmd_resp_stats.RespClearStatisticsH\x00R\n" +
	"clearStats\x12N\n" +
	"\x10wr_date_time_dst\x18; \x01(\v2#.cmd_resp_stats.RespSetTimeDatesDstH\x00R\rwrDateTimeDst\x12m\n" +
	"\x1bremote_display_button_event\x18A \x01(\v2,.cmd_resp_stats.RespRemoteDisplayButtonEventH\x00R\x18remoteDisplayButtonEvent\x12Q\n" +
	"\x0estart_realtime\x18< \x01(\v2(.cmd_resp_realtime.RespStartRealtimeDataH\x00R\rstartRealtime\x12N\n" +
	"\rstop_realtime\x18= \x01(\v2'.cmd_resp_realtime.RespStopRealtimeDataH\x00R\fstopRealtime\x12Q\n" +
	"\x11manifest_versions\x18F \x01(\v2\".cmd_resp_dfu.RespManifestVersionsH\x00R\x10manifestVersions\x12L\n" +
	"\x10reboot_comms_mcu\x18G \x01(\v2 .cmd_resp_dfu.RespRebootCommsMcuH\x00R\x0erebootCommsMcu\x12M\n" +
	"\finitiate_dfu\x18H \x01(\v2(.cmd_resp_dfu.RespInitiateFirmwareUpdateH\x00R\vinitiateDfu\x12T\n" +
	"\x10send_fw_manifest\x18I \x01(\v2(.cmd_resp_dfu.RespFirmwareUpdateManifestH\x00R\x0esendFwManifest\x12a\n" +
	"\x17begin_firmware_download\x18J \x01(\v2'.cmd_resp_dfu.RespBeginFirmwareDownloadH\x00R\x15beginFirmwareDownload\x12a\n" +
	"\x17download_firmware_chunk\x18K \x01(\v2'.cmd_resp_dfu.RespFirmwareDownloadChunkH\x00R\x15downloadFirmwareChunk\x12G\n" +
	"\rrealtime_data\x18P \x01(\v2 .cmd_resp_realtime.RealtimeData1H\x00R\frealtimeData\x12P\n" +
	"\x10realtime_display\x18Q \x01(\v2#.cmd_resp_realtime.RealtimeDisplay1H\x00R\x0frealtimeDisplay\x12>\n" +
	"\n" +
	"test_chunk\x18Z \x01(\v2\x1d.cmd_resp_comms.RespChunkTestH\x00R\ttestChunkB\n" +
	"\n" +
	"\bresponseJ\x04\b\n" +
	"\x10\v*\xe3\x02\n" +
	"\x0eEResponseCodes\x12\x14\n" +
	"\x10RESP_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fRESP_SUCCESS\x10\x01\x12\x13\n" +
	"\x0fRESP_PROHIBITED\x10\x02\x12\x0f\n" +
	"\vRESP_FAILED\x10\x03\x12\x14\n" +
	"\x10RESP_CMD_UNKNOWN\x10\x04\x12\x12\n" +
	"\x0eRESP_BAD_VALUE\x10\x05\x12\x14\n" +
	"\x10RESP_BAD_CHANNEL\x10\x06\x12\x12\n" +
	"\x0eRESP_CMD_ERROR\x10\a\x12\x18\n" +
	"\x14RESP_OPTION_MISMATCH\x10\b\x12\x14\n" +
	"\x10RESP_UNAVAILABLE\x10\t\x12\r\n" +
	"\tRESP_BUSY\x10\n" +
	"\x12\x10\n" +
	"\fRESP_TIMEOUT\x10\v\x12\x15\n" +
	"\x11RESP_SEQUENCE_ERR\x10\f\x12\x15\n" +
	"\x11RESP_NOT_WRITABLE\x10\r\x12\x14\n" +
	"\x10RESP_DEVICE_NACK\x10\x0e\x12\x1a\n" +
	"\x16RESP_FILE_SYSTEM_ERROR\x10\x0fb\x06proto3"

var (
	file_wrappers_proto_rawDescOnce sync.Once
	file_wrappers_proto_rawDescData []byte
)

func file_wrappers_proto_rawDescGZIP() []byte {
	file_wrappers_proto_rawDescOnce.Do(func() {
		file_wrappers_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_wrappers_proto_rawDesc), len(file_wrappers_proto_rawDesc)))
	})
	return file_wrappers_proto_rawDescData
}

var file_wrappers_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_wrappers_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_wrappers_proto_goTypes = []any{
	(EResponseCodes)(0),                                              // 0: wrappers.EResponseCodes
	(*WrapperCommand)(nil),                                           // 1: wrappers.WrapperCommand
	(*WrapperResponse)(nil),                                          // 2: wrappers.WrapperResponse
	(*cmd_resp_logs.CmdRequestLogCounts)(nil),                        // 3: cmd_resp_logs.CmdRequestLogCounts
	(*cmd_resp_logs.CmdRequestLogClear)(nil),                         // 4: cmd_resp_logs.CmdRequestLogClear
	(*cmd_resp_logs.CmdRequestLogEntries)(nil),                       // 5: cmd_resp_logs.CmdRequestLogEntries
	(*cmd_resp_logs.CmdRequestAuditLogCounts)(nil),                   // 6: cmd_resp_logs.CmdRequestAuditLogCounts
	(*cmd_resp_logs.CmdRequestAuditLogClear)(nil),                    // 7: cmd_resp_logs.CmdRequestAuditLogClear
	(*cmd_resp_logs.CmdRequestAuditLogReset)(nil),                    // 8: cmd_resp_logs.CmdRequestAuditLogReset
	(*cmd_resp_logs.CmdRequestAuditLogEntries)(nil),                  // 9: cmd_resp_logs.CmdRequestAuditLogEntries
	(*cmd_resp_config.CmdReadMonitorData)(nil),                       // 10: cmd_resp_config.CmdReadMonitorData
	(*cmd_resp_config.CmdReadUnitNetworkConfiguration)(nil),          // 11: cmd_resp_config.CmdReadUnitNetworkConfiguration
	(*cmd_resp_config.CmdReadActiveNetworkConfiguration)(nil),        // 12: cmd_resp_config.CmdReadActiveNetworkConfiguration
	(*cmd_resp_config.CmdReadPerChannelConfiguration)(nil),           // 13: cmd_resp_config.CmdReadPerChannelConfiguration
	(*cmd_resp_config.CmdReadPerChannelCurrentSenseSettings)(nil),    // 14: cmd_resp_config.CmdReadPerChannelCurrentSenseSettings
	(*cmd_resp_config.CmdReadPerChannelPermissiveSettings)(nil),      // 15: cmd_resp_config.CmdReadPerChannelPermissiveSettings
	(*cmd_resp_config.CmdReadFlashingYellowArrowConfiguration)(nil),  // 16: cmd_resp_config.CmdReadFlashingYellowArrowConfiguration
	(*cmd_resp_config.CmdReadDataKey)(nil),                           // 17: cmd_resp_config.CmdReadDataKey
	(*cmd_resp_config.CmdWriteDataKey)(nil),                          // 18: cmd_resp_config.CmdWriteDataKey
	(*cmd_resp_config.CmdReadFactorySettings)(nil),                   // 19: cmd_resp_config.CmdReadFactorySettings
	(*cmd_resp_config.CmdWriteFactorySettings)(nil),                  // 20: cmd_resp_config.CmdWriteFactorySettings
	(*cmd_resp_config.CmdReadUserSettings)(nil),                      // 21: cmd_resp_config.CmdReadUserSettings
	(*cmd_resp_config.CmdWriteUserSettings)(nil),                     // 22: cmd_resp_config.CmdWriteUserSettings
	(*cmd_resp_config.CmdReadPort1DisableOverrides)(nil),             // 23: cmd_resp_config.CmdReadPort1DisableOverrides
	(*cmd_resp_config.CmdWritePort1DisableOverrides)(nil),            // 24: cmd_resp_config.CmdWritePort1DisableOverrides
	(*cmd_resp_config.CmdWriteAgencyOptions)(nil),                    // 25: cmd_resp_config.CmdWriteAgencyOptions
	(*cmd_resp_config.CmdRemoteReset)(nil),                           // 26: cmd_resp_config.CmdRemoteReset
	(*cmd_resp_stats.CmdReadPort1Statistics)(nil),                    // 27: cmd_resp_stats.CmdReadPort1Statistics
	(*cmd_resp_stats.CmdReadDataKeyStatistics)(nil),                  // 28: cmd_resp_stats.CmdReadDataKeyStatistics
	(*cmd_resp_stats.CmdReadMainToIsolatedCommStatistics)(nil),       // 29: cmd_resp_stats.CmdReadMainToIsolatedCommStatistics
	(*cmd_resp_stats.CmdReadMainToCommsCommStatistics)(nil),          // 30: cmd_resp_stats.CmdReadMainToCommsCommStatistics
	(*cmd_resp_stats.CmdReadCommsToMainCommStatistics)(nil),          // 31: cmd_resp_stats.CmdReadCommsToMainCommStatistics
	(*cmd_resp_stats.CmdReadFlashStatistics)(nil),                    // 32: cmd_resp_stats.CmdReadFlashStatistics
	(*cmd_resp_stats.CmdReadWatchdogStatistics)(nil),                 // 33: cmd_resp_stats.CmdReadWatchdogStatistics
	(*cmd_resp_stats.CmdGetTimeDatesDst)(nil),                        // 34: cmd_resp_stats.CmdGetTimeDatesDst
	(*cmd_resp_stats.CmdClearStatistics)(nil),                        // 35: cmd_resp_stats.CmdClearStatistics
	(*cmd_resp_stats.CmdSetTimeDatesDst)(nil),                        // 36: cmd_resp_stats.CmdSetTimeDatesDst
	(*cmd_resp_stats.CmdRemoteDisplayButtonEvent)(nil),               // 37: cmd_resp_stats.CmdRemoteDisplayButtonEvent
	(*cmd_resp_realtime.CmdStartRealtimeData)(nil),                   // 38: cmd_resp_realtime.CmdStartRealtimeData
	(*cmd_resp_realtime.CmdStopRealtimeData)(nil),                    // 39: cmd_resp_realtime.CmdStopRealtimeData
	(*cmd_resp_dfu.CmdManifestVersions)(nil),                         // 40: cmd_resp_dfu.CmdManifestVersions
	(*cmd_resp_dfu.CmdRebootCommsMcu)(nil),                           // 41: cmd_resp_dfu.CmdRebootCommsMcu
	(*cmd_resp_dfu.CmdInitiateFirmwareUpdate)(nil),                   // 42: cmd_resp_dfu.CmdInitiateFirmwareUpdate
	(*cmd_resp_dfu.CmdFirmwareUpdateManifest)(nil),                   // 43: cmd_resp_dfu.CmdFirmwareUpdateManifest
	(*cmd_resp_dfu.CmdBeginFirmwareDownload)(nil),                    // 44: cmd_resp_dfu.CmdBeginFirmwareDownload
	(*cmd_resp_dfu.CmdFirmwareDownloadChunk)(nil),                    // 45: cmd_resp_dfu.CmdFirmwareDownloadChunk
	(*cmd_resp_comms.CmdChunkTest)(nil),                              // 46: cmd_resp_comms.CmdChunkTest
	(*cmd_resp_comms.RespAuthStatus)(nil),                            // 47: cmd_resp_comms.RespAuthStatus
	(*cmd_resp_logs.RespRequestLogCounts)(nil),                       // 48: cmd_resp_logs.RespRequestLogCounts
	(*cmd_resp_logs.RespRequestLogClear)(nil),                        // 49: cmd_resp_logs.RespRequestLogClear
	(*cmd_resp_logs.RespRequestLogEntries)(nil),                      // 50: cmd_resp_logs.RespRequestLogEntries
	(*cmd_resp_logs.RespRequestAuditLogCounts)(nil),                  // 51: cmd_resp_logs.RespRequestAuditLogCounts
	(*cmd_resp_logs.RespRequestAuditLogClear)(nil),                   // 52: cmd_resp_logs.RespRequestAuditLogClear
	(*cmd_resp_logs.RespRequestAuditLogReset)(nil),                   // 53: cmd_resp_logs.RespRequestAuditLogReset
	(*cmd_resp_logs.RespRequestAuditLogEntries)(nil),                 // 54: cmd_resp_logs.RespRequestAuditLogEntries
	(*cmd_resp_config.RespReadMonitorData)(nil),                      // 55: cmd_resp_config.RespReadMonitorData
	(*cmd_resp_config.RespReadUnitNetworkConfiguration)(nil),         // 56: cmd_resp_config.RespReadUnitNetworkConfiguration
	(*cmd_resp_config.RespReadActiveNetworkConfiguration)(nil),       // 57: cmd_resp_config.RespReadActiveNetworkConfiguration
	(*cmd_resp_config.RespReadPerChannelConfiguration)(nil),          // 58: cmd_resp_config.RespReadPerChannelConfiguration
	(*cmd_resp_config.RespReadPerChannelCurrentSenseSettings)(nil),   // 59: cmd_resp_config.RespReadPerChannelCurrentSenseSettings
	(*cmd_resp_config.RespReadPerChannelPermissiveSettings)(nil),     // 60: cmd_resp_config.RespReadPerChannelPermissiveSettings
	(*cmd_resp_config.RespReadFlashingYellowArrowConfiguration)(nil), // 61: cmd_resp_config.RespReadFlashingYellowArrowConfiguration
	(*cmd_resp_config.RespReadDataKey)(nil),                          // 62: cmd_resp_config.RespReadDataKey
	(*cmd_resp_config.RespWriteDataKey)(nil),                         // 63: cmd_resp_config.RespWriteDataKey
	(*cmd_resp_config.RespReadFactorySettings)(nil),                  // 64: cmd_resp_config.RespReadFactorySettings
	(*cmd_resp_config.RespWriteFactorySettings)(nil),                 // 65: cmd_resp_config.RespWriteFactorySettings
	(*cmd_resp_config.RespReadUserSettings)(nil),                     // 66: cmd_resp_config.RespReadUserSettings
	(*cmd_resp_config.RespWriteUserSettings)(nil),                    // 67: cmd_resp_config.RespWriteUserSettings
	(*cmd_resp_config.RespReadPort1DisableOverrides)(nil),            // 68: cmd_resp_config.RespReadPort1DisableOverrides
	(*cmd_resp_config.RespWritePort1DisableOverrides)(nil),           // 69: cmd_resp_config.RespWritePort1DisableOverrides
	(*cmd_resp_config.RespWriteAgencyOptions)(nil),                   // 70: cmd_resp_config.RespWriteAgencyOptions
	(*cmd_resp_config.RespRemoteReset)(nil),                          // 71: cmd_resp_config.RespRemoteReset
	(*cmd_resp_stats.RespReadPort1Statistics)(nil),                   // 72: cmd_resp_stats.RespReadPort1Statistics
	(*cmd_resp_stats.RespReadDataKeyStatistics)(nil),                 // 73: cmd_resp_stats.RespReadDataKeyStatistics
	(*cmd_resp_stats.RespReadMainToIsolatedCommStatistics)(nil),      // 74: cmd_resp_stats.RespReadMainToIsolatedCommStatistics
	(*cmd_resp_stats.RespReadMainToCommsCommStatistics)(nil),         // 75: cmd_resp_stats.RespReadMainToCommsCommStatistics
	(*cmd_resp_stats.RespReadCommsToMainCommStatistics)(nil),         // 76: cmd_resp_stats.RespReadCommsToMainCommStatistics
	(*cmd_resp_stats.RespReadFlashStatistics)(nil),                   // 77: cmd_resp_stats.RespReadFlashStatistics
	(*cmd_resp_stats.RespReadWatchdogStatistics)(nil),                // 78: cmd_resp_stats.RespReadWatchdogStatistics
	(*cmd_resp_stats.RespGetTimeDatesDst)(nil),                       // 79: cmd_resp_stats.RespGetTimeDatesDst
	(*cmd_resp_stats.RespClearStatistics)(nil),                       // 80: cmd_resp_stats.RespClearStatistics
	(*cmd_resp_stats.RespSetTimeDatesDst)(nil),                       // 81: cmd_resp_stats.RespSetTimeDatesDst
	(*cmd_resp_stats.RespRemoteDisplayButtonEvent)(nil),              // 82: cmd_resp_stats.RespRemoteDisplayButtonEvent
	(*cmd_resp_realtime.RespStartRealtimeData)(nil),                  // 83: cmd_resp_realtime.RespStartRealtimeData
	(*cmd_resp_realtime.RespStopRealtimeData)(nil),                   // 84: cmd_resp_realtime.RespStopRealtimeData
	(*cmd_resp_dfu.RespManifestVersions)(nil),                        // 85: cmd_resp_dfu.RespManifestVersions
	(*cmd_resp_dfu.RespRebootCommsMcu)(nil),                          // 86: cmd_resp_dfu.RespRebootCommsMcu
	(*cmd_resp_dfu.RespInitiateFirmwareUpdate)(nil),                  // 87: cmd_resp_dfu.RespInitiateFirmwareUpdate
	(*cmd_resp_dfu.RespFirmwareUpdateManifest)(nil),                  // 88: cmd_resp_dfu.RespFirmwareUpdateManifest
	(*cmd_resp_dfu.RespBeginFirmwareDownload)(nil),                   // 89: cmd_resp_dfu.RespBeginFirmwareDownload
	(*cmd_resp_dfu.RespFirmwareDownloadChunk)(nil),                   // 90: cmd_resp_dfu.RespFirmwareDownloadChunk
	(*cmd_resp_realtime.RealtimeData1)(nil),                          // 91: cmd_resp_realtime.RealtimeData1
	(*cmd_resp_realtime.RealtimeDisplay1)(nil),                       // 92: cmd_resp_realtime.RealtimeDisplay1
	(*cmd_resp_comms.RespChunkTest)(nil),                             // 93: cmd_resp_comms.RespChunkTest
}
var file_wrappers_proto_depIdxs = []int32{
	3,  // 0: wrappers.WrapperCommand.request_log_counts:type_name -> cmd_resp_logs.CmdRequestLogCounts
	4,  // 1: wrappers.WrapperCommand.log_clear:type_name -> cmd_resp_logs.CmdRequestLogClear
	5,  // 2: wrappers.WrapperCommand.request_log:type_name -> cmd_resp_logs.CmdRequestLogEntries
	6,  // 3: wrappers.WrapperCommand.req_auditlog_counts:type_name -> cmd_resp_logs.CmdRequestAuditLogCounts
	7,  // 4: wrappers.WrapperCommand.auditlog_clear:type_name -> cmd_resp_logs.CmdRequestAuditLogClear
	8,  // 5: wrappers.WrapperCommand.audit_log_reset:type_name -> cmd_resp_logs.CmdRequestAuditLogReset
	9,  // 6: wrappers.WrapperCommand.request_auditlog:type_name -> cmd_resp_logs.CmdRequestAuditLogEntries
	10, // 7: wrappers.WrapperCommand.rd_monitor_data:type_name -> cmd_resp_config.CmdReadMonitorData
	11, // 8: wrappers.WrapperCommand.rd_network_config_unit:type_name -> cmd_resp_config.CmdReadUnitNetworkConfiguration
	12, // 9: wrappers.WrapperCommand.rd_network_config_active:type_name -> cmd_resp_config.CmdReadActiveNetworkConfiguration
	13, // 10: wrappers.WrapperCommand.rd_channel_config:type_name -> cmd_resp_config.CmdReadPerChannelConfiguration
	14, // 11: wrappers.WrapperCommand.rd_channel_current_sense:type_name -> cmd_resp_config.CmdReadPerChannelCurrentSenseSettings
	15, // 12: wrappers.WrapperCommand.rd_channel_permissives:type_name -> cmd_resp_config.CmdReadPerChannelPermissiveSettings
	16, // 13: wrappers.WrapperCommand.rd_fya_config:type_name -> cmd_resp_config.CmdReadFlashingYellowArrowConfiguration
	17, // 14: wrappers.WrapperCommand.rd_data_key:type_name -> cmd_resp_config.CmdReadDataKey
	18, // 15: wrappers.WrapperCommand.wr_data_key:type_name -> cmd_resp_config.CmdWriteDataKey
	19, // 16: wrappers.WrapperCommand.rd_factory_settings:type_name -> cmd_resp_config.CmdReadFactorySettings
	20, // 17: wrappers.WrapperCommand.wr_factory_settings:type_name -> cmd_resp_config.CmdWriteFactorySettings
	21, // 18: wrappers.WrapperCommand.rd_user_settings:type_name -> cmd_resp_config.CmdReadUserSettings
	22, // 19: wrappers.WrapperCommand.wr_user_settings:type_name -> cmd_resp_config.CmdWriteUserSettings
	23, // 20: wrappers.WrapperCommand.rd_port1_disables:type_name -> cmd_resp_config.CmdReadPort1DisableOverrides
	24, // 21: wrappers.WrapperCommand.wr_port1_disables:type_name -> cmd_resp_config.CmdWritePort1DisableOverrides
	25, // 22: wrappers.WrapperCommand.wr_agency_options:type_name -> cmd_resp_config.CmdWriteAgencyOptions
	26, // 23: wrappers.WrapperCommand.remote_reset:type_name -> cmd_resp_config.CmdRemoteReset
	27, // 24: wrappers.WrapperCommand.rd_port1_stats:type_name -> cmd_resp_stats.CmdReadPort1Statistics
	28, // 25: wrappers.WrapperCommand.rd_data_key_stats:type_name -> cmd_resp_stats.CmdReadDataKeyStatistics
	29, // 26: wrappers.WrapperCommand.rd_main_iso_comms_stats:type_name -> cmd_resp_stats.CmdReadMainToIsolatedCommStatistics
	30, // 27: wrappers.WrapperCommand.rd_main_comms_stats:type_name -> cmd_resp_stats.CmdReadMainToCommsCommStatistics
	31, // 28: wrappers.WrapperCommand.rd_comms_main_stats:type_name -> cmd_resp_stats.CmdReadCommsToMainCommStatistics
	32, // 29: wrappers.WrapperCommand.rd_flash_stats:type_name -> cmd_resp_stats.CmdReadFlashStatistics
	33, // 30: wrappers.WrapperCommand.rd_watchdog_stats:type_name -> cmd_resp_stats.CmdReadWatchdogStatistics
	34, // 31: wrappers.WrapperCommand.rd_date_time_dst:type_name -> cmd_resp_stats.CmdGetTimeDatesDst
	35, // 32: wrappers.WrapperCommand.clear_stats:type_name -> cmd_resp_stats.CmdClearStatistics
	36, // 33: wrappers.WrapperCommand.wr_date_time_dst:type_name -> cmd_resp_stats.CmdSetTimeDatesDst
	37, // 34: wrappers.WrapperCommand.remote_display_button_event:type_name -> cmd_resp_stats.CmdRemoteDisplayButtonEvent
	38, // 35: wrappers.WrapperCommand.start_realtime:type_name -> cmd_resp_realtime.CmdStartRealtimeData
	39, // 36: wrappers.WrapperCommand.stop_realtime:type_name -> cmd_resp_realtime.CmdStopRealtimeData
	40, // 37: wrappers.WrapperCommand.manifest_versions:type_name -> cmd_resp_dfu.CmdManifestVersions
	41, // 38: wrappers.WrapperCommand.reboot_comms_mcu:type_name -> cmd_resp_dfu.CmdRebootCommsMcu
	42, // 39: wrappers.WrapperCommand.initiate_dfu:type_name -> cmd_resp_dfu.CmdInitiateFirmwareUpdate
	43, // 40: wrappers.WrapperCommand.send_fw_manifest:type_name -> cmd_resp_dfu.CmdFirmwareUpdateManifest
	44, // 41: wrappers.WrapperCommand.begin_firmware_download:type_name -> cmd_resp_dfu.CmdBeginFirmwareDownload
	45, // 42: wrappers.WrapperCommand.download_firmware_chunk:type_name -> cmd_resp_dfu.CmdFirmwareDownloadChunk
	46, // 43: wrappers.WrapperCommand.test_chunk:type_name -> cmd_resp_comms.CmdChunkTest
	0,  // 44: wrappers.WrapperResponse.code:type_name -> wrappers.EResponseCodes
	47, // 45: wrappers.WrapperResponse.auth_status:type_name -> cmd_resp_comms.RespAuthStatus
	48, // 46: wrappers.WrapperResponse.request_log_counts:type_name -> cmd_resp_logs.RespRequestLogCounts
	49, // 47: wrappers.WrapperResponse.log_clear:type_name -> cmd_resp_logs.RespRequestLogClear
	50, // 48: wrappers.WrapperResponse.request_log:type_name -> cmd_resp_logs.RespRequestLogEntries
	51, // 49: wrappers.WrapperResponse.req_auditlog_counts:type_name -> cmd_resp_logs.RespRequestAuditLogCounts
	52, // 50: wrappers.WrapperResponse.auditlog_clear:type_name -> cmd_resp_logs.RespRequestAuditLogClear
	53, // 51: wrappers.WrapperResponse.audit_log_reset:type_name -> cmd_resp_logs.RespRequestAuditLogReset
	54, // 52: wrappers.WrapperResponse.request_auditlog:type_name -> cmd_resp_logs.RespRequestAuditLogEntries
	55, // 53: wrappers.WrapperResponse.rd_monitor_data:type_name -> cmd_resp_config.RespReadMonitorData
	56, // 54: wrappers.WrapperResponse.rd_network_config_unit:type_name -> cmd_resp_config.RespReadUnitNetworkConfiguration
	57, // 55: wrappers.WrapperResponse.rd_network_config_active:type_name -> cmd_resp_config.RespReadActiveNetworkConfiguration
	58, // 56: wrappers.WrapperResponse.rd_channel_config:type_name -> cmd_resp_config.RespReadPerChannelConfiguration
	59, // 57: wrappers.WrapperResponse.rd_channel_current_sense:type_name -> cmd_resp_config.RespReadPerChannelCurrentSenseSettings
	60, // 58: wrappers.WrapperResponse.rd_channel_permissives:type_name -> cmd_resp_config.RespReadPerChannelPermissiveSettings
	61, // 59: wrappers.WrapperResponse.rd_fya_config:type_name -> cmd_resp_config.RespReadFlashingYellowArrowConfiguration
	62, // 60: wrappers.WrapperResponse.rd_data_key:type_name -> cmd_resp_config.RespReadDataKey
	63, // 61: wrappers.WrapperResponse.wr_data_key:type_name -> cmd_resp_config.RespWriteDataKey
	64, // 62: wrappers.WrapperResponse.rd_factory_settings:type_name -> cmd_resp_config.RespReadFactorySettings
	65, // 63: wrappers.WrapperResponse.wr_factory_settings:type_name -> cmd_resp_config.RespWriteFactorySettings
	66, // 64: wrappers.WrapperResponse.rd_user_settings:type_name -> cmd_resp_config.RespReadUserSettings
	67, // 65: wrappers.WrapperResponse.wr_user_settings:type_name -> cmd_resp_config.RespWriteUserSettings
	68, // 66: wrappers.WrapperResponse.rd_port1_disables:type_name -> cmd_resp_config.RespReadPort1DisableOverrides
	69, // 67: wrappers.WrapperResponse.wr_port1_disables:type_name -> cmd_resp_config.RespWritePort1DisableOverrides
	70, // 68: wrappers.WrapperResponse.wr_agency_options:type_name -> cmd_resp_config.RespWriteAgencyOptions
	71, // 69: wrappers.WrapperResponse.remote_reset:type_name -> cmd_resp_config.RespRemoteReset
	72, // 70: wrappers.WrapperResponse.rd_port1_stats:type_name -> cmd_resp_stats.RespReadPort1Statistics
	73, // 71: wrappers.WrapperResponse.rd_data_key_stats:type_name -> cmd_resp_stats.RespReadDataKeyStatistics
	74, // 72: wrappers.WrapperResponse.rd_main_iso_comms_stats:type_name -> cmd_resp_stats.RespReadMainToIsolatedCommStatistics
	75, // 73: wrappers.WrapperResponse.rd_main_comms_stats:type_name -> cmd_resp_stats.RespReadMainToCommsCommStatistics
	76, // 74: wrappers.WrapperResponse.rd_comms_main_stats:type_name -> cmd_resp_stats.RespReadCommsToMainCommStatistics
	77, // 75: wrappers.WrapperResponse.rd_flash_stats:type_name -> cmd_resp_stats.RespReadFlashStatistics
	78, // 76: wrappers.WrapperResponse.rd_watchdog_stats:type_name -> cmd_resp_stats.RespReadWatchdogStatistics
	79, // 77: wrappers.WrapperResponse.rd_date_time_dst:type_name -> cmd_resp_stats.RespGetTimeDatesDst
	80, // 78: wrappers.WrapperResponse.clear_stats:type_name -> cmd_resp_stats.RespClearStatistics
	81, // 79: wrappers.WrapperResponse.wr_date_time_dst:type_name -> cmd_resp_stats.RespSetTimeDatesDst
	82, // 80: wrappers.WrapperResponse.remote_display_button_event:type_name -> cmd_resp_stats.RespRemoteDisplayButtonEvent
	83, // 81: wrappers.WrapperResponse.start_realtime:type_name -> cmd_resp_realtime.RespStartRealtimeData
	84, // 82: wrappers.WrapperResponse.stop_realtime:type_name -> cmd_resp_realtime.RespStopRealtimeData
	85, // 83: wrappers.WrapperResponse.manifest_versions:type_name -> cmd_resp_dfu.RespManifestVersions
	86, // 84: wrappers.WrapperResponse.reboot_comms_mcu:type_name -> cmd_resp_dfu.RespRebootCommsMcu
	87, // 85: wrappers.WrapperResponse.initiate_dfu:type_name -> cmd_resp_dfu.RespInitiateFirmwareUpdate
	88, // 86: wrappers.WrapperResponse.send_fw_manifest:type_name -> cmd_resp_dfu.RespFirmwareUpdateManifest
	89, // 87: wrappers.WrapperResponse.begin_firmware_download:type_name -> cmd_resp_dfu.RespBeginFirmwareDownload
	90, // 88: wrappers.WrapperResponse.download_firmware_chunk:type_name -> cmd_resp_dfu.RespFirmwareDownloadChunk
	91, // 89: wrappers.WrapperResponse.realtime_data:type_name -> cmd_resp_realtime.RealtimeData1
	92, // 90: wrappers.WrapperResponse.realtime_display:type_name -> cmd_resp_realtime.RealtimeDisplay1
	93, // 91: wrappers.WrapperResponse.test_chunk:type_name -> cmd_resp_comms.RespChunkTest
	92, // [92:92] is the sub-list for method output_type
	92, // [92:92] is the sub-list for method input_type
	92, // [92:92] is the sub-list for extension type_name
	92, // [92:92] is the sub-list for extension extendee
	0,  // [0:92] is the sub-list for field type_name
}

func init() { file_wrappers_proto_init() }
func file_wrappers_proto_init() {
	if File_wrappers_proto != nil {
		return
	}
	file_wrappers_proto_msgTypes[0].OneofWrappers = []any{
		(*WrapperCommand_RequestLogCounts)(nil),
		(*WrapperCommand_LogClear)(nil),
		(*WrapperCommand_RequestLog)(nil),
		(*WrapperCommand_ReqAuditlogCounts)(nil),
		(*WrapperCommand_AuditlogClear)(nil),
		(*WrapperCommand_AuditLogReset)(nil),
		(*WrapperCommand_RequestAuditlog)(nil),
		(*WrapperCommand_RdMonitorData)(nil),
		(*WrapperCommand_RdNetworkConfigUnit)(nil),
		(*WrapperCommand_RdNetworkConfigActive)(nil),
		(*WrapperCommand_RdChannelConfig)(nil),
		(*WrapperCommand_RdChannelCurrentSense)(nil),
		(*WrapperCommand_RdChannelPermissives)(nil),
		(*WrapperCommand_RdFyaConfig)(nil),
		(*WrapperCommand_RdDataKey)(nil),
		(*WrapperCommand_WrDataKey)(nil),
		(*WrapperCommand_RdFactorySettings)(nil),
		(*WrapperCommand_WrFactorySettings)(nil),
		(*WrapperCommand_RdUserSettings)(nil),
		(*WrapperCommand_WrUserSettings)(nil),
		(*WrapperCommand_RdPort1Disables)(nil),
		(*WrapperCommand_WrPort1Disables)(nil),
		(*WrapperCommand_WrAgencyOptions)(nil),
		(*WrapperCommand_RemoteReset)(nil),
		(*WrapperCommand_RdPort1Stats)(nil),
		(*WrapperCommand_RdDataKeyStats)(nil),
		(*WrapperCommand_RdMainIsoCommsStats)(nil),
		(*WrapperCommand_RdMainCommsStats)(nil),
		(*WrapperCommand_RdCommsMainStats)(nil),
		(*WrapperCommand_RdFlashStats)(nil),
		(*WrapperCommand_RdWatchdogStats)(nil),
		(*WrapperCommand_RdDateTimeDst)(nil),
		(*WrapperCommand_ClearStats)(nil),
		(*WrapperCommand_WrDateTimeDst)(nil),
		(*WrapperCommand_RemoteDisplayButtonEvent)(nil),
		(*WrapperCommand_StartRealtime)(nil),
		(*WrapperCommand_StopRealtime)(nil),
		(*WrapperCommand_ManifestVersions)(nil),
		(*WrapperCommand_RebootCommsMcu)(nil),
		(*WrapperCommand_InitiateDfu)(nil),
		(*WrapperCommand_SendFwManifest)(nil),
		(*WrapperCommand_BeginFirmwareDownload)(nil),
		(*WrapperCommand_DownloadFirmwareChunk)(nil),
		(*WrapperCommand_TestChunk)(nil),
	}
	file_wrappers_proto_msgTypes[1].OneofWrappers = []any{
		(*WrapperResponse_AuthStatus)(nil),
		(*WrapperResponse_RequestLogCounts)(nil),
		(*WrapperResponse_LogClear)(nil),
		(*WrapperResponse_RequestLog)(nil),
		(*WrapperResponse_ReqAuditlogCounts)(nil),
		(*WrapperResponse_AuditlogClear)(nil),
		(*WrapperResponse_AuditLogReset)(nil),
		(*WrapperResponse_RequestAuditlog)(nil),
		(*WrapperResponse_RdMonitorData)(nil),
		(*WrapperResponse_RdNetworkConfigUnit)(nil),
		(*WrapperResponse_RdNetworkConfigActive)(nil),
		(*WrapperResponse_RdChannelConfig)(nil),
		(*WrapperResponse_RdChannelCurrentSense)(nil),
		(*WrapperResponse_RdChannelPermissives)(nil),
		(*WrapperResponse_RdFyaConfig)(nil),
		(*WrapperResponse_RdDataKey)(nil),
		(*WrapperResponse_WrDataKey)(nil),
		(*WrapperResponse_RdFactorySettings)(nil),
		(*WrapperResponse_WrFactorySettings)(nil),
		(*WrapperResponse_RdUserSettings)(nil),
		(*WrapperResponse_WrUserSettings)(nil),
		(*WrapperResponse_RdPort1Disables)(nil),
		(*WrapperResponse_WrPort1Disables)(nil),
		(*WrapperResponse_WrAgencyOptions)(nil),
		(*WrapperResponse_RemoteReset)(nil),
		(*WrapperResponse_RdPort1Stats)(nil),
		(*WrapperResponse_RdDataKeyStats)(nil),
		(*WrapperResponse_RdMainIsoCommsStats)(nil),
		(*WrapperResponse_RdMainCommsStats)(nil),
		(*WrapperResponse_RdCommsMainStats)(nil),
		(*WrapperResponse_RdFlashStats)(nil),
		(*WrapperResponse_RdWatchdogStats)(nil),
		(*WrapperResponse_RdDateTimeDst)(nil),
		(*WrapperResponse_ClearStats)(nil),
		(*WrapperResponse_WrDateTimeDst)(nil),
		(*WrapperResponse_RemoteDisplayButtonEvent)(nil),
		(*WrapperResponse_StartRealtime)(nil),
		(*WrapperResponse_StopRealtime)(nil),
		(*WrapperResponse_ManifestVersions)(nil),
		(*WrapperResponse_RebootCommsMcu)(nil),
		(*WrapperResponse_InitiateDfu)(nil),
		(*WrapperResponse_SendFwManifest)(nil),
		(*WrapperResponse_BeginFirmwareDownload)(nil),
		(*WrapperResponse_DownloadFirmwareChunk)(nil),
		(*WrapperResponse_RealtimeData)(nil),
		(*WrapperResponse_RealtimeDisplay)(nil),
		(*WrapperResponse_TestChunk)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_wrappers_proto_rawDesc), len(file_wrappers_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_wrappers_proto_goTypes,
		DependencyIndexes: file_wrappers_proto_depIdxs,
		EnumInfos:         file_wrappers_proto_enumTypes,
		MessageInfos:      file_wrappers_proto_msgTypes,
	}.Build()
	File_wrappers_proto = out.File
	file_wrappers_proto_goTypes = nil
	file_wrappers_proto_depIdxs = nil
}
