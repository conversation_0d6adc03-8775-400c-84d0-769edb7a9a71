//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  SETTINGS
//This file contains device settings and response codes not part of the logging system.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: settings.proto

package settings

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EStatusDisplay possible status displays .
type EStatusDisplay int32

const (
	EStatusDisplay_STATUS_DISPLAY_UNSPECIFIED EStatusDisplay = 0
	EStatusDisplay_STATUS_DISPLAY_MAIN        EStatusDisplay = 1
	EStatusDisplay_STATUS_DISPLAY_FAULT       EStatusDisplay = 2
	EStatusDisplay_STATUS_DISPLAY_ETHERNET    EStatusDisplay = 3
	EStatusDisplay_STATUS_DISPLAY_POWER       EStatusDisplay = 4
	EStatusDisplay_STATUS_DISPLAY_FW_VERSIONS EStatusDisplay = 5
)

// Enum value maps for EStatusDisplay.
var (
	EStatusDisplay_name = map[int32]string{
		0: "STATUS_DISPLAY_UNSPECIFIED",
		1: "STATUS_DISPLAY_MAIN",
		2: "STATUS_DISPLAY_FAULT",
		3: "STATUS_DISPLAY_ETHERNET",
		4: "STATUS_DISPLAY_POWER",
		5: "STATUS_DISPLAY_FW_VERSIONS",
	}
	EStatusDisplay_value = map[string]int32{
		"STATUS_DISPLAY_UNSPECIFIED": 0,
		"STATUS_DISPLAY_MAIN":        1,
		"STATUS_DISPLAY_FAULT":       2,
		"STATUS_DISPLAY_ETHERNET":    3,
		"STATUS_DISPLAY_POWER":       4,
		"STATUS_DISPLAY_FW_VERSIONS": 5,
	}
)

func (x EStatusDisplay) Enum() *EStatusDisplay {
	p := new(EStatusDisplay)
	*p = x
	return p
}

func (x EStatusDisplay) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EStatusDisplay) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[0].Descriptor()
}

func (EStatusDisplay) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[0]
}

func (x EStatusDisplay) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EStatusDisplay.Descriptor instead.
func (EStatusDisplay) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{0}
}

//	ENUM EDisableOverrideState possible Disable Override states.
//
// There are 3 states for the Port 1 Disable Override:
//   - No Override is being used
//   - The Override to disable Port 1 is being forced ON - used in ATSI testing and only temporarily in the field.
//   - The Override to disable Port 1 is being forced OFF - ie, Port 1 is not disabled.
type EDisableOverrideState int32

const (
	EDisableOverrideState_DISABLE_OVERRIDE_STATE_UNSPECIFIED EDisableOverrideState = 0
	EDisableOverrideState_DISABLE_OVERRIDE_STATE_NONE        EDisableOverrideState = 1
	EDisableOverrideState_DISABLE_OVERRIDE_STATE_FORCE_ON    EDisableOverrideState = 2
	EDisableOverrideState_DISABLE_OVERRIDE_STATE_FORCE_OFF   EDisableOverrideState = 3
)

// Enum value maps for EDisableOverrideState.
var (
	EDisableOverrideState_name = map[int32]string{
		0: "DISABLE_OVERRIDE_STATE_UNSPECIFIED",
		1: "DISABLE_OVERRIDE_STATE_NONE",
		2: "DISABLE_OVERRIDE_STATE_FORCE_ON",
		3: "DISABLE_OVERRIDE_STATE_FORCE_OFF",
	}
	EDisableOverrideState_value = map[string]int32{
		"DISABLE_OVERRIDE_STATE_UNSPECIFIED": 0,
		"DISABLE_OVERRIDE_STATE_NONE":        1,
		"DISABLE_OVERRIDE_STATE_FORCE_ON":    2,
		"DISABLE_OVERRIDE_STATE_FORCE_OFF":   3,
	}
)

func (x EDisableOverrideState) Enum() *EDisableOverrideState {
	p := new(EDisableOverrideState)
	*p = x
	return p
}

func (x EDisableOverrideState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EDisableOverrideState) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[1].Descriptor()
}

func (EDisableOverrideState) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[1]
}

func (x EDisableOverrideState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EDisableOverrideState.Descriptor instead.
func (EDisableOverrideState) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{1}
}

// ENUM EFlashingYellowArrowMode possible Flashing Yellow Arrow modes .
type EFlashingYellowArrowMode int32

const (
	EFlashingYellowArrowMode_FYA_MODE_UNSPECIFIED EFlashingYellowArrowMode = 0
	EFlashingYellowArrowMode_FYA_MODE_A           EFlashingYellowArrowMode = 1
	EFlashingYellowArrowMode_FYA_MODE_B           EFlashingYellowArrowMode = 2
	EFlashingYellowArrowMode_FYA_MODE_C           EFlashingYellowArrowMode = 3
	EFlashingYellowArrowMode_FYA_MODE_D           EFlashingYellowArrowMode = 4
	EFlashingYellowArrowMode_FYA_MODE_E           EFlashingYellowArrowMode = 5
	EFlashingYellowArrowMode_FYA_MODE_F           EFlashingYellowArrowMode = 6
	EFlashingYellowArrowMode_FYA_MODE_G           EFlashingYellowArrowMode = 7
	EFlashingYellowArrowMode_FYA_MODE_H           EFlashingYellowArrowMode = 8
	EFlashingYellowArrowMode_FYA_MODE_I           EFlashingYellowArrowMode = 9
	EFlashingYellowArrowMode_FYA_MODE_J           EFlashingYellowArrowMode = 10
	EFlashingYellowArrowMode_FYA_MODE_K           EFlashingYellowArrowMode = 11
	EFlashingYellowArrowMode_FYA_MODE_L           EFlashingYellowArrowMode = 12
	EFlashingYellowArrowMode_FYA_MODE_M           EFlashingYellowArrowMode = 13
	EFlashingYellowArrowMode_FYA_MODE_N           EFlashingYellowArrowMode = 14
	EFlashingYellowArrowMode_FYA_MODE_O           EFlashingYellowArrowMode = 15
	EFlashingYellowArrowMode_FYA_MODE_P           EFlashingYellowArrowMode = 16
)

// Enum value maps for EFlashingYellowArrowMode.
var (
	EFlashingYellowArrowMode_name = map[int32]string{
		0:  "FYA_MODE_UNSPECIFIED",
		1:  "FYA_MODE_A",
		2:  "FYA_MODE_B",
		3:  "FYA_MODE_C",
		4:  "FYA_MODE_D",
		5:  "FYA_MODE_E",
		6:  "FYA_MODE_F",
		7:  "FYA_MODE_G",
		8:  "FYA_MODE_H",
		9:  "FYA_MODE_I",
		10: "FYA_MODE_J",
		11: "FYA_MODE_K",
		12: "FYA_MODE_L",
		13: "FYA_MODE_M",
		14: "FYA_MODE_N",
		15: "FYA_MODE_O",
		16: "FYA_MODE_P",
	}
	EFlashingYellowArrowMode_value = map[string]int32{
		"FYA_MODE_UNSPECIFIED": 0,
		"FYA_MODE_A":           1,
		"FYA_MODE_B":           2,
		"FYA_MODE_C":           3,
		"FYA_MODE_D":           4,
		"FYA_MODE_E":           5,
		"FYA_MODE_F":           6,
		"FYA_MODE_G":           7,
		"FYA_MODE_H":           8,
		"FYA_MODE_I":           9,
		"FYA_MODE_J":           10,
		"FYA_MODE_K":           11,
		"FYA_MODE_L":           12,
		"FYA_MODE_M":           13,
		"FYA_MODE_N":           14,
		"FYA_MODE_O":           15,
		"FYA_MODE_P":           16,
	}
)

func (x EFlashingYellowArrowMode) Enum() *EFlashingYellowArrowMode {
	p := new(EFlashingYellowArrowMode)
	*p = x
	return p
}

func (x EFlashingYellowArrowMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EFlashingYellowArrowMode) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[2].Descriptor()
}

func (EFlashingYellowArrowMode) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[2]
}

func (x EFlashingYellowArrowMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EFlashingYellowArrowMode.Descriptor instead.
func (EFlashingYellowArrowMode) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{2}
}

// ENUM EConfigDataLocation specifies locations for the config data.
//
// The Jumperless Program Card is the factory default; if any of its
// jumpers are set by the Agency, then it becomes a Jumpered Program Card,
// and the fields covered by the jumpers cannot be changed (overwritten),
// and CmdWriteDataKey must exactly match those jumpered fields.
type EConfigDataLocation int32

const (
	EConfigDataLocation_CONFIG_DATA_LOC_UNSPECIFIED             EConfigDataLocation = 0
	EConfigDataLocation_CONFIG_DATA_LOC_JUMPERED_PROGRAM_CARD   EConfigDataLocation = 1
	EConfigDataLocation_CONFIG_DATA_LOC_JUMPERLESS_PROGRAM_CARD EConfigDataLocation = 2
	EConfigDataLocation_CONFIG_DATA_LOC_DATA_KEY                EConfigDataLocation = 3
	EConfigDataLocation_CONFIG_DATA_LOC_LEGACY_EDI              EConfigDataLocation = 4
	EConfigDataLocation_CONFIG_DATA_LOC_LEGACY_RENO             EConfigDataLocation = 5
	EConfigDataLocation_CONFIG_DATA_LOC_LEGACY_NO_MEM           EConfigDataLocation = 6
	EConfigDataLocation_CONFIG_DATA_LOC_NONE                    EConfigDataLocation = 255
)

// Enum value maps for EConfigDataLocation.
var (
	EConfigDataLocation_name = map[int32]string{
		0:   "CONFIG_DATA_LOC_UNSPECIFIED",
		1:   "CONFIG_DATA_LOC_JUMPERED_PROGRAM_CARD",
		2:   "CONFIG_DATA_LOC_JUMPERLESS_PROGRAM_CARD",
		3:   "CONFIG_DATA_LOC_DATA_KEY",
		4:   "CONFIG_DATA_LOC_LEGACY_EDI",
		5:   "CONFIG_DATA_LOC_LEGACY_RENO",
		6:   "CONFIG_DATA_LOC_LEGACY_NO_MEM",
		255: "CONFIG_DATA_LOC_NONE",
	}
	EConfigDataLocation_value = map[string]int32{
		"CONFIG_DATA_LOC_UNSPECIFIED":             0,
		"CONFIG_DATA_LOC_JUMPERED_PROGRAM_CARD":   1,
		"CONFIG_DATA_LOC_JUMPERLESS_PROGRAM_CARD": 2,
		"CONFIG_DATA_LOC_DATA_KEY":                3,
		"CONFIG_DATA_LOC_LEGACY_EDI":              4,
		"CONFIG_DATA_LOC_LEGACY_RENO":             5,
		"CONFIG_DATA_LOC_LEGACY_NO_MEM":           6,
		"CONFIG_DATA_LOC_NONE":                    255,
	}
)

func (x EConfigDataLocation) Enum() *EConfigDataLocation {
	p := new(EConfigDataLocation)
	*p = x
	return p
}

func (x EConfigDataLocation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EConfigDataLocation) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[3].Descriptor()
}

func (EConfigDataLocation) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[3]
}

func (x EConfigDataLocation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EConfigDataLocation.Descriptor instead.
func (EConfigDataLocation) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{3}
}

//	Type of the Program Card / Data Key data to be read.
//
// Either Current or Pending data may be requested;
// if Pending is requested but there is no pending data, the response will be for PC_DK_NONE.
type EPcDkReadType int32

const (
	EPcDkReadType_PC_DK_UNSPECIFIED EPcDkReadType = 0
	EPcDkReadType_PC_DK_NONE        EPcDkReadType = 1 // No Program Card / Data Key available per the request
	EPcDkReadType_PC_DK_CURRENT     EPcDkReadType = 2 // The Current Program Card / Data Key data
	EPcDkReadType_PC_DK_PENDING     EPcDkReadType = 3 // The Pending Program Card / Data Key data
)

// Enum value maps for EPcDkReadType.
var (
	EPcDkReadType_name = map[int32]string{
		0: "PC_DK_UNSPECIFIED",
		1: "PC_DK_NONE",
		2: "PC_DK_CURRENT",
		3: "PC_DK_PENDING",
	}
	EPcDkReadType_value = map[string]int32{
		"PC_DK_UNSPECIFIED": 0,
		"PC_DK_NONE":        1,
		"PC_DK_CURRENT":     2,
		"PC_DK_PENDING":     3,
	}
)

func (x EPcDkReadType) Enum() *EPcDkReadType {
	p := new(EPcDkReadType)
	*p = x
	return p
}

func (x EPcDkReadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EPcDkReadType) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[4].Descriptor()
}

func (EPcDkReadType) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[4]
}

func (x EPcDkReadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EPcDkReadType.Descriptor instead.
func (EPcDkReadType) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{4}
}

//	ENUM EWriteResult gives the results for a write of configuration or settings to the unit.
//
// Some of these are mapped from Serial NACK status codes.
type EWriteResult int32

const (
	EWriteResult_WRITE_RESULT_UNSPECIFIED               EWriteResult = 0
	EWriteResult_WRITE_RESULT_SUCCESS                   EWriteResult = 1
	EWriteResult_WRITE_RESULT_CRC_INVALID               EWriteResult = 2 // data key or user settings write
	EWriteResult_WRITE_RESULT_DESTINATION_UNAVAILABLE   EWriteResult = 3 // unavailable or has pending changes
	EWriteResult_WRITE_RESULT_WRITE_PROTECTED           EWriteResult = 4 // data key write
	EWriteResult_WRITE_RESULT_WRITE_PCB_OPTION_MISMATCH EWriteResult = 5 // factory settings write
	EWriteResult_WRITE_RESULT_WRITE_OUT_OF_RANGE        EWriteResult = 6 // overrides write
	EWriteResult_WRITE_RESULT_WRITE_INVALID_DATA        EWriteResult = 7 // user settings write
	EWriteResult_WRITE_RESULT_BUSY                      EWriteResult = 8 // Cannot accept right now; user settings write
	EWriteResult_WRITE_RESULT_NOT_PERMITTED             EWriteResult = 9 // Command was not permitted (maybe user permissions)
)

// Enum value maps for EWriteResult.
var (
	EWriteResult_name = map[int32]string{
		0: "WRITE_RESULT_UNSPECIFIED",
		1: "WRITE_RESULT_SUCCESS",
		2: "WRITE_RESULT_CRC_INVALID",
		3: "WRITE_RESULT_DESTINATION_UNAVAILABLE",
		4: "WRITE_RESULT_WRITE_PROTECTED",
		5: "WRITE_RESULT_WRITE_PCB_OPTION_MISMATCH",
		6: "WRITE_RESULT_WRITE_OUT_OF_RANGE",
		7: "WRITE_RESULT_WRITE_INVALID_DATA",
		8: "WRITE_RESULT_BUSY",
		9: "WRITE_RESULT_NOT_PERMITTED",
	}
	EWriteResult_value = map[string]int32{
		"WRITE_RESULT_UNSPECIFIED":               0,
		"WRITE_RESULT_SUCCESS":                   1,
		"WRITE_RESULT_CRC_INVALID":               2,
		"WRITE_RESULT_DESTINATION_UNAVAILABLE":   3,
		"WRITE_RESULT_WRITE_PROTECTED":           4,
		"WRITE_RESULT_WRITE_PCB_OPTION_MISMATCH": 5,
		"WRITE_RESULT_WRITE_OUT_OF_RANGE":        6,
		"WRITE_RESULT_WRITE_INVALID_DATA":        7,
		"WRITE_RESULT_BUSY":                      8,
		"WRITE_RESULT_NOT_PERMITTED":             9,
	}
)

func (x EWriteResult) Enum() *EWriteResult {
	p := new(EWriteResult)
	*p = x
	return p
}

func (x EWriteResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EWriteResult) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[5].Descriptor()
}

func (EWriteResult) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[5]
}

func (x EWriteResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EWriteResult.Descriptor instead.
func (EWriteResult) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{5}
}

// ENUM EMonitorStatistics are values for different types of monitor statistics.
type EMonitorStatistics int32

const (
	EMonitorStatistics_STATISTICS_UNSPECIFIED      EMonitorStatistics = 0
	EMonitorStatistics_STATISTICS_ALL              EMonitorStatistics = 1
	EMonitorStatistics_STATISTICS_PORT1            EMonitorStatistics = 2 // controller serial bus
	EMonitorStatistics_STATISTICS_DATA_KEY         EMonitorStatistics = 3 // or program card
	EMonitorStatistics_STATISTICS_MAIN_TO_ISOLATED EMonitorStatistics = 4 // main mcu to isolated mcu serial comms (main side)
	EMonitorStatistics_STATISTICS_MAIN_TO_COMMS    EMonitorStatistics = 5 // main mcu to communications mcu serial comms (main side)
	EMonitorStatistics_STATISTICS_COMMS_TO_MAIN    EMonitorStatistics = 6 // communications mcu to main mcu serial comms (communications side)
)

// Enum value maps for EMonitorStatistics.
var (
	EMonitorStatistics_name = map[int32]string{
		0: "STATISTICS_UNSPECIFIED",
		1: "STATISTICS_ALL",
		2: "STATISTICS_PORT1",
		3: "STATISTICS_DATA_KEY",
		4: "STATISTICS_MAIN_TO_ISOLATED",
		5: "STATISTICS_MAIN_TO_COMMS",
		6: "STATISTICS_COMMS_TO_MAIN",
	}
	EMonitorStatistics_value = map[string]int32{
		"STATISTICS_UNSPECIFIED":      0,
		"STATISTICS_ALL":              1,
		"STATISTICS_PORT1":            2,
		"STATISTICS_DATA_KEY":         3,
		"STATISTICS_MAIN_TO_ISOLATED": 4,
		"STATISTICS_MAIN_TO_COMMS":    5,
		"STATISTICS_COMMS_TO_MAIN":    6,
	}
)

func (x EMonitorStatistics) Enum() *EMonitorStatistics {
	p := new(EMonitorStatistics)
	*p = x
	return p
}

func (x EMonitorStatistics) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EMonitorStatistics) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[6].Descriptor()
}

func (EMonitorStatistics) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[6]
}

func (x EMonitorStatistics) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EMonitorStatistics.Descriptor instead.
func (EMonitorStatistics) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{6}
}

// ENUM ETimeSetResult are result values for setting time.
type ETimeSetResult int32

const (
	ETimeSetResult_TIME_SET_UNSPECIFIED            ETimeSetResult = 0
	ETimeSetResult_TIME_SET_SUCCESS                ETimeSetResult = 1
	ETimeSetResult_TIME_SET_DATE_TIME_OUT_OF_RANGE ETimeSetResult = 2
	ETimeSetResult_TIME_SET_DST_OUT_OF_RANGE       ETimeSetResult = 3
	ETimeSetResult_TIME_SET_LOG_ERR                ETimeSetResult = 4 // Failed to log to clock log when time changed
)

// Enum value maps for ETimeSetResult.
var (
	ETimeSetResult_name = map[int32]string{
		0: "TIME_SET_UNSPECIFIED",
		1: "TIME_SET_SUCCESS",
		2: "TIME_SET_DATE_TIME_OUT_OF_RANGE",
		3: "TIME_SET_DST_OUT_OF_RANGE",
		4: "TIME_SET_LOG_ERR",
	}
	ETimeSetResult_value = map[string]int32{
		"TIME_SET_UNSPECIFIED":            0,
		"TIME_SET_SUCCESS":                1,
		"TIME_SET_DATE_TIME_OUT_OF_RANGE": 2,
		"TIME_SET_DST_OUT_OF_RANGE":       3,
		"TIME_SET_LOG_ERR":                4,
	}
)

func (x ETimeSetResult) Enum() *ETimeSetResult {
	p := new(ETimeSetResult)
	*p = x
	return p
}

func (x ETimeSetResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ETimeSetResult) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[7].Descriptor()
}

func (ETimeSetResult) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[7]
}

func (x ETimeSetResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ETimeSetResult.Descriptor instead.
func (ETimeSetResult) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{7}
}

// ENUM EOemType defines the OEM Type that MMU is built for
type EOemType int32

const (
	EOemType_OEM_TYPE_UNSPECIFIED EOemType = 0
	EOemType_OEM_TYPE_EDI         EOemType = 1
	EOemType_OEM_TYPE_RENO        EOemType = 2
	EOemType_OEM_TYPE_MCCAIN      EOemType = 3
	EOemType_OEM_TYPE_ECONOLITE   EOemType = 4
	EOemType_OEM_TYPE_SAFETRAN    EOemType = 5
	EOemType_OEM_TYPE_ORIUX       EOemType = 6
	EOemType_OEM_TYPE_MOBOTREX    EOemType = 7
	EOemType_OEM_TYPE_CUBIC       EOemType = 8
)

// Enum value maps for EOemType.
var (
	EOemType_name = map[int32]string{
		0: "OEM_TYPE_UNSPECIFIED",
		1: "OEM_TYPE_EDI",
		2: "OEM_TYPE_RENO",
		3: "OEM_TYPE_MCCAIN",
		4: "OEM_TYPE_ECONOLITE",
		5: "OEM_TYPE_SAFETRAN",
		6: "OEM_TYPE_ORIUX",
		7: "OEM_TYPE_MOBOTREX",
		8: "OEM_TYPE_CUBIC",
	}
	EOemType_value = map[string]int32{
		"OEM_TYPE_UNSPECIFIED": 0,
		"OEM_TYPE_EDI":         1,
		"OEM_TYPE_RENO":        2,
		"OEM_TYPE_MCCAIN":      3,
		"OEM_TYPE_ECONOLITE":   4,
		"OEM_TYPE_SAFETRAN":    5,
		"OEM_TYPE_ORIUX":       6,
		"OEM_TYPE_MOBOTREX":    7,
		"OEM_TYPE_CUBIC":       8,
	}
)

func (x EOemType) Enum() *EOemType {
	p := new(EOemType)
	*p = x
	return p
}

func (x EOemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EOemType) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[8].Descriptor()
}

func (EOemType) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[8]
}

func (x EOemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EOemType.Descriptor instead.
func (EOemType) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{8}
}

// ENUM ERemoteResetType are types for Fault Clear / Configuration Acceptance (Remote Reset)
type ERemoteResetType int32

const (
	ERemoteResetType_REMOTE_RESET_TYPE_UNSPECIFIED               ERemoteResetType = 0
	ERemoteResetType_REMOTE_RESET_TYPE_FAULT_CLEAR               ERemoteResetType = 1
	ERemoteResetType_REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE ERemoteResetType = 2
)

// Enum value maps for ERemoteResetType.
var (
	ERemoteResetType_name = map[int32]string{
		0: "REMOTE_RESET_TYPE_UNSPECIFIED",
		1: "REMOTE_RESET_TYPE_FAULT_CLEAR",
		2: "REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE",
	}
	ERemoteResetType_value = map[string]int32{
		"REMOTE_RESET_TYPE_UNSPECIFIED":               0,
		"REMOTE_RESET_TYPE_FAULT_CLEAR":               1,
		"REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE": 2,
	}
)

func (x ERemoteResetType) Enum() *ERemoteResetType {
	p := new(ERemoteResetType)
	*p = x
	return p
}

func (x ERemoteResetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ERemoteResetType) Descriptor() protoreflect.EnumDescriptor {
	return file_settings_proto_enumTypes[9].Descriptor()
}

func (ERemoteResetType) Type() protoreflect.EnumType {
	return &file_settings_proto_enumTypes[9]
}

func (x ERemoteResetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ERemoteResetType.Descriptor instead.
func (ERemoteResetType) EnumDescriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{9}
}

// NetworkSettings - common network settings
type NetworkSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// True when the monitor expects its IP Address to be assigned by a DHCP server
	// False when IP address is statically assigned.
	DhcpClientEnabled bool `protobuf:"varint,1,opt,name=dhcp_client_enabled,json=dhcpClientEnabled,proto3" json:"dhcp_client_enabled,omitempty"`
	// True if the monitor will act as a DHCP server (always disabled in current implementation)
	DhcpServerEnabled bool `protobuf:"varint,2,opt,name=dhcp_server_enabled,json=dhcpServerEnabled,proto3" json:"dhcp_server_enabled,omitempty"`
	// The Ethernet hostname assigned to the monitor
	EthernetHostname string `protobuf:"bytes,3,opt,name=ethernet_hostname,json=ethernetHostname,proto3" json:"ethernet_hostname,omitempty"` // Max string length set in settings.options
	// The monitor's Ethernet IP Address
	MonitorIpv4Address *basic.IpAddressV4 `protobuf:"bytes,4,opt,name=monitor_ipv4_address,json=monitorIpv4Address,proto3" json:"monitor_ipv4_address,omitempty"`
	// The monitor's Ethernet Subnet Mask
	MonitorIpv4Subnet *basic.IpAddressV4 `protobuf:"bytes,5,opt,name=monitor_ipv4_subnet,json=monitorIpv4Subnet,proto3" json:"monitor_ipv4_subnet,omitempty"`
	// The monitor's Ethernet Gateway Address
	MonitorIpv4Gateway *basic.IpAddressV4 `protobuf:"bytes,6,opt,name=monitor_ipv4_gateway,json=monitorIpv4Gateway,proto3" json:"monitor_ipv4_gateway,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *NetworkSettings) Reset() {
	*x = NetworkSettings{}
	mi := &file_settings_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkSettings) ProtoMessage() {}

func (x *NetworkSettings) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkSettings.ProtoReflect.Descriptor instead.
func (*NetworkSettings) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{0}
}

func (x *NetworkSettings) GetDhcpClientEnabled() bool {
	if x != nil {
		return x.DhcpClientEnabled
	}
	return false
}

func (x *NetworkSettings) GetDhcpServerEnabled() bool {
	if x != nil {
		return x.DhcpServerEnabled
	}
	return false
}

func (x *NetworkSettings) GetEthernetHostname() string {
	if x != nil {
		return x.EthernetHostname
	}
	return ""
}

func (x *NetworkSettings) GetMonitorIpv4Address() *basic.IpAddressV4 {
	if x != nil {
		return x.MonitorIpv4Address
	}
	return nil
}

func (x *NetworkSettings) GetMonitorIpv4Subnet() *basic.IpAddressV4 {
	if x != nil {
		return x.MonitorIpv4Subnet
	}
	return nil
}

func (x *NetworkSettings) GetMonitorIpv4Gateway() *basic.IpAddressV4 {
	if x != nil {
		return x.MonitorIpv4Gateway
	}
	return nil
}

// FactorySettings - monitor settings for production
type FactorySettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// monitor serial number & model number
	Numbers *basic.ModelAndSerialNumber `protobuf:"bytes,1,opt,name=numbers,proto3" json:"numbers,omitempty"`
	// manufacturer's device description
	DeviceDescription string `protobuf:"bytes,2,opt,name=device_description,json=deviceDescription,proto3" json:"device_description,omitempty"` // Max string length set in settings.options
	// Manufacture date
	ManufactureDate *basic.DateStr `protobuf:"bytes,3,opt,name=manufacture_date,json=manufactureDate,proto3" json:"manufacture_date,omitempty"`
	// The Main Processor PCB Option Jumpers in the monitor
	PcbOptions *PcbOptionsMmu `protobuf:"bytes,4,opt,name=pcb_options,json=pcbOptions,proto3" json:"pcb_options,omitempty"`
	// The Factory options of the monitor. (32-bit value)
	FactoryOptions *FactoryOptionsMmu `protobuf:"bytes,5,opt,name=factory_options,json=factoryOptions,proto3" json:"factory_options,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FactorySettings) Reset() {
	*x = FactorySettings{}
	mi := &file_settings_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FactorySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FactorySettings) ProtoMessage() {}

func (x *FactorySettings) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FactorySettings.ProtoReflect.Descriptor instead.
func (*FactorySettings) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{1}
}

func (x *FactorySettings) GetNumbers() *basic.ModelAndSerialNumber {
	if x != nil {
		return x.Numbers
	}
	return nil
}

func (x *FactorySettings) GetDeviceDescription() string {
	if x != nil {
		return x.DeviceDescription
	}
	return ""
}

func (x *FactorySettings) GetManufactureDate() *basic.DateStr {
	if x != nil {
		return x.ManufactureDate
	}
	return nil
}

func (x *FactorySettings) GetPcbOptions() *PcbOptionsMmu {
	if x != nil {
		return x.PcbOptions
	}
	return nil
}

func (x *FactorySettings) GetFactoryOptions() *FactoryOptionsMmu {
	if x != nil {
		return x.FactoryOptions
	}
	return nil
}

//	UserOptionsBitmap - This message matches the options in the Main processor command 0x24 Read User Settings
//
// bitmap field "User Options"
type UserOptionsBitmap struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// TBD
	Option_1      bool `protobuf:"varint,1,opt,name=option_1,json=option1,proto3" json:"option_1,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserOptionsBitmap) Reset() {
	*x = UserOptionsBitmap{}
	mi := &file_settings_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserOptionsBitmap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserOptionsBitmap) ProtoMessage() {}

func (x *UserOptionsBitmap) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserOptionsBitmap.ProtoReflect.Descriptor instead.
func (*UserOptionsBitmap) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{2}
}

func (x *UserOptionsBitmap) GetOption_1() bool {
	if x != nil {
		return x.Option_1
	}
	return false
}

// UserSettings - System user settings.
type UserSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// User settings options
	UserOptions *UserOptionsBitmap `protobuf:"bytes,1,opt,name=user_options,json=userOptions,proto3" json:"user_options,omitempty"`
	// The number of minutes from last panel button press until display is turned off
	SleepTimeMinutes uint32 `protobuf:"varint,2,opt,name=sleep_time_minutes,json=sleepTimeMinutes,proto3" json:"sleep_time_minutes,omitempty"`
	// Display dimming level (0 = full brightness, 255 = off) while on
	DisplayDimming uint32 `protobuf:"varint,3,opt,name=display_dimming,json=displayDimming,proto3" json:"display_dimming,omitempty"`
	// The last selected status display
	LastStatusDisplay EStatusDisplay `protobuf:"varint,4,opt,name=last_status_display,json=lastStatusDisplay,proto3,enum=settings.EStatusDisplay" json:"last_status_display,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UserSettings) Reset() {
	*x = UserSettings{}
	mi := &file_settings_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSettings) ProtoMessage() {}

func (x *UserSettings) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSettings.ProtoReflect.Descriptor instead.
func (*UserSettings) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{3}
}

func (x *UserSettings) GetUserOptions() *UserOptionsBitmap {
	if x != nil {
		return x.UserOptions
	}
	return nil
}

func (x *UserSettings) GetSleepTimeMinutes() uint32 {
	if x != nil {
		return x.SleepTimeMinutes
	}
	return 0
}

func (x *UserSettings) GetDisplayDimming() uint32 {
	if x != nil {
		return x.DisplayDimming
	}
	return 0
}

func (x *UserSettings) GetLastStatusDisplay() EStatusDisplay {
	if x != nil {
		return x.LastStatusDisplay
	}
	return EStatusDisplay_STATUS_DISPLAY_UNSPECIFIED
}

// PerChannelSettings - settings per channel
type PerChannelSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// channel 1-32 that the settings apply to
	Channel uint32 `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"`
	// whether the channel is enabled
	Enabled bool `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Bitmap of permissive channels, LSbit = Ch.1
	PermissivesChmap uint32 `protobuf:"fixed32,3,opt,name=permissives_chmap,json=permissivesChmap,proto3" json:"permissives_chmap,omitempty"`
	// Green-Yellow multiple indication
	MultipleIndicationGrnYel bool `protobuf:"varint,4,opt,name=multiple_indication_grn_yel,json=multipleIndicationGrnYel,proto3" json:"multiple_indication_grn_yel,omitempty"`
	// Yellow-Red multiple indication
	MultipleIndicationYelRed bool `protobuf:"varint,5,opt,name=multiple_indication_yel_red,json=multipleIndicationYelRed,proto3" json:"multiple_indication_yel_red,omitempty"`
	// Green-Red multiple indication
	MultipleIndicationGrnRed bool `protobuf:"varint,6,opt,name=multiple_indication_grn_red,json=multipleIndicationGrnRed,proto3" json:"multiple_indication_grn_red,omitempty"`
	// Lack of indication signal (dark)
	LackOfSignal bool `protobuf:"varint,7,opt,name=lack_of_signal,json=lackOfSignal,proto3" json:"lack_of_signal,omitempty"`
	// Yellow is not monitored for this channel (typ for ped channel)
	// If enabled, yellow must be on within 100 ms of green channel off
	YellowDisable bool `protobuf:"varint,8,opt,name=yellow_disable,json=yellowDisable,proto3" json:"yellow_disable,omitempty"`
	// Checks that yellow is on for minimum time (2.7s)
	MinYellowChangeEnable bool `protobuf:"varint,9,opt,name=min_yellow_change_enable,json=minYellowChangeEnable,proto3" json:"min_yellow_change_enable,omitempty"`
	// Checks that conflicting green is not turned on within minimum (2.7s) time of channel green off.
	MinYellowPlusRedEnable bool `protobuf:"varint,10,opt,name=min_yellow_plus_red_enable,json=minYellowPlusRedEnable,proto3" json:"min_yellow_plus_red_enable,omitempty"`
	// Controller - monitor load switch state mismatch
	FieldCheckRed bool `protobuf:"varint,11,opt,name=field_check_red,json=fieldCheckRed,proto3" json:"field_check_red,omitempty"`
	// Controller - monitor load switch state mismatchs
	FieldCheckYellow bool `protobuf:"varint,12,opt,name=field_check_yellow,json=fieldCheckYellow,proto3" json:"field_check_yellow,omitempty"`
	// Controller - monitor load switch state mismatch
	FieldCheckGreen bool `protobuf:"varint,13,opt,name=field_check_green,json=fieldCheckGreen,proto3" json:"field_check_green,omitempty"`
	// Indication load switch current sense is low
	CurrentSenseRed bool `protobuf:"varint,14,opt,name=current_sense_red,json=currentSenseRed,proto3" json:"current_sense_red,omitempty"`
	// Indication load switch current sense is low
	CurrentSenseYellow bool `protobuf:"varint,15,opt,name=current_sense_yellow,json=currentSenseYellow,proto3" json:"current_sense_yellow,omitempty"`
	// Indication load switch current sense is low
	CurrentSenseGreen bool `protobuf:"varint,16,opt,name=current_sense_green,json=currentSenseGreen,proto3" json:"current_sense_green,omitempty"`
	// Current threshold for detection indication on in mA, Red
	RedOnThresholdCurrentMa uint32 `protobuf:"varint,17,opt,name=red_on_threshold_current_ma,json=redOnThresholdCurrentMa,proto3" json:"red_on_threshold_current_ma,omitempty"`
	// Current threshold for detection indication on in mA, Yellow
	YellowOnThresholdCurrentMa uint32 `protobuf:"varint,18,opt,name=yellow_on_threshold_current_ma,json=yellowOnThresholdCurrentMa,proto3" json:"yellow_on_threshold_current_ma,omitempty"`
	// Current threshold for detection indication on in mA, Green
	GreenOnThresholdCurrentMa uint32 `protobuf:"varint,19,opt,name=green_on_threshold_current_ma,json=greenOnThresholdCurrentMa,proto3" json:"green_on_threshold_current_ma,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *PerChannelSettings) Reset() {
	*x = PerChannelSettings{}
	mi := &file_settings_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerChannelSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerChannelSettings) ProtoMessage() {}

func (x *PerChannelSettings) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerChannelSettings.ProtoReflect.Descriptor instead.
func (*PerChannelSettings) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{4}
}

func (x *PerChannelSettings) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *PerChannelSettings) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *PerChannelSettings) GetPermissivesChmap() uint32 {
	if x != nil {
		return x.PermissivesChmap
	}
	return 0
}

func (x *PerChannelSettings) GetMultipleIndicationGrnYel() bool {
	if x != nil {
		return x.MultipleIndicationGrnYel
	}
	return false
}

func (x *PerChannelSettings) GetMultipleIndicationYelRed() bool {
	if x != nil {
		return x.MultipleIndicationYelRed
	}
	return false
}

func (x *PerChannelSettings) GetMultipleIndicationGrnRed() bool {
	if x != nil {
		return x.MultipleIndicationGrnRed
	}
	return false
}

func (x *PerChannelSettings) GetLackOfSignal() bool {
	if x != nil {
		return x.LackOfSignal
	}
	return false
}

func (x *PerChannelSettings) GetYellowDisable() bool {
	if x != nil {
		return x.YellowDisable
	}
	return false
}

func (x *PerChannelSettings) GetMinYellowChangeEnable() bool {
	if x != nil {
		return x.MinYellowChangeEnable
	}
	return false
}

func (x *PerChannelSettings) GetMinYellowPlusRedEnable() bool {
	if x != nil {
		return x.MinYellowPlusRedEnable
	}
	return false
}

func (x *PerChannelSettings) GetFieldCheckRed() bool {
	if x != nil {
		return x.FieldCheckRed
	}
	return false
}

func (x *PerChannelSettings) GetFieldCheckYellow() bool {
	if x != nil {
		return x.FieldCheckYellow
	}
	return false
}

func (x *PerChannelSettings) GetFieldCheckGreen() bool {
	if x != nil {
		return x.FieldCheckGreen
	}
	return false
}

func (x *PerChannelSettings) GetCurrentSenseRed() bool {
	if x != nil {
		return x.CurrentSenseRed
	}
	return false
}

func (x *PerChannelSettings) GetCurrentSenseYellow() bool {
	if x != nil {
		return x.CurrentSenseYellow
	}
	return false
}

func (x *PerChannelSettings) GetCurrentSenseGreen() bool {
	if x != nil {
		return x.CurrentSenseGreen
	}
	return false
}

func (x *PerChannelSettings) GetRedOnThresholdCurrentMa() uint32 {
	if x != nil {
		return x.RedOnThresholdCurrentMa
	}
	return 0
}

func (x *PerChannelSettings) GetYellowOnThresholdCurrentMa() uint32 {
	if x != nil {
		return x.YellowOnThresholdCurrentMa
	}
	return 0
}

func (x *PerChannelSettings) GetGreenOnThresholdCurrentMa() uint32 {
	if x != nil {
		return x.GreenOnThresholdCurrentMa
	}
	return 0
}

// PerChannelCurrentSenseSettings - settings per channel
type PerChannelCurrentSenseSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// channel 1-32 that the values apply to
	Channel uint32 `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"`
	// red current threshold for ON indication, in mA
	RedOnThresholdMa uint32 `protobuf:"varint,2,opt,name=red_on_threshold_ma,json=redOnThresholdMa,proto3" json:"red_on_threshold_ma,omitempty"`
	// yellow current threshold for ON indication on, in mA
	YellowOnThresholdMa uint32 `protobuf:"varint,3,opt,name=yellow_on_threshold_ma,json=yellowOnThresholdMa,proto3" json:"yellow_on_threshold_ma,omitempty"`
	// green current threshold for ON indication on, in mA
	GreenOnThresholdMa uint32 `protobuf:"varint,4,opt,name=green_on_threshold_ma,json=greenOnThresholdMa,proto3" json:"green_on_threshold_ma,omitempty"`
	// Walk current threshold for ON indication on, in mA
	WalkOnThresholdMa *uint32 `protobuf:"varint,5,opt,name=walk_on_threshold_ma,json=walkOnThresholdMa,proto3,oneof" json:"walk_on_threshold_ma,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PerChannelCurrentSenseSettings) Reset() {
	*x = PerChannelCurrentSenseSettings{}
	mi := &file_settings_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerChannelCurrentSenseSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerChannelCurrentSenseSettings) ProtoMessage() {}

func (x *PerChannelCurrentSenseSettings) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerChannelCurrentSenseSettings.ProtoReflect.Descriptor instead.
func (*PerChannelCurrentSenseSettings) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{5}
}

func (x *PerChannelCurrentSenseSettings) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *PerChannelCurrentSenseSettings) GetRedOnThresholdMa() uint32 {
	if x != nil {
		return x.RedOnThresholdMa
	}
	return 0
}

func (x *PerChannelCurrentSenseSettings) GetYellowOnThresholdMa() uint32 {
	if x != nil {
		return x.YellowOnThresholdMa
	}
	return 0
}

func (x *PerChannelCurrentSenseSettings) GetGreenOnThresholdMa() uint32 {
	if x != nil {
		return x.GreenOnThresholdMa
	}
	return 0
}

func (x *PerChannelCurrentSenseSettings) GetWalkOnThresholdMa() uint32 {
	if x != nil && x.WalkOnThresholdMa != nil {
		return *x.WalkOnThresholdMa
	}
	return 0
}

// PerChannelPermissives - settings per channel
type PerChannelPermissives struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// channel 1-32 that the value applies to
	Channel uint32 `protobuf:"varint,1,opt,name=channel,proto3" json:"channel,omitempty"`
	// bitmap of channel permissives.  LSbit = Ch.1
	PermissivesChmap uint32 `protobuf:"fixed32,2,opt,name=permissives_chmap,json=permissivesChmap,proto3" json:"permissives_chmap,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PerChannelPermissives) Reset() {
	*x = PerChannelPermissives{}
	mi := &file_settings_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerChannelPermissives) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerChannelPermissives) ProtoMessage() {}

func (x *PerChannelPermissives) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerChannelPermissives.ProtoReflect.Descriptor instead.
func (*PerChannelPermissives) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{6}
}

func (x *PerChannelPermissives) GetChannel() uint32 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *PerChannelPermissives) GetPermissivesChmap() uint32 {
	if x != nil {
		return x.PermissivesChmap
	}
	return 0
}

// FlashingYellowArrowSettings
type FlashingYellowArrowSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// configuration instance 1-4
	Instance uint32 `protobuf:"varint,1,opt,name=instance,proto3" json:"instance,omitempty"`
	// false if OFF, true if enabled
	Enabled bool `protobuf:"varint,2,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// Mode setting
	Mode EFlashingYellowArrowMode `protobuf:"varint,3,opt,name=mode,proto3,enum=settings.EFlashingYellowArrowMode" json:"mode,omitempty"`
	// FYA flashing frequency check
	FlashRateDetectEnabled bool `protobuf:"varint,4,opt,name=flash_rate_detect_enabled,json=flashRateDetectEnabled,proto3" json:"flash_rate_detect_enabled,omitempty"`
	// Yellow trap detection
	YellowTrapDetectEnabled bool `protobuf:"varint,5,opt,name=yellow_trap_detect_enabled,json=yellowTrapDetectEnabled,proto3" json:"yellow_trap_detect_enabled,omitempty"`
	// The overlap channel for the FYA, 1-32
	OverlapChannel uint32 `protobuf:"varint,6,opt,name=overlap_channel,json=overlapChannel,proto3" json:"overlap_channel,omitempty"`
	// The associated left turn phase channel, 1-32
	LeftTurnChannel uint32 `protobuf:"varint,7,opt,name=left_turn_channel,json=leftTurnChannel,proto3" json:"left_turn_channel,omitempty"`
	// The turn's opposing through phase channel, 1-32
	OpposingThruChannel uint32 `protobuf:"varint,8,opt,name=opposing_thru_channel,json=opposingThruChannel,proto3" json:"opposing_thru_channel,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *FlashingYellowArrowSettings) Reset() {
	*x = FlashingYellowArrowSettings{}
	mi := &file_settings_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlashingYellowArrowSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlashingYellowArrowSettings) ProtoMessage() {}

func (x *FlashingYellowArrowSettings) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlashingYellowArrowSettings.ProtoReflect.Descriptor instead.
func (*FlashingYellowArrowSettings) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{7}
}

func (x *FlashingYellowArrowSettings) GetInstance() uint32 {
	if x != nil {
		return x.Instance
	}
	return 0
}

func (x *FlashingYellowArrowSettings) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *FlashingYellowArrowSettings) GetMode() EFlashingYellowArrowMode {
	if x != nil {
		return x.Mode
	}
	return EFlashingYellowArrowMode_FYA_MODE_UNSPECIFIED
}

func (x *FlashingYellowArrowSettings) GetFlashRateDetectEnabled() bool {
	if x != nil {
		return x.FlashRateDetectEnabled
	}
	return false
}

func (x *FlashingYellowArrowSettings) GetYellowTrapDetectEnabled() bool {
	if x != nil {
		return x.YellowTrapDetectEnabled
	}
	return false
}

func (x *FlashingYellowArrowSettings) GetOverlapChannel() uint32 {
	if x != nil {
		return x.OverlapChannel
	}
	return 0
}

func (x *FlashingYellowArrowSettings) GetLeftTurnChannel() uint32 {
	if x != nil {
		return x.LeftTurnChannel
	}
	return 0
}

func (x *FlashingYellowArrowSettings) GetOpposingThruChannel() uint32 {
	if x != nil {
		return x.OpposingThruChannel
	}
	return 0
}

// FlashAreaStatistics - flash integrity check values for an area
type FlashAreaStatistics struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The most recent checksum calculated over the flash area
	CalculatedChecksum uint32 `protobuf:"fixed32,1,opt,name=calculated_checksum,json=calculatedChecksum,proto3" json:"calculated_checksum,omitempty"`
	// The checksum stored in the flash area
	StoredChecksum uint32 `protobuf:"fixed32,2,opt,name=stored_checksum,json=storedChecksum,proto3" json:"stored_checksum,omitempty"`
	// The size of the flash area in bytes
	SizeInBytes   uint32 `protobuf:"varint,3,opt,name=size_in_bytes,json=sizeInBytes,proto3" json:"size_in_bytes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FlashAreaStatistics) Reset() {
	*x = FlashAreaStatistics{}
	mi := &file_settings_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FlashAreaStatistics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FlashAreaStatistics) ProtoMessage() {}

func (x *FlashAreaStatistics) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FlashAreaStatistics.ProtoReflect.Descriptor instead.
func (*FlashAreaStatistics) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{8}
}

func (x *FlashAreaStatistics) GetCalculatedChecksum() uint32 {
	if x != nil {
		return x.CalculatedChecksum
	}
	return 0
}

func (x *FlashAreaStatistics) GetStoredChecksum() uint32 {
	if x != nil {
		return x.StoredChecksum
	}
	return 0
}

func (x *FlashAreaStatistics) GetSizeInBytes() uint32 {
	if x != nil {
		return x.SizeInBytes
	}
	return 0
}

//	PcbOptionsMmu defines possible PCB Options read from jumper settings.
//
// Currently only 4 bits of data are provided, abstracted as bools, corresponding to
// the 4 IO jumpers read in from the Main processor.
// Will probably be renamed or remapped later.
type PcbOptionsMmu struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Option1       bool                   `protobuf:"varint,1,opt,name=option1,proto3" json:"option1,omitempty"`
	Option2       bool                   `protobuf:"varint,2,opt,name=option2,proto3" json:"option2,omitempty"`
	Option3       bool                   `protobuf:"varint,3,opt,name=option3,proto3" json:"option3,omitempty"`
	Option4       bool                   `protobuf:"varint,4,opt,name=option4,proto3" json:"option4,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PcbOptionsMmu) Reset() {
	*x = PcbOptionsMmu{}
	mi := &file_settings_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PcbOptionsMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PcbOptionsMmu) ProtoMessage() {}

func (x *PcbOptionsMmu) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PcbOptionsMmu.ProtoReflect.Descriptor instead.
func (*PcbOptionsMmu) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{9}
}

func (x *PcbOptionsMmu) GetOption1() bool {
	if x != nil {
		return x.Option1
	}
	return false
}

func (x *PcbOptionsMmu) GetOption2() bool {
	if x != nil {
		return x.Option2
	}
	return false
}

func (x *PcbOptionsMmu) GetOption3() bool {
	if x != nil {
		return x.Option3
	}
	return false
}

func (x *PcbOptionsMmu) GetOption4() bool {
	if x != nil {
		return x.Option4
	}
	return false
}

//	FactoryOptionsMmu defines possible Factory Options written to the monitor.
//
// This is mapped from a 32-bit field in the serial data (td_Factory_Options).
type FactoryOptionsMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// OEM Type that MMU is built for
	OemType EOemType `protobuf:"varint,1,opt,name=oem_type,json=oemType,proto3,enum=settings.EOemType" json:"oem_type,omitempty"`
	// If true, the monitor supports 16 channels plus walk (16+ type)
	Ch_16WWalk bool `protobuf:"varint,2,opt,name=ch_16_w_walk,json=ch16WWalk,proto3" json:"ch_16_w_walk,omitempty"`
	// If true, the monitor supports Canadian Features (Fast flashing greens, European date format, and Celsius)
	CanadianFeatures bool `protobuf:"varint,3,opt,name=canadian_features,json=canadianFeatures,proto3" json:"canadian_features,omitempty"`
	// If true, the monitor supports LSU (Load Sensing Unit)
	LsuEnable     bool `protobuf:"varint,4,opt,name=lsu_enable,json=lsuEnable,proto3" json:"lsu_enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FactoryOptionsMmu) Reset() {
	*x = FactoryOptionsMmu{}
	mi := &file_settings_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FactoryOptionsMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FactoryOptionsMmu) ProtoMessage() {}

func (x *FactoryOptionsMmu) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FactoryOptionsMmu.ProtoReflect.Descriptor instead.
func (*FactoryOptionsMmu) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{10}
}

func (x *FactoryOptionsMmu) GetOemType() EOemType {
	if x != nil {
		return x.OemType
	}
	return EOemType_OEM_TYPE_UNSPECIFIED
}

func (x *FactoryOptionsMmu) GetCh_16WWalk() bool {
	if x != nil {
		return x.Ch_16WWalk
	}
	return false
}

func (x *FactoryOptionsMmu) GetCanadianFeatures() bool {
	if x != nil {
		return x.CanadianFeatures
	}
	return false
}

func (x *FactoryOptionsMmu) GetLsuEnable() bool {
	if x != nil {
		return x.LsuEnable
	}
	return false
}

// AgencyOptionsMmu - defines Agency Options for the monitor
type AgencyOptionsMmu struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Must user validate through Bluetooth before allowing front panel edits
	MustLogInToEdit bool `protobuf:"varint,1,opt,name=must_log_in_to_edit,json=mustLogInToEdit,proto3" json:"must_log_in_to_edit,omitempty"`
	// Must user validate through Bluetooth before allowing front panel view
	MustLogInToView bool `protobuf:"varint,2,opt,name=must_log_in_to_view,json=mustLogInToView,proto3" json:"must_log_in_to_view,omitempty"`
	// Program Card data cannot be changed through the front panel
	NoFpConfigChange bool `protobuf:"varint,3,opt,name=no_fp_config_change,json=noFpConfigChange,proto3" json:"no_fp_config_change,omitempty"`
	// Allow the Remote Configuration Acceptance via the Remote Reset command
	PermitRemoteConfigAcceptance bool `protobuf:"varint,4,opt,name=permit_remote_config_acceptance,json=permitRemoteConfigAcceptance,proto3" json:"permit_remote_config_acceptance,omitempty"`
	// Allow the Remote Fault Clear via the Remote Reset command
	// Note: in the future, we may add more, and more distinct, fault clear options
	PermitRemoteFaultClear bool `protobuf:"varint,5,opt,name=permit_remote_fault_clear,json=permitRemoteFaultClear,proto3" json:"permit_remote_fault_clear,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *AgencyOptionsMmu) Reset() {
	*x = AgencyOptionsMmu{}
	mi := &file_settings_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgencyOptionsMmu) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgencyOptionsMmu) ProtoMessage() {}

func (x *AgencyOptionsMmu) ProtoReflect() protoreflect.Message {
	mi := &file_settings_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgencyOptionsMmu.ProtoReflect.Descriptor instead.
func (*AgencyOptionsMmu) Descriptor() ([]byte, []int) {
	return file_settings_proto_rawDescGZIP(), []int{11}
}

func (x *AgencyOptionsMmu) GetMustLogInToEdit() bool {
	if x != nil {
		return x.MustLogInToEdit
	}
	return false
}

func (x *AgencyOptionsMmu) GetMustLogInToView() bool {
	if x != nil {
		return x.MustLogInToView
	}
	return false
}

func (x *AgencyOptionsMmu) GetNoFpConfigChange() bool {
	if x != nil {
		return x.NoFpConfigChange
	}
	return false
}

func (x *AgencyOptionsMmu) GetPermitRemoteConfigAcceptance() bool {
	if x != nil {
		return x.PermitRemoteConfigAcceptance
	}
	return false
}

func (x *AgencyOptionsMmu) GetPermitRemoteFaultClear() bool {
	if x != nil {
		return x.PermitRemoteFaultClear
	}
	return false
}

var File_settings_proto protoreflect.FileDescriptor

const file_settings_proto_rawDesc = "" +
	"\n" +
	"\x0esettings.proto\x12\bsettings\x1a\vbasic.proto\"\xee\x02\n" +
	"\x0fNetworkSettings\x12.\n" +
	"\x13dhcp_client_enabled\x18\x01 \x01(\bR\x11dhcpClientEnabled\x12.\n" +
	"\x13dhcp_server_enabled\x18\x02 \x01(\bR\x11dhcpServerEnabled\x12+\n" +
	"\x11ethernet_hostname\x18\x03 \x01(\tR\x10ethernetHostname\x12D\n" +
	"\x14monitor_ipv4_address\x18\x04 \x01(\v2\x12.basic.IpAddressV4R\x12monitorIpv4Address\x12B\n" +
	"\x13monitor_ipv4_subnet\x18\x05 \x01(\v2\x12.basic.IpAddressV4R\x11monitorIpv4Subnet\x12D\n" +
	"\x14monitor_ipv4_gateway\x18\x06 \x01(\v2\x12.basic.IpAddressV4R\x12monitorIpv4Gateway\"\xb2\x02\n" +
	"\x0fFactorySettings\x125\n" +
	"\anumbers\x18\x01 \x01(\v2\x1b.basic.ModelAndSerialNumberR\anumbers\x12-\n" +
	"\x12device_description\x18\x02 \x01(\tR\x11deviceDescription\x129\n" +
	"\x10manufacture_date\x18\x03 \x01(\v2\x0e.basic.DateStrR\x0fmanufactureDate\x128\n" +
	"\vpcb_options\x18\x04 \x01(\v2\x17.settings.PcbOptionsMmuR\n" +
	"pcbOptions\x12D\n" +
	"\x0ffactory_options\x18\x05 \x01(\v2\x1b.settings.FactoryOptionsMmuR\x0efactoryOptions\".\n" +
	"\x11UserOptionsBitmap\x12\x19\n" +
	"\boption_1\x18\x01 \x01(\bR\aoption1\"\xef\x01\n" +
	"\fUserSettings\x12>\n" +
	"\fuser_options\x18\x01 \x01(\v2\x1b.settings.UserOptionsBitmapR\vuserOptions\x12,\n" +
	"\x12sleep_time_minutes\x18\x02 \x01(\rR\x10sleepTimeMinutes\x12'\n" +
	"\x0fdisplay_dimming\x18\x03 \x01(\rR\x0edisplayDimming\x12H\n" +
	"\x13last_status_display\x18\x04 \x01(\x0e2\x18.settings.EStatusDisplayR\x11lastStatusDisplay\"\xc8\a\n" +
	"\x12PerChannelSettings\x12\x18\n" +
	"\achannel\x18\x01 \x01(\rR\achannel\x12\x18\n" +
	"\aenabled\x18\x02 \x01(\bR\aenabled\x12+\n" +
	"\x11permissives_chmap\x18\x03 \x01(\aR\x10permissivesChmap\x12=\n" +
	"\x1bmultiple_indication_grn_yel\x18\x04 \x01(\bR\x18multipleIndicationGrnYel\x12=\n" +
	"\x1bmultiple_indication_yel_red\x18\x05 \x01(\bR\x18multipleIndicationYelRed\x12=\n" +
	"\x1bmultiple_indication_grn_red\x18\x06 \x01(\bR\x18multipleIndicationGrnRed\x12$\n" +
	"\x0elack_of_signal\x18\a \x01(\bR\flackOfSignal\x12%\n" +
	"\x0eyellow_disable\x18\b \x01(\bR\ryellowDisable\x127\n" +
	"\x18min_yellow_change_enable\x18\t \x01(\bR\x15minYellowChangeEnable\x12:\n" +
	"\x1amin_yellow_plus_red_enable\x18\n" +
	" \x01(\bR\x16minYellowPlusRedEnable\x12&\n" +
	"\x0ffield_check_red\x18\v \x01(\bR\rfieldCheckRed\x12,\n" +
	"\x12field_check_yellow\x18\f \x01(\bR\x10fieldCheckYellow\x12*\n" +
	"\x11field_check_green\x18\r \x01(\bR\x0ffieldCheckGreen\x12*\n" +
	"\x11current_sense_red\x18\x0e \x01(\bR\x0fcurrentSenseRed\x120\n" +
	"\x14current_sense_yellow\x18\x0f \x01(\bR\x12currentSenseYellow\x12.\n" +
	"\x13current_sense_green\x18\x10 \x01(\bR\x11currentSenseGreen\x12<\n" +
	"\x1bred_on_threshold_current_ma\x18\x11 \x01(\rR\x17redOnThresholdCurrentMa\x12B\n" +
	"\x1eyellow_on_threshold_current_ma\x18\x12 \x01(\rR\x1ayellowOnThresholdCurrentMa\x12@\n" +
	"\x1dgreen_on_threshold_current_ma\x18\x13 \x01(\rR\x19greenOnThresholdCurrentMa\"\xa0\x02\n" +
	"\x1ePerChannelCurrentSenseSettings\x12\x18\n" +
	"\achannel\x18\x01 \x01(\rR\achannel\x12-\n" +
	"\x13red_on_threshold_ma\x18\x02 \x01(\rR\x10redOnThresholdMa\x123\n" +
	"\x16yellow_on_threshold_ma\x18\x03 \x01(\rR\x13yellowOnThresholdMa\x121\n" +
	"\x15green_on_threshold_ma\x18\x04 \x01(\rR\x12greenOnThresholdMa\x124\n" +
	"\x14walk_on_threshold_ma\x18\x05 \x01(\rH\x00R\x11walkOnThresholdMa\x88\x01\x01B\x17\n" +
	"\x15_walk_on_threshold_ma\"^\n" +
	"\x15PerChannelPermissives\x12\x18\n" +
	"\achannel\x18\x01 \x01(\rR\achannel\x12+\n" +
	"\x11permissives_chmap\x18\x02 \x01(\aR\x10permissivesChmap\"\x8c\x03\n" +
	"\x1bFlashingYellowArrowSettings\x12\x1a\n" +
	"\binstance\x18\x01 \x01(\rR\binstance\x12\x18\n" +
	"\aenabled\x18\x02 \x01(\bR\aenabled\x126\n" +
	"\x04mode\x18\x03 \x01(\x0e2\".settings.EFlashingYellowArrowModeR\x04mode\x129\n" +
	"\x19flash_rate_detect_enabled\x18\x04 \x01(\bR\x16flashRateDetectEnabled\x12;\n" +
	"\x1ayellow_trap_detect_enabled\x18\x05 \x01(\bR\x17yellowTrapDetectEnabled\x12'\n" +
	"\x0foverlap_channel\x18\x06 \x01(\rR\x0eoverlapChannel\x12*\n" +
	"\x11left_turn_channel\x18\a \x01(\rR\x0fleftTurnChannel\x122\n" +
	"\x15opposing_thru_channel\x18\b \x01(\rR\x13opposingThruChannel\"\x93\x01\n" +
	"\x13FlashAreaStatistics\x12/\n" +
	"\x13calculated_checksum\x18\x01 \x01(\aR\x12calculatedChecksum\x12'\n" +
	"\x0fstored_checksum\x18\x02 \x01(\aR\x0estoredChecksum\x12\"\n" +
	"\rsize_in_bytes\x18\x03 \x01(\rR\vsizeInBytes\"w\n" +
	"\rPcbOptionsMmu\x12\x18\n" +
	"\aoption1\x18\x01 \x01(\bR\aoption1\x12\x18\n" +
	"\aoption2\x18\x02 \x01(\bR\aoption2\x12\x18\n" +
	"\aoption3\x18\x03 \x01(\bR\aoption3\x12\x18\n" +
	"\aoption4\x18\x04 \x01(\bR\aoption4\"\xaf\x01\n" +
	"\x11FactoryOptionsMmu\x12-\n" +
	"\boem_type\x18\x01 \x01(\x0e2\x12.settings.EOemTypeR\aoemType\x12\x1f\n" +
	"\fch_16_w_walk\x18\x02 \x01(\bR\tch16WWalk\x12+\n" +
	"\x11canadian_features\x18\x03 \x01(\bR\x10canadianFeatures\x12\x1d\n" +
	"\n" +
	"lsu_enable\x18\x04 \x01(\bR\tlsuEnable\"\x9f\x02\n" +
	"\x10AgencyOptionsMmu\x12,\n" +
	"\x13must_log_in_to_edit\x18\x01 \x01(\bR\x0fmustLogInToEdit\x12,\n" +
	"\x13must_log_in_to_view\x18\x02 \x01(\bR\x0fmustLogInToView\x12-\n" +
	"\x13no_fp_config_change\x18\x03 \x01(\bR\x10noFpConfigChange\x12E\n" +
	"\x1fpermit_remote_config_acceptance\x18\x04 \x01(\bR\x1cpermitRemoteConfigAcceptance\x129\n" +
	"\x19permit_remote_fault_clear\x18\x05 \x01(\bR\x16permitRemoteFaultClear*\xba\x01\n" +
	"\x0eEStatusDisplay\x12\x1e\n" +
	"\x1aSTATUS_DISPLAY_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13STATUS_DISPLAY_MAIN\x10\x01\x12\x18\n" +
	"\x14STATUS_DISPLAY_FAULT\x10\x02\x12\x1b\n" +
	"\x17STATUS_DISPLAY_ETHERNET\x10\x03\x12\x18\n" +
	"\x14STATUS_DISPLAY_POWER\x10\x04\x12\x1e\n" +
	"\x1aSTATUS_DISPLAY_FW_VERSIONS\x10\x05*\xab\x01\n" +
	"\x15EDisableOverrideState\x12&\n" +
	"\"DISABLE_OVERRIDE_STATE_UNSPECIFIED\x10\x00\x12\x1f\n" +
	"\x1bDISABLE_OVERRIDE_STATE_NONE\x10\x01\x12#\n" +
	"\x1fDISABLE_OVERRIDE_STATE_FORCE_ON\x10\x02\x12$\n" +
	" DISABLE_OVERRIDE_STATE_FORCE_OFF\x10\x03*\xb4\x02\n" +
	"\x18EFlashingYellowArrowMode\x12\x18\n" +
	"\x14FYA_MODE_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"FYA_MODE_A\x10\x01\x12\x0e\n" +
	"\n" +
	"FYA_MODE_B\x10\x02\x12\x0e\n" +
	"\n" +
	"FYA_MODE_C\x10\x03\x12\x0e\n" +
	"\n" +
	"FYA_MODE_D\x10\x04\x12\x0e\n" +
	"\n" +
	"FYA_MODE_E\x10\x05\x12\x0e\n" +
	"\n" +
	"FYA_MODE_F\x10\x06\x12\x0e\n" +
	"\n" +
	"FYA_MODE_G\x10\a\x12\x0e\n" +
	"\n" +
	"FYA_MODE_H\x10\b\x12\x0e\n" +
	"\n" +
	"FYA_MODE_I\x10\t\x12\x0e\n" +
	"\n" +
	"FYA_MODE_J\x10\n" +
	"\x12\x0e\n" +
	"\n" +
	"FYA_MODE_K\x10\v\x12\x0e\n" +
	"\n" +
	"FYA_MODE_L\x10\f\x12\x0e\n" +
	"\n" +
	"FYA_MODE_M\x10\r\x12\x0e\n" +
	"\n" +
	"FYA_MODE_N\x10\x0e\x12\x0e\n" +
	"\n" +
	"FYA_MODE_O\x10\x0f\x12\x0e\n" +
	"\n" +
	"FYA_MODE_P\x10\x10*\xab\x02\n" +
	"\x13EConfigDataLocation\x12\x1f\n" +
	"\x1bCONFIG_DATA_LOC_UNSPECIFIED\x10\x00\x12)\n" +
	"%CONFIG_DATA_LOC_JUMPERED_PROGRAM_CARD\x10\x01\x12+\n" +
	"'CONFIG_DATA_LOC_JUMPERLESS_PROGRAM_CARD\x10\x02\x12\x1c\n" +
	"\x18CONFIG_DATA_LOC_DATA_KEY\x10\x03\x12\x1e\n" +
	"\x1aCONFIG_DATA_LOC_LEGACY_EDI\x10\x04\x12\x1f\n" +
	"\x1bCONFIG_DATA_LOC_LEGACY_RENO\x10\x05\x12!\n" +
	"\x1dCONFIG_DATA_LOC_LEGACY_NO_MEM\x10\x06\x12\x19\n" +
	"\x14CONFIG_DATA_LOC_NONE\x10\xff\x01*\\\n" +
	"\rEPcDkReadType\x12\x15\n" +
	"\x11PC_DK_UNSPECIFIED\x10\x00\x12\x0e\n" +
	"\n" +
	"PC_DK_NONE\x10\x01\x12\x11\n" +
	"\rPC_DK_CURRENT\x10\x02\x12\x11\n" +
	"\rPC_DK_PENDING\x10\x03*\xdd\x02\n" +
	"\fEWriteResult\x12\x1c\n" +
	"\x18WRITE_RESULT_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14WRITE_RESULT_SUCCESS\x10\x01\x12\x1c\n" +
	"\x18WRITE_RESULT_CRC_INVALID\x10\x02\x12(\n" +
	"$WRITE_RESULT_DESTINATION_UNAVAILABLE\x10\x03\x12 \n" +
	"\x1cWRITE_RESULT_WRITE_PROTECTED\x10\x04\x12*\n" +
	"&WRITE_RESULT_WRITE_PCB_OPTION_MISMATCH\x10\x05\x12#\n" +
	"\x1fWRITE_RESULT_WRITE_OUT_OF_RANGE\x10\x06\x12#\n" +
	"\x1fWRITE_RESULT_WRITE_INVALID_DATA\x10\a\x12\x15\n" +
	"\x11WRITE_RESULT_BUSY\x10\b\x12\x1e\n" +
	"\x1aWRITE_RESULT_NOT_PERMITTED\x10\t*\xd0\x01\n" +
	"\x12EMonitorStatistics\x12\x1a\n" +
	"\x16STATISTICS_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eSTATISTICS_ALL\x10\x01\x12\x14\n" +
	"\x10STATISTICS_PORT1\x10\x02\x12\x17\n" +
	"\x13STATISTICS_DATA_KEY\x10\x03\x12\x1f\n" +
	"\x1bSTATISTICS_MAIN_TO_ISOLATED\x10\x04\x12\x1c\n" +
	"\x18STATISTICS_MAIN_TO_COMMS\x10\x05\x12\x1c\n" +
	"\x18STATISTICS_COMMS_TO_MAIN\x10\x06*\x9a\x01\n" +
	"\x0eETimeSetResult\x12\x18\n" +
	"\x14TIME_SET_UNSPECIFIED\x10\x00\x12\x14\n" +
	"\x10TIME_SET_SUCCESS\x10\x01\x12#\n" +
	"\x1fTIME_SET_DATE_TIME_OUT_OF_RANGE\x10\x02\x12\x1d\n" +
	"\x19TIME_SET_DST_OUT_OF_RANGE\x10\x03\x12\x14\n" +
	"\x10TIME_SET_LOG_ERR\x10\x04*\xcc\x01\n" +
	"\bEOemType\x12\x18\n" +
	"\x14OEM_TYPE_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fOEM_TYPE_EDI\x10\x01\x12\x11\n" +
	"\rOEM_TYPE_RENO\x10\x02\x12\x13\n" +
	"\x0fOEM_TYPE_MCCAIN\x10\x03\x12\x16\n" +
	"\x12OEM_TYPE_ECONOLITE\x10\x04\x12\x15\n" +
	"\x11OEM_TYPE_SAFETRAN\x10\x05\x12\x12\n" +
	"\x0eOEM_TYPE_ORIUX\x10\x06\x12\x15\n" +
	"\x11OEM_TYPE_MOBOTREX\x10\a\x12\x12\n" +
	"\x0eOEM_TYPE_CUBIC\x10\b*\x89\x01\n" +
	"\x10ERemoteResetType\x12!\n" +
	"\x1dREMOTE_RESET_TYPE_UNSPECIFIED\x10\x00\x12!\n" +
	"\x1dREMOTE_RESET_TYPE_FAULT_CLEAR\x10\x01\x12/\n" +
	"+REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE\x10\x02b\x06proto3"

var (
	file_settings_proto_rawDescOnce sync.Once
	file_settings_proto_rawDescData []byte
)

func file_settings_proto_rawDescGZIP() []byte {
	file_settings_proto_rawDescOnce.Do(func() {
		file_settings_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_settings_proto_rawDesc), len(file_settings_proto_rawDesc)))
	})
	return file_settings_proto_rawDescData
}

var file_settings_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_settings_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_settings_proto_goTypes = []any{
	(EStatusDisplay)(0),                    // 0: settings.EStatusDisplay
	(EDisableOverrideState)(0),             // 1: settings.EDisableOverrideState
	(EFlashingYellowArrowMode)(0),          // 2: settings.EFlashingYellowArrowMode
	(EConfigDataLocation)(0),               // 3: settings.EConfigDataLocation
	(EPcDkReadType)(0),                     // 4: settings.EPcDkReadType
	(EWriteResult)(0),                      // 5: settings.EWriteResult
	(EMonitorStatistics)(0),                // 6: settings.EMonitorStatistics
	(ETimeSetResult)(0),                    // 7: settings.ETimeSetResult
	(EOemType)(0),                          // 8: settings.EOemType
	(ERemoteResetType)(0),                  // 9: settings.ERemoteResetType
	(*NetworkSettings)(nil),                // 10: settings.NetworkSettings
	(*FactorySettings)(nil),                // 11: settings.FactorySettings
	(*UserOptionsBitmap)(nil),              // 12: settings.UserOptionsBitmap
	(*UserSettings)(nil),                   // 13: settings.UserSettings
	(*PerChannelSettings)(nil),             // 14: settings.PerChannelSettings
	(*PerChannelCurrentSenseSettings)(nil), // 15: settings.PerChannelCurrentSenseSettings
	(*PerChannelPermissives)(nil),          // 16: settings.PerChannelPermissives
	(*FlashingYellowArrowSettings)(nil),    // 17: settings.FlashingYellowArrowSettings
	(*FlashAreaStatistics)(nil),            // 18: settings.FlashAreaStatistics
	(*PcbOptionsMmu)(nil),                  // 19: settings.PcbOptionsMmu
	(*FactoryOptionsMmu)(nil),              // 20: settings.FactoryOptionsMmu
	(*AgencyOptionsMmu)(nil),               // 21: settings.AgencyOptionsMmu
	(*basic.IpAddressV4)(nil),              // 22: basic.IpAddressV4
	(*basic.ModelAndSerialNumber)(nil),     // 23: basic.ModelAndSerialNumber
	(*basic.DateStr)(nil),                  // 24: basic.DateStr
}
var file_settings_proto_depIdxs = []int32{
	22, // 0: settings.NetworkSettings.monitor_ipv4_address:type_name -> basic.IpAddressV4
	22, // 1: settings.NetworkSettings.monitor_ipv4_subnet:type_name -> basic.IpAddressV4
	22, // 2: settings.NetworkSettings.monitor_ipv4_gateway:type_name -> basic.IpAddressV4
	23, // 3: settings.FactorySettings.numbers:type_name -> basic.ModelAndSerialNumber
	24, // 4: settings.FactorySettings.manufacture_date:type_name -> basic.DateStr
	19, // 5: settings.FactorySettings.pcb_options:type_name -> settings.PcbOptionsMmu
	20, // 6: settings.FactorySettings.factory_options:type_name -> settings.FactoryOptionsMmu
	12, // 7: settings.UserSettings.user_options:type_name -> settings.UserOptionsBitmap
	0,  // 8: settings.UserSettings.last_status_display:type_name -> settings.EStatusDisplay
	2,  // 9: settings.FlashingYellowArrowSettings.mode:type_name -> settings.EFlashingYellowArrowMode
	8,  // 10: settings.FactoryOptionsMmu.oem_type:type_name -> settings.EOemType
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_settings_proto_init() }
func file_settings_proto_init() {
	if File_settings_proto != nil {
		return
	}
	file_settings_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_settings_proto_rawDesc), len(file_settings_proto_rawDesc)),
			NumEnums:      10,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_settings_proto_goTypes,
		DependencyIndexes: file_settings_proto_depIdxs,
		EnumInfos:         file_settings_proto_enumTypes,
		MessageInfos:      file_settings_proto_msgTypes,
	}.Build()
	File_settings_proto = out.File
	file_settings_proto_goTypes = nil
	file_settings_proto_depIdxs = nil
}
