//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  DISCOVERY
//These messages are used for MMU disovery by the PC App over a UDP socket, and must be framed
//using the Authentication Transport Framing

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: discovery.proto

package discovery

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ENUM EDiscoveryDeviceType identifies the type of device.
type EDiscoveryDeviceType int32

const (
	EDiscoveryDeviceType_DISC_TYPE_UNSPECIFIED EDiscoveryDeviceType = 0 // For DiscHelloDevice, this means "ALL" device types
	EDiscoveryDeviceType_DISC_TYPE_MMU         EDiscoveryDeviceType = 1 // The responding device is an MMU
)

// Enum value maps for EDiscoveryDeviceType.
var (
	EDiscoveryDeviceType_name = map[int32]string{
		0: "DISC_TYPE_UNSPECIFIED",
		1: "DISC_TYPE_MMU",
	}
	EDiscoveryDeviceType_value = map[string]int32{
		"DISC_TYPE_UNSPECIFIED": 0,
		"DISC_TYPE_MMU":         1,
	}
)

func (x EDiscoveryDeviceType) Enum() *EDiscoveryDeviceType {
	p := new(EDiscoveryDeviceType)
	*p = x
	return p
}

func (x EDiscoveryDeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EDiscoveryDeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_discovery_proto_enumTypes[0].Descriptor()
}

func (EDiscoveryDeviceType) Type() protoreflect.EnumType {
	return &file_discovery_proto_enumTypes[0]
}

func (x EDiscoveryDeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EDiscoveryDeviceType.Descriptor instead.
func (EDiscoveryDeviceType) EnumDescriptor() ([]byte, []int) {
	return file_discovery_proto_rawDescGZIP(), []int{0}
}

//	DiscHelloDevice is sent by the PC Application to request a DiscHelloApp be sent back.  This message
//
// may be sent as a UDP broadcast to identify all devices on the local network, or as a directed
// UDP message to request a response from a specific unit.
type DiscHelloDevice struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the hello message version
	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	// The IP address and port where the PC App wants the device to send the DiscHelloApp response.
	// If pc_app_address is absent or blank, the device will send the response to the socket it
	// received the DiscHelloDevice datagram from.
	PcAppIp4Address *basic.IpAddressV4 `protobuf:"bytes,2,opt,name=pc_app_ip4_address,json=pcAppIp4Address,proto3" json:"pc_app_ip4_address,omitempty"`
	PcAppPort       uint32             `protobuf:"varint,3,opt,name=pc_app_port,json=pcAppPort,proto3" json:"pc_app_port,omitempty"`
	// The device type(s) that should reply.  If DISC_TYPE_UNSPECIFIED, then ALL device types
	// should respond.
	DeviceType EDiscoveryDeviceType `protobuf:"varint,5,opt,name=device_type,json=deviceType,proto3,enum=discovery.EDiscoveryDeviceType" json:"device_type,omitempty"`
	// The target group number of devices for a Broadcast DiscHelloDevice.  If non-zero, only devices
	// assigned to that group number will respond.  Used to limit the responses on a large network.
	// If group_number==0, the ALL devices will respond, regardless of their group number.
	// For a Directed DiscHelloDevice, this should be 0 to ensure the device responds.
	GroupNumber   uint32 `protobuf:"varint,4,opt,name=group_number,json=groupNumber,proto3" json:"group_number,omitempty"` // MAX 32767
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DiscHelloDevice) Reset() {
	*x = DiscHelloDevice{}
	mi := &file_discovery_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DiscHelloDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscHelloDevice) ProtoMessage() {}

func (x *DiscHelloDevice) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscHelloDevice.ProtoReflect.Descriptor instead.
func (*DiscHelloDevice) Descriptor() ([]byte, []int) {
	return file_discovery_proto_rawDescGZIP(), []int{0}
}

func (x *DiscHelloDevice) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *DiscHelloDevice) GetPcAppIp4Address() *basic.IpAddressV4 {
	if x != nil {
		return x.PcAppIp4Address
	}
	return nil
}

func (x *DiscHelloDevice) GetPcAppPort() uint32 {
	if x != nil {
		return x.PcAppPort
	}
	return 0
}

func (x *DiscHelloDevice) GetDeviceType() EDiscoveryDeviceType {
	if x != nil {
		return x.DeviceType
	}
	return EDiscoveryDeviceType_DISC_TYPE_UNSPECIFIED
}

func (x *DiscHelloDevice) GetGroupNumber() uint32 {
	if x != nil {
		return x.GroupNumber
	}
	return 0
}

// DiscHelloApp is sent by the device firmware in response to the DiscHelloDevice message.
type DiscHelloApp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the hello message version
	// NOTE: This may be less than the DiscHelloDevice.version, meaning the the firmware
	//
	//	only supports the version 1 fields in the DiscHelloDevice message and has
	//	responded accordingly.
	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	// The IP address of the device.  Normally this should match the socket the app received the datagram
	// from.
	DeviceIp4Address *basic.IpAddressV4 `protobuf:"bytes,2,opt,name=device_ip4_address,json=deviceIp4Address,proto3" json:"device_ip4_address,omitempty"`
	// The type of device that is sending this message
	DeviceType EDiscoveryDeviceType `protobuf:"varint,3,opt,name=device_type,json=deviceType,proto3,enum=discovery.EDiscoveryDeviceType" json:"device_type,omitempty"`
	// The group number that this device is assigned to.  If no group number is assigned,
	// the value will be zero (0)
	GroupNumber uint32 `protobuf:"varint,4,opt,name=group_number,json=groupNumber,proto3" json:"group_number,omitempty"`
	// A unique identifier for this device.  May be the serial number.
	DeviceId []byte `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"` // Max data length set in discovery.options
	// A text string with the user assigned name of the device.
	NameString    string `protobuf:"bytes,6,opt,name=name_string,json=nameString,proto3" json:"name_string,omitempty"` // Max string length set in discovery.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DiscHelloApp) Reset() {
	*x = DiscHelloApp{}
	mi := &file_discovery_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DiscHelloApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DiscHelloApp) ProtoMessage() {}

func (x *DiscHelloApp) ProtoReflect() protoreflect.Message {
	mi := &file_discovery_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DiscHelloApp.ProtoReflect.Descriptor instead.
func (*DiscHelloApp) Descriptor() ([]byte, []int) {
	return file_discovery_proto_rawDescGZIP(), []int{1}
}

func (x *DiscHelloApp) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *DiscHelloApp) GetDeviceIp4Address() *basic.IpAddressV4 {
	if x != nil {
		return x.DeviceIp4Address
	}
	return nil
}

func (x *DiscHelloApp) GetDeviceType() EDiscoveryDeviceType {
	if x != nil {
		return x.DeviceType
	}
	return EDiscoveryDeviceType_DISC_TYPE_UNSPECIFIED
}

func (x *DiscHelloApp) GetGroupNumber() uint32 {
	if x != nil {
		return x.GroupNumber
	}
	return 0
}

func (x *DiscHelloApp) GetDeviceId() []byte {
	if x != nil {
		return x.DeviceId
	}
	return nil
}

func (x *DiscHelloApp) GetNameString() string {
	if x != nil {
		return x.NameString
	}
	return ""
}

var File_discovery_proto protoreflect.FileDescriptor

const file_discovery_proto_rawDesc = "" +
	"\n" +
	"\x0fdiscovery.proto\x12\tdiscovery\x1a\vbasic.proto\"\xf1\x01\n" +
	"\x0fDiscHelloDevice\x12\x18\n" +
	"\aversion\x18\x01 \x01(\rR\aversion\x12?\n" +
	"\x12pc_app_ip4_address\x18\x02 \x01(\v2\x12.basic.IpAddressV4R\x0fpcAppIp4Address\x12\x1e\n" +
	"\vpc_app_port\x18\x03 \x01(\rR\tpcAppPort\x12@\n" +
	"\vdevice_type\x18\x05 \x01(\x0e2\x1f.discovery.EDiscoveryDeviceTypeR\n" +
	"deviceType\x12!\n" +
	"\fgroup_number\x18\x04 \x01(\rR\vgroupNumber\"\x8d\x02\n" +
	"\fDiscHelloApp\x12\x18\n" +
	"\aversion\x18\x01 \x01(\rR\aversion\x12@\n" +
	"\x12device_ip4_address\x18\x02 \x01(\v2\x12.basic.IpAddressV4R\x10deviceIp4Address\x12@\n" +
	"\vdevice_type\x18\x03 \x01(\x0e2\x1f.discovery.EDiscoveryDeviceTypeR\n" +
	"deviceType\x12!\n" +
	"\fgroup_number\x18\x04 \x01(\rR\vgroupNumber\x12\x1b\n" +
	"\tdevice_id\x18\x05 \x01(\fR\bdeviceId\x12\x1f\n" +
	"\vname_string\x18\x06 \x01(\tR\n" +
	"nameString*D\n" +
	"\x14EDiscoveryDeviceType\x12\x19\n" +
	"\x15DISC_TYPE_UNSPECIFIED\x10\x00\x12\x11\n" +
	"\rDISC_TYPE_MMU\x10\x01b\x06proto3"

var (
	file_discovery_proto_rawDescOnce sync.Once
	file_discovery_proto_rawDescData []byte
)

func file_discovery_proto_rawDescGZIP() []byte {
	file_discovery_proto_rawDescOnce.Do(func() {
		file_discovery_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_discovery_proto_rawDesc), len(file_discovery_proto_rawDesc)))
	})
	return file_discovery_proto_rawDescData
}

var file_discovery_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_discovery_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_discovery_proto_goTypes = []any{
	(EDiscoveryDeviceType)(0), // 0: discovery.EDiscoveryDeviceType
	(*DiscHelloDevice)(nil),   // 1: discovery.DiscHelloDevice
	(*DiscHelloApp)(nil),      // 2: discovery.DiscHelloApp
	(*basic.IpAddressV4)(nil), // 3: basic.IpAddressV4
}
var file_discovery_proto_depIdxs = []int32{
	3, // 0: discovery.DiscHelloDevice.pc_app_ip4_address:type_name -> basic.IpAddressV4
	0, // 1: discovery.DiscHelloDevice.device_type:type_name -> discovery.EDiscoveryDeviceType
	3, // 2: discovery.DiscHelloApp.device_ip4_address:type_name -> basic.IpAddressV4
	0, // 3: discovery.DiscHelloApp.device_type:type_name -> discovery.EDiscoveryDeviceType
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_discovery_proto_init() }
func file_discovery_proto_init() {
	if File_discovery_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_discovery_proto_rawDesc), len(file_discovery_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_discovery_proto_goTypes,
		DependencyIndexes: file_discovery_proto_depIdxs,
		EnumInfos:         file_discovery_proto_enumTypes,
		MessageInfos:      file_discovery_proto_msgTypes,
	}.Build()
	File_discovery_proto = out.File
	file_discovery_proto_goTypes = nil
	file_discovery_proto_depIdxs = nil
}
