//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  CMD_RESP_REALTIME
//Commands and responses for managing realtime data, and message formats for sending realtime data

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: cmd_resp_realtime.proto

package cmd_resp_realtime

import (
	realtime "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/realtime"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//	CmdStartRealtimeData enables publishing of real-time data messages with the selected data at
//
// the selected interval.
// This command may be sent while real-time data is already enabled to change the selected data
// and/or intervals.  This command may also stop real-time data by setting channel_status_chmap = 0
// and all "bool" fields to false, and/or sending send_interval_ms = 0
type CmdStartRealtimeData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// If this field is non-zero, then a "RealtimeData1" message will be sent with the field "channel_status",
	// which will have measured values for the selected channels.  Channels are selected by setting the
	// bit in this field for the channel.
	// The value is a bitmap of the channels for which channel data will be sent.  ('1' bit = enabled) LSbit = Ch.1
	SendChannelStatusChmap uint32 `protobuf:"fixed32,1,opt,name=send_channel_status_chmap,json=sendChannelStatusChmap,proto3" json:"send_channel_status_chmap,omitempty"`
	// Fault information, general status, and monitored input voltages will be sent if true.
	// The data is sent in a "RealtimeData1" message field "monitor_fault_and_status"
	SendMonitorFaultAndStatus bool `protobuf:"varint,2,opt,name=send_monitor_fault_and_status,json=sendMonitorFaultAndStatus,proto3" json:"send_monitor_fault_and_status,omitempty"`
	// The monitor panel LED states and LCD display text will be sent if true.  This will be sent in a
	// "RealtimeDisplay1" message field "monitor_display"
	SendMonitorDisplay bool `protobuf:"varint,3,opt,name=send_monitor_display,json=sendMonitorDisplay,proto3" json:"send_monitor_display,omitempty"`
	// If true, then after the first message only display text lines that have changed will be sent,
	// though the update_full_display_data_seconds will be used subsequently to determine when and if
	// the full display data is sent again.
	// This can reduce the total realtime data significantly.
	// If false, the full display data is always sent when send_monitor_display is true.
	MonitorDisplayChangedOnly bool `protobuf:"varint,4,opt,name=monitor_display_changed_only,json=monitorDisplayChangedOnly,proto3" json:"monitor_display_changed_only,omitempty"`
	// The interval at which to send the real-time data in milliseconds.  The value will be rounded
	// up to the next multiple of 100 ms.  A value greater than 10,000 (10 seconds) will be set as
	// 10,000.  A value of 0 will be rounded up to 100.
	SendIntervalMs uint32 `protobuf:"varint,5,opt,name=send_interval_ms,json=sendIntervalMs,proto3" json:"send_interval_ms,omitempty"`
	// If monitor_display_changed_only is true, this is the number of seconds between updates of the full display data.
	// If 0 when monitor_display_changed_only is true, then the full display data is only sent once.
	UpdateFullDisplayDataSeconds uint32 `protobuf:"varint,6,opt,name=update_full_display_data_seconds,json=updateFullDisplayDataSeconds,proto3" json:"update_full_display_data_seconds,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *CmdStartRealtimeData) Reset() {
	*x = CmdStartRealtimeData{}
	mi := &file_cmd_resp_realtime_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdStartRealtimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdStartRealtimeData) ProtoMessage() {}

func (x *CmdStartRealtimeData) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_realtime_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdStartRealtimeData.ProtoReflect.Descriptor instead.
func (*CmdStartRealtimeData) Descriptor() ([]byte, []int) {
	return file_cmd_resp_realtime_proto_rawDescGZIP(), []int{0}
}

func (x *CmdStartRealtimeData) GetSendChannelStatusChmap() uint32 {
	if x != nil {
		return x.SendChannelStatusChmap
	}
	return 0
}

func (x *CmdStartRealtimeData) GetSendMonitorFaultAndStatus() bool {
	if x != nil {
		return x.SendMonitorFaultAndStatus
	}
	return false
}

func (x *CmdStartRealtimeData) GetSendMonitorDisplay() bool {
	if x != nil {
		return x.SendMonitorDisplay
	}
	return false
}

func (x *CmdStartRealtimeData) GetMonitorDisplayChangedOnly() bool {
	if x != nil {
		return x.MonitorDisplayChangedOnly
	}
	return false
}

func (x *CmdStartRealtimeData) GetSendIntervalMs() uint32 {
	if x != nil {
		return x.SendIntervalMs
	}
	return 0
}

func (x *CmdStartRealtimeData) GetUpdateFullDisplayDataSeconds() uint32 {
	if x != nil {
		return x.UpdateFullDisplayDataSeconds
	}
	return 0
}

// RespStartRealtimeData - confirms the enable or update of real-time data
type RespStartRealtimeData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The actual real-time send interval (after rounding) that is used.  If 0, then real-time
	// data was stopped.
	SendIntervalMs uint32 `protobuf:"varint,1,opt,name=send_interval_ms,json=sendIntervalMs,proto3" json:"send_interval_ms,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RespStartRealtimeData) Reset() {
	*x = RespStartRealtimeData{}
	mi := &file_cmd_resp_realtime_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespStartRealtimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespStartRealtimeData) ProtoMessage() {}

func (x *RespStartRealtimeData) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_realtime_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespStartRealtimeData.ProtoReflect.Descriptor instead.
func (*RespStartRealtimeData) Descriptor() ([]byte, []int) {
	return file_cmd_resp_realtime_proto_rawDescGZIP(), []int{1}
}

func (x *RespStartRealtimeData) GetSendIntervalMs() uint32 {
	if x != nil {
		return x.SendIntervalMs
	}
	return 0
}

// CmdStopRealtimeData will stop any real-time data being sent
type CmdStopRealtimeData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdStopRealtimeData) Reset() {
	*x = CmdStopRealtimeData{}
	mi := &file_cmd_resp_realtime_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdStopRealtimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdStopRealtimeData) ProtoMessage() {}

func (x *CmdStopRealtimeData) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_realtime_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdStopRealtimeData.ProtoReflect.Descriptor instead.
func (*CmdStopRealtimeData) Descriptor() ([]byte, []int) {
	return file_cmd_resp_realtime_proto_rawDescGZIP(), []int{2}
}

func (x *CmdStopRealtimeData) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

// RespStopRealtimeData - confirms the enable or update of real-time data
type RespStopRealtimeData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespStopRealtimeData) Reset() {
	*x = RespStopRealtimeData{}
	mi := &file_cmd_resp_realtime_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespStopRealtimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespStopRealtimeData) ProtoMessage() {}

func (x *RespStopRealtimeData) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_realtime_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespStopRealtimeData.ProtoReflect.Descriptor instead.
func (*RespStopRealtimeData) Descriptor() ([]byte, []int) {
	return file_cmd_resp_realtime_proto_rawDescGZIP(), []int{3}
}

func (x *RespStopRealtimeData) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

// RealtimeData1 - This message is used to send real-time operational data to the application
type RealtimeData1 struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Realtime data message sequence number.  Increments for each message (of this type) sent.
	// It may also be reset to 1 at any time.
	// May be used by the App to determine if it is missing messages.
	SequenceNumber uint32 `protobuf:"varint,1,opt,name=sequence_number,json=sequenceNumber,proto3" json:"sequence_number,omitempty"`
	// provides the indicator on/off and field checks for all channels, and voltages
	// and timers for desired channels.
	ChannelStatus *realtime.ChannelStatusData `protobuf:"bytes,2,opt,name=channel_status,json=channelStatus,proto3,oneof" json:"channel_status,omitempty"`
	// provides fault, status, and monitored inputs
	MonitorFaultAndStatus *realtime.MonitorPresentStatus `protobuf:"bytes,3,opt,name=monitor_fault_and_status,json=monitorFaultAndStatus,proto3,oneof" json:"monitor_fault_and_status,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *RealtimeData1) Reset() {
	*x = RealtimeData1{}
	mi := &file_cmd_resp_realtime_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RealtimeData1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealtimeData1) ProtoMessage() {}

func (x *RealtimeData1) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_realtime_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealtimeData1.ProtoReflect.Descriptor instead.
func (*RealtimeData1) Descriptor() ([]byte, []int) {
	return file_cmd_resp_realtime_proto_rawDescGZIP(), []int{4}
}

func (x *RealtimeData1) GetSequenceNumber() uint32 {
	if x != nil {
		return x.SequenceNumber
	}
	return 0
}

func (x *RealtimeData1) GetChannelStatus() *realtime.ChannelStatusData {
	if x != nil {
		return x.ChannelStatus
	}
	return nil
}

func (x *RealtimeData1) GetMonitorFaultAndStatus() *realtime.MonitorPresentStatus {
	if x != nil {
		return x.MonitorFaultAndStatus
	}
	return nil
}

// RealtimeDisplay1 - This message is used to send real-time display info to the application
type RealtimeDisplay1 struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Realtime display message sequence number.  Increments for each message (of this type) sent.
	// It may also be reset to 1 at any time.
	// May be used by the App to determine if it is missing messages.
	SequenceNumber uint32 `protobuf:"varint,1,opt,name=sequence_number,json=sequenceNumber,proto3" json:"sequence_number,omitempty"`
	// provides the panel indicator displays for all channels and the display text.
	MonitorDisplay *realtime.MonitorDisplayData `protobuf:"bytes,2,opt,name=monitor_display,json=monitorDisplay,proto3" json:"monitor_display,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RealtimeDisplay1) Reset() {
	*x = RealtimeDisplay1{}
	mi := &file_cmd_resp_realtime_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RealtimeDisplay1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RealtimeDisplay1) ProtoMessage() {}

func (x *RealtimeDisplay1) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_realtime_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RealtimeDisplay1.ProtoReflect.Descriptor instead.
func (*RealtimeDisplay1) Descriptor() ([]byte, []int) {
	return file_cmd_resp_realtime_proto_rawDescGZIP(), []int{5}
}

func (x *RealtimeDisplay1) GetSequenceNumber() uint32 {
	if x != nil {
		return x.SequenceNumber
	}
	return 0
}

func (x *RealtimeDisplay1) GetMonitorDisplay() *realtime.MonitorDisplayData {
	if x != nil {
		return x.MonitorDisplay
	}
	return nil
}

var File_cmd_resp_realtime_proto protoreflect.FileDescriptor

const file_cmd_resp_realtime_proto_rawDesc = "" +
	"\n" +
	"\x17cmd_resp_realtime.proto\x12\x11cmd_resp_realtime\x1a\x0erealtime.proto\"\xf8\x02\n" +
	"\x14CmdStartRealtimeData\x129\n" +
	"\x19send_channel_status_chmap\x18\x01 \x01(\aR\x16sendChannelStatusChmap\x12@\n" +
	"\x1dsend_monitor_fault_and_status\x18\x02 \x01(\bR\x19sendMonitorFaultAndStatus\x120\n" +
	"\x14send_monitor_display\x18\x03 \x01(\bR\x12sendMonitorDisplay\x12?\n" +
	"\x1cmonitor_display_changed_only\x18\x04 \x01(\bR\x19monitorDisplayChangedOnly\x12(\n" +
	"\x10send_interval_ms\x18\x05 \x01(\rR\x0esendIntervalMs\x12F\n" +
	" update_full_display_data_seconds\x18\x06 \x01(\rR\x1cupdateFullDisplayDataSeconds\"A\n" +
	"\x15RespStartRealtimeData\x12(\n" +
	"\x10send_interval_ms\x18\x01 \x01(\rR\x0esendIntervalMs\"/\n" +
	"\x13CmdStopRealtimeData\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"0\n" +
	"\x14RespStopRealtimeData\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\x8f\x02\n" +
	"\rRealtimeData1\x12'\n" +
	"\x0fsequence_number\x18\x01 \x01(\rR\x0esequenceNumber\x12G\n" +
	"\x0echannel_status\x18\x02 \x01(\v2\x1b.realtime.ChannelStatusDataH\x00R\rchannelStatus\x88\x01\x01\x12\\\n" +
	"\x18monitor_fault_and_status\x18\x03 \x01(\v2\x1e.realtime.MonitorPresentStatusH\x01R\x15monitorFaultAndStatus\x88\x01\x01B\x11\n" +
	"\x0f_channel_statusB\x1b\n" +
	"\x19_monitor_fault_and_status\"\x82\x01\n" +
	"\x10RealtimeDisplay1\x12'\n" +
	"\x0fsequence_number\x18\x01 \x01(\rR\x0esequenceNumber\x12E\n" +
	"\x0fmonitor_display\x18\x02 \x01(\v2\x1c.realtime.MonitorDisplayDataR\x0emonitorDisplayb\x06proto3"

var (
	file_cmd_resp_realtime_proto_rawDescOnce sync.Once
	file_cmd_resp_realtime_proto_rawDescData []byte
)

func file_cmd_resp_realtime_proto_rawDescGZIP() []byte {
	file_cmd_resp_realtime_proto_rawDescOnce.Do(func() {
		file_cmd_resp_realtime_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cmd_resp_realtime_proto_rawDesc), len(file_cmd_resp_realtime_proto_rawDesc)))
	})
	return file_cmd_resp_realtime_proto_rawDescData
}

var file_cmd_resp_realtime_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_cmd_resp_realtime_proto_goTypes = []any{
	(*CmdStartRealtimeData)(nil),          // 0: cmd_resp_realtime.CmdStartRealtimeData
	(*RespStartRealtimeData)(nil),         // 1: cmd_resp_realtime.RespStartRealtimeData
	(*CmdStopRealtimeData)(nil),           // 2: cmd_resp_realtime.CmdStopRealtimeData
	(*RespStopRealtimeData)(nil),          // 3: cmd_resp_realtime.RespStopRealtimeData
	(*RealtimeData1)(nil),                 // 4: cmd_resp_realtime.RealtimeData1
	(*RealtimeDisplay1)(nil),              // 5: cmd_resp_realtime.RealtimeDisplay1
	(*realtime.ChannelStatusData)(nil),    // 6: realtime.ChannelStatusData
	(*realtime.MonitorPresentStatus)(nil), // 7: realtime.MonitorPresentStatus
	(*realtime.MonitorDisplayData)(nil),   // 8: realtime.MonitorDisplayData
}
var file_cmd_resp_realtime_proto_depIdxs = []int32{
	6, // 0: cmd_resp_realtime.RealtimeData1.channel_status:type_name -> realtime.ChannelStatusData
	7, // 1: cmd_resp_realtime.RealtimeData1.monitor_fault_and_status:type_name -> realtime.MonitorPresentStatus
	8, // 2: cmd_resp_realtime.RealtimeDisplay1.monitor_display:type_name -> realtime.MonitorDisplayData
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_cmd_resp_realtime_proto_init() }
func file_cmd_resp_realtime_proto_init() {
	if File_cmd_resp_realtime_proto != nil {
		return
	}
	file_cmd_resp_realtime_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cmd_resp_realtime_proto_rawDesc), len(file_cmd_resp_realtime_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_resp_realtime_proto_goTypes,
		DependencyIndexes: file_cmd_resp_realtime_proto_depIdxs,
		MessageInfos:      file_cmd_resp_realtime_proto_msgTypes,
	}.Build()
	File_cmd_resp_realtime_proto = out.File
	file_cmd_resp_realtime_proto_goTypes = nil
	file_cmd_resp_realtime_proto_depIdxs = nil
}
