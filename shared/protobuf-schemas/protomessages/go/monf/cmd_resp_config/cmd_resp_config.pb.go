//*
//*******************************************************************************************************
// © Copyright 2024- Synapse ITS
//*******************************************************************************************************
//
//---------------------------------------------------------------------------------------------------------
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
//>>>>>>>>>                                                                                     <<<<<<<<<<<
//---------------------------------------------------------------------------------------------------------

//  CMD_RESP_CONFIG
//Command (from app) and response (from monitor) message formats for configuration related commands.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: cmd_resp_config.proto

package cmd_resp_config

import (
	basic "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/basic"
	dfu "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/dfu"
	mon_logs "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/mon_logs"
	settings "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/settings"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Types for Remote Configuration Acceptance / Fault Clear
type CpRemoteResetType int32

const (
	CpRemoteResetType_REMOTE_RESET_TYPE_UNSPECIFIED               CpRemoteResetType = 0
	CpRemoteResetType_REMOTE_RESET_TYPE_NONE                      CpRemoteResetType = 1
	CpRemoteResetType_REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE CpRemoteResetType = 2
	CpRemoteResetType_REMOTE_RESET_TYPE_FAULT_CLEAR               CpRemoteResetType = 3
)

// Enum value maps for CpRemoteResetType.
var (
	CpRemoteResetType_name = map[int32]string{
		0: "REMOTE_RESET_TYPE_UNSPECIFIED",
		1: "REMOTE_RESET_TYPE_NONE",
		2: "REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE",
		3: "REMOTE_RESET_TYPE_FAULT_CLEAR",
	}
	CpRemoteResetType_value = map[string]int32{
		"REMOTE_RESET_TYPE_UNSPECIFIED":               0,
		"REMOTE_RESET_TYPE_NONE":                      1,
		"REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE": 2,
		"REMOTE_RESET_TYPE_FAULT_CLEAR":               3,
	}
)

func (x CpRemoteResetType) Enum() *CpRemoteResetType {
	p := new(CpRemoteResetType)
	*p = x
	return p
}

func (x CpRemoteResetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CpRemoteResetType) Descriptor() protoreflect.EnumDescriptor {
	return file_cmd_resp_config_proto_enumTypes[0].Descriptor()
}

func (CpRemoteResetType) Type() protoreflect.EnumType {
	return &file_cmd_resp_config_proto_enumTypes[0]
}

func (x CpRemoteResetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CpRemoteResetType.Descriptor instead.
func (CpRemoteResetType) EnumDescriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{0}
}

// CmdReadDataKey is used to Read the Program Card / Data Key (0x20)
type CmdReadDataKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Type of the Program Card / Data Key data to be read (Current or Pending).
	PcDkType      settings.EPcDkReadType `protobuf:"varint,1,opt,name=pc_dk_type,json=pcDkType,proto3,enum=settings.EPcDkReadType" json:"pc_dk_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadDataKey) Reset() {
	*x = CmdReadDataKey{}
	mi := &file_cmd_resp_config_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadDataKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadDataKey) ProtoMessage() {}

func (x *CmdReadDataKey) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadDataKey.ProtoReflect.Descriptor instead.
func (*CmdReadDataKey) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{0}
}

func (x *CmdReadDataKey) GetPcDkType() settings.EPcDkReadType {
	if x != nil {
		return x.PcDkType
	}
	return settings.EPcDkReadType(0)
}

//	RespReadDataKey returns the contents of the monitor's data key or program card.
//
// The data comes from the Main processor response to the command 0x20 Read Data Key
type RespReadDataKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This shows the source (data set) of the data that was read.
	Source settings.EConfigDataLocation `protobuf:"varint,1,opt,name=source,proto3,enum=settings.EConfigDataLocation" json:"source,omitempty"`
	// Raw contents of the entire Program Card or Data Key
	DataKeyData []byte `protobuf:"bytes,2,opt,name=data_key_data,json=dataKeyData,proto3" json:"data_key_data,omitempty"` // Max data length set in cmd_resp_config.options
	// Type of the Program Card / Data Key data that was read.
	// If Pending was requested but there is no pending data, the response will be PC_DK_NONE
	// and the data_key_data will be empty.
	PcDkType      settings.EPcDkReadType `protobuf:"varint,3,opt,name=pc_dk_type,json=pcDkType,proto3,enum=settings.EPcDkReadType" json:"pc_dk_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadDataKey) Reset() {
	*x = RespReadDataKey{}
	mi := &file_cmd_resp_config_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadDataKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadDataKey) ProtoMessage() {}

func (x *RespReadDataKey) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadDataKey.ProtoReflect.Descriptor instead.
func (*RespReadDataKey) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{1}
}

func (x *RespReadDataKey) GetSource() settings.EConfigDataLocation {
	if x != nil {
		return x.Source
	}
	return settings.EConfigDataLocation(0)
}

func (x *RespReadDataKey) GetDataKeyData() []byte {
	if x != nil {
		return x.DataKeyData
	}
	return nil
}

func (x *RespReadDataKey) GetPcDkType() settings.EPcDkReadType {
	if x != nil {
		return x.PcDkType
	}
	return settings.EPcDkReadType(0)
}

// CmdWriteDataKey is used to Write the Program Card / Data Key (0xA0)
type CmdWriteDataKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is the destination (data set) for the data being written.
	Destination settings.EConfigDataLocation `protobuf:"varint,1,opt,name=destination,proto3,enum=settings.EConfigDataLocation" json:"destination,omitempty"`
	// Raw contents for the entire Program Card or Data Key
	DataKeyData   []byte `protobuf:"bytes,2,opt,name=data_key_data,json=dataKeyData,proto3" json:"data_key_data,omitempty"` // Max data length set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdWriteDataKey) Reset() {
	*x = CmdWriteDataKey{}
	mi := &file_cmd_resp_config_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdWriteDataKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdWriteDataKey) ProtoMessage() {}

func (x *CmdWriteDataKey) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdWriteDataKey.ProtoReflect.Descriptor instead.
func (*CmdWriteDataKey) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{2}
}

func (x *CmdWriteDataKey) GetDestination() settings.EConfigDataLocation {
	if x != nil {
		return x.Destination
	}
	return settings.EConfigDataLocation(0)
}

func (x *CmdWriteDataKey) GetDataKeyData() []byte {
	if x != nil {
		return x.DataKeyData
	}
	return nil
}

//	RespWriteDataKey returns the result of the attempt to write the Pending Program Card / Data Key data.
//
// The data comes from the Main processor response to the command 0xA0 Write Program Card / Data Key.
// NOTE: The data will be kept in a Pending state until an agency representative "accepts" the configuration.
type RespWriteDataKey struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Result of the write operation
	Result        settings.EWriteResult `protobuf:"varint,1,opt,name=result,proto3,enum=settings.EWriteResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespWriteDataKey) Reset() {
	*x = RespWriteDataKey{}
	mi := &file_cmd_resp_config_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespWriteDataKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespWriteDataKey) ProtoMessage() {}

func (x *RespWriteDataKey) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespWriteDataKey.ProtoReflect.Descriptor instead.
func (*RespWriteDataKey) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{3}
}

func (x *RespWriteDataKey) GetResult() settings.EWriteResult {
	if x != nil {
		return x.Result
	}
	return settings.EWriteResult(0)
}

// CmdReadFactorySettings is used to read the monitor's factory configuration
type CmdReadFactorySettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadFactorySettings) Reset() {
	*x = CmdReadFactorySettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadFactorySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadFactorySettings) ProtoMessage() {}

func (x *CmdReadFactorySettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadFactorySettings.ProtoReflect.Descriptor instead.
func (*CmdReadFactorySettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{4}
}

func (x *CmdReadFactorySettings) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadFactorySettings returns factory information from the Main processor response
//
// to the command 0x21 Read Factory Settings
type RespReadFactorySettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Monitor settings read from the unit
	Factory *settings.FactorySettings `protobuf:"bytes,1,opt,name=factory,proto3" json:"factory,omitempty"`
	// monitor hardware revisions
	Hardware *mon_logs.HardwareRevisionsMmu `protobuf:"bytes,2,opt,name=hardware,proto3" json:"hardware,omitempty"`
	// the maximum number of channels this monitor supports.
	MaxChannelsSupported uint32 `protobuf:"varint,3,opt,name=max_channels_supported,json=maxChannelsSupported,proto3" json:"max_channels_supported,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RespReadFactorySettings) Reset() {
	*x = RespReadFactorySettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadFactorySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadFactorySettings) ProtoMessage() {}

func (x *RespReadFactorySettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadFactorySettings.ProtoReflect.Descriptor instead.
func (*RespReadFactorySettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{5}
}

func (x *RespReadFactorySettings) GetFactory() *settings.FactorySettings {
	if x != nil {
		return x.Factory
	}
	return nil
}

func (x *RespReadFactorySettings) GetHardware() *mon_logs.HardwareRevisionsMmu {
	if x != nil {
		return x.Hardware
	}
	return nil
}

func (x *RespReadFactorySettings) GetMaxChannelsSupported() uint32 {
	if x != nil {
		return x.MaxChannelsSupported
	}
	return 0
}

// CmdWriteFactorySettings is used to write the monitor's factory configuration.
//
// Details: Will wait for the Main processor to complete writing the settings to the unit
// before returning.
//
// Note: The value for the pcb_options field must match the Main Processor PCB Option Jumpers,
// as seen in the RespReadMonitorData message, for the command to be accepted.
type CmdWriteFactorySettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Monitor settings to be written to the unit
	Factory       *settings.FactorySettings `protobuf:"bytes,1,opt,name=factory,proto3" json:"factory,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdWriteFactorySettings) Reset() {
	*x = CmdWriteFactorySettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdWriteFactorySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdWriteFactorySettings) ProtoMessage() {}

func (x *CmdWriteFactorySettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdWriteFactorySettings.ProtoReflect.Descriptor instead.
func (*CmdWriteFactorySettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{6}
}

func (x *CmdWriteFactorySettings) GetFactory() *settings.FactorySettings {
	if x != nil {
		return x.Factory
	}
	return nil
}

//	RespWriteFactorySettings result of writing the settings.
//
// The data comes from the Main processor response to the command 0xA2 Write Factory Data
// AND 0xA3 Check Write to Factory Data
// NOTE: The monitor firmware will check the result and return the response at completion
type RespWriteFactorySettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        settings.EWriteResult  `protobuf:"varint,1,opt,name=result,proto3,enum=settings.EWriteResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespWriteFactorySettings) Reset() {
	*x = RespWriteFactorySettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespWriteFactorySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespWriteFactorySettings) ProtoMessage() {}

func (x *RespWriteFactorySettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespWriteFactorySettings.ProtoReflect.Descriptor instead.
func (*RespWriteFactorySettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{7}
}

func (x *RespWriteFactorySettings) GetResult() settings.EWriteResult {
	if x != nil {
		return x.Result
	}
	return settings.EWriteResult(0)
}

// CmdWriteAgencyOptions is used to Write the Agency Options (0xAC) to the monitor.
//
// Details: Will wait for the Main processor to complete writing the new Config Log entry
// before returning.
type CmdWriteAgencyOptions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Agency settings to be written to the unit
	Agency        *settings.AgencyOptionsMmu `protobuf:"bytes,1,opt,name=agency,proto3" json:"agency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdWriteAgencyOptions) Reset() {
	*x = CmdWriteAgencyOptions{}
	mi := &file_cmd_resp_config_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdWriteAgencyOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdWriteAgencyOptions) ProtoMessage() {}

func (x *CmdWriteAgencyOptions) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdWriteAgencyOptions.ProtoReflect.Descriptor instead.
func (*CmdWriteAgencyOptions) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{8}
}

func (x *CmdWriteAgencyOptions) GetAgency() *settings.AgencyOptionsMmu {
	if x != nil {
		return x.Agency
	}
	return nil
}

//	RespWriteAgencyOptions result of writing these options.
//
// The data comes from the Main processor response to the Write Agency Options (0xAC) command.
// NOTE: The monitor firmware will check the result and return the response at completion
type RespWriteAgencyOptions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        settings.EWriteResult  `protobuf:"varint,1,opt,name=result,proto3,enum=settings.EWriteResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespWriteAgencyOptions) Reset() {
	*x = RespWriteAgencyOptions{}
	mi := &file_cmd_resp_config_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespWriteAgencyOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespWriteAgencyOptions) ProtoMessage() {}

func (x *RespWriteAgencyOptions) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespWriteAgencyOptions.ProtoReflect.Descriptor instead.
func (*RespWriteAgencyOptions) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{9}
}

func (x *RespWriteAgencyOptions) GetResult() settings.EWriteResult {
	if x != nil {
		return x.Result
	}
	return settings.EWriteResult(0)
}

//	CmdReadMonitorData is a single command to get both factory settings and firmware versions
//
// along with total monitor runtme
type CmdReadMonitorData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadMonitorData) Reset() {
	*x = CmdReadMonitorData{}
	mi := &file_cmd_resp_config_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadMonitorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadMonitorData) ProtoMessage() {}

func (x *CmdReadMonitorData) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadMonitorData.ProtoReflect.Descriptor instead.
func (*CmdReadMonitorData) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{10}
}

func (x *CmdReadMonitorData) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadMonitorData returns factory settings message with a firmware versions message
//
// and the monitor runtime in seconds.
// The data comes from the Main processor response to the command 0x23 Read Monitor Data
type RespReadMonitorData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Monitor settings read from the unit
	Factory *settings.FactorySettings `protobuf:"bytes,1,opt,name=factory,proto3" json:"factory,omitempty"`
	// The Monitor ID, assigned by the agency
	MonitorId string `protobuf:"bytes,7,opt,name=monitor_id,json=monitorId,proto3" json:"monitor_id,omitempty"`
	// User assigned description of the monitor
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"` // Max string length set in cmd_resp_config.options
	// monitor hardware revisions
	Hardware *mon_logs.HardwareRevisionsMmu `protobuf:"bytes,3,opt,name=hardware,proto3" json:"hardware,omitempty"`
	// supported number of channels
	SupportedChannelCount uint32 `protobuf:"varint,4,opt,name=supported_channel_count,json=supportedChannelCount,proto3" json:"supported_channel_count,omitempty"`
	// (Uptime) Total run time for the monitor since being shipped, in seconds
	RunTimeSeconds uint32 `protobuf:"varint,5,opt,name=run_time_seconds,json=runTimeSeconds,proto3" json:"run_time_seconds,omitempty"`
	// Firmware version information
	FwVersions    []*dfu.FirmwareVersionSimple `protobuf:"bytes,6,rep,name=fw_versions,json=fwVersions,proto3" json:"fw_versions,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadMonitorData) Reset() {
	*x = RespReadMonitorData{}
	mi := &file_cmd_resp_config_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadMonitorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadMonitorData) ProtoMessage() {}

func (x *RespReadMonitorData) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadMonitorData.ProtoReflect.Descriptor instead.
func (*RespReadMonitorData) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{11}
}

func (x *RespReadMonitorData) GetFactory() *settings.FactorySettings {
	if x != nil {
		return x.Factory
	}
	return nil
}

func (x *RespReadMonitorData) GetMonitorId() string {
	if x != nil {
		return x.MonitorId
	}
	return ""
}

func (x *RespReadMonitorData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RespReadMonitorData) GetHardware() *mon_logs.HardwareRevisionsMmu {
	if x != nil {
		return x.Hardware
	}
	return nil
}

func (x *RespReadMonitorData) GetSupportedChannelCount() uint32 {
	if x != nil {
		return x.SupportedChannelCount
	}
	return 0
}

func (x *RespReadMonitorData) GetRunTimeSeconds() uint32 {
	if x != nil {
		return x.RunTimeSeconds
	}
	return 0
}

func (x *RespReadMonitorData) GetFwVersions() []*dfu.FirmwareVersionSimple {
	if x != nil {
		return x.FwVersions
	}
	return nil
}

// CmdReadUserSettings returns customer configurable settings
type CmdReadUserSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadUserSettings) Reset() {
	*x = CmdReadUserSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadUserSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadUserSettings) ProtoMessage() {}

func (x *CmdReadUserSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadUserSettings.ProtoReflect.Descriptor instead.
func (*CmdReadUserSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{12}
}

func (x *CmdReadUserSettings) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadUserSettings
//
// The data comes from the Main processor response to the command 0x24 Read User Settings
type RespReadUserSettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Values        *settings.UserSettings `protobuf:"bytes,1,opt,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadUserSettings) Reset() {
	*x = RespReadUserSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadUserSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadUserSettings) ProtoMessage() {}

func (x *RespReadUserSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadUserSettings.ProtoReflect.Descriptor instead.
func (*RespReadUserSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{13}
}

func (x *RespReadUserSettings) GetValues() *settings.UserSettings {
	if x != nil {
		return x.Values
	}
	return nil
}

// CmdWriteUserSettings sets customer configurable settings
type CmdWriteUserSettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Values        *settings.UserSettings `protobuf:"bytes,1,opt,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdWriteUserSettings) Reset() {
	*x = CmdWriteUserSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdWriteUserSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdWriteUserSettings) ProtoMessage() {}

func (x *CmdWriteUserSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdWriteUserSettings.ProtoReflect.Descriptor instead.
func (*CmdWriteUserSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{14}
}

func (x *CmdWriteUserSettings) GetValues() *settings.UserSettings {
	if x != nil {
		return x.Values
	}
	return nil
}

//	RespWriteUserSettings
//
// The data comes from the Main processor response to the command 0xA8 Write User Settings
type RespWriteUserSettings struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        settings.EWriteResult  `protobuf:"varint,1,opt,name=result,proto3,enum=settings.EWriteResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespWriteUserSettings) Reset() {
	*x = RespWriteUserSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespWriteUserSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespWriteUserSettings) ProtoMessage() {}

func (x *RespWriteUserSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespWriteUserSettings.ProtoReflect.Descriptor instead.
func (*RespWriteUserSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{15}
}

func (x *RespWriteUserSettings) GetResult() settings.EWriteResult {
	if x != nil {
		return x.Result
	}
	return settings.EWriteResult(0)
}

// CmdReadPort1DisableOverrides returns the state of the disable overrides for Serial Bus (SDLC)/Port 1
type CmdReadPort1DisableOverrides struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadPort1DisableOverrides) Reset() {
	*x = CmdReadPort1DisableOverrides{}
	mi := &file_cmd_resp_config_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadPort1DisableOverrides) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadPort1DisableOverrides) ProtoMessage() {}

func (x *CmdReadPort1DisableOverrides) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadPort1DisableOverrides.ProtoReflect.Descriptor instead.
func (*CmdReadPort1DisableOverrides) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{16}
}

func (x *CmdReadPort1DisableOverrides) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadPort1DisableOverrides
//
// The data comes from the Main processor response to the command 0x25 Read Port 1 Disable Overrides
type RespReadPort1DisableOverrides struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// State of the Port 1 Disable Override
	Port1DisableOverride settings.EDisableOverrideState `protobuf:"varint,1,opt,name=port1_disable_override,json=port1DisableOverride,proto3,enum=settings.EDisableOverrideState" json:"port1_disable_override,omitempty"`
	// Field checks disabled
	FieldChecksDisabled bool `protobuf:"varint,2,opt,name=field_checks_disabled,json=fieldChecksDisabled,proto3" json:"field_checks_disabled,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RespReadPort1DisableOverrides) Reset() {
	*x = RespReadPort1DisableOverrides{}
	mi := &file_cmd_resp_config_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadPort1DisableOverrides) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadPort1DisableOverrides) ProtoMessage() {}

func (x *RespReadPort1DisableOverrides) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadPort1DisableOverrides.ProtoReflect.Descriptor instead.
func (*RespReadPort1DisableOverrides) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{17}
}

func (x *RespReadPort1DisableOverrides) GetPort1DisableOverride() settings.EDisableOverrideState {
	if x != nil {
		return x.Port1DisableOverride
	}
	return settings.EDisableOverrideState(0)
}

func (x *RespReadPort1DisableOverrides) GetFieldChecksDisabled() bool {
	if x != nil {
		return x.FieldChecksDisabled
	}
	return false
}

// CmdWritePort1DisableOverrides sets the state of the disable overrides for Serial Bus/Port 1
type CmdWritePort1DisableOverrides struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// State of the Port 1 Disable Override
	Port1DisableOverride settings.EDisableOverrideState `protobuf:"varint,1,opt,name=port1_disable_override,json=port1DisableOverride,proto3,enum=settings.EDisableOverrideState" json:"port1_disable_override,omitempty"`
	// Field checks disable
	FieldChecksDisable bool `protobuf:"varint,2,opt,name=field_checks_disable,json=fieldChecksDisable,proto3" json:"field_checks_disable,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CmdWritePort1DisableOverrides) Reset() {
	*x = CmdWritePort1DisableOverrides{}
	mi := &file_cmd_resp_config_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdWritePort1DisableOverrides) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdWritePort1DisableOverrides) ProtoMessage() {}

func (x *CmdWritePort1DisableOverrides) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdWritePort1DisableOverrides.ProtoReflect.Descriptor instead.
func (*CmdWritePort1DisableOverrides) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{18}
}

func (x *CmdWritePort1DisableOverrides) GetPort1DisableOverride() settings.EDisableOverrideState {
	if x != nil {
		return x.Port1DisableOverride
	}
	return settings.EDisableOverrideState(0)
}

func (x *CmdWritePort1DisableOverrides) GetFieldChecksDisable() bool {
	if x != nil {
		return x.FieldChecksDisable
	}
	return false
}

//	RespWritePort1DisableOverrides
//
// The data comes from the Main processor response to the command 0xA9 Write Port 1 Disable Override
type RespWritePort1DisableOverrides struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        settings.EWriteResult  `protobuf:"varint,1,opt,name=result,proto3,enum=settings.EWriteResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespWritePort1DisableOverrides) Reset() {
	*x = RespWritePort1DisableOverrides{}
	mi := &file_cmd_resp_config_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespWritePort1DisableOverrides) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespWritePort1DisableOverrides) ProtoMessage() {}

func (x *RespWritePort1DisableOverrides) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespWritePort1DisableOverrides.ProtoReflect.Descriptor instead.
func (*RespWritePort1DisableOverrides) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{19}
}

func (x *RespWritePort1DisableOverrides) GetResult() settings.EWriteResult {
	if x != nil {
		return x.Result
	}
	return settings.EWriteResult(0)
}

//	CmdReadUnitNetworkConfiguration returns monitor ID and network configuration stored
//
// in the monitor unit (as opposed to the data key)
type CmdReadUnitNetworkConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadUnitNetworkConfiguration) Reset() {
	*x = CmdReadUnitNetworkConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadUnitNetworkConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadUnitNetworkConfiguration) ProtoMessage() {}

func (x *CmdReadUnitNetworkConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadUnitNetworkConfiguration.ProtoReflect.Descriptor instead.
func (*CmdReadUnitNetworkConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{20}
}

func (x *CmdReadUnitNetworkConfiguration) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadUnitNetworkConfiguration - this is the monitor ID and network configuration stored in the unit (not active values)
//
// The data comes from the Main processor response to the command 0x26 Read Configuration Unit Settings
type RespReadUnitNetworkConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Monitor and user ID strings
	Ids *basic.MonitorAndUserIds `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids,omitempty"`
	// Minimum flash time (values 6 - 15)
	MinFlashTime uint32 `protobuf:"varint,2,opt,name=min_flash_time,json=minFlashTime,proto3" json:"min_flash_time,omitempty"`
	// True when Ethernet settings from the data key (or program card) are in use
	UsingDatakeyEthernet bool `protobuf:"varint,3,opt,name=using_datakey_ethernet,json=usingDatakeyEthernet,proto3" json:"using_datakey_ethernet,omitempty"`
	// The unit's network configuration
	NetworkConfiguration *settings.NetworkSettings `protobuf:"bytes,4,opt,name=network_configuration,json=networkConfiguration,proto3" json:"network_configuration,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RespReadUnitNetworkConfiguration) Reset() {
	*x = RespReadUnitNetworkConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadUnitNetworkConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadUnitNetworkConfiguration) ProtoMessage() {}

func (x *RespReadUnitNetworkConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadUnitNetworkConfiguration.ProtoReflect.Descriptor instead.
func (*RespReadUnitNetworkConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{21}
}

func (x *RespReadUnitNetworkConfiguration) GetIds() *basic.MonitorAndUserIds {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *RespReadUnitNetworkConfiguration) GetMinFlashTime() uint32 {
	if x != nil {
		return x.MinFlashTime
	}
	return 0
}

func (x *RespReadUnitNetworkConfiguration) GetUsingDatakeyEthernet() bool {
	if x != nil {
		return x.UsingDatakeyEthernet
	}
	return false
}

func (x *RespReadUnitNetworkConfiguration) GetNetworkConfiguration() *settings.NetworkSettings {
	if x != nil {
		return x.NetworkConfiguration
	}
	return nil
}

// CmdReadActiveNetworkConfiguration returns the system's presently active networking configuration
type CmdReadActiveNetworkConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadActiveNetworkConfiguration) Reset() {
	*x = CmdReadActiveNetworkConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadActiveNetworkConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadActiveNetworkConfiguration) ProtoMessage() {}

func (x *CmdReadActiveNetworkConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadActiveNetworkConfiguration.ProtoReflect.Descriptor instead.
func (*CmdReadActiveNetworkConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{22}
}

func (x *CmdReadActiveNetworkConfiguration) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadActiveNetworkConfiguration - this is the presently active Ethernet config, including values
//
// assigned by a DCHP server if the DHCP client is active.
// The data comes from the Comms processor
type RespReadActiveNetworkConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// True when Ethernet settings from the data key (or program card) are in use
	UsingDatakeyEthernet bool `protobuf:"varint,1,opt,name=using_datakey_ethernet,json=usingDatakeyEthernet,proto3" json:"using_datakey_ethernet,omitempty"`
	// The active Ethernet interface values
	NetworkConfiguration *settings.NetworkSettings `protobuf:"bytes,2,opt,name=network_configuration,json=networkConfiguration,proto3" json:"network_configuration,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RespReadActiveNetworkConfiguration) Reset() {
	*x = RespReadActiveNetworkConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadActiveNetworkConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadActiveNetworkConfiguration) ProtoMessage() {}

func (x *RespReadActiveNetworkConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadActiveNetworkConfiguration.ProtoReflect.Descriptor instead.
func (*RespReadActiveNetworkConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{23}
}

func (x *RespReadActiveNetworkConfiguration) GetUsingDatakeyEthernet() bool {
	if x != nil {
		return x.UsingDatakeyEthernet
	}
	return false
}

func (x *RespReadActiveNetworkConfiguration) GetNetworkConfiguration() *settings.NetworkSettings {
	if x != nil {
		return x.NetworkConfiguration
	}
	return nil
}

//	CmdReadPerChannelConfiguration returns the configuration settings for one or more channels.
//
// Implementation note: The comms MCU will issue command 0x28 for each channel requested.
type CmdReadPerChannelConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The channels (1-32) to request configuration
	Channel       []uint32 `protobuf:"varint,1,rep,packed,name=channel,proto3" json:"channel,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadPerChannelConfiguration) Reset() {
	*x = CmdReadPerChannelConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadPerChannelConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadPerChannelConfiguration) ProtoMessage() {}

func (x *CmdReadPerChannelConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadPerChannelConfiguration.ProtoReflect.Descriptor instead.
func (*CmdReadPerChannelConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{24}
}

func (x *CmdReadPerChannelConfiguration) GetChannel() []uint32 {
	if x != nil {
		return x.Channel
	}
	return nil
}

//	RespReadPerChannelConfiguration - returns the configuration settings for the requested channel(s)
//
// The data comes from the Main processor command 0x28 Read Configuration Per Channel Settings
type RespReadPerChannelConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// One field per requested channel
	ChSettings    []*settings.PerChannelSettings `protobuf:"bytes,1,rep,name=ch_settings,json=chSettings,proto3" json:"ch_settings,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadPerChannelConfiguration) Reset() {
	*x = RespReadPerChannelConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadPerChannelConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadPerChannelConfiguration) ProtoMessage() {}

func (x *RespReadPerChannelConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadPerChannelConfiguration.ProtoReflect.Descriptor instead.
func (*RespReadPerChannelConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{25}
}

func (x *RespReadPerChannelConfiguration) GetChSettings() []*settings.PerChannelSettings {
	if x != nil {
		return x.ChSettings
	}
	return nil
}

//	CmdReadPerChannelCurrentSenseSettings returns the current sense settings for one or more channels
//
// Implmentation note: The Comms MCU will issue command 0x29 but only return the requested channels.
type CmdReadPerChannelCurrentSenseSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The channels (1-32) to request configuration
	Channel       []uint32 `protobuf:"varint,1,rep,packed,name=channel,proto3" json:"channel,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadPerChannelCurrentSenseSettings) Reset() {
	*x = CmdReadPerChannelCurrentSenseSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadPerChannelCurrentSenseSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadPerChannelCurrentSenseSettings) ProtoMessage() {}

func (x *CmdReadPerChannelCurrentSenseSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadPerChannelCurrentSenseSettings.ProtoReflect.Descriptor instead.
func (*CmdReadPerChannelCurrentSenseSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{26}
}

func (x *CmdReadPerChannelCurrentSenseSettings) GetChannel() []uint32 {
	if x != nil {
		return x.Channel
	}
	return nil
}

//	RespReadPerChannelCurrentSenseSettings - returns the current sense settings for the requested channel(s)
//
// The data comes from the Main processor command 0x29 Read Configuration Current Sense Settings
type RespReadPerChannelCurrentSenseSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// One field per requested channel
	// For 16X MMU, channels 2, 4, 6, & 8 may have Walk current thresholds
	ChSettings    []*settings.PerChannelCurrentSenseSettings `protobuf:"bytes,1,rep,name=ch_settings,json=chSettings,proto3" json:"ch_settings,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadPerChannelCurrentSenseSettings) Reset() {
	*x = RespReadPerChannelCurrentSenseSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadPerChannelCurrentSenseSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadPerChannelCurrentSenseSettings) ProtoMessage() {}

func (x *RespReadPerChannelCurrentSenseSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadPerChannelCurrentSenseSettings.ProtoReflect.Descriptor instead.
func (*RespReadPerChannelCurrentSenseSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{27}
}

func (x *RespReadPerChannelCurrentSenseSettings) GetChSettings() []*settings.PerChannelCurrentSenseSettings {
	if x != nil {
		return x.ChSettings
	}
	return nil
}

//	CmdReadPerChannelPermissiveSettings returns the permissive turn channel(s) for a given channel
//
// Implementation note: The Comms MCU will issue command 0x29 but only return the requested channels.
type CmdReadPerChannelPermissiveSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The channels (1-32) to request permissives
	Channel       []uint32 `protobuf:"varint,1,rep,packed,name=channel,proto3" json:"channel,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadPerChannelPermissiveSettings) Reset() {
	*x = CmdReadPerChannelPermissiveSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadPerChannelPermissiveSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadPerChannelPermissiveSettings) ProtoMessage() {}

func (x *CmdReadPerChannelPermissiveSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadPerChannelPermissiveSettings.ProtoReflect.Descriptor instead.
func (*CmdReadPerChannelPermissiveSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{28}
}

func (x *CmdReadPerChannelPermissiveSettings) GetChannel() []uint32 {
	if x != nil {
		return x.Channel
	}
	return nil
}

//	RespReadPerChannelPermissiveSettings - returns the permissive settings for the requested channel(s)
//
// The data comes from the Main processor command 0x2A Read Configuration Permissive Settings
type RespReadPerChannelPermissiveSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// One field per requested channel
	ChSettings    []*settings.PerChannelPermissives `protobuf:"bytes,1,rep,name=ch_settings,json=chSettings,proto3" json:"ch_settings,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadPerChannelPermissiveSettings) Reset() {
	*x = RespReadPerChannelPermissiveSettings{}
	mi := &file_cmd_resp_config_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadPerChannelPermissiveSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadPerChannelPermissiveSettings) ProtoMessage() {}

func (x *RespReadPerChannelPermissiveSettings) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadPerChannelPermissiveSettings.ProtoReflect.Descriptor instead.
func (*RespReadPerChannelPermissiveSettings) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{29}
}

func (x *RespReadPerChannelPermissiveSettings) GetChSettings() []*settings.PerChannelPermissives {
	if x != nil {
		return x.ChSettings
	}
	return nil
}

// CmdReadFlashingYellowArrowConfiguration returns all flashing yellow arrow configuration instances.
type CmdReadFlashingYellowArrowConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This is a placeholder field for future params
	Always0       uint32 `protobuf:"varint,1,opt,name=always0,proto3" json:"always0,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdReadFlashingYellowArrowConfiguration) Reset() {
	*x = CmdReadFlashingYellowArrowConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdReadFlashingYellowArrowConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdReadFlashingYellowArrowConfiguration) ProtoMessage() {}

func (x *CmdReadFlashingYellowArrowConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdReadFlashingYellowArrowConfiguration.ProtoReflect.Descriptor instead.
func (*CmdReadFlashingYellowArrowConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{30}
}

func (x *CmdReadFlashingYellowArrowConfiguration) GetAlways0() uint32 {
	if x != nil {
		return x.Always0
	}
	return 0
}

//	RespReadFlashingYellowArrowConfiguration - returns all instances (typically 4)
//
// The data comes from the Main processor response to the command 0x2B Read Configuration FYA Settings
type RespReadFlashingYellowArrowConfiguration struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The FYA configuration for each instance
	FyaSettings   []*settings.FlashingYellowArrowSettings `protobuf:"bytes,1,rep,name=fya_settings,json=fyaSettings,proto3" json:"fya_settings,omitempty"` // Max repeat count set in cmd_resp_config.options
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespReadFlashingYellowArrowConfiguration) Reset() {
	*x = RespReadFlashingYellowArrowConfiguration{}
	mi := &file_cmd_resp_config_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespReadFlashingYellowArrowConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespReadFlashingYellowArrowConfiguration) ProtoMessage() {}

func (x *RespReadFlashingYellowArrowConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespReadFlashingYellowArrowConfiguration.ProtoReflect.Descriptor instead.
func (*RespReadFlashingYellowArrowConfiguration) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{31}
}

func (x *RespReadFlashingYellowArrowConfiguration) GetFyaSettings() []*settings.FlashingYellowArrowSettings {
	if x != nil {
		return x.FyaSettings
	}
	return nil
}

//	CmdRemoteReset is used to do a Remote Fault Clear / Configuration Acceptance (Remote Reset 0xFE).
//
// To be accepted, the AgencyOptionsMmu field permit_remote_config_acceptance or permit_remote_fault_clear
// must have been set to true.
type CmdRemoteReset struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The type of remote reset to perform.
	ResetType     CpRemoteResetType `protobuf:"varint,1,opt,name=reset_type,json=resetType,proto3,enum=cmd_resp_config.CpRemoteResetType" json:"reset_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CmdRemoteReset) Reset() {
	*x = CmdRemoteReset{}
	mi := &file_cmd_resp_config_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CmdRemoteReset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmdRemoteReset) ProtoMessage() {}

func (x *CmdRemoteReset) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmdRemoteReset.ProtoReflect.Descriptor instead.
func (*CmdRemoteReset) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{32}
}

func (x *CmdRemoteReset) GetResetType() CpRemoteResetType {
	if x != nil {
		return x.ResetType
	}
	return CpRemoteResetType_REMOTE_RESET_TYPE_UNSPECIFIED
}

//	RespRemoteReset - returns the result of the Remote Fault Clear / Configuration Acceptance (Remote Reset).
//
// Note that if the command had no effect (eg, no Fault to clear or Configuration Pending to accept),
// the command will still be accepted and the OK response will be returned.
type RespRemoteReset struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        settings.EWriteResult  `protobuf:"varint,1,opt,name=result,proto3,enum=settings.EWriteResult" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RespRemoteReset) Reset() {
	*x = RespRemoteReset{}
	mi := &file_cmd_resp_config_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RespRemoteReset) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RespRemoteReset) ProtoMessage() {}

func (x *RespRemoteReset) ProtoReflect() protoreflect.Message {
	mi := &file_cmd_resp_config_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RespRemoteReset.ProtoReflect.Descriptor instead.
func (*RespRemoteReset) Descriptor() ([]byte, []int) {
	return file_cmd_resp_config_proto_rawDescGZIP(), []int{33}
}

func (x *RespRemoteReset) GetResult() settings.EWriteResult {
	if x != nil {
		return x.Result
	}
	return settings.EWriteResult(0)
}

var File_cmd_resp_config_proto protoreflect.FileDescriptor

const file_cmd_resp_config_proto_rawDesc = "" +
	"\n" +
	"\x15cmd_resp_config.proto\x12\x0fcmd_resp_config\x1a\vbasic.proto\x1a\x0esettings.proto\x1a\tdfu.proto\x1a\x0emon_logs.proto\"G\n" +
	"\x0eCmdReadDataKey\x125\n" +
	"\n" +
	"pc_dk_type\x18\x01 \x01(\x0e2\x17.settings.EPcDkReadTypeR\bpcDkType\"\xa3\x01\n" +
	"\x0fRespReadDataKey\x125\n" +
	"\x06source\x18\x01 \x01(\x0e2\x1d.settings.EConfigDataLocationR\x06source\x12\"\n" +
	"\rdata_key_data\x18\x02 \x01(\fR\vdataKeyData\x125\n" +
	"\n" +
	"pc_dk_type\x18\x03 \x01(\x0e2\x17.settings.EPcDkReadTypeR\bpcDkType\"|\n" +
	"\x0fCmdWriteDataKey\x12?\n" +
	"\vdestination\x18\x01 \x01(\x0e2\x1d.settings.EConfigDataLocationR\vdestination\x12\"\n" +
	"\rdata_key_data\x18\x02 \x01(\fR\vdataKeyDataJ\x04\b\x03\x10\x04\"B\n" +
	"\x10RespWriteDataKey\x12.\n" +
	"\x06result\x18\x01 \x01(\x0e2\x16.settings.EWriteResultR\x06result\"2\n" +
	"\x16CmdReadFactorySettings\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xc0\x01\n" +
	"\x17RespReadFactorySettings\x123\n" +
	"\afactory\x18\x01 \x01(\v2\x19.settings.FactorySettingsR\afactory\x12:\n" +
	"\bhardware\x18\x02 \x01(\v2\x1e.mon_logs.HardwareRevisionsMmuR\bhardware\x124\n" +
	"\x16max_channels_supported\x18\x03 \x01(\rR\x14maxChannelsSupported\"T\n" +
	"\x17CmdWriteFactorySettings\x123\n" +
	"\afactory\x18\x01 \x01(\v2\x19.settings.FactorySettingsR\afactoryJ\x04\b\x02\x10\x03\"J\n" +
	"\x18RespWriteFactorySettings\x12.\n" +
	"\x06result\x18\x01 \x01(\x0e2\x16.settings.EWriteResultR\x06result\"K\n" +
	"\x15CmdWriteAgencyOptions\x122\n" +
	"\x06agency\x18\x01 \x01(\v2\x1a.settings.AgencyOptionsMmuR\x06agency\"H\n" +
	"\x16RespWriteAgencyOptions\x12.\n" +
	"\x06result\x18\x01 \x01(\x0e2\x16.settings.EWriteResultR\x06result\".\n" +
	"\x12CmdReadMonitorData\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xdd\x02\n" +
	"\x13RespReadMonitorData\x123\n" +
	"\afactory\x18\x01 \x01(\v2\x19.settings.FactorySettingsR\afactory\x12\x1d\n" +
	"\n" +
	"monitor_id\x18\a \x01(\tR\tmonitorId\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12:\n" +
	"\bhardware\x18\x03 \x01(\v2\x1e.mon_logs.HardwareRevisionsMmuR\bhardware\x126\n" +
	"\x17supported_channel_count\x18\x04 \x01(\rR\x15supportedChannelCount\x12(\n" +
	"\x10run_time_seconds\x18\x05 \x01(\rR\x0erunTimeSeconds\x12;\n" +
	"\vfw_versions\x18\x06 \x03(\v2\x1a.dfu.FirmwareVersionSimpleR\n" +
	"fwVersions\"/\n" +
	"\x13CmdReadUserSettings\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"F\n" +
	"\x14RespReadUserSettings\x12.\n" +
	"\x06values\x18\x01 \x01(\v2\x16.settings.UserSettingsR\x06values\"F\n" +
	"\x14CmdWriteUserSettings\x12.\n" +
	"\x06values\x18\x01 \x01(\v2\x16.settings.UserSettingsR\x06values\"G\n" +
	"\x15RespWriteUserSettings\x12.\n" +
	"\x06result\x18\x01 \x01(\x0e2\x16.settings.EWriteResultR\x06result\"8\n" +
	"\x1cCmdReadPort1DisableOverrides\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xaa\x01\n" +
	"\x1dRespReadPort1DisableOverrides\x12U\n" +
	"\x16port1_disable_override\x18\x01 \x01(\x0e2\x1f.settings.EDisableOverrideStateR\x14port1DisableOverride\x122\n" +
	"\x15field_checks_disabled\x18\x02 \x01(\bR\x13fieldChecksDisabled\"\xa8\x01\n" +
	"\x1dCmdWritePort1DisableOverrides\x12U\n" +
	"\x16port1_disable_override\x18\x01 \x01(\x0e2\x1f.settings.EDisableOverrideStateR\x14port1DisableOverride\x120\n" +
	"\x14field_checks_disable\x18\x02 \x01(\bR\x12fieldChecksDisable\"P\n" +
	"\x1eRespWritePort1DisableOverrides\x12.\n" +
	"\x06result\x18\x01 \x01(\x0e2\x16.settings.EWriteResultR\x06result\";\n" +
	"\x1fCmdReadUnitNetworkConfiguration\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xfa\x01\n" +
	" RespReadUnitNetworkConfiguration\x12*\n" +
	"\x03ids\x18\x01 \x01(\v2\x18.basic.MonitorAndUserIdsR\x03ids\x12$\n" +
	"\x0emin_flash_time\x18\x02 \x01(\rR\fminFlashTime\x124\n" +
	"\x16using_datakey_ethernet\x18\x03 \x01(\bR\x14usingDatakeyEthernet\x12N\n" +
	"\x15network_configuration\x18\x04 \x01(\v2\x19.settings.NetworkSettingsR\x14networkConfiguration\"=\n" +
	"!CmdReadActiveNetworkConfiguration\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"\xaa\x01\n" +
	"\"RespReadActiveNetworkConfiguration\x124\n" +
	"\x16using_datakey_ethernet\x18\x01 \x01(\bR\x14usingDatakeyEthernet\x12N\n" +
	"\x15network_configuration\x18\x02 \x01(\v2\x19.settings.NetworkSettingsR\x14networkConfiguration\":\n" +
	"\x1eCmdReadPerChannelConfiguration\x12\x18\n" +
	"\achannel\x18\x01 \x03(\rR\achannel\"`\n" +
	"\x1fRespReadPerChannelConfiguration\x12=\n" +
	"\vch_settings\x18\x01 \x03(\v2\x1c.settings.PerChannelSettingsR\n" +
	"chSettings\"A\n" +
	"%CmdReadPerChannelCurrentSenseSettings\x12\x18\n" +
	"\achannel\x18\x01 \x03(\rR\achannel\"s\n" +
	"&RespReadPerChannelCurrentSenseSettings\x12I\n" +
	"\vch_settings\x18\x01 \x03(\v2(.settings.PerChannelCurrentSenseSettingsR\n" +
	"chSettings\"?\n" +
	"#CmdReadPerChannelPermissiveSettings\x12\x18\n" +
	"\achannel\x18\x01 \x03(\rR\achannel\"h\n" +
	"$RespReadPerChannelPermissiveSettings\x12@\n" +
	"\vch_settings\x18\x01 \x03(\v2\x1f.settings.PerChannelPermissivesR\n" +
	"chSettings\"C\n" +
	"'CmdReadFlashingYellowArrowConfiguration\x12\x18\n" +
	"\aalways0\x18\x01 \x01(\rR\aalways0\"t\n" +
	"(RespReadFlashingYellowArrowConfiguration\x12H\n" +
	"\ffya_settings\x18\x01 \x03(\v2%.settings.FlashingYellowArrowSettingsR\vfyaSettings\"S\n" +
	"\x0eCmdRemoteReset\x12A\n" +
	"\n" +
	"reset_type\x18\x01 \x01(\x0e2\".cmd_resp_config.CpRemoteResetTypeR\tresetType\"A\n" +
	"\x0fRespRemoteReset\x12.\n" +
	"\x06result\x18\x01 \x01(\x0e2\x16.settings.EWriteResultR\x06result*\xa6\x01\n" +
	"\x11CpRemoteResetType\x12!\n" +
	"\x1dREMOTE_RESET_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16REMOTE_RESET_TYPE_NONE\x10\x01\x12/\n" +
	"+REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE\x10\x02\x12!\n" +
	"\x1dREMOTE_RESET_TYPE_FAULT_CLEAR\x10\x03b\x06proto3"

var (
	file_cmd_resp_config_proto_rawDescOnce sync.Once
	file_cmd_resp_config_proto_rawDescData []byte
)

func file_cmd_resp_config_proto_rawDescGZIP() []byte {
	file_cmd_resp_config_proto_rawDescOnce.Do(func() {
		file_cmd_resp_config_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_cmd_resp_config_proto_rawDesc), len(file_cmd_resp_config_proto_rawDesc)))
	})
	return file_cmd_resp_config_proto_rawDescData
}

var file_cmd_resp_config_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_cmd_resp_config_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_cmd_resp_config_proto_goTypes = []any{
	(CpRemoteResetType)(0),                           // 0: cmd_resp_config.CpRemoteResetType
	(*CmdReadDataKey)(nil),                           // 1: cmd_resp_config.CmdReadDataKey
	(*RespReadDataKey)(nil),                          // 2: cmd_resp_config.RespReadDataKey
	(*CmdWriteDataKey)(nil),                          // 3: cmd_resp_config.CmdWriteDataKey
	(*RespWriteDataKey)(nil),                         // 4: cmd_resp_config.RespWriteDataKey
	(*CmdReadFactorySettings)(nil),                   // 5: cmd_resp_config.CmdReadFactorySettings
	(*RespReadFactorySettings)(nil),                  // 6: cmd_resp_config.RespReadFactorySettings
	(*CmdWriteFactorySettings)(nil),                  // 7: cmd_resp_config.CmdWriteFactorySettings
	(*RespWriteFactorySettings)(nil),                 // 8: cmd_resp_config.RespWriteFactorySettings
	(*CmdWriteAgencyOptions)(nil),                    // 9: cmd_resp_config.CmdWriteAgencyOptions
	(*RespWriteAgencyOptions)(nil),                   // 10: cmd_resp_config.RespWriteAgencyOptions
	(*CmdReadMonitorData)(nil),                       // 11: cmd_resp_config.CmdReadMonitorData
	(*RespReadMonitorData)(nil),                      // 12: cmd_resp_config.RespReadMonitorData
	(*CmdReadUserSettings)(nil),                      // 13: cmd_resp_config.CmdReadUserSettings
	(*RespReadUserSettings)(nil),                     // 14: cmd_resp_config.RespReadUserSettings
	(*CmdWriteUserSettings)(nil),                     // 15: cmd_resp_config.CmdWriteUserSettings
	(*RespWriteUserSettings)(nil),                    // 16: cmd_resp_config.RespWriteUserSettings
	(*CmdReadPort1DisableOverrides)(nil),             // 17: cmd_resp_config.CmdReadPort1DisableOverrides
	(*RespReadPort1DisableOverrides)(nil),            // 18: cmd_resp_config.RespReadPort1DisableOverrides
	(*CmdWritePort1DisableOverrides)(nil),            // 19: cmd_resp_config.CmdWritePort1DisableOverrides
	(*RespWritePort1DisableOverrides)(nil),           // 20: cmd_resp_config.RespWritePort1DisableOverrides
	(*CmdReadUnitNetworkConfiguration)(nil),          // 21: cmd_resp_config.CmdReadUnitNetworkConfiguration
	(*RespReadUnitNetworkConfiguration)(nil),         // 22: cmd_resp_config.RespReadUnitNetworkConfiguration
	(*CmdReadActiveNetworkConfiguration)(nil),        // 23: cmd_resp_config.CmdReadActiveNetworkConfiguration
	(*RespReadActiveNetworkConfiguration)(nil),       // 24: cmd_resp_config.RespReadActiveNetworkConfiguration
	(*CmdReadPerChannelConfiguration)(nil),           // 25: cmd_resp_config.CmdReadPerChannelConfiguration
	(*RespReadPerChannelConfiguration)(nil),          // 26: cmd_resp_config.RespReadPerChannelConfiguration
	(*CmdReadPerChannelCurrentSenseSettings)(nil),    // 27: cmd_resp_config.CmdReadPerChannelCurrentSenseSettings
	(*RespReadPerChannelCurrentSenseSettings)(nil),   // 28: cmd_resp_config.RespReadPerChannelCurrentSenseSettings
	(*CmdReadPerChannelPermissiveSettings)(nil),      // 29: cmd_resp_config.CmdReadPerChannelPermissiveSettings
	(*RespReadPerChannelPermissiveSettings)(nil),     // 30: cmd_resp_config.RespReadPerChannelPermissiveSettings
	(*CmdReadFlashingYellowArrowConfiguration)(nil),  // 31: cmd_resp_config.CmdReadFlashingYellowArrowConfiguration
	(*RespReadFlashingYellowArrowConfiguration)(nil), // 32: cmd_resp_config.RespReadFlashingYellowArrowConfiguration
	(*CmdRemoteReset)(nil),                           // 33: cmd_resp_config.CmdRemoteReset
	(*RespRemoteReset)(nil),                          // 34: cmd_resp_config.RespRemoteReset
	(settings.EPcDkReadType)(0),                      // 35: settings.EPcDkReadType
	(settings.EConfigDataLocation)(0),                // 36: settings.EConfigDataLocation
	(settings.EWriteResult)(0),                       // 37: settings.EWriteResult
	(*settings.FactorySettings)(nil),                 // 38: settings.FactorySettings
	(*mon_logs.HardwareRevisionsMmu)(nil),            // 39: mon_logs.HardwareRevisionsMmu
	(*settings.AgencyOptionsMmu)(nil),                // 40: settings.AgencyOptionsMmu
	(*dfu.FirmwareVersionSimple)(nil),                // 41: dfu.FirmwareVersionSimple
	(*settings.UserSettings)(nil),                    // 42: settings.UserSettings
	(settings.EDisableOverrideState)(0),              // 43: settings.EDisableOverrideState
	(*basic.MonitorAndUserIds)(nil),                  // 44: basic.MonitorAndUserIds
	(*settings.NetworkSettings)(nil),                 // 45: settings.NetworkSettings
	(*settings.PerChannelSettings)(nil),              // 46: settings.PerChannelSettings
	(*settings.PerChannelCurrentSenseSettings)(nil),  // 47: settings.PerChannelCurrentSenseSettings
	(*settings.PerChannelPermissives)(nil),           // 48: settings.PerChannelPermissives
	(*settings.FlashingYellowArrowSettings)(nil),     // 49: settings.FlashingYellowArrowSettings
}
var file_cmd_resp_config_proto_depIdxs = []int32{
	35, // 0: cmd_resp_config.CmdReadDataKey.pc_dk_type:type_name -> settings.EPcDkReadType
	36, // 1: cmd_resp_config.RespReadDataKey.source:type_name -> settings.EConfigDataLocation
	35, // 2: cmd_resp_config.RespReadDataKey.pc_dk_type:type_name -> settings.EPcDkReadType
	36, // 3: cmd_resp_config.CmdWriteDataKey.destination:type_name -> settings.EConfigDataLocation
	37, // 4: cmd_resp_config.RespWriteDataKey.result:type_name -> settings.EWriteResult
	38, // 5: cmd_resp_config.RespReadFactorySettings.factory:type_name -> settings.FactorySettings
	39, // 6: cmd_resp_config.RespReadFactorySettings.hardware:type_name -> mon_logs.HardwareRevisionsMmu
	38, // 7: cmd_resp_config.CmdWriteFactorySettings.factory:type_name -> settings.FactorySettings
	37, // 8: cmd_resp_config.RespWriteFactorySettings.result:type_name -> settings.EWriteResult
	40, // 9: cmd_resp_config.CmdWriteAgencyOptions.agency:type_name -> settings.AgencyOptionsMmu
	37, // 10: cmd_resp_config.RespWriteAgencyOptions.result:type_name -> settings.EWriteResult
	38, // 11: cmd_resp_config.RespReadMonitorData.factory:type_name -> settings.FactorySettings
	39, // 12: cmd_resp_config.RespReadMonitorData.hardware:type_name -> mon_logs.HardwareRevisionsMmu
	41, // 13: cmd_resp_config.RespReadMonitorData.fw_versions:type_name -> dfu.FirmwareVersionSimple
	42, // 14: cmd_resp_config.RespReadUserSettings.values:type_name -> settings.UserSettings
	42, // 15: cmd_resp_config.CmdWriteUserSettings.values:type_name -> settings.UserSettings
	37, // 16: cmd_resp_config.RespWriteUserSettings.result:type_name -> settings.EWriteResult
	43, // 17: cmd_resp_config.RespReadPort1DisableOverrides.port1_disable_override:type_name -> settings.EDisableOverrideState
	43, // 18: cmd_resp_config.CmdWritePort1DisableOverrides.port1_disable_override:type_name -> settings.EDisableOverrideState
	37, // 19: cmd_resp_config.RespWritePort1DisableOverrides.result:type_name -> settings.EWriteResult
	44, // 20: cmd_resp_config.RespReadUnitNetworkConfiguration.ids:type_name -> basic.MonitorAndUserIds
	45, // 21: cmd_resp_config.RespReadUnitNetworkConfiguration.network_configuration:type_name -> settings.NetworkSettings
	45, // 22: cmd_resp_config.RespReadActiveNetworkConfiguration.network_configuration:type_name -> settings.NetworkSettings
	46, // 23: cmd_resp_config.RespReadPerChannelConfiguration.ch_settings:type_name -> settings.PerChannelSettings
	47, // 24: cmd_resp_config.RespReadPerChannelCurrentSenseSettings.ch_settings:type_name -> settings.PerChannelCurrentSenseSettings
	48, // 25: cmd_resp_config.RespReadPerChannelPermissiveSettings.ch_settings:type_name -> settings.PerChannelPermissives
	49, // 26: cmd_resp_config.RespReadFlashingYellowArrowConfiguration.fya_settings:type_name -> settings.FlashingYellowArrowSettings
	0,  // 27: cmd_resp_config.CmdRemoteReset.reset_type:type_name -> cmd_resp_config.CpRemoteResetType
	37, // 28: cmd_resp_config.RespRemoteReset.result:type_name -> settings.EWriteResult
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_cmd_resp_config_proto_init() }
func file_cmd_resp_config_proto_init() {
	if File_cmd_resp_config_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_cmd_resp_config_proto_rawDesc), len(file_cmd_resp_config_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_cmd_resp_config_proto_goTypes,
		DependencyIndexes: file_cmd_resp_config_proto_depIdxs,
		EnumInfos:         file_cmd_resp_config_proto_enumTypes,
		MessageInfos:      file_cmd_resp_config_proto_msgTypes,
	}.Build()
	File_cmd_resp_config_proto = out.File
	file_cmd_resp_config_proto_goTypes = nil
	file_cmd_resp_config_proto_depIdxs = nil
}
