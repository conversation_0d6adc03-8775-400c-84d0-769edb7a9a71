// source: cmd_resp_dfu.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var dfu_pb = require('./dfu_pb.js');
goog.object.extend(proto, dfu_pb);
goog.exportSymbol('proto.cmd_resp_dfu.CmdBeginFirmwareDownload', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.CmdFirmwareDownloadChunk', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.CmdFirmwareUpdateManifest', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.CmdManifestVersions', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.CmdRebootCommsMcu', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.RespBeginFirmwareDownload', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.RespFirmwareDownloadChunk', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.RespFirmwareUpdateManifest', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.RespInitiateFirmwareUpdate', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.RespManifestVersions', null, global);
goog.exportSymbol('proto.cmd_resp_dfu.RespRebootCommsMcu', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.CmdManifestVersions = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.CmdManifestVersions, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.CmdManifestVersions.displayName = 'proto.cmd_resp_dfu.CmdManifestVersions';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.RespManifestVersions = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_dfu.RespManifestVersions.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_dfu.RespManifestVersions, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.RespManifestVersions.displayName = 'proto.cmd_resp_dfu.RespManifestVersions';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.CmdRebootCommsMcu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.CmdRebootCommsMcu.displayName = 'proto.cmd_resp_dfu.CmdRebootCommsMcu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.RespRebootCommsMcu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.RespRebootCommsMcu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.RespRebootCommsMcu.displayName = 'proto.cmd_resp_dfu.RespRebootCommsMcu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.displayName = 'proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.RespInitiateFirmwareUpdate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.displayName = 'proto.cmd_resp_dfu.RespInitiateFirmwareUpdate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_dfu.CmdFirmwareUpdateManifest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.displayName = 'proto.cmd_resp_dfu.CmdFirmwareUpdateManifest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.RespFirmwareUpdateManifest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.RespFirmwareUpdateManifest.displayName = 'proto.cmd_resp_dfu.RespFirmwareUpdateManifest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.CmdBeginFirmwareDownload, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.CmdBeginFirmwareDownload.displayName = 'proto.cmd_resp_dfu.CmdBeginFirmwareDownload';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.RespBeginFirmwareDownload, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.RespBeginFirmwareDownload.displayName = 'proto.cmd_resp_dfu.RespBeginFirmwareDownload';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.CmdFirmwareDownloadChunk, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.displayName = 'proto.cmd_resp_dfu.CmdFirmwareDownloadChunk';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_dfu.RespFirmwareDownloadChunk, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_dfu.RespFirmwareDownloadChunk.displayName = 'proto.cmd_resp_dfu.RespFirmwareDownloadChunk';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.CmdManifestVersions.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.CmdManifestVersions.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.CmdManifestVersions} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdManifestVersions.toObject = function(includeInstance, msg) {
  var f, obj = {
manifestType: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.CmdManifestVersions}
 */
proto.cmd_resp_dfu.CmdManifestVersions.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.CmdManifestVersions;
  return proto.cmd_resp_dfu.CmdManifestVersions.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.CmdManifestVersions} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.CmdManifestVersions}
 */
proto.cmd_resp_dfu.CmdManifestVersions.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EFirmwareVersionsManifest} */ (reader.readEnum());
      msg.setManifestType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.CmdManifestVersions.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.CmdManifestVersions.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.CmdManifestVersions} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdManifestVersions.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getManifestType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional dfu.EFirmwareVersionsManifest manifest_type = 1;
 * @return {!proto.dfu.EFirmwareVersionsManifest}
 */
proto.cmd_resp_dfu.CmdManifestVersions.prototype.getManifestType = function() {
  return /** @type {!proto.dfu.EFirmwareVersionsManifest} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EFirmwareVersionsManifest} value
 * @return {!proto.cmd_resp_dfu.CmdManifestVersions} returns this
 */
proto.cmd_resp_dfu.CmdManifestVersions.prototype.setManifestType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_dfu.RespManifestVersions.repeatedFields_ = [2,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.RespManifestVersions.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.RespManifestVersions} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespManifestVersions.toObject = function(includeInstance, msg) {
  var f, obj = {
manifestType: jspb.Message.getFieldWithDefault(msg, 1, 0),
imageStatusesList: jspb.Message.toObjectList(msg.getImageStatusesList(),
    dfu_pb.DfuManifestStatusEntry.toObject, includeInstance),
fwUpdateStatus: jspb.Message.getFieldWithDefault(msg, 3, 0),
thisModelNumber: jspb.Message.getFieldWithDefault(msg, 4, 0),
supportedModelNumbersList: (f = jspb.Message.getRepeatedField(msg, 5)) == null ? undefined : f,
packageVersion: jspb.Message.getFieldWithDefault(msg, 6, ""),
packageBuildDate: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.RespManifestVersions}
 */
proto.cmd_resp_dfu.RespManifestVersions.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.RespManifestVersions;
  return proto.cmd_resp_dfu.RespManifestVersions.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.RespManifestVersions} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.RespManifestVersions}
 */
proto.cmd_resp_dfu.RespManifestVersions.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EFirmwareVersionsManifest} */ (reader.readEnum());
      msg.setManifestType(value);
      break;
    case 2:
      var value = new dfu_pb.DfuManifestStatusEntry;
      reader.readMessage(value,dfu_pb.DfuManifestStatusEntry.deserializeBinaryFromReader);
      msg.addImageStatuses(value);
      break;
    case 3:
      var value = /** @type {!proto.dfu.EFirmwareUpdateStatus} */ (reader.readEnum());
      msg.setFwUpdateStatus(value);
      break;
    case 4:
      var value = /** @type {!proto.dfu.EMonitorModelNumber} */ (reader.readEnum());
      msg.setThisModelNumber(value);
      break;
    case 5:
      var values = /** @type {!Array<!proto.dfu.EMonitorModelNumber>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addSupportedModelNumbers(values[i]);
      }
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setPackageVersion(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setPackageBuildDate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.RespManifestVersions.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.RespManifestVersions} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespManifestVersions.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getManifestType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getImageStatusesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      dfu_pb.DfuManifestStatusEntry.serializeBinaryToWriter
    );
  }
  f = message.getFwUpdateStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getThisModelNumber();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getSupportedModelNumbersList();
  if (f.length > 0) {
    writer.writePackedEnum(
      5,
      f
    );
  }
  f = message.getPackageVersion();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getPackageBuildDate();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional dfu.EFirmwareVersionsManifest manifest_type = 1;
 * @return {!proto.dfu.EFirmwareVersionsManifest}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.getManifestType = function() {
  return /** @type {!proto.dfu.EFirmwareVersionsManifest} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EFirmwareVersionsManifest} value
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.setManifestType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * repeated dfu.DfuManifestStatusEntry image_statuses = 2;
 * @return {!Array<!proto.dfu.DfuManifestStatusEntry>}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.getImageStatusesList = function() {
  return /** @type{!Array<!proto.dfu.DfuManifestStatusEntry>} */ (
    jspb.Message.getRepeatedWrapperField(this, dfu_pb.DfuManifestStatusEntry, 2));
};


/**
 * @param {!Array<!proto.dfu.DfuManifestStatusEntry>} value
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
*/
proto.cmd_resp_dfu.RespManifestVersions.prototype.setImageStatusesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.dfu.DfuManifestStatusEntry=} opt_value
 * @param {number=} opt_index
 * @return {!proto.dfu.DfuManifestStatusEntry}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.addImageStatuses = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.dfu.DfuManifestStatusEntry, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.clearImageStatusesList = function() {
  return this.setImageStatusesList([]);
};


/**
 * optional dfu.EFirmwareUpdateStatus fw_update_status = 3;
 * @return {!proto.dfu.EFirmwareUpdateStatus}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.getFwUpdateStatus = function() {
  return /** @type {!proto.dfu.EFirmwareUpdateStatus} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.dfu.EFirmwareUpdateStatus} value
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.setFwUpdateStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional dfu.EMonitorModelNumber this_model_number = 4;
 * @return {!proto.dfu.EMonitorModelNumber}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.getThisModelNumber = function() {
  return /** @type {!proto.dfu.EMonitorModelNumber} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.dfu.EMonitorModelNumber} value
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.setThisModelNumber = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * repeated dfu.EMonitorModelNumber supported_model_numbers = 5;
 * @return {!Array<!proto.dfu.EMonitorModelNumber>}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.getSupportedModelNumbersList = function() {
  return /** @type {!Array<!proto.dfu.EMonitorModelNumber>} */ (jspb.Message.getRepeatedField(this, 5));
};


/**
 * @param {!Array<!proto.dfu.EMonitorModelNumber>} value
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.setSupportedModelNumbersList = function(value) {
  return jspb.Message.setField(this, 5, value || []);
};


/**
 * @param {!proto.dfu.EMonitorModelNumber} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.addSupportedModelNumbers = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 5, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.clearSupportedModelNumbersList = function() {
  return this.setSupportedModelNumbersList([]);
};


/**
 * optional string package_version = 6;
 * @return {string}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.getPackageVersion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.setPackageVersion = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string package_build_date = 7;
 * @return {string}
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.getPackageBuildDate = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_dfu.RespManifestVersions} returns this
 */
proto.cmd_resp_dfu.RespManifestVersions.prototype.setPackageBuildDate = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.CmdRebootCommsMcu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.CmdRebootCommsMcu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.CmdRebootCommsMcu}
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.CmdRebootCommsMcu;
  return proto.cmd_resp_dfu.CmdRebootCommsMcu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.CmdRebootCommsMcu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.CmdRebootCommsMcu}
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.CmdRebootCommsMcu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.CmdRebootCommsMcu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.CmdRebootCommsMcu} returns this
 */
proto.cmd_resp_dfu.CmdRebootCommsMcu.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.RespRebootCommsMcu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.RespRebootCommsMcu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.toObject = function(includeInstance, msg) {
  var f, obj = {
rebootInMs: jspb.Message.getFieldWithDefault(msg, 1, 0),
resultCode: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.RespRebootCommsMcu}
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.RespRebootCommsMcu;
  return proto.cmd_resp_dfu.RespRebootCommsMcu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.RespRebootCommsMcu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.RespRebootCommsMcu}
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRebootInMs(value);
      break;
    case 2:
      var value = /** @type {!proto.dfu.EDfuResultCode} */ (reader.readEnum());
      msg.setResultCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.RespRebootCommsMcu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.RespRebootCommsMcu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRebootInMs();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getResultCode();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
};


/**
 * optional uint32 reboot_in_ms = 1;
 * @return {number}
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.prototype.getRebootInMs = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.RespRebootCommsMcu} returns this
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.prototype.setRebootInMs = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional dfu.EDfuResultCode result_code = 2;
 * @return {!proto.dfu.EDfuResultCode}
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.prototype.getResultCode = function() {
  return /** @type {!proto.dfu.EDfuResultCode} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.dfu.EDfuResultCode} value
 * @return {!proto.cmd_resp_dfu.RespRebootCommsMcu} returns this
 */
proto.cmd_resp_dfu.RespRebootCommsMcu.prototype.setResultCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate}
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate;
  return proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate}
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate} returns this
 */
proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.RespInitiateFirmwareUpdate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.toObject = function(includeInstance, msg) {
  var f, obj = {
resultCode: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.RespInitiateFirmwareUpdate}
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.RespInitiateFirmwareUpdate;
  return proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.RespInitiateFirmwareUpdate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.RespInitiateFirmwareUpdate}
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EDfuResultCode} */ (reader.readEnum());
      msg.setResultCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.RespInitiateFirmwareUpdate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResultCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional dfu.EDfuResultCode result_code = 1;
 * @return {!proto.dfu.EDfuResultCode}
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.prototype.getResultCode = function() {
  return /** @type {!proto.dfu.EDfuResultCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EDfuResultCode} value
 * @return {!proto.cmd_resp_dfu.RespInitiateFirmwareUpdate} returns this
 */
proto.cmd_resp_dfu.RespInitiateFirmwareUpdate.prototype.setResultCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.repeatedFields_ = [2,3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.toObject = function(includeInstance, msg) {
  var f, obj = {
timeoutSeconds: jspb.Message.getFieldWithDefault(msg, 1, 0),
entriesList: jspb.Message.toObjectList(msg.getEntriesList(),
    dfu_pb.DfuManifestEntry.toObject, includeInstance),
supportedModelNumbersList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f,
packageVersion: jspb.Message.getFieldWithDefault(msg, 4, ""),
packageBuildDate: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.CmdFirmwareUpdateManifest;
  return proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTimeoutSeconds(value);
      break;
    case 2:
      var value = new dfu_pb.DfuManifestEntry;
      reader.readMessage(value,dfu_pb.DfuManifestEntry.deserializeBinaryFromReader);
      msg.addEntries(value);
      break;
    case 3:
      var values = /** @type {!Array<!proto.dfu.EMonitorModelNumber>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addSupportedModelNumbers(values[i]);
      }
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPackageVersion(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setPackageBuildDate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTimeoutSeconds();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEntriesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      dfu_pb.DfuManifestEntry.serializeBinaryToWriter
    );
  }
  f = message.getSupportedModelNumbersList();
  if (f.length > 0) {
    writer.writePackedEnum(
      3,
      f
    );
  }
  f = message.getPackageVersion();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getPackageBuildDate();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional uint32 timeout_seconds = 1;
 * @return {number}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.getTimeoutSeconds = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.setTimeoutSeconds = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * repeated dfu.DfuManifestEntry entries = 2;
 * @return {!Array<!proto.dfu.DfuManifestEntry>}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.getEntriesList = function() {
  return /** @type{!Array<!proto.dfu.DfuManifestEntry>} */ (
    jspb.Message.getRepeatedWrapperField(this, dfu_pb.DfuManifestEntry, 2));
};


/**
 * @param {!Array<!proto.dfu.DfuManifestEntry>} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
*/
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.setEntriesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.dfu.DfuManifestEntry=} opt_value
 * @param {number=} opt_index
 * @return {!proto.dfu.DfuManifestEntry}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.addEntries = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.dfu.DfuManifestEntry, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.clearEntriesList = function() {
  return this.setEntriesList([]);
};


/**
 * repeated dfu.EMonitorModelNumber supported_model_numbers = 3;
 * @return {!Array<!proto.dfu.EMonitorModelNumber>}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.getSupportedModelNumbersList = function() {
  return /** @type {!Array<!proto.dfu.EMonitorModelNumber>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<!proto.dfu.EMonitorModelNumber>} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.setSupportedModelNumbersList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {!proto.dfu.EMonitorModelNumber} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.addSupportedModelNumbers = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.clearSupportedModelNumbersList = function() {
  return this.setSupportedModelNumbersList([]);
};


/**
 * optional string package_version = 4;
 * @return {string}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.getPackageVersion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.setPackageVersion = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string package_build_date = 5;
 * @return {string}
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.getPackageBuildDate = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareUpdateManifest.prototype.setPackageBuildDate = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.RespFirmwareUpdateManifest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.RespFirmwareUpdateManifest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.toObject = function(includeInstance, msg) {
  var f, obj = {
resultCode: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.RespFirmwareUpdateManifest}
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.RespFirmwareUpdateManifest;
  return proto.cmd_resp_dfu.RespFirmwareUpdateManifest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.RespFirmwareUpdateManifest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.RespFirmwareUpdateManifest}
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EDfuResultCode} */ (reader.readEnum());
      msg.setResultCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.RespFirmwareUpdateManifest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.RespFirmwareUpdateManifest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResultCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional dfu.EDfuResultCode result_code = 1;
 * @return {!proto.dfu.EDfuResultCode}
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.prototype.getResultCode = function() {
  return /** @type {!proto.dfu.EDfuResultCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EDfuResultCode} value
 * @return {!proto.cmd_resp_dfu.RespFirmwareUpdateManifest} returns this
 */
proto.cmd_resp_dfu.RespFirmwareUpdateManifest.prototype.setResultCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.CmdBeginFirmwareDownload.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.toObject = function(includeInstance, msg) {
  var f, obj = {
targetMcu: jspb.Message.getFieldWithDefault(msg, 1, 0),
imageType: jspb.Message.getFieldWithDefault(msg, 2, 0),
filename: jspb.Message.getFieldWithDefault(msg, 3, ""),
downloadRequestId: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.CmdBeginFirmwareDownload;
  return proto.cmd_resp_dfu.CmdBeginFirmwareDownload.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EProcessorType} */ (reader.readEnum());
      msg.setTargetMcu(value);
      break;
    case 2:
      var value = /** @type {!proto.dfu.EImageType} */ (reader.readEnum());
      msg.setImageType(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilename(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDownloadRequestId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.CmdBeginFirmwareDownload.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTargetMcu();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getImageType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getFilename();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getDownloadRequestId();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional dfu.EProcessorType target_mcu = 1;
 * @return {!proto.dfu.EProcessorType}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.getTargetMcu = function() {
  return /** @type {!proto.dfu.EProcessorType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EProcessorType} value
 * @return {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload} returns this
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.setTargetMcu = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional dfu.EImageType image_type = 2;
 * @return {!proto.dfu.EImageType}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.getImageType = function() {
  return /** @type {!proto.dfu.EImageType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.dfu.EImageType} value
 * @return {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload} returns this
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.setImageType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional string filename = 3;
 * @return {string}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.getFilename = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload} returns this
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.setFilename = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional uint32 download_request_id = 4;
 * @return {number}
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.getDownloadRequestId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.CmdBeginFirmwareDownload} returns this
 */
proto.cmd_resp_dfu.CmdBeginFirmwareDownload.prototype.setDownloadRequestId = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.RespBeginFirmwareDownload.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.RespBeginFirmwareDownload} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.toObject = function(includeInstance, msg) {
  var f, obj = {
resultCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
maxChunkSizeBytes: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.RespBeginFirmwareDownload}
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.RespBeginFirmwareDownload;
  return proto.cmd_resp_dfu.RespBeginFirmwareDownload.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.RespBeginFirmwareDownload} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.RespBeginFirmwareDownload}
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EDfuResultCode} */ (reader.readEnum());
      msg.setResultCode(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMaxChunkSizeBytes(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.RespBeginFirmwareDownload.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.RespBeginFirmwareDownload} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResultCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMaxChunkSizeBytes();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
};


/**
 * optional dfu.EDfuResultCode result_code = 1;
 * @return {!proto.dfu.EDfuResultCode}
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.prototype.getResultCode = function() {
  return /** @type {!proto.dfu.EDfuResultCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EDfuResultCode} value
 * @return {!proto.cmd_resp_dfu.RespBeginFirmwareDownload} returns this
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.prototype.setResultCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 max_chunk_size_bytes = 3;
 * @return {number}
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.prototype.getMaxChunkSizeBytes = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.RespBeginFirmwareDownload} returns this
 */
proto.cmd_resp_dfu.RespBeginFirmwareDownload.prototype.setMaxChunkSizeBytes = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.toObject = function(includeInstance, msg) {
  var f, obj = {
downloadRequestId: jspb.Message.getFieldWithDefault(msg, 1, 0),
offset: jspb.Message.getFieldWithDefault(msg, 2, 0),
sizeBytes: jspb.Message.getFieldWithDefault(msg, 3, 0),
lastChunk: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
chunkData: msg.getChunkData_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.CmdFirmwareDownloadChunk;
  return proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDownloadRequestId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setOffset(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSizeBytes(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setLastChunk(value);
      break;
    case 5:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setChunkData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDownloadRequestId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getOffset();
  if (f !== 0) {
    writer.writeFixed32(
      2,
      f
    );
  }
  f = message.getSizeBytes();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getLastChunk();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getChunkData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      5,
      f
    );
  }
};


/**
 * optional uint32 download_request_id = 1;
 * @return {number}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.getDownloadRequestId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.setDownloadRequestId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional fixed32 offset = 2;
 * @return {number}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.getOffset = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.setOffset = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 size_bytes = 3;
 * @return {number}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.getSizeBytes = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.setSizeBytes = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bool last_chunk = 4;
 * @return {boolean}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.getLastChunk = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.setLastChunk = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional bytes chunk_data = 5;
 * @return {string}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.getChunkData = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * optional bytes chunk_data = 5;
 * This is a type-conversion wrapper around `getChunkData()`
 * @return {string}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.getChunkData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getChunkData()));
};


/**
 * optional bytes chunk_data = 5;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getChunkData()`
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.getChunkData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getChunkData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.CmdFirmwareDownloadChunk.prototype.setChunkData = function(value) {
  return jspb.Message.setProto3BytesField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_dfu.RespFirmwareDownloadChunk.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.toObject = function(includeInstance, msg) {
  var f, obj = {
offsetExpected: jspb.Message.getFieldWithDefault(msg, 1, 0),
offsetReceived: jspb.Message.getFieldWithDefault(msg, 2, 0),
complete: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
resultCode: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_dfu.RespFirmwareDownloadChunk;
  return proto.cmd_resp_dfu.RespFirmwareDownloadChunk.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setOffsetExpected(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setOffsetReceived(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setComplete(value);
      break;
    case 4:
      var value = /** @type {!proto.dfu.EDfuResultCode} */ (reader.readEnum());
      msg.setResultCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_dfu.RespFirmwareDownloadChunk.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOffsetExpected();
  if (f !== 0) {
    writer.writeFixed32(
      1,
      f
    );
  }
  f = message.getOffsetReceived();
  if (f !== 0) {
    writer.writeFixed32(
      2,
      f
    );
  }
  f = message.getComplete();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getResultCode();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
};


/**
 * optional fixed32 offset_expected = 1;
 * @return {number}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.getOffsetExpected = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.setOffsetExpected = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional fixed32 offset_received = 2;
 * @return {number}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.getOffsetReceived = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.setOffsetReceived = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional bool complete = 3;
 * @return {boolean}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.getComplete = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.setComplete = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional dfu.EDfuResultCode result_code = 4;
 * @return {!proto.dfu.EDfuResultCode}
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.getResultCode = function() {
  return /** @type {!proto.dfu.EDfuResultCode} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.dfu.EDfuResultCode} value
 * @return {!proto.cmd_resp_dfu.RespFirmwareDownloadChunk} returns this
 */
proto.cmd_resp_dfu.RespFirmwareDownloadChunk.prototype.setResultCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


goog.object.extend(exports, proto.cmd_resp_dfu);
