// source: mon_logs.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
var mon_faults_pb = require('./mon_faults_pb.js');
goog.object.extend(proto, mon_faults_pb);
var settings_pb = require('./settings_pb.js');
goog.object.extend(proto, settings_pb);
goog.exportSymbol('proto.mon_logs.AlarmLogEntry', null, global);
goog.exportSymbol('proto.mon_logs.AlarmLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.ClockLogEntry', null, global);
goog.exportSymbol('proto.mon_logs.ClockLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.ConfigLogEntryMmu', null, global);
goog.exportSymbol('proto.mon_logs.ConfigLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.DataKeyErrorCodeBitmap', null, global);
goog.exportSymbol('proto.mon_logs.EAlarmSeverity', null, global);
goog.exportSymbol('proto.mon_logs.EAlarmSource', null, global);
goog.exportSymbol('proto.mon_logs.ECaptureChannelInput', null, global);
goog.exportSymbol('proto.mon_logs.EClockLogSource', null, global);
goog.exportSymbol('proto.mon_logs.EMonitorLogType', null, global);
goog.exportSymbol('proto.mon_logs.EPowerLogEventTiming', null, global);
goog.exportSymbol('proto.mon_logs.EPowerLogEventType', null, global);
goog.exportSymbol('proto.mon_logs.EResetLogSource', null, global);
goog.exportSymbol('proto.mon_logs.FaultFactsLogEntry', null, global);
goog.exportSymbol('proto.mon_logs.FaultFactsLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.FaultHeaderLogEntryMmu', null, global);
goog.exportSymbol('proto.mon_logs.FaultHeaderLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.FaultMeasurementLogEntryMmu', null, global);
goog.exportSymbol('proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.FaultSequenceLogEntryMmu', null, global);
goog.exportSymbol('proto.mon_logs.FaultSequenceLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.HardwareRevisionsMmu', null, global);
goog.exportSymbol('proto.mon_logs.LogEntryCount', null, global);
goog.exportSymbol('proto.mon_logs.MmuPowerMonitors', null, global);
goog.exportSymbol('proto.mon_logs.Port1LogEntryMmu', null, global);
goog.exportSymbol('proto.mon_logs.Port1LogLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.PowerLogEntryMmu', null, global);
goog.exportSymbol('proto.mon_logs.PowerLogMultipleEntriesMmu', null, global);
goog.exportSymbol('proto.mon_logs.ResetLogEntryMmu', null, global);
goog.exportSymbol('proto.mon_logs.ResetLogMultipleEntriesMmu', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.MmuPowerMonitors = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.MmuPowerMonitors, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.MmuPowerMonitors.displayName = 'proto.mon_logs.MmuPowerMonitors';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.PowerLogEntryMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.PowerLogEntryMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.PowerLogEntryMmu.displayName = 'proto.mon_logs.PowerLogEntryMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.PowerLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.PowerLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.PowerLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.PowerLogMultipleEntriesMmu.displayName = 'proto.mon_logs.PowerLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.ResetLogEntryMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.ResetLogEntryMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.ResetLogEntryMmu.displayName = 'proto.mon_logs.ResetLogEntryMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.ResetLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.ResetLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.ResetLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.ResetLogMultipleEntriesMmu.displayName = 'proto.mon_logs.ResetLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.ClockLogEntry = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.ClockLogEntry, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.ClockLogEntry.displayName = 'proto.mon_logs.ClockLogEntry';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.ClockLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.ClockLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.ClockLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.ClockLogMultipleEntriesMmu.displayName = 'proto.mon_logs.ClockLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.HardwareRevisionsMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.HardwareRevisionsMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.HardwareRevisionsMmu.displayName = 'proto.mon_logs.HardwareRevisionsMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.ConfigLogEntryMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.ConfigLogEntryMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.ConfigLogEntryMmu.displayName = 'proto.mon_logs.ConfigLogEntryMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.ConfigLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.ConfigLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.ConfigLogMultipleEntriesMmu.displayName = 'proto.mon_logs.ConfigLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.Port1LogEntryMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.Port1LogEntryMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.Port1LogEntryMmu.displayName = 'proto.mon_logs.Port1LogEntryMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.Port1LogLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.Port1LogLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.Port1LogLogMultipleEntriesMmu.displayName = 'proto.mon_logs.Port1LogLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultHeaderLogEntryMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.FaultHeaderLogEntryMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultHeaderLogEntryMmu.displayName = 'proto.mon_logs.FaultHeaderLogEntryMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.FaultHeaderLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.displayName = 'proto.mon_logs.FaultHeaderLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultMeasurementLogEntryMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.FaultMeasurementLogEntryMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.FaultMeasurementLogEntryMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultMeasurementLogEntryMmu.displayName = 'proto.mon_logs.FaultMeasurementLogEntryMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.displayName = 'proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultSequenceLogEntryMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.FaultSequenceLogEntryMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultSequenceLogEntryMmu.displayName = 'proto.mon_logs.FaultSequenceLogEntryMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.FaultSequenceLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.displayName = 'proto.mon_logs.FaultSequenceLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultFactsLogEntry = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.FaultFactsLogEntry, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultFactsLogEntry.displayName = 'proto.mon_logs.FaultFactsLogEntry';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.FaultFactsLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.FaultFactsLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.FaultFactsLogMultipleEntriesMmu.displayName = 'proto.mon_logs.FaultFactsLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.AlarmLogEntry = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.AlarmLogEntry, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.AlarmLogEntry.displayName = 'proto.mon_logs.AlarmLogEntry';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.mon_logs.AlarmLogMultipleEntriesMmu.repeatedFields_, null);
};
goog.inherits(proto.mon_logs.AlarmLogMultipleEntriesMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.AlarmLogMultipleEntriesMmu.displayName = 'proto.mon_logs.AlarmLogMultipleEntriesMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.LogEntryCount = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.LogEntryCount, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.LogEntryCount.displayName = 'proto.mon_logs.LogEntryCount';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_logs.DataKeyErrorCodeBitmap = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_logs.DataKeyErrorCodeBitmap, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_logs.DataKeyErrorCodeBitmap.displayName = 'proto.mon_logs.DataKeyErrorCodeBitmap';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.MmuPowerMonitors.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.MmuPowerMonitors.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.MmuPowerMonitors} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.MmuPowerMonitors.toObject = function(includeInstance, msg) {
  var f, obj = {
acMainsVolts: (f = msg.getAcMainsVolts()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
dc24VoltsMon1: (f = msg.getDc24VoltsMon1()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
dc24VoltsMon2: (f = msg.getDc24VoltsMon2()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
cvmVolt: (f = msg.getCvmVolt()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
lineFrequencyHz: (f = msg.getLineFrequencyHz()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.MmuPowerMonitors}
 */
proto.mon_logs.MmuPowerMonitors.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.MmuPowerMonitors;
  return proto.mon_logs.MmuPowerMonitors.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.MmuPowerMonitors} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.MmuPowerMonitors}
 */
proto.mon_logs.MmuPowerMonitors.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setAcMainsVolts(value);
      break;
    case 2:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setDc24VoltsMon1(value);
      break;
    case 3:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setDc24VoltsMon2(value);
      break;
    case 4:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setCvmVolt(value);
      break;
    case 5:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setLineFrequencyHz(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.MmuPowerMonitors.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.MmuPowerMonitors.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.MmuPowerMonitors} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.MmuPowerMonitors.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAcMainsVolts();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getDc24VoltsMon1();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getDc24VoltsMon2();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getCvmVolt();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getLineFrequencyHz();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
};


/**
 * optional basic.NowMinMaxFloat ac_mains_volts = 1;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.mon_logs.MmuPowerMonitors.prototype.getAcMainsVolts = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 1));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
*/
proto.mon_logs.MmuPowerMonitors.prototype.setAcMainsVolts = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
 */
proto.mon_logs.MmuPowerMonitors.prototype.clearAcMainsVolts = function() {
  return this.setAcMainsVolts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.MmuPowerMonitors.prototype.hasAcMainsVolts = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional basic.NowMinMaxFloat dc_24_volts_mon1 = 2;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.mon_logs.MmuPowerMonitors.prototype.getDc24VoltsMon1 = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 2));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
*/
proto.mon_logs.MmuPowerMonitors.prototype.setDc24VoltsMon1 = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
 */
proto.mon_logs.MmuPowerMonitors.prototype.clearDc24VoltsMon1 = function() {
  return this.setDc24VoltsMon1(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.MmuPowerMonitors.prototype.hasDc24VoltsMon1 = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional basic.NowMinMaxFloat dc_24_volts_mon2 = 3;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.mon_logs.MmuPowerMonitors.prototype.getDc24VoltsMon2 = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 3));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
*/
proto.mon_logs.MmuPowerMonitors.prototype.setDc24VoltsMon2 = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
 */
proto.mon_logs.MmuPowerMonitors.prototype.clearDc24VoltsMon2 = function() {
  return this.setDc24VoltsMon2(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.MmuPowerMonitors.prototype.hasDc24VoltsMon2 = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional basic.NowMinMaxFloat cvm_volt = 4;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.mon_logs.MmuPowerMonitors.prototype.getCvmVolt = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 4));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
*/
proto.mon_logs.MmuPowerMonitors.prototype.setCvmVolt = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
 */
proto.mon_logs.MmuPowerMonitors.prototype.clearCvmVolt = function() {
  return this.setCvmVolt(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.MmuPowerMonitors.prototype.hasCvmVolt = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional basic.NowMinMaxFloat line_frequency_hz = 5;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.mon_logs.MmuPowerMonitors.prototype.getLineFrequencyHz = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 5));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
*/
proto.mon_logs.MmuPowerMonitors.prototype.setLineFrequencyHz = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.MmuPowerMonitors} returns this
 */
proto.mon_logs.MmuPowerMonitors.prototype.clearLineFrequencyHz = function() {
  return this.setLineFrequencyHz(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.MmuPowerMonitors.prototype.hasLineFrequencyHz = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.PowerLogEntryMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.PowerLogEntryMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.PowerLogEntryMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
entryId: jspb.Message.getFieldWithDefault(msg, 1, 0),
eventType: jspb.Message.getFieldWithDefault(msg, 2, 0),
entryTimestamp: (f = msg.getEntryTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
powerVoltages: (f = msg.getPowerVoltages()) && proto.mon_logs.MmuPowerMonitors.toObject(includeInstance, f),
temperatureDegf: (f = msg.getTemperatureDegf()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
eventTiming: jspb.Message.getFieldWithDefault(msg, 8, 0),
powerInterruptTimeMs: (f = jspb.Message.getField(msg, 7)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.PowerLogEntryMmu}
 */
proto.mon_logs.PowerLogEntryMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.PowerLogEntryMmu;
  return proto.mon_logs.PowerLogEntryMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.PowerLogEntryMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.PowerLogEntryMmu}
 */
proto.mon_logs.PowerLogEntryMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 2:
      var value = /** @type {!proto.mon_logs.EPowerLogEventType} */ (reader.readEnum());
      msg.setEventType(value);
      break;
    case 3:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setEntryTimestamp(value);
      break;
    case 4:
      var value = new proto.mon_logs.MmuPowerMonitors;
      reader.readMessage(value,proto.mon_logs.MmuPowerMonitors.deserializeBinaryFromReader);
      msg.setPowerVoltages(value);
      break;
    case 5:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setTemperatureDegf(value);
      break;
    case 8:
      var value = /** @type {!proto.mon_logs.EPowerLogEventTiming} */ (reader.readEnum());
      msg.setEventTiming(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPowerInterruptTimeMs(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.PowerLogEntryMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.PowerLogEntryMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.PowerLogEntryMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEventType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getEntryTimestamp();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getPowerVoltages();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.mon_logs.MmuPowerMonitors.serializeBinaryToWriter
    );
  }
  f = message.getTemperatureDegf();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getEventTiming();
  if (f !== 0.0) {
    writer.writeEnum(
      8,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 7));
  if (f != null) {
    writer.writeUint32(
      7,
      f
    );
  }
};


/**
 * optional uint32 entry_id = 1;
 * @return {number}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional EPowerLogEventType event_type = 2;
 * @return {!proto.mon_logs.EPowerLogEventType}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.getEventType = function() {
  return /** @type {!proto.mon_logs.EPowerLogEventType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.mon_logs.EPowerLogEventType} value
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.setEventType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional basic.LocalDateTime entry_timestamp = 3;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.getEntryTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 3));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
*/
proto.mon_logs.PowerLogEntryMmu.prototype.setEntryTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.clearEntryTimestamp = function() {
  return this.setEntryTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.hasEntryTimestamp = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional MmuPowerMonitors power_voltages = 4;
 * @return {?proto.mon_logs.MmuPowerMonitors}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.getPowerVoltages = function() {
  return /** @type{?proto.mon_logs.MmuPowerMonitors} */ (
    jspb.Message.getWrapperField(this, proto.mon_logs.MmuPowerMonitors, 4));
};


/**
 * @param {?proto.mon_logs.MmuPowerMonitors|undefined} value
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
*/
proto.mon_logs.PowerLogEntryMmu.prototype.setPowerVoltages = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.clearPowerVoltages = function() {
  return this.setPowerVoltages(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.hasPowerVoltages = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional basic.NowMinMaxFloat temperature_degf = 5;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.getTemperatureDegf = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 5));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
*/
proto.mon_logs.PowerLogEntryMmu.prototype.setTemperatureDegf = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.clearTemperatureDegf = function() {
  return this.setTemperatureDegf(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.hasTemperatureDegf = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional EPowerLogEventTiming event_timing = 8;
 * @return {!proto.mon_logs.EPowerLogEventTiming}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.getEventTiming = function() {
  return /** @type {!proto.mon_logs.EPowerLogEventTiming} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {!proto.mon_logs.EPowerLogEventTiming} value
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.setEventTiming = function(value) {
  return jspb.Message.setProto3EnumField(this, 8, value);
};


/**
 * optional uint32 power_interrupt_time_ms = 7;
 * @return {number}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.getPowerInterruptTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.setPowerInterruptTimeMs = function(value) {
  return jspb.Message.setField(this, 7, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.PowerLogEntryMmu} returns this
 */
proto.mon_logs.PowerLogEntryMmu.prototype.clearPowerInterruptTimeMs = function() {
  return jspb.Message.setField(this, 7, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.PowerLogEntryMmu.prototype.hasPowerInterruptTimeMs = function() {
  return jspb.Message.getField(this, 7) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.PowerLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.PowerLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.PowerLogEntryMmu.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.PowerLogMultipleEntriesMmu}
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.PowerLogMultipleEntriesMmu;
  return proto.mon_logs.PowerLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.PowerLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.PowerLogMultipleEntriesMmu}
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.PowerLogEntryMmu;
      reader.readMessage(value,proto.mon_logs.PowerLogEntryMmu.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.PowerLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.PowerLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.PowerLogEntryMmu.serializeBinaryToWriter
    );
  }
};


/**
 * repeated PowerLogEntryMmu log_entry = 1;
 * @return {!Array<!proto.mon_logs.PowerLogEntryMmu>}
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.PowerLogEntryMmu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.PowerLogEntryMmu, 1));
};


/**
 * @param {!Array<!proto.mon_logs.PowerLogEntryMmu>} value
 * @return {!proto.mon_logs.PowerLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.PowerLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.PowerLogEntryMmu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.PowerLogEntryMmu}
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.PowerLogEntryMmu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.PowerLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.PowerLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.ResetLogEntryMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.ResetLogEntryMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ResetLogEntryMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
entryId: jspb.Message.getFieldWithDefault(msg, 1, 0),
resetSource: jspb.Message.getFieldWithDefault(msg, 2, 0),
entryTimestamp: (f = msg.getEntryTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
faultCleared: jspb.Message.getFieldWithDefault(msg, 4, 0),
diagnosticCleared: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
faultId: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.ResetLogEntryMmu}
 */
proto.mon_logs.ResetLogEntryMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.ResetLogEntryMmu;
  return proto.mon_logs.ResetLogEntryMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.ResetLogEntryMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.ResetLogEntryMmu}
 */
proto.mon_logs.ResetLogEntryMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 2:
      var value = /** @type {!proto.mon_logs.EResetLogSource} */ (reader.readEnum());
      msg.setResetSource(value);
      break;
    case 3:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setEntryTimestamp(value);
      break;
    case 4:
      var value = /** @type {!proto.mon_faults.EFaultCode} */ (reader.readEnum());
      msg.setFaultCleared(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDiagnosticCleared(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.ResetLogEntryMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.ResetLogEntryMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ResetLogEntryMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getResetSource();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getEntryTimestamp();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getFaultCleared();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getDiagnosticCleared();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getFaultId();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
};


/**
 * optional uint32 entry_id = 1;
 * @return {number}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.ResetLogEntryMmu} returns this
 */
proto.mon_logs.ResetLogEntryMmu.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional EResetLogSource reset_source = 2;
 * @return {!proto.mon_logs.EResetLogSource}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.getResetSource = function() {
  return /** @type {!proto.mon_logs.EResetLogSource} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.mon_logs.EResetLogSource} value
 * @return {!proto.mon_logs.ResetLogEntryMmu} returns this
 */
proto.mon_logs.ResetLogEntryMmu.prototype.setResetSource = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional basic.LocalDateTime entry_timestamp = 3;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.getEntryTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 3));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.ResetLogEntryMmu} returns this
*/
proto.mon_logs.ResetLogEntryMmu.prototype.setEntryTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ResetLogEntryMmu} returns this
 */
proto.mon_logs.ResetLogEntryMmu.prototype.clearEntryTimestamp = function() {
  return this.setEntryTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.hasEntryTimestamp = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional mon_faults.EFaultCode fault_cleared = 4;
 * @return {!proto.mon_faults.EFaultCode}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.getFaultCleared = function() {
  return /** @type {!proto.mon_faults.EFaultCode} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.mon_faults.EFaultCode} value
 * @return {!proto.mon_logs.ResetLogEntryMmu} returns this
 */
proto.mon_logs.ResetLogEntryMmu.prototype.setFaultCleared = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional bool diagnostic_cleared = 5;
 * @return {boolean}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.getDiagnosticCleared = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_logs.ResetLogEntryMmu} returns this
 */
proto.mon_logs.ResetLogEntryMmu.prototype.setDiagnosticCleared = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional uint32 fault_id = 6;
 * @return {number}
 */
proto.mon_logs.ResetLogEntryMmu.prototype.getFaultId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.ResetLogEntryMmu} returns this
 */
proto.mon_logs.ResetLogEntryMmu.prototype.setFaultId = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.ResetLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.ResetLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.ResetLogEntryMmu.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.ResetLogMultipleEntriesMmu}
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.ResetLogMultipleEntriesMmu;
  return proto.mon_logs.ResetLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.ResetLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.ResetLogMultipleEntriesMmu}
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.ResetLogEntryMmu;
      reader.readMessage(value,proto.mon_logs.ResetLogEntryMmu.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.ResetLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.ResetLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.ResetLogEntryMmu.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ResetLogEntryMmu log_entry = 1;
 * @return {!Array<!proto.mon_logs.ResetLogEntryMmu>}
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.ResetLogEntryMmu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.ResetLogEntryMmu, 1));
};


/**
 * @param {!Array<!proto.mon_logs.ResetLogEntryMmu>} value
 * @return {!proto.mon_logs.ResetLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.ResetLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.ResetLogEntryMmu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.ResetLogEntryMmu}
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.ResetLogEntryMmu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.ResetLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.ResetLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.ClockLogEntry.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.ClockLogEntry.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.ClockLogEntry} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ClockLogEntry.toObject = function(includeInstance, msg) {
  var f, obj = {
entryId: jspb.Message.getFieldWithDefault(msg, 1, 0),
clockSource: jspb.Message.getFieldWithDefault(msg, 2, 0),
previousDatetime: (f = msg.getPreviousDatetime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
newDatetime: (f = msg.getNewDatetime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
runTime: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.ClockLogEntry}
 */
proto.mon_logs.ClockLogEntry.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.ClockLogEntry;
  return proto.mon_logs.ClockLogEntry.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.ClockLogEntry} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.ClockLogEntry}
 */
proto.mon_logs.ClockLogEntry.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 2:
      var value = /** @type {!proto.mon_logs.EClockLogSource} */ (reader.readEnum());
      msg.setClockSource(value);
      break;
    case 3:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setPreviousDatetime(value);
      break;
    case 4:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setNewDatetime(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRunTime(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.ClockLogEntry.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.ClockLogEntry.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.ClockLogEntry} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ClockLogEntry.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getClockSource();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getPreviousDatetime();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getNewDatetime();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getRunTime();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
};


/**
 * optional uint32 entry_id = 1;
 * @return {number}
 */
proto.mon_logs.ClockLogEntry.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.ClockLogEntry} returns this
 */
proto.mon_logs.ClockLogEntry.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional EClockLogSource clock_source = 2;
 * @return {!proto.mon_logs.EClockLogSource}
 */
proto.mon_logs.ClockLogEntry.prototype.getClockSource = function() {
  return /** @type {!proto.mon_logs.EClockLogSource} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.mon_logs.EClockLogSource} value
 * @return {!proto.mon_logs.ClockLogEntry} returns this
 */
proto.mon_logs.ClockLogEntry.prototype.setClockSource = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional basic.LocalDateTime previous_datetime = 3;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.ClockLogEntry.prototype.getPreviousDatetime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 3));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.ClockLogEntry} returns this
*/
proto.mon_logs.ClockLogEntry.prototype.setPreviousDatetime = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ClockLogEntry} returns this
 */
proto.mon_logs.ClockLogEntry.prototype.clearPreviousDatetime = function() {
  return this.setPreviousDatetime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ClockLogEntry.prototype.hasPreviousDatetime = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional basic.LocalDateTime new_datetime = 4;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.ClockLogEntry.prototype.getNewDatetime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 4));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.ClockLogEntry} returns this
*/
proto.mon_logs.ClockLogEntry.prototype.setNewDatetime = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ClockLogEntry} returns this
 */
proto.mon_logs.ClockLogEntry.prototype.clearNewDatetime = function() {
  return this.setNewDatetime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ClockLogEntry.prototype.hasNewDatetime = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional uint32 run_time = 5;
 * @return {number}
 */
proto.mon_logs.ClockLogEntry.prototype.getRunTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.ClockLogEntry} returns this
 */
proto.mon_logs.ClockLogEntry.prototype.setRunTime = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.ClockLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.ClockLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.ClockLogEntry.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.ClockLogMultipleEntriesMmu}
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.ClockLogMultipleEntriesMmu;
  return proto.mon_logs.ClockLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.ClockLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.ClockLogMultipleEntriesMmu}
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.ClockLogEntry;
      reader.readMessage(value,proto.mon_logs.ClockLogEntry.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.ClockLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.ClockLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.ClockLogEntry.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ClockLogEntry log_entry = 1;
 * @return {!Array<!proto.mon_logs.ClockLogEntry>}
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.ClockLogEntry>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.ClockLogEntry, 1));
};


/**
 * @param {!Array<!proto.mon_logs.ClockLogEntry>} value
 * @return {!proto.mon_logs.ClockLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.ClockLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.ClockLogEntry=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.ClockLogEntry}
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.ClockLogEntry, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.ClockLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.ClockLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.HardwareRevisionsMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.HardwareRevisionsMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.HardwareRevisionsMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
mainHardwareRevision: jspb.Message.getFieldWithDefault(msg, 1, 0),
isoHardwareRevision: jspb.Message.getFieldWithDefault(msg, 2, 0),
displayHardwareRevision: jspb.Message.getFieldWithDefault(msg, 3, 0),
displayRmsHardwareRevision: jspb.Message.getFieldWithDefault(msg, 4, 0),
commsHardwareRevision: jspb.Message.getFieldWithDefault(msg, 5, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.HardwareRevisionsMmu}
 */
proto.mon_logs.HardwareRevisionsMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.HardwareRevisionsMmu;
  return proto.mon_logs.HardwareRevisionsMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.HardwareRevisionsMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.HardwareRevisionsMmu}
 */
proto.mon_logs.HardwareRevisionsMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMainHardwareRevision(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIsoHardwareRevision(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDisplayHardwareRevision(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDisplayRmsHardwareRevision(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCommsHardwareRevision(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.HardwareRevisionsMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.HardwareRevisionsMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.HardwareRevisionsMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMainHardwareRevision();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getIsoHardwareRevision();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getDisplayHardwareRevision();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getDisplayRmsHardwareRevision();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getCommsHardwareRevision();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
};


/**
 * optional uint32 main_hardware_revision = 1;
 * @return {number}
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.getMainHardwareRevision = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.HardwareRevisionsMmu} returns this
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.setMainHardwareRevision = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 iso_hardware_revision = 2;
 * @return {number}
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.getIsoHardwareRevision = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.HardwareRevisionsMmu} returns this
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.setIsoHardwareRevision = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 display_hardware_revision = 3;
 * @return {number}
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.getDisplayHardwareRevision = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.HardwareRevisionsMmu} returns this
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.setDisplayHardwareRevision = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 display_rms_hardware_revision = 4;
 * @return {number}
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.getDisplayRmsHardwareRevision = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.HardwareRevisionsMmu} returns this
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.setDisplayRmsHardwareRevision = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 comms_hardware_revision = 5;
 * @return {number}
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.getCommsHardwareRevision = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.HardwareRevisionsMmu} returns this
 */
proto.mon_logs.HardwareRevisionsMmu.prototype.setCommsHardwareRevision = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.ConfigLogEntryMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.ConfigLogEntryMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ConfigLogEntryMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
configId: jspb.Message.getFieldWithDefault(msg, 1, 0),
hardware: (f = msg.getHardware()) && proto.mon_logs.HardwareRevisionsMmu.toObject(includeInstance, f),
dataKeyCrc: jspb.Message.getFieldWithDefault(msg, 10, 0),
dataKeyData: msg.getDataKeyData_asB64(),
factoryOptions: (f = msg.getFactoryOptions()) && settings_pb.FactoryOptionsMmu.toObject(includeInstance, f),
pcbOptions: (f = msg.getPcbOptions()) && settings_pb.PcbOptionsMmu.toObject(includeInstance, f),
agencyOptions: (f = msg.getAgencyOptions()) && settings_pb.AgencyOptionsMmu.toObject(includeInstance, f),
monitorCrc: jspb.Message.getFieldWithDefault(msg, 12, 0),
entryTimestamp: (f = msg.getEntryTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
numbers: (f = msg.getNumbers()) && basic_pb.ModelAndSerialNumber.toObject(includeInstance, f),
mainMcuFwVersion: (f = msg.getMainMcuFwVersion()) && basic_pb.VersionStrThree.toObject(includeInstance, f),
mainMcuFwDate: (f = msg.getMainMcuFwDate()) && basic_pb.DateStr.toObject(includeInstance, f),
isolatedMcuFwVersion: (f = msg.getIsolatedMcuFwVersion()) && basic_pb.VersionStrThree.toObject(includeInstance, f),
isolatedMcuFwDate: (f = msg.getIsolatedMcuFwDate()) && basic_pb.DateStr.toObject(includeInstance, f),
dispMcuFwVersion: (f = msg.getDispMcuFwVersion()) && basic_pb.VersionStrThree.toObject(includeInstance, f),
dispMcuFwDate: (f = msg.getDispMcuFwDate()) && basic_pb.DateStr.toObject(includeInstance, f),
commsMcuFwVersion: (f = msg.getCommsMcuFwVersion()) && basic_pb.VersionStrThree.toObject(includeInstance, f),
commsMcuFwDate: (f = msg.getCommsMcuFwDate()) && basic_pb.DateStr.toObject(includeInstance, f),
bleMcuFwVersion: (f = msg.getBleMcuFwVersion()) && basic_pb.VersionStrThree.toObject(includeInstance, f),
bleMcuFwDate: (f = msg.getBleMcuFwDate()) && basic_pb.DateStr.toObject(includeInstance, f),
pkgMcuFwVersion: (f = msg.getPkgMcuFwVersion()) && basic_pb.VersionStrThree.toObject(includeInstance, f),
pkgMcuFwDate: (f = msg.getPkgMcuFwDate()) && basic_pb.DateStr.toObject(includeInstance, f),
dataSrc: jspb.Message.getFieldWithDefault(msg, 25, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.ConfigLogEntryMmu}
 */
proto.mon_logs.ConfigLogEntryMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.ConfigLogEntryMmu;
  return proto.mon_logs.ConfigLogEntryMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.ConfigLogEntryMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.ConfigLogEntryMmu}
 */
proto.mon_logs.ConfigLogEntryMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setConfigId(value);
      break;
    case 2:
      var value = new proto.mon_logs.HardwareRevisionsMmu;
      reader.readMessage(value,proto.mon_logs.HardwareRevisionsMmu.deserializeBinaryFromReader);
      msg.setHardware(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDataKeyCrc(value);
      break;
    case 9:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setDataKeyData(value);
      break;
    case 23:
      var value = new settings_pb.FactoryOptionsMmu;
      reader.readMessage(value,settings_pb.FactoryOptionsMmu.deserializeBinaryFromReader);
      msg.setFactoryOptions(value);
      break;
    case 22:
      var value = new settings_pb.PcbOptionsMmu;
      reader.readMessage(value,settings_pb.PcbOptionsMmu.deserializeBinaryFromReader);
      msg.setPcbOptions(value);
      break;
    case 24:
      var value = new settings_pb.AgencyOptionsMmu;
      reader.readMessage(value,settings_pb.AgencyOptionsMmu.deserializeBinaryFromReader);
      msg.setAgencyOptions(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMonitorCrc(value);
      break;
    case 4:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setEntryTimestamp(value);
      break;
    case 5:
      var value = new basic_pb.ModelAndSerialNumber;
      reader.readMessage(value,basic_pb.ModelAndSerialNumber.deserializeBinaryFromReader);
      msg.setNumbers(value);
      break;
    case 6:
      var value = new basic_pb.VersionStrThree;
      reader.readMessage(value,basic_pb.VersionStrThree.deserializeBinaryFromReader);
      msg.setMainMcuFwVersion(value);
      break;
    case 13:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setMainMcuFwDate(value);
      break;
    case 7:
      var value = new basic_pb.VersionStrThree;
      reader.readMessage(value,basic_pb.VersionStrThree.deserializeBinaryFromReader);
      msg.setIsolatedMcuFwVersion(value);
      break;
    case 14:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setIsolatedMcuFwDate(value);
      break;
    case 15:
      var value = new basic_pb.VersionStrThree;
      reader.readMessage(value,basic_pb.VersionStrThree.deserializeBinaryFromReader);
      msg.setDispMcuFwVersion(value);
      break;
    case 16:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setDispMcuFwDate(value);
      break;
    case 8:
      var value = new basic_pb.VersionStrThree;
      reader.readMessage(value,basic_pb.VersionStrThree.deserializeBinaryFromReader);
      msg.setCommsMcuFwVersion(value);
      break;
    case 17:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setCommsMcuFwDate(value);
      break;
    case 18:
      var value = new basic_pb.VersionStrThree;
      reader.readMessage(value,basic_pb.VersionStrThree.deserializeBinaryFromReader);
      msg.setBleMcuFwVersion(value);
      break;
    case 19:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setBleMcuFwDate(value);
      break;
    case 20:
      var value = new basic_pb.VersionStrThree;
      reader.readMessage(value,basic_pb.VersionStrThree.deserializeBinaryFromReader);
      msg.setPkgMcuFwVersion(value);
      break;
    case 21:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setPkgMcuFwDate(value);
      break;
    case 25:
      var value = /** @type {!proto.settings.EConfigDataLocation} */ (reader.readEnum());
      msg.setDataSrc(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.ConfigLogEntryMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.ConfigLogEntryMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ConfigLogEntryMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getConfigId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getHardware();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.mon_logs.HardwareRevisionsMmu.serializeBinaryToWriter
    );
  }
  f = message.getDataKeyCrc();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getDataKeyData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      9,
      f
    );
  }
  f = message.getFactoryOptions();
  if (f != null) {
    writer.writeMessage(
      23,
      f,
      settings_pb.FactoryOptionsMmu.serializeBinaryToWriter
    );
  }
  f = message.getPcbOptions();
  if (f != null) {
    writer.writeMessage(
      22,
      f,
      settings_pb.PcbOptionsMmu.serializeBinaryToWriter
    );
  }
  f = message.getAgencyOptions();
  if (f != null) {
    writer.writeMessage(
      24,
      f,
      settings_pb.AgencyOptionsMmu.serializeBinaryToWriter
    );
  }
  f = message.getMonitorCrc();
  if (f !== 0) {
    writer.writeUint32(
      12,
      f
    );
  }
  f = message.getEntryTimestamp();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getNumbers();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      basic_pb.ModelAndSerialNumber.serializeBinaryToWriter
    );
  }
  f = message.getMainMcuFwVersion();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      basic_pb.VersionStrThree.serializeBinaryToWriter
    );
  }
  f = message.getMainMcuFwDate();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
  f = message.getIsolatedMcuFwVersion();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      basic_pb.VersionStrThree.serializeBinaryToWriter
    );
  }
  f = message.getIsolatedMcuFwDate();
  if (f != null) {
    writer.writeMessage(
      14,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
  f = message.getDispMcuFwVersion();
  if (f != null) {
    writer.writeMessage(
      15,
      f,
      basic_pb.VersionStrThree.serializeBinaryToWriter
    );
  }
  f = message.getDispMcuFwDate();
  if (f != null) {
    writer.writeMessage(
      16,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
  f = message.getCommsMcuFwVersion();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      basic_pb.VersionStrThree.serializeBinaryToWriter
    );
  }
  f = message.getCommsMcuFwDate();
  if (f != null) {
    writer.writeMessage(
      17,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
  f = message.getBleMcuFwVersion();
  if (f != null) {
    writer.writeMessage(
      18,
      f,
      basic_pb.VersionStrThree.serializeBinaryToWriter
    );
  }
  f = message.getBleMcuFwDate();
  if (f != null) {
    writer.writeMessage(
      19,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
  f = message.getPkgMcuFwVersion();
  if (f != null) {
    writer.writeMessage(
      20,
      f,
      basic_pb.VersionStrThree.serializeBinaryToWriter
    );
  }
  f = message.getPkgMcuFwDate();
  if (f != null) {
    writer.writeMessage(
      21,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
  f = message.getDataSrc();
  if (f !== 0.0) {
    writer.writeEnum(
      25,
      f
    );
  }
};


/**
 * optional uint32 config_id = 1;
 * @return {number}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getConfigId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.setConfigId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional HardwareRevisionsMmu hardware = 2;
 * @return {?proto.mon_logs.HardwareRevisionsMmu}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getHardware = function() {
  return /** @type{?proto.mon_logs.HardwareRevisionsMmu} */ (
    jspb.Message.getWrapperField(this, proto.mon_logs.HardwareRevisionsMmu, 2));
};


/**
 * @param {?proto.mon_logs.HardwareRevisionsMmu|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setHardware = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearHardware = function() {
  return this.setHardware(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasHardware = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional uint32 data_key_crc = 10;
 * @return {number}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getDataKeyCrc = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.setDataKeyCrc = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional bytes data_key_data = 9;
 * @return {string}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getDataKeyData = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * optional bytes data_key_data = 9;
 * This is a type-conversion wrapper around `getDataKeyData()`
 * @return {string}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getDataKeyData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getDataKeyData()));
};


/**
 * optional bytes data_key_data = 9;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getDataKeyData()`
 * @return {!Uint8Array}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getDataKeyData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getDataKeyData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.setDataKeyData = function(value) {
  return jspb.Message.setProto3BytesField(this, 9, value);
};


/**
 * optional settings.FactoryOptionsMmu factory_options = 23;
 * @return {?proto.settings.FactoryOptionsMmu}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getFactoryOptions = function() {
  return /** @type{?proto.settings.FactoryOptionsMmu} */ (
    jspb.Message.getWrapperField(this, settings_pb.FactoryOptionsMmu, 23));
};


/**
 * @param {?proto.settings.FactoryOptionsMmu|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setFactoryOptions = function(value) {
  return jspb.Message.setWrapperField(this, 23, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearFactoryOptions = function() {
  return this.setFactoryOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasFactoryOptions = function() {
  return jspb.Message.getField(this, 23) != null;
};


/**
 * optional settings.PcbOptionsMmu pcb_options = 22;
 * @return {?proto.settings.PcbOptionsMmu}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getPcbOptions = function() {
  return /** @type{?proto.settings.PcbOptionsMmu} */ (
    jspb.Message.getWrapperField(this, settings_pb.PcbOptionsMmu, 22));
};


/**
 * @param {?proto.settings.PcbOptionsMmu|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setPcbOptions = function(value) {
  return jspb.Message.setWrapperField(this, 22, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearPcbOptions = function() {
  return this.setPcbOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasPcbOptions = function() {
  return jspb.Message.getField(this, 22) != null;
};


/**
 * optional settings.AgencyOptionsMmu agency_options = 24;
 * @return {?proto.settings.AgencyOptionsMmu}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getAgencyOptions = function() {
  return /** @type{?proto.settings.AgencyOptionsMmu} */ (
    jspb.Message.getWrapperField(this, settings_pb.AgencyOptionsMmu, 24));
};


/**
 * @param {?proto.settings.AgencyOptionsMmu|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setAgencyOptions = function(value) {
  return jspb.Message.setWrapperField(this, 24, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearAgencyOptions = function() {
  return this.setAgencyOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasAgencyOptions = function() {
  return jspb.Message.getField(this, 24) != null;
};


/**
 * optional uint32 monitor_crc = 12;
 * @return {number}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getMonitorCrc = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.setMonitorCrc = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional basic.LocalDateTime entry_timestamp = 4;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getEntryTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 4));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setEntryTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearEntryTimestamp = function() {
  return this.setEntryTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasEntryTimestamp = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional basic.ModelAndSerialNumber numbers = 5;
 * @return {?proto.basic.ModelAndSerialNumber}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getNumbers = function() {
  return /** @type{?proto.basic.ModelAndSerialNumber} */ (
    jspb.Message.getWrapperField(this, basic_pb.ModelAndSerialNumber, 5));
};


/**
 * @param {?proto.basic.ModelAndSerialNumber|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setNumbers = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearNumbers = function() {
  return this.setNumbers(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasNumbers = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional basic.VersionStrThree main_mcu_fw_version = 6;
 * @return {?proto.basic.VersionStrThree}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getMainMcuFwVersion = function() {
  return /** @type{?proto.basic.VersionStrThree} */ (
    jspb.Message.getWrapperField(this, basic_pb.VersionStrThree, 6));
};


/**
 * @param {?proto.basic.VersionStrThree|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setMainMcuFwVersion = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearMainMcuFwVersion = function() {
  return this.setMainMcuFwVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasMainMcuFwVersion = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional basic.DateStr main_mcu_fw_date = 13;
 * @return {?proto.basic.DateStr}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getMainMcuFwDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 13));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setMainMcuFwDate = function(value) {
  return jspb.Message.setWrapperField(this, 13, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearMainMcuFwDate = function() {
  return this.setMainMcuFwDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasMainMcuFwDate = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional basic.VersionStrThree isolated_mcu_fw_version = 7;
 * @return {?proto.basic.VersionStrThree}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getIsolatedMcuFwVersion = function() {
  return /** @type{?proto.basic.VersionStrThree} */ (
    jspb.Message.getWrapperField(this, basic_pb.VersionStrThree, 7));
};


/**
 * @param {?proto.basic.VersionStrThree|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setIsolatedMcuFwVersion = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearIsolatedMcuFwVersion = function() {
  return this.setIsolatedMcuFwVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasIsolatedMcuFwVersion = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional basic.DateStr isolated_mcu_fw_date = 14;
 * @return {?proto.basic.DateStr}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getIsolatedMcuFwDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 14));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setIsolatedMcuFwDate = function(value) {
  return jspb.Message.setWrapperField(this, 14, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearIsolatedMcuFwDate = function() {
  return this.setIsolatedMcuFwDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasIsolatedMcuFwDate = function() {
  return jspb.Message.getField(this, 14) != null;
};


/**
 * optional basic.VersionStrThree disp_mcu_fw_version = 15;
 * @return {?proto.basic.VersionStrThree}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getDispMcuFwVersion = function() {
  return /** @type{?proto.basic.VersionStrThree} */ (
    jspb.Message.getWrapperField(this, basic_pb.VersionStrThree, 15));
};


/**
 * @param {?proto.basic.VersionStrThree|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setDispMcuFwVersion = function(value) {
  return jspb.Message.setWrapperField(this, 15, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearDispMcuFwVersion = function() {
  return this.setDispMcuFwVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasDispMcuFwVersion = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional basic.DateStr disp_mcu_fw_date = 16;
 * @return {?proto.basic.DateStr}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getDispMcuFwDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 16));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setDispMcuFwDate = function(value) {
  return jspb.Message.setWrapperField(this, 16, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearDispMcuFwDate = function() {
  return this.setDispMcuFwDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasDispMcuFwDate = function() {
  return jspb.Message.getField(this, 16) != null;
};


/**
 * optional basic.VersionStrThree comms_mcu_fw_version = 8;
 * @return {?proto.basic.VersionStrThree}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getCommsMcuFwVersion = function() {
  return /** @type{?proto.basic.VersionStrThree} */ (
    jspb.Message.getWrapperField(this, basic_pb.VersionStrThree, 8));
};


/**
 * @param {?proto.basic.VersionStrThree|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setCommsMcuFwVersion = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearCommsMcuFwVersion = function() {
  return this.setCommsMcuFwVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasCommsMcuFwVersion = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional basic.DateStr comms_mcu_fw_date = 17;
 * @return {?proto.basic.DateStr}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getCommsMcuFwDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 17));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setCommsMcuFwDate = function(value) {
  return jspb.Message.setWrapperField(this, 17, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearCommsMcuFwDate = function() {
  return this.setCommsMcuFwDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasCommsMcuFwDate = function() {
  return jspb.Message.getField(this, 17) != null;
};


/**
 * optional basic.VersionStrThree ble_mcu_fw_version = 18;
 * @return {?proto.basic.VersionStrThree}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getBleMcuFwVersion = function() {
  return /** @type{?proto.basic.VersionStrThree} */ (
    jspb.Message.getWrapperField(this, basic_pb.VersionStrThree, 18));
};


/**
 * @param {?proto.basic.VersionStrThree|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setBleMcuFwVersion = function(value) {
  return jspb.Message.setWrapperField(this, 18, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearBleMcuFwVersion = function() {
  return this.setBleMcuFwVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasBleMcuFwVersion = function() {
  return jspb.Message.getField(this, 18) != null;
};


/**
 * optional basic.DateStr ble_mcu_fw_date = 19;
 * @return {?proto.basic.DateStr}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getBleMcuFwDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 19));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setBleMcuFwDate = function(value) {
  return jspb.Message.setWrapperField(this, 19, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearBleMcuFwDate = function() {
  return this.setBleMcuFwDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasBleMcuFwDate = function() {
  return jspb.Message.getField(this, 19) != null;
};


/**
 * optional basic.VersionStrThree pkg_mcu_fw_version = 20;
 * @return {?proto.basic.VersionStrThree}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getPkgMcuFwVersion = function() {
  return /** @type{?proto.basic.VersionStrThree} */ (
    jspb.Message.getWrapperField(this, basic_pb.VersionStrThree, 20));
};


/**
 * @param {?proto.basic.VersionStrThree|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setPkgMcuFwVersion = function(value) {
  return jspb.Message.setWrapperField(this, 20, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearPkgMcuFwVersion = function() {
  return this.setPkgMcuFwVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasPkgMcuFwVersion = function() {
  return jspb.Message.getField(this, 20) != null;
};


/**
 * optional basic.DateStr pkg_mcu_fw_date = 21;
 * @return {?proto.basic.DateStr}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getPkgMcuFwDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 21));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
*/
proto.mon_logs.ConfigLogEntryMmu.prototype.setPkgMcuFwDate = function(value) {
  return jspb.Message.setWrapperField(this, 21, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.clearPkgMcuFwDate = function() {
  return this.setPkgMcuFwDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.hasPkgMcuFwDate = function() {
  return jspb.Message.getField(this, 21) != null;
};


/**
 * optional settings.EConfigDataLocation data_src = 25;
 * @return {!proto.settings.EConfigDataLocation}
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.getDataSrc = function() {
  return /** @type {!proto.settings.EConfigDataLocation} */ (jspb.Message.getFieldWithDefault(this, 25, 0));
};


/**
 * @param {!proto.settings.EConfigDataLocation} value
 * @return {!proto.mon_logs.ConfigLogEntryMmu} returns this
 */
proto.mon_logs.ConfigLogEntryMmu.prototype.setDataSrc = function(value) {
  return jspb.Message.setProto3EnumField(this, 25, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.ConfigLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.ConfigLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.ConfigLogEntryMmu.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.ConfigLogMultipleEntriesMmu}
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.ConfigLogMultipleEntriesMmu;
  return proto.mon_logs.ConfigLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.ConfigLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.ConfigLogMultipleEntriesMmu}
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.ConfigLogEntryMmu;
      reader.readMessage(value,proto.mon_logs.ConfigLogEntryMmu.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.ConfigLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.ConfigLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.ConfigLogEntryMmu.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ConfigLogEntryMmu log_entry = 1;
 * @return {!Array<!proto.mon_logs.ConfigLogEntryMmu>}
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.ConfigLogEntryMmu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.ConfigLogEntryMmu, 1));
};


/**
 * @param {!Array<!proto.mon_logs.ConfigLogEntryMmu>} value
 * @return {!proto.mon_logs.ConfigLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.ConfigLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.ConfigLogEntryMmu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.ConfigLogEntryMmu}
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.ConfigLogEntryMmu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.ConfigLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.ConfigLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.Port1LogEntryMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.Port1LogEntryMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.Port1LogEntryMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
entryId: jspb.Message.getFieldWithDefault(msg, 1, 0),
entryTimestamp: (f = msg.getEntryTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
timestampWhenCleared: (f = msg.getTimestampWhenCleared()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
crcErrorCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
idleErrorCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
frameErrorCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
timeoutErrorCount: jspb.Message.getFieldWithDefault(msg, 7, 0),
shortErrorCount: jspb.Message.getFieldWithDefault(msg, 8, 0),
longErrorCount: jspb.Message.getFieldWithDefault(msg, 9, 0),
unknownErrorCount: jspb.Message.getFieldWithDefault(msg, 10, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.Port1LogEntryMmu}
 */
proto.mon_logs.Port1LogEntryMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.Port1LogEntryMmu;
  return proto.mon_logs.Port1LogEntryMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.Port1LogEntryMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.Port1LogEntryMmu}
 */
proto.mon_logs.Port1LogEntryMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 2:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setEntryTimestamp(value);
      break;
    case 3:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setTimestampWhenCleared(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCrcErrorCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIdleErrorCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFrameErrorCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTimeoutErrorCount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setShortErrorCount(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLongErrorCount(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUnknownErrorCount(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.Port1LogEntryMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.Port1LogEntryMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.Port1LogEntryMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEntryTimestamp();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getTimestampWhenCleared();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getCrcErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getIdleErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getFrameErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getTimeoutErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getShortErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getLongErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      9,
      f
    );
  }
  f = message.getUnknownErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
};


/**
 * optional uint32 entry_id = 1;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional basic.LocalDateTime entry_timestamp = 2;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getEntryTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 2));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
*/
proto.mon_logs.Port1LogEntryMmu.prototype.setEntryTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.clearEntryTimestamp = function() {
  return this.setEntryTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.hasEntryTimestamp = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional basic.LocalDateTime timestamp_when_cleared = 3;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getTimestampWhenCleared = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 3));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
*/
proto.mon_logs.Port1LogEntryMmu.prototype.setTimestampWhenCleared = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.clearTimestampWhenCleared = function() {
  return this.setTimestampWhenCleared(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.hasTimestampWhenCleared = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional uint32 crc_error_count = 4;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getCrcErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setCrcErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 idle_error_count = 5;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getIdleErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setIdleErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 frame_error_count = 6;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getFrameErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setFrameErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 timeout_error_count = 7;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getTimeoutErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setTimeoutErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 short_error_count = 8;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getShortErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setShortErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional uint32 long_error_count = 9;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getLongErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setLongErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional uint32 unknown_error_count = 10;
 * @return {number}
 */
proto.mon_logs.Port1LogEntryMmu.prototype.getUnknownErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.Port1LogEntryMmu} returns this
 */
proto.mon_logs.Port1LogEntryMmu.prototype.setUnknownErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.Port1LogLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.Port1LogLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.Port1LogEntryMmu.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.Port1LogLogMultipleEntriesMmu}
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.Port1LogLogMultipleEntriesMmu;
  return proto.mon_logs.Port1LogLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.Port1LogLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.Port1LogLogMultipleEntriesMmu}
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.Port1LogEntryMmu;
      reader.readMessage(value,proto.mon_logs.Port1LogEntryMmu.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.Port1LogLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.Port1LogLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.Port1LogEntryMmu.serializeBinaryToWriter
    );
  }
};


/**
 * repeated Port1LogEntryMmu log_entry = 1;
 * @return {!Array<!proto.mon_logs.Port1LogEntryMmu>}
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.Port1LogEntryMmu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.Port1LogEntryMmu, 1));
};


/**
 * @param {!Array<!proto.mon_logs.Port1LogEntryMmu>} value
 * @return {!proto.mon_logs.Port1LogLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.Port1LogLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.Port1LogEntryMmu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.Port1LogEntryMmu}
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.Port1LogEntryMmu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.Port1LogLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.Port1LogLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultHeaderLogEntryMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultHeaderLogEntryMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultHeaderLogEntryMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
faultId: jspb.Message.getFieldWithDefault(msg, 1, 0),
entryTimestamp: (f = msg.getEntryTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
configIdInUse: jspb.Message.getFieldWithDefault(msg, 3, 0),
dataKeyCrc: jspb.Message.getFieldWithDefault(msg, 4, 0),
numbers: (f = msg.getNumbers()) && basic_pb.ModelAndSerialNumber.toObject(includeInstance, f),
ids: (f = msg.getIds()) && basic_pb.MonitorAndUserIds.toObject(includeInstance, f),
measurementLogFrequency: jspb.Message.getFieldWithDefault(msg, 7, 0),
measurementLogEntryCount: jspb.Message.getFieldWithDefault(msg, 8, 0),
sequenceLogFrequency: jspb.Message.getFieldWithDefault(msg, 9, 0),
sequenceLogEntryCount: jspb.Message.getFieldWithDefault(msg, 10, 0),
faultCode: jspb.Message.getFieldWithDefault(msg, 11, 0),
diagnosticFault: jspb.Message.getBooleanFieldWithDefault(msg, 12, false),
faultSubcode: (f = msg.getFaultSubcode()) && mon_faults_pb.MmuSubFaultTypeValue.toObject(includeInstance, f),
diagnosticCode: jspb.Message.getFieldWithDefault(msg, 18, 0),
channelsChmap: jspb.Message.getFieldWithDefault(msg, 19, 0),
redIndicationsChmap: jspb.Message.getFieldWithDefault(msg, 20, 0),
yellowIndicationsChmap: jspb.Message.getFieldWithDefault(msg, 21, 0),
greenIndicationsChmap: jspb.Message.getFieldWithDefault(msg, 22, 0),
walkIndicationsChmap: jspb.Message.getFieldWithDefault(msg, 23, 0),
redsOnChmap: jspb.Message.getFieldWithDefault(msg, 24, 0),
yellowsOnChmap: jspb.Message.getFieldWithDefault(msg, 25, 0),
greensOnChmap: jspb.Message.getFieldWithDefault(msg, 26, 0),
walkOnChmap: jspb.Message.getFieldWithDefault(msg, 27, 0),
lineFrequency: jspb.Message.getFloatingPointFieldWithDefault(msg, 28, 0.0),
temperatureDegf: jspb.Message.getFloatingPointFieldWithDefault(msg, 29, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultHeaderLogEntryMmu;
  return proto.mon_logs.FaultHeaderLogEntryMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultHeaderLogEntryMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultId(value);
      break;
    case 2:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setEntryTimestamp(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setConfigIdInUse(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDataKeyCrc(value);
      break;
    case 5:
      var value = new basic_pb.ModelAndSerialNumber;
      reader.readMessage(value,basic_pb.ModelAndSerialNumber.deserializeBinaryFromReader);
      msg.setNumbers(value);
      break;
    case 6:
      var value = new basic_pb.MonitorAndUserIds;
      reader.readMessage(value,basic_pb.MonitorAndUserIds.deserializeBinaryFromReader);
      msg.setIds(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMeasurementLogFrequency(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMeasurementLogEntryCount(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSequenceLogFrequency(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSequenceLogEntryCount(value);
      break;
    case 11:
      var value = /** @type {!proto.mon_faults.EFaultCode} */ (reader.readEnum());
      msg.setFaultCode(value);
      break;
    case 12:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDiagnosticFault(value);
      break;
    case 13:
      var value = new mon_faults_pb.MmuSubFaultTypeValue;
      reader.readMessage(value,mon_faults_pb.MmuSubFaultTypeValue.deserializeBinaryFromReader);
      msg.setFaultSubcode(value);
      break;
    case 18:
      var value = /** @type {!proto.mon_faults.ESubFaultDiagnostic} */ (reader.readEnum());
      msg.setDiagnosticCode(value);
      break;
    case 19:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setChannelsChmap(value);
      break;
    case 20:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedIndicationsChmap(value);
      break;
    case 21:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowIndicationsChmap(value);
      break;
    case 22:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreenIndicationsChmap(value);
      break;
    case 23:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalkIndicationsChmap(value);
      break;
    case 24:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnChmap(value);
      break;
    case 25:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnChmap(value);
      break;
    case 26:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnChmap(value);
      break;
    case 27:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalkOnChmap(value);
      break;
    case 28:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setLineFrequency(value);
      break;
    case 29:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setTemperatureDegf(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultHeaderLogEntryMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultHeaderLogEntryMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultHeaderLogEntryMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFaultId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEntryTimestamp();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getConfigIdInUse();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getDataKeyCrc();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getNumbers();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      basic_pb.ModelAndSerialNumber.serializeBinaryToWriter
    );
  }
  f = message.getIds();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      basic_pb.MonitorAndUserIds.serializeBinaryToWriter
    );
  }
  f = message.getMeasurementLogFrequency();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getMeasurementLogEntryCount();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getSequenceLogFrequency();
  if (f !== 0) {
    writer.writeUint32(
      9,
      f
    );
  }
  f = message.getSequenceLogEntryCount();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getFaultCode();
  if (f !== 0.0) {
    writer.writeEnum(
      11,
      f
    );
  }
  f = message.getDiagnosticFault();
  if (f) {
    writer.writeBool(
      12,
      f
    );
  }
  f = message.getFaultSubcode();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      mon_faults_pb.MmuSubFaultTypeValue.serializeBinaryToWriter
    );
  }
  f = message.getDiagnosticCode();
  if (f !== 0.0) {
    writer.writeEnum(
      18,
      f
    );
  }
  f = message.getChannelsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      19,
      f
    );
  }
  f = message.getRedIndicationsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      20,
      f
    );
  }
  f = message.getYellowIndicationsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      21,
      f
    );
  }
  f = message.getGreenIndicationsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      22,
      f
    );
  }
  f = message.getWalkIndicationsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      23,
      f
    );
  }
  f = message.getRedsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      24,
      f
    );
  }
  f = message.getYellowsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      25,
      f
    );
  }
  f = message.getGreensOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      26,
      f
    );
  }
  f = message.getWalkOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      27,
      f
    );
  }
  f = message.getLineFrequency();
  if (f !== 0.0) {
    writer.writeFloat(
      28,
      f
    );
  }
  f = message.getTemperatureDegf();
  if (f !== 0.0) {
    writer.writeFloat(
      29,
      f
    );
  }
};


/**
 * optional uint32 fault_id = 1;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getFaultId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setFaultId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional basic.LocalDateTime entry_timestamp = 2;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getEntryTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 2));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
*/
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setEntryTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.clearEntryTimestamp = function() {
  return this.setEntryTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.hasEntryTimestamp = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional uint32 config_id_in_use = 3;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getConfigIdInUse = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setConfigIdInUse = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 data_key_crc = 4;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getDataKeyCrc = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setDataKeyCrc = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional basic.ModelAndSerialNumber numbers = 5;
 * @return {?proto.basic.ModelAndSerialNumber}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getNumbers = function() {
  return /** @type{?proto.basic.ModelAndSerialNumber} */ (
    jspb.Message.getWrapperField(this, basic_pb.ModelAndSerialNumber, 5));
};


/**
 * @param {?proto.basic.ModelAndSerialNumber|undefined} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
*/
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setNumbers = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.clearNumbers = function() {
  return this.setNumbers(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.hasNumbers = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional basic.MonitorAndUserIds ids = 6;
 * @return {?proto.basic.MonitorAndUserIds}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getIds = function() {
  return /** @type{?proto.basic.MonitorAndUserIds} */ (
    jspb.Message.getWrapperField(this, basic_pb.MonitorAndUserIds, 6));
};


/**
 * @param {?proto.basic.MonitorAndUserIds|undefined} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
*/
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setIds = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.clearIds = function() {
  return this.setIds(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.hasIds = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional uint32 measurement_log_frequency = 7;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getMeasurementLogFrequency = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setMeasurementLogFrequency = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 measurement_log_entry_count = 8;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getMeasurementLogEntryCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setMeasurementLogEntryCount = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional uint32 sequence_log_frequency = 9;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getSequenceLogFrequency = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setSequenceLogFrequency = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional uint32 sequence_log_entry_count = 10;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getSequenceLogEntryCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setSequenceLogEntryCount = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional mon_faults.EFaultCode fault_code = 11;
 * @return {!proto.mon_faults.EFaultCode}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getFaultCode = function() {
  return /** @type {!proto.mon_faults.EFaultCode} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {!proto.mon_faults.EFaultCode} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setFaultCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 11, value);
};


/**
 * optional bool diagnostic_fault = 12;
 * @return {boolean}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getDiagnosticFault = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 12, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setDiagnosticFault = function(value) {
  return jspb.Message.setProto3BooleanField(this, 12, value);
};


/**
 * optional mon_faults.MmuSubFaultTypeValue fault_subcode = 13;
 * @return {?proto.mon_faults.MmuSubFaultTypeValue}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getFaultSubcode = function() {
  return /** @type{?proto.mon_faults.MmuSubFaultTypeValue} */ (
    jspb.Message.getWrapperField(this, mon_faults_pb.MmuSubFaultTypeValue, 13));
};


/**
 * @param {?proto.mon_faults.MmuSubFaultTypeValue|undefined} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
*/
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setFaultSubcode = function(value) {
  return jspb.Message.setWrapperField(this, 13, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.clearFaultSubcode = function() {
  return this.setFaultSubcode(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.hasFaultSubcode = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional mon_faults.ESubFaultDiagnostic diagnostic_code = 18;
 * @return {!proto.mon_faults.ESubFaultDiagnostic}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getDiagnosticCode = function() {
  return /** @type {!proto.mon_faults.ESubFaultDiagnostic} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {!proto.mon_faults.ESubFaultDiagnostic} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setDiagnosticCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 18, value);
};


/**
 * optional fixed32 channels_chmap = 19;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getChannelsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 19, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setChannelsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 19, value);
};


/**
 * optional fixed32 red_indications_chmap = 20;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getRedIndicationsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 20, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setRedIndicationsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 20, value);
};


/**
 * optional fixed32 yellow_indications_chmap = 21;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getYellowIndicationsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 21, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setYellowIndicationsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 21, value);
};


/**
 * optional fixed32 green_indications_chmap = 22;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getGreenIndicationsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 22, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setGreenIndicationsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 22, value);
};


/**
 * optional fixed32 walk_indications_chmap = 23;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getWalkIndicationsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 23, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setWalkIndicationsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 23, value);
};


/**
 * optional fixed32 reds_on_chmap = 24;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getRedsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 24, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setRedsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 24, value);
};


/**
 * optional fixed32 yellows_on_chmap = 25;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getYellowsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 25, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setYellowsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 25, value);
};


/**
 * optional fixed32 greens_on_chmap = 26;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getGreensOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 26, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setGreensOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 26, value);
};


/**
 * optional fixed32 walk_on_chmap = 27;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getWalkOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 27, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setWalkOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 27, value);
};


/**
 * optional float line_frequency = 28;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getLineFrequency = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 28, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setLineFrequency = function(value) {
  return jspb.Message.setProto3FloatField(this, 28, value);
};


/**
 * optional float temperature_degf = 29;
 * @return {number}
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.getTemperatureDegf = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 29, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu} returns this
 */
proto.mon_logs.FaultHeaderLogEntryMmu.prototype.setTemperatureDegf = function(value) {
  return jspb.Message.setProto3FloatField(this, 29, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultHeaderLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.FaultHeaderLogEntryMmu.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultHeaderLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultHeaderLogMultipleEntriesMmu;
  return proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultHeaderLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultHeaderLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.FaultHeaderLogEntryMmu;
      reader.readMessage(value,proto.mon_logs.FaultHeaderLogEntryMmu.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultHeaderLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.FaultHeaderLogEntryMmu.serializeBinaryToWriter
    );
  }
};


/**
 * repeated FaultHeaderLogEntryMmu log_entry = 1;
 * @return {!Array<!proto.mon_logs.FaultHeaderLogEntryMmu>}
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.FaultHeaderLogEntryMmu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.FaultHeaderLogEntryMmu, 1));
};


/**
 * @param {!Array<!proto.mon_logs.FaultHeaderLogEntryMmu>} value
 * @return {!proto.mon_logs.FaultHeaderLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.FaultHeaderLogEntryMmu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.FaultHeaderLogEntryMmu}
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.FaultHeaderLogEntryMmu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultHeaderLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.FaultHeaderLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.repeatedFields_ = [20,21,22,23];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultMeasurementLogEntryMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultMeasurementLogEntryMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
faultId: jspb.Message.getFieldWithDefault(msg, 1, 0),
entryId: jspb.Message.getFieldWithDefault(msg, 2, 0),
mainsAcVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 3)) == null ? undefined : f,
redsOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 4, 0),
yellowsOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 5, 0),
greensOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 6, 0),
walksOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 7, 0),
redsOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 8, 0),
yellowsOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 9, 0),
greensOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 10, 0),
walksOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 11, 0),
redsOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 12, 0),
yellowsOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 13, 0),
greensOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 14, 0),
walksOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 15, 0),
redsFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 16, 0),
yellowsFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 17, 0),
greensFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 18, 0),
walksFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 19, 0),
redChannelsList: jspb.Message.toObjectList(msg.getRedChannelsList(),
    mon_faults_pb.FaultIndicationChVoltCurrent.toObject, includeInstance),
yellowChannelsList: jspb.Message.toObjectList(msg.getYellowChannelsList(),
    mon_faults_pb.FaultIndicationChVoltCurrent.toObject, includeInstance),
greenChannelsList: jspb.Message.toObjectList(msg.getGreenChannelsList(),
    mon_faults_pb.FaultIndicationChVoltCurrent.toObject, includeInstance),
walkChannelsList: jspb.Message.toObjectList(msg.getWalkChannelsList(),
    mon_faults_pb.FaultIndicationChVoltCurrent.toObject, includeInstance),
inhibit24vMonitorVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 24)) == null ? undefined : f,
monitor24v1Volts: (f = jspb.Message.getOptionalFloatingPointField(msg, 25)) == null ? undefined : f,
monitor24v2Volts: (f = jspb.Message.getOptionalFloatingPointField(msg, 26)) == null ? undefined : f,
controllerMonitorVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 27)) == null ? undefined : f,
typeSelectVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 28)) == null ? undefined : f,
localFlashVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 29)) == null ? undefined : f,
externalResetVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 30)) == null ? undefined : f,
port1DisableVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 31)) == null ? undefined : f,
externalWatchdogVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 32)) == null ? undefined : f,
redEnabledVolts: (f = jspb.Message.getOptionalFloatingPointField(msg, 33)) == null ? undefined : f,
inputStates: (f = msg.getInputStates()) && mon_faults_pb.MmuMonitoredInputsStatusBitmap.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultMeasurementLogEntryMmu;
  return proto.mon_logs.FaultMeasurementLogEntryMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultMeasurementLogEntryMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setMainsAcVolts(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnNormalChmap(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnNormalChmap(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnNormalChmap(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksOnNormalChmap(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnLackofsignalChmap(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnLackofsignalChmap(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnLackofsignalChmap(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksOnLackofsignalChmap(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnCurrentChmap(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnCurrentChmap(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnCurrentChmap(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksOnCurrentChmap(value);
      break;
    case 16:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsFieldCheckChmap(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsFieldCheckChmap(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensFieldCheckChmap(value);
      break;
    case 19:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksFieldCheckChmap(value);
      break;
    case 20:
      var value = new mon_faults_pb.FaultIndicationChVoltCurrent;
      reader.readMessage(value,mon_faults_pb.FaultIndicationChVoltCurrent.deserializeBinaryFromReader);
      msg.addRedChannels(value);
      break;
    case 21:
      var value = new mon_faults_pb.FaultIndicationChVoltCurrent;
      reader.readMessage(value,mon_faults_pb.FaultIndicationChVoltCurrent.deserializeBinaryFromReader);
      msg.addYellowChannels(value);
      break;
    case 22:
      var value = new mon_faults_pb.FaultIndicationChVoltCurrent;
      reader.readMessage(value,mon_faults_pb.FaultIndicationChVoltCurrent.deserializeBinaryFromReader);
      msg.addGreenChannels(value);
      break;
    case 23:
      var value = new mon_faults_pb.FaultIndicationChVoltCurrent;
      reader.readMessage(value,mon_faults_pb.FaultIndicationChVoltCurrent.deserializeBinaryFromReader);
      msg.addWalkChannels(value);
      break;
    case 24:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInhibit24vMonitorVolts(value);
      break;
    case 25:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setMonitor24v1Volts(value);
      break;
    case 26:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setMonitor24v2Volts(value);
      break;
    case 27:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setControllerMonitorVolts(value);
      break;
    case 28:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setTypeSelectVolts(value);
      break;
    case 29:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setLocalFlashVolts(value);
      break;
    case 30:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setExternalResetVolts(value);
      break;
    case 31:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setPort1DisableVolts(value);
      break;
    case 32:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setExternalWatchdogVolts(value);
      break;
    case 33:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setRedEnabledVolts(value);
      break;
    case 34:
      var value = new mon_faults_pb.MmuMonitoredInputsStatusBitmap;
      reader.readMessage(value,mon_faults_pb.MmuMonitoredInputsStatusBitmap.deserializeBinaryFromReader);
      msg.setInputStates(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultMeasurementLogEntryMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultMeasurementLogEntryMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFaultId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeFloat(
      3,
      f
    );
  }
  f = message.getRedsOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      4,
      f
    );
  }
  f = message.getYellowsOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      5,
      f
    );
  }
  f = message.getGreensOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      6,
      f
    );
  }
  f = message.getWalksOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      7,
      f
    );
  }
  f = message.getRedsOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      8,
      f
    );
  }
  f = message.getYellowsOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      9,
      f
    );
  }
  f = message.getGreensOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      10,
      f
    );
  }
  f = message.getWalksOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      11,
      f
    );
  }
  f = message.getRedsOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      12,
      f
    );
  }
  f = message.getYellowsOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      13,
      f
    );
  }
  f = message.getGreensOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      14,
      f
    );
  }
  f = message.getWalksOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      15,
      f
    );
  }
  f = message.getRedsFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      16,
      f
    );
  }
  f = message.getYellowsFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      17,
      f
    );
  }
  f = message.getGreensFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      18,
      f
    );
  }
  f = message.getWalksFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      19,
      f
    );
  }
  f = message.getRedChannelsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      20,
      f,
      mon_faults_pb.FaultIndicationChVoltCurrent.serializeBinaryToWriter
    );
  }
  f = message.getYellowChannelsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      21,
      f,
      mon_faults_pb.FaultIndicationChVoltCurrent.serializeBinaryToWriter
    );
  }
  f = message.getGreenChannelsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      22,
      f,
      mon_faults_pb.FaultIndicationChVoltCurrent.serializeBinaryToWriter
    );
  }
  f = message.getWalkChannelsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      23,
      f,
      mon_faults_pb.FaultIndicationChVoltCurrent.serializeBinaryToWriter
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 24));
  if (f != null) {
    writer.writeFloat(
      24,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 25));
  if (f != null) {
    writer.writeFloat(
      25,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 26));
  if (f != null) {
    writer.writeFloat(
      26,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 27));
  if (f != null) {
    writer.writeFloat(
      27,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 28));
  if (f != null) {
    writer.writeFloat(
      28,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 29));
  if (f != null) {
    writer.writeFloat(
      29,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 30));
  if (f != null) {
    writer.writeFloat(
      30,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 31));
  if (f != null) {
    writer.writeFloat(
      31,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 32));
  if (f != null) {
    writer.writeFloat(
      32,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 33));
  if (f != null) {
    writer.writeFloat(
      33,
      f
    );
  }
  f = message.getInputStates();
  if (f != null) {
    writer.writeMessage(
      34,
      f,
      mon_faults_pb.MmuMonitoredInputsStatusBitmap.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 fault_id = 1;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getFaultId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setFaultId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 entry_id = 2;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional float mains_ac_volts = 3;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getMainsAcVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setMainsAcVolts = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearMainsAcVolts = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasMainsAcVolts = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional fixed32 reds_on_normal_chmap = 4;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getRedsOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setRedsOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional fixed32 yellows_on_normal_chmap = 5;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getYellowsOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setYellowsOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional fixed32 greens_on_normal_chmap = 6;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getGreensOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setGreensOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional fixed32 walks_on_normal_chmap = 7;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getWalksOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setWalksOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional fixed32 reds_on_lackofsignal_chmap = 8;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getRedsOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setRedsOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional fixed32 yellows_on_lackofsignal_chmap = 9;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getYellowsOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setYellowsOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional fixed32 greens_on_lackofsignal_chmap = 10;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getGreensOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setGreensOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional fixed32 walks_on_lackofsignal_chmap = 11;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getWalksOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setWalksOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional fixed32 reds_on_current_chmap = 12;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getRedsOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setRedsOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional fixed32 yellows_on_current_chmap = 13;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getYellowsOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setYellowsOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional fixed32 greens_on_current_chmap = 14;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getGreensOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setGreensOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 14, value);
};


/**
 * optional fixed32 walks_on_current_chmap = 15;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getWalksOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 15, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setWalksOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 15, value);
};


/**
 * optional fixed32 reds_field_check_chmap = 16;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getRedsFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 16, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setRedsFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 16, value);
};


/**
 * optional fixed32 yellows_field_check_chmap = 17;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getYellowsFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 17, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setYellowsFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 17, value);
};


/**
 * optional fixed32 greens_field_check_chmap = 18;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getGreensFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setGreensFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * optional fixed32 walks_field_check_chmap = 19;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getWalksFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 19, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setWalksFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 19, value);
};


/**
 * repeated mon_faults.FaultIndicationChVoltCurrent red_channels = 20;
 * @return {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getRedChannelsList = function() {
  return /** @type{!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} */ (
    jspb.Message.getRepeatedWrapperField(this, mon_faults_pb.FaultIndicationChVoltCurrent, 20));
};


/**
 * @param {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
*/
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setRedChannelsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 20, value);
};


/**
 * @param {!proto.mon_faults.FaultIndicationChVoltCurrent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.addRedChannels = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 20, opt_value, proto.mon_faults.FaultIndicationChVoltCurrent, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearRedChannelsList = function() {
  return this.setRedChannelsList([]);
};


/**
 * repeated mon_faults.FaultIndicationChVoltCurrent yellow_channels = 21;
 * @return {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getYellowChannelsList = function() {
  return /** @type{!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} */ (
    jspb.Message.getRepeatedWrapperField(this, mon_faults_pb.FaultIndicationChVoltCurrent, 21));
};


/**
 * @param {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
*/
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setYellowChannelsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 21, value);
};


/**
 * @param {!proto.mon_faults.FaultIndicationChVoltCurrent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.addYellowChannels = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 21, opt_value, proto.mon_faults.FaultIndicationChVoltCurrent, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearYellowChannelsList = function() {
  return this.setYellowChannelsList([]);
};


/**
 * repeated mon_faults.FaultIndicationChVoltCurrent green_channels = 22;
 * @return {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getGreenChannelsList = function() {
  return /** @type{!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} */ (
    jspb.Message.getRepeatedWrapperField(this, mon_faults_pb.FaultIndicationChVoltCurrent, 22));
};


/**
 * @param {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
*/
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setGreenChannelsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 22, value);
};


/**
 * @param {!proto.mon_faults.FaultIndicationChVoltCurrent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.addGreenChannels = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 22, opt_value, proto.mon_faults.FaultIndicationChVoltCurrent, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearGreenChannelsList = function() {
  return this.setGreenChannelsList([]);
};


/**
 * repeated mon_faults.FaultIndicationChVoltCurrent walk_channels = 23;
 * @return {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getWalkChannelsList = function() {
  return /** @type{!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} */ (
    jspb.Message.getRepeatedWrapperField(this, mon_faults_pb.FaultIndicationChVoltCurrent, 23));
};


/**
 * @param {!Array<!proto.mon_faults.FaultIndicationChVoltCurrent>} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
*/
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setWalkChannelsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 23, value);
};


/**
 * @param {!proto.mon_faults.FaultIndicationChVoltCurrent=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.addWalkChannels = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 23, opt_value, proto.mon_faults.FaultIndicationChVoltCurrent, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearWalkChannelsList = function() {
  return this.setWalkChannelsList([]);
};


/**
 * optional float inhibit_24v_monitor_volts = 24;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getInhibit24vMonitorVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 24, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setInhibit24vMonitorVolts = function(value) {
  return jspb.Message.setField(this, 24, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearInhibit24vMonitorVolts = function() {
  return jspb.Message.setField(this, 24, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasInhibit24vMonitorVolts = function() {
  return jspb.Message.getField(this, 24) != null;
};


/**
 * optional float monitor_24v_1_volts = 25;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getMonitor24v1Volts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 25, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setMonitor24v1Volts = function(value) {
  return jspb.Message.setField(this, 25, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearMonitor24v1Volts = function() {
  return jspb.Message.setField(this, 25, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasMonitor24v1Volts = function() {
  return jspb.Message.getField(this, 25) != null;
};


/**
 * optional float monitor_24v_2_volts = 26;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getMonitor24v2Volts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 26, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setMonitor24v2Volts = function(value) {
  return jspb.Message.setField(this, 26, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearMonitor24v2Volts = function() {
  return jspb.Message.setField(this, 26, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasMonitor24v2Volts = function() {
  return jspb.Message.getField(this, 26) != null;
};


/**
 * optional float controller_monitor_volts = 27;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getControllerMonitorVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 27, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setControllerMonitorVolts = function(value) {
  return jspb.Message.setField(this, 27, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearControllerMonitorVolts = function() {
  return jspb.Message.setField(this, 27, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasControllerMonitorVolts = function() {
  return jspb.Message.getField(this, 27) != null;
};


/**
 * optional float type_select_volts = 28;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getTypeSelectVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 28, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setTypeSelectVolts = function(value) {
  return jspb.Message.setField(this, 28, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearTypeSelectVolts = function() {
  return jspb.Message.setField(this, 28, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasTypeSelectVolts = function() {
  return jspb.Message.getField(this, 28) != null;
};


/**
 * optional float local_flash_volts = 29;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getLocalFlashVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 29, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setLocalFlashVolts = function(value) {
  return jspb.Message.setField(this, 29, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearLocalFlashVolts = function() {
  return jspb.Message.setField(this, 29, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasLocalFlashVolts = function() {
  return jspb.Message.getField(this, 29) != null;
};


/**
 * optional float external_reset_volts = 30;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getExternalResetVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 30, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setExternalResetVolts = function(value) {
  return jspb.Message.setField(this, 30, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearExternalResetVolts = function() {
  return jspb.Message.setField(this, 30, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasExternalResetVolts = function() {
  return jspb.Message.getField(this, 30) != null;
};


/**
 * optional float port1_disable_volts = 31;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getPort1DisableVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 31, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setPort1DisableVolts = function(value) {
  return jspb.Message.setField(this, 31, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearPort1DisableVolts = function() {
  return jspb.Message.setField(this, 31, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasPort1DisableVolts = function() {
  return jspb.Message.getField(this, 31) != null;
};


/**
 * optional float external_watchdog_volts = 32;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getExternalWatchdogVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 32, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setExternalWatchdogVolts = function(value) {
  return jspb.Message.setField(this, 32, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearExternalWatchdogVolts = function() {
  return jspb.Message.setField(this, 32, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasExternalWatchdogVolts = function() {
  return jspb.Message.getField(this, 32) != null;
};


/**
 * optional float red_enabled_volts = 33;
 * @return {number}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getRedEnabledVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 33, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setRedEnabledVolts = function(value) {
  return jspb.Message.setField(this, 33, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearRedEnabledVolts = function() {
  return jspb.Message.setField(this, 33, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasRedEnabledVolts = function() {
  return jspb.Message.getField(this, 33) != null;
};


/**
 * optional mon_faults.MmuMonitoredInputsStatusBitmap input_states = 34;
 * @return {?proto.mon_faults.MmuMonitoredInputsStatusBitmap}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.getInputStates = function() {
  return /** @type{?proto.mon_faults.MmuMonitoredInputsStatusBitmap} */ (
    jspb.Message.getWrapperField(this, mon_faults_pb.MmuMonitoredInputsStatusBitmap, 34));
};


/**
 * @param {?proto.mon_faults.MmuMonitoredInputsStatusBitmap|undefined} value
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
*/
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.setInputStates = function(value) {
  return jspb.Message.setWrapperField(this, 34, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.clearInputStates = function() {
  return this.setInputStates(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultMeasurementLogEntryMmu.prototype.hasInputStates = function() {
  return jspb.Message.getField(this, 34) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.FaultMeasurementLogEntryMmu.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu;
  return proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.FaultMeasurementLogEntryMmu;
      reader.readMessage(value,proto.mon_logs.FaultMeasurementLogEntryMmu.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.FaultMeasurementLogEntryMmu.serializeBinaryToWriter
    );
  }
};


/**
 * repeated FaultMeasurementLogEntryMmu log_entry = 1;
 * @return {!Array<!proto.mon_logs.FaultMeasurementLogEntryMmu>}
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.FaultMeasurementLogEntryMmu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.FaultMeasurementLogEntryMmu, 1));
};


/**
 * @param {!Array<!proto.mon_logs.FaultMeasurementLogEntryMmu>} value
 * @return {!proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.FaultMeasurementLogEntryMmu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.FaultMeasurementLogEntryMmu}
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.FaultMeasurementLogEntryMmu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultSequenceLogEntryMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultSequenceLogEntryMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultSequenceLogEntryMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
faultId: jspb.Message.getFieldWithDefault(msg, 1, 0),
entryId: jspb.Message.getFieldWithDefault(msg, 2, 0),
periodCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
controlStates: (f = msg.getControlStates()) && mon_faults_pb.MmuMonitoredControlStatesBitmap.toObject(includeInstance, f),
inputStates: (f = msg.getInputStates()) && mon_faults_pb.MmuMonitoredInputsStatusBitmap.toObject(includeInstance, f),
redsOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 6, 0),
yellowsOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 7, 0),
greensOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 8, 0),
walksOnNormalChmap: jspb.Message.getFieldWithDefault(msg, 9, 0),
redsOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 10, 0),
yellowsOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 11, 0),
greensOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 12, 0),
walksOnLackofsignalChmap: jspb.Message.getFieldWithDefault(msg, 13, 0),
redsOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 14, 0),
yellowsOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 15, 0),
greensOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 16, 0),
walksOnCurrentChmap: jspb.Message.getFieldWithDefault(msg, 17, 0),
redsFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 18, 0),
yellowsFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 19, 0),
greensFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 20, 0),
walksFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 21, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultSequenceLogEntryMmu;
  return proto.mon_logs.FaultSequenceLogEntryMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultSequenceLogEntryMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPeriodCount(value);
      break;
    case 4:
      var value = new mon_faults_pb.MmuMonitoredControlStatesBitmap;
      reader.readMessage(value,mon_faults_pb.MmuMonitoredControlStatesBitmap.deserializeBinaryFromReader);
      msg.setControlStates(value);
      break;
    case 5:
      var value = new mon_faults_pb.MmuMonitoredInputsStatusBitmap;
      reader.readMessage(value,mon_faults_pb.MmuMonitoredInputsStatusBitmap.deserializeBinaryFromReader);
      msg.setInputStates(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnNormalChmap(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnNormalChmap(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnNormalChmap(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksOnNormalChmap(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnLackofsignalChmap(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnLackofsignalChmap(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnLackofsignalChmap(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksOnLackofsignalChmap(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnCurrentChmap(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnCurrentChmap(value);
      break;
    case 16:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnCurrentChmap(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksOnCurrentChmap(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsFieldCheckChmap(value);
      break;
    case 19:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsFieldCheckChmap(value);
      break;
    case 20:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensFieldCheckChmap(value);
      break;
    case 21:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksFieldCheckChmap(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultSequenceLogEntryMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultSequenceLogEntryMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultSequenceLogEntryMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFaultId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getPeriodCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getControlStates();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      mon_faults_pb.MmuMonitoredControlStatesBitmap.serializeBinaryToWriter
    );
  }
  f = message.getInputStates();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      mon_faults_pb.MmuMonitoredInputsStatusBitmap.serializeBinaryToWriter
    );
  }
  f = message.getRedsOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      6,
      f
    );
  }
  f = message.getYellowsOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      7,
      f
    );
  }
  f = message.getGreensOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      8,
      f
    );
  }
  f = message.getWalksOnNormalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      9,
      f
    );
  }
  f = message.getRedsOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      10,
      f
    );
  }
  f = message.getYellowsOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      11,
      f
    );
  }
  f = message.getGreensOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      12,
      f
    );
  }
  f = message.getWalksOnLackofsignalChmap();
  if (f !== 0) {
    writer.writeFixed32(
      13,
      f
    );
  }
  f = message.getRedsOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      14,
      f
    );
  }
  f = message.getYellowsOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      15,
      f
    );
  }
  f = message.getGreensOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      16,
      f
    );
  }
  f = message.getWalksOnCurrentChmap();
  if (f !== 0) {
    writer.writeFixed32(
      17,
      f
    );
  }
  f = message.getRedsFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      18,
      f
    );
  }
  f = message.getYellowsFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      19,
      f
    );
  }
  f = message.getGreensFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      20,
      f
    );
  }
  f = message.getWalksFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      21,
      f
    );
  }
};


/**
 * optional uint32 fault_id = 1;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getFaultId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setFaultId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 entry_id = 2;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 period_count = 3;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getPeriodCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setPeriodCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional mon_faults.MmuMonitoredControlStatesBitmap control_states = 4;
 * @return {?proto.mon_faults.MmuMonitoredControlStatesBitmap}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getControlStates = function() {
  return /** @type{?proto.mon_faults.MmuMonitoredControlStatesBitmap} */ (
    jspb.Message.getWrapperField(this, mon_faults_pb.MmuMonitoredControlStatesBitmap, 4));
};


/**
 * @param {?proto.mon_faults.MmuMonitoredControlStatesBitmap|undefined} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
*/
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setControlStates = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.clearControlStates = function() {
  return this.setControlStates(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.hasControlStates = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional mon_faults.MmuMonitoredInputsStatusBitmap input_states = 5;
 * @return {?proto.mon_faults.MmuMonitoredInputsStatusBitmap}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getInputStates = function() {
  return /** @type{?proto.mon_faults.MmuMonitoredInputsStatusBitmap} */ (
    jspb.Message.getWrapperField(this, mon_faults_pb.MmuMonitoredInputsStatusBitmap, 5));
};


/**
 * @param {?proto.mon_faults.MmuMonitoredInputsStatusBitmap|undefined} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
*/
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setInputStates = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.clearInputStates = function() {
  return this.setInputStates(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.hasInputStates = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional fixed32 reds_on_normal_chmap = 6;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getRedsOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setRedsOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional fixed32 yellows_on_normal_chmap = 7;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getYellowsOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setYellowsOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional fixed32 greens_on_normal_chmap = 8;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getGreensOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setGreensOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional fixed32 walks_on_normal_chmap = 9;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getWalksOnNormalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setWalksOnNormalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional fixed32 reds_on_lackofsignal_chmap = 10;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getRedsOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setRedsOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional fixed32 yellows_on_lackofsignal_chmap = 11;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getYellowsOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setYellowsOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional fixed32 greens_on_lackofsignal_chmap = 12;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getGreensOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setGreensOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional fixed32 walks_on_lackofsignal_chmap = 13;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getWalksOnLackofsignalChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setWalksOnLackofsignalChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 13, value);
};


/**
 * optional fixed32 reds_on_current_chmap = 14;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getRedsOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setRedsOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 14, value);
};


/**
 * optional fixed32 yellows_on_current_chmap = 15;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getYellowsOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 15, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setYellowsOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 15, value);
};


/**
 * optional fixed32 greens_on_current_chmap = 16;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getGreensOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 16, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setGreensOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 16, value);
};


/**
 * optional fixed32 walks_on_current_chmap = 17;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getWalksOnCurrentChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 17, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setWalksOnCurrentChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 17, value);
};


/**
 * optional fixed32 reds_field_check_chmap = 18;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getRedsFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setRedsFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * optional fixed32 yellows_field_check_chmap = 19;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getYellowsFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 19, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setYellowsFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 19, value);
};


/**
 * optional fixed32 greens_field_check_chmap = 20;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getGreensFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 20, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setGreensFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 20, value);
};


/**
 * optional fixed32 walks_field_check_chmap = 21;
 * @return {number}
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.getWalksFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 21, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu} returns this
 */
proto.mon_logs.FaultSequenceLogEntryMmu.prototype.setWalksFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 21, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultSequenceLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.FaultSequenceLogEntryMmu.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultSequenceLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultSequenceLogMultipleEntriesMmu;
  return proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultSequenceLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultSequenceLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.FaultSequenceLogEntryMmu;
      reader.readMessage(value,proto.mon_logs.FaultSequenceLogEntryMmu.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultSequenceLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.FaultSequenceLogEntryMmu.serializeBinaryToWriter
    );
  }
};


/**
 * repeated FaultSequenceLogEntryMmu log_entry = 1;
 * @return {!Array<!proto.mon_logs.FaultSequenceLogEntryMmu>}
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.FaultSequenceLogEntryMmu>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.FaultSequenceLogEntryMmu, 1));
};


/**
 * @param {!Array<!proto.mon_logs.FaultSequenceLogEntryMmu>} value
 * @return {!proto.mon_logs.FaultSequenceLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.FaultSequenceLogEntryMmu=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.FaultSequenceLogEntryMmu}
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.FaultSequenceLogEntryMmu, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultSequenceLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.FaultSequenceLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultFactsLogEntry.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultFactsLogEntry} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultFactsLogEntry.toObject = function(includeInstance, msg) {
  var f, obj = {
faultId: jspb.Message.getFieldWithDefault(msg, 1, 0),
entryId: jspb.Message.getFieldWithDefault(msg, 2, 0),
datetime: (f = msg.getDatetime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
samples16bCv: msg.getSamples16bCv_asB64(),
captureTimeMs: jspb.Message.getFieldWithDefault(msg, 5, 0),
captureChannel: jspb.Message.getFieldWithDefault(msg, 6, 0),
channelInput: jspb.Message.getFieldWithDefault(msg, 7, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultFactsLogEntry}
 */
proto.mon_logs.FaultFactsLogEntry.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultFactsLogEntry;
  return proto.mon_logs.FaultFactsLogEntry.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultFactsLogEntry} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultFactsLogEntry}
 */
proto.mon_logs.FaultFactsLogEntry.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultId(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 3:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setDatetime(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setSamples16bCv(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCaptureTimeMs(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCaptureChannel(value);
      break;
    case 7:
      var value = /** @type {!proto.mon_logs.ECaptureChannelInput} */ (reader.readEnum());
      msg.setChannelInput(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultFactsLogEntry.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultFactsLogEntry} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultFactsLogEntry.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFaultId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getDatetime();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getSamples16bCv_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
  f = message.getCaptureTimeMs();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getCaptureChannel();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getChannelInput();
  if (f !== 0.0) {
    writer.writeEnum(
      7,
      f
    );
  }
};


/**
 * optional uint32 fault_id = 1;
 * @return {number}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getFaultId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
 */
proto.mon_logs.FaultFactsLogEntry.prototype.setFaultId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 entry_id = 2;
 * @return {number}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
 */
proto.mon_logs.FaultFactsLogEntry.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional basic.LocalDateTime datetime = 3;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getDatetime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 3));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
*/
proto.mon_logs.FaultFactsLogEntry.prototype.setDatetime = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
 */
proto.mon_logs.FaultFactsLogEntry.prototype.clearDatetime = function() {
  return this.setDatetime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.hasDatetime = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional bytes samples_16b_cv = 4;
 * @return {string}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getSamples16bCv = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes samples_16b_cv = 4;
 * This is a type-conversion wrapper around `getSamples16bCv()`
 * @return {string}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getSamples16bCv_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getSamples16bCv()));
};


/**
 * optional bytes samples_16b_cv = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getSamples16bCv()`
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getSamples16bCv_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getSamples16bCv()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
 */
proto.mon_logs.FaultFactsLogEntry.prototype.setSamples16bCv = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};


/**
 * optional uint32 capture_time_ms = 5;
 * @return {number}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getCaptureTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
 */
proto.mon_logs.FaultFactsLogEntry.prototype.setCaptureTimeMs = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 capture_channel = 6;
 * @return {number}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getCaptureChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
 */
proto.mon_logs.FaultFactsLogEntry.prototype.setCaptureChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional ECaptureChannelInput channel_input = 7;
 * @return {!proto.mon_logs.ECaptureChannelInput}
 */
proto.mon_logs.FaultFactsLogEntry.prototype.getChannelInput = function() {
  return /** @type {!proto.mon_logs.ECaptureChannelInput} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {!proto.mon_logs.ECaptureChannelInput} value
 * @return {!proto.mon_logs.FaultFactsLogEntry} returns this
 */
proto.mon_logs.FaultFactsLogEntry.prototype.setChannelInput = function(value) {
  return jspb.Message.setProto3EnumField(this, 7, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.FaultFactsLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.FaultFactsLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.FaultFactsLogEntry.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.FaultFactsLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.FaultFactsLogMultipleEntriesMmu;
  return proto.mon_logs.FaultFactsLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.FaultFactsLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.FaultFactsLogMultipleEntriesMmu}
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.FaultFactsLogEntry;
      reader.readMessage(value,proto.mon_logs.FaultFactsLogEntry.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.FaultFactsLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.FaultFactsLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.FaultFactsLogEntry.serializeBinaryToWriter
    );
  }
};


/**
 * repeated FaultFactsLogEntry log_entry = 1;
 * @return {!Array<!proto.mon_logs.FaultFactsLogEntry>}
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.FaultFactsLogEntry>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.FaultFactsLogEntry, 1));
};


/**
 * @param {!Array<!proto.mon_logs.FaultFactsLogEntry>} value
 * @return {!proto.mon_logs.FaultFactsLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.FaultFactsLogEntry=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.FaultFactsLogEntry}
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.FaultFactsLogEntry, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.FaultFactsLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.FaultFactsLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.AlarmLogEntry.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.AlarmLogEntry.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.AlarmLogEntry} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.AlarmLogEntry.toObject = function(includeInstance, msg) {
  var f, obj = {
entryId: jspb.Message.getFieldWithDefault(msg, 1, 0),
severity: jspb.Message.getFieldWithDefault(msg, 2, 0),
source: jspb.Message.getFieldWithDefault(msg, 3, 0),
datetime: (f = msg.getDatetime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
text: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.AlarmLogEntry}
 */
proto.mon_logs.AlarmLogEntry.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.AlarmLogEntry;
  return proto.mon_logs.AlarmLogEntry.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.AlarmLogEntry} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.AlarmLogEntry}
 */
proto.mon_logs.AlarmLogEntry.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryId(value);
      break;
    case 2:
      var value = /** @type {!proto.mon_logs.EAlarmSeverity} */ (reader.readEnum());
      msg.setSeverity(value);
      break;
    case 3:
      var value = /** @type {!proto.mon_logs.EAlarmSource} */ (reader.readEnum());
      msg.setSource(value);
      break;
    case 4:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setDatetime(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setText(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.AlarmLogEntry.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.AlarmLogEntry.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.AlarmLogEntry} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.AlarmLogEntry.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEntryId();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getSeverity();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getSource();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getDatetime();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getText();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional uint32 entry_id = 1;
 * @return {number}
 */
proto.mon_logs.AlarmLogEntry.prototype.getEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.AlarmLogEntry} returns this
 */
proto.mon_logs.AlarmLogEntry.prototype.setEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional EAlarmSeverity severity = 2;
 * @return {!proto.mon_logs.EAlarmSeverity}
 */
proto.mon_logs.AlarmLogEntry.prototype.getSeverity = function() {
  return /** @type {!proto.mon_logs.EAlarmSeverity} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.mon_logs.EAlarmSeverity} value
 * @return {!proto.mon_logs.AlarmLogEntry} returns this
 */
proto.mon_logs.AlarmLogEntry.prototype.setSeverity = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional EAlarmSource source = 3;
 * @return {!proto.mon_logs.EAlarmSource}
 */
proto.mon_logs.AlarmLogEntry.prototype.getSource = function() {
  return /** @type {!proto.mon_logs.EAlarmSource} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.mon_logs.EAlarmSource} value
 * @return {!proto.mon_logs.AlarmLogEntry} returns this
 */
proto.mon_logs.AlarmLogEntry.prototype.setSource = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional basic.LocalDateTime datetime = 4;
 * @return {?proto.basic.LocalDateTime}
 */
proto.mon_logs.AlarmLogEntry.prototype.getDatetime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 4));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.mon_logs.AlarmLogEntry} returns this
*/
proto.mon_logs.AlarmLogEntry.prototype.setDatetime = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.mon_logs.AlarmLogEntry} returns this
 */
proto.mon_logs.AlarmLogEntry.prototype.clearDatetime = function() {
  return this.setDatetime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.AlarmLogEntry.prototype.hasDatetime = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string text = 5;
 * @return {string}
 */
proto.mon_logs.AlarmLogEntry.prototype.getText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.mon_logs.AlarmLogEntry} returns this
 */
proto.mon_logs.AlarmLogEntry.prototype.setText = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.AlarmLogMultipleEntriesMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.AlarmLogMultipleEntriesMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.mon_logs.AlarmLogEntry.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.AlarmLogMultipleEntriesMmu}
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.AlarmLogMultipleEntriesMmu;
  return proto.mon_logs.AlarmLogMultipleEntriesMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.AlarmLogMultipleEntriesMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.AlarmLogMultipleEntriesMmu}
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.mon_logs.AlarmLogEntry;
      reader.readMessage(value,proto.mon_logs.AlarmLogEntry.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.AlarmLogMultipleEntriesMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.AlarmLogMultipleEntriesMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.mon_logs.AlarmLogEntry.serializeBinaryToWriter
    );
  }
};


/**
 * repeated AlarmLogEntry log_entry = 1;
 * @return {!Array<!proto.mon_logs.AlarmLogEntry>}
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.mon_logs.AlarmLogEntry>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.mon_logs.AlarmLogEntry, 1));
};


/**
 * @param {!Array<!proto.mon_logs.AlarmLogEntry>} value
 * @return {!proto.mon_logs.AlarmLogMultipleEntriesMmu} returns this
*/
proto.mon_logs.AlarmLogMultipleEntriesMmu.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.AlarmLogEntry=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.AlarmLogEntry}
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.AlarmLogEntry, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.mon_logs.AlarmLogMultipleEntriesMmu} returns this
 */
proto.mon_logs.AlarmLogMultipleEntriesMmu.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.LogEntryCount.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.LogEntryCount.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.LogEntryCount} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.LogEntryCount.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0),
startEntryId: jspb.Message.getFieldWithDefault(msg, 2, 0),
entries: jspb.Message.getFieldWithDefault(msg, 3, 0),
format: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.LogEntryCount}
 */
proto.mon_logs.LogEntryCount.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.LogEntryCount;
  return proto.mon_logs.LogEntryCount.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.LogEntryCount} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.LogEntryCount}
 */
proto.mon_logs.LogEntryCount.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.mon_logs.EMonitorLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setStartEntryId(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntries(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFormat(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.LogEntryCount.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.LogEntryCount.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.LogEntryCount} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.LogEntryCount.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getStartEntryId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getEntries();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional EMonitorLogType log = 1;
 * @return {!proto.mon_logs.EMonitorLogType}
 */
proto.mon_logs.LogEntryCount.prototype.getLog = function() {
  return /** @type {!proto.mon_logs.EMonitorLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.mon_logs.EMonitorLogType} value
 * @return {!proto.mon_logs.LogEntryCount} returns this
 */
proto.mon_logs.LogEntryCount.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 start_entry_id = 2;
 * @return {number}
 */
proto.mon_logs.LogEntryCount.prototype.getStartEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.LogEntryCount} returns this
 */
proto.mon_logs.LogEntryCount.prototype.setStartEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 entries = 3;
 * @return {number}
 */
proto.mon_logs.LogEntryCount.prototype.getEntries = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.LogEntryCount} returns this
 */
proto.mon_logs.LogEntryCount.prototype.setEntries = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 format = 4;
 * @return {number}
 */
proto.mon_logs.LogEntryCount.prototype.getFormat = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_logs.LogEntryCount} returns this
 */
proto.mon_logs.LogEntryCount.prototype.setFormat = function(value) {
  return jspb.Message.setField(this, 4, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_logs.LogEntryCount} returns this
 */
proto.mon_logs.LogEntryCount.prototype.clearFormat = function() {
  return jspb.Message.setField(this, 4, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_logs.LogEntryCount.prototype.hasFormat = function() {
  return jspb.Message.getField(this, 4) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_logs.DataKeyErrorCodeBitmap.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_logs.DataKeyErrorCodeBitmap.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_logs.DataKeyErrorCodeBitmap} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.DataKeyErrorCodeBitmap.toObject = function(includeInstance, msg) {
  var f, obj = {
error1: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
error2: jspb.Message.getBooleanFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_logs.DataKeyErrorCodeBitmap}
 */
proto.mon_logs.DataKeyErrorCodeBitmap.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_logs.DataKeyErrorCodeBitmap;
  return proto.mon_logs.DataKeyErrorCodeBitmap.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_logs.DataKeyErrorCodeBitmap} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_logs.DataKeyErrorCodeBitmap}
 */
proto.mon_logs.DataKeyErrorCodeBitmap.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setError1(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setError2(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_logs.DataKeyErrorCodeBitmap.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_logs.DataKeyErrorCodeBitmap.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_logs.DataKeyErrorCodeBitmap} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_logs.DataKeyErrorCodeBitmap.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getError1();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getError2();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional bool error_1 = 1;
 * @return {boolean}
 */
proto.mon_logs.DataKeyErrorCodeBitmap.prototype.getError1 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_logs.DataKeyErrorCodeBitmap} returns this
 */
proto.mon_logs.DataKeyErrorCodeBitmap.prototype.setError1 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool error_2 = 2;
 * @return {boolean}
 */
proto.mon_logs.DataKeyErrorCodeBitmap.prototype.getError2 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_logs.DataKeyErrorCodeBitmap} returns this
 */
proto.mon_logs.DataKeyErrorCodeBitmap.prototype.setError2 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * @enum {number}
 */
proto.mon_logs.EPowerLogEventType = {
  PWR_LOG_EVENT_UNSPECIFIED: 0,
  PWR_LOG_EVENT_POWER_UP: 1,
  PWR_LOG_EVENT_POWER_DOWN: 2,
  PWR_LOG_EVENT_CONTROLLER_UP: 3,
  PWR_LOG_EVENT_CONTROLLER_DOWN: 4,
  PWR_LOG_EVENT_LOW_VOLTAGE: 5,
  PWR_LOG_EVENT_HIGH_VOLTAGE: 6,
  PWR_LOG_EVENT_POWER_INTERRUPTED: 7,
  PWR_LOG_EVENT_POWER_DOWN_TIMEOUT: 8,
  PWR_LOG_EVENT_NRESET_TIMEOUT: 9,
  PWR_LOG_EVENT_NRESET_RECOVERY: 10,
  PWR_LOG_EVENT_HDSP_TIMEOUT: 11,
  PWR_LOG_EVENT_FREQUENCY_LOW: 12,
  PWR_LOG_EVENT_FREQUENCY_HIGH: 13,
  PWR_LOG_EVENT_TIMED: 14,
  PWR_LOG_EVENT_LOW_VOLTAGE_RECOVERY: 15,
  PWR_LOG_EVENT_HIGH_VOLTAGE_RECOVERY: 16,
  PWR_LOG_EVENT_FREQUENCY_LOW_RECOVERY: 17,
  PWR_LOG_EVENT_FREQUENCY_HIGH_RECOVERY: 18,
  PWR_LOG_EVENT_TEST: 19
};

/**
 * @enum {number}
 */
proto.mon_logs.EPowerLogEventTiming = {
  PWR_LOG_PERIOD_UNSPECIFIED: 0,
  PWR_LOG_PERIOD_NO_TIMED_EVENT: 1,
  PWR_LOG_PERIOD_EVERY_1_HR: 2,
  PWR_LOG_PERIOD_EVERY_2_HRS: 3,
  PWR_LOG_PERIOD_EVERY_4_HRS: 4,
  PWR_LOG_PERIOD_EVERY_8_HRS: 5,
  PWR_LOG_PERIOD_EVERY_12_HRS: 6,
  PWR_LOG_PERIOD_EVERY_1_DAY: 7,
  PWR_LOG_PERIOD_EVERY_2_DAYS: 8,
  PWR_LOG_PERIOD_EVERY_7_DAYS: 9,
  PWR_LOG_PERIOD_EVERY_14_DAYS: 10,
  PWR_LOG_PERIOD_EVERY_1_MONTH: 11
};

/**
 * @enum {number}
 */
proto.mon_logs.EResetLogSource = {
  RST_LOG_SOURCE_UNSPECIFIED: 0,
  RST_LOG_SOURCE_FRONT_PANEL: 1,
  RST_LOG_SOURCE_EXTERNAL: 2,
  RST_LOG_SOURCE_NON_LATCHED: 3,
  RST_LOG_SOURCE_POWER_CYCLE: 4,
  RST_LOG_SOURCE_ADU: 5,
  RST_LOG_SOURCE_CONFIG_CHANGED: 6,
  RST_LOG_SOURCE_REMOTE: 7,
  RST_LOG_SOURCE_TEST: 8
};

/**
 * @enum {number}
 */
proto.mon_logs.EClockLogSource = {
  CLOCK_LOG_SOURCE_UNSPECIFIED: 0,
  CLOCK_LOG_SOURCE_CONTROLLER: 1,
  CLOCK_LOG_SOURCE_ETHERNET: 2,
  CLOCK_LOG_SOURCE_BLUETOOTH: 3,
  CLOCK_LOG_SOURCE_FRONTPANEL: 4
};

/**
 * @enum {number}
 */
proto.mon_logs.ECaptureChannelInput = {
  CH_INPUT_UNSPECIFIED: 0,
  CH_INPUT_RED_VOLTAGE: 1,
  CH_INPUT_YELLOW_VOLTAGE: 2,
  CH_INPUT_GREEN_VOLTAGE: 3,
  CH_INPUT_WALK_VOLTAGE: 4,
  CH_INPUT_RED_CURRENT: 5,
  CH_INPUT_YELLOW_CURRENT: 6,
  CH_INPUT_GREEN_CURRENT: 7,
  CH_INPUT_WALK_CURRENT: 8,
  CH_INPUT_AC_LINE_VOLTS: 9,
  CH_INPUT_24V_1_VOLTS: 10,
  CH_INPUT_24V_2_VOLTS: 11,
  CH_INPUT_CVM_VOLTS: 12,
  CH_INPUT_LINE_FREQUENCY: 13,
  CH_INPUT_RED_ENABLE_VOLTS: 14,
  CH_INPUT_EXTERNAL_WATCHDOG_VOLTS: 15,
  CH_INPUT_TYPE_SELECT_VOLTS: 16,
  CH_INPUT_SDLC_DISABLE_VOLTS: 17,
  CH_INPUT_24V_MONITOR_INHIBIT_VOLTS: 18,
  CH_INPUT_LOCAL_FLASH_VOLTS: 19,
  CH_INPUT_TEST: 254
};

/**
 * @enum {number}
 */
proto.mon_logs.EAlarmSeverity = {
  ALARM_SEVERITY_CRITICAL: 0,
  ALARM_SEVERITY_MAJOR: 1,
  ALARM_SEVERITY_MINOR: 2,
  ALARM_SEVERITY_INFORM: 3,
  ALARM_SEVERITY_TEST: 15
};

/**
 * @enum {number}
 */
proto.mon_logs.EAlarmSource = {
  ALARM_SOURCE_MONITOR_OPERATION: 0,
  ALARM_SOURCE_MONITOR_HARDWARE: 1,
  ALARM_SOURCE_OTHER_CABINET_HARDWARE: 2,
  ALARM_SOURCE_EXTERNAL_SOURCE: 3,
  ALARM_SOURCE_INTERNAL_COMMUNICATION: 4,
  ALARM_SOURCE_EXTERNAL_COMMUNICATION: 5,
  ALARM_SOURCE_TEST: 14,
  ALARM_SOURCE_UNSPECIFIED: 15
};

/**
 * @enum {number}
 */
proto.mon_logs.EMonitorLogType = {
  MON_LOG_UNSPECIFIED: 0,
  MON_LOG_ALL: 1,
  MON_LOG_POWER: 2,
  MON_LOG_RESET: 3,
  MON_LOG_CLOCK: 4,
  MON_LOG_CONFIGURATION: 5,
  MON_LOG_PORT1: 6,
  MON_LOG_FAULT_HEADER: 7,
  MON_LOG_FAULT_MEASUREMENT: 8,
  MON_LOG_FAULT_SEQUENCE: 9,
  MON_LOG_FAULT_FACTS: 10,
  MON_LOG_ALARM: 11
};

goog.object.extend(exports, proto.mon_logs);
