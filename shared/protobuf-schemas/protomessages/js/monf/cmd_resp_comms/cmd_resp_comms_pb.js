// source: cmd_resp_comms.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

goog.exportSymbol('proto.cmd_resp_comms.CmdChunkTest', null, global);
goog.exportSymbol('proto.cmd_resp_comms.EAuthenticationResult', null, global);
goog.exportSymbol('proto.cmd_resp_comms.RespAuthStatus', null, global);
goog.exportSymbol('proto.cmd_resp_comms.RespChunkTest', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_comms.RespAuthStatus = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_comms.RespAuthStatus, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_comms.RespAuthStatus.displayName = 'proto.cmd_resp_comms.RespAuthStatus';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_comms.CmdChunkTest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_comms.CmdChunkTest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_comms.CmdChunkTest.displayName = 'proto.cmd_resp_comms.CmdChunkTest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_comms.RespChunkTest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_comms.RespChunkTest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_comms.RespChunkTest.displayName = 'proto.cmd_resp_comms.RespChunkTest';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_comms.RespAuthStatus.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_comms.RespAuthStatus.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_comms.RespAuthStatus} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_comms.RespAuthStatus.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0),
existingConnectionName: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_comms.RespAuthStatus}
 */
proto.cmd_resp_comms.RespAuthStatus.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_comms.RespAuthStatus;
  return proto.cmd_resp_comms.RespAuthStatus.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_comms.RespAuthStatus} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_comms.RespAuthStatus}
 */
proto.cmd_resp_comms.RespAuthStatus.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.cmd_resp_comms.EAuthenticationResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setExistingConnectionName(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_comms.RespAuthStatus.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_comms.RespAuthStatus.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_comms.RespAuthStatus} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_comms.RespAuthStatus.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getExistingConnectionName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional EAuthenticationResult result = 1;
 * @return {!proto.cmd_resp_comms.EAuthenticationResult}
 */
proto.cmd_resp_comms.RespAuthStatus.prototype.getResult = function() {
  return /** @type {!proto.cmd_resp_comms.EAuthenticationResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.cmd_resp_comms.EAuthenticationResult} value
 * @return {!proto.cmd_resp_comms.RespAuthStatus} returns this
 */
proto.cmd_resp_comms.RespAuthStatus.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional string existing_connection_name = 2;
 * @return {string}
 */
proto.cmd_resp_comms.RespAuthStatus.prototype.getExistingConnectionName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_comms.RespAuthStatus} returns this
 */
proto.cmd_resp_comms.RespAuthStatus.prototype.setExistingConnectionName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_comms.CmdChunkTest.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_comms.CmdChunkTest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_comms.CmdChunkTest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_comms.CmdChunkTest.toObject = function(includeInstance, msg) {
  var f, obj = {
data: msg.getData_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_comms.CmdChunkTest}
 */
proto.cmd_resp_comms.CmdChunkTest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_comms.CmdChunkTest;
  return proto.cmd_resp_comms.CmdChunkTest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_comms.CmdChunkTest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_comms.CmdChunkTest}
 */
proto.cmd_resp_comms.CmdChunkTest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_comms.CmdChunkTest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_comms.CmdChunkTest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_comms.CmdChunkTest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_comms.CmdChunkTest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
};


/**
 * optional bytes data = 1;
 * @return {string}
 */
proto.cmd_resp_comms.CmdChunkTest.prototype.getData = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes data = 1;
 * This is a type-conversion wrapper around `getData()`
 * @return {string}
 */
proto.cmd_resp_comms.CmdChunkTest.prototype.getData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getData()));
};


/**
 * optional bytes data = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getData()`
 * @return {!Uint8Array}
 */
proto.cmd_resp_comms.CmdChunkTest.prototype.getData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.cmd_resp_comms.CmdChunkTest} returns this
 */
proto.cmd_resp_comms.CmdChunkTest.prototype.setData = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_comms.RespChunkTest.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_comms.RespChunkTest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_comms.RespChunkTest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_comms.RespChunkTest.toObject = function(includeInstance, msg) {
  var f, obj = {
dataEcho: msg.getDataEcho_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_comms.RespChunkTest}
 */
proto.cmd_resp_comms.RespChunkTest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_comms.RespChunkTest;
  return proto.cmd_resp_comms.RespChunkTest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_comms.RespChunkTest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_comms.RespChunkTest}
 */
proto.cmd_resp_comms.RespChunkTest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setDataEcho(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_comms.RespChunkTest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_comms.RespChunkTest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_comms.RespChunkTest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_comms.RespChunkTest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDataEcho_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      1,
      f
    );
  }
};


/**
 * optional bytes data_echo = 1;
 * @return {string}
 */
proto.cmd_resp_comms.RespChunkTest.prototype.getDataEcho = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * optional bytes data_echo = 1;
 * This is a type-conversion wrapper around `getDataEcho()`
 * @return {string}
 */
proto.cmd_resp_comms.RespChunkTest.prototype.getDataEcho_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getDataEcho()));
};


/**
 * optional bytes data_echo = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getDataEcho()`
 * @return {!Uint8Array}
 */
proto.cmd_resp_comms.RespChunkTest.prototype.getDataEcho_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getDataEcho()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.cmd_resp_comms.RespChunkTest} returns this
 */
proto.cmd_resp_comms.RespChunkTest.prototype.setDataEcho = function(value) {
  return jspb.Message.setProto3BytesField(this, 1, value);
};


/**
 * @enum {number}
 */
proto.cmd_resp_comms.EAuthenticationResult = {
  AUTH_UNSPECIFIED: 0,
  AUTH_SUCCESS: 1,
  AUTH_NO_ACCESS: 2,
  AUTH_HAS_CONNECTION: 7
};

goog.object.extend(exports, proto.cmd_resp_comms);
