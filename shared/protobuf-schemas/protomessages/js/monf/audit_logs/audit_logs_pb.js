// source: audit_logs.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
goog.exportSymbol('proto.audit_logs.AuditLogEntryComms', null, global);
goog.exportSymbol('proto.audit_logs.AuditLogEntryComms.EntryPropertyCase', null, global);
goog.exportSymbol('proto.audit_logs.AuditLogEntryCount', null, global);
goog.exportSymbol('proto.audit_logs.AuditLogMultipleEntriesComms', null, global);
goog.exportSymbol('proto.audit_logs.EAuditLogType', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.audit_logs.AuditLogEntryComms = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.audit_logs.AuditLogEntryComms.oneofGroups_);
};
goog.inherits(proto.audit_logs.AuditLogEntryComms, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.audit_logs.AuditLogEntryComms.displayName = 'proto.audit_logs.AuditLogEntryComms';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.audit_logs.AuditLogMultipleEntriesComms = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.audit_logs.AuditLogMultipleEntriesComms.repeatedFields_, null);
};
goog.inherits(proto.audit_logs.AuditLogMultipleEntriesComms, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.audit_logs.AuditLogMultipleEntriesComms.displayName = 'proto.audit_logs.AuditLogMultipleEntriesComms';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.audit_logs.AuditLogEntryCount = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.audit_logs.AuditLogEntryCount, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.audit_logs.AuditLogEntryCount.displayName = 'proto.audit_logs.AuditLogEntryCount';
}

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.audit_logs.AuditLogEntryComms.oneofGroups_ = [[3,4,5]];

/**
 * @enum {number}
 */
proto.audit_logs.AuditLogEntryComms.EntryPropertyCase = {
  ENTRY_PROPERTY_NOT_SET: 0,
  INTEGER_VAL: 3,
  FLOAT_VAL: 4,
  STRING_TEXT: 5
};

/**
 * @return {proto.audit_logs.AuditLogEntryComms.EntryPropertyCase}
 */
proto.audit_logs.AuditLogEntryComms.prototype.getEntryPropertyCase = function() {
  return /** @type {proto.audit_logs.AuditLogEntryComms.EntryPropertyCase} */(jspb.Message.computeOneofCase(this, proto.audit_logs.AuditLogEntryComms.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.audit_logs.AuditLogEntryComms.prototype.toObject = function(opt_includeInstance) {
  return proto.audit_logs.AuditLogEntryComms.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.audit_logs.AuditLogEntryComms} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.audit_logs.AuditLogEntryComms.toObject = function(includeInstance, msg) {
  var f, obj = {
entryTimestamp: (f = msg.getEntryTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
entryText: jspb.Message.getFieldWithDefault(msg, 2, ""),
integerVal: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
floatVal: (f = jspb.Message.getOptionalFloatingPointField(msg, 4)) == null ? undefined : f,
stringText: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.audit_logs.AuditLogEntryComms}
 */
proto.audit_logs.AuditLogEntryComms.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.audit_logs.AuditLogEntryComms;
  return proto.audit_logs.AuditLogEntryComms.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.audit_logs.AuditLogEntryComms} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.audit_logs.AuditLogEntryComms}
 */
proto.audit_logs.AuditLogEntryComms.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setEntryTimestamp(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setEntryText(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setIntegerVal(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setFloatVal(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setStringText(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.audit_logs.AuditLogEntryComms.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.audit_logs.AuditLogEntryComms.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.audit_logs.AuditLogEntryComms} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.audit_logs.AuditLogEntryComms.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEntryTimestamp();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getEntryText();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeFloat(
      4,
      f
    );
  }
  f = /** @type {string} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional basic.LocalDateTime entry_timestamp = 1;
 * @return {?proto.basic.LocalDateTime}
 */
proto.audit_logs.AuditLogEntryComms.prototype.getEntryTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 1));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
*/
proto.audit_logs.AuditLogEntryComms.prototype.setEntryTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.clearEntryTimestamp = function() {
  return this.setEntryTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.audit_logs.AuditLogEntryComms.prototype.hasEntryTimestamp = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string entry_text = 2;
 * @return {string}
 */
proto.audit_logs.AuditLogEntryComms.prototype.getEntryText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.setEntryText = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 integer_val = 3;
 * @return {number}
 */
proto.audit_logs.AuditLogEntryComms.prototype.getIntegerVal = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.setIntegerVal = function(value) {
  return jspb.Message.setOneofField(this, 3, proto.audit_logs.AuditLogEntryComms.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.clearIntegerVal = function() {
  return jspb.Message.setOneofField(this, 3, proto.audit_logs.AuditLogEntryComms.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.audit_logs.AuditLogEntryComms.prototype.hasIntegerVal = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional float float_val = 4;
 * @return {number}
 */
proto.audit_logs.AuditLogEntryComms.prototype.getFloatVal = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 4, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.setFloatVal = function(value) {
  return jspb.Message.setOneofField(this, 4, proto.audit_logs.AuditLogEntryComms.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.clearFloatVal = function() {
  return jspb.Message.setOneofField(this, 4, proto.audit_logs.AuditLogEntryComms.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.audit_logs.AuditLogEntryComms.prototype.hasFloatVal = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional string string_text = 5;
 * @return {string}
 */
proto.audit_logs.AuditLogEntryComms.prototype.getStringText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.setStringText = function(value) {
  return jspb.Message.setOneofField(this, 5, proto.audit_logs.AuditLogEntryComms.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.audit_logs.AuditLogEntryComms} returns this
 */
proto.audit_logs.AuditLogEntryComms.prototype.clearStringText = function() {
  return jspb.Message.setOneofField(this, 5, proto.audit_logs.AuditLogEntryComms.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.audit_logs.AuditLogEntryComms.prototype.hasStringText = function() {
  return jspb.Message.getField(this, 5) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.audit_logs.AuditLogMultipleEntriesComms.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.audit_logs.AuditLogMultipleEntriesComms.prototype.toObject = function(opt_includeInstance) {
  return proto.audit_logs.AuditLogMultipleEntriesComms.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.audit_logs.AuditLogMultipleEntriesComms} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.audit_logs.AuditLogMultipleEntriesComms.toObject = function(includeInstance, msg) {
  var f, obj = {
logEntryList: jspb.Message.toObjectList(msg.getLogEntryList(),
    proto.audit_logs.AuditLogEntryComms.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.audit_logs.AuditLogMultipleEntriesComms}
 */
proto.audit_logs.AuditLogMultipleEntriesComms.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.audit_logs.AuditLogMultipleEntriesComms;
  return proto.audit_logs.AuditLogMultipleEntriesComms.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.audit_logs.AuditLogMultipleEntriesComms} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.audit_logs.AuditLogMultipleEntriesComms}
 */
proto.audit_logs.AuditLogMultipleEntriesComms.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.audit_logs.AuditLogEntryComms;
      reader.readMessage(value,proto.audit_logs.AuditLogEntryComms.deserializeBinaryFromReader);
      msg.addLogEntry(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.audit_logs.AuditLogMultipleEntriesComms.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.audit_logs.AuditLogMultipleEntriesComms.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.audit_logs.AuditLogMultipleEntriesComms} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.audit_logs.AuditLogMultipleEntriesComms.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogEntryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.audit_logs.AuditLogEntryComms.serializeBinaryToWriter
    );
  }
};


/**
 * repeated AuditLogEntryComms log_entry = 1;
 * @return {!Array<!proto.audit_logs.AuditLogEntryComms>}
 */
proto.audit_logs.AuditLogMultipleEntriesComms.prototype.getLogEntryList = function() {
  return /** @type{!Array<!proto.audit_logs.AuditLogEntryComms>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.audit_logs.AuditLogEntryComms, 1));
};


/**
 * @param {!Array<!proto.audit_logs.AuditLogEntryComms>} value
 * @return {!proto.audit_logs.AuditLogMultipleEntriesComms} returns this
*/
proto.audit_logs.AuditLogMultipleEntriesComms.prototype.setLogEntryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.audit_logs.AuditLogEntryComms=} opt_value
 * @param {number=} opt_index
 * @return {!proto.audit_logs.AuditLogEntryComms}
 */
proto.audit_logs.AuditLogMultipleEntriesComms.prototype.addLogEntry = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.audit_logs.AuditLogEntryComms, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.audit_logs.AuditLogMultipleEntriesComms} returns this
 */
proto.audit_logs.AuditLogMultipleEntriesComms.prototype.clearLogEntryList = function() {
  return this.setLogEntryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.audit_logs.AuditLogEntryCount.prototype.toObject = function(opt_includeInstance) {
  return proto.audit_logs.AuditLogEntryCount.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.audit_logs.AuditLogEntryCount} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.audit_logs.AuditLogEntryCount.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0),
entries: jspb.Message.getFieldWithDefault(msg, 2, 0),
maxEntries: jspb.Message.getFieldWithDefault(msg, 3, 0),
entriesPerFile: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.audit_logs.AuditLogEntryCount}
 */
proto.audit_logs.AuditLogEntryCount.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.audit_logs.AuditLogEntryCount;
  return proto.audit_logs.AuditLogEntryCount.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.audit_logs.AuditLogEntryCount} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.audit_logs.AuditLogEntryCount}
 */
proto.audit_logs.AuditLogEntryCount.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.audit_logs.EAuditLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntries(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMaxEntries(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntriesPerFile(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.audit_logs.AuditLogEntryCount.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.audit_logs.AuditLogEntryCount.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.audit_logs.AuditLogEntryCount} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.audit_logs.AuditLogEntryCount.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getEntries();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getMaxEntries();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getEntriesPerFile();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional EAuditLogType log = 1;
 * @return {!proto.audit_logs.EAuditLogType}
 */
proto.audit_logs.AuditLogEntryCount.prototype.getLog = function() {
  return /** @type {!proto.audit_logs.EAuditLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @return {!proto.audit_logs.AuditLogEntryCount} returns this
 */
proto.audit_logs.AuditLogEntryCount.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 entries = 2;
 * @return {number}
 */
proto.audit_logs.AuditLogEntryCount.prototype.getEntries = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.audit_logs.AuditLogEntryCount} returns this
 */
proto.audit_logs.AuditLogEntryCount.prototype.setEntries = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 max_entries = 3;
 * @return {number}
 */
proto.audit_logs.AuditLogEntryCount.prototype.getMaxEntries = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.audit_logs.AuditLogEntryCount} returns this
 */
proto.audit_logs.AuditLogEntryCount.prototype.setMaxEntries = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 entries_per_file = 4;
 * @return {number}
 */
proto.audit_logs.AuditLogEntryCount.prototype.getEntriesPerFile = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.audit_logs.AuditLogEntryCount} returns this
 */
proto.audit_logs.AuditLogEntryCount.prototype.setEntriesPerFile = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * @enum {number}
 */
proto.audit_logs.EAuditLogType = {
  AUD_LOG_UNSPECIFIED: 0,
  AUD_LOG_ACCESS: 1,
  AUD_LOG_NETWORK: 2,
  AUD_LOG_CONFIGURATION: 3,
  AUD_LOG_BLUETOOTH: 4,
  AUD_LOG_SYSTEM: 5
};

goog.object.extend(exports, proto.audit_logs);
