// source: cmd_resp_stats.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
var mon_logs_pb = require('./mon_logs_pb.js');
goog.object.extend(proto, mon_logs_pb);
var settings_pb = require('./settings_pb.js');
goog.object.extend(proto, settings_pb);
goog.exportSymbol('proto.cmd_resp_stats.CmdClearStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdGetTimeDatesDst', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadDataKeyStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadFlashStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadInternalSupplyVoltages', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadPort1Statistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdReadWatchdogStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent', null, global);
goog.exportSymbol('proto.cmd_resp_stats.CmdSetTimeDatesDst', null, global);
goog.exportSymbol('proto.cmd_resp_stats.EDisplayButtonEvent', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespClearStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespGetTimeDatesDst', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadCommsToMainCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadDataKeyStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadFlashStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadInternalSupplyVoltages', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadMainToCommsCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadPort1Statistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespReadWatchdogStatistics', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespRemoteDisplayButtonEvent', null, global);
goog.exportSymbol('proto.cmd_resp_stats.RespSetTimeDatesDst', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadPort1Statistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadPort1Statistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadPort1Statistics.displayName = 'proto.cmd_resp_stats.CmdReadPort1Statistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadPort1Statistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadPort1Statistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadPort1Statistics.displayName = 'proto.cmd_resp_stats.RespReadPort1Statistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadDataKeyStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadDataKeyStatistics.displayName = 'proto.cmd_resp_stats.CmdReadDataKeyStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadDataKeyStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadDataKeyStatistics.displayName = 'proto.cmd_resp_stats.RespReadDataKeyStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.displayName = 'proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.displayName = 'proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.displayName = 'proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.displayName = 'proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.displayName = 'proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadMainToCommsCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.displayName = 'proto.cmd_resp_stats.RespReadMainToCommsCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.displayName = 'proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadCommsToMainCommStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.displayName = 'proto.cmd_resp_stats.RespReadCommsToMainCommStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadFlashStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadFlashStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadFlashStatistics.displayName = 'proto.cmd_resp_stats.CmdReadFlashStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadFlashStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadFlashStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadFlashStatistics.displayName = 'proto.cmd_resp_stats.RespReadFlashStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadWatchdogStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadWatchdogStatistics.displayName = 'proto.cmd_resp_stats.CmdReadWatchdogStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadWatchdogStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadWatchdogStatistics.displayName = 'proto.cmd_resp_stats.RespReadWatchdogStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdReadInternalSupplyVoltages, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.displayName = 'proto.cmd_resp_stats.CmdReadInternalSupplyVoltages';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespReadInternalSupplyVoltages, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespReadInternalSupplyVoltages.displayName = 'proto.cmd_resp_stats.RespReadInternalSupplyVoltages';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdGetTimeDatesDst, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdGetTimeDatesDst.displayName = 'proto.cmd_resp_stats.CmdGetTimeDatesDst';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespGetTimeDatesDst = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespGetTimeDatesDst, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespGetTimeDatesDst.displayName = 'proto.cmd_resp_stats.RespGetTimeDatesDst';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdClearStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdClearStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdClearStatistics.displayName = 'proto.cmd_resp_stats.CmdClearStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespClearStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespClearStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespClearStatistics.displayName = 'proto.cmd_resp_stats.RespClearStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdSetTimeDatesDst, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdSetTimeDatesDst.displayName = 'proto.cmd_resp_stats.CmdSetTimeDatesDst';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespSetTimeDatesDst = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespSetTimeDatesDst, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespSetTimeDatesDst.displayName = 'proto.cmd_resp_stats.RespSetTimeDatesDst';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.displayName = 'proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_stats.RespRemoteDisplayButtonEvent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.displayName = 'proto.cmd_resp_stats.RespRemoteDisplayButtonEvent';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadPort1Statistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadPort1Statistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadPort1Statistics}
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadPort1Statistics;
  return proto.cmd_resp_stats.CmdReadPort1Statistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadPort1Statistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadPort1Statistics}
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadPort1Statistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadPort1Statistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.CmdReadPort1Statistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadPort1Statistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadPort1Statistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadPort1Statistics.toObject = function(includeInstance, msg) {
  var f, obj = {
frame1RxCount: jspb.Message.getFieldWithDefault(msg, 1, 0),
frame3RxCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
frame9RxCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
shortFrameErrorCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
controlByteErrorCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
crcErrorCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
idleStateErrorCount: jspb.Message.getFieldWithDefault(msg, 7, 0),
framingErrorCount: jspb.Message.getFieldWithDefault(msg, 8, 0),
longFrameErrorCount: jspb.Message.getFieldWithDefault(msg, 9, 0),
frameTimeoutErrorCount: jspb.Message.getFieldWithDefault(msg, 10, 0),
unknownFrameErrorCount: jspb.Message.getFieldWithDefault(msg, 11, 0),
unknownFrameNumber: jspb.Message.getFieldWithDefault(msg, 12, 0),
lastClearTimestamp: (f = msg.getLastClearTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadPort1Statistics;
  return proto.cmd_resp_stats.RespReadPort1Statistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadPort1Statistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFrame1RxCount(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFrame3RxCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFrame9RxCount(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setShortFrameErrorCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setControlByteErrorCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCrcErrorCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIdleStateErrorCount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFramingErrorCount(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLongFrameErrorCount(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFrameTimeoutErrorCount(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUnknownFrameErrorCount(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUnknownFrameNumber(value);
      break;
    case 13:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setLastClearTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadPort1Statistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadPort1Statistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadPort1Statistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFrame1RxCount();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getFrame3RxCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getFrame9RxCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getShortFrameErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getControlByteErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getCrcErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getIdleStateErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getFramingErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getLongFrameErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      9,
      f
    );
  }
  f = message.getFrameTimeoutErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getUnknownFrameErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      11,
      f
    );
  }
  f = message.getUnknownFrameNumber();
  if (f !== 0) {
    writer.writeUint32(
      12,
      f
    );
  }
  f = message.getLastClearTimestamp();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 frame_1_rx_count = 1;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getFrame1RxCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setFrame1RxCount = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 frame_3_rx_count = 2;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getFrame3RxCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setFrame3RxCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 frame_9_rx_count = 3;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getFrame9RxCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setFrame9RxCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 short_frame_error_count = 4;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getShortFrameErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setShortFrameErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 control_byte_error_count = 5;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getControlByteErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setControlByteErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 crc_error_count = 6;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getCrcErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setCrcErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 idle_state_error_count = 7;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getIdleStateErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setIdleStateErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 framing_error_count = 8;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getFramingErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setFramingErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional uint32 long_frame_error_count = 9;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getLongFrameErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setLongFrameErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional uint32 frame_timeout_error_count = 10;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getFrameTimeoutErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setFrameTimeoutErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional uint32 unknown_frame_error_count = 11;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getUnknownFrameErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setUnknownFrameErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional uint32 unknown_frame_number = 12;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getUnknownFrameNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setUnknownFrameNumber = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};


/**
 * optional basic.LocalDateTime last_clear_timestamp = 13;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.getLastClearTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 13));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
*/
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.setLastClearTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 13, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadPort1Statistics} returns this
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.clearLastClearTimestamp = function() {
  return this.setLastClearTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadPort1Statistics.prototype.hasLastClearTimestamp = function() {
  return jspb.Message.getField(this, 13) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadDataKeyStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadDataKeyStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadDataKeyStatistics}
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadDataKeyStatistics;
  return proto.cmd_resp_stats.CmdReadDataKeyStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadDataKeyStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadDataKeyStatistics}
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadDataKeyStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadDataKeyStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.CmdReadDataKeyStatistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadDataKeyStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadDataKeyStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
source: jspb.Message.getFieldWithDefault(msg, 1, 0),
readCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
removedSeconds: jspb.Message.getFieldWithDefault(msg, 3, 0),
crcErrorCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
faultsFromBadReadsCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
errors: (f = msg.getErrors()) && mon_logs_pb.DataKeyErrorCodeBitmap.toObject(includeInstance, f),
lastClearTimestamp: (f = msg.getLastClearTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadDataKeyStatistics;
  return proto.cmd_resp_stats.RespReadDataKeyStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadDataKeyStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EConfigDataLocation} */ (reader.readEnum());
      msg.setSource(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setReadCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRemovedSeconds(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCrcErrorCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultsFromBadReadsCount(value);
      break;
    case 6:
      var value = new mon_logs_pb.DataKeyErrorCodeBitmap;
      reader.readMessage(value,mon_logs_pb.DataKeyErrorCodeBitmap.deserializeBinaryFromReader);
      msg.setErrors(value);
      break;
    case 7:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setLastClearTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadDataKeyStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadDataKeyStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSource();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getReadCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getRemovedSeconds();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getCrcErrorCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getFaultsFromBadReadsCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getErrors();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      mon_logs_pb.DataKeyErrorCodeBitmap.serializeBinaryToWriter
    );
  }
  f = message.getLastClearTimestamp();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.EConfigDataLocation source = 1;
 * @return {!proto.settings.EConfigDataLocation}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.getSource = function() {
  return /** @type {!proto.settings.EConfigDataLocation} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EConfigDataLocation} value
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.setSource = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 read_count = 2;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.getReadCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.setReadCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 removed_seconds = 3;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.getRemovedSeconds = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.setRemovedSeconds = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 crc_error_count = 4;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.getCrcErrorCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.setCrcErrorCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 faults_from_bad_reads_count = 5;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.getFaultsFromBadReadsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.setFaultsFromBadReadsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional mon_logs.DataKeyErrorCodeBitmap errors = 6;
 * @return {?proto.mon_logs.DataKeyErrorCodeBitmap}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.getErrors = function() {
  return /** @type{?proto.mon_logs.DataKeyErrorCodeBitmap} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.DataKeyErrorCodeBitmap, 6));
};


/**
 * @param {?proto.mon_logs.DataKeyErrorCodeBitmap|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
*/
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.setErrors = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.clearErrors = function() {
  return this.setErrors(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.hasErrors = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional basic.LocalDateTime last_clear_timestamp = 7;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.getLastClearTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 7));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
*/
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.setLastClearTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 7, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadDataKeyStatistics} returns this
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.clearLastClearTimestamp = function() {
  return this.setLastClearTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadDataKeyStatistics.prototype.hasLastClearTimestamp = function() {
  return jspb.Message.getField(this, 7) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics}
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics;
  return proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics}
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
sentCount: jspb.Message.getFieldWithDefault(msg, 1, 0),
receivedCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
unexpectedReceivedCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
checksumErrorsCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
dataLengthErrorsCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
timeoutErrorsCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
uartResetCount: jspb.Message.getFieldWithDefault(msg, 7, 0),
badFrameCount: jspb.Message.getFieldWithDefault(msg, 8, 0),
lastClearTimestamp: (f = msg.getLastClearTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics;
  return proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSentCount(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setReceivedCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUnexpectedReceivedCount(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChecksumErrorsCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDataLengthErrorsCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTimeoutErrorsCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUartResetCount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBadFrameCount(value);
      break;
    case 9:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setLastClearTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSentCount();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getUnexpectedReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getChecksumErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getDataLengthErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getTimeoutErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getUartResetCount();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getBadFrameCount();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getLastClearTimestamp();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 sent_count = 1;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getSentCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setSentCount = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 received_count = 2;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 unexpected_received_count = 3;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getUnexpectedReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setUnexpectedReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 checksum_errors_count = 4;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getChecksumErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setChecksumErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 data_length_errors_count = 5;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getDataLengthErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setDataLengthErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 timeout_errors_count = 6;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getTimeoutErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setTimeoutErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 uart_reset_count = 7;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getUartResetCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setUartResetCount = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 bad_frame_count = 8;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getBadFrameCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setBadFrameCount = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional basic.LocalDateTime last_clear_timestamp = 9;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.getLastClearTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 9));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
*/
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.setLastClearTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.clearLastClearTimestamp = function() {
  return this.setLastClearTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics.prototype.hasLastClearTimestamp = function() {
  return jspb.Message.getField(this, 9) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics}
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics;
  return proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics}
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.CmdReadMainToDisplayCommStatistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
sentCount: jspb.Message.getFieldWithDefault(msg, 1, 0),
receivedCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
unexpectedReceivedCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
checksumErrorsCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
dataLengthErrorsCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
timeoutErrorsCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
uartResetCount: jspb.Message.getFieldWithDefault(msg, 7, 0),
badFrameCount: jspb.Message.getFieldWithDefault(msg, 8, 0),
lastClearTimestamp: (f = msg.getLastClearTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics;
  return proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSentCount(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setReceivedCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUnexpectedReceivedCount(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChecksumErrorsCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDataLengthErrorsCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTimeoutErrorsCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUartResetCount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBadFrameCount(value);
      break;
    case 9:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setLastClearTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSentCount();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getUnexpectedReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getChecksumErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getDataLengthErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getTimeoutErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getUartResetCount();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getBadFrameCount();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getLastClearTimestamp();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 sent_count = 1;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getSentCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setSentCount = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 received_count = 2;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 unexpected_received_count = 3;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getUnexpectedReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setUnexpectedReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 checksum_errors_count = 4;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getChecksumErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setChecksumErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 data_length_errors_count = 5;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getDataLengthErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setDataLengthErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 timeout_errors_count = 6;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getTimeoutErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setTimeoutErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 uart_reset_count = 7;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getUartResetCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setUartResetCount = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 bad_frame_count = 8;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getBadFrameCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setBadFrameCount = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional basic.LocalDateTime last_clear_timestamp = 9;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.getLastClearTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 9));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
*/
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.setLastClearTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.clearLastClearTimestamp = function() {
  return this.setLastClearTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadMainToDisplayCommStatistics.prototype.hasLastClearTimestamp = function() {
  return jspb.Message.getField(this, 9) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics}
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics;
  return proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics}
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
sentCount: jspb.Message.getFieldWithDefault(msg, 1, 0),
receivedCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
unknownReceivedCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
checksumErrorsCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
dataLengthErrorsCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
pingTimeoutCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
uartResetCount: jspb.Message.getFieldWithDefault(msg, 7, 0),
badFrameCount: jspb.Message.getFieldWithDefault(msg, 8, 0),
lastClearTimestamp: (f = msg.getLastClearTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadMainToCommsCommStatistics;
  return proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSentCount(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setReceivedCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUnknownReceivedCount(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChecksumErrorsCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDataLengthErrorsCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPingTimeoutCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUartResetCount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBadFrameCount(value);
      break;
    case 9:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setLastClearTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSentCount();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getUnknownReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getChecksumErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getDataLengthErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getPingTimeoutCount();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getUartResetCount();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getBadFrameCount();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getLastClearTimestamp();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 sent_count = 1;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getSentCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setSentCount = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 received_count = 2;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 unknown_received_count = 3;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getUnknownReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setUnknownReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 checksum_errors_count = 4;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getChecksumErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setChecksumErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 data_length_errors_count = 5;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getDataLengthErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setDataLengthErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 ping_timeout_count = 6;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getPingTimeoutCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setPingTimeoutCount = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 uart_reset_count = 7;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getUartResetCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setUartResetCount = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 bad_frame_count = 8;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getBadFrameCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setBadFrameCount = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional basic.LocalDateTime last_clear_timestamp = 9;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.getLastClearTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 9));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
*/
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.setLastClearTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.clearLastClearTimestamp = function() {
  return this.setLastClearTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadMainToCommsCommStatistics.prototype.hasLastClearTimestamp = function() {
  return jspb.Message.getField(this, 9) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics}
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics;
  return proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics}
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
sentCount: jspb.Message.getFieldWithDefault(msg, 1, 0),
receivedCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
unexpectedReceivedCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
checksumErrorsCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
dataLengthErrorsCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
timeoutErrorsCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
badFrameCount: jspb.Message.getFieldWithDefault(msg, 7, 0),
lastClearTimestamp: (f = msg.getLastClearTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadCommsToMainCommStatistics;
  return proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSentCount(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setReceivedCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUnexpectedReceivedCount(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChecksumErrorsCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDataLengthErrorsCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTimeoutErrorsCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBadFrameCount(value);
      break;
    case 8:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setLastClearTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSentCount();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getUnexpectedReceivedCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getChecksumErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getDataLengthErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getTimeoutErrorsCount();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getBadFrameCount();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getLastClearTimestamp();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 sent_count = 1;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getSentCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setSentCount = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 received_count = 2;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 unexpected_received_count = 3;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getUnexpectedReceivedCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setUnexpectedReceivedCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 checksum_errors_count = 4;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getChecksumErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setChecksumErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 data_length_errors_count = 5;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getDataLengthErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setDataLengthErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 timeout_errors_count = 6;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getTimeoutErrorsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setTimeoutErrorsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 bad_frame_count = 7;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getBadFrameCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setBadFrameCount = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional basic.LocalDateTime last_clear_timestamp = 8;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.getLastClearTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 8));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
*/
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.setLastClearTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 8, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} returns this
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.clearLastClearTimestamp = function() {
  return this.setLastClearTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadCommsToMainCommStatistics.prototype.hasLastClearTimestamp = function() {
  return jspb.Message.getField(this, 8) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadFlashStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadFlashStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadFlashStatistics}
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadFlashStatistics;
  return proto.cmd_resp_stats.CmdReadFlashStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadFlashStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadFlashStatistics}
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadFlashStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadFlashStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadFlashStatistics} returns this
 */
proto.cmd_resp_stats.CmdReadFlashStatistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadFlashStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadFlashStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadFlashStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
factoryArea: (f = msg.getFactoryArea()) && settings_pb.FlashAreaStatistics.toObject(includeInstance, f),
generalArea: (f = msg.getGeneralArea()) && settings_pb.FlashAreaStatistics.toObject(includeInstance, f),
headerArea: (f = msg.getHeaderArea()) && settings_pb.FlashAreaStatistics.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadFlashStatistics;
  return proto.cmd_resp_stats.RespReadFlashStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadFlashStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.FlashAreaStatistics;
      reader.readMessage(value,settings_pb.FlashAreaStatistics.deserializeBinaryFromReader);
      msg.setFactoryArea(value);
      break;
    case 2:
      var value = new settings_pb.FlashAreaStatistics;
      reader.readMessage(value,settings_pb.FlashAreaStatistics.deserializeBinaryFromReader);
      msg.setGeneralArea(value);
      break;
    case 3:
      var value = new settings_pb.FlashAreaStatistics;
      reader.readMessage(value,settings_pb.FlashAreaStatistics.deserializeBinaryFromReader);
      msg.setHeaderArea(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadFlashStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadFlashStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadFlashStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFactoryArea();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      settings_pb.FlashAreaStatistics.serializeBinaryToWriter
    );
  }
  f = message.getGeneralArea();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      settings_pb.FlashAreaStatistics.serializeBinaryToWriter
    );
  }
  f = message.getHeaderArea();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      settings_pb.FlashAreaStatistics.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.FlashAreaStatistics factory_area = 1;
 * @return {?proto.settings.FlashAreaStatistics}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.getFactoryArea = function() {
  return /** @type{?proto.settings.FlashAreaStatistics} */ (
    jspb.Message.getWrapperField(this, settings_pb.FlashAreaStatistics, 1));
};


/**
 * @param {?proto.settings.FlashAreaStatistics|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics} returns this
*/
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.setFactoryArea = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics} returns this
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.clearFactoryArea = function() {
  return this.setFactoryArea(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.hasFactoryArea = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional settings.FlashAreaStatistics general_area = 2;
 * @return {?proto.settings.FlashAreaStatistics}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.getGeneralArea = function() {
  return /** @type{?proto.settings.FlashAreaStatistics} */ (
    jspb.Message.getWrapperField(this, settings_pb.FlashAreaStatistics, 2));
};


/**
 * @param {?proto.settings.FlashAreaStatistics|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics} returns this
*/
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.setGeneralArea = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics} returns this
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.clearGeneralArea = function() {
  return this.setGeneralArea(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.hasGeneralArea = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional settings.FlashAreaStatistics header_area = 3;
 * @return {?proto.settings.FlashAreaStatistics}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.getHeaderArea = function() {
  return /** @type{?proto.settings.FlashAreaStatistics} */ (
    jspb.Message.getWrapperField(this, settings_pb.FlashAreaStatistics, 3));
};


/**
 * @param {?proto.settings.FlashAreaStatistics|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics} returns this
*/
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.setHeaderArea = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadFlashStatistics} returns this
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.clearHeaderArea = function() {
  return this.setHeaderArea(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadFlashStatistics.prototype.hasHeaderArea = function() {
  return jspb.Message.getField(this, 3) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadWatchdogStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadWatchdogStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadWatchdogStatistics}
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadWatchdogStatistics;
  return proto.cmd_resp_stats.CmdReadWatchdogStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadWatchdogStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadWatchdogStatistics}
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadWatchdogStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadWatchdogStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.CmdReadWatchdogStatistics.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadWatchdogStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadWatchdogStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
mainLoopCount: jspb.Message.getFieldWithDefault(msg, 1, 0),
tick1msCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
faultProcessingCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
adcProcessingCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
rmsCalcuationCount: jspb.Message.getFieldWithDefault(msg, 5, 0),
commMcuRxCount: jspb.Message.getFieldWithDefault(msg, 6, 0),
isoMcuTxCount: jspb.Message.getFieldWithDefault(msg, 7, 0),
pcardDkeyReadCount: jspb.Message.getFieldWithDefault(msg, 8, 0),
getInputsCount: jspb.Message.getFieldWithDefault(msg, 9, 0),
setLedsCount: jspb.Message.getFieldWithDefault(msg, 10, 0),
displayMcuTxCount: jspb.Message.getFieldWithDefault(msg, 11, 0),
backgroundChecksumCount: jspb.Message.getFieldWithDefault(msg, 12, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadWatchdogStatistics;
  return proto.cmd_resp_stats.RespReadWatchdogStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadWatchdogStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMainLoopCount(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTick1msCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultProcessingCount(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAdcProcessingCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRmsCalcuationCount(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setCommMcuRxCount(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setIsoMcuTxCount(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPcardDkeyReadCount(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setGetInputsCount(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSetLedsCount(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDisplayMcuTxCount(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setBackgroundChecksumCount(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadWatchdogStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadWatchdogStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMainLoopCount();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getTick1msCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getFaultProcessingCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getAdcProcessingCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getRmsCalcuationCount();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getCommMcuRxCount();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getIsoMcuTxCount();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getPcardDkeyReadCount();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
  f = message.getGetInputsCount();
  if (f !== 0) {
    writer.writeUint32(
      9,
      f
    );
  }
  f = message.getSetLedsCount();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getDisplayMcuTxCount();
  if (f !== 0) {
    writer.writeUint32(
      11,
      f
    );
  }
  f = message.getBackgroundChecksumCount();
  if (f !== 0) {
    writer.writeUint32(
      12,
      f
    );
  }
};


/**
 * optional uint32 main_loop_count = 1;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getMainLoopCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setMainLoopCount = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 tick_1ms_count = 2;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getTick1msCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setTick1msCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 fault_processing_count = 3;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getFaultProcessingCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setFaultProcessingCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 adc_processing_count = 4;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getAdcProcessingCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setAdcProcessingCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 rms_calcuation_count = 5;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getRmsCalcuationCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setRmsCalcuationCount = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 comm_mcu_rx_count = 6;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getCommMcuRxCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setCommMcuRxCount = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 iso_mcu_tx_count = 7;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getIsoMcuTxCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setIsoMcuTxCount = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 pcard_dkey_read_count = 8;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getPcardDkeyReadCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setPcardDkeyReadCount = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional uint32 get_inputs_count = 9;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getGetInputsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setGetInputsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional uint32 set_leds_count = 10;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getSetLedsCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setSetLedsCount = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional uint32 display_mcu_tx_count = 11;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getDisplayMcuTxCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setDisplayMcuTxCount = function(value) {
  return jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional uint32 background_checksum_count = 12;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.getBackgroundChecksumCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 12, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadWatchdogStatistics} returns this
 */
proto.cmd_resp_stats.RespReadWatchdogStatistics.prototype.setBackgroundChecksumCount = function(value) {
  return jspb.Message.setProto3IntField(this, 12, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdReadInternalSupplyVoltages} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdReadInternalSupplyVoltages}
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdReadInternalSupplyVoltages;
  return proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdReadInternalSupplyVoltages} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdReadInternalSupplyVoltages}
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdReadInternalSupplyVoltages} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdReadInternalSupplyVoltages} returns this
 */
proto.cmd_resp_stats.CmdReadInternalSupplyVoltages.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespReadInternalSupplyVoltages.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.toObject = function(includeInstance, msg) {
  var f, obj = {
acMainsPresentVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 1, 0.0),
mainMcu5vVolts: (f = msg.getMainMcu5vVolts()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
mainMcu3v3Volts: (f = msg.getMainMcu3v3Volts()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
mainMcuNeg3v3Volts: (f = msg.getMainMcuNeg3v3Volts()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
isolatedMcu5vVolts: (f = msg.getIsolatedMcu5vVolts()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f),
isolatedMcu3v3Volts: (f = msg.getIsolatedMcu3v3Volts()) && basic_pb.NowMinMaxFloat.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespReadInternalSupplyVoltages;
  return proto.cmd_resp_stats.RespReadInternalSupplyVoltages.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setAcMainsPresentVolts(value);
      break;
    case 2:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setMainMcu5vVolts(value);
      break;
    case 3:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setMainMcu3v3Volts(value);
      break;
    case 4:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setMainMcuNeg3v3Volts(value);
      break;
    case 5:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setIsolatedMcu5vVolts(value);
      break;
    case 6:
      var value = new basic_pb.NowMinMaxFloat;
      reader.readMessage(value,basic_pb.NowMinMaxFloat.deserializeBinaryFromReader);
      msg.setIsolatedMcu3v3Volts(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespReadInternalSupplyVoltages.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAcMainsPresentVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      1,
      f
    );
  }
  f = message.getMainMcu5vVolts();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getMainMcu3v3Volts();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getMainMcuNeg3v3Volts();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getIsolatedMcu5vVolts();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
  f = message.getIsolatedMcu3v3Volts();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      basic_pb.NowMinMaxFloat.serializeBinaryToWriter
    );
  }
};


/**
 * optional float ac_mains_present_volts = 1;
 * @return {number}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.getAcMainsPresentVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 1, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.setAcMainsPresentVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 1, value);
};


/**
 * optional basic.NowMinMaxFloat main_mcu_5v_volts = 2;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.getMainMcu5vVolts = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 2));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
*/
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.setMainMcu5vVolts = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.clearMainMcu5vVolts = function() {
  return this.setMainMcu5vVolts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.hasMainMcu5vVolts = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional basic.NowMinMaxFloat main_mcu_3v3_volts = 3;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.getMainMcu3v3Volts = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 3));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
*/
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.setMainMcu3v3Volts = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.clearMainMcu3v3Volts = function() {
  return this.setMainMcu3v3Volts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.hasMainMcu3v3Volts = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional basic.NowMinMaxFloat main_mcu_neg3v3_volts = 4;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.getMainMcuNeg3v3Volts = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 4));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
*/
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.setMainMcuNeg3v3Volts = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.clearMainMcuNeg3v3Volts = function() {
  return this.setMainMcuNeg3v3Volts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.hasMainMcuNeg3v3Volts = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional basic.NowMinMaxFloat isolated_mcu_5v_volts = 5;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.getIsolatedMcu5vVolts = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 5));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
*/
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.setIsolatedMcu5vVolts = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.clearIsolatedMcu5vVolts = function() {
  return this.setIsolatedMcu5vVolts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.hasIsolatedMcu5vVolts = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional basic.NowMinMaxFloat isolated_mcu_3v3_volts = 6;
 * @return {?proto.basic.NowMinMaxFloat}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.getIsolatedMcu3v3Volts = function() {
  return /** @type{?proto.basic.NowMinMaxFloat} */ (
    jspb.Message.getWrapperField(this, basic_pb.NowMinMaxFloat, 6));
};


/**
 * @param {?proto.basic.NowMinMaxFloat|undefined} value
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
*/
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.setIsolatedMcu3v3Volts = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespReadInternalSupplyVoltages} returns this
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.clearIsolatedMcu3v3Volts = function() {
  return this.setIsolatedMcu3v3Volts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespReadInternalSupplyVoltages.prototype.hasIsolatedMcu3v3Volts = function() {
  return jspb.Message.getField(this, 6) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdGetTimeDatesDst.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdGetTimeDatesDst} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdGetTimeDatesDst}
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdGetTimeDatesDst;
  return proto.cmd_resp_stats.CmdGetTimeDatesDst.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdGetTimeDatesDst} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdGetTimeDatesDst}
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdGetTimeDatesDst.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdGetTimeDatesDst} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_stats.CmdGetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.CmdGetTimeDatesDst.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespGetTimeDatesDst.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespGetTimeDatesDst} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.toObject = function(includeInstance, msg) {
  var f, obj = {
presentDateTime: (f = msg.getPresentDateTime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
dstEnabled: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
dstSettings: (f = msg.getDstSettings()) && basic_pb.DaylightSavingsSettings.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespGetTimeDatesDst}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespGetTimeDatesDst;
  return proto.cmd_resp_stats.RespGetTimeDatesDst.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespGetTimeDatesDst} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespGetTimeDatesDst}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setPresentDateTime(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDstEnabled(value);
      break;
    case 3:
      var value = new basic_pb.DaylightSavingsSettings;
      reader.readMessage(value,basic_pb.DaylightSavingsSettings.deserializeBinaryFromReader);
      msg.setDstSettings(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespGetTimeDatesDst.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespGetTimeDatesDst} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPresentDateTime();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getDstEnabled();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getDstSettings();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.DaylightSavingsSettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional basic.LocalDateTime present_date_time = 1;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.getPresentDateTime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 1));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespGetTimeDatesDst} returns this
*/
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.setPresentDateTime = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespGetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.clearPresentDateTime = function() {
  return this.setPresentDateTime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.hasPresentDateTime = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional bool dst_enabled = 2;
 * @return {boolean}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.getDstEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_stats.RespGetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.setDstEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional basic.DaylightSavingsSettings dst_settings = 3;
 * @return {?proto.basic.DaylightSavingsSettings}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.getDstSettings = function() {
  return /** @type{?proto.basic.DaylightSavingsSettings} */ (
    jspb.Message.getWrapperField(this, basic_pb.DaylightSavingsSettings, 3));
};


/**
 * @param {?proto.basic.DaylightSavingsSettings|undefined} value
 * @return {!proto.cmd_resp_stats.RespGetTimeDatesDst} returns this
*/
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.setDstSettings = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespGetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.clearDstSettings = function() {
  return this.setDstSettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespGetTimeDatesDst.prototype.hasDstSettings = function() {
  return jspb.Message.getField(this, 3) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdClearStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdClearStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdClearStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdClearStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
statisticsToClear: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdClearStatistics}
 */
proto.cmd_resp_stats.CmdClearStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdClearStatistics;
  return proto.cmd_resp_stats.CmdClearStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdClearStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdClearStatistics}
 */
proto.cmd_resp_stats.CmdClearStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EMonitorStatistics} */ (reader.readEnum());
      msg.setStatisticsToClear(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdClearStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdClearStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdClearStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdClearStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStatisticsToClear();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EMonitorStatistics statistics_to_clear = 1;
 * @return {!proto.settings.EMonitorStatistics}
 */
proto.cmd_resp_stats.CmdClearStatistics.prototype.getStatisticsToClear = function() {
  return /** @type {!proto.settings.EMonitorStatistics} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EMonitorStatistics} value
 * @return {!proto.cmd_resp_stats.CmdClearStatistics} returns this
 */
proto.cmd_resp_stats.CmdClearStatistics.prototype.setStatisticsToClear = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespClearStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespClearStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespClearStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespClearStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
statisticsCleared: jspb.Message.getFieldWithDefault(msg, 1, 0),
timestamp: (f = msg.getTimestamp()) && basic_pb.LocalDateTime.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespClearStatistics}
 */
proto.cmd_resp_stats.RespClearStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespClearStatistics;
  return proto.cmd_resp_stats.RespClearStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespClearStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespClearStatistics}
 */
proto.cmd_resp_stats.RespClearStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EMonitorStatistics} */ (reader.readEnum());
      msg.setStatisticsCleared(value);
      break;
    case 2:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setTimestamp(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespClearStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespClearStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespClearStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespClearStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStatisticsCleared();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getTimestamp();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.EMonitorStatistics statistics_cleared = 1;
 * @return {!proto.settings.EMonitorStatistics}
 */
proto.cmd_resp_stats.RespClearStatistics.prototype.getStatisticsCleared = function() {
  return /** @type {!proto.settings.EMonitorStatistics} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EMonitorStatistics} value
 * @return {!proto.cmd_resp_stats.RespClearStatistics} returns this
 */
proto.cmd_resp_stats.RespClearStatistics.prototype.setStatisticsCleared = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional basic.LocalDateTime timestamp = 2;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.RespClearStatistics.prototype.getTimestamp = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 2));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.RespClearStatistics} returns this
*/
proto.cmd_resp_stats.RespClearStatistics.prototype.setTimestamp = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.RespClearStatistics} returns this
 */
proto.cmd_resp_stats.RespClearStatistics.prototype.clearTimestamp = function() {
  return this.setTimestamp(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.RespClearStatistics.prototype.hasTimestamp = function() {
  return jspb.Message.getField(this, 2) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdSetTimeDatesDst.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdSetTimeDatesDst} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.toObject = function(includeInstance, msg) {
  var f, obj = {
presentDateTime: (f = msg.getPresentDateTime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
dstEnabled: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
dstSettings: (f = msg.getDstSettings()) && basic_pb.DaylightSavingsSettings.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdSetTimeDatesDst}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdSetTimeDatesDst;
  return proto.cmd_resp_stats.CmdSetTimeDatesDst.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdSetTimeDatesDst} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdSetTimeDatesDst}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setPresentDateTime(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDstEnabled(value);
      break;
    case 3:
      var value = new basic_pb.DaylightSavingsSettings;
      reader.readMessage(value,basic_pb.DaylightSavingsSettings.deserializeBinaryFromReader);
      msg.setDstSettings(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdSetTimeDatesDst.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdSetTimeDatesDst} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPresentDateTime();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getDstEnabled();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getDstSettings();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.DaylightSavingsSettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional basic.LocalDateTime present_date_time = 1;
 * @return {?proto.basic.LocalDateTime}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.getPresentDateTime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 1));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.cmd_resp_stats.CmdSetTimeDatesDst} returns this
*/
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.setPresentDateTime = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.CmdSetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.clearPresentDateTime = function() {
  return this.setPresentDateTime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.hasPresentDateTime = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional bool dst_enabled = 2;
 * @return {boolean}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.getDstEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_stats.CmdSetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.setDstEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional basic.DaylightSavingsSettings dst_settings = 3;
 * @return {?proto.basic.DaylightSavingsSettings}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.getDstSettings = function() {
  return /** @type{?proto.basic.DaylightSavingsSettings} */ (
    jspb.Message.getWrapperField(this, basic_pb.DaylightSavingsSettings, 3));
};


/**
 * @param {?proto.basic.DaylightSavingsSettings|undefined} value
 * @return {!proto.cmd_resp_stats.CmdSetTimeDatesDst} returns this
*/
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.setDstSettings = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_stats.CmdSetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.clearDstSettings = function() {
  return this.setDstSettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_stats.CmdSetTimeDatesDst.prototype.hasDstSettings = function() {
  return jspb.Message.getField(this, 3) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespSetTimeDatesDst.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespSetTimeDatesDst} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespSetTimeDatesDst}
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespSetTimeDatesDst;
  return proto.cmd_resp_stats.RespSetTimeDatesDst.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespSetTimeDatesDst} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespSetTimeDatesDst}
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.ETimeSetResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespSetTimeDatesDst.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespSetTimeDatesDst} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.ETimeSetResult result = 1;
 * @return {!proto.settings.ETimeSetResult}
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.prototype.getResult = function() {
  return /** @type {!proto.settings.ETimeSetResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.ETimeSetResult} value
 * @return {!proto.cmd_resp_stats.RespSetTimeDatesDst} returns this
 */
proto.cmd_resp_stats.RespSetTimeDatesDst.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
help: jspb.Message.getFieldWithDefault(msg, 1, 0),
up: jspb.Message.getFieldWithDefault(msg, 2, 0),
down: jspb.Message.getFieldWithDefault(msg, 3, 0),
enter: jspb.Message.getFieldWithDefault(msg, 4, 0),
back: jspb.Message.getFieldWithDefault(msg, 5, 0),
left: jspb.Message.getFieldWithDefault(msg, 6, 0),
right: jspb.Message.getFieldWithDefault(msg, 7, 0),
reset: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent;
  return proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setHelp(value);
      break;
    case 2:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setUp(value);
      break;
    case 3:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setDown(value);
      break;
    case 4:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setEnter(value);
      break;
    case 5:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setBack(value);
      break;
    case 6:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setLeft(value);
      break;
    case 7:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setRight(value);
      break;
    case 8:
      var value = /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (reader.readEnum());
      msg.setReset(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getHelp();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getUp();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getDown();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getEnter();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = message.getBack();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getLeft();
  if (f !== 0.0) {
    writer.writeEnum(
      6,
      f
    );
  }
  f = message.getRight();
  if (f !== 0.0) {
    writer.writeEnum(
      7,
      f
    );
  }
  f = message.getReset();
  if (f !== 0.0) {
    writer.writeEnum(
      8,
      f
    );
  }
};


/**
 * optional EDisplayButtonEvent help = 1;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getHelp = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setHelp = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional EDisplayButtonEvent up = 2;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getUp = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setUp = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional EDisplayButtonEvent down = 3;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getDown = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setDown = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional EDisplayButtonEvent enter = 4;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getEnter = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setEnter = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};


/**
 * optional EDisplayButtonEvent back = 5;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getBack = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setBack = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional EDisplayButtonEvent left = 6;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getLeft = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setLeft = function(value) {
  return jspb.Message.setProto3EnumField(this, 6, value);
};


/**
 * optional EDisplayButtonEvent right = 7;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getRight = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setRight = function(value) {
  return jspb.Message.setProto3EnumField(this, 7, value);
};


/**
 * optional EDisplayButtonEvent reset = 8;
 * @return {!proto.cmd_resp_stats.EDisplayButtonEvent}
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.getReset = function() {
  return /** @type {!proto.cmd_resp_stats.EDisplayButtonEvent} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {!proto.cmd_resp_stats.EDisplayButtonEvent} value
 * @return {!proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent.prototype.setReset = function(value) {
  return jspb.Message.setProto3EnumField(this, 8, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_stats.RespRemoteDisplayButtonEvent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_stats.RespRemoteDisplayButtonEvent}
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_stats.RespRemoteDisplayButtonEvent;
  return proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_stats.RespRemoteDisplayButtonEvent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_stats.RespRemoteDisplayButtonEvent}
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EWriteResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_stats.RespRemoteDisplayButtonEvent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EWriteResult result = 1;
 * @return {!proto.settings.EWriteResult}
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.prototype.getResult = function() {
  return /** @type {!proto.settings.EWriteResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EWriteResult} value
 * @return {!proto.cmd_resp_stats.RespRemoteDisplayButtonEvent} returns this
 */
proto.cmd_resp_stats.RespRemoteDisplayButtonEvent.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * @enum {number}
 */
proto.cmd_resp_stats.EDisplayButtonEvent = {
  DISPLAY_BUTTON_EVENT_PRESSED: 0,
  DISPLAY_BUTTON_EVENT_IDLE: 255
};

goog.object.extend(exports, proto.cmd_resp_stats);
