// source: cmd_resp_logs.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var mon_logs_pb = require('./mon_logs_pb.js');
goog.object.extend(proto, mon_logs_pb);
var audit_logs_pb = require('./audit_logs_pb.js');
goog.object.extend(proto, audit_logs_pb);
goog.exportSymbol('proto.cmd_resp_logs.CmdRequestAuditLogClear', null, global);
goog.exportSymbol('proto.cmd_resp_logs.CmdRequestAuditLogCounts', null, global);
goog.exportSymbol('proto.cmd_resp_logs.CmdRequestAuditLogEntries', null, global);
goog.exportSymbol('proto.cmd_resp_logs.CmdRequestAuditLogReset', null, global);
goog.exportSymbol('proto.cmd_resp_logs.CmdRequestLogClear', null, global);
goog.exportSymbol('proto.cmd_resp_logs.CmdRequestLogCounts', null, global);
goog.exportSymbol('proto.cmd_resp_logs.CmdRequestLogEntries', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestAuditLogClear', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestAuditLogCounts', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestAuditLogEntries', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestAuditLogEntries.EntriesCase', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestAuditLogReset', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestLogClear', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestLogCounts', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestLogEntries', null, global);
goog.exportSymbol('proto.cmd_resp_logs.RespRequestLogEntries.EntriesCase', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.CmdRequestLogCounts = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.CmdRequestLogCounts.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.CmdRequestLogCounts, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.CmdRequestLogCounts.displayName = 'proto.cmd_resp_logs.CmdRequestLogCounts';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.RespRequestLogCounts = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.RespRequestLogCounts.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.RespRequestLogCounts, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.RespRequestLogCounts.displayName = 'proto.cmd_resp_logs.RespRequestLogCounts';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.CmdRequestLogClear = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.CmdRequestLogClear.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.CmdRequestLogClear, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.CmdRequestLogClear.displayName = 'proto.cmd_resp_logs.CmdRequestLogClear';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.RespRequestLogClear = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.RespRequestLogClear.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.RespRequestLogClear, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.RespRequestLogClear.displayName = 'proto.cmd_resp_logs.RespRequestLogClear';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.CmdRequestLogEntries = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_logs.CmdRequestLogEntries, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.CmdRequestLogEntries.displayName = 'proto.cmd_resp_logs.CmdRequestLogEntries';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.RespRequestLogEntries = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_);
};
goog.inherits(proto.cmd_resp_logs.RespRequestLogEntries, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.RespRequestLogEntries.displayName = 'proto.cmd_resp_logs.RespRequestLogEntries';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.CmdRequestAuditLogCounts.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.CmdRequestAuditLogCounts, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.CmdRequestAuditLogCounts.displayName = 'proto.cmd_resp_logs.CmdRequestAuditLogCounts';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.RespRequestAuditLogCounts.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.RespRequestAuditLogCounts, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.RespRequestAuditLogCounts.displayName = 'proto.cmd_resp_logs.RespRequestAuditLogCounts';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.CmdRequestAuditLogClear.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.CmdRequestAuditLogClear, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.CmdRequestAuditLogClear.displayName = 'proto.cmd_resp_logs.CmdRequestAuditLogClear';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.RespRequestAuditLogClear = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_logs.RespRequestAuditLogClear.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_logs.RespRequestAuditLogClear, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.RespRequestAuditLogClear.displayName = 'proto.cmd_resp_logs.RespRequestAuditLogClear';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_logs.CmdRequestAuditLogReset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.CmdRequestAuditLogReset.displayName = 'proto.cmd_resp_logs.CmdRequestAuditLogReset';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.RespRequestAuditLogReset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_logs.RespRequestAuditLogReset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.RespRequestAuditLogReset.displayName = 'proto.cmd_resp_logs.RespRequestAuditLogReset';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_logs.CmdRequestAuditLogEntries, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.CmdRequestAuditLogEntries.displayName = 'proto.cmd_resp_logs.CmdRequestAuditLogEntries';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.cmd_resp_logs.RespRequestAuditLogEntries.oneofGroups_);
};
goog.inherits(proto.cmd_resp_logs.RespRequestAuditLogEntries, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_logs.RespRequestAuditLogEntries.displayName = 'proto.cmd_resp_logs.RespRequestAuditLogEntries';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.CmdRequestLogCounts.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.CmdRequestLogCounts.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.CmdRequestLogCounts.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.CmdRequestLogCounts} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestLogCounts.toObject = function(includeInstance, msg) {
  var f, obj = {
logList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.CmdRequestLogCounts}
 */
proto.cmd_resp_logs.CmdRequestLogCounts.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.CmdRequestLogCounts;
  return proto.cmd_resp_logs.CmdRequestLogCounts.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.CmdRequestLogCounts} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.CmdRequestLogCounts}
 */
proto.cmd_resp_logs.CmdRequestLogCounts.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<!proto.mon_logs.EMonitorLogType>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addLog(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.CmdRequestLogCounts.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.CmdRequestLogCounts.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.CmdRequestLogCounts} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestLogCounts.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogList();
  if (f.length > 0) {
    writer.writePackedEnum(
      1,
      f
    );
  }
};


/**
 * repeated mon_logs.EMonitorLogType log = 1;
 * @return {!Array<!proto.mon_logs.EMonitorLogType>}
 */
proto.cmd_resp_logs.CmdRequestLogCounts.prototype.getLogList = function() {
  return /** @type {!Array<!proto.mon_logs.EMonitorLogType>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<!proto.mon_logs.EMonitorLogType>} value
 * @return {!proto.cmd_resp_logs.CmdRequestLogCounts} returns this
 */
proto.cmd_resp_logs.CmdRequestLogCounts.prototype.setLogList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {!proto.mon_logs.EMonitorLogType} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_logs.CmdRequestLogCounts} returns this
 */
proto.cmd_resp_logs.CmdRequestLogCounts.prototype.addLog = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.CmdRequestLogCounts} returns this
 */
proto.cmd_resp_logs.CmdRequestLogCounts.prototype.clearLogList = function() {
  return this.setLogList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.RespRequestLogCounts.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.RespRequestLogCounts.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.RespRequestLogCounts.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.RespRequestLogCounts} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestLogCounts.toObject = function(includeInstance, msg) {
  var f, obj = {
logList: jspb.Message.toObjectList(msg.getLogList(),
    mon_logs_pb.LogEntryCount.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.RespRequestLogCounts}
 */
proto.cmd_resp_logs.RespRequestLogCounts.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.RespRequestLogCounts;
  return proto.cmd_resp_logs.RespRequestLogCounts.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.RespRequestLogCounts} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.RespRequestLogCounts}
 */
proto.cmd_resp_logs.RespRequestLogCounts.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new mon_logs_pb.LogEntryCount;
      reader.readMessage(value,mon_logs_pb.LogEntryCount.deserializeBinaryFromReader);
      msg.addLog(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.RespRequestLogCounts.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.RespRequestLogCounts.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.RespRequestLogCounts} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestLogCounts.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      mon_logs_pb.LogEntryCount.serializeBinaryToWriter
    );
  }
};


/**
 * repeated mon_logs.LogEntryCount log = 1;
 * @return {!Array<!proto.mon_logs.LogEntryCount>}
 */
proto.cmd_resp_logs.RespRequestLogCounts.prototype.getLogList = function() {
  return /** @type{!Array<!proto.mon_logs.LogEntryCount>} */ (
    jspb.Message.getRepeatedWrapperField(this, mon_logs_pb.LogEntryCount, 1));
};


/**
 * @param {!Array<!proto.mon_logs.LogEntryCount>} value
 * @return {!proto.cmd_resp_logs.RespRequestLogCounts} returns this
*/
proto.cmd_resp_logs.RespRequestLogCounts.prototype.setLogList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.LogEntryCount=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.LogEntryCount}
 */
proto.cmd_resp_logs.RespRequestLogCounts.prototype.addLog = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.LogEntryCount, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.RespRequestLogCounts} returns this
 */
proto.cmd_resp_logs.RespRequestLogCounts.prototype.clearLogList = function() {
  return this.setLogList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.CmdRequestLogClear.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.CmdRequestLogClear.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.CmdRequestLogClear.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.CmdRequestLogClear} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestLogClear.toObject = function(includeInstance, msg) {
  var f, obj = {
logList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.CmdRequestLogClear}
 */
proto.cmd_resp_logs.CmdRequestLogClear.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.CmdRequestLogClear;
  return proto.cmd_resp_logs.CmdRequestLogClear.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.CmdRequestLogClear} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.CmdRequestLogClear}
 */
proto.cmd_resp_logs.CmdRequestLogClear.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<!proto.mon_logs.EMonitorLogType>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addLog(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.CmdRequestLogClear.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.CmdRequestLogClear.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.CmdRequestLogClear} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestLogClear.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogList();
  if (f.length > 0) {
    writer.writePackedEnum(
      1,
      f
    );
  }
};


/**
 * repeated mon_logs.EMonitorLogType log = 1;
 * @return {!Array<!proto.mon_logs.EMonitorLogType>}
 */
proto.cmd_resp_logs.CmdRequestLogClear.prototype.getLogList = function() {
  return /** @type {!Array<!proto.mon_logs.EMonitorLogType>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<!proto.mon_logs.EMonitorLogType>} value
 * @return {!proto.cmd_resp_logs.CmdRequestLogClear} returns this
 */
proto.cmd_resp_logs.CmdRequestLogClear.prototype.setLogList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {!proto.mon_logs.EMonitorLogType} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_logs.CmdRequestLogClear} returns this
 */
proto.cmd_resp_logs.CmdRequestLogClear.prototype.addLog = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.CmdRequestLogClear} returns this
 */
proto.cmd_resp_logs.CmdRequestLogClear.prototype.clearLogList = function() {
  return this.setLogList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.RespRequestLogClear.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.RespRequestLogClear.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.RespRequestLogClear.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.RespRequestLogClear} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestLogClear.toObject = function(includeInstance, msg) {
  var f, obj = {
logList: jspb.Message.toObjectList(msg.getLogList(),
    mon_logs_pb.LogEntryCount.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.RespRequestLogClear}
 */
proto.cmd_resp_logs.RespRequestLogClear.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.RespRequestLogClear;
  return proto.cmd_resp_logs.RespRequestLogClear.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.RespRequestLogClear} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.RespRequestLogClear}
 */
proto.cmd_resp_logs.RespRequestLogClear.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new mon_logs_pb.LogEntryCount;
      reader.readMessage(value,mon_logs_pb.LogEntryCount.deserializeBinaryFromReader);
      msg.addLog(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.RespRequestLogClear.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.RespRequestLogClear.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.RespRequestLogClear} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestLogClear.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      mon_logs_pb.LogEntryCount.serializeBinaryToWriter
    );
  }
};


/**
 * repeated mon_logs.LogEntryCount log = 1;
 * @return {!Array<!proto.mon_logs.LogEntryCount>}
 */
proto.cmd_resp_logs.RespRequestLogClear.prototype.getLogList = function() {
  return /** @type{!Array<!proto.mon_logs.LogEntryCount>} */ (
    jspb.Message.getRepeatedWrapperField(this, mon_logs_pb.LogEntryCount, 1));
};


/**
 * @param {!Array<!proto.mon_logs.LogEntryCount>} value
 * @return {!proto.cmd_resp_logs.RespRequestLogClear} returns this
*/
proto.cmd_resp_logs.RespRequestLogClear.prototype.setLogList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.mon_logs.LogEntryCount=} opt_value
 * @param {number=} opt_index
 * @return {!proto.mon_logs.LogEntryCount}
 */
proto.cmd_resp_logs.RespRequestLogClear.prototype.addLog = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.mon_logs.LogEntryCount, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.RespRequestLogClear} returns this
 */
proto.cmd_resp_logs.RespRequestLogClear.prototype.clearLogList = function() {
  return this.setLogList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.CmdRequestLogEntries.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.CmdRequestLogEntries} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestLogEntries.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0),
faultId: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
startingEntryId: jspb.Message.getFieldWithDefault(msg, 3, 0),
entryReturnCount: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.CmdRequestLogEntries}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.CmdRequestLogEntries;
  return proto.cmd_resp_logs.CmdRequestLogEntries.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.CmdRequestLogEntries} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.CmdRequestLogEntries}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.mon_logs.EMonitorLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setFaultId(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setStartingEntryId(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryReturnCount(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.CmdRequestLogEntries.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.CmdRequestLogEntries} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestLogEntries.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getStartingEntryId();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getEntryReturnCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional mon_logs.EMonitorLogType log = 1;
 * @return {!proto.mon_logs.EMonitorLogType}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.getLog = function() {
  return /** @type {!proto.mon_logs.EMonitorLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.mon_logs.EMonitorLogType} value
 * @return {!proto.cmd_resp_logs.CmdRequestLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 fault_id = 2;
 * @return {number}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.getFaultId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.CmdRequestLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.setFaultId = function(value) {
  return jspb.Message.setField(this, 2, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.cmd_resp_logs.CmdRequestLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.clearFaultId = function() {
  return jspb.Message.setField(this, 2, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.hasFaultId = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional uint32 starting_entry_id = 3;
 * @return {number}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.getStartingEntryId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.CmdRequestLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.setStartingEntryId = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 entry_return_count = 4;
 * @return {number}
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.getEntryReturnCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.CmdRequestLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestLogEntries.prototype.setEntryReturnCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_ = [[4,5,6,7,8,9,10,11,12,13]];

/**
 * @enum {number}
 */
proto.cmd_resp_logs.RespRequestLogEntries.EntriesCase = {
  ENTRIES_NOT_SET: 0,
  POWER_LOG: 4,
  RESET_LOG: 5,
  CLOCK_LOG: 6,
  CONFIG_LOG: 7,
  PORT1_LOG: 8,
  FAULT_HDR_LOG: 9,
  FAULT_MSR_LOG: 10,
  FAULT_SEQ_LOG: 11,
  FAULT_FACTS_LOG: 12,
  ALARM_LOG: 13
};

/**
 * @return {proto.cmd_resp_logs.RespRequestLogEntries.EntriesCase}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getEntriesCase = function() {
  return /** @type {proto.cmd_resp_logs.RespRequestLogEntries.EntriesCase} */(jspb.Message.computeOneofCase(this, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.RespRequestLogEntries.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.RespRequestLogEntries} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestLogEntries.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0),
totalMessageCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
messageSequenceCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
powerLog: (f = msg.getPowerLog()) && mon_logs_pb.PowerLogMultipleEntriesMmu.toObject(includeInstance, f),
resetLog: (f = msg.getResetLog()) && mon_logs_pb.ResetLogMultipleEntriesMmu.toObject(includeInstance, f),
clockLog: (f = msg.getClockLog()) && mon_logs_pb.ClockLogMultipleEntriesMmu.toObject(includeInstance, f),
configLog: (f = msg.getConfigLog()) && mon_logs_pb.ConfigLogMultipleEntriesMmu.toObject(includeInstance, f),
port1Log: (f = msg.getPort1Log()) && mon_logs_pb.Port1LogLogMultipleEntriesMmu.toObject(includeInstance, f),
faultHdrLog: (f = msg.getFaultHdrLog()) && mon_logs_pb.FaultHeaderLogMultipleEntriesMmu.toObject(includeInstance, f),
faultMsrLog: (f = msg.getFaultMsrLog()) && mon_logs_pb.FaultMeasurementLogMultipleEntriesMmu.toObject(includeInstance, f),
faultSeqLog: (f = msg.getFaultSeqLog()) && mon_logs_pb.FaultSequenceLogMultipleEntriesMmu.toObject(includeInstance, f),
faultFactsLog: (f = msg.getFaultFactsLog()) && mon_logs_pb.FaultFactsLogMultipleEntriesMmu.toObject(includeInstance, f),
alarmLog: (f = msg.getAlarmLog()) && mon_logs_pb.AlarmLogMultipleEntriesMmu.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries}
 */
proto.cmd_resp_logs.RespRequestLogEntries.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.RespRequestLogEntries;
  return proto.cmd_resp_logs.RespRequestLogEntries.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.RespRequestLogEntries} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries}
 */
proto.cmd_resp_logs.RespRequestLogEntries.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.mon_logs.EMonitorLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTotalMessageCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMessageSequenceCount(value);
      break;
    case 4:
      var value = new mon_logs_pb.PowerLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.PowerLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setPowerLog(value);
      break;
    case 5:
      var value = new mon_logs_pb.ResetLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.ResetLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setResetLog(value);
      break;
    case 6:
      var value = new mon_logs_pb.ClockLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.ClockLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setClockLog(value);
      break;
    case 7:
      var value = new mon_logs_pb.ConfigLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.ConfigLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setConfigLog(value);
      break;
    case 8:
      var value = new mon_logs_pb.Port1LogLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.Port1LogLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setPort1Log(value);
      break;
    case 9:
      var value = new mon_logs_pb.FaultHeaderLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.FaultHeaderLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setFaultHdrLog(value);
      break;
    case 10:
      var value = new mon_logs_pb.FaultMeasurementLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.FaultMeasurementLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setFaultMsrLog(value);
      break;
    case 11:
      var value = new mon_logs_pb.FaultSequenceLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.FaultSequenceLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setFaultSeqLog(value);
      break;
    case 12:
      var value = new mon_logs_pb.FaultFactsLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.FaultFactsLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setFaultFactsLog(value);
      break;
    case 13:
      var value = new mon_logs_pb.AlarmLogMultipleEntriesMmu;
      reader.readMessage(value,mon_logs_pb.AlarmLogMultipleEntriesMmu.deserializeBinaryFromReader);
      msg.setAlarmLog(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.RespRequestLogEntries.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.RespRequestLogEntries} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestLogEntries.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getTotalMessageCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getMessageSequenceCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getPowerLog();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      mon_logs_pb.PowerLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getResetLog();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      mon_logs_pb.ResetLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getClockLog();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      mon_logs_pb.ClockLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getConfigLog();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      mon_logs_pb.ConfigLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getPort1Log();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      mon_logs_pb.Port1LogLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getFaultHdrLog();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      mon_logs_pb.FaultHeaderLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getFaultMsrLog();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      mon_logs_pb.FaultMeasurementLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getFaultSeqLog();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      mon_logs_pb.FaultSequenceLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getFaultFactsLog();
  if (f != null) {
    writer.writeMessage(
      12,
      f,
      mon_logs_pb.FaultFactsLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
  f = message.getAlarmLog();
  if (f != null) {
    writer.writeMessage(
      13,
      f,
      mon_logs_pb.AlarmLogMultipleEntriesMmu.serializeBinaryToWriter
    );
  }
};


/**
 * optional mon_logs.EMonitorLogType log = 1;
 * @return {!proto.mon_logs.EMonitorLogType}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getLog = function() {
  return /** @type {!proto.mon_logs.EMonitorLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.mon_logs.EMonitorLogType} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 total_message_count = 2;
 * @return {number}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getTotalMessageCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setTotalMessageCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 message_sequence_count = 3;
 * @return {number}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getMessageSequenceCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setMessageSequenceCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional mon_logs.PowerLogMultipleEntriesMmu power_log = 4;
 * @return {?proto.mon_logs.PowerLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getPowerLog = function() {
  return /** @type{?proto.mon_logs.PowerLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.PowerLogMultipleEntriesMmu, 4));
};


/**
 * @param {?proto.mon_logs.PowerLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setPowerLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 4, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearPowerLog = function() {
  return this.setPowerLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasPowerLog = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional mon_logs.ResetLogMultipleEntriesMmu reset_log = 5;
 * @return {?proto.mon_logs.ResetLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getResetLog = function() {
  return /** @type{?proto.mon_logs.ResetLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.ResetLogMultipleEntriesMmu, 5));
};


/**
 * @param {?proto.mon_logs.ResetLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setResetLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 5, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearResetLog = function() {
  return this.setResetLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasResetLog = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional mon_logs.ClockLogMultipleEntriesMmu clock_log = 6;
 * @return {?proto.mon_logs.ClockLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getClockLog = function() {
  return /** @type{?proto.mon_logs.ClockLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.ClockLogMultipleEntriesMmu, 6));
};


/**
 * @param {?proto.mon_logs.ClockLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setClockLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 6, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearClockLog = function() {
  return this.setClockLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasClockLog = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional mon_logs.ConfigLogMultipleEntriesMmu config_log = 7;
 * @return {?proto.mon_logs.ConfigLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getConfigLog = function() {
  return /** @type{?proto.mon_logs.ConfigLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.ConfigLogMultipleEntriesMmu, 7));
};


/**
 * @param {?proto.mon_logs.ConfigLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setConfigLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 7, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearConfigLog = function() {
  return this.setConfigLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasConfigLog = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional mon_logs.Port1LogLogMultipleEntriesMmu port1_log = 8;
 * @return {?proto.mon_logs.Port1LogLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getPort1Log = function() {
  return /** @type{?proto.mon_logs.Port1LogLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.Port1LogLogMultipleEntriesMmu, 8));
};


/**
 * @param {?proto.mon_logs.Port1LogLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setPort1Log = function(value) {
  return jspb.Message.setOneofWrapperField(this, 8, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearPort1Log = function() {
  return this.setPort1Log(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasPort1Log = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional mon_logs.FaultHeaderLogMultipleEntriesMmu fault_hdr_log = 9;
 * @return {?proto.mon_logs.FaultHeaderLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getFaultHdrLog = function() {
  return /** @type{?proto.mon_logs.FaultHeaderLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.FaultHeaderLogMultipleEntriesMmu, 9));
};


/**
 * @param {?proto.mon_logs.FaultHeaderLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setFaultHdrLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 9, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearFaultHdrLog = function() {
  return this.setFaultHdrLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasFaultHdrLog = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional mon_logs.FaultMeasurementLogMultipleEntriesMmu fault_msr_log = 10;
 * @return {?proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getFaultMsrLog = function() {
  return /** @type{?proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.FaultMeasurementLogMultipleEntriesMmu, 10));
};


/**
 * @param {?proto.mon_logs.FaultMeasurementLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setFaultMsrLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 10, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearFaultMsrLog = function() {
  return this.setFaultMsrLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasFaultMsrLog = function() {
  return jspb.Message.getField(this, 10) != null;
};


/**
 * optional mon_logs.FaultSequenceLogMultipleEntriesMmu fault_seq_log = 11;
 * @return {?proto.mon_logs.FaultSequenceLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getFaultSeqLog = function() {
  return /** @type{?proto.mon_logs.FaultSequenceLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.FaultSequenceLogMultipleEntriesMmu, 11));
};


/**
 * @param {?proto.mon_logs.FaultSequenceLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setFaultSeqLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 11, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearFaultSeqLog = function() {
  return this.setFaultSeqLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasFaultSeqLog = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional mon_logs.FaultFactsLogMultipleEntriesMmu fault_facts_log = 12;
 * @return {?proto.mon_logs.FaultFactsLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getFaultFactsLog = function() {
  return /** @type{?proto.mon_logs.FaultFactsLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.FaultFactsLogMultipleEntriesMmu, 12));
};


/**
 * @param {?proto.mon_logs.FaultFactsLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setFaultFactsLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 12, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearFaultFactsLog = function() {
  return this.setFaultFactsLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasFaultFactsLog = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional mon_logs.AlarmLogMultipleEntriesMmu alarm_log = 13;
 * @return {?proto.mon_logs.AlarmLogMultipleEntriesMmu}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.getAlarmLog = function() {
  return /** @type{?proto.mon_logs.AlarmLogMultipleEntriesMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.AlarmLogMultipleEntriesMmu, 13));
};


/**
 * @param {?proto.mon_logs.AlarmLogMultipleEntriesMmu|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestLogEntries.prototype.setAlarmLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 13, proto.cmd_resp_logs.RespRequestLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.clearAlarmLog = function() {
  return this.setAlarmLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestLogEntries.prototype.hasAlarmLog = function() {
  return jspb.Message.getField(this, 13) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.CmdRequestAuditLogCounts.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogCounts} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.toObject = function(includeInstance, msg) {
  var f, obj = {
logList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogCounts}
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.CmdRequestAuditLogCounts;
  return proto.cmd_resp_logs.CmdRequestAuditLogCounts.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogCounts} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogCounts}
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<!proto.audit_logs.EAuditLogType>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addLog(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.CmdRequestAuditLogCounts.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogCounts} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogList();
  if (f.length > 0) {
    writer.writePackedEnum(
      1,
      f
    );
  }
};


/**
 * repeated audit_logs.EAuditLogType log = 1;
 * @return {!Array<!proto.audit_logs.EAuditLogType>}
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.prototype.getLogList = function() {
  return /** @type {!Array<!proto.audit_logs.EAuditLogType>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<!proto.audit_logs.EAuditLogType>} value
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogCounts} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.prototype.setLogList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogCounts} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.prototype.addLog = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogCounts} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogCounts.prototype.clearLogList = function() {
  return this.setLogList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.RespRequestAuditLogCounts.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogCounts} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.toObject = function(includeInstance, msg) {
  var f, obj = {
logsSpaceLimited: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
logList: jspb.Message.toObjectList(msg.getLogList(),
    audit_logs_pb.AuditLogEntryCount.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogCounts}
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.RespRequestAuditLogCounts;
  return proto.cmd_resp_logs.RespRequestAuditLogCounts.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogCounts} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogCounts}
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setLogsSpaceLimited(value);
      break;
    case 1:
      var value = new audit_logs_pb.AuditLogEntryCount;
      reader.readMessage(value,audit_logs_pb.AuditLogEntryCount.deserializeBinaryFromReader);
      msg.addLog(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.RespRequestAuditLogCounts.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogCounts} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogsSpaceLimited();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getLogList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      audit_logs_pb.AuditLogEntryCount.serializeBinaryToWriter
    );
  }
};


/**
 * optional bool logs_space_limited = 2;
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.getLogsSpaceLimited = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogCounts} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.setLogsSpaceLimited = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * repeated audit_logs.AuditLogEntryCount log = 1;
 * @return {!Array<!proto.audit_logs.AuditLogEntryCount>}
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.getLogList = function() {
  return /** @type{!Array<!proto.audit_logs.AuditLogEntryCount>} */ (
    jspb.Message.getRepeatedWrapperField(this, audit_logs_pb.AuditLogEntryCount, 1));
};


/**
 * @param {!Array<!proto.audit_logs.AuditLogEntryCount>} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogCounts} returns this
*/
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.setLogList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.audit_logs.AuditLogEntryCount=} opt_value
 * @param {number=} opt_index
 * @return {!proto.audit_logs.AuditLogEntryCount}
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.addLog = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.audit_logs.AuditLogEntryCount, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogCounts} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogCounts.prototype.clearLogList = function() {
  return this.setLogList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.CmdRequestAuditLogClear.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogClear} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.toObject = function(includeInstance, msg) {
  var f, obj = {
logList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogClear}
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.CmdRequestAuditLogClear;
  return proto.cmd_resp_logs.CmdRequestAuditLogClear.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogClear} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogClear}
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<!proto.audit_logs.EAuditLogType>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addLog(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.CmdRequestAuditLogClear.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogClear} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogList();
  if (f.length > 0) {
    writer.writePackedEnum(
      1,
      f
    );
  }
};


/**
 * repeated audit_logs.EAuditLogType log = 1;
 * @return {!Array<!proto.audit_logs.EAuditLogType>}
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.prototype.getLogList = function() {
  return /** @type {!Array<!proto.audit_logs.EAuditLogType>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<!proto.audit_logs.EAuditLogType>} value
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogClear} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.prototype.setLogList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogClear} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.prototype.addLog = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogClear} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogClear.prototype.clearLogList = function() {
  return this.setLogList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.RespRequestAuditLogClear.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogClear} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.toObject = function(includeInstance, msg) {
  var f, obj = {
logList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogClear}
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.RespRequestAuditLogClear;
  return proto.cmd_resp_logs.RespRequestAuditLogClear.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogClear} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogClear}
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<!proto.audit_logs.EAuditLogType>} */ (reader.isDelimited() ? reader.readPackedEnum() : [reader.readEnum()]);
      for (var i = 0; i < values.length; i++) {
        msg.addLog(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.RespRequestAuditLogClear.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogClear} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogList();
  if (f.length > 0) {
    writer.writePackedEnum(
      1,
      f
    );
  }
};


/**
 * repeated audit_logs.EAuditLogType log = 1;
 * @return {!Array<!proto.audit_logs.EAuditLogType>}
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.prototype.getLogList = function() {
  return /** @type {!Array<!proto.audit_logs.EAuditLogType>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<!proto.audit_logs.EAuditLogType>} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogClear} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.prototype.setLogList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogClear} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.prototype.addLog = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogClear} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogClear.prototype.clearLogList = function() {
  return this.setLogList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.CmdRequestAuditLogReset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogReset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0),
maxEntries: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogReset}
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.CmdRequestAuditLogReset;
  return proto.cmd_resp_logs.CmdRequestAuditLogReset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogReset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogReset}
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.audit_logs.EAuditLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMaxEntries(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.CmdRequestAuditLogReset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogReset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getMaxEntries();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
};


/**
 * optional audit_logs.EAuditLogType log = 1;
 * @return {!proto.audit_logs.EAuditLogType}
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.prototype.getLog = function() {
  return /** @type {!proto.audit_logs.EAuditLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogReset} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 max_entries = 2;
 * @return {number}
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.prototype.getMaxEntries = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogReset} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogReset.prototype.setMaxEntries = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.RespRequestAuditLogReset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogReset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogReset}
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.RespRequestAuditLogReset;
  return proto.cmd_resp_logs.RespRequestAuditLogReset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogReset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogReset}
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.audit_logs.EAuditLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.RespRequestAuditLogReset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogReset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional audit_logs.EAuditLogType log = 1;
 * @return {!proto.audit_logs.EAuditLogType}
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.prototype.getLog = function() {
  return /** @type {!proto.audit_logs.EAuditLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogReset} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogReset.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.CmdRequestAuditLogEntries.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogEntries} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0),
startingEntryIndex: jspb.Message.getFieldWithDefault(msg, 2, 0),
entryReturnCount: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogEntries}
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.CmdRequestAuditLogEntries;
  return proto.cmd_resp_logs.CmdRequestAuditLogEntries.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogEntries} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogEntries}
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.audit_logs.EAuditLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setStartingEntryIndex(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEntryReturnCount(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.CmdRequestAuditLogEntries.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.CmdRequestAuditLogEntries} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getStartingEntryIndex();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getEntryReturnCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
};


/**
 * optional audit_logs.EAuditLogType log = 1;
 * @return {!proto.audit_logs.EAuditLogType}
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.getLog = function() {
  return /** @type {!proto.audit_logs.EAuditLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 starting_entry_index = 2;
 * @return {number}
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.getStartingEntryIndex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.setStartingEntryIndex = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 entry_return_count = 3;
 * @return {number}
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.getEntryReturnCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.CmdRequestAuditLogEntries} returns this
 */
proto.cmd_resp_logs.CmdRequestAuditLogEntries.prototype.setEntryReturnCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.oneofGroups_ = [[4]];

/**
 * @enum {number}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.EntriesCase = {
  ENTRIES_NOT_SET: 0,
  FORMAT1: 4
};

/**
 * @return {proto.cmd_resp_logs.RespRequestAuditLogEntries.EntriesCase}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.getEntriesCase = function() {
  return /** @type {proto.cmd_resp_logs.RespRequestAuditLogEntries.EntriesCase} */(jspb.Message.computeOneofCase(this, proto.cmd_resp_logs.RespRequestAuditLogEntries.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_logs.RespRequestAuditLogEntries.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogEntries} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.toObject = function(includeInstance, msg) {
  var f, obj = {
log: jspb.Message.getFieldWithDefault(msg, 1, 0),
totalMessageCount: jspb.Message.getFieldWithDefault(msg, 2, 0),
messageSequenceCount: jspb.Message.getFieldWithDefault(msg, 3, 0),
format1: (f = msg.getFormat1()) && audit_logs_pb.AuditLogMultipleEntriesComms.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogEntries}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_logs.RespRequestAuditLogEntries;
  return proto.cmd_resp_logs.RespRequestAuditLogEntries.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogEntries} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogEntries}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.audit_logs.EAuditLogType} */ (reader.readEnum());
      msg.setLog(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTotalMessageCount(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMessageSequenceCount(value);
      break;
    case 4:
      var value = new audit_logs_pb.AuditLogMultipleEntriesComms;
      reader.readMessage(value,audit_logs_pb.AuditLogMultipleEntriesComms.deserializeBinaryFromReader);
      msg.setFormat1(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_logs.RespRequestAuditLogEntries.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_logs.RespRequestAuditLogEntries} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLog();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getTotalMessageCount();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getMessageSequenceCount();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getFormat1();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      audit_logs_pb.AuditLogMultipleEntriesComms.serializeBinaryToWriter
    );
  }
};


/**
 * optional audit_logs.EAuditLogType log = 1;
 * @return {!proto.audit_logs.EAuditLogType}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.getLog = function() {
  return /** @type {!proto.audit_logs.EAuditLogType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.audit_logs.EAuditLogType} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.setLog = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 total_message_count = 2;
 * @return {number}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.getTotalMessageCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.setTotalMessageCount = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 message_sequence_count = 3;
 * @return {number}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.getMessageSequenceCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.setMessageSequenceCount = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional audit_logs.AuditLogMultipleEntriesComms format1 = 4;
 * @return {?proto.audit_logs.AuditLogMultipleEntriesComms}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.getFormat1 = function() {
  return /** @type{?proto.audit_logs.AuditLogMultipleEntriesComms} */ (
    jspb.Message.getWrapperField(this, audit_logs_pb.AuditLogMultipleEntriesComms, 4));
};


/**
 * @param {?proto.audit_logs.AuditLogMultipleEntriesComms|undefined} value
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogEntries} returns this
*/
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.setFormat1 = function(value) {
  return jspb.Message.setOneofWrapperField(this, 4, proto.cmd_resp_logs.RespRequestAuditLogEntries.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_logs.RespRequestAuditLogEntries} returns this
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.clearFormat1 = function() {
  return this.setFormat1(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_logs.RespRequestAuditLogEntries.prototype.hasFormat1 = function() {
  return jspb.Message.getField(this, 4) != null;
};


goog.object.extend(exports, proto.cmd_resp_logs);
