// source: cmd_resp_realtime.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var realtime_pb = require('./realtime_pb.js');
goog.object.extend(proto, realtime_pb);
goog.exportSymbol('proto.cmd_resp_realtime.CmdStartRealtimeData', null, global);
goog.exportSymbol('proto.cmd_resp_realtime.CmdStopRealtimeData', null, global);
goog.exportSymbol('proto.cmd_resp_realtime.RealtimeData1', null, global);
goog.exportSymbol('proto.cmd_resp_realtime.RealtimeDisplay1', null, global);
goog.exportSymbol('proto.cmd_resp_realtime.RespStartRealtimeData', null, global);
goog.exportSymbol('proto.cmd_resp_realtime.RespStopRealtimeData', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_realtime.CmdStartRealtimeData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_realtime.CmdStartRealtimeData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_realtime.CmdStartRealtimeData.displayName = 'proto.cmd_resp_realtime.CmdStartRealtimeData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_realtime.RespStartRealtimeData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_realtime.RespStartRealtimeData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_realtime.RespStartRealtimeData.displayName = 'proto.cmd_resp_realtime.RespStartRealtimeData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_realtime.CmdStopRealtimeData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_realtime.CmdStopRealtimeData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_realtime.CmdStopRealtimeData.displayName = 'proto.cmd_resp_realtime.CmdStopRealtimeData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_realtime.RespStopRealtimeData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_realtime.RespStopRealtimeData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_realtime.RespStopRealtimeData.displayName = 'proto.cmd_resp_realtime.RespStopRealtimeData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_realtime.RealtimeData1 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_realtime.RealtimeData1, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_realtime.RealtimeData1.displayName = 'proto.cmd_resp_realtime.RealtimeData1';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_realtime.RealtimeDisplay1 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_realtime.RealtimeDisplay1, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_realtime.RealtimeDisplay1.displayName = 'proto.cmd_resp_realtime.RealtimeDisplay1';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_realtime.CmdStartRealtimeData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_realtime.CmdStartRealtimeData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.toObject = function(includeInstance, msg) {
  var f, obj = {
sendChannelStatusChmap: jspb.Message.getFieldWithDefault(msg, 1, 0),
sendMonitorFaultAndStatus: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
sendMonitorDisplay: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
monitorDisplayChangedOnly: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
sendIntervalMs: jspb.Message.getFieldWithDefault(msg, 5, 0),
updateFullDisplayDataSeconds: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_realtime.CmdStartRealtimeData;
  return proto.cmd_resp_realtime.CmdStartRealtimeData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_realtime.CmdStartRealtimeData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setSendChannelStatusChmap(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSendMonitorFaultAndStatus(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setSendMonitorDisplay(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMonitorDisplayChangedOnly(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSendIntervalMs(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setUpdateFullDisplayDataSeconds(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_realtime.CmdStartRealtimeData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_realtime.CmdStartRealtimeData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSendChannelStatusChmap();
  if (f !== 0) {
    writer.writeFixed32(
      1,
      f
    );
  }
  f = message.getSendMonitorFaultAndStatus();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getSendMonitorDisplay();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getMonitorDisplayChangedOnly();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getSendIntervalMs();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getUpdateFullDisplayDataSeconds();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
};


/**
 * optional fixed32 send_channel_status_chmap = 1;
 * @return {number}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.getSendChannelStatusChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData} returns this
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.setSendChannelStatusChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bool send_monitor_fault_and_status = 2;
 * @return {boolean}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.getSendMonitorFaultAndStatus = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData} returns this
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.setSendMonitorFaultAndStatus = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional bool send_monitor_display = 3;
 * @return {boolean}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.getSendMonitorDisplay = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData} returns this
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.setSendMonitorDisplay = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional bool monitor_display_changed_only = 4;
 * @return {boolean}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.getMonitorDisplayChangedOnly = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData} returns this
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.setMonitorDisplayChangedOnly = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional uint32 send_interval_ms = 5;
 * @return {number}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.getSendIntervalMs = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData} returns this
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.setSendIntervalMs = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 update_full_display_data_seconds = 6;
 * @return {number}
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.getUpdateFullDisplayDataSeconds = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.CmdStartRealtimeData} returns this
 */
proto.cmd_resp_realtime.CmdStartRealtimeData.prototype.setUpdateFullDisplayDataSeconds = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_realtime.RespStartRealtimeData.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_realtime.RespStartRealtimeData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_realtime.RespStartRealtimeData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RespStartRealtimeData.toObject = function(includeInstance, msg) {
  var f, obj = {
sendIntervalMs: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_realtime.RespStartRealtimeData}
 */
proto.cmd_resp_realtime.RespStartRealtimeData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_realtime.RespStartRealtimeData;
  return proto.cmd_resp_realtime.RespStartRealtimeData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_realtime.RespStartRealtimeData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_realtime.RespStartRealtimeData}
 */
proto.cmd_resp_realtime.RespStartRealtimeData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSendIntervalMs(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_realtime.RespStartRealtimeData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_realtime.RespStartRealtimeData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_realtime.RespStartRealtimeData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RespStartRealtimeData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSendIntervalMs();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 send_interval_ms = 1;
 * @return {number}
 */
proto.cmd_resp_realtime.RespStartRealtimeData.prototype.getSendIntervalMs = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.RespStartRealtimeData} returns this
 */
proto.cmd_resp_realtime.RespStartRealtimeData.prototype.setSendIntervalMs = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_realtime.CmdStopRealtimeData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_realtime.CmdStopRealtimeData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_realtime.CmdStopRealtimeData}
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_realtime.CmdStopRealtimeData;
  return proto.cmd_resp_realtime.CmdStopRealtimeData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_realtime.CmdStopRealtimeData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_realtime.CmdStopRealtimeData}
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_realtime.CmdStopRealtimeData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_realtime.CmdStopRealtimeData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.CmdStopRealtimeData} returns this
 */
proto.cmd_resp_realtime.CmdStopRealtimeData.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_realtime.RespStopRealtimeData.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_realtime.RespStopRealtimeData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_realtime.RespStopRealtimeData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RespStopRealtimeData.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_realtime.RespStopRealtimeData}
 */
proto.cmd_resp_realtime.RespStopRealtimeData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_realtime.RespStopRealtimeData;
  return proto.cmd_resp_realtime.RespStopRealtimeData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_realtime.RespStopRealtimeData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_realtime.RespStopRealtimeData}
 */
proto.cmd_resp_realtime.RespStopRealtimeData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_realtime.RespStopRealtimeData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_realtime.RespStopRealtimeData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_realtime.RespStopRealtimeData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RespStopRealtimeData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_realtime.RespStopRealtimeData.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.RespStopRealtimeData} returns this
 */
proto.cmd_resp_realtime.RespStopRealtimeData.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_realtime.RealtimeData1.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_realtime.RealtimeData1} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RealtimeData1.toObject = function(includeInstance, msg) {
  var f, obj = {
sequenceNumber: jspb.Message.getFieldWithDefault(msg, 1, 0),
channelStatus: (f = msg.getChannelStatus()) && realtime_pb.ChannelStatusData.toObject(includeInstance, f),
monitorFaultAndStatus: (f = msg.getMonitorFaultAndStatus()) && realtime_pb.MonitorPresentStatus.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_realtime.RealtimeData1}
 */
proto.cmd_resp_realtime.RealtimeData1.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_realtime.RealtimeData1;
  return proto.cmd_resp_realtime.RealtimeData1.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_realtime.RealtimeData1} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_realtime.RealtimeData1}
 */
proto.cmd_resp_realtime.RealtimeData1.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSequenceNumber(value);
      break;
    case 2:
      var value = new realtime_pb.ChannelStatusData;
      reader.readMessage(value,realtime_pb.ChannelStatusData.deserializeBinaryFromReader);
      msg.setChannelStatus(value);
      break;
    case 3:
      var value = new realtime_pb.MonitorPresentStatus;
      reader.readMessage(value,realtime_pb.MonitorPresentStatus.deserializeBinaryFromReader);
      msg.setMonitorFaultAndStatus(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_realtime.RealtimeData1.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_realtime.RealtimeData1} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RealtimeData1.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSequenceNumber();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getChannelStatus();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      realtime_pb.ChannelStatusData.serializeBinaryToWriter
    );
  }
  f = message.getMonitorFaultAndStatus();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      realtime_pb.MonitorPresentStatus.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 sequence_number = 1;
 * @return {number}
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.getSequenceNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.RealtimeData1} returns this
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.setSequenceNumber = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional realtime.ChannelStatusData channel_status = 2;
 * @return {?proto.realtime.ChannelStatusData}
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.getChannelStatus = function() {
  return /** @type{?proto.realtime.ChannelStatusData} */ (
    jspb.Message.getWrapperField(this, realtime_pb.ChannelStatusData, 2));
};


/**
 * @param {?proto.realtime.ChannelStatusData|undefined} value
 * @return {!proto.cmd_resp_realtime.RealtimeData1} returns this
*/
proto.cmd_resp_realtime.RealtimeData1.prototype.setChannelStatus = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_realtime.RealtimeData1} returns this
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.clearChannelStatus = function() {
  return this.setChannelStatus(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.hasChannelStatus = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional realtime.MonitorPresentStatus monitor_fault_and_status = 3;
 * @return {?proto.realtime.MonitorPresentStatus}
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.getMonitorFaultAndStatus = function() {
  return /** @type{?proto.realtime.MonitorPresentStatus} */ (
    jspb.Message.getWrapperField(this, realtime_pb.MonitorPresentStatus, 3));
};


/**
 * @param {?proto.realtime.MonitorPresentStatus|undefined} value
 * @return {!proto.cmd_resp_realtime.RealtimeData1} returns this
*/
proto.cmd_resp_realtime.RealtimeData1.prototype.setMonitorFaultAndStatus = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_realtime.RealtimeData1} returns this
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.clearMonitorFaultAndStatus = function() {
  return this.setMonitorFaultAndStatus(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_realtime.RealtimeData1.prototype.hasMonitorFaultAndStatus = function() {
  return jspb.Message.getField(this, 3) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_realtime.RealtimeDisplay1.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_realtime.RealtimeDisplay1} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RealtimeDisplay1.toObject = function(includeInstance, msg) {
  var f, obj = {
sequenceNumber: jspb.Message.getFieldWithDefault(msg, 1, 0),
monitorDisplay: (f = msg.getMonitorDisplay()) && realtime_pb.MonitorDisplayData.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_realtime.RealtimeDisplay1}
 */
proto.cmd_resp_realtime.RealtimeDisplay1.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_realtime.RealtimeDisplay1;
  return proto.cmd_resp_realtime.RealtimeDisplay1.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_realtime.RealtimeDisplay1} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_realtime.RealtimeDisplay1}
 */
proto.cmd_resp_realtime.RealtimeDisplay1.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSequenceNumber(value);
      break;
    case 2:
      var value = new realtime_pb.MonitorDisplayData;
      reader.readMessage(value,realtime_pb.MonitorDisplayData.deserializeBinaryFromReader);
      msg.setMonitorDisplay(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_realtime.RealtimeDisplay1.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_realtime.RealtimeDisplay1} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_realtime.RealtimeDisplay1.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSequenceNumber();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getMonitorDisplay();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      realtime_pb.MonitorDisplayData.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 sequence_number = 1;
 * @return {number}
 */
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.getSequenceNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_realtime.RealtimeDisplay1} returns this
 */
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.setSequenceNumber = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional realtime.MonitorDisplayData monitor_display = 2;
 * @return {?proto.realtime.MonitorDisplayData}
 */
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.getMonitorDisplay = function() {
  return /** @type{?proto.realtime.MonitorDisplayData} */ (
    jspb.Message.getWrapperField(this, realtime_pb.MonitorDisplayData, 2));
};


/**
 * @param {?proto.realtime.MonitorDisplayData|undefined} value
 * @return {!proto.cmd_resp_realtime.RealtimeDisplay1} returns this
*/
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.setMonitorDisplay = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_realtime.RealtimeDisplay1} returns this
 */
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.clearMonitorDisplay = function() {
  return this.setMonitorDisplay(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_realtime.RealtimeDisplay1.prototype.hasMonitorDisplay = function() {
  return jspb.Message.getField(this, 2) != null;
};


goog.object.extend(exports, proto.cmd_resp_realtime);
