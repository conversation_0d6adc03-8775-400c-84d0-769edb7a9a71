// source: discovery.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
goog.exportSymbol('proto.discovery.DiscHelloApp', null, global);
goog.exportSymbol('proto.discovery.DiscHelloDevice', null, global);
goog.exportSymbol('proto.discovery.EDiscoveryDeviceType', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.discovery.DiscHelloDevice = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.discovery.DiscHelloDevice, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.discovery.DiscHelloDevice.displayName = 'proto.discovery.DiscHelloDevice';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.discovery.DiscHelloApp = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.discovery.DiscHelloApp, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.discovery.DiscHelloApp.displayName = 'proto.discovery.DiscHelloApp';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.discovery.DiscHelloDevice.prototype.toObject = function(opt_includeInstance) {
  return proto.discovery.DiscHelloDevice.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.discovery.DiscHelloDevice} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.discovery.DiscHelloDevice.toObject = function(includeInstance, msg) {
  var f, obj = {
version: jspb.Message.getFieldWithDefault(msg, 1, 0),
pcAppIp4Address: (f = msg.getPcAppIp4Address()) && basic_pb.IpAddressV4.toObject(includeInstance, f),
pcAppPort: jspb.Message.getFieldWithDefault(msg, 3, 0),
deviceType: jspb.Message.getFieldWithDefault(msg, 5, 0),
groupNumber: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.discovery.DiscHelloDevice}
 */
proto.discovery.DiscHelloDevice.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.discovery.DiscHelloDevice;
  return proto.discovery.DiscHelloDevice.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.discovery.DiscHelloDevice} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.discovery.DiscHelloDevice}
 */
proto.discovery.DiscHelloDevice.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 2:
      var value = new basic_pb.IpAddressV4;
      reader.readMessage(value,basic_pb.IpAddressV4.deserializeBinaryFromReader);
      msg.setPcAppIp4Address(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setPcAppPort(value);
      break;
    case 5:
      var value = /** @type {!proto.discovery.EDiscoveryDeviceType} */ (reader.readEnum());
      msg.setDeviceType(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setGroupNumber(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.discovery.DiscHelloDevice.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.discovery.DiscHelloDevice.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.discovery.DiscHelloDevice} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.discovery.DiscHelloDevice.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getPcAppIp4Address();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      basic_pb.IpAddressV4.serializeBinaryToWriter
    );
  }
  f = message.getPcAppPort();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getDeviceType();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getGroupNumber();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional uint32 version = 1;
 * @return {number}
 */
proto.discovery.DiscHelloDevice.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.discovery.DiscHelloDevice} returns this
 */
proto.discovery.DiscHelloDevice.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional basic.IpAddressV4 pc_app_ip4_address = 2;
 * @return {?proto.basic.IpAddressV4}
 */
proto.discovery.DiscHelloDevice.prototype.getPcAppIp4Address = function() {
  return /** @type{?proto.basic.IpAddressV4} */ (
    jspb.Message.getWrapperField(this, basic_pb.IpAddressV4, 2));
};


/**
 * @param {?proto.basic.IpAddressV4|undefined} value
 * @return {!proto.discovery.DiscHelloDevice} returns this
*/
proto.discovery.DiscHelloDevice.prototype.setPcAppIp4Address = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.discovery.DiscHelloDevice} returns this
 */
proto.discovery.DiscHelloDevice.prototype.clearPcAppIp4Address = function() {
  return this.setPcAppIp4Address(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.discovery.DiscHelloDevice.prototype.hasPcAppIp4Address = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional uint32 pc_app_port = 3;
 * @return {number}
 */
proto.discovery.DiscHelloDevice.prototype.getPcAppPort = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.discovery.DiscHelloDevice} returns this
 */
proto.discovery.DiscHelloDevice.prototype.setPcAppPort = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional EDiscoveryDeviceType device_type = 5;
 * @return {!proto.discovery.EDiscoveryDeviceType}
 */
proto.discovery.DiscHelloDevice.prototype.getDeviceType = function() {
  return /** @type {!proto.discovery.EDiscoveryDeviceType} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.discovery.EDiscoveryDeviceType} value
 * @return {!proto.discovery.DiscHelloDevice} returns this
 */
proto.discovery.DiscHelloDevice.prototype.setDeviceType = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional uint32 group_number = 4;
 * @return {number}
 */
proto.discovery.DiscHelloDevice.prototype.getGroupNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.discovery.DiscHelloDevice} returns this
 */
proto.discovery.DiscHelloDevice.prototype.setGroupNumber = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.discovery.DiscHelloApp.prototype.toObject = function(opt_includeInstance) {
  return proto.discovery.DiscHelloApp.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.discovery.DiscHelloApp} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.discovery.DiscHelloApp.toObject = function(includeInstance, msg) {
  var f, obj = {
version: jspb.Message.getFieldWithDefault(msg, 1, 0),
deviceIp4Address: (f = msg.getDeviceIp4Address()) && basic_pb.IpAddressV4.toObject(includeInstance, f),
deviceType: jspb.Message.getFieldWithDefault(msg, 3, 0),
groupNumber: jspb.Message.getFieldWithDefault(msg, 4, 0),
deviceId: msg.getDeviceId_asB64(),
nameString: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.discovery.DiscHelloApp}
 */
proto.discovery.DiscHelloApp.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.discovery.DiscHelloApp;
  return proto.discovery.DiscHelloApp.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.discovery.DiscHelloApp} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.discovery.DiscHelloApp}
 */
proto.discovery.DiscHelloApp.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 2:
      var value = new basic_pb.IpAddressV4;
      reader.readMessage(value,basic_pb.IpAddressV4.deserializeBinaryFromReader);
      msg.setDeviceIp4Address(value);
      break;
    case 3:
      var value = /** @type {!proto.discovery.EDiscoveryDeviceType} */ (reader.readEnum());
      msg.setDeviceType(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setGroupNumber(value);
      break;
    case 5:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setDeviceId(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setNameString(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.discovery.DiscHelloApp.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.discovery.DiscHelloApp.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.discovery.DiscHelloApp} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.discovery.DiscHelloApp.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getDeviceIp4Address();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      basic_pb.IpAddressV4.serializeBinaryToWriter
    );
  }
  f = message.getDeviceType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getGroupNumber();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getDeviceId_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      5,
      f
    );
  }
  f = message.getNameString();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional uint32 version = 1;
 * @return {number}
 */
proto.discovery.DiscHelloApp.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.discovery.DiscHelloApp} returns this
 */
proto.discovery.DiscHelloApp.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional basic.IpAddressV4 device_ip4_address = 2;
 * @return {?proto.basic.IpAddressV4}
 */
proto.discovery.DiscHelloApp.prototype.getDeviceIp4Address = function() {
  return /** @type{?proto.basic.IpAddressV4} */ (
    jspb.Message.getWrapperField(this, basic_pb.IpAddressV4, 2));
};


/**
 * @param {?proto.basic.IpAddressV4|undefined} value
 * @return {!proto.discovery.DiscHelloApp} returns this
*/
proto.discovery.DiscHelloApp.prototype.setDeviceIp4Address = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.discovery.DiscHelloApp} returns this
 */
proto.discovery.DiscHelloApp.prototype.clearDeviceIp4Address = function() {
  return this.setDeviceIp4Address(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.discovery.DiscHelloApp.prototype.hasDeviceIp4Address = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional EDiscoveryDeviceType device_type = 3;
 * @return {!proto.discovery.EDiscoveryDeviceType}
 */
proto.discovery.DiscHelloApp.prototype.getDeviceType = function() {
  return /** @type {!proto.discovery.EDiscoveryDeviceType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.discovery.EDiscoveryDeviceType} value
 * @return {!proto.discovery.DiscHelloApp} returns this
 */
proto.discovery.DiscHelloApp.prototype.setDeviceType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional uint32 group_number = 4;
 * @return {number}
 */
proto.discovery.DiscHelloApp.prototype.getGroupNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.discovery.DiscHelloApp} returns this
 */
proto.discovery.DiscHelloApp.prototype.setGroupNumber = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional bytes device_id = 5;
 * @return {string}
 */
proto.discovery.DiscHelloApp.prototype.getDeviceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * optional bytes device_id = 5;
 * This is a type-conversion wrapper around `getDeviceId()`
 * @return {string}
 */
proto.discovery.DiscHelloApp.prototype.getDeviceId_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getDeviceId()));
};


/**
 * optional bytes device_id = 5;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getDeviceId()`
 * @return {!Uint8Array}
 */
proto.discovery.DiscHelloApp.prototype.getDeviceId_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getDeviceId()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.discovery.DiscHelloApp} returns this
 */
proto.discovery.DiscHelloApp.prototype.setDeviceId = function(value) {
  return jspb.Message.setProto3BytesField(this, 5, value);
};


/**
 * optional string name_string = 6;
 * @return {string}
 */
proto.discovery.DiscHelloApp.prototype.getNameString = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.discovery.DiscHelloApp} returns this
 */
proto.discovery.DiscHelloApp.prototype.setNameString = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * @enum {number}
 */
proto.discovery.EDiscoveryDeviceType = {
  DISC_TYPE_UNSPECIFIED: 0,
  DISC_TYPE_MMU: 1
};

goog.object.extend(exports, proto.discovery);
