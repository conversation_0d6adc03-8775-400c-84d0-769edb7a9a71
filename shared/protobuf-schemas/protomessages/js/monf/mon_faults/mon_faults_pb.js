// source: mon_faults.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

goog.exportSymbol('proto.mon_faults.EFaultCode', null, global);
goog.exportSymbol('proto.mon_faults.ESubFaultDataKey', null, global);
goog.exportSymbol('proto.mon_faults.ESubFaultDiagnostic', null, global);
goog.exportSymbol('proto.mon_faults.ESubFaultLackOfSignal', null, global);
goog.exportSymbol('proto.mon_faults.ESubFaultMultipleIndication', null, global);
goog.exportSymbol('proto.mon_faults.ESubFaultSerialPort', null, global);
goog.exportSymbol('proto.mon_faults.ESubFaultSkippedYellow', null, global);
goog.exportSymbol('proto.mon_faults.FaultIndicationChVoltCurrent', null, global);
goog.exportSymbol('proto.mon_faults.MmuMonitoredControlStatesBitmap', null, global);
goog.exportSymbol('proto.mon_faults.MmuMonitoredInputsStatusBitmap', null, global);
goog.exportSymbol('proto.mon_faults.MmuSubFaultTypeValue', null, global);
goog.exportSymbol('proto.mon_faults.MmuSubFaultTypeValue.SubcodeTypeCase', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_faults.MmuSubFaultTypeValue = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_);
};
goog.inherits(proto.mon_faults.MmuSubFaultTypeValue, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_faults.MmuSubFaultTypeValue.displayName = 'proto.mon_faults.MmuSubFaultTypeValue';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_faults.MmuMonitoredControlStatesBitmap, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_faults.MmuMonitoredControlStatesBitmap.displayName = 'proto.mon_faults.MmuMonitoredControlStatesBitmap';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_faults.MmuMonitoredInputsStatusBitmap, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_faults.MmuMonitoredInputsStatusBitmap.displayName = 'proto.mon_faults.MmuMonitoredInputsStatusBitmap';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.mon_faults.FaultIndicationChVoltCurrent = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.mon_faults.FaultIndicationChVoltCurrent, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.mon_faults.FaultIndicationChVoltCurrent.displayName = 'proto.mon_faults.FaultIndicationChVoltCurrent';
}

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_ = [[1,2,3,4,5]];

/**
 * @enum {number}
 */
proto.mon_faults.MmuSubFaultTypeValue.SubcodeTypeCase = {
  SUBCODE_TYPE_NOT_SET: 0,
  SERIAL_PORT: 1,
  MULTIPLE_INDICATION: 2,
  LACK_OF_SIGNAL: 3,
  SKIPPED_YELLOW: 4,
  DATA_KEY: 5
};

/**
 * @return {proto.mon_faults.MmuSubFaultTypeValue.SubcodeTypeCase}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.getSubcodeTypeCase = function() {
  return /** @type {proto.mon_faults.MmuSubFaultTypeValue.SubcodeTypeCase} */(jspb.Message.computeOneofCase(this, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_faults.MmuSubFaultTypeValue.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_faults.MmuSubFaultTypeValue} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.MmuSubFaultTypeValue.toObject = function(includeInstance, msg) {
  var f, obj = {
serialPort: (f = jspb.Message.getField(msg, 1)) == null ? undefined : f,
multipleIndication: (f = jspb.Message.getField(msg, 2)) == null ? undefined : f,
lackOfSignal: (f = jspb.Message.getField(msg, 3)) == null ? undefined : f,
skippedYellow: (f = jspb.Message.getField(msg, 4)) == null ? undefined : f,
dataKey: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_faults.MmuSubFaultTypeValue}
 */
proto.mon_faults.MmuSubFaultTypeValue.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_faults.MmuSubFaultTypeValue;
  return proto.mon_faults.MmuSubFaultTypeValue.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_faults.MmuSubFaultTypeValue} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_faults.MmuSubFaultTypeValue}
 */
proto.mon_faults.MmuSubFaultTypeValue.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.mon_faults.ESubFaultSerialPort} */ (reader.readEnum());
      msg.setSerialPort(value);
      break;
    case 2:
      var value = /** @type {!proto.mon_faults.ESubFaultMultipleIndication} */ (reader.readEnum());
      msg.setMultipleIndication(value);
      break;
    case 3:
      var value = /** @type {!proto.mon_faults.ESubFaultLackOfSignal} */ (reader.readEnum());
      msg.setLackOfSignal(value);
      break;
    case 4:
      var value = /** @type {!proto.mon_faults.ESubFaultSkippedYellow} */ (reader.readEnum());
      msg.setSkippedYellow(value);
      break;
    case 5:
      var value = /** @type {!proto.mon_faults.ESubFaultDataKey} */ (reader.readEnum());
      msg.setDataKey(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_faults.MmuSubFaultTypeValue.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_faults.MmuSubFaultTypeValue} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.MmuSubFaultTypeValue.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = /** @type {!proto.mon_faults.ESubFaultSerialPort} */ (jspb.Message.getField(message, 1));
  if (f != null) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = /** @type {!proto.mon_faults.ESubFaultMultipleIndication} */ (jspb.Message.getField(message, 2));
  if (f != null) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = /** @type {!proto.mon_faults.ESubFaultLackOfSignal} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = /** @type {!proto.mon_faults.ESubFaultSkippedYellow} */ (jspb.Message.getField(message, 4));
  if (f != null) {
    writer.writeEnum(
      4,
      f
    );
  }
  f = /** @type {!proto.mon_faults.ESubFaultDataKey} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeEnum(
      5,
      f
    );
  }
};


/**
 * optional ESubFaultSerialPort serial_port = 1;
 * @return {!proto.mon_faults.ESubFaultSerialPort}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.getSerialPort = function() {
  return /** @type {!proto.mon_faults.ESubFaultSerialPort} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.mon_faults.ESubFaultSerialPort} value
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.setSerialPort = function(value) {
  return jspb.Message.setOneofField(this, 1, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.clearSerialPort = function() {
  return jspb.Message.setOneofField(this, 1, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.hasSerialPort = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional ESubFaultMultipleIndication multiple_indication = 2;
 * @return {!proto.mon_faults.ESubFaultMultipleIndication}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.getMultipleIndication = function() {
  return /** @type {!proto.mon_faults.ESubFaultMultipleIndication} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.mon_faults.ESubFaultMultipleIndication} value
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.setMultipleIndication = function(value) {
  return jspb.Message.setOneofField(this, 2, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.clearMultipleIndication = function() {
  return jspb.Message.setOneofField(this, 2, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.hasMultipleIndication = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional ESubFaultLackOfSignal lack_of_signal = 3;
 * @return {!proto.mon_faults.ESubFaultLackOfSignal}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.getLackOfSignal = function() {
  return /** @type {!proto.mon_faults.ESubFaultLackOfSignal} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.mon_faults.ESubFaultLackOfSignal} value
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.setLackOfSignal = function(value) {
  return jspb.Message.setOneofField(this, 3, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.clearLackOfSignal = function() {
  return jspb.Message.setOneofField(this, 3, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.hasLackOfSignal = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional ESubFaultSkippedYellow skipped_yellow = 4;
 * @return {!proto.mon_faults.ESubFaultSkippedYellow}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.getSkippedYellow = function() {
  return /** @type {!proto.mon_faults.ESubFaultSkippedYellow} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.mon_faults.ESubFaultSkippedYellow} value
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.setSkippedYellow = function(value) {
  return jspb.Message.setOneofField(this, 4, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.clearSkippedYellow = function() {
  return jspb.Message.setOneofField(this, 4, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.hasSkippedYellow = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional ESubFaultDataKey data_key = 5;
 * @return {!proto.mon_faults.ESubFaultDataKey}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.getDataKey = function() {
  return /** @type {!proto.mon_faults.ESubFaultDataKey} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.mon_faults.ESubFaultDataKey} value
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.setDataKey = function(value) {
  return jspb.Message.setOneofField(this, 5, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_faults.MmuSubFaultTypeValue} returns this
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.clearDataKey = function() {
  return jspb.Message.setOneofField(this, 5, proto.mon_faults.MmuSubFaultTypeValue.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_faults.MmuSubFaultTypeValue.prototype.hasDataKey = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_faults.MmuMonitoredControlStatesBitmap.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_faults.MmuMonitoredControlStatesBitmap} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.toObject = function(includeInstance, msg) {
  var f, obj = {
startDelayRelay: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
resetEvent: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
startupFlashCall: jspb.Message.getBooleanFieldWithDefault(msg, 6, false),
acLineValid: jspb.Message.getBooleanFieldWithDefault(msg, 7, false),
faultRelay: jspb.Message.getBooleanFieldWithDefault(msg, 8, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_faults.MmuMonitoredControlStatesBitmap}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_faults.MmuMonitoredControlStatesBitmap;
  return proto.mon_faults.MmuMonitoredControlStatesBitmap.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_faults.MmuMonitoredControlStatesBitmap} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_faults.MmuMonitoredControlStatesBitmap}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setStartDelayRelay(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setResetEvent(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setStartupFlashCall(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setAcLineValid(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFaultRelay(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_faults.MmuMonitoredControlStatesBitmap.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_faults.MmuMonitoredControlStatesBitmap} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStartDelayRelay();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getResetEvent();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getStartupFlashCall();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
  f = message.getAcLineValid();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getFaultRelay();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
};


/**
 * optional bool start_delay_relay = 4;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.getStartDelayRelay = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredControlStatesBitmap} returns this
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.setStartDelayRelay = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional bool reset_event = 5;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.getResetEvent = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredControlStatesBitmap} returns this
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.setResetEvent = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional bool startup_flash_call = 6;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.getStartupFlashCall = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 6, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredControlStatesBitmap} returns this
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.setStartupFlashCall = function(value) {
  return jspb.Message.setProto3BooleanField(this, 6, value);
};


/**
 * optional bool ac_line_valid = 7;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.getAcLineValid = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredControlStatesBitmap} returns this
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.setAcLineValid = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * optional bool fault_relay = 8;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.getFaultRelay = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredControlStatesBitmap} returns this
 */
proto.mon_faults.MmuMonitoredControlStatesBitmap.prototype.setFaultRelay = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_faults.MmuMonitoredInputsStatusBitmap.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.toObject = function(includeInstance, msg) {
  var f, obj = {
monitor24vInhibit: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
monitor24v1: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
monitor24v2: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
controllerVoltage: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
typeSelect: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
redEnable: jspb.Message.getBooleanFieldWithDefault(msg, 6, false),
externalReset: jspb.Message.getBooleanFieldWithDefault(msg, 7, false),
port1Disable: jspb.Message.getBooleanFieldWithDefault(msg, 8, false),
programCardInserted: jspb.Message.getBooleanFieldWithDefault(msg, 9, false),
localFlash: jspb.Message.getBooleanFieldWithDefault(msg, 10, false),
externalWatchdog: jspb.Message.getBooleanFieldWithDefault(msg, 11, false),
alarm: jspb.Message.getBooleanFieldWithDefault(msg, 12, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_faults.MmuMonitoredInputsStatusBitmap;
  return proto.mon_faults.MmuMonitoredInputsStatusBitmap.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMonitor24vInhibit(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMonitor24v1(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMonitor24v2(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setControllerVoltage(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setTypeSelect(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setRedEnable(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setExternalReset(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setPort1Disable(value);
      break;
    case 9:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setProgramCardInserted(value);
      break;
    case 10:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setLocalFlash(value);
      break;
    case 11:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setExternalWatchdog(value);
      break;
    case 12:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setAlarm(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_faults.MmuMonitoredInputsStatusBitmap.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMonitor24vInhibit();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getMonitor24v1();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getMonitor24v2();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getControllerVoltage();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getTypeSelect();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getRedEnable();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
  f = message.getExternalReset();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getPort1Disable();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
  f = message.getProgramCardInserted();
  if (f) {
    writer.writeBool(
      9,
      f
    );
  }
  f = message.getLocalFlash();
  if (f) {
    writer.writeBool(
      10,
      f
    );
  }
  f = message.getExternalWatchdog();
  if (f) {
    writer.writeBool(
      11,
      f
    );
  }
  f = message.getAlarm();
  if (f) {
    writer.writeBool(
      12,
      f
    );
  }
};


/**
 * optional bool monitor_24v_inhibit = 1;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getMonitor24vInhibit = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setMonitor24vInhibit = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool monitor_24v_1 = 2;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getMonitor24v1 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setMonitor24v1 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional bool monitor_24v_2 = 3;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getMonitor24v2 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setMonitor24v2 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional bool controller_voltage = 4;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getControllerVoltage = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setControllerVoltage = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional bool type_select = 5;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getTypeSelect = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setTypeSelect = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional bool red_enable = 6;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getRedEnable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 6, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setRedEnable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 6, value);
};


/**
 * optional bool external_reset = 7;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getExternalReset = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setExternalReset = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * optional bool port1_disable = 8;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getPort1Disable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setPort1Disable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};


/**
 * optional bool program_card_inserted = 9;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getProgramCardInserted = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 9, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setProgramCardInserted = function(value) {
  return jspb.Message.setProto3BooleanField(this, 9, value);
};


/**
 * optional bool local_flash = 10;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getLocalFlash = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 10, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setLocalFlash = function(value) {
  return jspb.Message.setProto3BooleanField(this, 10, value);
};


/**
 * optional bool external_watchdog = 11;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getExternalWatchdog = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 11, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setExternalWatchdog = function(value) {
  return jspb.Message.setProto3BooleanField(this, 11, value);
};


/**
 * optional bool alarm = 12;
 * @return {boolean}
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.getAlarm = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 12, false));
};


/**
 * @param {boolean} value
 * @return {!proto.mon_faults.MmuMonitoredInputsStatusBitmap} returns this
 */
proto.mon_faults.MmuMonitoredInputsStatusBitmap.prototype.setAlarm = function(value) {
  return jspb.Message.setProto3BooleanField(this, 12, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.toObject = function(opt_includeInstance) {
  return proto.mon_faults.FaultIndicationChVoltCurrent.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.mon_faults.FaultIndicationChVoltCurrent} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.FaultIndicationChVoltCurrent.toObject = function(includeInstance, msg) {
  var f, obj = {
channel: jspb.Message.getFieldWithDefault(msg, 1, 0),
voltageV: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
currentA: (f = jspb.Message.getOptionalFloatingPointField(msg, 3)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.mon_faults.FaultIndicationChVoltCurrent;
  return proto.mon_faults.FaultIndicationChVoltCurrent.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.mon_faults.FaultIndicationChVoltCurrent} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChannel(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setVoltageV(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setCurrentA(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.mon_faults.FaultIndicationChVoltCurrent.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.mon_faults.FaultIndicationChVoltCurrent} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.mon_faults.FaultIndicationChVoltCurrent.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannel();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getVoltageV();
  if (f !== 0.0) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 3));
  if (f != null) {
    writer.writeFloat(
      3,
      f
    );
  }
};


/**
 * optional uint32 channel = 1;
 * @return {number}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.getChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent} returns this
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.setChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional float voltage_v = 2;
 * @return {number}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.getVoltageV = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent} returns this
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.setVoltageV = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional float current_a = 3;
 * @return {number}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.getCurrentA = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent} returns this
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.setCurrentA = function(value) {
  return jspb.Message.setField(this, 3, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.mon_faults.FaultIndicationChVoltCurrent} returns this
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.clearCurrentA = function() {
  return jspb.Message.setField(this, 3, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.mon_faults.FaultIndicationChVoltCurrent.prototype.hasCurrentA = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * @enum {number}
 */
proto.mon_faults.EFaultCode = {
  FAULT_CODE_UNSPECIFIED: 0,
  FAULT_CODE_SERIAL_PORT1: 1,
  FAULT_CODE_MONITOR_MAINS: 3,
  FAULT_CODE_FLASH: 5,
  FAULT_CODE_CONTROLLER_VOLTAGE: 9,
  FAULT_CODE_24V_MONITOR_1: 10,
  FAULT_CODE_24V_MONITOR_2: 11,
  FAULT_CODE_CONFLICT: 12,
  FAULT_CODE_MULTIPLE_INDICATION: 14,
  FAULT_CODE_EXTERNAL_WATCHDOG: 15,
  FAULT_CODE_LACK_OF_SIGNAL: 16,
  FAULT_CODE_MINIMUM_Y_CLEARANCE: 17,
  FAULT_CODE_SKIPPED_Y_CLEARANCE: 18,
  FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE: 19,
  FAULT_CODE_FIELD_CHECK: 20,
  FAULT_CODE_FLASHING_Y_ARROW: 21,
  FAULT_CODE_DATAKEY_DATA: 23,
  FAULT_CODE_DATAKEY_ABSENT: 24,
  FAULT_CODE_MULTIPLE_FAULTS: 100
};

/**
 * @enum {number}
 */
proto.mon_faults.ESubFaultSerialPort = {
  SUBFLT_SERIAL_UNSPECIFIED: 0,
  SUBFLT_SERIAL_TIMEOUT: 1,
  SUBFLT_SERIAL_QUALITY: 2,
  SUBFLT_SERIAL_RESET: 3
};

/**
 * @enum {number}
 */
proto.mon_faults.ESubFaultMultipleIndication = {
  SUBFLT_MULTIND_UNSPECIFIED: 0,
  SUBFLT_MULTIND_GREEN_YELLOW: 1,
  SUBFLT_MULTIND_GREEN_RED: 2,
  SUBFLT_MULTIND_YELLOW_RED: 3,
  SUBFLT_MULTIND_MULTIPLE: 4,
  SUBFLT_MULTIND_FLASHING_Y_ARROW: 5
};

/**
 * @enum {number}
 */
proto.mon_faults.ESubFaultLackOfSignal = {
  SUBFLT_LACKOFSIG_UNSPECIFIED: 0,
  SUBFLT_LACKOFSIG_NORMAL: 1,
  SUBFLT_LACKOFSIG_FLASHING_Y_ARROW: 2
};

/**
 * @enum {number}
 */
proto.mon_faults.ESubFaultSkippedYellow = {
  SUBFLT_SKIPPEDY_UNSPECIFIED: 0,
  SUBFLT_SKIPPEDY_STANDARD: 1,
  SUBFLT_SKIPPEDY_FYA_FLASHING_Y: 2,
  SUBFLT_SKIPPEDY_FYA_GREEN: 3
};

/**
 * @enum {number}
 */
proto.mon_faults.ESubFaultDataKey = {
  SUBFLT_DATAKEY_UNSPECIFIED: 0,
  SUBFLT_DATAKEY_CRC_ERROR: 1,
  SUBFLT_DATAKEY_FORMAT_ERROR: 2,
  SUBFLT_DATAKEY_DATA_ERROR: 3,
  SUBFLT_DATAKEY_RAM_CRC_ERROR: 4
};

/**
 * @enum {number}
 */
proto.mon_faults.ESubFaultDiagnostic = {
  SUBFLT_DIAG_UNSPECIFIED: 0,
  SUBFLT_DIAG_1: 1,
  SUBFLT_DIAG_2: 2,
  SUBFLT_DIAG_3: 3,
  SUBFLT_DIAG_4: 4
};

goog.object.extend(exports, proto.mon_faults);
