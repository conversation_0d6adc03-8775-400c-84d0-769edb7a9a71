// source: settings.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
goog.exportSymbol('proto.settings.AgencyOptionsMmu', null, global);
goog.exportSymbol('proto.settings.EConfigDataLocation', null, global);
goog.exportSymbol('proto.settings.EDisableOverrideState', null, global);
goog.exportSymbol('proto.settings.EFlashingYellowArrowMode', null, global);
goog.exportSymbol('proto.settings.EMonitorStatistics', null, global);
goog.exportSymbol('proto.settings.EOemType', null, global);
goog.exportSymbol('proto.settings.EPcDkReadType', null, global);
goog.exportSymbol('proto.settings.ERemoteResetType', null, global);
goog.exportSymbol('proto.settings.EStatusDisplay', null, global);
goog.exportSymbol('proto.settings.ETimeSetResult', null, global);
goog.exportSymbol('proto.settings.EWriteResult', null, global);
goog.exportSymbol('proto.settings.FactoryOptionsMmu', null, global);
goog.exportSymbol('proto.settings.FactorySettings', null, global);
goog.exportSymbol('proto.settings.FlashAreaStatistics', null, global);
goog.exportSymbol('proto.settings.FlashingYellowArrowSettings', null, global);
goog.exportSymbol('proto.settings.NetworkSettings', null, global);
goog.exportSymbol('proto.settings.PcbOptionsMmu', null, global);
goog.exportSymbol('proto.settings.PerChannelCurrentSenseSettings', null, global);
goog.exportSymbol('proto.settings.PerChannelPermissives', null, global);
goog.exportSymbol('proto.settings.PerChannelSettings', null, global);
goog.exportSymbol('proto.settings.UserOptionsBitmap', null, global);
goog.exportSymbol('proto.settings.UserSettings', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.NetworkSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.NetworkSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.NetworkSettings.displayName = 'proto.settings.NetworkSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.FactorySettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.FactorySettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.FactorySettings.displayName = 'proto.settings.FactorySettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.UserOptionsBitmap = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.UserOptionsBitmap, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.UserOptionsBitmap.displayName = 'proto.settings.UserOptionsBitmap';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.UserSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.UserSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.UserSettings.displayName = 'proto.settings.UserSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.PerChannelSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.PerChannelSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.PerChannelSettings.displayName = 'proto.settings.PerChannelSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.PerChannelCurrentSenseSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.PerChannelCurrentSenseSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.PerChannelCurrentSenseSettings.displayName = 'proto.settings.PerChannelCurrentSenseSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.PerChannelPermissives = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.PerChannelPermissives, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.PerChannelPermissives.displayName = 'proto.settings.PerChannelPermissives';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.FlashingYellowArrowSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.FlashingYellowArrowSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.FlashingYellowArrowSettings.displayName = 'proto.settings.FlashingYellowArrowSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.FlashAreaStatistics = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.FlashAreaStatistics, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.FlashAreaStatistics.displayName = 'proto.settings.FlashAreaStatistics';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.PcbOptionsMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.PcbOptionsMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.PcbOptionsMmu.displayName = 'proto.settings.PcbOptionsMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.FactoryOptionsMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.FactoryOptionsMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.FactoryOptionsMmu.displayName = 'proto.settings.FactoryOptionsMmu';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.settings.AgencyOptionsMmu = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.settings.AgencyOptionsMmu, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.settings.AgencyOptionsMmu.displayName = 'proto.settings.AgencyOptionsMmu';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.NetworkSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.NetworkSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.NetworkSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.NetworkSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
dhcpClientEnabled: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
dhcpServerEnabled: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
ethernetHostname: jspb.Message.getFieldWithDefault(msg, 3, ""),
monitorIpv4Address: (f = msg.getMonitorIpv4Address()) && basic_pb.IpAddressV4.toObject(includeInstance, f),
monitorIpv4Subnet: (f = msg.getMonitorIpv4Subnet()) && basic_pb.IpAddressV4.toObject(includeInstance, f),
monitorIpv4Gateway: (f = msg.getMonitorIpv4Gateway()) && basic_pb.IpAddressV4.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.NetworkSettings}
 */
proto.settings.NetworkSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.NetworkSettings;
  return proto.settings.NetworkSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.NetworkSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.NetworkSettings}
 */
proto.settings.NetworkSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDhcpClientEnabled(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDhcpServerEnabled(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setEthernetHostname(value);
      break;
    case 4:
      var value = new basic_pb.IpAddressV4;
      reader.readMessage(value,basic_pb.IpAddressV4.deserializeBinaryFromReader);
      msg.setMonitorIpv4Address(value);
      break;
    case 5:
      var value = new basic_pb.IpAddressV4;
      reader.readMessage(value,basic_pb.IpAddressV4.deserializeBinaryFromReader);
      msg.setMonitorIpv4Subnet(value);
      break;
    case 6:
      var value = new basic_pb.IpAddressV4;
      reader.readMessage(value,basic_pb.IpAddressV4.deserializeBinaryFromReader);
      msg.setMonitorIpv4Gateway(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.NetworkSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.NetworkSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.NetworkSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.NetworkSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDhcpClientEnabled();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getDhcpServerEnabled();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getEthernetHostname();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getMonitorIpv4Address();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      basic_pb.IpAddressV4.serializeBinaryToWriter
    );
  }
  f = message.getMonitorIpv4Subnet();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      basic_pb.IpAddressV4.serializeBinaryToWriter
    );
  }
  f = message.getMonitorIpv4Gateway();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      basic_pb.IpAddressV4.serializeBinaryToWriter
    );
  }
};


/**
 * optional bool dhcp_client_enabled = 1;
 * @return {boolean}
 */
proto.settings.NetworkSettings.prototype.getDhcpClientEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.NetworkSettings} returns this
 */
proto.settings.NetworkSettings.prototype.setDhcpClientEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool dhcp_server_enabled = 2;
 * @return {boolean}
 */
proto.settings.NetworkSettings.prototype.getDhcpServerEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.NetworkSettings} returns this
 */
proto.settings.NetworkSettings.prototype.setDhcpServerEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional string ethernet_hostname = 3;
 * @return {string}
 */
proto.settings.NetworkSettings.prototype.getEthernetHostname = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.settings.NetworkSettings} returns this
 */
proto.settings.NetworkSettings.prototype.setEthernetHostname = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional basic.IpAddressV4 monitor_ipv4_address = 4;
 * @return {?proto.basic.IpAddressV4}
 */
proto.settings.NetworkSettings.prototype.getMonitorIpv4Address = function() {
  return /** @type{?proto.basic.IpAddressV4} */ (
    jspb.Message.getWrapperField(this, basic_pb.IpAddressV4, 4));
};


/**
 * @param {?proto.basic.IpAddressV4|undefined} value
 * @return {!proto.settings.NetworkSettings} returns this
*/
proto.settings.NetworkSettings.prototype.setMonitorIpv4Address = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.NetworkSettings} returns this
 */
proto.settings.NetworkSettings.prototype.clearMonitorIpv4Address = function() {
  return this.setMonitorIpv4Address(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.NetworkSettings.prototype.hasMonitorIpv4Address = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional basic.IpAddressV4 monitor_ipv4_subnet = 5;
 * @return {?proto.basic.IpAddressV4}
 */
proto.settings.NetworkSettings.prototype.getMonitorIpv4Subnet = function() {
  return /** @type{?proto.basic.IpAddressV4} */ (
    jspb.Message.getWrapperField(this, basic_pb.IpAddressV4, 5));
};


/**
 * @param {?proto.basic.IpAddressV4|undefined} value
 * @return {!proto.settings.NetworkSettings} returns this
*/
proto.settings.NetworkSettings.prototype.setMonitorIpv4Subnet = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.NetworkSettings} returns this
 */
proto.settings.NetworkSettings.prototype.clearMonitorIpv4Subnet = function() {
  return this.setMonitorIpv4Subnet(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.NetworkSettings.prototype.hasMonitorIpv4Subnet = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * optional basic.IpAddressV4 monitor_ipv4_gateway = 6;
 * @return {?proto.basic.IpAddressV4}
 */
proto.settings.NetworkSettings.prototype.getMonitorIpv4Gateway = function() {
  return /** @type{?proto.basic.IpAddressV4} */ (
    jspb.Message.getWrapperField(this, basic_pb.IpAddressV4, 6));
};


/**
 * @param {?proto.basic.IpAddressV4|undefined} value
 * @return {!proto.settings.NetworkSettings} returns this
*/
proto.settings.NetworkSettings.prototype.setMonitorIpv4Gateway = function(value) {
  return jspb.Message.setWrapperField(this, 6, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.NetworkSettings} returns this
 */
proto.settings.NetworkSettings.prototype.clearMonitorIpv4Gateway = function() {
  return this.setMonitorIpv4Gateway(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.NetworkSettings.prototype.hasMonitorIpv4Gateway = function() {
  return jspb.Message.getField(this, 6) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.FactorySettings.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.FactorySettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.FactorySettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FactorySettings.toObject = function(includeInstance, msg) {
  var f, obj = {
numbers: (f = msg.getNumbers()) && basic_pb.ModelAndSerialNumber.toObject(includeInstance, f),
deviceDescription: jspb.Message.getFieldWithDefault(msg, 2, ""),
manufactureDate: (f = msg.getManufactureDate()) && basic_pb.DateStr.toObject(includeInstance, f),
pcbOptions: (f = msg.getPcbOptions()) && proto.settings.PcbOptionsMmu.toObject(includeInstance, f),
factoryOptions: (f = msg.getFactoryOptions()) && proto.settings.FactoryOptionsMmu.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.FactorySettings}
 */
proto.settings.FactorySettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.FactorySettings;
  return proto.settings.FactorySettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.FactorySettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.FactorySettings}
 */
proto.settings.FactorySettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new basic_pb.ModelAndSerialNumber;
      reader.readMessage(value,basic_pb.ModelAndSerialNumber.deserializeBinaryFromReader);
      msg.setNumbers(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeviceDescription(value);
      break;
    case 3:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setManufactureDate(value);
      break;
    case 4:
      var value = new proto.settings.PcbOptionsMmu;
      reader.readMessage(value,proto.settings.PcbOptionsMmu.deserializeBinaryFromReader);
      msg.setPcbOptions(value);
      break;
    case 5:
      var value = new proto.settings.FactoryOptionsMmu;
      reader.readMessage(value,proto.settings.FactoryOptionsMmu.deserializeBinaryFromReader);
      msg.setFactoryOptions(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.FactorySettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.FactorySettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.FactorySettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FactorySettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getNumbers();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      basic_pb.ModelAndSerialNumber.serializeBinaryToWriter
    );
  }
  f = message.getDeviceDescription();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getManufactureDate();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
  f = message.getPcbOptions();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      proto.settings.PcbOptionsMmu.serializeBinaryToWriter
    );
  }
  f = message.getFactoryOptions();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      proto.settings.FactoryOptionsMmu.serializeBinaryToWriter
    );
  }
};


/**
 * optional basic.ModelAndSerialNumber numbers = 1;
 * @return {?proto.basic.ModelAndSerialNumber}
 */
proto.settings.FactorySettings.prototype.getNumbers = function() {
  return /** @type{?proto.basic.ModelAndSerialNumber} */ (
    jspb.Message.getWrapperField(this, basic_pb.ModelAndSerialNumber, 1));
};


/**
 * @param {?proto.basic.ModelAndSerialNumber|undefined} value
 * @return {!proto.settings.FactorySettings} returns this
*/
proto.settings.FactorySettings.prototype.setNumbers = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.FactorySettings} returns this
 */
proto.settings.FactorySettings.prototype.clearNumbers = function() {
  return this.setNumbers(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.FactorySettings.prototype.hasNumbers = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string device_description = 2;
 * @return {string}
 */
proto.settings.FactorySettings.prototype.getDeviceDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.settings.FactorySettings} returns this
 */
proto.settings.FactorySettings.prototype.setDeviceDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional basic.DateStr manufacture_date = 3;
 * @return {?proto.basic.DateStr}
 */
proto.settings.FactorySettings.prototype.getManufactureDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 3));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.settings.FactorySettings} returns this
*/
proto.settings.FactorySettings.prototype.setManufactureDate = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.FactorySettings} returns this
 */
proto.settings.FactorySettings.prototype.clearManufactureDate = function() {
  return this.setManufactureDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.FactorySettings.prototype.hasManufactureDate = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional PcbOptionsMmu pcb_options = 4;
 * @return {?proto.settings.PcbOptionsMmu}
 */
proto.settings.FactorySettings.prototype.getPcbOptions = function() {
  return /** @type{?proto.settings.PcbOptionsMmu} */ (
    jspb.Message.getWrapperField(this, proto.settings.PcbOptionsMmu, 4));
};


/**
 * @param {?proto.settings.PcbOptionsMmu|undefined} value
 * @return {!proto.settings.FactorySettings} returns this
*/
proto.settings.FactorySettings.prototype.setPcbOptions = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.FactorySettings} returns this
 */
proto.settings.FactorySettings.prototype.clearPcbOptions = function() {
  return this.setPcbOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.FactorySettings.prototype.hasPcbOptions = function() {
  return jspb.Message.getField(this, 4) != null;
};


/**
 * optional FactoryOptionsMmu factory_options = 5;
 * @return {?proto.settings.FactoryOptionsMmu}
 */
proto.settings.FactorySettings.prototype.getFactoryOptions = function() {
  return /** @type{?proto.settings.FactoryOptionsMmu} */ (
    jspb.Message.getWrapperField(this, proto.settings.FactoryOptionsMmu, 5));
};


/**
 * @param {?proto.settings.FactoryOptionsMmu|undefined} value
 * @return {!proto.settings.FactorySettings} returns this
*/
proto.settings.FactorySettings.prototype.setFactoryOptions = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.FactorySettings} returns this
 */
proto.settings.FactorySettings.prototype.clearFactoryOptions = function() {
  return this.setFactoryOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.FactorySettings.prototype.hasFactoryOptions = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.UserOptionsBitmap.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.UserOptionsBitmap.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.UserOptionsBitmap} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.UserOptionsBitmap.toObject = function(includeInstance, msg) {
  var f, obj = {
option1: jspb.Message.getBooleanFieldWithDefault(msg, 1, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.UserOptionsBitmap}
 */
proto.settings.UserOptionsBitmap.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.UserOptionsBitmap;
  return proto.settings.UserOptionsBitmap.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.UserOptionsBitmap} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.UserOptionsBitmap}
 */
proto.settings.UserOptionsBitmap.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOption1(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.UserOptionsBitmap.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.UserOptionsBitmap.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.UserOptionsBitmap} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.UserOptionsBitmap.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOption1();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
};


/**
 * optional bool option_1 = 1;
 * @return {boolean}
 */
proto.settings.UserOptionsBitmap.prototype.getOption1 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.UserOptionsBitmap} returns this
 */
proto.settings.UserOptionsBitmap.prototype.setOption1 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.UserSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.UserSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.UserSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.UserSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
userOptions: (f = msg.getUserOptions()) && proto.settings.UserOptionsBitmap.toObject(includeInstance, f),
sleepTimeMinutes: jspb.Message.getFieldWithDefault(msg, 2, 0),
displayDimming: jspb.Message.getFieldWithDefault(msg, 3, 0),
lastStatusDisplay: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.UserSettings}
 */
proto.settings.UserSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.UserSettings;
  return proto.settings.UserSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.UserSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.UserSettings}
 */
proto.settings.UserSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.settings.UserOptionsBitmap;
      reader.readMessage(value,proto.settings.UserOptionsBitmap.deserializeBinaryFromReader);
      msg.setUserOptions(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSleepTimeMinutes(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDisplayDimming(value);
      break;
    case 4:
      var value = /** @type {!proto.settings.EStatusDisplay} */ (reader.readEnum());
      msg.setLastStatusDisplay(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.UserSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.UserSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.UserSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.UserSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getUserOptions();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.settings.UserOptionsBitmap.serializeBinaryToWriter
    );
  }
  f = message.getSleepTimeMinutes();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getDisplayDimming();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getLastStatusDisplay();
  if (f !== 0.0) {
    writer.writeEnum(
      4,
      f
    );
  }
};


/**
 * optional UserOptionsBitmap user_options = 1;
 * @return {?proto.settings.UserOptionsBitmap}
 */
proto.settings.UserSettings.prototype.getUserOptions = function() {
  return /** @type{?proto.settings.UserOptionsBitmap} */ (
    jspb.Message.getWrapperField(this, proto.settings.UserOptionsBitmap, 1));
};


/**
 * @param {?proto.settings.UserOptionsBitmap|undefined} value
 * @return {!proto.settings.UserSettings} returns this
*/
proto.settings.UserSettings.prototype.setUserOptions = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.settings.UserSettings} returns this
 */
proto.settings.UserSettings.prototype.clearUserOptions = function() {
  return this.setUserOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.UserSettings.prototype.hasUserOptions = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional uint32 sleep_time_minutes = 2;
 * @return {number}
 */
proto.settings.UserSettings.prototype.getSleepTimeMinutes = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.UserSettings} returns this
 */
proto.settings.UserSettings.prototype.setSleepTimeMinutes = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 display_dimming = 3;
 * @return {number}
 */
proto.settings.UserSettings.prototype.getDisplayDimming = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.UserSettings} returns this
 */
proto.settings.UserSettings.prototype.setDisplayDimming = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional EStatusDisplay last_status_display = 4;
 * @return {!proto.settings.EStatusDisplay}
 */
proto.settings.UserSettings.prototype.getLastStatusDisplay = function() {
  return /** @type {!proto.settings.EStatusDisplay} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {!proto.settings.EStatusDisplay} value
 * @return {!proto.settings.UserSettings} returns this
 */
proto.settings.UserSettings.prototype.setLastStatusDisplay = function(value) {
  return jspb.Message.setProto3EnumField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.PerChannelSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.PerChannelSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.PerChannelSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PerChannelSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
channel: jspb.Message.getFieldWithDefault(msg, 1, 0),
enabled: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
permissivesChmap: jspb.Message.getFieldWithDefault(msg, 3, 0),
multipleIndicationGrnYel: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
multipleIndicationYelRed: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
multipleIndicationGrnRed: jspb.Message.getBooleanFieldWithDefault(msg, 6, false),
lackOfSignal: jspb.Message.getBooleanFieldWithDefault(msg, 7, false),
yellowDisable: jspb.Message.getBooleanFieldWithDefault(msg, 8, false),
minYellowChangeEnable: jspb.Message.getBooleanFieldWithDefault(msg, 9, false),
minYellowPlusRedEnable: jspb.Message.getBooleanFieldWithDefault(msg, 10, false),
fieldCheckRed: jspb.Message.getBooleanFieldWithDefault(msg, 11, false),
fieldCheckYellow: jspb.Message.getBooleanFieldWithDefault(msg, 12, false),
fieldCheckGreen: jspb.Message.getBooleanFieldWithDefault(msg, 13, false),
currentSenseRed: jspb.Message.getBooleanFieldWithDefault(msg, 14, false),
currentSenseYellow: jspb.Message.getBooleanFieldWithDefault(msg, 15, false),
currentSenseGreen: jspb.Message.getBooleanFieldWithDefault(msg, 16, false),
redOnThresholdCurrentMa: jspb.Message.getFieldWithDefault(msg, 17, 0),
yellowOnThresholdCurrentMa: jspb.Message.getFieldWithDefault(msg, 18, 0),
greenOnThresholdCurrentMa: jspb.Message.getFieldWithDefault(msg, 19, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.PerChannelSettings}
 */
proto.settings.PerChannelSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.PerChannelSettings;
  return proto.settings.PerChannelSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.PerChannelSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.PerChannelSettings}
 */
proto.settings.PerChannelSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChannel(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setEnabled(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setPermissivesChmap(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMultipleIndicationGrnYel(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMultipleIndicationYelRed(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMultipleIndicationGrnRed(value);
      break;
    case 7:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setLackOfSignal(value);
      break;
    case 8:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setYellowDisable(value);
      break;
    case 9:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMinYellowChangeEnable(value);
      break;
    case 10:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMinYellowPlusRedEnable(value);
      break;
    case 11:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFieldCheckRed(value);
      break;
    case 12:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFieldCheckYellow(value);
      break;
    case 13:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFieldCheckGreen(value);
      break;
    case 14:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setCurrentSenseRed(value);
      break;
    case 15:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setCurrentSenseYellow(value);
      break;
    case 16:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setCurrentSenseGreen(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRedOnThresholdCurrentMa(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setYellowOnThresholdCurrentMa(value);
      break;
    case 19:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setGreenOnThresholdCurrentMa(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.PerChannelSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.PerChannelSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.PerChannelSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PerChannelSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannel();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEnabled();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getPermissivesChmap();
  if (f !== 0) {
    writer.writeFixed32(
      3,
      f
    );
  }
  f = message.getMultipleIndicationGrnYel();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getMultipleIndicationYelRed();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getMultipleIndicationGrnRed();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
  f = message.getLackOfSignal();
  if (f) {
    writer.writeBool(
      7,
      f
    );
  }
  f = message.getYellowDisable();
  if (f) {
    writer.writeBool(
      8,
      f
    );
  }
  f = message.getMinYellowChangeEnable();
  if (f) {
    writer.writeBool(
      9,
      f
    );
  }
  f = message.getMinYellowPlusRedEnable();
  if (f) {
    writer.writeBool(
      10,
      f
    );
  }
  f = message.getFieldCheckRed();
  if (f) {
    writer.writeBool(
      11,
      f
    );
  }
  f = message.getFieldCheckYellow();
  if (f) {
    writer.writeBool(
      12,
      f
    );
  }
  f = message.getFieldCheckGreen();
  if (f) {
    writer.writeBool(
      13,
      f
    );
  }
  f = message.getCurrentSenseRed();
  if (f) {
    writer.writeBool(
      14,
      f
    );
  }
  f = message.getCurrentSenseYellow();
  if (f) {
    writer.writeBool(
      15,
      f
    );
  }
  f = message.getCurrentSenseGreen();
  if (f) {
    writer.writeBool(
      16,
      f
    );
  }
  f = message.getRedOnThresholdCurrentMa();
  if (f !== 0) {
    writer.writeUint32(
      17,
      f
    );
  }
  f = message.getYellowOnThresholdCurrentMa();
  if (f !== 0) {
    writer.writeUint32(
      18,
      f
    );
  }
  f = message.getGreenOnThresholdCurrentMa();
  if (f !== 0) {
    writer.writeUint32(
      19,
      f
    );
  }
};


/**
 * optional uint32 channel = 1;
 * @return {number}
 */
proto.settings.PerChannelSettings.prototype.getChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bool enabled = 2;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional fixed32 permissives_chmap = 3;
 * @return {number}
 */
proto.settings.PerChannelSettings.prototype.getPermissivesChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setPermissivesChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional bool multiple_indication_grn_yel = 4;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getMultipleIndicationGrnYel = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setMultipleIndicationGrnYel = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional bool multiple_indication_yel_red = 5;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getMultipleIndicationYelRed = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setMultipleIndicationYelRed = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional bool multiple_indication_grn_red = 6;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getMultipleIndicationGrnRed = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 6, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setMultipleIndicationGrnRed = function(value) {
  return jspb.Message.setProto3BooleanField(this, 6, value);
};


/**
 * optional bool lack_of_signal = 7;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getLackOfSignal = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 7, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setLackOfSignal = function(value) {
  return jspb.Message.setProto3BooleanField(this, 7, value);
};


/**
 * optional bool yellow_disable = 8;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getYellowDisable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 8, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setYellowDisable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 8, value);
};


/**
 * optional bool min_yellow_change_enable = 9;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getMinYellowChangeEnable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 9, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setMinYellowChangeEnable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 9, value);
};


/**
 * optional bool min_yellow_plus_red_enable = 10;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getMinYellowPlusRedEnable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 10, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setMinYellowPlusRedEnable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 10, value);
};


/**
 * optional bool field_check_red = 11;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getFieldCheckRed = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 11, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setFieldCheckRed = function(value) {
  return jspb.Message.setProto3BooleanField(this, 11, value);
};


/**
 * optional bool field_check_yellow = 12;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getFieldCheckYellow = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 12, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setFieldCheckYellow = function(value) {
  return jspb.Message.setProto3BooleanField(this, 12, value);
};


/**
 * optional bool field_check_green = 13;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getFieldCheckGreen = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 13, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setFieldCheckGreen = function(value) {
  return jspb.Message.setProto3BooleanField(this, 13, value);
};


/**
 * optional bool current_sense_red = 14;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getCurrentSenseRed = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 14, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setCurrentSenseRed = function(value) {
  return jspb.Message.setProto3BooleanField(this, 14, value);
};


/**
 * optional bool current_sense_yellow = 15;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getCurrentSenseYellow = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 15, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setCurrentSenseYellow = function(value) {
  return jspb.Message.setProto3BooleanField(this, 15, value);
};


/**
 * optional bool current_sense_green = 16;
 * @return {boolean}
 */
proto.settings.PerChannelSettings.prototype.getCurrentSenseGreen = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 16, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setCurrentSenseGreen = function(value) {
  return jspb.Message.setProto3BooleanField(this, 16, value);
};


/**
 * optional uint32 red_on_threshold_current_ma = 17;
 * @return {number}
 */
proto.settings.PerChannelSettings.prototype.getRedOnThresholdCurrentMa = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 17, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setRedOnThresholdCurrentMa = function(value) {
  return jspb.Message.setProto3IntField(this, 17, value);
};


/**
 * optional uint32 yellow_on_threshold_current_ma = 18;
 * @return {number}
 */
proto.settings.PerChannelSettings.prototype.getYellowOnThresholdCurrentMa = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setYellowOnThresholdCurrentMa = function(value) {
  return jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * optional uint32 green_on_threshold_current_ma = 19;
 * @return {number}
 */
proto.settings.PerChannelSettings.prototype.getGreenOnThresholdCurrentMa = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 19, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelSettings} returns this
 */
proto.settings.PerChannelSettings.prototype.setGreenOnThresholdCurrentMa = function(value) {
  return jspb.Message.setProto3IntField(this, 19, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.PerChannelCurrentSenseSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.PerChannelCurrentSenseSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PerChannelCurrentSenseSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
channel: jspb.Message.getFieldWithDefault(msg, 1, 0),
redOnThresholdMa: jspb.Message.getFieldWithDefault(msg, 2, 0),
yellowOnThresholdMa: jspb.Message.getFieldWithDefault(msg, 3, 0),
greenOnThresholdMa: jspb.Message.getFieldWithDefault(msg, 4, 0),
walkOnThresholdMa: (f = jspb.Message.getField(msg, 5)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.PerChannelCurrentSenseSettings}
 */
proto.settings.PerChannelCurrentSenseSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.PerChannelCurrentSenseSettings;
  return proto.settings.PerChannelCurrentSenseSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.PerChannelCurrentSenseSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.PerChannelCurrentSenseSettings}
 */
proto.settings.PerChannelCurrentSenseSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChannel(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRedOnThresholdMa(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setYellowOnThresholdMa(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setGreenOnThresholdMa(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setWalkOnThresholdMa(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.PerChannelCurrentSenseSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.PerChannelCurrentSenseSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PerChannelCurrentSenseSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannel();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getRedOnThresholdMa();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getYellowOnThresholdMa();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getGreenOnThresholdMa();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 5));
  if (f != null) {
    writer.writeUint32(
      5,
      f
    );
  }
};


/**
 * optional uint32 channel = 1;
 * @return {number}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.getChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelCurrentSenseSettings} returns this
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.setChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 red_on_threshold_ma = 2;
 * @return {number}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.getRedOnThresholdMa = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelCurrentSenseSettings} returns this
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.setRedOnThresholdMa = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 yellow_on_threshold_ma = 3;
 * @return {number}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.getYellowOnThresholdMa = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelCurrentSenseSettings} returns this
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.setYellowOnThresholdMa = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 green_on_threshold_ma = 4;
 * @return {number}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.getGreenOnThresholdMa = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelCurrentSenseSettings} returns this
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.setGreenOnThresholdMa = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 walk_on_threshold_ma = 5;
 * @return {number}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.getWalkOnThresholdMa = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelCurrentSenseSettings} returns this
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.setWalkOnThresholdMa = function(value) {
  return jspb.Message.setField(this, 5, value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.settings.PerChannelCurrentSenseSettings} returns this
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.clearWalkOnThresholdMa = function() {
  return jspb.Message.setField(this, 5, undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.settings.PerChannelCurrentSenseSettings.prototype.hasWalkOnThresholdMa = function() {
  return jspb.Message.getField(this, 5) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.PerChannelPermissives.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.PerChannelPermissives.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.PerChannelPermissives} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PerChannelPermissives.toObject = function(includeInstance, msg) {
  var f, obj = {
channel: jspb.Message.getFieldWithDefault(msg, 1, 0),
permissivesChmap: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.PerChannelPermissives}
 */
proto.settings.PerChannelPermissives.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.PerChannelPermissives;
  return proto.settings.PerChannelPermissives.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.PerChannelPermissives} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.PerChannelPermissives}
 */
proto.settings.PerChannelPermissives.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChannel(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setPermissivesChmap(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.PerChannelPermissives.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.PerChannelPermissives.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.PerChannelPermissives} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PerChannelPermissives.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannel();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getPermissivesChmap();
  if (f !== 0) {
    writer.writeFixed32(
      2,
      f
    );
  }
};


/**
 * optional uint32 channel = 1;
 * @return {number}
 */
proto.settings.PerChannelPermissives.prototype.getChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelPermissives} returns this
 */
proto.settings.PerChannelPermissives.prototype.setChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional fixed32 permissives_chmap = 2;
 * @return {number}
 */
proto.settings.PerChannelPermissives.prototype.getPermissivesChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.PerChannelPermissives} returns this
 */
proto.settings.PerChannelPermissives.prototype.setPermissivesChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.FlashingYellowArrowSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.FlashingYellowArrowSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.FlashingYellowArrowSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FlashingYellowArrowSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
instance: jspb.Message.getFieldWithDefault(msg, 1, 0),
enabled: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
mode: jspb.Message.getFieldWithDefault(msg, 3, 0),
flashRateDetectEnabled: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
yellowTrapDetectEnabled: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
overlapChannel: jspb.Message.getFieldWithDefault(msg, 6, 0),
leftTurnChannel: jspb.Message.getFieldWithDefault(msg, 7, 0),
opposingThruChannel: jspb.Message.getFieldWithDefault(msg, 8, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.FlashingYellowArrowSettings}
 */
proto.settings.FlashingYellowArrowSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.FlashingYellowArrowSettings;
  return proto.settings.FlashingYellowArrowSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.FlashingYellowArrowSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.FlashingYellowArrowSettings}
 */
proto.settings.FlashingYellowArrowSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setInstance(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setEnabled(value);
      break;
    case 3:
      var value = /** @type {!proto.settings.EFlashingYellowArrowMode} */ (reader.readEnum());
      msg.setMode(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFlashRateDetectEnabled(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setYellowTrapDetectEnabled(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOverlapChannel(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLeftTurnChannel(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOpposingThruChannel(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.FlashingYellowArrowSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.FlashingYellowArrowSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.FlashingYellowArrowSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FlashingYellowArrowSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getInstance();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getEnabled();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getMode();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getFlashRateDetectEnabled();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getYellowTrapDetectEnabled();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getOverlapChannel();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
  f = message.getLeftTurnChannel();
  if (f !== 0) {
    writer.writeUint32(
      7,
      f
    );
  }
  f = message.getOpposingThruChannel();
  if (f !== 0) {
    writer.writeUint32(
      8,
      f
    );
  }
};


/**
 * optional uint32 instance = 1;
 * @return {number}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getInstance = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setInstance = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional bool enabled = 2;
 * @return {boolean}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional EFlashingYellowArrowMode mode = 3;
 * @return {!proto.settings.EFlashingYellowArrowMode}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getMode = function() {
  return /** @type {!proto.settings.EFlashingYellowArrowMode} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.settings.EFlashingYellowArrowMode} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setMode = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional bool flash_rate_detect_enabled = 4;
 * @return {boolean}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getFlashRateDetectEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setFlashRateDetectEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional bool yellow_trap_detect_enabled = 5;
 * @return {boolean}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getYellowTrapDetectEnabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setYellowTrapDetectEnabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional uint32 overlap_channel = 6;
 * @return {number}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getOverlapChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setOverlapChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional uint32 left_turn_channel = 7;
 * @return {number}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getLeftTurnChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setLeftTurnChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional uint32 opposing_thru_channel = 8;
 * @return {number}
 */
proto.settings.FlashingYellowArrowSettings.prototype.getOpposingThruChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.FlashingYellowArrowSettings} returns this
 */
proto.settings.FlashingYellowArrowSettings.prototype.setOpposingThruChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.FlashAreaStatistics.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.FlashAreaStatistics.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.FlashAreaStatistics} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FlashAreaStatistics.toObject = function(includeInstance, msg) {
  var f, obj = {
calculatedChecksum: jspb.Message.getFieldWithDefault(msg, 1, 0),
storedChecksum: jspb.Message.getFieldWithDefault(msg, 2, 0),
sizeInBytes: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.FlashAreaStatistics}
 */
proto.settings.FlashAreaStatistics.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.FlashAreaStatistics;
  return proto.settings.FlashAreaStatistics.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.FlashAreaStatistics} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.FlashAreaStatistics}
 */
proto.settings.FlashAreaStatistics.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setCalculatedChecksum(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setStoredChecksum(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSizeInBytes(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.FlashAreaStatistics.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.FlashAreaStatistics.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.FlashAreaStatistics} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FlashAreaStatistics.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCalculatedChecksum();
  if (f !== 0) {
    writer.writeFixed32(
      1,
      f
    );
  }
  f = message.getStoredChecksum();
  if (f !== 0) {
    writer.writeFixed32(
      2,
      f
    );
  }
  f = message.getSizeInBytes();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
};


/**
 * optional fixed32 calculated_checksum = 1;
 * @return {number}
 */
proto.settings.FlashAreaStatistics.prototype.getCalculatedChecksum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.FlashAreaStatistics} returns this
 */
proto.settings.FlashAreaStatistics.prototype.setCalculatedChecksum = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional fixed32 stored_checksum = 2;
 * @return {number}
 */
proto.settings.FlashAreaStatistics.prototype.getStoredChecksum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.FlashAreaStatistics} returns this
 */
proto.settings.FlashAreaStatistics.prototype.setStoredChecksum = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 size_in_bytes = 3;
 * @return {number}
 */
proto.settings.FlashAreaStatistics.prototype.getSizeInBytes = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.settings.FlashAreaStatistics} returns this
 */
proto.settings.FlashAreaStatistics.prototype.setSizeInBytes = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.PcbOptionsMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.PcbOptionsMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.PcbOptionsMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PcbOptionsMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
option1: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
option2: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
option3: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
option4: jspb.Message.getBooleanFieldWithDefault(msg, 4, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.PcbOptionsMmu}
 */
proto.settings.PcbOptionsMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.PcbOptionsMmu;
  return proto.settings.PcbOptionsMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.PcbOptionsMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.PcbOptionsMmu}
 */
proto.settings.PcbOptionsMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOption1(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOption2(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOption3(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOption4(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.PcbOptionsMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.PcbOptionsMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.PcbOptionsMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.PcbOptionsMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOption1();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getOption2();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getOption3();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getOption4();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
};


/**
 * optional bool option1 = 1;
 * @return {boolean}
 */
proto.settings.PcbOptionsMmu.prototype.getOption1 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PcbOptionsMmu} returns this
 */
proto.settings.PcbOptionsMmu.prototype.setOption1 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool option2 = 2;
 * @return {boolean}
 */
proto.settings.PcbOptionsMmu.prototype.getOption2 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PcbOptionsMmu} returns this
 */
proto.settings.PcbOptionsMmu.prototype.setOption2 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional bool option3 = 3;
 * @return {boolean}
 */
proto.settings.PcbOptionsMmu.prototype.getOption3 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PcbOptionsMmu} returns this
 */
proto.settings.PcbOptionsMmu.prototype.setOption3 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional bool option4 = 4;
 * @return {boolean}
 */
proto.settings.PcbOptionsMmu.prototype.getOption4 = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.PcbOptionsMmu} returns this
 */
proto.settings.PcbOptionsMmu.prototype.setOption4 = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.FactoryOptionsMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.FactoryOptionsMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.FactoryOptionsMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FactoryOptionsMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
oemType: jspb.Message.getFieldWithDefault(msg, 1, 0),
ch16WWalk: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
canadianFeatures: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
lsuEnable: jspb.Message.getBooleanFieldWithDefault(msg, 4, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.FactoryOptionsMmu}
 */
proto.settings.FactoryOptionsMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.FactoryOptionsMmu;
  return proto.settings.FactoryOptionsMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.FactoryOptionsMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.FactoryOptionsMmu}
 */
proto.settings.FactoryOptionsMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EOemType} */ (reader.readEnum());
      msg.setOemType(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setCh16WWalk(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setCanadianFeatures(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setLsuEnable(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.FactoryOptionsMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.FactoryOptionsMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.FactoryOptionsMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.FactoryOptionsMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOemType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getCh16WWalk();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getCanadianFeatures();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getLsuEnable();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
};


/**
 * optional EOemType oem_type = 1;
 * @return {!proto.settings.EOemType}
 */
proto.settings.FactoryOptionsMmu.prototype.getOemType = function() {
  return /** @type {!proto.settings.EOemType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EOemType} value
 * @return {!proto.settings.FactoryOptionsMmu} returns this
 */
proto.settings.FactoryOptionsMmu.prototype.setOemType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bool ch_16_w_walk = 2;
 * @return {boolean}
 */
proto.settings.FactoryOptionsMmu.prototype.getCh16WWalk = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.FactoryOptionsMmu} returns this
 */
proto.settings.FactoryOptionsMmu.prototype.setCh16WWalk = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional bool canadian_features = 3;
 * @return {boolean}
 */
proto.settings.FactoryOptionsMmu.prototype.getCanadianFeatures = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.FactoryOptionsMmu} returns this
 */
proto.settings.FactoryOptionsMmu.prototype.setCanadianFeatures = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional bool lsu_enable = 4;
 * @return {boolean}
 */
proto.settings.FactoryOptionsMmu.prototype.getLsuEnable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.FactoryOptionsMmu} returns this
 */
proto.settings.FactoryOptionsMmu.prototype.setLsuEnable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.settings.AgencyOptionsMmu.prototype.toObject = function(opt_includeInstance) {
  return proto.settings.AgencyOptionsMmu.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.settings.AgencyOptionsMmu} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.AgencyOptionsMmu.toObject = function(includeInstance, msg) {
  var f, obj = {
mustLogInToEdit: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
mustLogInToView: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
noFpConfigChange: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
permitRemoteConfigAcceptance: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
permitRemoteFaultClear: jspb.Message.getBooleanFieldWithDefault(msg, 5, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.settings.AgencyOptionsMmu}
 */
proto.settings.AgencyOptionsMmu.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.settings.AgencyOptionsMmu;
  return proto.settings.AgencyOptionsMmu.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.settings.AgencyOptionsMmu} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.settings.AgencyOptionsMmu}
 */
proto.settings.AgencyOptionsMmu.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMustLogInToEdit(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setMustLogInToView(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setNoFpConfigChange(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setPermitRemoteConfigAcceptance(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setPermitRemoteFaultClear(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.settings.AgencyOptionsMmu.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.settings.AgencyOptionsMmu.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.settings.AgencyOptionsMmu} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.settings.AgencyOptionsMmu.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMustLogInToEdit();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getMustLogInToView();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getNoFpConfigChange();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getPermitRemoteConfigAcceptance();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getPermitRemoteFaultClear();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
};


/**
 * optional bool must_log_in_to_edit = 1;
 * @return {boolean}
 */
proto.settings.AgencyOptionsMmu.prototype.getMustLogInToEdit = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.AgencyOptionsMmu} returns this
 */
proto.settings.AgencyOptionsMmu.prototype.setMustLogInToEdit = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool must_log_in_to_view = 2;
 * @return {boolean}
 */
proto.settings.AgencyOptionsMmu.prototype.getMustLogInToView = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.AgencyOptionsMmu} returns this
 */
proto.settings.AgencyOptionsMmu.prototype.setMustLogInToView = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional bool no_fp_config_change = 3;
 * @return {boolean}
 */
proto.settings.AgencyOptionsMmu.prototype.getNoFpConfigChange = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.AgencyOptionsMmu} returns this
 */
proto.settings.AgencyOptionsMmu.prototype.setNoFpConfigChange = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional bool permit_remote_config_acceptance = 4;
 * @return {boolean}
 */
proto.settings.AgencyOptionsMmu.prototype.getPermitRemoteConfigAcceptance = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.AgencyOptionsMmu} returns this
 */
proto.settings.AgencyOptionsMmu.prototype.setPermitRemoteConfigAcceptance = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional bool permit_remote_fault_clear = 5;
 * @return {boolean}
 */
proto.settings.AgencyOptionsMmu.prototype.getPermitRemoteFaultClear = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.settings.AgencyOptionsMmu} returns this
 */
proto.settings.AgencyOptionsMmu.prototype.setPermitRemoteFaultClear = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * @enum {number}
 */
proto.settings.EStatusDisplay = {
  STATUS_DISPLAY_UNSPECIFIED: 0,
  STATUS_DISPLAY_MAIN: 1,
  STATUS_DISPLAY_FAULT: 2,
  STATUS_DISPLAY_ETHERNET: 3,
  STATUS_DISPLAY_POWER: 4,
  STATUS_DISPLAY_FW_VERSIONS: 5
};

/**
 * @enum {number}
 */
proto.settings.EDisableOverrideState = {
  DISABLE_OVERRIDE_STATE_UNSPECIFIED: 0,
  DISABLE_OVERRIDE_STATE_NONE: 1,
  DISABLE_OVERRIDE_STATE_FORCE_ON: 2,
  DISABLE_OVERRIDE_STATE_FORCE_OFF: 3
};

/**
 * @enum {number}
 */
proto.settings.EFlashingYellowArrowMode = {
  FYA_MODE_UNSPECIFIED: 0,
  FYA_MODE_A: 1,
  FYA_MODE_B: 2,
  FYA_MODE_C: 3,
  FYA_MODE_D: 4,
  FYA_MODE_E: 5,
  FYA_MODE_F: 6,
  FYA_MODE_G: 7,
  FYA_MODE_H: 8,
  FYA_MODE_I: 9,
  FYA_MODE_J: 10,
  FYA_MODE_K: 11,
  FYA_MODE_L: 12,
  FYA_MODE_M: 13,
  FYA_MODE_N: 14,
  FYA_MODE_O: 15,
  FYA_MODE_P: 16
};

/**
 * @enum {number}
 */
proto.settings.EConfigDataLocation = {
  CONFIG_DATA_LOC_UNSPECIFIED: 0,
  CONFIG_DATA_LOC_JUMPERED_PROGRAM_CARD: 1,
  CONFIG_DATA_LOC_JUMPERLESS_PROGRAM_CARD: 2,
  CONFIG_DATA_LOC_DATA_KEY: 3,
  CONFIG_DATA_LOC_LEGACY_EDI: 4,
  CONFIG_DATA_LOC_LEGACY_RENO: 5,
  CONFIG_DATA_LOC_LEGACY_NO_MEM: 6,
  CONFIG_DATA_LOC_NONE: 255
};

/**
 * @enum {number}
 */
proto.settings.EPcDkReadType = {
  PC_DK_UNSPECIFIED: 0,
  PC_DK_NONE: 1,
  PC_DK_CURRENT: 2,
  PC_DK_PENDING: 3
};

/**
 * @enum {number}
 */
proto.settings.EWriteResult = {
  WRITE_RESULT_UNSPECIFIED: 0,
  WRITE_RESULT_SUCCESS: 1,
  WRITE_RESULT_CRC_INVALID: 2,
  WRITE_RESULT_DESTINATION_UNAVAILABLE: 3,
  WRITE_RESULT_WRITE_PROTECTED: 4,
  WRITE_RESULT_WRITE_PCB_OPTION_MISMATCH: 5,
  WRITE_RESULT_WRITE_OUT_OF_RANGE: 6,
  WRITE_RESULT_WRITE_INVALID_DATA: 7,
  WRITE_RESULT_BUSY: 8,
  WRITE_RESULT_NOT_PERMITTED: 9
};

/**
 * @enum {number}
 */
proto.settings.EMonitorStatistics = {
  STATISTICS_UNSPECIFIED: 0,
  STATISTICS_ALL: 1,
  STATISTICS_PORT1: 2,
  STATISTICS_DATA_KEY: 3,
  STATISTICS_MAIN_TO_ISOLATED: 4,
  STATISTICS_MAIN_TO_COMMS: 5,
  STATISTICS_COMMS_TO_MAIN: 6
};

/**
 * @enum {number}
 */
proto.settings.ETimeSetResult = {
  TIME_SET_UNSPECIFIED: 0,
  TIME_SET_SUCCESS: 1,
  TIME_SET_DATE_TIME_OUT_OF_RANGE: 2,
  TIME_SET_DST_OUT_OF_RANGE: 3,
  TIME_SET_LOG_ERR: 4
};

/**
 * @enum {number}
 */
proto.settings.EOemType = {
  OEM_TYPE_UNSPECIFIED: 0,
  OEM_TYPE_EDI: 1,
  OEM_TYPE_RENO: 2,
  OEM_TYPE_MCCAIN: 3,
  OEM_TYPE_ECONOLITE: 4,
  OEM_TYPE_SAFETRAN: 5,
  OEM_TYPE_ORIUX: 6,
  OEM_TYPE_MOBOTREX: 7,
  OEM_TYPE_CUBIC: 8
};

/**
 * @enum {number}
 */
proto.settings.ERemoteResetType = {
  REMOTE_RESET_TYPE_UNSPECIFIED: 0,
  REMOTE_RESET_TYPE_FAULT_CLEAR: 1,
  REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE: 2
};

goog.object.extend(exports, proto.settings);
