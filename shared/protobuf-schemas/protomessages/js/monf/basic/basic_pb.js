// source: basic.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

goog.exportSymbol('proto.basic.DateStr', null, global);
goog.exportSymbol('proto.basic.DaylightSavingsSettings', null, global);
goog.exportSymbol('proto.basic.EDaysOfTheWeek', null, global);
goog.exportSymbol('proto.basic.IpAddressV4', null, global);
goog.exportSymbol('proto.basic.LocalDateTime', null, global);
goog.exportSymbol('proto.basic.ModelAndSerialNumber', null, global);
goog.exportSymbol('proto.basic.MonitorAndUserIds', null, global);
goog.exportSymbol('proto.basic.NowMinMaxFloat', null, global);
goog.exportSymbol('proto.basic.VersionStrThree', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.LocalDateTime = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.LocalDateTime, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.LocalDateTime.displayName = 'proto.basic.LocalDateTime';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.VersionStrThree = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.VersionStrThree, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.VersionStrThree.displayName = 'proto.basic.VersionStrThree';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.DateStr = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.DateStr, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.DateStr.displayName = 'proto.basic.DateStr';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.IpAddressV4 = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.IpAddressV4, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.IpAddressV4.displayName = 'proto.basic.IpAddressV4';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.NowMinMaxFloat = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.NowMinMaxFloat, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.NowMinMaxFloat.displayName = 'proto.basic.NowMinMaxFloat';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.ModelAndSerialNumber = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.ModelAndSerialNumber, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.ModelAndSerialNumber.displayName = 'proto.basic.ModelAndSerialNumber';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.MonitorAndUserIds = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.MonitorAndUserIds, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.MonitorAndUserIds.displayName = 'proto.basic.MonitorAndUserIds';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.basic.DaylightSavingsSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.basic.DaylightSavingsSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.basic.DaylightSavingsSettings.displayName = 'proto.basic.DaylightSavingsSettings';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.LocalDateTime.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.LocalDateTime.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.LocalDateTime} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.LocalDateTime.toObject = function(includeInstance, msg) {
  var f, obj = {
year: jspb.Message.getFieldWithDefault(msg, 1, 0),
month: jspb.Message.getFieldWithDefault(msg, 2, 0),
day: jspb.Message.getFieldWithDefault(msg, 3, 0),
hour: jspb.Message.getFieldWithDefault(msg, 4, 0),
minute: jspb.Message.getFieldWithDefault(msg, 5, 0),
second: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.LocalDateTime}
 */
proto.basic.LocalDateTime.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.LocalDateTime;
  return proto.basic.LocalDateTime.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.LocalDateTime} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.LocalDateTime}
 */
proto.basic.LocalDateTime.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setYear(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMonth(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setDay(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setHour(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMinute(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSecond(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.LocalDateTime.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.LocalDateTime.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.LocalDateTime} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.LocalDateTime.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getYear();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getMonth();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getDay();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getHour();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getMinute();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getSecond();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
};


/**
 * optional uint32 year = 1;
 * @return {number}
 */
proto.basic.LocalDateTime.prototype.getYear = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.LocalDateTime} returns this
 */
proto.basic.LocalDateTime.prototype.setYear = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 month = 2;
 * @return {number}
 */
proto.basic.LocalDateTime.prototype.getMonth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.LocalDateTime} returns this
 */
proto.basic.LocalDateTime.prototype.setMonth = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 day = 3;
 * @return {number}
 */
proto.basic.LocalDateTime.prototype.getDay = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.LocalDateTime} returns this
 */
proto.basic.LocalDateTime.prototype.setDay = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 hour = 4;
 * @return {number}
 */
proto.basic.LocalDateTime.prototype.getHour = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.LocalDateTime} returns this
 */
proto.basic.LocalDateTime.prototype.setHour = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 minute = 5;
 * @return {number}
 */
proto.basic.LocalDateTime.prototype.getMinute = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.LocalDateTime} returns this
 */
proto.basic.LocalDateTime.prototype.setMinute = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional uint32 second = 6;
 * @return {number}
 */
proto.basic.LocalDateTime.prototype.getSecond = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.LocalDateTime} returns this
 */
proto.basic.LocalDateTime.prototype.setSecond = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.VersionStrThree.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.VersionStrThree.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.VersionStrThree} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.VersionStrThree.toObject = function(includeInstance, msg) {
  var f, obj = {
major: jspb.Message.getFieldWithDefault(msg, 1, ""),
minor: jspb.Message.getFieldWithDefault(msg, 2, ""),
revision: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.VersionStrThree}
 */
proto.basic.VersionStrThree.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.VersionStrThree;
  return proto.basic.VersionStrThree.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.VersionStrThree} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.VersionStrThree}
 */
proto.basic.VersionStrThree.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setMajor(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setMinor(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setRevision(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.VersionStrThree.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.VersionStrThree.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.VersionStrThree} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.VersionStrThree.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMajor();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getMinor();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getRevision();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string major = 1;
 * @return {string}
 */
proto.basic.VersionStrThree.prototype.getMajor = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.VersionStrThree} returns this
 */
proto.basic.VersionStrThree.prototype.setMajor = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string minor = 2;
 * @return {string}
 */
proto.basic.VersionStrThree.prototype.getMinor = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.VersionStrThree} returns this
 */
proto.basic.VersionStrThree.prototype.setMinor = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string revision = 3;
 * @return {string}
 */
proto.basic.VersionStrThree.prototype.getRevision = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.VersionStrThree} returns this
 */
proto.basic.VersionStrThree.prototype.setRevision = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.DateStr.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.DateStr.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.DateStr} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.DateStr.toObject = function(includeInstance, msg) {
  var f, obj = {
month: jspb.Message.getFieldWithDefault(msg, 1, ""),
day: jspb.Message.getFieldWithDefault(msg, 2, ""),
year: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.DateStr}
 */
proto.basic.DateStr.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.DateStr;
  return proto.basic.DateStr.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.DateStr} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.DateStr}
 */
proto.basic.DateStr.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setMonth(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setDay(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setYear(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.DateStr.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.DateStr.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.DateStr} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.DateStr.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMonth();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getDay();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getYear();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string month = 1;
 * @return {string}
 */
proto.basic.DateStr.prototype.getMonth = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.DateStr} returns this
 */
proto.basic.DateStr.prototype.setMonth = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string day = 2;
 * @return {string}
 */
proto.basic.DateStr.prototype.getDay = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.DateStr} returns this
 */
proto.basic.DateStr.prototype.setDay = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string year = 3;
 * @return {string}
 */
proto.basic.DateStr.prototype.getYear = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.DateStr} returns this
 */
proto.basic.DateStr.prototype.setYear = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.IpAddressV4.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.IpAddressV4.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.IpAddressV4} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.IpAddressV4.toObject = function(includeInstance, msg) {
  var f, obj = {
octet1Mso: jspb.Message.getFieldWithDefault(msg, 1, 0),
octet2: jspb.Message.getFieldWithDefault(msg, 2, 0),
octet3: jspb.Message.getFieldWithDefault(msg, 3, 0),
octet4Lso: jspb.Message.getFieldWithDefault(msg, 4, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.IpAddressV4}
 */
proto.basic.IpAddressV4.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.IpAddressV4;
  return proto.basic.IpAddressV4.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.IpAddressV4} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.IpAddressV4}
 */
proto.basic.IpAddressV4.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOctet1Mso(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOctet2(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOctet3(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setOctet4Lso(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.IpAddressV4.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.IpAddressV4.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.IpAddressV4} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.IpAddressV4.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getOctet1Mso();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getOctet2();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getOctet3();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getOctet4Lso();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
};


/**
 * optional uint32 octet_1_mso = 1;
 * @return {number}
 */
proto.basic.IpAddressV4.prototype.getOctet1Mso = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.IpAddressV4} returns this
 */
proto.basic.IpAddressV4.prototype.setOctet1Mso = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 octet_2 = 2;
 * @return {number}
 */
proto.basic.IpAddressV4.prototype.getOctet2 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.IpAddressV4} returns this
 */
proto.basic.IpAddressV4.prototype.setOctet2 = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional uint32 octet_3 = 3;
 * @return {number}
 */
proto.basic.IpAddressV4.prototype.getOctet3 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.IpAddressV4} returns this
 */
proto.basic.IpAddressV4.prototype.setOctet3 = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 octet_4_lso = 4;
 * @return {number}
 */
proto.basic.IpAddressV4.prototype.getOctet4Lso = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.IpAddressV4} returns this
 */
proto.basic.IpAddressV4.prototype.setOctet4Lso = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.NowMinMaxFloat.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.NowMinMaxFloat.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.NowMinMaxFloat} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.NowMinMaxFloat.toObject = function(includeInstance, msg) {
  var f, obj = {
present: jspb.Message.getFloatingPointFieldWithDefault(msg, 1, 0.0),
minimum: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
maximum: jspb.Message.getFloatingPointFieldWithDefault(msg, 3, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.NowMinMaxFloat}
 */
proto.basic.NowMinMaxFloat.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.NowMinMaxFloat;
  return proto.basic.NowMinMaxFloat.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.NowMinMaxFloat} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.NowMinMaxFloat}
 */
proto.basic.NowMinMaxFloat.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setPresent(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setMinimum(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setMaximum(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.NowMinMaxFloat.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.NowMinMaxFloat.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.NowMinMaxFloat} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.NowMinMaxFloat.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPresent();
  if (f !== 0.0) {
    writer.writeFloat(
      1,
      f
    );
  }
  f = message.getMinimum();
  if (f !== 0.0) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = message.getMaximum();
  if (f !== 0.0) {
    writer.writeFloat(
      3,
      f
    );
  }
};


/**
 * optional float present = 1;
 * @return {number}
 */
proto.basic.NowMinMaxFloat.prototype.getPresent = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 1, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.basic.NowMinMaxFloat} returns this
 */
proto.basic.NowMinMaxFloat.prototype.setPresent = function(value) {
  return jspb.Message.setProto3FloatField(this, 1, value);
};


/**
 * optional float minimum = 2;
 * @return {number}
 */
proto.basic.NowMinMaxFloat.prototype.getMinimum = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.basic.NowMinMaxFloat} returns this
 */
proto.basic.NowMinMaxFloat.prototype.setMinimum = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional float maximum = 3;
 * @return {number}
 */
proto.basic.NowMinMaxFloat.prototype.getMaximum = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.basic.NowMinMaxFloat} returns this
 */
proto.basic.NowMinMaxFloat.prototype.setMaximum = function(value) {
  return jspb.Message.setProto3FloatField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.ModelAndSerialNumber.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.ModelAndSerialNumber.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.ModelAndSerialNumber} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.ModelAndSerialNumber.toObject = function(includeInstance, msg) {
  var f, obj = {
serial: jspb.Message.getFieldWithDefault(msg, 1, ""),
model: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.ModelAndSerialNumber}
 */
proto.basic.ModelAndSerialNumber.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.ModelAndSerialNumber;
  return proto.basic.ModelAndSerialNumber.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.ModelAndSerialNumber} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.ModelAndSerialNumber}
 */
proto.basic.ModelAndSerialNumber.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSerial(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setModel(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.ModelAndSerialNumber.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.ModelAndSerialNumber.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.ModelAndSerialNumber} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.ModelAndSerialNumber.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSerial();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getModel();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string serial = 1;
 * @return {string}
 */
proto.basic.ModelAndSerialNumber.prototype.getSerial = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.ModelAndSerialNumber} returns this
 */
proto.basic.ModelAndSerialNumber.prototype.setSerial = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string model = 2;
 * @return {string}
 */
proto.basic.ModelAndSerialNumber.prototype.getModel = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.ModelAndSerialNumber} returns this
 */
proto.basic.ModelAndSerialNumber.prototype.setModel = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.MonitorAndUserIds.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.MonitorAndUserIds.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.MonitorAndUserIds} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.MonitorAndUserIds.toObject = function(includeInstance, msg) {
  var f, obj = {
monitor: jspb.Message.getFieldWithDefault(msg, 1, ""),
user: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.MonitorAndUserIds}
 */
proto.basic.MonitorAndUserIds.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.MonitorAndUserIds;
  return proto.basic.MonitorAndUserIds.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.MonitorAndUserIds} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.MonitorAndUserIds}
 */
proto.basic.MonitorAndUserIds.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setMonitor(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUser(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.MonitorAndUserIds.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.MonitorAndUserIds.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.MonitorAndUserIds} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.MonitorAndUserIds.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getMonitor();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUser();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string monitor = 1;
 * @return {string}
 */
proto.basic.MonitorAndUserIds.prototype.getMonitor = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.MonitorAndUserIds} returns this
 */
proto.basic.MonitorAndUserIds.prototype.setMonitor = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string user = 2;
 * @return {string}
 */
proto.basic.MonitorAndUserIds.prototype.getUser = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.basic.MonitorAndUserIds} returns this
 */
proto.basic.MonitorAndUserIds.prototype.setUser = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.basic.DaylightSavingsSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.basic.DaylightSavingsSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.basic.DaylightSavingsSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.DaylightSavingsSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
startWeekOfMonth: jspb.Message.getFieldWithDefault(msg, 1, 0),
startDayOfWeek: jspb.Message.getFieldWithDefault(msg, 2, 0),
startMonthOfYear: jspb.Message.getFieldWithDefault(msg, 3, 0),
endWeekOfMonth: jspb.Message.getFieldWithDefault(msg, 4, 0),
endDayOfWeek: jspb.Message.getFieldWithDefault(msg, 5, 0),
endMonthOfYear: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.basic.DaylightSavingsSettings}
 */
proto.basic.DaylightSavingsSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.basic.DaylightSavingsSettings;
  return proto.basic.DaylightSavingsSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.basic.DaylightSavingsSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.basic.DaylightSavingsSettings}
 */
proto.basic.DaylightSavingsSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setStartWeekOfMonth(value);
      break;
    case 2:
      var value = /** @type {!proto.basic.EDaysOfTheWeek} */ (reader.readEnum());
      msg.setStartDayOfWeek(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setStartMonthOfYear(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEndWeekOfMonth(value);
      break;
    case 5:
      var value = /** @type {!proto.basic.EDaysOfTheWeek} */ (reader.readEnum());
      msg.setEndDayOfWeek(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setEndMonthOfYear(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.basic.DaylightSavingsSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.basic.DaylightSavingsSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.basic.DaylightSavingsSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.basic.DaylightSavingsSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStartWeekOfMonth();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getStartDayOfWeek();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getStartMonthOfYear();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
  f = message.getEndWeekOfMonth();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getEndDayOfWeek();
  if (f !== 0.0) {
    writer.writeEnum(
      5,
      f
    );
  }
  f = message.getEndMonthOfYear();
  if (f !== 0) {
    writer.writeUint32(
      6,
      f
    );
  }
};


/**
 * optional uint32 start_week_of_month = 1;
 * @return {number}
 */
proto.basic.DaylightSavingsSettings.prototype.getStartWeekOfMonth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.DaylightSavingsSettings} returns this
 */
proto.basic.DaylightSavingsSettings.prototype.setStartWeekOfMonth = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional EDaysOfTheWeek start_day_of_week = 2;
 * @return {!proto.basic.EDaysOfTheWeek}
 */
proto.basic.DaylightSavingsSettings.prototype.getStartDayOfWeek = function() {
  return /** @type {!proto.basic.EDaysOfTheWeek} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.basic.EDaysOfTheWeek} value
 * @return {!proto.basic.DaylightSavingsSettings} returns this
 */
proto.basic.DaylightSavingsSettings.prototype.setStartDayOfWeek = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional uint32 start_month_of_year = 3;
 * @return {number}
 */
proto.basic.DaylightSavingsSettings.prototype.getStartMonthOfYear = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.DaylightSavingsSettings} returns this
 */
proto.basic.DaylightSavingsSettings.prototype.setStartMonthOfYear = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional uint32 end_week_of_month = 4;
 * @return {number}
 */
proto.basic.DaylightSavingsSettings.prototype.getEndWeekOfMonth = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.DaylightSavingsSettings} returns this
 */
proto.basic.DaylightSavingsSettings.prototype.setEndWeekOfMonth = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional EDaysOfTheWeek end_day_of_week = 5;
 * @return {!proto.basic.EDaysOfTheWeek}
 */
proto.basic.DaylightSavingsSettings.prototype.getEndDayOfWeek = function() {
  return /** @type {!proto.basic.EDaysOfTheWeek} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {!proto.basic.EDaysOfTheWeek} value
 * @return {!proto.basic.DaylightSavingsSettings} returns this
 */
proto.basic.DaylightSavingsSettings.prototype.setEndDayOfWeek = function(value) {
  return jspb.Message.setProto3EnumField(this, 5, value);
};


/**
 * optional uint32 end_month_of_year = 6;
 * @return {number}
 */
proto.basic.DaylightSavingsSettings.prototype.getEndMonthOfYear = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.basic.DaylightSavingsSettings} returns this
 */
proto.basic.DaylightSavingsSettings.prototype.setEndMonthOfYear = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * @enum {number}
 */
proto.basic.EDaysOfTheWeek = {
  DAY_UNSPECIFIED: 0,
  DAY_SUNDAY: 1,
  DAY_MONDAY: 2,
  DAY_TUESDAY: 3,
  DAY_WEDNESDAY: 4,
  DAY_THURSDAY: 5,
  DAY_FRIDAY: 6,
  DAY_SATURDAY: 7
};

goog.object.extend(exports, proto.basic);
