// source: wrappers.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var cmd_resp_comms_pb = require('./cmd_resp_comms_pb.js');
goog.object.extend(proto, cmd_resp_comms_pb);
var cmd_resp_logs_pb = require('./cmd_resp_logs_pb.js');
goog.object.extend(proto, cmd_resp_logs_pb);
var cmd_resp_config_pb = require('./cmd_resp_config_pb.js');
goog.object.extend(proto, cmd_resp_config_pb);
var cmd_resp_stats_pb = require('./cmd_resp_stats_pb.js');
goog.object.extend(proto, cmd_resp_stats_pb);
var cmd_resp_dfu_pb = require('./cmd_resp_dfu_pb.js');
goog.object.extend(proto, cmd_resp_dfu_pb);
var cmd_resp_realtime_pb = require('./cmd_resp_realtime_pb.js');
goog.object.extend(proto, cmd_resp_realtime_pb);
goog.exportSymbol('proto.wrappers.EResponseCodes', null, global);
goog.exportSymbol('proto.wrappers.WrapperCommand', null, global);
goog.exportSymbol('proto.wrappers.WrapperCommand.CommandCase', null, global);
goog.exportSymbol('proto.wrappers.WrapperResponse', null, global);
goog.exportSymbol('proto.wrappers.WrapperResponse.ResponseCase', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.wrappers.WrapperCommand = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.wrappers.WrapperCommand.oneofGroups_);
};
goog.inherits(proto.wrappers.WrapperCommand, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.wrappers.WrapperCommand.displayName = 'proto.wrappers.WrapperCommand';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.wrappers.WrapperResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.wrappers.WrapperResponse.oneofGroups_);
};
goog.inherits(proto.wrappers.WrapperResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.wrappers.WrapperResponse.displayName = 'proto.wrappers.WrapperResponse';
}

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.wrappers.WrapperCommand.oneofGroups_ = [[20,21,22,23,24,25,26,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,50,51,52,53,54,55,56,57,58,59,65,60,61,70,71,72,73,74,75,90]];

/**
 * @enum {number}
 */
proto.wrappers.WrapperCommand.CommandCase = {
  COMMAND_NOT_SET: 0,
  REQUEST_LOG_COUNTS: 20,
  LOG_CLEAR: 21,
  REQUEST_LOG: 22,
  REQ_AUDITLOG_COUNTS: 23,
  AUDITLOG_CLEAR: 24,
  AUDIT_LOG_RESET: 25,
  REQUEST_AUDITLOG: 26,
  RD_MONITOR_DATA: 31,
  RD_NETWORK_CONFIG_UNIT: 32,
  RD_NETWORK_CONFIG_ACTIVE: 33,
  RD_CHANNEL_CONFIG: 34,
  RD_CHANNEL_CURRENT_SENSE: 35,
  RD_CHANNEL_PERMISSIVES: 36,
  RD_FYA_CONFIG: 37,
  RD_DATA_KEY: 38,
  WR_DATA_KEY: 39,
  RD_FACTORY_SETTINGS: 40,
  WR_FACTORY_SETTINGS: 41,
  RD_USER_SETTINGS: 42,
  WR_USER_SETTINGS: 43,
  RD_PORT1_DISABLES: 44,
  WR_PORT1_DISABLES: 45,
  WR_AGENCY_OPTIONS: 46,
  REMOTE_RESET: 47,
  RD_PORT1_STATS: 50,
  RD_DATA_KEY_STATS: 51,
  RD_MAIN_ISO_COMMS_STATS: 52,
  RD_MAIN_COMMS_STATS: 53,
  RD_COMMS_MAIN_STATS: 54,
  RD_FLASH_STATS: 55,
  RD_WATCHDOG_STATS: 56,
  RD_DATE_TIME_DST: 57,
  CLEAR_STATS: 58,
  WR_DATE_TIME_DST: 59,
  REMOTE_DISPLAY_BUTTON_EVENT: 65,
  START_REALTIME: 60,
  STOP_REALTIME: 61,
  MANIFEST_VERSIONS: 70,
  REBOOT_COMMS_MCU: 71,
  INITIATE_DFU: 72,
  SEND_FW_MANIFEST: 73,
  BEGIN_FIRMWARE_DOWNLOAD: 74,
  DOWNLOAD_FIRMWARE_CHUNK: 75,
  TEST_CHUNK: 90
};

/**
 * @return {proto.wrappers.WrapperCommand.CommandCase}
 */
proto.wrappers.WrapperCommand.prototype.getCommandCase = function() {
  return /** @type {proto.wrappers.WrapperCommand.CommandCase} */(jspb.Message.computeOneofCase(this, proto.wrappers.WrapperCommand.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.wrappers.WrapperCommand.prototype.toObject = function(opt_includeInstance) {
  return proto.wrappers.WrapperCommand.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.wrappers.WrapperCommand} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.wrappers.WrapperCommand.toObject = function(includeInstance, msg) {
  var f, obj = {
version: jspb.Message.getFieldWithDefault(msg, 1, 0),
requestId: jspb.Message.getFieldWithDefault(msg, 2, 0),
requestLogCounts: (f = msg.getRequestLogCounts()) && cmd_resp_logs_pb.CmdRequestLogCounts.toObject(includeInstance, f),
logClear: (f = msg.getLogClear()) && cmd_resp_logs_pb.CmdRequestLogClear.toObject(includeInstance, f),
requestLog: (f = msg.getRequestLog()) && cmd_resp_logs_pb.CmdRequestLogEntries.toObject(includeInstance, f),
reqAuditlogCounts: (f = msg.getReqAuditlogCounts()) && cmd_resp_logs_pb.CmdRequestAuditLogCounts.toObject(includeInstance, f),
auditlogClear: (f = msg.getAuditlogClear()) && cmd_resp_logs_pb.CmdRequestAuditLogClear.toObject(includeInstance, f),
auditLogReset: (f = msg.getAuditLogReset()) && cmd_resp_logs_pb.CmdRequestAuditLogReset.toObject(includeInstance, f),
requestAuditlog: (f = msg.getRequestAuditlog()) && cmd_resp_logs_pb.CmdRequestAuditLogEntries.toObject(includeInstance, f),
rdMonitorData: (f = msg.getRdMonitorData()) && cmd_resp_config_pb.CmdReadMonitorData.toObject(includeInstance, f),
rdNetworkConfigUnit: (f = msg.getRdNetworkConfigUnit()) && cmd_resp_config_pb.CmdReadUnitNetworkConfiguration.toObject(includeInstance, f),
rdNetworkConfigActive: (f = msg.getRdNetworkConfigActive()) && cmd_resp_config_pb.CmdReadActiveNetworkConfiguration.toObject(includeInstance, f),
rdChannelConfig: (f = msg.getRdChannelConfig()) && cmd_resp_config_pb.CmdReadPerChannelConfiguration.toObject(includeInstance, f),
rdChannelCurrentSense: (f = msg.getRdChannelCurrentSense()) && cmd_resp_config_pb.CmdReadPerChannelCurrentSenseSettings.toObject(includeInstance, f),
rdChannelPermissives: (f = msg.getRdChannelPermissives()) && cmd_resp_config_pb.CmdReadPerChannelPermissiveSettings.toObject(includeInstance, f),
rdFyaConfig: (f = msg.getRdFyaConfig()) && cmd_resp_config_pb.CmdReadFlashingYellowArrowConfiguration.toObject(includeInstance, f),
rdDataKey: (f = msg.getRdDataKey()) && cmd_resp_config_pb.CmdReadDataKey.toObject(includeInstance, f),
wrDataKey: (f = msg.getWrDataKey()) && cmd_resp_config_pb.CmdWriteDataKey.toObject(includeInstance, f),
rdFactorySettings: (f = msg.getRdFactorySettings()) && cmd_resp_config_pb.CmdReadFactorySettings.toObject(includeInstance, f),
wrFactorySettings: (f = msg.getWrFactorySettings()) && cmd_resp_config_pb.CmdWriteFactorySettings.toObject(includeInstance, f),
rdUserSettings: (f = msg.getRdUserSettings()) && cmd_resp_config_pb.CmdReadUserSettings.toObject(includeInstance, f),
wrUserSettings: (f = msg.getWrUserSettings()) && cmd_resp_config_pb.CmdWriteUserSettings.toObject(includeInstance, f),
rdPort1Disables: (f = msg.getRdPort1Disables()) && cmd_resp_config_pb.CmdReadPort1DisableOverrides.toObject(includeInstance, f),
wrPort1Disables: (f = msg.getWrPort1Disables()) && cmd_resp_config_pb.CmdWritePort1DisableOverrides.toObject(includeInstance, f),
wrAgencyOptions: (f = msg.getWrAgencyOptions()) && cmd_resp_config_pb.CmdWriteAgencyOptions.toObject(includeInstance, f),
remoteReset: (f = msg.getRemoteReset()) && cmd_resp_config_pb.CmdRemoteReset.toObject(includeInstance, f),
rdPort1Stats: (f = msg.getRdPort1Stats()) && cmd_resp_stats_pb.CmdReadPort1Statistics.toObject(includeInstance, f),
rdDataKeyStats: (f = msg.getRdDataKeyStats()) && cmd_resp_stats_pb.CmdReadDataKeyStatistics.toObject(includeInstance, f),
rdMainIsoCommsStats: (f = msg.getRdMainIsoCommsStats()) && cmd_resp_stats_pb.CmdReadMainToIsolatedCommStatistics.toObject(includeInstance, f),
rdMainCommsStats: (f = msg.getRdMainCommsStats()) && cmd_resp_stats_pb.CmdReadMainToCommsCommStatistics.toObject(includeInstance, f),
rdCommsMainStats: (f = msg.getRdCommsMainStats()) && cmd_resp_stats_pb.CmdReadCommsToMainCommStatistics.toObject(includeInstance, f),
rdFlashStats: (f = msg.getRdFlashStats()) && cmd_resp_stats_pb.CmdReadFlashStatistics.toObject(includeInstance, f),
rdWatchdogStats: (f = msg.getRdWatchdogStats()) && cmd_resp_stats_pb.CmdReadWatchdogStatistics.toObject(includeInstance, f),
rdDateTimeDst: (f = msg.getRdDateTimeDst()) && cmd_resp_stats_pb.CmdGetTimeDatesDst.toObject(includeInstance, f),
clearStats: (f = msg.getClearStats()) && cmd_resp_stats_pb.CmdClearStatistics.toObject(includeInstance, f),
wrDateTimeDst: (f = msg.getWrDateTimeDst()) && cmd_resp_stats_pb.CmdSetTimeDatesDst.toObject(includeInstance, f),
remoteDisplayButtonEvent: (f = msg.getRemoteDisplayButtonEvent()) && cmd_resp_stats_pb.CmdRemoteDisplayButtonEvent.toObject(includeInstance, f),
startRealtime: (f = msg.getStartRealtime()) && cmd_resp_realtime_pb.CmdStartRealtimeData.toObject(includeInstance, f),
stopRealtime: (f = msg.getStopRealtime()) && cmd_resp_realtime_pb.CmdStopRealtimeData.toObject(includeInstance, f),
manifestVersions: (f = msg.getManifestVersions()) && cmd_resp_dfu_pb.CmdManifestVersions.toObject(includeInstance, f),
rebootCommsMcu: (f = msg.getRebootCommsMcu()) && cmd_resp_dfu_pb.CmdRebootCommsMcu.toObject(includeInstance, f),
initiateDfu: (f = msg.getInitiateDfu()) && cmd_resp_dfu_pb.CmdInitiateFirmwareUpdate.toObject(includeInstance, f),
sendFwManifest: (f = msg.getSendFwManifest()) && cmd_resp_dfu_pb.CmdFirmwareUpdateManifest.toObject(includeInstance, f),
beginFirmwareDownload: (f = msg.getBeginFirmwareDownload()) && cmd_resp_dfu_pb.CmdBeginFirmwareDownload.toObject(includeInstance, f),
downloadFirmwareChunk: (f = msg.getDownloadFirmwareChunk()) && cmd_resp_dfu_pb.CmdFirmwareDownloadChunk.toObject(includeInstance, f),
testChunk: (f = msg.getTestChunk()) && cmd_resp_comms_pb.CmdChunkTest.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.wrappers.WrapperCommand}
 */
proto.wrappers.WrapperCommand.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.wrappers.WrapperCommand;
  return proto.wrappers.WrapperCommand.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.wrappers.WrapperCommand} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.wrappers.WrapperCommand}
 */
proto.wrappers.WrapperCommand.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRequestId(value);
      break;
    case 20:
      var value = new cmd_resp_logs_pb.CmdRequestLogCounts;
      reader.readMessage(value,cmd_resp_logs_pb.CmdRequestLogCounts.deserializeBinaryFromReader);
      msg.setRequestLogCounts(value);
      break;
    case 21:
      var value = new cmd_resp_logs_pb.CmdRequestLogClear;
      reader.readMessage(value,cmd_resp_logs_pb.CmdRequestLogClear.deserializeBinaryFromReader);
      msg.setLogClear(value);
      break;
    case 22:
      var value = new cmd_resp_logs_pb.CmdRequestLogEntries;
      reader.readMessage(value,cmd_resp_logs_pb.CmdRequestLogEntries.deserializeBinaryFromReader);
      msg.setRequestLog(value);
      break;
    case 23:
      var value = new cmd_resp_logs_pb.CmdRequestAuditLogCounts;
      reader.readMessage(value,cmd_resp_logs_pb.CmdRequestAuditLogCounts.deserializeBinaryFromReader);
      msg.setReqAuditlogCounts(value);
      break;
    case 24:
      var value = new cmd_resp_logs_pb.CmdRequestAuditLogClear;
      reader.readMessage(value,cmd_resp_logs_pb.CmdRequestAuditLogClear.deserializeBinaryFromReader);
      msg.setAuditlogClear(value);
      break;
    case 25:
      var value = new cmd_resp_logs_pb.CmdRequestAuditLogReset;
      reader.readMessage(value,cmd_resp_logs_pb.CmdRequestAuditLogReset.deserializeBinaryFromReader);
      msg.setAuditLogReset(value);
      break;
    case 26:
      var value = new cmd_resp_logs_pb.CmdRequestAuditLogEntries;
      reader.readMessage(value,cmd_resp_logs_pb.CmdRequestAuditLogEntries.deserializeBinaryFromReader);
      msg.setRequestAuditlog(value);
      break;
    case 31:
      var value = new cmd_resp_config_pb.CmdReadMonitorData;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadMonitorData.deserializeBinaryFromReader);
      msg.setRdMonitorData(value);
      break;
    case 32:
      var value = new cmd_resp_config_pb.CmdReadUnitNetworkConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadUnitNetworkConfiguration.deserializeBinaryFromReader);
      msg.setRdNetworkConfigUnit(value);
      break;
    case 33:
      var value = new cmd_resp_config_pb.CmdReadActiveNetworkConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadActiveNetworkConfiguration.deserializeBinaryFromReader);
      msg.setRdNetworkConfigActive(value);
      break;
    case 34:
      var value = new cmd_resp_config_pb.CmdReadPerChannelConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadPerChannelConfiguration.deserializeBinaryFromReader);
      msg.setRdChannelConfig(value);
      break;
    case 35:
      var value = new cmd_resp_config_pb.CmdReadPerChannelCurrentSenseSettings;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadPerChannelCurrentSenseSettings.deserializeBinaryFromReader);
      msg.setRdChannelCurrentSense(value);
      break;
    case 36:
      var value = new cmd_resp_config_pb.CmdReadPerChannelPermissiveSettings;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadPerChannelPermissiveSettings.deserializeBinaryFromReader);
      msg.setRdChannelPermissives(value);
      break;
    case 37:
      var value = new cmd_resp_config_pb.CmdReadFlashingYellowArrowConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadFlashingYellowArrowConfiguration.deserializeBinaryFromReader);
      msg.setRdFyaConfig(value);
      break;
    case 38:
      var value = new cmd_resp_config_pb.CmdReadDataKey;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadDataKey.deserializeBinaryFromReader);
      msg.setRdDataKey(value);
      break;
    case 39:
      var value = new cmd_resp_config_pb.CmdWriteDataKey;
      reader.readMessage(value,cmd_resp_config_pb.CmdWriteDataKey.deserializeBinaryFromReader);
      msg.setWrDataKey(value);
      break;
    case 40:
      var value = new cmd_resp_config_pb.CmdReadFactorySettings;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadFactorySettings.deserializeBinaryFromReader);
      msg.setRdFactorySettings(value);
      break;
    case 41:
      var value = new cmd_resp_config_pb.CmdWriteFactorySettings;
      reader.readMessage(value,cmd_resp_config_pb.CmdWriteFactorySettings.deserializeBinaryFromReader);
      msg.setWrFactorySettings(value);
      break;
    case 42:
      var value = new cmd_resp_config_pb.CmdReadUserSettings;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadUserSettings.deserializeBinaryFromReader);
      msg.setRdUserSettings(value);
      break;
    case 43:
      var value = new cmd_resp_config_pb.CmdWriteUserSettings;
      reader.readMessage(value,cmd_resp_config_pb.CmdWriteUserSettings.deserializeBinaryFromReader);
      msg.setWrUserSettings(value);
      break;
    case 44:
      var value = new cmd_resp_config_pb.CmdReadPort1DisableOverrides;
      reader.readMessage(value,cmd_resp_config_pb.CmdReadPort1DisableOverrides.deserializeBinaryFromReader);
      msg.setRdPort1Disables(value);
      break;
    case 45:
      var value = new cmd_resp_config_pb.CmdWritePort1DisableOverrides;
      reader.readMessage(value,cmd_resp_config_pb.CmdWritePort1DisableOverrides.deserializeBinaryFromReader);
      msg.setWrPort1Disables(value);
      break;
    case 46:
      var value = new cmd_resp_config_pb.CmdWriteAgencyOptions;
      reader.readMessage(value,cmd_resp_config_pb.CmdWriteAgencyOptions.deserializeBinaryFromReader);
      msg.setWrAgencyOptions(value);
      break;
    case 47:
      var value = new cmd_resp_config_pb.CmdRemoteReset;
      reader.readMessage(value,cmd_resp_config_pb.CmdRemoteReset.deserializeBinaryFromReader);
      msg.setRemoteReset(value);
      break;
    case 50:
      var value = new cmd_resp_stats_pb.CmdReadPort1Statistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdReadPort1Statistics.deserializeBinaryFromReader);
      msg.setRdPort1Stats(value);
      break;
    case 51:
      var value = new cmd_resp_stats_pb.CmdReadDataKeyStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdReadDataKeyStatistics.deserializeBinaryFromReader);
      msg.setRdDataKeyStats(value);
      break;
    case 52:
      var value = new cmd_resp_stats_pb.CmdReadMainToIsolatedCommStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdReadMainToIsolatedCommStatistics.deserializeBinaryFromReader);
      msg.setRdMainIsoCommsStats(value);
      break;
    case 53:
      var value = new cmd_resp_stats_pb.CmdReadMainToCommsCommStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdReadMainToCommsCommStatistics.deserializeBinaryFromReader);
      msg.setRdMainCommsStats(value);
      break;
    case 54:
      var value = new cmd_resp_stats_pb.CmdReadCommsToMainCommStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdReadCommsToMainCommStatistics.deserializeBinaryFromReader);
      msg.setRdCommsMainStats(value);
      break;
    case 55:
      var value = new cmd_resp_stats_pb.CmdReadFlashStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdReadFlashStatistics.deserializeBinaryFromReader);
      msg.setRdFlashStats(value);
      break;
    case 56:
      var value = new cmd_resp_stats_pb.CmdReadWatchdogStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdReadWatchdogStatistics.deserializeBinaryFromReader);
      msg.setRdWatchdogStats(value);
      break;
    case 57:
      var value = new cmd_resp_stats_pb.CmdGetTimeDatesDst;
      reader.readMessage(value,cmd_resp_stats_pb.CmdGetTimeDatesDst.deserializeBinaryFromReader);
      msg.setRdDateTimeDst(value);
      break;
    case 58:
      var value = new cmd_resp_stats_pb.CmdClearStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.CmdClearStatistics.deserializeBinaryFromReader);
      msg.setClearStats(value);
      break;
    case 59:
      var value = new cmd_resp_stats_pb.CmdSetTimeDatesDst;
      reader.readMessage(value,cmd_resp_stats_pb.CmdSetTimeDatesDst.deserializeBinaryFromReader);
      msg.setWrDateTimeDst(value);
      break;
    case 65:
      var value = new cmd_resp_stats_pb.CmdRemoteDisplayButtonEvent;
      reader.readMessage(value,cmd_resp_stats_pb.CmdRemoteDisplayButtonEvent.deserializeBinaryFromReader);
      msg.setRemoteDisplayButtonEvent(value);
      break;
    case 60:
      var value = new cmd_resp_realtime_pb.CmdStartRealtimeData;
      reader.readMessage(value,cmd_resp_realtime_pb.CmdStartRealtimeData.deserializeBinaryFromReader);
      msg.setStartRealtime(value);
      break;
    case 61:
      var value = new cmd_resp_realtime_pb.CmdStopRealtimeData;
      reader.readMessage(value,cmd_resp_realtime_pb.CmdStopRealtimeData.deserializeBinaryFromReader);
      msg.setStopRealtime(value);
      break;
    case 70:
      var value = new cmd_resp_dfu_pb.CmdManifestVersions;
      reader.readMessage(value,cmd_resp_dfu_pb.CmdManifestVersions.deserializeBinaryFromReader);
      msg.setManifestVersions(value);
      break;
    case 71:
      var value = new cmd_resp_dfu_pb.CmdRebootCommsMcu;
      reader.readMessage(value,cmd_resp_dfu_pb.CmdRebootCommsMcu.deserializeBinaryFromReader);
      msg.setRebootCommsMcu(value);
      break;
    case 72:
      var value = new cmd_resp_dfu_pb.CmdInitiateFirmwareUpdate;
      reader.readMessage(value,cmd_resp_dfu_pb.CmdInitiateFirmwareUpdate.deserializeBinaryFromReader);
      msg.setInitiateDfu(value);
      break;
    case 73:
      var value = new cmd_resp_dfu_pb.CmdFirmwareUpdateManifest;
      reader.readMessage(value,cmd_resp_dfu_pb.CmdFirmwareUpdateManifest.deserializeBinaryFromReader);
      msg.setSendFwManifest(value);
      break;
    case 74:
      var value = new cmd_resp_dfu_pb.CmdBeginFirmwareDownload;
      reader.readMessage(value,cmd_resp_dfu_pb.CmdBeginFirmwareDownload.deserializeBinaryFromReader);
      msg.setBeginFirmwareDownload(value);
      break;
    case 75:
      var value = new cmd_resp_dfu_pb.CmdFirmwareDownloadChunk;
      reader.readMessage(value,cmd_resp_dfu_pb.CmdFirmwareDownloadChunk.deserializeBinaryFromReader);
      msg.setDownloadFirmwareChunk(value);
      break;
    case 90:
      var value = new cmd_resp_comms_pb.CmdChunkTest;
      reader.readMessage(value,cmd_resp_comms_pb.CmdChunkTest.deserializeBinaryFromReader);
      msg.setTestChunk(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.wrappers.WrapperCommand.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.wrappers.WrapperCommand.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.wrappers.WrapperCommand} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.wrappers.WrapperCommand.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getRequestId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getRequestLogCounts();
  if (f != null) {
    writer.writeMessage(
      20,
      f,
      cmd_resp_logs_pb.CmdRequestLogCounts.serializeBinaryToWriter
    );
  }
  f = message.getLogClear();
  if (f != null) {
    writer.writeMessage(
      21,
      f,
      cmd_resp_logs_pb.CmdRequestLogClear.serializeBinaryToWriter
    );
  }
  f = message.getRequestLog();
  if (f != null) {
    writer.writeMessage(
      22,
      f,
      cmd_resp_logs_pb.CmdRequestLogEntries.serializeBinaryToWriter
    );
  }
  f = message.getReqAuditlogCounts();
  if (f != null) {
    writer.writeMessage(
      23,
      f,
      cmd_resp_logs_pb.CmdRequestAuditLogCounts.serializeBinaryToWriter
    );
  }
  f = message.getAuditlogClear();
  if (f != null) {
    writer.writeMessage(
      24,
      f,
      cmd_resp_logs_pb.CmdRequestAuditLogClear.serializeBinaryToWriter
    );
  }
  f = message.getAuditLogReset();
  if (f != null) {
    writer.writeMessage(
      25,
      f,
      cmd_resp_logs_pb.CmdRequestAuditLogReset.serializeBinaryToWriter
    );
  }
  f = message.getRequestAuditlog();
  if (f != null) {
    writer.writeMessage(
      26,
      f,
      cmd_resp_logs_pb.CmdRequestAuditLogEntries.serializeBinaryToWriter
    );
  }
  f = message.getRdMonitorData();
  if (f != null) {
    writer.writeMessage(
      31,
      f,
      cmd_resp_config_pb.CmdReadMonitorData.serializeBinaryToWriter
    );
  }
  f = message.getRdNetworkConfigUnit();
  if (f != null) {
    writer.writeMessage(
      32,
      f,
      cmd_resp_config_pb.CmdReadUnitNetworkConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdNetworkConfigActive();
  if (f != null) {
    writer.writeMessage(
      33,
      f,
      cmd_resp_config_pb.CmdReadActiveNetworkConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdChannelConfig();
  if (f != null) {
    writer.writeMessage(
      34,
      f,
      cmd_resp_config_pb.CmdReadPerChannelConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdChannelCurrentSense();
  if (f != null) {
    writer.writeMessage(
      35,
      f,
      cmd_resp_config_pb.CmdReadPerChannelCurrentSenseSettings.serializeBinaryToWriter
    );
  }
  f = message.getRdChannelPermissives();
  if (f != null) {
    writer.writeMessage(
      36,
      f,
      cmd_resp_config_pb.CmdReadPerChannelPermissiveSettings.serializeBinaryToWriter
    );
  }
  f = message.getRdFyaConfig();
  if (f != null) {
    writer.writeMessage(
      37,
      f,
      cmd_resp_config_pb.CmdReadFlashingYellowArrowConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdDataKey();
  if (f != null) {
    writer.writeMessage(
      38,
      f,
      cmd_resp_config_pb.CmdReadDataKey.serializeBinaryToWriter
    );
  }
  f = message.getWrDataKey();
  if (f != null) {
    writer.writeMessage(
      39,
      f,
      cmd_resp_config_pb.CmdWriteDataKey.serializeBinaryToWriter
    );
  }
  f = message.getRdFactorySettings();
  if (f != null) {
    writer.writeMessage(
      40,
      f,
      cmd_resp_config_pb.CmdReadFactorySettings.serializeBinaryToWriter
    );
  }
  f = message.getWrFactorySettings();
  if (f != null) {
    writer.writeMessage(
      41,
      f,
      cmd_resp_config_pb.CmdWriteFactorySettings.serializeBinaryToWriter
    );
  }
  f = message.getRdUserSettings();
  if (f != null) {
    writer.writeMessage(
      42,
      f,
      cmd_resp_config_pb.CmdReadUserSettings.serializeBinaryToWriter
    );
  }
  f = message.getWrUserSettings();
  if (f != null) {
    writer.writeMessage(
      43,
      f,
      cmd_resp_config_pb.CmdWriteUserSettings.serializeBinaryToWriter
    );
  }
  f = message.getRdPort1Disables();
  if (f != null) {
    writer.writeMessage(
      44,
      f,
      cmd_resp_config_pb.CmdReadPort1DisableOverrides.serializeBinaryToWriter
    );
  }
  f = message.getWrPort1Disables();
  if (f != null) {
    writer.writeMessage(
      45,
      f,
      cmd_resp_config_pb.CmdWritePort1DisableOverrides.serializeBinaryToWriter
    );
  }
  f = message.getWrAgencyOptions();
  if (f != null) {
    writer.writeMessage(
      46,
      f,
      cmd_resp_config_pb.CmdWriteAgencyOptions.serializeBinaryToWriter
    );
  }
  f = message.getRemoteReset();
  if (f != null) {
    writer.writeMessage(
      47,
      f,
      cmd_resp_config_pb.CmdRemoteReset.serializeBinaryToWriter
    );
  }
  f = message.getRdPort1Stats();
  if (f != null) {
    writer.writeMessage(
      50,
      f,
      cmd_resp_stats_pb.CmdReadPort1Statistics.serializeBinaryToWriter
    );
  }
  f = message.getRdDataKeyStats();
  if (f != null) {
    writer.writeMessage(
      51,
      f,
      cmd_resp_stats_pb.CmdReadDataKeyStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdMainIsoCommsStats();
  if (f != null) {
    writer.writeMessage(
      52,
      f,
      cmd_resp_stats_pb.CmdReadMainToIsolatedCommStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdMainCommsStats();
  if (f != null) {
    writer.writeMessage(
      53,
      f,
      cmd_resp_stats_pb.CmdReadMainToCommsCommStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdCommsMainStats();
  if (f != null) {
    writer.writeMessage(
      54,
      f,
      cmd_resp_stats_pb.CmdReadCommsToMainCommStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdFlashStats();
  if (f != null) {
    writer.writeMessage(
      55,
      f,
      cmd_resp_stats_pb.CmdReadFlashStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdWatchdogStats();
  if (f != null) {
    writer.writeMessage(
      56,
      f,
      cmd_resp_stats_pb.CmdReadWatchdogStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdDateTimeDst();
  if (f != null) {
    writer.writeMessage(
      57,
      f,
      cmd_resp_stats_pb.CmdGetTimeDatesDst.serializeBinaryToWriter
    );
  }
  f = message.getClearStats();
  if (f != null) {
    writer.writeMessage(
      58,
      f,
      cmd_resp_stats_pb.CmdClearStatistics.serializeBinaryToWriter
    );
  }
  f = message.getWrDateTimeDst();
  if (f != null) {
    writer.writeMessage(
      59,
      f,
      cmd_resp_stats_pb.CmdSetTimeDatesDst.serializeBinaryToWriter
    );
  }
  f = message.getRemoteDisplayButtonEvent();
  if (f != null) {
    writer.writeMessage(
      65,
      f,
      cmd_resp_stats_pb.CmdRemoteDisplayButtonEvent.serializeBinaryToWriter
    );
  }
  f = message.getStartRealtime();
  if (f != null) {
    writer.writeMessage(
      60,
      f,
      cmd_resp_realtime_pb.CmdStartRealtimeData.serializeBinaryToWriter
    );
  }
  f = message.getStopRealtime();
  if (f != null) {
    writer.writeMessage(
      61,
      f,
      cmd_resp_realtime_pb.CmdStopRealtimeData.serializeBinaryToWriter
    );
  }
  f = message.getManifestVersions();
  if (f != null) {
    writer.writeMessage(
      70,
      f,
      cmd_resp_dfu_pb.CmdManifestVersions.serializeBinaryToWriter
    );
  }
  f = message.getRebootCommsMcu();
  if (f != null) {
    writer.writeMessage(
      71,
      f,
      cmd_resp_dfu_pb.CmdRebootCommsMcu.serializeBinaryToWriter
    );
  }
  f = message.getInitiateDfu();
  if (f != null) {
    writer.writeMessage(
      72,
      f,
      cmd_resp_dfu_pb.CmdInitiateFirmwareUpdate.serializeBinaryToWriter
    );
  }
  f = message.getSendFwManifest();
  if (f != null) {
    writer.writeMessage(
      73,
      f,
      cmd_resp_dfu_pb.CmdFirmwareUpdateManifest.serializeBinaryToWriter
    );
  }
  f = message.getBeginFirmwareDownload();
  if (f != null) {
    writer.writeMessage(
      74,
      f,
      cmd_resp_dfu_pb.CmdBeginFirmwareDownload.serializeBinaryToWriter
    );
  }
  f = message.getDownloadFirmwareChunk();
  if (f != null) {
    writer.writeMessage(
      75,
      f,
      cmd_resp_dfu_pb.CmdFirmwareDownloadChunk.serializeBinaryToWriter
    );
  }
  f = message.getTestChunk();
  if (f != null) {
    writer.writeMessage(
      90,
      f,
      cmd_resp_comms_pb.CmdChunkTest.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 version = 1;
 * @return {number}
 */
proto.wrappers.WrapperCommand.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 request_id = 2;
 * @return {number}
 */
proto.wrappers.WrapperCommand.prototype.getRequestId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.setRequestId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional cmd_resp_logs.CmdRequestLogCounts request_log_counts = 20;
 * @return {?proto.cmd_resp_logs.CmdRequestLogCounts}
 */
proto.wrappers.WrapperCommand.prototype.getRequestLogCounts = function() {
  return /** @type{?proto.cmd_resp_logs.CmdRequestLogCounts} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.CmdRequestLogCounts, 20));
};


/**
 * @param {?proto.cmd_resp_logs.CmdRequestLogCounts|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRequestLogCounts = function(value) {
  return jspb.Message.setOneofWrapperField(this, 20, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRequestLogCounts = function() {
  return this.setRequestLogCounts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRequestLogCounts = function() {
  return jspb.Message.getField(this, 20) != null;
};


/**
 * optional cmd_resp_logs.CmdRequestLogClear log_clear = 21;
 * @return {?proto.cmd_resp_logs.CmdRequestLogClear}
 */
proto.wrappers.WrapperCommand.prototype.getLogClear = function() {
  return /** @type{?proto.cmd_resp_logs.CmdRequestLogClear} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.CmdRequestLogClear, 21));
};


/**
 * @param {?proto.cmd_resp_logs.CmdRequestLogClear|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setLogClear = function(value) {
  return jspb.Message.setOneofWrapperField(this, 21, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearLogClear = function() {
  return this.setLogClear(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasLogClear = function() {
  return jspb.Message.getField(this, 21) != null;
};


/**
 * optional cmd_resp_logs.CmdRequestLogEntries request_log = 22;
 * @return {?proto.cmd_resp_logs.CmdRequestLogEntries}
 */
proto.wrappers.WrapperCommand.prototype.getRequestLog = function() {
  return /** @type{?proto.cmd_resp_logs.CmdRequestLogEntries} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.CmdRequestLogEntries, 22));
};


/**
 * @param {?proto.cmd_resp_logs.CmdRequestLogEntries|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRequestLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 22, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRequestLog = function() {
  return this.setRequestLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRequestLog = function() {
  return jspb.Message.getField(this, 22) != null;
};


/**
 * optional cmd_resp_logs.CmdRequestAuditLogCounts req_auditlog_counts = 23;
 * @return {?proto.cmd_resp_logs.CmdRequestAuditLogCounts}
 */
proto.wrappers.WrapperCommand.prototype.getReqAuditlogCounts = function() {
  return /** @type{?proto.cmd_resp_logs.CmdRequestAuditLogCounts} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.CmdRequestAuditLogCounts, 23));
};


/**
 * @param {?proto.cmd_resp_logs.CmdRequestAuditLogCounts|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setReqAuditlogCounts = function(value) {
  return jspb.Message.setOneofWrapperField(this, 23, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearReqAuditlogCounts = function() {
  return this.setReqAuditlogCounts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasReqAuditlogCounts = function() {
  return jspb.Message.getField(this, 23) != null;
};


/**
 * optional cmd_resp_logs.CmdRequestAuditLogClear auditlog_clear = 24;
 * @return {?proto.cmd_resp_logs.CmdRequestAuditLogClear}
 */
proto.wrappers.WrapperCommand.prototype.getAuditlogClear = function() {
  return /** @type{?proto.cmd_resp_logs.CmdRequestAuditLogClear} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.CmdRequestAuditLogClear, 24));
};


/**
 * @param {?proto.cmd_resp_logs.CmdRequestAuditLogClear|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setAuditlogClear = function(value) {
  return jspb.Message.setOneofWrapperField(this, 24, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearAuditlogClear = function() {
  return this.setAuditlogClear(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasAuditlogClear = function() {
  return jspb.Message.getField(this, 24) != null;
};


/**
 * optional cmd_resp_logs.CmdRequestAuditLogReset audit_log_reset = 25;
 * @return {?proto.cmd_resp_logs.CmdRequestAuditLogReset}
 */
proto.wrappers.WrapperCommand.prototype.getAuditLogReset = function() {
  return /** @type{?proto.cmd_resp_logs.CmdRequestAuditLogReset} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.CmdRequestAuditLogReset, 25));
};


/**
 * @param {?proto.cmd_resp_logs.CmdRequestAuditLogReset|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setAuditLogReset = function(value) {
  return jspb.Message.setOneofWrapperField(this, 25, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearAuditLogReset = function() {
  return this.setAuditLogReset(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasAuditLogReset = function() {
  return jspb.Message.getField(this, 25) != null;
};


/**
 * optional cmd_resp_logs.CmdRequestAuditLogEntries request_auditlog = 26;
 * @return {?proto.cmd_resp_logs.CmdRequestAuditLogEntries}
 */
proto.wrappers.WrapperCommand.prototype.getRequestAuditlog = function() {
  return /** @type{?proto.cmd_resp_logs.CmdRequestAuditLogEntries} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.CmdRequestAuditLogEntries, 26));
};


/**
 * @param {?proto.cmd_resp_logs.CmdRequestAuditLogEntries|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRequestAuditlog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 26, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRequestAuditlog = function() {
  return this.setRequestAuditlog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRequestAuditlog = function() {
  return jspb.Message.getField(this, 26) != null;
};


/**
 * optional cmd_resp_config.CmdReadMonitorData rd_monitor_data = 31;
 * @return {?proto.cmd_resp_config.CmdReadMonitorData}
 */
proto.wrappers.WrapperCommand.prototype.getRdMonitorData = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadMonitorData} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadMonitorData, 31));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadMonitorData|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdMonitorData = function(value) {
  return jspb.Message.setOneofWrapperField(this, 31, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdMonitorData = function() {
  return this.setRdMonitorData(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdMonitorData = function() {
  return jspb.Message.getField(this, 31) != null;
};


/**
 * optional cmd_resp_config.CmdReadUnitNetworkConfiguration rd_network_config_unit = 32;
 * @return {?proto.cmd_resp_config.CmdReadUnitNetworkConfiguration}
 */
proto.wrappers.WrapperCommand.prototype.getRdNetworkConfigUnit = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadUnitNetworkConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadUnitNetworkConfiguration, 32));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadUnitNetworkConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdNetworkConfigUnit = function(value) {
  return jspb.Message.setOneofWrapperField(this, 32, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdNetworkConfigUnit = function() {
  return this.setRdNetworkConfigUnit(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdNetworkConfigUnit = function() {
  return jspb.Message.getField(this, 32) != null;
};


/**
 * optional cmd_resp_config.CmdReadActiveNetworkConfiguration rd_network_config_active = 33;
 * @return {?proto.cmd_resp_config.CmdReadActiveNetworkConfiguration}
 */
proto.wrappers.WrapperCommand.prototype.getRdNetworkConfigActive = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadActiveNetworkConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadActiveNetworkConfiguration, 33));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadActiveNetworkConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdNetworkConfigActive = function(value) {
  return jspb.Message.setOneofWrapperField(this, 33, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdNetworkConfigActive = function() {
  return this.setRdNetworkConfigActive(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdNetworkConfigActive = function() {
  return jspb.Message.getField(this, 33) != null;
};


/**
 * optional cmd_resp_config.CmdReadPerChannelConfiguration rd_channel_config = 34;
 * @return {?proto.cmd_resp_config.CmdReadPerChannelConfiguration}
 */
proto.wrappers.WrapperCommand.prototype.getRdChannelConfig = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadPerChannelConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadPerChannelConfiguration, 34));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadPerChannelConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdChannelConfig = function(value) {
  return jspb.Message.setOneofWrapperField(this, 34, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdChannelConfig = function() {
  return this.setRdChannelConfig(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdChannelConfig = function() {
  return jspb.Message.getField(this, 34) != null;
};


/**
 * optional cmd_resp_config.CmdReadPerChannelCurrentSenseSettings rd_channel_current_sense = 35;
 * @return {?proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings}
 */
proto.wrappers.WrapperCommand.prototype.getRdChannelCurrentSense = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadPerChannelCurrentSenseSettings, 35));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdChannelCurrentSense = function(value) {
  return jspb.Message.setOneofWrapperField(this, 35, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdChannelCurrentSense = function() {
  return this.setRdChannelCurrentSense(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdChannelCurrentSense = function() {
  return jspb.Message.getField(this, 35) != null;
};


/**
 * optional cmd_resp_config.CmdReadPerChannelPermissiveSettings rd_channel_permissives = 36;
 * @return {?proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings}
 */
proto.wrappers.WrapperCommand.prototype.getRdChannelPermissives = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadPerChannelPermissiveSettings, 36));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdChannelPermissives = function(value) {
  return jspb.Message.setOneofWrapperField(this, 36, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdChannelPermissives = function() {
  return this.setRdChannelPermissives(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdChannelPermissives = function() {
  return jspb.Message.getField(this, 36) != null;
};


/**
 * optional cmd_resp_config.CmdReadFlashingYellowArrowConfiguration rd_fya_config = 37;
 * @return {?proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration}
 */
proto.wrappers.WrapperCommand.prototype.getRdFyaConfig = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadFlashingYellowArrowConfiguration, 37));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdFyaConfig = function(value) {
  return jspb.Message.setOneofWrapperField(this, 37, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdFyaConfig = function() {
  return this.setRdFyaConfig(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdFyaConfig = function() {
  return jspb.Message.getField(this, 37) != null;
};


/**
 * optional cmd_resp_config.CmdReadDataKey rd_data_key = 38;
 * @return {?proto.cmd_resp_config.CmdReadDataKey}
 */
proto.wrappers.WrapperCommand.prototype.getRdDataKey = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadDataKey} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadDataKey, 38));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadDataKey|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdDataKey = function(value) {
  return jspb.Message.setOneofWrapperField(this, 38, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdDataKey = function() {
  return this.setRdDataKey(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdDataKey = function() {
  return jspb.Message.getField(this, 38) != null;
};


/**
 * optional cmd_resp_config.CmdWriteDataKey wr_data_key = 39;
 * @return {?proto.cmd_resp_config.CmdWriteDataKey}
 */
proto.wrappers.WrapperCommand.prototype.getWrDataKey = function() {
  return /** @type{?proto.cmd_resp_config.CmdWriteDataKey} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdWriteDataKey, 39));
};


/**
 * @param {?proto.cmd_resp_config.CmdWriteDataKey|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setWrDataKey = function(value) {
  return jspb.Message.setOneofWrapperField(this, 39, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearWrDataKey = function() {
  return this.setWrDataKey(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasWrDataKey = function() {
  return jspb.Message.getField(this, 39) != null;
};


/**
 * optional cmd_resp_config.CmdReadFactorySettings rd_factory_settings = 40;
 * @return {?proto.cmd_resp_config.CmdReadFactorySettings}
 */
proto.wrappers.WrapperCommand.prototype.getRdFactorySettings = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadFactorySettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadFactorySettings, 40));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadFactorySettings|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdFactorySettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 40, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdFactorySettings = function() {
  return this.setRdFactorySettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdFactorySettings = function() {
  return jspb.Message.getField(this, 40) != null;
};


/**
 * optional cmd_resp_config.CmdWriteFactorySettings wr_factory_settings = 41;
 * @return {?proto.cmd_resp_config.CmdWriteFactorySettings}
 */
proto.wrappers.WrapperCommand.prototype.getWrFactorySettings = function() {
  return /** @type{?proto.cmd_resp_config.CmdWriteFactorySettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdWriteFactorySettings, 41));
};


/**
 * @param {?proto.cmd_resp_config.CmdWriteFactorySettings|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setWrFactorySettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 41, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearWrFactorySettings = function() {
  return this.setWrFactorySettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasWrFactorySettings = function() {
  return jspb.Message.getField(this, 41) != null;
};


/**
 * optional cmd_resp_config.CmdReadUserSettings rd_user_settings = 42;
 * @return {?proto.cmd_resp_config.CmdReadUserSettings}
 */
proto.wrappers.WrapperCommand.prototype.getRdUserSettings = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadUserSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadUserSettings, 42));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadUserSettings|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdUserSettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 42, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdUserSettings = function() {
  return this.setRdUserSettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdUserSettings = function() {
  return jspb.Message.getField(this, 42) != null;
};


/**
 * optional cmd_resp_config.CmdWriteUserSettings wr_user_settings = 43;
 * @return {?proto.cmd_resp_config.CmdWriteUserSettings}
 */
proto.wrappers.WrapperCommand.prototype.getWrUserSettings = function() {
  return /** @type{?proto.cmd_resp_config.CmdWriteUserSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdWriteUserSettings, 43));
};


/**
 * @param {?proto.cmd_resp_config.CmdWriteUserSettings|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setWrUserSettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 43, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearWrUserSettings = function() {
  return this.setWrUserSettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasWrUserSettings = function() {
  return jspb.Message.getField(this, 43) != null;
};


/**
 * optional cmd_resp_config.CmdReadPort1DisableOverrides rd_port1_disables = 44;
 * @return {?proto.cmd_resp_config.CmdReadPort1DisableOverrides}
 */
proto.wrappers.WrapperCommand.prototype.getRdPort1Disables = function() {
  return /** @type{?proto.cmd_resp_config.CmdReadPort1DisableOverrides} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdReadPort1DisableOverrides, 44));
};


/**
 * @param {?proto.cmd_resp_config.CmdReadPort1DisableOverrides|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdPort1Disables = function(value) {
  return jspb.Message.setOneofWrapperField(this, 44, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdPort1Disables = function() {
  return this.setRdPort1Disables(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdPort1Disables = function() {
  return jspb.Message.getField(this, 44) != null;
};


/**
 * optional cmd_resp_config.CmdWritePort1DisableOverrides wr_port1_disables = 45;
 * @return {?proto.cmd_resp_config.CmdWritePort1DisableOverrides}
 */
proto.wrappers.WrapperCommand.prototype.getWrPort1Disables = function() {
  return /** @type{?proto.cmd_resp_config.CmdWritePort1DisableOverrides} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdWritePort1DisableOverrides, 45));
};


/**
 * @param {?proto.cmd_resp_config.CmdWritePort1DisableOverrides|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setWrPort1Disables = function(value) {
  return jspb.Message.setOneofWrapperField(this, 45, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearWrPort1Disables = function() {
  return this.setWrPort1Disables(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasWrPort1Disables = function() {
  return jspb.Message.getField(this, 45) != null;
};


/**
 * optional cmd_resp_config.CmdWriteAgencyOptions wr_agency_options = 46;
 * @return {?proto.cmd_resp_config.CmdWriteAgencyOptions}
 */
proto.wrappers.WrapperCommand.prototype.getWrAgencyOptions = function() {
  return /** @type{?proto.cmd_resp_config.CmdWriteAgencyOptions} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdWriteAgencyOptions, 46));
};


/**
 * @param {?proto.cmd_resp_config.CmdWriteAgencyOptions|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setWrAgencyOptions = function(value) {
  return jspb.Message.setOneofWrapperField(this, 46, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearWrAgencyOptions = function() {
  return this.setWrAgencyOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasWrAgencyOptions = function() {
  return jspb.Message.getField(this, 46) != null;
};


/**
 * optional cmd_resp_config.CmdRemoteReset remote_reset = 47;
 * @return {?proto.cmd_resp_config.CmdRemoteReset}
 */
proto.wrappers.WrapperCommand.prototype.getRemoteReset = function() {
  return /** @type{?proto.cmd_resp_config.CmdRemoteReset} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.CmdRemoteReset, 47));
};


/**
 * @param {?proto.cmd_resp_config.CmdRemoteReset|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRemoteReset = function(value) {
  return jspb.Message.setOneofWrapperField(this, 47, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRemoteReset = function() {
  return this.setRemoteReset(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRemoteReset = function() {
  return jspb.Message.getField(this, 47) != null;
};


/**
 * optional cmd_resp_stats.CmdReadPort1Statistics rd_port1_stats = 50;
 * @return {?proto.cmd_resp_stats.CmdReadPort1Statistics}
 */
proto.wrappers.WrapperCommand.prototype.getRdPort1Stats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdReadPort1Statistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdReadPort1Statistics, 50));
};


/**
 * @param {?proto.cmd_resp_stats.CmdReadPort1Statistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdPort1Stats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 50, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdPort1Stats = function() {
  return this.setRdPort1Stats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdPort1Stats = function() {
  return jspb.Message.getField(this, 50) != null;
};


/**
 * optional cmd_resp_stats.CmdReadDataKeyStatistics rd_data_key_stats = 51;
 * @return {?proto.cmd_resp_stats.CmdReadDataKeyStatistics}
 */
proto.wrappers.WrapperCommand.prototype.getRdDataKeyStats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdReadDataKeyStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdReadDataKeyStatistics, 51));
};


/**
 * @param {?proto.cmd_resp_stats.CmdReadDataKeyStatistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdDataKeyStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 51, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdDataKeyStats = function() {
  return this.setRdDataKeyStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdDataKeyStats = function() {
  return jspb.Message.getField(this, 51) != null;
};


/**
 * optional cmd_resp_stats.CmdReadMainToIsolatedCommStatistics rd_main_iso_comms_stats = 52;
 * @return {?proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics}
 */
proto.wrappers.WrapperCommand.prototype.getRdMainIsoCommsStats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdReadMainToIsolatedCommStatistics, 52));
};


/**
 * @param {?proto.cmd_resp_stats.CmdReadMainToIsolatedCommStatistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdMainIsoCommsStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 52, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdMainIsoCommsStats = function() {
  return this.setRdMainIsoCommsStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdMainIsoCommsStats = function() {
  return jspb.Message.getField(this, 52) != null;
};


/**
 * optional cmd_resp_stats.CmdReadMainToCommsCommStatistics rd_main_comms_stats = 53;
 * @return {?proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics}
 */
proto.wrappers.WrapperCommand.prototype.getRdMainCommsStats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdReadMainToCommsCommStatistics, 53));
};


/**
 * @param {?proto.cmd_resp_stats.CmdReadMainToCommsCommStatistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdMainCommsStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 53, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdMainCommsStats = function() {
  return this.setRdMainCommsStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdMainCommsStats = function() {
  return jspb.Message.getField(this, 53) != null;
};


/**
 * optional cmd_resp_stats.CmdReadCommsToMainCommStatistics rd_comms_main_stats = 54;
 * @return {?proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics}
 */
proto.wrappers.WrapperCommand.prototype.getRdCommsMainStats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdReadCommsToMainCommStatistics, 54));
};


/**
 * @param {?proto.cmd_resp_stats.CmdReadCommsToMainCommStatistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdCommsMainStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 54, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdCommsMainStats = function() {
  return this.setRdCommsMainStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdCommsMainStats = function() {
  return jspb.Message.getField(this, 54) != null;
};


/**
 * optional cmd_resp_stats.CmdReadFlashStatistics rd_flash_stats = 55;
 * @return {?proto.cmd_resp_stats.CmdReadFlashStatistics}
 */
proto.wrappers.WrapperCommand.prototype.getRdFlashStats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdReadFlashStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdReadFlashStatistics, 55));
};


/**
 * @param {?proto.cmd_resp_stats.CmdReadFlashStatistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdFlashStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 55, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdFlashStats = function() {
  return this.setRdFlashStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdFlashStats = function() {
  return jspb.Message.getField(this, 55) != null;
};


/**
 * optional cmd_resp_stats.CmdReadWatchdogStatistics rd_watchdog_stats = 56;
 * @return {?proto.cmd_resp_stats.CmdReadWatchdogStatistics}
 */
proto.wrappers.WrapperCommand.prototype.getRdWatchdogStats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdReadWatchdogStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdReadWatchdogStatistics, 56));
};


/**
 * @param {?proto.cmd_resp_stats.CmdReadWatchdogStatistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdWatchdogStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 56, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdWatchdogStats = function() {
  return this.setRdWatchdogStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdWatchdogStats = function() {
  return jspb.Message.getField(this, 56) != null;
};


/**
 * optional cmd_resp_stats.CmdGetTimeDatesDst rd_date_time_dst = 57;
 * @return {?proto.cmd_resp_stats.CmdGetTimeDatesDst}
 */
proto.wrappers.WrapperCommand.prototype.getRdDateTimeDst = function() {
  return /** @type{?proto.cmd_resp_stats.CmdGetTimeDatesDst} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdGetTimeDatesDst, 57));
};


/**
 * @param {?proto.cmd_resp_stats.CmdGetTimeDatesDst|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRdDateTimeDst = function(value) {
  return jspb.Message.setOneofWrapperField(this, 57, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRdDateTimeDst = function() {
  return this.setRdDateTimeDst(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRdDateTimeDst = function() {
  return jspb.Message.getField(this, 57) != null;
};


/**
 * optional cmd_resp_stats.CmdClearStatistics clear_stats = 58;
 * @return {?proto.cmd_resp_stats.CmdClearStatistics}
 */
proto.wrappers.WrapperCommand.prototype.getClearStats = function() {
  return /** @type{?proto.cmd_resp_stats.CmdClearStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdClearStatistics, 58));
};


/**
 * @param {?proto.cmd_resp_stats.CmdClearStatistics|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setClearStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 58, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearClearStats = function() {
  return this.setClearStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasClearStats = function() {
  return jspb.Message.getField(this, 58) != null;
};


/**
 * optional cmd_resp_stats.CmdSetTimeDatesDst wr_date_time_dst = 59;
 * @return {?proto.cmd_resp_stats.CmdSetTimeDatesDst}
 */
proto.wrappers.WrapperCommand.prototype.getWrDateTimeDst = function() {
  return /** @type{?proto.cmd_resp_stats.CmdSetTimeDatesDst} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdSetTimeDatesDst, 59));
};


/**
 * @param {?proto.cmd_resp_stats.CmdSetTimeDatesDst|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setWrDateTimeDst = function(value) {
  return jspb.Message.setOneofWrapperField(this, 59, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearWrDateTimeDst = function() {
  return this.setWrDateTimeDst(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasWrDateTimeDst = function() {
  return jspb.Message.getField(this, 59) != null;
};


/**
 * optional cmd_resp_stats.CmdRemoteDisplayButtonEvent remote_display_button_event = 65;
 * @return {?proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent}
 */
proto.wrappers.WrapperCommand.prototype.getRemoteDisplayButtonEvent = function() {
  return /** @type{?proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.CmdRemoteDisplayButtonEvent, 65));
};


/**
 * @param {?proto.cmd_resp_stats.CmdRemoteDisplayButtonEvent|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRemoteDisplayButtonEvent = function(value) {
  return jspb.Message.setOneofWrapperField(this, 65, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRemoteDisplayButtonEvent = function() {
  return this.setRemoteDisplayButtonEvent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRemoteDisplayButtonEvent = function() {
  return jspb.Message.getField(this, 65) != null;
};


/**
 * optional cmd_resp_realtime.CmdStartRealtimeData start_realtime = 60;
 * @return {?proto.cmd_resp_realtime.CmdStartRealtimeData}
 */
proto.wrappers.WrapperCommand.prototype.getStartRealtime = function() {
  return /** @type{?proto.cmd_resp_realtime.CmdStartRealtimeData} */ (
    jspb.Message.getWrapperField(this, cmd_resp_realtime_pb.CmdStartRealtimeData, 60));
};


/**
 * @param {?proto.cmd_resp_realtime.CmdStartRealtimeData|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setStartRealtime = function(value) {
  return jspb.Message.setOneofWrapperField(this, 60, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearStartRealtime = function() {
  return this.setStartRealtime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasStartRealtime = function() {
  return jspb.Message.getField(this, 60) != null;
};


/**
 * optional cmd_resp_realtime.CmdStopRealtimeData stop_realtime = 61;
 * @return {?proto.cmd_resp_realtime.CmdStopRealtimeData}
 */
proto.wrappers.WrapperCommand.prototype.getStopRealtime = function() {
  return /** @type{?proto.cmd_resp_realtime.CmdStopRealtimeData} */ (
    jspb.Message.getWrapperField(this, cmd_resp_realtime_pb.CmdStopRealtimeData, 61));
};


/**
 * @param {?proto.cmd_resp_realtime.CmdStopRealtimeData|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setStopRealtime = function(value) {
  return jspb.Message.setOneofWrapperField(this, 61, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearStopRealtime = function() {
  return this.setStopRealtime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasStopRealtime = function() {
  return jspb.Message.getField(this, 61) != null;
};


/**
 * optional cmd_resp_dfu.CmdManifestVersions manifest_versions = 70;
 * @return {?proto.cmd_resp_dfu.CmdManifestVersions}
 */
proto.wrappers.WrapperCommand.prototype.getManifestVersions = function() {
  return /** @type{?proto.cmd_resp_dfu.CmdManifestVersions} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.CmdManifestVersions, 70));
};


/**
 * @param {?proto.cmd_resp_dfu.CmdManifestVersions|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setManifestVersions = function(value) {
  return jspb.Message.setOneofWrapperField(this, 70, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearManifestVersions = function() {
  return this.setManifestVersions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasManifestVersions = function() {
  return jspb.Message.getField(this, 70) != null;
};


/**
 * optional cmd_resp_dfu.CmdRebootCommsMcu reboot_comms_mcu = 71;
 * @return {?proto.cmd_resp_dfu.CmdRebootCommsMcu}
 */
proto.wrappers.WrapperCommand.prototype.getRebootCommsMcu = function() {
  return /** @type{?proto.cmd_resp_dfu.CmdRebootCommsMcu} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.CmdRebootCommsMcu, 71));
};


/**
 * @param {?proto.cmd_resp_dfu.CmdRebootCommsMcu|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setRebootCommsMcu = function(value) {
  return jspb.Message.setOneofWrapperField(this, 71, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearRebootCommsMcu = function() {
  return this.setRebootCommsMcu(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasRebootCommsMcu = function() {
  return jspb.Message.getField(this, 71) != null;
};


/**
 * optional cmd_resp_dfu.CmdInitiateFirmwareUpdate initiate_dfu = 72;
 * @return {?proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate}
 */
proto.wrappers.WrapperCommand.prototype.getInitiateDfu = function() {
  return /** @type{?proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.CmdInitiateFirmwareUpdate, 72));
};


/**
 * @param {?proto.cmd_resp_dfu.CmdInitiateFirmwareUpdate|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setInitiateDfu = function(value) {
  return jspb.Message.setOneofWrapperField(this, 72, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearInitiateDfu = function() {
  return this.setInitiateDfu(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasInitiateDfu = function() {
  return jspb.Message.getField(this, 72) != null;
};


/**
 * optional cmd_resp_dfu.CmdFirmwareUpdateManifest send_fw_manifest = 73;
 * @return {?proto.cmd_resp_dfu.CmdFirmwareUpdateManifest}
 */
proto.wrappers.WrapperCommand.prototype.getSendFwManifest = function() {
  return /** @type{?proto.cmd_resp_dfu.CmdFirmwareUpdateManifest} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.CmdFirmwareUpdateManifest, 73));
};


/**
 * @param {?proto.cmd_resp_dfu.CmdFirmwareUpdateManifest|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setSendFwManifest = function(value) {
  return jspb.Message.setOneofWrapperField(this, 73, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearSendFwManifest = function() {
  return this.setSendFwManifest(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasSendFwManifest = function() {
  return jspb.Message.getField(this, 73) != null;
};


/**
 * optional cmd_resp_dfu.CmdBeginFirmwareDownload begin_firmware_download = 74;
 * @return {?proto.cmd_resp_dfu.CmdBeginFirmwareDownload}
 */
proto.wrappers.WrapperCommand.prototype.getBeginFirmwareDownload = function() {
  return /** @type{?proto.cmd_resp_dfu.CmdBeginFirmwareDownload} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.CmdBeginFirmwareDownload, 74));
};


/**
 * @param {?proto.cmd_resp_dfu.CmdBeginFirmwareDownload|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setBeginFirmwareDownload = function(value) {
  return jspb.Message.setOneofWrapperField(this, 74, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearBeginFirmwareDownload = function() {
  return this.setBeginFirmwareDownload(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasBeginFirmwareDownload = function() {
  return jspb.Message.getField(this, 74) != null;
};


/**
 * optional cmd_resp_dfu.CmdFirmwareDownloadChunk download_firmware_chunk = 75;
 * @return {?proto.cmd_resp_dfu.CmdFirmwareDownloadChunk}
 */
proto.wrappers.WrapperCommand.prototype.getDownloadFirmwareChunk = function() {
  return /** @type{?proto.cmd_resp_dfu.CmdFirmwareDownloadChunk} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.CmdFirmwareDownloadChunk, 75));
};


/**
 * @param {?proto.cmd_resp_dfu.CmdFirmwareDownloadChunk|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setDownloadFirmwareChunk = function(value) {
  return jspb.Message.setOneofWrapperField(this, 75, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearDownloadFirmwareChunk = function() {
  return this.setDownloadFirmwareChunk(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasDownloadFirmwareChunk = function() {
  return jspb.Message.getField(this, 75) != null;
};


/**
 * optional cmd_resp_comms.CmdChunkTest test_chunk = 90;
 * @return {?proto.cmd_resp_comms.CmdChunkTest}
 */
proto.wrappers.WrapperCommand.prototype.getTestChunk = function() {
  return /** @type{?proto.cmd_resp_comms.CmdChunkTest} */ (
    jspb.Message.getWrapperField(this, cmd_resp_comms_pb.CmdChunkTest, 90));
};


/**
 * @param {?proto.cmd_resp_comms.CmdChunkTest|undefined} value
 * @return {!proto.wrappers.WrapperCommand} returns this
*/
proto.wrappers.WrapperCommand.prototype.setTestChunk = function(value) {
  return jspb.Message.setOneofWrapperField(this, 90, proto.wrappers.WrapperCommand.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperCommand} returns this
 */
proto.wrappers.WrapperCommand.prototype.clearTestChunk = function() {
  return this.setTestChunk(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperCommand.prototype.hasTestChunk = function() {
  return jspb.Message.getField(this, 90) != null;
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.wrappers.WrapperResponse.oneofGroups_ = [[11,20,21,22,23,24,25,26,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,50,51,52,53,54,55,56,57,58,59,65,60,61,70,71,72,73,74,75,80,81,90]];

/**
 * @enum {number}
 */
proto.wrappers.WrapperResponse.ResponseCase = {
  RESPONSE_NOT_SET: 0,
  AUTH_STATUS: 11,
  REQUEST_LOG_COUNTS: 20,
  LOG_CLEAR: 21,
  REQUEST_LOG: 22,
  REQ_AUDITLOG_COUNTS: 23,
  AUDITLOG_CLEAR: 24,
  AUDIT_LOG_RESET: 25,
  REQUEST_AUDITLOG: 26,
  RD_MONITOR_DATA: 31,
  RD_NETWORK_CONFIG_UNIT: 32,
  RD_NETWORK_CONFIG_ACTIVE: 33,
  RD_CHANNEL_CONFIG: 34,
  RD_CHANNEL_CURRENT_SENSE: 35,
  RD_CHANNEL_PERMISSIVES: 36,
  RD_FYA_CONFIG: 37,
  RD_DATA_KEY: 38,
  WR_DATA_KEY: 39,
  RD_FACTORY_SETTINGS: 40,
  WR_FACTORY_SETTINGS: 41,
  RD_USER_SETTINGS: 42,
  WR_USER_SETTINGS: 43,
  RD_PORT1_DISABLES: 44,
  WR_PORT1_DISABLES: 45,
  WR_AGENCY_OPTIONS: 46,
  REMOTE_RESET: 47,
  RD_PORT1_STATS: 50,
  RD_DATA_KEY_STATS: 51,
  RD_MAIN_ISO_COMMS_STATS: 52,
  RD_MAIN_COMMS_STATS: 53,
  RD_COMMS_MAIN_STATS: 54,
  RD_FLASH_STATS: 55,
  RD_WATCHDOG_STATS: 56,
  RD_DATE_TIME_DST: 57,
  CLEAR_STATS: 58,
  WR_DATE_TIME_DST: 59,
  REMOTE_DISPLAY_BUTTON_EVENT: 65,
  START_REALTIME: 60,
  STOP_REALTIME: 61,
  MANIFEST_VERSIONS: 70,
  REBOOT_COMMS_MCU: 71,
  INITIATE_DFU: 72,
  SEND_FW_MANIFEST: 73,
  BEGIN_FIRMWARE_DOWNLOAD: 74,
  DOWNLOAD_FIRMWARE_CHUNK: 75,
  REALTIME_DATA: 80,
  REALTIME_DISPLAY: 81,
  TEST_CHUNK: 90
};

/**
 * @return {proto.wrappers.WrapperResponse.ResponseCase}
 */
proto.wrappers.WrapperResponse.prototype.getResponseCase = function() {
  return /** @type {proto.wrappers.WrapperResponse.ResponseCase} */(jspb.Message.computeOneofCase(this, proto.wrappers.WrapperResponse.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.wrappers.WrapperResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.wrappers.WrapperResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.wrappers.WrapperResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.wrappers.WrapperResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
version: jspb.Message.getFieldWithDefault(msg, 1, 0),
requestId: jspb.Message.getFieldWithDefault(msg, 2, 0),
code: jspb.Message.getFieldWithDefault(msg, 3, 0),
resultTxt: jspb.Message.getFieldWithDefault(msg, 4, ""),
authStatus: (f = msg.getAuthStatus()) && cmd_resp_comms_pb.RespAuthStatus.toObject(includeInstance, f),
requestLogCounts: (f = msg.getRequestLogCounts()) && cmd_resp_logs_pb.RespRequestLogCounts.toObject(includeInstance, f),
logClear: (f = msg.getLogClear()) && cmd_resp_logs_pb.RespRequestLogClear.toObject(includeInstance, f),
requestLog: (f = msg.getRequestLog()) && cmd_resp_logs_pb.RespRequestLogEntries.toObject(includeInstance, f),
reqAuditlogCounts: (f = msg.getReqAuditlogCounts()) && cmd_resp_logs_pb.RespRequestAuditLogCounts.toObject(includeInstance, f),
auditlogClear: (f = msg.getAuditlogClear()) && cmd_resp_logs_pb.RespRequestAuditLogClear.toObject(includeInstance, f),
auditLogReset: (f = msg.getAuditLogReset()) && cmd_resp_logs_pb.RespRequestAuditLogReset.toObject(includeInstance, f),
requestAuditlog: (f = msg.getRequestAuditlog()) && cmd_resp_logs_pb.RespRequestAuditLogEntries.toObject(includeInstance, f),
rdMonitorData: (f = msg.getRdMonitorData()) && cmd_resp_config_pb.RespReadMonitorData.toObject(includeInstance, f),
rdNetworkConfigUnit: (f = msg.getRdNetworkConfigUnit()) && cmd_resp_config_pb.RespReadUnitNetworkConfiguration.toObject(includeInstance, f),
rdNetworkConfigActive: (f = msg.getRdNetworkConfigActive()) && cmd_resp_config_pb.RespReadActiveNetworkConfiguration.toObject(includeInstance, f),
rdChannelConfig: (f = msg.getRdChannelConfig()) && cmd_resp_config_pb.RespReadPerChannelConfiguration.toObject(includeInstance, f),
rdChannelCurrentSense: (f = msg.getRdChannelCurrentSense()) && cmd_resp_config_pb.RespReadPerChannelCurrentSenseSettings.toObject(includeInstance, f),
rdChannelPermissives: (f = msg.getRdChannelPermissives()) && cmd_resp_config_pb.RespReadPerChannelPermissiveSettings.toObject(includeInstance, f),
rdFyaConfig: (f = msg.getRdFyaConfig()) && cmd_resp_config_pb.RespReadFlashingYellowArrowConfiguration.toObject(includeInstance, f),
rdDataKey: (f = msg.getRdDataKey()) && cmd_resp_config_pb.RespReadDataKey.toObject(includeInstance, f),
wrDataKey: (f = msg.getWrDataKey()) && cmd_resp_config_pb.RespWriteDataKey.toObject(includeInstance, f),
rdFactorySettings: (f = msg.getRdFactorySettings()) && cmd_resp_config_pb.RespReadFactorySettings.toObject(includeInstance, f),
wrFactorySettings: (f = msg.getWrFactorySettings()) && cmd_resp_config_pb.RespWriteFactorySettings.toObject(includeInstance, f),
rdUserSettings: (f = msg.getRdUserSettings()) && cmd_resp_config_pb.RespReadUserSettings.toObject(includeInstance, f),
wrUserSettings: (f = msg.getWrUserSettings()) && cmd_resp_config_pb.RespWriteUserSettings.toObject(includeInstance, f),
rdPort1Disables: (f = msg.getRdPort1Disables()) && cmd_resp_config_pb.RespReadPort1DisableOverrides.toObject(includeInstance, f),
wrPort1Disables: (f = msg.getWrPort1Disables()) && cmd_resp_config_pb.RespWritePort1DisableOverrides.toObject(includeInstance, f),
wrAgencyOptions: (f = msg.getWrAgencyOptions()) && cmd_resp_config_pb.RespWriteAgencyOptions.toObject(includeInstance, f),
remoteReset: (f = msg.getRemoteReset()) && cmd_resp_config_pb.RespRemoteReset.toObject(includeInstance, f),
rdPort1Stats: (f = msg.getRdPort1Stats()) && cmd_resp_stats_pb.RespReadPort1Statistics.toObject(includeInstance, f),
rdDataKeyStats: (f = msg.getRdDataKeyStats()) && cmd_resp_stats_pb.RespReadDataKeyStatistics.toObject(includeInstance, f),
rdMainIsoCommsStats: (f = msg.getRdMainIsoCommsStats()) && cmd_resp_stats_pb.RespReadMainToIsolatedCommStatistics.toObject(includeInstance, f),
rdMainCommsStats: (f = msg.getRdMainCommsStats()) && cmd_resp_stats_pb.RespReadMainToCommsCommStatistics.toObject(includeInstance, f),
rdCommsMainStats: (f = msg.getRdCommsMainStats()) && cmd_resp_stats_pb.RespReadCommsToMainCommStatistics.toObject(includeInstance, f),
rdFlashStats: (f = msg.getRdFlashStats()) && cmd_resp_stats_pb.RespReadFlashStatistics.toObject(includeInstance, f),
rdWatchdogStats: (f = msg.getRdWatchdogStats()) && cmd_resp_stats_pb.RespReadWatchdogStatistics.toObject(includeInstance, f),
rdDateTimeDst: (f = msg.getRdDateTimeDst()) && cmd_resp_stats_pb.RespGetTimeDatesDst.toObject(includeInstance, f),
clearStats: (f = msg.getClearStats()) && cmd_resp_stats_pb.RespClearStatistics.toObject(includeInstance, f),
wrDateTimeDst: (f = msg.getWrDateTimeDst()) && cmd_resp_stats_pb.RespSetTimeDatesDst.toObject(includeInstance, f),
remoteDisplayButtonEvent: (f = msg.getRemoteDisplayButtonEvent()) && cmd_resp_stats_pb.RespRemoteDisplayButtonEvent.toObject(includeInstance, f),
startRealtime: (f = msg.getStartRealtime()) && cmd_resp_realtime_pb.RespStartRealtimeData.toObject(includeInstance, f),
stopRealtime: (f = msg.getStopRealtime()) && cmd_resp_realtime_pb.RespStopRealtimeData.toObject(includeInstance, f),
manifestVersions: (f = msg.getManifestVersions()) && cmd_resp_dfu_pb.RespManifestVersions.toObject(includeInstance, f),
rebootCommsMcu: (f = msg.getRebootCommsMcu()) && cmd_resp_dfu_pb.RespRebootCommsMcu.toObject(includeInstance, f),
initiateDfu: (f = msg.getInitiateDfu()) && cmd_resp_dfu_pb.RespInitiateFirmwareUpdate.toObject(includeInstance, f),
sendFwManifest: (f = msg.getSendFwManifest()) && cmd_resp_dfu_pb.RespFirmwareUpdateManifest.toObject(includeInstance, f),
beginFirmwareDownload: (f = msg.getBeginFirmwareDownload()) && cmd_resp_dfu_pb.RespBeginFirmwareDownload.toObject(includeInstance, f),
downloadFirmwareChunk: (f = msg.getDownloadFirmwareChunk()) && cmd_resp_dfu_pb.RespFirmwareDownloadChunk.toObject(includeInstance, f),
realtimeData: (f = msg.getRealtimeData()) && cmd_resp_realtime_pb.RealtimeData1.toObject(includeInstance, f),
realtimeDisplay: (f = msg.getRealtimeDisplay()) && cmd_resp_realtime_pb.RealtimeDisplay1.toObject(includeInstance, f),
testChunk: (f = msg.getTestChunk()) && cmd_resp_comms_pb.RespChunkTest.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.wrappers.WrapperResponse}
 */
proto.wrappers.WrapperResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.wrappers.WrapperResponse;
  return proto.wrappers.WrapperResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.wrappers.WrapperResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.wrappers.WrapperResponse}
 */
proto.wrappers.WrapperResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setVersion(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRequestId(value);
      break;
    case 3:
      var value = /** @type {!proto.wrappers.EResponseCodes} */ (reader.readEnum());
      msg.setCode(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setResultTxt(value);
      break;
    case 11:
      var value = new cmd_resp_comms_pb.RespAuthStatus;
      reader.readMessage(value,cmd_resp_comms_pb.RespAuthStatus.deserializeBinaryFromReader);
      msg.setAuthStatus(value);
      break;
    case 20:
      var value = new cmd_resp_logs_pb.RespRequestLogCounts;
      reader.readMessage(value,cmd_resp_logs_pb.RespRequestLogCounts.deserializeBinaryFromReader);
      msg.setRequestLogCounts(value);
      break;
    case 21:
      var value = new cmd_resp_logs_pb.RespRequestLogClear;
      reader.readMessage(value,cmd_resp_logs_pb.RespRequestLogClear.deserializeBinaryFromReader);
      msg.setLogClear(value);
      break;
    case 22:
      var value = new cmd_resp_logs_pb.RespRequestLogEntries;
      reader.readMessage(value,cmd_resp_logs_pb.RespRequestLogEntries.deserializeBinaryFromReader);
      msg.setRequestLog(value);
      break;
    case 23:
      var value = new cmd_resp_logs_pb.RespRequestAuditLogCounts;
      reader.readMessage(value,cmd_resp_logs_pb.RespRequestAuditLogCounts.deserializeBinaryFromReader);
      msg.setReqAuditlogCounts(value);
      break;
    case 24:
      var value = new cmd_resp_logs_pb.RespRequestAuditLogClear;
      reader.readMessage(value,cmd_resp_logs_pb.RespRequestAuditLogClear.deserializeBinaryFromReader);
      msg.setAuditlogClear(value);
      break;
    case 25:
      var value = new cmd_resp_logs_pb.RespRequestAuditLogReset;
      reader.readMessage(value,cmd_resp_logs_pb.RespRequestAuditLogReset.deserializeBinaryFromReader);
      msg.setAuditLogReset(value);
      break;
    case 26:
      var value = new cmd_resp_logs_pb.RespRequestAuditLogEntries;
      reader.readMessage(value,cmd_resp_logs_pb.RespRequestAuditLogEntries.deserializeBinaryFromReader);
      msg.setRequestAuditlog(value);
      break;
    case 31:
      var value = new cmd_resp_config_pb.RespReadMonitorData;
      reader.readMessage(value,cmd_resp_config_pb.RespReadMonitorData.deserializeBinaryFromReader);
      msg.setRdMonitorData(value);
      break;
    case 32:
      var value = new cmd_resp_config_pb.RespReadUnitNetworkConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.RespReadUnitNetworkConfiguration.deserializeBinaryFromReader);
      msg.setRdNetworkConfigUnit(value);
      break;
    case 33:
      var value = new cmd_resp_config_pb.RespReadActiveNetworkConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.RespReadActiveNetworkConfiguration.deserializeBinaryFromReader);
      msg.setRdNetworkConfigActive(value);
      break;
    case 34:
      var value = new cmd_resp_config_pb.RespReadPerChannelConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.RespReadPerChannelConfiguration.deserializeBinaryFromReader);
      msg.setRdChannelConfig(value);
      break;
    case 35:
      var value = new cmd_resp_config_pb.RespReadPerChannelCurrentSenseSettings;
      reader.readMessage(value,cmd_resp_config_pb.RespReadPerChannelCurrentSenseSettings.deserializeBinaryFromReader);
      msg.setRdChannelCurrentSense(value);
      break;
    case 36:
      var value = new cmd_resp_config_pb.RespReadPerChannelPermissiveSettings;
      reader.readMessage(value,cmd_resp_config_pb.RespReadPerChannelPermissiveSettings.deserializeBinaryFromReader);
      msg.setRdChannelPermissives(value);
      break;
    case 37:
      var value = new cmd_resp_config_pb.RespReadFlashingYellowArrowConfiguration;
      reader.readMessage(value,cmd_resp_config_pb.RespReadFlashingYellowArrowConfiguration.deserializeBinaryFromReader);
      msg.setRdFyaConfig(value);
      break;
    case 38:
      var value = new cmd_resp_config_pb.RespReadDataKey;
      reader.readMessage(value,cmd_resp_config_pb.RespReadDataKey.deserializeBinaryFromReader);
      msg.setRdDataKey(value);
      break;
    case 39:
      var value = new cmd_resp_config_pb.RespWriteDataKey;
      reader.readMessage(value,cmd_resp_config_pb.RespWriteDataKey.deserializeBinaryFromReader);
      msg.setWrDataKey(value);
      break;
    case 40:
      var value = new cmd_resp_config_pb.RespReadFactorySettings;
      reader.readMessage(value,cmd_resp_config_pb.RespReadFactorySettings.deserializeBinaryFromReader);
      msg.setRdFactorySettings(value);
      break;
    case 41:
      var value = new cmd_resp_config_pb.RespWriteFactorySettings;
      reader.readMessage(value,cmd_resp_config_pb.RespWriteFactorySettings.deserializeBinaryFromReader);
      msg.setWrFactorySettings(value);
      break;
    case 42:
      var value = new cmd_resp_config_pb.RespReadUserSettings;
      reader.readMessage(value,cmd_resp_config_pb.RespReadUserSettings.deserializeBinaryFromReader);
      msg.setRdUserSettings(value);
      break;
    case 43:
      var value = new cmd_resp_config_pb.RespWriteUserSettings;
      reader.readMessage(value,cmd_resp_config_pb.RespWriteUserSettings.deserializeBinaryFromReader);
      msg.setWrUserSettings(value);
      break;
    case 44:
      var value = new cmd_resp_config_pb.RespReadPort1DisableOverrides;
      reader.readMessage(value,cmd_resp_config_pb.RespReadPort1DisableOverrides.deserializeBinaryFromReader);
      msg.setRdPort1Disables(value);
      break;
    case 45:
      var value = new cmd_resp_config_pb.RespWritePort1DisableOverrides;
      reader.readMessage(value,cmd_resp_config_pb.RespWritePort1DisableOverrides.deserializeBinaryFromReader);
      msg.setWrPort1Disables(value);
      break;
    case 46:
      var value = new cmd_resp_config_pb.RespWriteAgencyOptions;
      reader.readMessage(value,cmd_resp_config_pb.RespWriteAgencyOptions.deserializeBinaryFromReader);
      msg.setWrAgencyOptions(value);
      break;
    case 47:
      var value = new cmd_resp_config_pb.RespRemoteReset;
      reader.readMessage(value,cmd_resp_config_pb.RespRemoteReset.deserializeBinaryFromReader);
      msg.setRemoteReset(value);
      break;
    case 50:
      var value = new cmd_resp_stats_pb.RespReadPort1Statistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespReadPort1Statistics.deserializeBinaryFromReader);
      msg.setRdPort1Stats(value);
      break;
    case 51:
      var value = new cmd_resp_stats_pb.RespReadDataKeyStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespReadDataKeyStatistics.deserializeBinaryFromReader);
      msg.setRdDataKeyStats(value);
      break;
    case 52:
      var value = new cmd_resp_stats_pb.RespReadMainToIsolatedCommStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespReadMainToIsolatedCommStatistics.deserializeBinaryFromReader);
      msg.setRdMainIsoCommsStats(value);
      break;
    case 53:
      var value = new cmd_resp_stats_pb.RespReadMainToCommsCommStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespReadMainToCommsCommStatistics.deserializeBinaryFromReader);
      msg.setRdMainCommsStats(value);
      break;
    case 54:
      var value = new cmd_resp_stats_pb.RespReadCommsToMainCommStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespReadCommsToMainCommStatistics.deserializeBinaryFromReader);
      msg.setRdCommsMainStats(value);
      break;
    case 55:
      var value = new cmd_resp_stats_pb.RespReadFlashStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespReadFlashStatistics.deserializeBinaryFromReader);
      msg.setRdFlashStats(value);
      break;
    case 56:
      var value = new cmd_resp_stats_pb.RespReadWatchdogStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespReadWatchdogStatistics.deserializeBinaryFromReader);
      msg.setRdWatchdogStats(value);
      break;
    case 57:
      var value = new cmd_resp_stats_pb.RespGetTimeDatesDst;
      reader.readMessage(value,cmd_resp_stats_pb.RespGetTimeDatesDst.deserializeBinaryFromReader);
      msg.setRdDateTimeDst(value);
      break;
    case 58:
      var value = new cmd_resp_stats_pb.RespClearStatistics;
      reader.readMessage(value,cmd_resp_stats_pb.RespClearStatistics.deserializeBinaryFromReader);
      msg.setClearStats(value);
      break;
    case 59:
      var value = new cmd_resp_stats_pb.RespSetTimeDatesDst;
      reader.readMessage(value,cmd_resp_stats_pb.RespSetTimeDatesDst.deserializeBinaryFromReader);
      msg.setWrDateTimeDst(value);
      break;
    case 65:
      var value = new cmd_resp_stats_pb.RespRemoteDisplayButtonEvent;
      reader.readMessage(value,cmd_resp_stats_pb.RespRemoteDisplayButtonEvent.deserializeBinaryFromReader);
      msg.setRemoteDisplayButtonEvent(value);
      break;
    case 60:
      var value = new cmd_resp_realtime_pb.RespStartRealtimeData;
      reader.readMessage(value,cmd_resp_realtime_pb.RespStartRealtimeData.deserializeBinaryFromReader);
      msg.setStartRealtime(value);
      break;
    case 61:
      var value = new cmd_resp_realtime_pb.RespStopRealtimeData;
      reader.readMessage(value,cmd_resp_realtime_pb.RespStopRealtimeData.deserializeBinaryFromReader);
      msg.setStopRealtime(value);
      break;
    case 70:
      var value = new cmd_resp_dfu_pb.RespManifestVersions;
      reader.readMessage(value,cmd_resp_dfu_pb.RespManifestVersions.deserializeBinaryFromReader);
      msg.setManifestVersions(value);
      break;
    case 71:
      var value = new cmd_resp_dfu_pb.RespRebootCommsMcu;
      reader.readMessage(value,cmd_resp_dfu_pb.RespRebootCommsMcu.deserializeBinaryFromReader);
      msg.setRebootCommsMcu(value);
      break;
    case 72:
      var value = new cmd_resp_dfu_pb.RespInitiateFirmwareUpdate;
      reader.readMessage(value,cmd_resp_dfu_pb.RespInitiateFirmwareUpdate.deserializeBinaryFromReader);
      msg.setInitiateDfu(value);
      break;
    case 73:
      var value = new cmd_resp_dfu_pb.RespFirmwareUpdateManifest;
      reader.readMessage(value,cmd_resp_dfu_pb.RespFirmwareUpdateManifest.deserializeBinaryFromReader);
      msg.setSendFwManifest(value);
      break;
    case 74:
      var value = new cmd_resp_dfu_pb.RespBeginFirmwareDownload;
      reader.readMessage(value,cmd_resp_dfu_pb.RespBeginFirmwareDownload.deserializeBinaryFromReader);
      msg.setBeginFirmwareDownload(value);
      break;
    case 75:
      var value = new cmd_resp_dfu_pb.RespFirmwareDownloadChunk;
      reader.readMessage(value,cmd_resp_dfu_pb.RespFirmwareDownloadChunk.deserializeBinaryFromReader);
      msg.setDownloadFirmwareChunk(value);
      break;
    case 80:
      var value = new cmd_resp_realtime_pb.RealtimeData1;
      reader.readMessage(value,cmd_resp_realtime_pb.RealtimeData1.deserializeBinaryFromReader);
      msg.setRealtimeData(value);
      break;
    case 81:
      var value = new cmd_resp_realtime_pb.RealtimeDisplay1;
      reader.readMessage(value,cmd_resp_realtime_pb.RealtimeDisplay1.deserializeBinaryFromReader);
      msg.setRealtimeDisplay(value);
      break;
    case 90:
      var value = new cmd_resp_comms_pb.RespChunkTest;
      reader.readMessage(value,cmd_resp_comms_pb.RespChunkTest.deserializeBinaryFromReader);
      msg.setTestChunk(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.wrappers.WrapperResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.wrappers.WrapperResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.wrappers.WrapperResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.wrappers.WrapperResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVersion();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getRequestId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getCode();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
  f = message.getResultTxt();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getAuthStatus();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      cmd_resp_comms_pb.RespAuthStatus.serializeBinaryToWriter
    );
  }
  f = message.getRequestLogCounts();
  if (f != null) {
    writer.writeMessage(
      20,
      f,
      cmd_resp_logs_pb.RespRequestLogCounts.serializeBinaryToWriter
    );
  }
  f = message.getLogClear();
  if (f != null) {
    writer.writeMessage(
      21,
      f,
      cmd_resp_logs_pb.RespRequestLogClear.serializeBinaryToWriter
    );
  }
  f = message.getRequestLog();
  if (f != null) {
    writer.writeMessage(
      22,
      f,
      cmd_resp_logs_pb.RespRequestLogEntries.serializeBinaryToWriter
    );
  }
  f = message.getReqAuditlogCounts();
  if (f != null) {
    writer.writeMessage(
      23,
      f,
      cmd_resp_logs_pb.RespRequestAuditLogCounts.serializeBinaryToWriter
    );
  }
  f = message.getAuditlogClear();
  if (f != null) {
    writer.writeMessage(
      24,
      f,
      cmd_resp_logs_pb.RespRequestAuditLogClear.serializeBinaryToWriter
    );
  }
  f = message.getAuditLogReset();
  if (f != null) {
    writer.writeMessage(
      25,
      f,
      cmd_resp_logs_pb.RespRequestAuditLogReset.serializeBinaryToWriter
    );
  }
  f = message.getRequestAuditlog();
  if (f != null) {
    writer.writeMessage(
      26,
      f,
      cmd_resp_logs_pb.RespRequestAuditLogEntries.serializeBinaryToWriter
    );
  }
  f = message.getRdMonitorData();
  if (f != null) {
    writer.writeMessage(
      31,
      f,
      cmd_resp_config_pb.RespReadMonitorData.serializeBinaryToWriter
    );
  }
  f = message.getRdNetworkConfigUnit();
  if (f != null) {
    writer.writeMessage(
      32,
      f,
      cmd_resp_config_pb.RespReadUnitNetworkConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdNetworkConfigActive();
  if (f != null) {
    writer.writeMessage(
      33,
      f,
      cmd_resp_config_pb.RespReadActiveNetworkConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdChannelConfig();
  if (f != null) {
    writer.writeMessage(
      34,
      f,
      cmd_resp_config_pb.RespReadPerChannelConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdChannelCurrentSense();
  if (f != null) {
    writer.writeMessage(
      35,
      f,
      cmd_resp_config_pb.RespReadPerChannelCurrentSenseSettings.serializeBinaryToWriter
    );
  }
  f = message.getRdChannelPermissives();
  if (f != null) {
    writer.writeMessage(
      36,
      f,
      cmd_resp_config_pb.RespReadPerChannelPermissiveSettings.serializeBinaryToWriter
    );
  }
  f = message.getRdFyaConfig();
  if (f != null) {
    writer.writeMessage(
      37,
      f,
      cmd_resp_config_pb.RespReadFlashingYellowArrowConfiguration.serializeBinaryToWriter
    );
  }
  f = message.getRdDataKey();
  if (f != null) {
    writer.writeMessage(
      38,
      f,
      cmd_resp_config_pb.RespReadDataKey.serializeBinaryToWriter
    );
  }
  f = message.getWrDataKey();
  if (f != null) {
    writer.writeMessage(
      39,
      f,
      cmd_resp_config_pb.RespWriteDataKey.serializeBinaryToWriter
    );
  }
  f = message.getRdFactorySettings();
  if (f != null) {
    writer.writeMessage(
      40,
      f,
      cmd_resp_config_pb.RespReadFactorySettings.serializeBinaryToWriter
    );
  }
  f = message.getWrFactorySettings();
  if (f != null) {
    writer.writeMessage(
      41,
      f,
      cmd_resp_config_pb.RespWriteFactorySettings.serializeBinaryToWriter
    );
  }
  f = message.getRdUserSettings();
  if (f != null) {
    writer.writeMessage(
      42,
      f,
      cmd_resp_config_pb.RespReadUserSettings.serializeBinaryToWriter
    );
  }
  f = message.getWrUserSettings();
  if (f != null) {
    writer.writeMessage(
      43,
      f,
      cmd_resp_config_pb.RespWriteUserSettings.serializeBinaryToWriter
    );
  }
  f = message.getRdPort1Disables();
  if (f != null) {
    writer.writeMessage(
      44,
      f,
      cmd_resp_config_pb.RespReadPort1DisableOverrides.serializeBinaryToWriter
    );
  }
  f = message.getWrPort1Disables();
  if (f != null) {
    writer.writeMessage(
      45,
      f,
      cmd_resp_config_pb.RespWritePort1DisableOverrides.serializeBinaryToWriter
    );
  }
  f = message.getWrAgencyOptions();
  if (f != null) {
    writer.writeMessage(
      46,
      f,
      cmd_resp_config_pb.RespWriteAgencyOptions.serializeBinaryToWriter
    );
  }
  f = message.getRemoteReset();
  if (f != null) {
    writer.writeMessage(
      47,
      f,
      cmd_resp_config_pb.RespRemoteReset.serializeBinaryToWriter
    );
  }
  f = message.getRdPort1Stats();
  if (f != null) {
    writer.writeMessage(
      50,
      f,
      cmd_resp_stats_pb.RespReadPort1Statistics.serializeBinaryToWriter
    );
  }
  f = message.getRdDataKeyStats();
  if (f != null) {
    writer.writeMessage(
      51,
      f,
      cmd_resp_stats_pb.RespReadDataKeyStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdMainIsoCommsStats();
  if (f != null) {
    writer.writeMessage(
      52,
      f,
      cmd_resp_stats_pb.RespReadMainToIsolatedCommStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdMainCommsStats();
  if (f != null) {
    writer.writeMessage(
      53,
      f,
      cmd_resp_stats_pb.RespReadMainToCommsCommStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdCommsMainStats();
  if (f != null) {
    writer.writeMessage(
      54,
      f,
      cmd_resp_stats_pb.RespReadCommsToMainCommStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdFlashStats();
  if (f != null) {
    writer.writeMessage(
      55,
      f,
      cmd_resp_stats_pb.RespReadFlashStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdWatchdogStats();
  if (f != null) {
    writer.writeMessage(
      56,
      f,
      cmd_resp_stats_pb.RespReadWatchdogStatistics.serializeBinaryToWriter
    );
  }
  f = message.getRdDateTimeDst();
  if (f != null) {
    writer.writeMessage(
      57,
      f,
      cmd_resp_stats_pb.RespGetTimeDatesDst.serializeBinaryToWriter
    );
  }
  f = message.getClearStats();
  if (f != null) {
    writer.writeMessage(
      58,
      f,
      cmd_resp_stats_pb.RespClearStatistics.serializeBinaryToWriter
    );
  }
  f = message.getWrDateTimeDst();
  if (f != null) {
    writer.writeMessage(
      59,
      f,
      cmd_resp_stats_pb.RespSetTimeDatesDst.serializeBinaryToWriter
    );
  }
  f = message.getRemoteDisplayButtonEvent();
  if (f != null) {
    writer.writeMessage(
      65,
      f,
      cmd_resp_stats_pb.RespRemoteDisplayButtonEvent.serializeBinaryToWriter
    );
  }
  f = message.getStartRealtime();
  if (f != null) {
    writer.writeMessage(
      60,
      f,
      cmd_resp_realtime_pb.RespStartRealtimeData.serializeBinaryToWriter
    );
  }
  f = message.getStopRealtime();
  if (f != null) {
    writer.writeMessage(
      61,
      f,
      cmd_resp_realtime_pb.RespStopRealtimeData.serializeBinaryToWriter
    );
  }
  f = message.getManifestVersions();
  if (f != null) {
    writer.writeMessage(
      70,
      f,
      cmd_resp_dfu_pb.RespManifestVersions.serializeBinaryToWriter
    );
  }
  f = message.getRebootCommsMcu();
  if (f != null) {
    writer.writeMessage(
      71,
      f,
      cmd_resp_dfu_pb.RespRebootCommsMcu.serializeBinaryToWriter
    );
  }
  f = message.getInitiateDfu();
  if (f != null) {
    writer.writeMessage(
      72,
      f,
      cmd_resp_dfu_pb.RespInitiateFirmwareUpdate.serializeBinaryToWriter
    );
  }
  f = message.getSendFwManifest();
  if (f != null) {
    writer.writeMessage(
      73,
      f,
      cmd_resp_dfu_pb.RespFirmwareUpdateManifest.serializeBinaryToWriter
    );
  }
  f = message.getBeginFirmwareDownload();
  if (f != null) {
    writer.writeMessage(
      74,
      f,
      cmd_resp_dfu_pb.RespBeginFirmwareDownload.serializeBinaryToWriter
    );
  }
  f = message.getDownloadFirmwareChunk();
  if (f != null) {
    writer.writeMessage(
      75,
      f,
      cmd_resp_dfu_pb.RespFirmwareDownloadChunk.serializeBinaryToWriter
    );
  }
  f = message.getRealtimeData();
  if (f != null) {
    writer.writeMessage(
      80,
      f,
      cmd_resp_realtime_pb.RealtimeData1.serializeBinaryToWriter
    );
  }
  f = message.getRealtimeDisplay();
  if (f != null) {
    writer.writeMessage(
      81,
      f,
      cmd_resp_realtime_pb.RealtimeDisplay1.serializeBinaryToWriter
    );
  }
  f = message.getTestChunk();
  if (f != null) {
    writer.writeMessage(
      90,
      f,
      cmd_resp_comms_pb.RespChunkTest.serializeBinaryToWriter
    );
  }
};


/**
 * optional uint32 version = 1;
 * @return {number}
 */
proto.wrappers.WrapperResponse.prototype.getVersion = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.setVersion = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional uint32 request_id = 2;
 * @return {number}
 */
proto.wrappers.WrapperResponse.prototype.getRequestId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.setRequestId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional EResponseCodes code = 3;
 * @return {!proto.wrappers.EResponseCodes}
 */
proto.wrappers.WrapperResponse.prototype.getCode = function() {
  return /** @type {!proto.wrappers.EResponseCodes} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.wrappers.EResponseCodes} value
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.setCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};


/**
 * optional string result_txt = 4;
 * @return {string}
 */
proto.wrappers.WrapperResponse.prototype.getResultTxt = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.setResultTxt = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional cmd_resp_comms.RespAuthStatus auth_status = 11;
 * @return {?proto.cmd_resp_comms.RespAuthStatus}
 */
proto.wrappers.WrapperResponse.prototype.getAuthStatus = function() {
  return /** @type{?proto.cmd_resp_comms.RespAuthStatus} */ (
    jspb.Message.getWrapperField(this, cmd_resp_comms_pb.RespAuthStatus, 11));
};


/**
 * @param {?proto.cmd_resp_comms.RespAuthStatus|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setAuthStatus = function(value) {
  return jspb.Message.setOneofWrapperField(this, 11, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearAuthStatus = function() {
  return this.setAuthStatus(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasAuthStatus = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional cmd_resp_logs.RespRequestLogCounts request_log_counts = 20;
 * @return {?proto.cmd_resp_logs.RespRequestLogCounts}
 */
proto.wrappers.WrapperResponse.prototype.getRequestLogCounts = function() {
  return /** @type{?proto.cmd_resp_logs.RespRequestLogCounts} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.RespRequestLogCounts, 20));
};


/**
 * @param {?proto.cmd_resp_logs.RespRequestLogCounts|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRequestLogCounts = function(value) {
  return jspb.Message.setOneofWrapperField(this, 20, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRequestLogCounts = function() {
  return this.setRequestLogCounts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRequestLogCounts = function() {
  return jspb.Message.getField(this, 20) != null;
};


/**
 * optional cmd_resp_logs.RespRequestLogClear log_clear = 21;
 * @return {?proto.cmd_resp_logs.RespRequestLogClear}
 */
proto.wrappers.WrapperResponse.prototype.getLogClear = function() {
  return /** @type{?proto.cmd_resp_logs.RespRequestLogClear} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.RespRequestLogClear, 21));
};


/**
 * @param {?proto.cmd_resp_logs.RespRequestLogClear|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setLogClear = function(value) {
  return jspb.Message.setOneofWrapperField(this, 21, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearLogClear = function() {
  return this.setLogClear(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasLogClear = function() {
  return jspb.Message.getField(this, 21) != null;
};


/**
 * optional cmd_resp_logs.RespRequestLogEntries request_log = 22;
 * @return {?proto.cmd_resp_logs.RespRequestLogEntries}
 */
proto.wrappers.WrapperResponse.prototype.getRequestLog = function() {
  return /** @type{?proto.cmd_resp_logs.RespRequestLogEntries} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.RespRequestLogEntries, 22));
};


/**
 * @param {?proto.cmd_resp_logs.RespRequestLogEntries|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRequestLog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 22, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRequestLog = function() {
  return this.setRequestLog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRequestLog = function() {
  return jspb.Message.getField(this, 22) != null;
};


/**
 * optional cmd_resp_logs.RespRequestAuditLogCounts req_auditlog_counts = 23;
 * @return {?proto.cmd_resp_logs.RespRequestAuditLogCounts}
 */
proto.wrappers.WrapperResponse.prototype.getReqAuditlogCounts = function() {
  return /** @type{?proto.cmd_resp_logs.RespRequestAuditLogCounts} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.RespRequestAuditLogCounts, 23));
};


/**
 * @param {?proto.cmd_resp_logs.RespRequestAuditLogCounts|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setReqAuditlogCounts = function(value) {
  return jspb.Message.setOneofWrapperField(this, 23, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearReqAuditlogCounts = function() {
  return this.setReqAuditlogCounts(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasReqAuditlogCounts = function() {
  return jspb.Message.getField(this, 23) != null;
};


/**
 * optional cmd_resp_logs.RespRequestAuditLogClear auditlog_clear = 24;
 * @return {?proto.cmd_resp_logs.RespRequestAuditLogClear}
 */
proto.wrappers.WrapperResponse.prototype.getAuditlogClear = function() {
  return /** @type{?proto.cmd_resp_logs.RespRequestAuditLogClear} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.RespRequestAuditLogClear, 24));
};


/**
 * @param {?proto.cmd_resp_logs.RespRequestAuditLogClear|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setAuditlogClear = function(value) {
  return jspb.Message.setOneofWrapperField(this, 24, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearAuditlogClear = function() {
  return this.setAuditlogClear(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasAuditlogClear = function() {
  return jspb.Message.getField(this, 24) != null;
};


/**
 * optional cmd_resp_logs.RespRequestAuditLogReset audit_log_reset = 25;
 * @return {?proto.cmd_resp_logs.RespRequestAuditLogReset}
 */
proto.wrappers.WrapperResponse.prototype.getAuditLogReset = function() {
  return /** @type{?proto.cmd_resp_logs.RespRequestAuditLogReset} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.RespRequestAuditLogReset, 25));
};


/**
 * @param {?proto.cmd_resp_logs.RespRequestAuditLogReset|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setAuditLogReset = function(value) {
  return jspb.Message.setOneofWrapperField(this, 25, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearAuditLogReset = function() {
  return this.setAuditLogReset(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasAuditLogReset = function() {
  return jspb.Message.getField(this, 25) != null;
};


/**
 * optional cmd_resp_logs.RespRequestAuditLogEntries request_auditlog = 26;
 * @return {?proto.cmd_resp_logs.RespRequestAuditLogEntries}
 */
proto.wrappers.WrapperResponse.prototype.getRequestAuditlog = function() {
  return /** @type{?proto.cmd_resp_logs.RespRequestAuditLogEntries} */ (
    jspb.Message.getWrapperField(this, cmd_resp_logs_pb.RespRequestAuditLogEntries, 26));
};


/**
 * @param {?proto.cmd_resp_logs.RespRequestAuditLogEntries|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRequestAuditlog = function(value) {
  return jspb.Message.setOneofWrapperField(this, 26, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRequestAuditlog = function() {
  return this.setRequestAuditlog(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRequestAuditlog = function() {
  return jspb.Message.getField(this, 26) != null;
};


/**
 * optional cmd_resp_config.RespReadMonitorData rd_monitor_data = 31;
 * @return {?proto.cmd_resp_config.RespReadMonitorData}
 */
proto.wrappers.WrapperResponse.prototype.getRdMonitorData = function() {
  return /** @type{?proto.cmd_resp_config.RespReadMonitorData} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadMonitorData, 31));
};


/**
 * @param {?proto.cmd_resp_config.RespReadMonitorData|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdMonitorData = function(value) {
  return jspb.Message.setOneofWrapperField(this, 31, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdMonitorData = function() {
  return this.setRdMonitorData(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdMonitorData = function() {
  return jspb.Message.getField(this, 31) != null;
};


/**
 * optional cmd_resp_config.RespReadUnitNetworkConfiguration rd_network_config_unit = 32;
 * @return {?proto.cmd_resp_config.RespReadUnitNetworkConfiguration}
 */
proto.wrappers.WrapperResponse.prototype.getRdNetworkConfigUnit = function() {
  return /** @type{?proto.cmd_resp_config.RespReadUnitNetworkConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadUnitNetworkConfiguration, 32));
};


/**
 * @param {?proto.cmd_resp_config.RespReadUnitNetworkConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdNetworkConfigUnit = function(value) {
  return jspb.Message.setOneofWrapperField(this, 32, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdNetworkConfigUnit = function() {
  return this.setRdNetworkConfigUnit(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdNetworkConfigUnit = function() {
  return jspb.Message.getField(this, 32) != null;
};


/**
 * optional cmd_resp_config.RespReadActiveNetworkConfiguration rd_network_config_active = 33;
 * @return {?proto.cmd_resp_config.RespReadActiveNetworkConfiguration}
 */
proto.wrappers.WrapperResponse.prototype.getRdNetworkConfigActive = function() {
  return /** @type{?proto.cmd_resp_config.RespReadActiveNetworkConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadActiveNetworkConfiguration, 33));
};


/**
 * @param {?proto.cmd_resp_config.RespReadActiveNetworkConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdNetworkConfigActive = function(value) {
  return jspb.Message.setOneofWrapperField(this, 33, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdNetworkConfigActive = function() {
  return this.setRdNetworkConfigActive(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdNetworkConfigActive = function() {
  return jspb.Message.getField(this, 33) != null;
};


/**
 * optional cmd_resp_config.RespReadPerChannelConfiguration rd_channel_config = 34;
 * @return {?proto.cmd_resp_config.RespReadPerChannelConfiguration}
 */
proto.wrappers.WrapperResponse.prototype.getRdChannelConfig = function() {
  return /** @type{?proto.cmd_resp_config.RespReadPerChannelConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadPerChannelConfiguration, 34));
};


/**
 * @param {?proto.cmd_resp_config.RespReadPerChannelConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdChannelConfig = function(value) {
  return jspb.Message.setOneofWrapperField(this, 34, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdChannelConfig = function() {
  return this.setRdChannelConfig(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdChannelConfig = function() {
  return jspb.Message.getField(this, 34) != null;
};


/**
 * optional cmd_resp_config.RespReadPerChannelCurrentSenseSettings rd_channel_current_sense = 35;
 * @return {?proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings}
 */
proto.wrappers.WrapperResponse.prototype.getRdChannelCurrentSense = function() {
  return /** @type{?proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadPerChannelCurrentSenseSettings, 35));
};


/**
 * @param {?proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdChannelCurrentSense = function(value) {
  return jspb.Message.setOneofWrapperField(this, 35, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdChannelCurrentSense = function() {
  return this.setRdChannelCurrentSense(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdChannelCurrentSense = function() {
  return jspb.Message.getField(this, 35) != null;
};


/**
 * optional cmd_resp_config.RespReadPerChannelPermissiveSettings rd_channel_permissives = 36;
 * @return {?proto.cmd_resp_config.RespReadPerChannelPermissiveSettings}
 */
proto.wrappers.WrapperResponse.prototype.getRdChannelPermissives = function() {
  return /** @type{?proto.cmd_resp_config.RespReadPerChannelPermissiveSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadPerChannelPermissiveSettings, 36));
};


/**
 * @param {?proto.cmd_resp_config.RespReadPerChannelPermissiveSettings|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdChannelPermissives = function(value) {
  return jspb.Message.setOneofWrapperField(this, 36, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdChannelPermissives = function() {
  return this.setRdChannelPermissives(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdChannelPermissives = function() {
  return jspb.Message.getField(this, 36) != null;
};


/**
 * optional cmd_resp_config.RespReadFlashingYellowArrowConfiguration rd_fya_config = 37;
 * @return {?proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration}
 */
proto.wrappers.WrapperResponse.prototype.getRdFyaConfig = function() {
  return /** @type{?proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadFlashingYellowArrowConfiguration, 37));
};


/**
 * @param {?proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdFyaConfig = function(value) {
  return jspb.Message.setOneofWrapperField(this, 37, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdFyaConfig = function() {
  return this.setRdFyaConfig(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdFyaConfig = function() {
  return jspb.Message.getField(this, 37) != null;
};


/**
 * optional cmd_resp_config.RespReadDataKey rd_data_key = 38;
 * @return {?proto.cmd_resp_config.RespReadDataKey}
 */
proto.wrappers.WrapperResponse.prototype.getRdDataKey = function() {
  return /** @type{?proto.cmd_resp_config.RespReadDataKey} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadDataKey, 38));
};


/**
 * @param {?proto.cmd_resp_config.RespReadDataKey|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdDataKey = function(value) {
  return jspb.Message.setOneofWrapperField(this, 38, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdDataKey = function() {
  return this.setRdDataKey(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdDataKey = function() {
  return jspb.Message.getField(this, 38) != null;
};


/**
 * optional cmd_resp_config.RespWriteDataKey wr_data_key = 39;
 * @return {?proto.cmd_resp_config.RespWriteDataKey}
 */
proto.wrappers.WrapperResponse.prototype.getWrDataKey = function() {
  return /** @type{?proto.cmd_resp_config.RespWriteDataKey} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespWriteDataKey, 39));
};


/**
 * @param {?proto.cmd_resp_config.RespWriteDataKey|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setWrDataKey = function(value) {
  return jspb.Message.setOneofWrapperField(this, 39, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearWrDataKey = function() {
  return this.setWrDataKey(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasWrDataKey = function() {
  return jspb.Message.getField(this, 39) != null;
};


/**
 * optional cmd_resp_config.RespReadFactorySettings rd_factory_settings = 40;
 * @return {?proto.cmd_resp_config.RespReadFactorySettings}
 */
proto.wrappers.WrapperResponse.prototype.getRdFactorySettings = function() {
  return /** @type{?proto.cmd_resp_config.RespReadFactorySettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadFactorySettings, 40));
};


/**
 * @param {?proto.cmd_resp_config.RespReadFactorySettings|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdFactorySettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 40, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdFactorySettings = function() {
  return this.setRdFactorySettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdFactorySettings = function() {
  return jspb.Message.getField(this, 40) != null;
};


/**
 * optional cmd_resp_config.RespWriteFactorySettings wr_factory_settings = 41;
 * @return {?proto.cmd_resp_config.RespWriteFactorySettings}
 */
proto.wrappers.WrapperResponse.prototype.getWrFactorySettings = function() {
  return /** @type{?proto.cmd_resp_config.RespWriteFactorySettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespWriteFactorySettings, 41));
};


/**
 * @param {?proto.cmd_resp_config.RespWriteFactorySettings|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setWrFactorySettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 41, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearWrFactorySettings = function() {
  return this.setWrFactorySettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasWrFactorySettings = function() {
  return jspb.Message.getField(this, 41) != null;
};


/**
 * optional cmd_resp_config.RespReadUserSettings rd_user_settings = 42;
 * @return {?proto.cmd_resp_config.RespReadUserSettings}
 */
proto.wrappers.WrapperResponse.prototype.getRdUserSettings = function() {
  return /** @type{?proto.cmd_resp_config.RespReadUserSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadUserSettings, 42));
};


/**
 * @param {?proto.cmd_resp_config.RespReadUserSettings|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdUserSettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 42, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdUserSettings = function() {
  return this.setRdUserSettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdUserSettings = function() {
  return jspb.Message.getField(this, 42) != null;
};


/**
 * optional cmd_resp_config.RespWriteUserSettings wr_user_settings = 43;
 * @return {?proto.cmd_resp_config.RespWriteUserSettings}
 */
proto.wrappers.WrapperResponse.prototype.getWrUserSettings = function() {
  return /** @type{?proto.cmd_resp_config.RespWriteUserSettings} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespWriteUserSettings, 43));
};


/**
 * @param {?proto.cmd_resp_config.RespWriteUserSettings|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setWrUserSettings = function(value) {
  return jspb.Message.setOneofWrapperField(this, 43, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearWrUserSettings = function() {
  return this.setWrUserSettings(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasWrUserSettings = function() {
  return jspb.Message.getField(this, 43) != null;
};


/**
 * optional cmd_resp_config.RespReadPort1DisableOverrides rd_port1_disables = 44;
 * @return {?proto.cmd_resp_config.RespReadPort1DisableOverrides}
 */
proto.wrappers.WrapperResponse.prototype.getRdPort1Disables = function() {
  return /** @type{?proto.cmd_resp_config.RespReadPort1DisableOverrides} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespReadPort1DisableOverrides, 44));
};


/**
 * @param {?proto.cmd_resp_config.RespReadPort1DisableOverrides|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdPort1Disables = function(value) {
  return jspb.Message.setOneofWrapperField(this, 44, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdPort1Disables = function() {
  return this.setRdPort1Disables(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdPort1Disables = function() {
  return jspb.Message.getField(this, 44) != null;
};


/**
 * optional cmd_resp_config.RespWritePort1DisableOverrides wr_port1_disables = 45;
 * @return {?proto.cmd_resp_config.RespWritePort1DisableOverrides}
 */
proto.wrappers.WrapperResponse.prototype.getWrPort1Disables = function() {
  return /** @type{?proto.cmd_resp_config.RespWritePort1DisableOverrides} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespWritePort1DisableOverrides, 45));
};


/**
 * @param {?proto.cmd_resp_config.RespWritePort1DisableOverrides|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setWrPort1Disables = function(value) {
  return jspb.Message.setOneofWrapperField(this, 45, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearWrPort1Disables = function() {
  return this.setWrPort1Disables(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasWrPort1Disables = function() {
  return jspb.Message.getField(this, 45) != null;
};


/**
 * optional cmd_resp_config.RespWriteAgencyOptions wr_agency_options = 46;
 * @return {?proto.cmd_resp_config.RespWriteAgencyOptions}
 */
proto.wrappers.WrapperResponse.prototype.getWrAgencyOptions = function() {
  return /** @type{?proto.cmd_resp_config.RespWriteAgencyOptions} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespWriteAgencyOptions, 46));
};


/**
 * @param {?proto.cmd_resp_config.RespWriteAgencyOptions|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setWrAgencyOptions = function(value) {
  return jspb.Message.setOneofWrapperField(this, 46, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearWrAgencyOptions = function() {
  return this.setWrAgencyOptions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasWrAgencyOptions = function() {
  return jspb.Message.getField(this, 46) != null;
};


/**
 * optional cmd_resp_config.RespRemoteReset remote_reset = 47;
 * @return {?proto.cmd_resp_config.RespRemoteReset}
 */
proto.wrappers.WrapperResponse.prototype.getRemoteReset = function() {
  return /** @type{?proto.cmd_resp_config.RespRemoteReset} */ (
    jspb.Message.getWrapperField(this, cmd_resp_config_pb.RespRemoteReset, 47));
};


/**
 * @param {?proto.cmd_resp_config.RespRemoteReset|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRemoteReset = function(value) {
  return jspb.Message.setOneofWrapperField(this, 47, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRemoteReset = function() {
  return this.setRemoteReset(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRemoteReset = function() {
  return jspb.Message.getField(this, 47) != null;
};


/**
 * optional cmd_resp_stats.RespReadPort1Statistics rd_port1_stats = 50;
 * @return {?proto.cmd_resp_stats.RespReadPort1Statistics}
 */
proto.wrappers.WrapperResponse.prototype.getRdPort1Stats = function() {
  return /** @type{?proto.cmd_resp_stats.RespReadPort1Statistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespReadPort1Statistics, 50));
};


/**
 * @param {?proto.cmd_resp_stats.RespReadPort1Statistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdPort1Stats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 50, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdPort1Stats = function() {
  return this.setRdPort1Stats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdPort1Stats = function() {
  return jspb.Message.getField(this, 50) != null;
};


/**
 * optional cmd_resp_stats.RespReadDataKeyStatistics rd_data_key_stats = 51;
 * @return {?proto.cmd_resp_stats.RespReadDataKeyStatistics}
 */
proto.wrappers.WrapperResponse.prototype.getRdDataKeyStats = function() {
  return /** @type{?proto.cmd_resp_stats.RespReadDataKeyStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespReadDataKeyStatistics, 51));
};


/**
 * @param {?proto.cmd_resp_stats.RespReadDataKeyStatistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdDataKeyStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 51, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdDataKeyStats = function() {
  return this.setRdDataKeyStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdDataKeyStats = function() {
  return jspb.Message.getField(this, 51) != null;
};


/**
 * optional cmd_resp_stats.RespReadMainToIsolatedCommStatistics rd_main_iso_comms_stats = 52;
 * @return {?proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics}
 */
proto.wrappers.WrapperResponse.prototype.getRdMainIsoCommsStats = function() {
  return /** @type{?proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespReadMainToIsolatedCommStatistics, 52));
};


/**
 * @param {?proto.cmd_resp_stats.RespReadMainToIsolatedCommStatistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdMainIsoCommsStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 52, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdMainIsoCommsStats = function() {
  return this.setRdMainIsoCommsStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdMainIsoCommsStats = function() {
  return jspb.Message.getField(this, 52) != null;
};


/**
 * optional cmd_resp_stats.RespReadMainToCommsCommStatistics rd_main_comms_stats = 53;
 * @return {?proto.cmd_resp_stats.RespReadMainToCommsCommStatistics}
 */
proto.wrappers.WrapperResponse.prototype.getRdMainCommsStats = function() {
  return /** @type{?proto.cmd_resp_stats.RespReadMainToCommsCommStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespReadMainToCommsCommStatistics, 53));
};


/**
 * @param {?proto.cmd_resp_stats.RespReadMainToCommsCommStatistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdMainCommsStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 53, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdMainCommsStats = function() {
  return this.setRdMainCommsStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdMainCommsStats = function() {
  return jspb.Message.getField(this, 53) != null;
};


/**
 * optional cmd_resp_stats.RespReadCommsToMainCommStatistics rd_comms_main_stats = 54;
 * @return {?proto.cmd_resp_stats.RespReadCommsToMainCommStatistics}
 */
proto.wrappers.WrapperResponse.prototype.getRdCommsMainStats = function() {
  return /** @type{?proto.cmd_resp_stats.RespReadCommsToMainCommStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespReadCommsToMainCommStatistics, 54));
};


/**
 * @param {?proto.cmd_resp_stats.RespReadCommsToMainCommStatistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdCommsMainStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 54, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdCommsMainStats = function() {
  return this.setRdCommsMainStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdCommsMainStats = function() {
  return jspb.Message.getField(this, 54) != null;
};


/**
 * optional cmd_resp_stats.RespReadFlashStatistics rd_flash_stats = 55;
 * @return {?proto.cmd_resp_stats.RespReadFlashStatistics}
 */
proto.wrappers.WrapperResponse.prototype.getRdFlashStats = function() {
  return /** @type{?proto.cmd_resp_stats.RespReadFlashStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespReadFlashStatistics, 55));
};


/**
 * @param {?proto.cmd_resp_stats.RespReadFlashStatistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdFlashStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 55, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdFlashStats = function() {
  return this.setRdFlashStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdFlashStats = function() {
  return jspb.Message.getField(this, 55) != null;
};


/**
 * optional cmd_resp_stats.RespReadWatchdogStatistics rd_watchdog_stats = 56;
 * @return {?proto.cmd_resp_stats.RespReadWatchdogStatistics}
 */
proto.wrappers.WrapperResponse.prototype.getRdWatchdogStats = function() {
  return /** @type{?proto.cmd_resp_stats.RespReadWatchdogStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespReadWatchdogStatistics, 56));
};


/**
 * @param {?proto.cmd_resp_stats.RespReadWatchdogStatistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdWatchdogStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 56, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdWatchdogStats = function() {
  return this.setRdWatchdogStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdWatchdogStats = function() {
  return jspb.Message.getField(this, 56) != null;
};


/**
 * optional cmd_resp_stats.RespGetTimeDatesDst rd_date_time_dst = 57;
 * @return {?proto.cmd_resp_stats.RespGetTimeDatesDst}
 */
proto.wrappers.WrapperResponse.prototype.getRdDateTimeDst = function() {
  return /** @type{?proto.cmd_resp_stats.RespGetTimeDatesDst} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespGetTimeDatesDst, 57));
};


/**
 * @param {?proto.cmd_resp_stats.RespGetTimeDatesDst|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRdDateTimeDst = function(value) {
  return jspb.Message.setOneofWrapperField(this, 57, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRdDateTimeDst = function() {
  return this.setRdDateTimeDst(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRdDateTimeDst = function() {
  return jspb.Message.getField(this, 57) != null;
};


/**
 * optional cmd_resp_stats.RespClearStatistics clear_stats = 58;
 * @return {?proto.cmd_resp_stats.RespClearStatistics}
 */
proto.wrappers.WrapperResponse.prototype.getClearStats = function() {
  return /** @type{?proto.cmd_resp_stats.RespClearStatistics} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespClearStatistics, 58));
};


/**
 * @param {?proto.cmd_resp_stats.RespClearStatistics|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setClearStats = function(value) {
  return jspb.Message.setOneofWrapperField(this, 58, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearClearStats = function() {
  return this.setClearStats(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasClearStats = function() {
  return jspb.Message.getField(this, 58) != null;
};


/**
 * optional cmd_resp_stats.RespSetTimeDatesDst wr_date_time_dst = 59;
 * @return {?proto.cmd_resp_stats.RespSetTimeDatesDst}
 */
proto.wrappers.WrapperResponse.prototype.getWrDateTimeDst = function() {
  return /** @type{?proto.cmd_resp_stats.RespSetTimeDatesDst} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespSetTimeDatesDst, 59));
};


/**
 * @param {?proto.cmd_resp_stats.RespSetTimeDatesDst|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setWrDateTimeDst = function(value) {
  return jspb.Message.setOneofWrapperField(this, 59, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearWrDateTimeDst = function() {
  return this.setWrDateTimeDst(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasWrDateTimeDst = function() {
  return jspb.Message.getField(this, 59) != null;
};


/**
 * optional cmd_resp_stats.RespRemoteDisplayButtonEvent remote_display_button_event = 65;
 * @return {?proto.cmd_resp_stats.RespRemoteDisplayButtonEvent}
 */
proto.wrappers.WrapperResponse.prototype.getRemoteDisplayButtonEvent = function() {
  return /** @type{?proto.cmd_resp_stats.RespRemoteDisplayButtonEvent} */ (
    jspb.Message.getWrapperField(this, cmd_resp_stats_pb.RespRemoteDisplayButtonEvent, 65));
};


/**
 * @param {?proto.cmd_resp_stats.RespRemoteDisplayButtonEvent|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRemoteDisplayButtonEvent = function(value) {
  return jspb.Message.setOneofWrapperField(this, 65, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRemoteDisplayButtonEvent = function() {
  return this.setRemoteDisplayButtonEvent(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRemoteDisplayButtonEvent = function() {
  return jspb.Message.getField(this, 65) != null;
};


/**
 * optional cmd_resp_realtime.RespStartRealtimeData start_realtime = 60;
 * @return {?proto.cmd_resp_realtime.RespStartRealtimeData}
 */
proto.wrappers.WrapperResponse.prototype.getStartRealtime = function() {
  return /** @type{?proto.cmd_resp_realtime.RespStartRealtimeData} */ (
    jspb.Message.getWrapperField(this, cmd_resp_realtime_pb.RespStartRealtimeData, 60));
};


/**
 * @param {?proto.cmd_resp_realtime.RespStartRealtimeData|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setStartRealtime = function(value) {
  return jspb.Message.setOneofWrapperField(this, 60, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearStartRealtime = function() {
  return this.setStartRealtime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasStartRealtime = function() {
  return jspb.Message.getField(this, 60) != null;
};


/**
 * optional cmd_resp_realtime.RespStopRealtimeData stop_realtime = 61;
 * @return {?proto.cmd_resp_realtime.RespStopRealtimeData}
 */
proto.wrappers.WrapperResponse.prototype.getStopRealtime = function() {
  return /** @type{?proto.cmd_resp_realtime.RespStopRealtimeData} */ (
    jspb.Message.getWrapperField(this, cmd_resp_realtime_pb.RespStopRealtimeData, 61));
};


/**
 * @param {?proto.cmd_resp_realtime.RespStopRealtimeData|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setStopRealtime = function(value) {
  return jspb.Message.setOneofWrapperField(this, 61, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearStopRealtime = function() {
  return this.setStopRealtime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasStopRealtime = function() {
  return jspb.Message.getField(this, 61) != null;
};


/**
 * optional cmd_resp_dfu.RespManifestVersions manifest_versions = 70;
 * @return {?proto.cmd_resp_dfu.RespManifestVersions}
 */
proto.wrappers.WrapperResponse.prototype.getManifestVersions = function() {
  return /** @type{?proto.cmd_resp_dfu.RespManifestVersions} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.RespManifestVersions, 70));
};


/**
 * @param {?proto.cmd_resp_dfu.RespManifestVersions|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setManifestVersions = function(value) {
  return jspb.Message.setOneofWrapperField(this, 70, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearManifestVersions = function() {
  return this.setManifestVersions(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasManifestVersions = function() {
  return jspb.Message.getField(this, 70) != null;
};


/**
 * optional cmd_resp_dfu.RespRebootCommsMcu reboot_comms_mcu = 71;
 * @return {?proto.cmd_resp_dfu.RespRebootCommsMcu}
 */
proto.wrappers.WrapperResponse.prototype.getRebootCommsMcu = function() {
  return /** @type{?proto.cmd_resp_dfu.RespRebootCommsMcu} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.RespRebootCommsMcu, 71));
};


/**
 * @param {?proto.cmd_resp_dfu.RespRebootCommsMcu|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRebootCommsMcu = function(value) {
  return jspb.Message.setOneofWrapperField(this, 71, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRebootCommsMcu = function() {
  return this.setRebootCommsMcu(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRebootCommsMcu = function() {
  return jspb.Message.getField(this, 71) != null;
};


/**
 * optional cmd_resp_dfu.RespInitiateFirmwareUpdate initiate_dfu = 72;
 * @return {?proto.cmd_resp_dfu.RespInitiateFirmwareUpdate}
 */
proto.wrappers.WrapperResponse.prototype.getInitiateDfu = function() {
  return /** @type{?proto.cmd_resp_dfu.RespInitiateFirmwareUpdate} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.RespInitiateFirmwareUpdate, 72));
};


/**
 * @param {?proto.cmd_resp_dfu.RespInitiateFirmwareUpdate|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setInitiateDfu = function(value) {
  return jspb.Message.setOneofWrapperField(this, 72, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearInitiateDfu = function() {
  return this.setInitiateDfu(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasInitiateDfu = function() {
  return jspb.Message.getField(this, 72) != null;
};


/**
 * optional cmd_resp_dfu.RespFirmwareUpdateManifest send_fw_manifest = 73;
 * @return {?proto.cmd_resp_dfu.RespFirmwareUpdateManifest}
 */
proto.wrappers.WrapperResponse.prototype.getSendFwManifest = function() {
  return /** @type{?proto.cmd_resp_dfu.RespFirmwareUpdateManifest} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.RespFirmwareUpdateManifest, 73));
};


/**
 * @param {?proto.cmd_resp_dfu.RespFirmwareUpdateManifest|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setSendFwManifest = function(value) {
  return jspb.Message.setOneofWrapperField(this, 73, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearSendFwManifest = function() {
  return this.setSendFwManifest(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasSendFwManifest = function() {
  return jspb.Message.getField(this, 73) != null;
};


/**
 * optional cmd_resp_dfu.RespBeginFirmwareDownload begin_firmware_download = 74;
 * @return {?proto.cmd_resp_dfu.RespBeginFirmwareDownload}
 */
proto.wrappers.WrapperResponse.prototype.getBeginFirmwareDownload = function() {
  return /** @type{?proto.cmd_resp_dfu.RespBeginFirmwareDownload} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.RespBeginFirmwareDownload, 74));
};


/**
 * @param {?proto.cmd_resp_dfu.RespBeginFirmwareDownload|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setBeginFirmwareDownload = function(value) {
  return jspb.Message.setOneofWrapperField(this, 74, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearBeginFirmwareDownload = function() {
  return this.setBeginFirmwareDownload(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasBeginFirmwareDownload = function() {
  return jspb.Message.getField(this, 74) != null;
};


/**
 * optional cmd_resp_dfu.RespFirmwareDownloadChunk download_firmware_chunk = 75;
 * @return {?proto.cmd_resp_dfu.RespFirmwareDownloadChunk}
 */
proto.wrappers.WrapperResponse.prototype.getDownloadFirmwareChunk = function() {
  return /** @type{?proto.cmd_resp_dfu.RespFirmwareDownloadChunk} */ (
    jspb.Message.getWrapperField(this, cmd_resp_dfu_pb.RespFirmwareDownloadChunk, 75));
};


/**
 * @param {?proto.cmd_resp_dfu.RespFirmwareDownloadChunk|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setDownloadFirmwareChunk = function(value) {
  return jspb.Message.setOneofWrapperField(this, 75, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearDownloadFirmwareChunk = function() {
  return this.setDownloadFirmwareChunk(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasDownloadFirmwareChunk = function() {
  return jspb.Message.getField(this, 75) != null;
};


/**
 * optional cmd_resp_realtime.RealtimeData1 realtime_data = 80;
 * @return {?proto.cmd_resp_realtime.RealtimeData1}
 */
proto.wrappers.WrapperResponse.prototype.getRealtimeData = function() {
  return /** @type{?proto.cmd_resp_realtime.RealtimeData1} */ (
    jspb.Message.getWrapperField(this, cmd_resp_realtime_pb.RealtimeData1, 80));
};


/**
 * @param {?proto.cmd_resp_realtime.RealtimeData1|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRealtimeData = function(value) {
  return jspb.Message.setOneofWrapperField(this, 80, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRealtimeData = function() {
  return this.setRealtimeData(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRealtimeData = function() {
  return jspb.Message.getField(this, 80) != null;
};


/**
 * optional cmd_resp_realtime.RealtimeDisplay1 realtime_display = 81;
 * @return {?proto.cmd_resp_realtime.RealtimeDisplay1}
 */
proto.wrappers.WrapperResponse.prototype.getRealtimeDisplay = function() {
  return /** @type{?proto.cmd_resp_realtime.RealtimeDisplay1} */ (
    jspb.Message.getWrapperField(this, cmd_resp_realtime_pb.RealtimeDisplay1, 81));
};


/**
 * @param {?proto.cmd_resp_realtime.RealtimeDisplay1|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setRealtimeDisplay = function(value) {
  return jspb.Message.setOneofWrapperField(this, 81, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearRealtimeDisplay = function() {
  return this.setRealtimeDisplay(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasRealtimeDisplay = function() {
  return jspb.Message.getField(this, 81) != null;
};


/**
 * optional cmd_resp_comms.RespChunkTest test_chunk = 90;
 * @return {?proto.cmd_resp_comms.RespChunkTest}
 */
proto.wrappers.WrapperResponse.prototype.getTestChunk = function() {
  return /** @type{?proto.cmd_resp_comms.RespChunkTest} */ (
    jspb.Message.getWrapperField(this, cmd_resp_comms_pb.RespChunkTest, 90));
};


/**
 * @param {?proto.cmd_resp_comms.RespChunkTest|undefined} value
 * @return {!proto.wrappers.WrapperResponse} returns this
*/
proto.wrappers.WrapperResponse.prototype.setTestChunk = function(value) {
  return jspb.Message.setOneofWrapperField(this, 90, proto.wrappers.WrapperResponse.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.wrappers.WrapperResponse} returns this
 */
proto.wrappers.WrapperResponse.prototype.clearTestChunk = function() {
  return this.setTestChunk(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.wrappers.WrapperResponse.prototype.hasTestChunk = function() {
  return jspb.Message.getField(this, 90) != null;
};


/**
 * @enum {number}
 */
proto.wrappers.EResponseCodes = {
  RESP_UNSPECIFIED: 0,
  RESP_SUCCESS: 1,
  RESP_PROHIBITED: 2,
  RESP_FAILED: 3,
  RESP_CMD_UNKNOWN: 4,
  RESP_BAD_VALUE: 5,
  RESP_BAD_CHANNEL: 6,
  RESP_CMD_ERROR: 7,
  RESP_OPTION_MISMATCH: 8,
  RESP_UNAVAILABLE: 9,
  RESP_BUSY: 10,
  RESP_TIMEOUT: 11,
  RESP_SEQUENCE_ERR: 12,
  RESP_NOT_WRITABLE: 13,
  RESP_DEVICE_NACK: 14,
  RESP_FILE_SYSTEM_ERROR: 15
};

goog.object.extend(exports, proto.wrappers);
