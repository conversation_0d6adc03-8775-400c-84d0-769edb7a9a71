// source: cmd_resp_config.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
var settings_pb = require('./settings_pb.js');
goog.object.extend(proto, settings_pb);
var dfu_pb = require('./dfu_pb.js');
goog.object.extend(proto, dfu_pb);
var mon_logs_pb = require('./mon_logs_pb.js');
goog.object.extend(proto, mon_logs_pb);
goog.exportSymbol('proto.cmd_resp_config.CmdReadActiveNetworkConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadDataKey', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadFactorySettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadMonitorData', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadPerChannelConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadPort1DisableOverrides', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadUnitNetworkConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdReadUserSettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdRemoteReset', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdWriteAgencyOptions', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdWriteDataKey', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdWriteFactorySettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdWritePort1DisableOverrides', null, global);
goog.exportSymbol('proto.cmd_resp_config.CmdWriteUserSettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.CpRemoteResetType', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadActiveNetworkConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadDataKey', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadFactorySettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadMonitorData', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadPerChannelConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadPerChannelPermissiveSettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadPort1DisableOverrides', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadUnitNetworkConfiguration', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespReadUserSettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespRemoteReset', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespWriteAgencyOptions', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespWriteDataKey', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespWriteFactorySettings', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespWritePort1DisableOverrides', null, global);
goog.exportSymbol('proto.cmd_resp_config.RespWriteUserSettings', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadDataKey = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadDataKey, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadDataKey.displayName = 'proto.cmd_resp_config.CmdReadDataKey';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadDataKey = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespReadDataKey, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadDataKey.displayName = 'proto.cmd_resp_config.RespReadDataKey';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdWriteDataKey = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdWriteDataKey, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdWriteDataKey.displayName = 'proto.cmd_resp_config.CmdWriteDataKey';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespWriteDataKey = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespWriteDataKey, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespWriteDataKey.displayName = 'proto.cmd_resp_config.RespWriteDataKey';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadFactorySettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadFactorySettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadFactorySettings.displayName = 'proto.cmd_resp_config.CmdReadFactorySettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadFactorySettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespReadFactorySettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadFactorySettings.displayName = 'proto.cmd_resp_config.RespReadFactorySettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdWriteFactorySettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdWriteFactorySettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdWriteFactorySettings.displayName = 'proto.cmd_resp_config.CmdWriteFactorySettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespWriteFactorySettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespWriteFactorySettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespWriteFactorySettings.displayName = 'proto.cmd_resp_config.RespWriteFactorySettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdWriteAgencyOptions = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdWriteAgencyOptions, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdWriteAgencyOptions.displayName = 'proto.cmd_resp_config.CmdWriteAgencyOptions';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespWriteAgencyOptions = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespWriteAgencyOptions, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespWriteAgencyOptions.displayName = 'proto.cmd_resp_config.RespWriteAgencyOptions';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadMonitorData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadMonitorData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadMonitorData.displayName = 'proto.cmd_resp_config.CmdReadMonitorData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadMonitorData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.RespReadMonitorData.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.RespReadMonitorData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadMonitorData.displayName = 'proto.cmd_resp_config.RespReadMonitorData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadUserSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadUserSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadUserSettings.displayName = 'proto.cmd_resp_config.CmdReadUserSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadUserSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespReadUserSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadUserSettings.displayName = 'proto.cmd_resp_config.RespReadUserSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdWriteUserSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdWriteUserSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdWriteUserSettings.displayName = 'proto.cmd_resp_config.CmdWriteUserSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespWriteUserSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespWriteUserSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespWriteUserSettings.displayName = 'proto.cmd_resp_config.RespWriteUserSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadPort1DisableOverrides, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadPort1DisableOverrides.displayName = 'proto.cmd_resp_config.CmdReadPort1DisableOverrides';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespReadPort1DisableOverrides, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadPort1DisableOverrides.displayName = 'proto.cmd_resp_config.RespReadPort1DisableOverrides';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdWritePort1DisableOverrides, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdWritePort1DisableOverrides.displayName = 'proto.cmd_resp_config.CmdWritePort1DisableOverrides';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespWritePort1DisableOverrides, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespWritePort1DisableOverrides.displayName = 'proto.cmd_resp_config.RespWritePort1DisableOverrides';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadUnitNetworkConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.displayName = 'proto.cmd_resp_config.CmdReadUnitNetworkConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespReadUnitNetworkConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadUnitNetworkConfiguration.displayName = 'proto.cmd_resp_config.RespReadUnitNetworkConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadActiveNetworkConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.displayName = 'proto.cmd_resp_config.CmdReadActiveNetworkConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespReadActiveNetworkConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadActiveNetworkConfiguration.displayName = 'proto.cmd_resp_config.RespReadActiveNetworkConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.CmdReadPerChannelConfiguration.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadPerChannelConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadPerChannelConfiguration.displayName = 'proto.cmd_resp_config.CmdReadPerChannelConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.RespReadPerChannelConfiguration.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.RespReadPerChannelConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadPerChannelConfiguration.displayName = 'proto.cmd_resp_config.RespReadPerChannelConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.displayName = 'proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.displayName = 'proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.displayName = 'proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.RespReadPerChannelPermissiveSettings, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.displayName = 'proto.cmd_resp_config.RespReadPerChannelPermissiveSettings';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.displayName = 'proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.repeatedFields_, null);
};
goog.inherits(proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.displayName = 'proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.CmdRemoteReset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.CmdRemoteReset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.CmdRemoteReset.displayName = 'proto.cmd_resp_config.CmdRemoteReset';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.cmd_resp_config.RespRemoteReset = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.cmd_resp_config.RespRemoteReset, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.cmd_resp_config.RespRemoteReset.displayName = 'proto.cmd_resp_config.RespRemoteReset';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadDataKey.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadDataKey.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadDataKey} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadDataKey.toObject = function(includeInstance, msg) {
  var f, obj = {
pcDkType: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadDataKey}
 */
proto.cmd_resp_config.CmdReadDataKey.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadDataKey;
  return proto.cmd_resp_config.CmdReadDataKey.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadDataKey} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadDataKey}
 */
proto.cmd_resp_config.CmdReadDataKey.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EPcDkReadType} */ (reader.readEnum());
      msg.setPcDkType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadDataKey.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadDataKey.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadDataKey} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadDataKey.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPcDkType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EPcDkReadType pc_dk_type = 1;
 * @return {!proto.settings.EPcDkReadType}
 */
proto.cmd_resp_config.CmdReadDataKey.prototype.getPcDkType = function() {
  return /** @type {!proto.settings.EPcDkReadType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EPcDkReadType} value
 * @return {!proto.cmd_resp_config.CmdReadDataKey} returns this
 */
proto.cmd_resp_config.CmdReadDataKey.prototype.setPcDkType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadDataKey.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadDataKey.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadDataKey} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadDataKey.toObject = function(includeInstance, msg) {
  var f, obj = {
source: jspb.Message.getFieldWithDefault(msg, 1, 0),
dataKeyData: msg.getDataKeyData_asB64(),
pcDkType: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadDataKey}
 */
proto.cmd_resp_config.RespReadDataKey.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadDataKey;
  return proto.cmd_resp_config.RespReadDataKey.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadDataKey} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadDataKey}
 */
proto.cmd_resp_config.RespReadDataKey.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EConfigDataLocation} */ (reader.readEnum());
      msg.setSource(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setDataKeyData(value);
      break;
    case 3:
      var value = /** @type {!proto.settings.EPcDkReadType} */ (reader.readEnum());
      msg.setPcDkType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadDataKey.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadDataKey.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadDataKey} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadDataKey.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSource();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getDataKeyData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
  f = message.getPcDkType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
};


/**
 * optional settings.EConfigDataLocation source = 1;
 * @return {!proto.settings.EConfigDataLocation}
 */
proto.cmd_resp_config.RespReadDataKey.prototype.getSource = function() {
  return /** @type {!proto.settings.EConfigDataLocation} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EConfigDataLocation} value
 * @return {!proto.cmd_resp_config.RespReadDataKey} returns this
 */
proto.cmd_resp_config.RespReadDataKey.prototype.setSource = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bytes data_key_data = 2;
 * @return {string}
 */
proto.cmd_resp_config.RespReadDataKey.prototype.getDataKeyData = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes data_key_data = 2;
 * This is a type-conversion wrapper around `getDataKeyData()`
 * @return {string}
 */
proto.cmd_resp_config.RespReadDataKey.prototype.getDataKeyData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getDataKeyData()));
};


/**
 * optional bytes data_key_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getDataKeyData()`
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadDataKey.prototype.getDataKeyData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getDataKeyData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.cmd_resp_config.RespReadDataKey} returns this
 */
proto.cmd_resp_config.RespReadDataKey.prototype.setDataKeyData = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};


/**
 * optional settings.EPcDkReadType pc_dk_type = 3;
 * @return {!proto.settings.EPcDkReadType}
 */
proto.cmd_resp_config.RespReadDataKey.prototype.getPcDkType = function() {
  return /** @type {!proto.settings.EPcDkReadType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.settings.EPcDkReadType} value
 * @return {!proto.cmd_resp_config.RespReadDataKey} returns this
 */
proto.cmd_resp_config.RespReadDataKey.prototype.setPcDkType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdWriteDataKey.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdWriteDataKey} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteDataKey.toObject = function(includeInstance, msg) {
  var f, obj = {
destination: jspb.Message.getFieldWithDefault(msg, 1, 0),
dataKeyData: msg.getDataKeyData_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdWriteDataKey}
 */
proto.cmd_resp_config.CmdWriteDataKey.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdWriteDataKey;
  return proto.cmd_resp_config.CmdWriteDataKey.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdWriteDataKey} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdWriteDataKey}
 */
proto.cmd_resp_config.CmdWriteDataKey.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EConfigDataLocation} */ (reader.readEnum());
      msg.setDestination(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setDataKeyData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdWriteDataKey.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdWriteDataKey} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteDataKey.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDestination();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getDataKeyData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional settings.EConfigDataLocation destination = 1;
 * @return {!proto.settings.EConfigDataLocation}
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.getDestination = function() {
  return /** @type {!proto.settings.EConfigDataLocation} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EConfigDataLocation} value
 * @return {!proto.cmd_resp_config.CmdWriteDataKey} returns this
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.setDestination = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bytes data_key_data = 2;
 * @return {string}
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.getDataKeyData = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes data_key_data = 2;
 * This is a type-conversion wrapper around `getDataKeyData()`
 * @return {string}
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.getDataKeyData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getDataKeyData()));
};


/**
 * optional bytes data_key_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getDataKeyData()`
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.getDataKeyData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getDataKeyData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.cmd_resp_config.CmdWriteDataKey} returns this
 */
proto.cmd_resp_config.CmdWriteDataKey.prototype.setDataKeyData = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespWriteDataKey.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespWriteDataKey.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespWriteDataKey} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteDataKey.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespWriteDataKey}
 */
proto.cmd_resp_config.RespWriteDataKey.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespWriteDataKey;
  return proto.cmd_resp_config.RespWriteDataKey.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespWriteDataKey} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespWriteDataKey}
 */
proto.cmd_resp_config.RespWriteDataKey.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EWriteResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespWriteDataKey.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespWriteDataKey.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespWriteDataKey} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteDataKey.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EWriteResult result = 1;
 * @return {!proto.settings.EWriteResult}
 */
proto.cmd_resp_config.RespWriteDataKey.prototype.getResult = function() {
  return /** @type {!proto.settings.EWriteResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EWriteResult} value
 * @return {!proto.cmd_resp_config.RespWriteDataKey} returns this
 */
proto.cmd_resp_config.RespWriteDataKey.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadFactorySettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadFactorySettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadFactorySettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadFactorySettings.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadFactorySettings}
 */
proto.cmd_resp_config.CmdReadFactorySettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadFactorySettings;
  return proto.cmd_resp_config.CmdReadFactorySettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadFactorySettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadFactorySettings}
 */
proto.cmd_resp_config.CmdReadFactorySettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadFactorySettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadFactorySettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadFactorySettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadFactorySettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_config.CmdReadFactorySettings.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.CmdReadFactorySettings} returns this
 */
proto.cmd_resp_config.CmdReadFactorySettings.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadFactorySettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadFactorySettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadFactorySettings.toObject = function(includeInstance, msg) {
  var f, obj = {
factory: (f = msg.getFactory()) && settings_pb.FactorySettings.toObject(includeInstance, f),
hardware: (f = msg.getHardware()) && mon_logs_pb.HardwareRevisionsMmu.toObject(includeInstance, f),
maxChannelsSupported: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadFactorySettings}
 */
proto.cmd_resp_config.RespReadFactorySettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadFactorySettings;
  return proto.cmd_resp_config.RespReadFactorySettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadFactorySettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadFactorySettings}
 */
proto.cmd_resp_config.RespReadFactorySettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.FactorySettings;
      reader.readMessage(value,settings_pb.FactorySettings.deserializeBinaryFromReader);
      msg.setFactory(value);
      break;
    case 2:
      var value = new mon_logs_pb.HardwareRevisionsMmu;
      reader.readMessage(value,mon_logs_pb.HardwareRevisionsMmu.deserializeBinaryFromReader);
      msg.setHardware(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMaxChannelsSupported(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadFactorySettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadFactorySettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadFactorySettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFactory();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      settings_pb.FactorySettings.serializeBinaryToWriter
    );
  }
  f = message.getHardware();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      mon_logs_pb.HardwareRevisionsMmu.serializeBinaryToWriter
    );
  }
  f = message.getMaxChannelsSupported();
  if (f !== 0) {
    writer.writeUint32(
      3,
      f
    );
  }
};


/**
 * optional settings.FactorySettings factory = 1;
 * @return {?proto.settings.FactorySettings}
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.getFactory = function() {
  return /** @type{?proto.settings.FactorySettings} */ (
    jspb.Message.getWrapperField(this, settings_pb.FactorySettings, 1));
};


/**
 * @param {?proto.settings.FactorySettings|undefined} value
 * @return {!proto.cmd_resp_config.RespReadFactorySettings} returns this
*/
proto.cmd_resp_config.RespReadFactorySettings.prototype.setFactory = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadFactorySettings} returns this
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.clearFactory = function() {
  return this.setFactory(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.hasFactory = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional mon_logs.HardwareRevisionsMmu hardware = 2;
 * @return {?proto.mon_logs.HardwareRevisionsMmu}
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.getHardware = function() {
  return /** @type{?proto.mon_logs.HardwareRevisionsMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.HardwareRevisionsMmu, 2));
};


/**
 * @param {?proto.mon_logs.HardwareRevisionsMmu|undefined} value
 * @return {!proto.cmd_resp_config.RespReadFactorySettings} returns this
*/
proto.cmd_resp_config.RespReadFactorySettings.prototype.setHardware = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadFactorySettings} returns this
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.clearHardware = function() {
  return this.setHardware(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.hasHardware = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional uint32 max_channels_supported = 3;
 * @return {number}
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.getMaxChannelsSupported = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.RespReadFactorySettings} returns this
 */
proto.cmd_resp_config.RespReadFactorySettings.prototype.setMaxChannelsSupported = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdWriteFactorySettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdWriteFactorySettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdWriteFactorySettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteFactorySettings.toObject = function(includeInstance, msg) {
  var f, obj = {
factory: (f = msg.getFactory()) && settings_pb.FactorySettings.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdWriteFactorySettings}
 */
proto.cmd_resp_config.CmdWriteFactorySettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdWriteFactorySettings;
  return proto.cmd_resp_config.CmdWriteFactorySettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdWriteFactorySettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdWriteFactorySettings}
 */
proto.cmd_resp_config.CmdWriteFactorySettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.FactorySettings;
      reader.readMessage(value,settings_pb.FactorySettings.deserializeBinaryFromReader);
      msg.setFactory(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdWriteFactorySettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdWriteFactorySettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdWriteFactorySettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteFactorySettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFactory();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      settings_pb.FactorySettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.FactorySettings factory = 1;
 * @return {?proto.settings.FactorySettings}
 */
proto.cmd_resp_config.CmdWriteFactorySettings.prototype.getFactory = function() {
  return /** @type{?proto.settings.FactorySettings} */ (
    jspb.Message.getWrapperField(this, settings_pb.FactorySettings, 1));
};


/**
 * @param {?proto.settings.FactorySettings|undefined} value
 * @return {!proto.cmd_resp_config.CmdWriteFactorySettings} returns this
*/
proto.cmd_resp_config.CmdWriteFactorySettings.prototype.setFactory = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.CmdWriteFactorySettings} returns this
 */
proto.cmd_resp_config.CmdWriteFactorySettings.prototype.clearFactory = function() {
  return this.setFactory(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.CmdWriteFactorySettings.prototype.hasFactory = function() {
  return jspb.Message.getField(this, 1) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespWriteFactorySettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespWriteFactorySettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespWriteFactorySettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteFactorySettings.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespWriteFactorySettings}
 */
proto.cmd_resp_config.RespWriteFactorySettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespWriteFactorySettings;
  return proto.cmd_resp_config.RespWriteFactorySettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespWriteFactorySettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespWriteFactorySettings}
 */
proto.cmd_resp_config.RespWriteFactorySettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EWriteResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespWriteFactorySettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespWriteFactorySettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespWriteFactorySettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteFactorySettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EWriteResult result = 1;
 * @return {!proto.settings.EWriteResult}
 */
proto.cmd_resp_config.RespWriteFactorySettings.prototype.getResult = function() {
  return /** @type {!proto.settings.EWriteResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EWriteResult} value
 * @return {!proto.cmd_resp_config.RespWriteFactorySettings} returns this
 */
proto.cmd_resp_config.RespWriteFactorySettings.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdWriteAgencyOptions.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdWriteAgencyOptions} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.toObject = function(includeInstance, msg) {
  var f, obj = {
agency: (f = msg.getAgency()) && settings_pb.AgencyOptionsMmu.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdWriteAgencyOptions}
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdWriteAgencyOptions;
  return proto.cmd_resp_config.CmdWriteAgencyOptions.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdWriteAgencyOptions} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdWriteAgencyOptions}
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.AgencyOptionsMmu;
      reader.readMessage(value,settings_pb.AgencyOptionsMmu.deserializeBinaryFromReader);
      msg.setAgency(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdWriteAgencyOptions.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdWriteAgencyOptions} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAgency();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      settings_pb.AgencyOptionsMmu.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.AgencyOptionsMmu agency = 1;
 * @return {?proto.settings.AgencyOptionsMmu}
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.prototype.getAgency = function() {
  return /** @type{?proto.settings.AgencyOptionsMmu} */ (
    jspb.Message.getWrapperField(this, settings_pb.AgencyOptionsMmu, 1));
};


/**
 * @param {?proto.settings.AgencyOptionsMmu|undefined} value
 * @return {!proto.cmd_resp_config.CmdWriteAgencyOptions} returns this
*/
proto.cmd_resp_config.CmdWriteAgencyOptions.prototype.setAgency = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.CmdWriteAgencyOptions} returns this
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.prototype.clearAgency = function() {
  return this.setAgency(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.CmdWriteAgencyOptions.prototype.hasAgency = function() {
  return jspb.Message.getField(this, 1) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespWriteAgencyOptions.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespWriteAgencyOptions.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespWriteAgencyOptions} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteAgencyOptions.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespWriteAgencyOptions}
 */
proto.cmd_resp_config.RespWriteAgencyOptions.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespWriteAgencyOptions;
  return proto.cmd_resp_config.RespWriteAgencyOptions.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespWriteAgencyOptions} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespWriteAgencyOptions}
 */
proto.cmd_resp_config.RespWriteAgencyOptions.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EWriteResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespWriteAgencyOptions.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespWriteAgencyOptions.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespWriteAgencyOptions} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteAgencyOptions.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EWriteResult result = 1;
 * @return {!proto.settings.EWriteResult}
 */
proto.cmd_resp_config.RespWriteAgencyOptions.prototype.getResult = function() {
  return /** @type {!proto.settings.EWriteResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EWriteResult} value
 * @return {!proto.cmd_resp_config.RespWriteAgencyOptions} returns this
 */
proto.cmd_resp_config.RespWriteAgencyOptions.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadMonitorData.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadMonitorData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadMonitorData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadMonitorData.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadMonitorData}
 */
proto.cmd_resp_config.CmdReadMonitorData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadMonitorData;
  return proto.cmd_resp_config.CmdReadMonitorData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadMonitorData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadMonitorData}
 */
proto.cmd_resp_config.CmdReadMonitorData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadMonitorData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadMonitorData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadMonitorData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadMonitorData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_config.CmdReadMonitorData.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.CmdReadMonitorData} returns this
 */
proto.cmd_resp_config.CmdReadMonitorData.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.RespReadMonitorData.repeatedFields_ = [6];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadMonitorData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadMonitorData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadMonitorData.toObject = function(includeInstance, msg) {
  var f, obj = {
factory: (f = msg.getFactory()) && settings_pb.FactorySettings.toObject(includeInstance, f),
monitorId: jspb.Message.getFieldWithDefault(msg, 7, ""),
userId: jspb.Message.getFieldWithDefault(msg, 2, ""),
hardware: (f = msg.getHardware()) && mon_logs_pb.HardwareRevisionsMmu.toObject(includeInstance, f),
supportedChannelCount: jspb.Message.getFieldWithDefault(msg, 4, 0),
runTimeSeconds: jspb.Message.getFieldWithDefault(msg, 5, 0),
fwVersionsList: jspb.Message.toObjectList(msg.getFwVersionsList(),
    dfu_pb.FirmwareVersionSimple.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadMonitorData}
 */
proto.cmd_resp_config.RespReadMonitorData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadMonitorData;
  return proto.cmd_resp_config.RespReadMonitorData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadMonitorData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadMonitorData}
 */
proto.cmd_resp_config.RespReadMonitorData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.FactorySettings;
      reader.readMessage(value,settings_pb.FactorySettings.deserializeBinaryFromReader);
      msg.setFactory(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setMonitorId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setUserId(value);
      break;
    case 3:
      var value = new mon_logs_pb.HardwareRevisionsMmu;
      reader.readMessage(value,mon_logs_pb.HardwareRevisionsMmu.deserializeBinaryFromReader);
      msg.setHardware(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSupportedChannelCount(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRunTimeSeconds(value);
      break;
    case 6:
      var value = new dfu_pb.FirmwareVersionSimple;
      reader.readMessage(value,dfu_pb.FirmwareVersionSimple.deserializeBinaryFromReader);
      msg.addFwVersions(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadMonitorData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadMonitorData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadMonitorData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFactory();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      settings_pb.FactorySettings.serializeBinaryToWriter
    );
  }
  f = message.getMonitorId();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getUserId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getHardware();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      mon_logs_pb.HardwareRevisionsMmu.serializeBinaryToWriter
    );
  }
  f = message.getSupportedChannelCount();
  if (f !== 0) {
    writer.writeUint32(
      4,
      f
    );
  }
  f = message.getRunTimeSeconds();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getFwVersionsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      6,
      f,
      dfu_pb.FirmwareVersionSimple.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.FactorySettings factory = 1;
 * @return {?proto.settings.FactorySettings}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.getFactory = function() {
  return /** @type{?proto.settings.FactorySettings} */ (
    jspb.Message.getWrapperField(this, settings_pb.FactorySettings, 1));
};


/**
 * @param {?proto.settings.FactorySettings|undefined} value
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
*/
proto.cmd_resp_config.RespReadMonitorData.prototype.setFactory = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.clearFactory = function() {
  return this.setFactory(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.hasFactory = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string monitor_id = 7;
 * @return {string}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.getMonitorId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.setMonitorId = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string user_id = 2;
 * @return {string}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.getUserId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.setUserId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional mon_logs.HardwareRevisionsMmu hardware = 3;
 * @return {?proto.mon_logs.HardwareRevisionsMmu}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.getHardware = function() {
  return /** @type{?proto.mon_logs.HardwareRevisionsMmu} */ (
    jspb.Message.getWrapperField(this, mon_logs_pb.HardwareRevisionsMmu, 3));
};


/**
 * @param {?proto.mon_logs.HardwareRevisionsMmu|undefined} value
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
*/
proto.cmd_resp_config.RespReadMonitorData.prototype.setHardware = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.clearHardware = function() {
  return this.setHardware(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.hasHardware = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional uint32 supported_channel_count = 4;
 * @return {number}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.getSupportedChannelCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.setSupportedChannelCount = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional uint32 run_time_seconds = 5;
 * @return {number}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.getRunTimeSeconds = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.setRunTimeSeconds = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * repeated dfu.FirmwareVersionSimple fw_versions = 6;
 * @return {!Array<!proto.dfu.FirmwareVersionSimple>}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.getFwVersionsList = function() {
  return /** @type{!Array<!proto.dfu.FirmwareVersionSimple>} */ (
    jspb.Message.getRepeatedWrapperField(this, dfu_pb.FirmwareVersionSimple, 6));
};


/**
 * @param {!Array<!proto.dfu.FirmwareVersionSimple>} value
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
*/
proto.cmd_resp_config.RespReadMonitorData.prototype.setFwVersionsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 6, value);
};


/**
 * @param {!proto.dfu.FirmwareVersionSimple=} opt_value
 * @param {number=} opt_index
 * @return {!proto.dfu.FirmwareVersionSimple}
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.addFwVersions = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 6, opt_value, proto.dfu.FirmwareVersionSimple, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.RespReadMonitorData} returns this
 */
proto.cmd_resp_config.RespReadMonitorData.prototype.clearFwVersionsList = function() {
  return this.setFwVersionsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadUserSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadUserSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadUserSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadUserSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadUserSettings}
 */
proto.cmd_resp_config.CmdReadUserSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadUserSettings;
  return proto.cmd_resp_config.CmdReadUserSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadUserSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadUserSettings}
 */
proto.cmd_resp_config.CmdReadUserSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadUserSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadUserSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadUserSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadUserSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_config.CmdReadUserSettings.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.CmdReadUserSettings} returns this
 */
proto.cmd_resp_config.CmdReadUserSettings.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadUserSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadUserSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadUserSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadUserSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
values: (f = msg.getValues()) && settings_pb.UserSettings.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadUserSettings}
 */
proto.cmd_resp_config.RespReadUserSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadUserSettings;
  return proto.cmd_resp_config.RespReadUserSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadUserSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadUserSettings}
 */
proto.cmd_resp_config.RespReadUserSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.UserSettings;
      reader.readMessage(value,settings_pb.UserSettings.deserializeBinaryFromReader);
      msg.setValues(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadUserSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadUserSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadUserSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadUserSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValues();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      settings_pb.UserSettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.UserSettings values = 1;
 * @return {?proto.settings.UserSettings}
 */
proto.cmd_resp_config.RespReadUserSettings.prototype.getValues = function() {
  return /** @type{?proto.settings.UserSettings} */ (
    jspb.Message.getWrapperField(this, settings_pb.UserSettings, 1));
};


/**
 * @param {?proto.settings.UserSettings|undefined} value
 * @return {!proto.cmd_resp_config.RespReadUserSettings} returns this
*/
proto.cmd_resp_config.RespReadUserSettings.prototype.setValues = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadUserSettings} returns this
 */
proto.cmd_resp_config.RespReadUserSettings.prototype.clearValues = function() {
  return this.setValues(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadUserSettings.prototype.hasValues = function() {
  return jspb.Message.getField(this, 1) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdWriteUserSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdWriteUserSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdWriteUserSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteUserSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
values: (f = msg.getValues()) && settings_pb.UserSettings.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdWriteUserSettings}
 */
proto.cmd_resp_config.CmdWriteUserSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdWriteUserSettings;
  return proto.cmd_resp_config.CmdWriteUserSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdWriteUserSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdWriteUserSettings}
 */
proto.cmd_resp_config.CmdWriteUserSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.UserSettings;
      reader.readMessage(value,settings_pb.UserSettings.deserializeBinaryFromReader);
      msg.setValues(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdWriteUserSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdWriteUserSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdWriteUserSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWriteUserSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getValues();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      settings_pb.UserSettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional settings.UserSettings values = 1;
 * @return {?proto.settings.UserSettings}
 */
proto.cmd_resp_config.CmdWriteUserSettings.prototype.getValues = function() {
  return /** @type{?proto.settings.UserSettings} */ (
    jspb.Message.getWrapperField(this, settings_pb.UserSettings, 1));
};


/**
 * @param {?proto.settings.UserSettings|undefined} value
 * @return {!proto.cmd_resp_config.CmdWriteUserSettings} returns this
*/
proto.cmd_resp_config.CmdWriteUserSettings.prototype.setValues = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.CmdWriteUserSettings} returns this
 */
proto.cmd_resp_config.CmdWriteUserSettings.prototype.clearValues = function() {
  return this.setValues(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.CmdWriteUserSettings.prototype.hasValues = function() {
  return jspb.Message.getField(this, 1) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespWriteUserSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespWriteUserSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespWriteUserSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteUserSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespWriteUserSettings}
 */
proto.cmd_resp_config.RespWriteUserSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespWriteUserSettings;
  return proto.cmd_resp_config.RespWriteUserSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespWriteUserSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespWriteUserSettings}
 */
proto.cmd_resp_config.RespWriteUserSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EWriteResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespWriteUserSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespWriteUserSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespWriteUserSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWriteUserSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EWriteResult result = 1;
 * @return {!proto.settings.EWriteResult}
 */
proto.cmd_resp_config.RespWriteUserSettings.prototype.getResult = function() {
  return /** @type {!proto.settings.EWriteResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EWriteResult} value
 * @return {!proto.cmd_resp_config.RespWriteUserSettings} returns this
 */
proto.cmd_resp_config.RespWriteUserSettings.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadPort1DisableOverrides.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadPort1DisableOverrides} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadPort1DisableOverrides}
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadPort1DisableOverrides;
  return proto.cmd_resp_config.CmdReadPort1DisableOverrides.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadPort1DisableOverrides} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadPort1DisableOverrides}
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadPort1DisableOverrides.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadPort1DisableOverrides} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.CmdReadPort1DisableOverrides} returns this
 */
proto.cmd_resp_config.CmdReadPort1DisableOverrides.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadPort1DisableOverrides.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadPort1DisableOverrides} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.toObject = function(includeInstance, msg) {
  var f, obj = {
port1DisableOverride: jspb.Message.getFieldWithDefault(msg, 1, 0),
fieldChecksDisabled: jspb.Message.getBooleanFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadPort1DisableOverrides}
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadPort1DisableOverrides;
  return proto.cmd_resp_config.RespReadPort1DisableOverrides.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadPort1DisableOverrides} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadPort1DisableOverrides}
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EDisableOverrideState} */ (reader.readEnum());
      msg.setPort1DisableOverride(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFieldChecksDisabled(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadPort1DisableOverrides.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadPort1DisableOverrides} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPort1DisableOverride();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getFieldChecksDisabled();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional settings.EDisableOverrideState port1_disable_override = 1;
 * @return {!proto.settings.EDisableOverrideState}
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.prototype.getPort1DisableOverride = function() {
  return /** @type {!proto.settings.EDisableOverrideState} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EDisableOverrideState} value
 * @return {!proto.cmd_resp_config.RespReadPort1DisableOverrides} returns this
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.prototype.setPort1DisableOverride = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bool field_checks_disabled = 2;
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.prototype.getFieldChecksDisabled = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_config.RespReadPort1DisableOverrides} returns this
 */
proto.cmd_resp_config.RespReadPort1DisableOverrides.prototype.setFieldChecksDisabled = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdWritePort1DisableOverrides.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdWritePort1DisableOverrides} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.toObject = function(includeInstance, msg) {
  var f, obj = {
port1DisableOverride: jspb.Message.getFieldWithDefault(msg, 1, 0),
fieldChecksDisable: jspb.Message.getBooleanFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdWritePort1DisableOverrides}
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdWritePort1DisableOverrides;
  return proto.cmd_resp_config.CmdWritePort1DisableOverrides.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdWritePort1DisableOverrides} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdWritePort1DisableOverrides}
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EDisableOverrideState} */ (reader.readEnum());
      msg.setPort1DisableOverride(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFieldChecksDisable(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdWritePort1DisableOverrides.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdWritePort1DisableOverrides} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPort1DisableOverride();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getFieldChecksDisable();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional settings.EDisableOverrideState port1_disable_override = 1;
 * @return {!proto.settings.EDisableOverrideState}
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.prototype.getPort1DisableOverride = function() {
  return /** @type {!proto.settings.EDisableOverrideState} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EDisableOverrideState} value
 * @return {!proto.cmd_resp_config.CmdWritePort1DisableOverrides} returns this
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.prototype.setPort1DisableOverride = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bool field_checks_disable = 2;
 * @return {boolean}
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.prototype.getFieldChecksDisable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_config.CmdWritePort1DisableOverrides} returns this
 */
proto.cmd_resp_config.CmdWritePort1DisableOverrides.prototype.setFieldChecksDisable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespWritePort1DisableOverrides.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespWritePort1DisableOverrides} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespWritePort1DisableOverrides}
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespWritePort1DisableOverrides;
  return proto.cmd_resp_config.RespWritePort1DisableOverrides.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespWritePort1DisableOverrides} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespWritePort1DisableOverrides}
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EWriteResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespWritePort1DisableOverrides.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespWritePort1DisableOverrides} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EWriteResult result = 1;
 * @return {!proto.settings.EWriteResult}
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.prototype.getResult = function() {
  return /** @type {!proto.settings.EWriteResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EWriteResult} value
 * @return {!proto.cmd_resp_config.RespWritePort1DisableOverrides} returns this
 */
proto.cmd_resp_config.RespWritePort1DisableOverrides.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadUnitNetworkConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadUnitNetworkConfiguration}
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadUnitNetworkConfiguration;
  return proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadUnitNetworkConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadUnitNetworkConfiguration}
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadUnitNetworkConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.CmdReadUnitNetworkConfiguration} returns this
 */
proto.cmd_resp_config.CmdReadUnitNetworkConfiguration.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadUnitNetworkConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
ids: (f = msg.getIds()) && basic_pb.MonitorAndUserIds.toObject(includeInstance, f),
minFlashTime: jspb.Message.getFieldWithDefault(msg, 2, 0),
usingDatakeyEthernet: jspb.Message.getBooleanFieldWithDefault(msg, 3, false),
networkConfiguration: (f = msg.getNetworkConfiguration()) && settings_pb.NetworkSettings.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadUnitNetworkConfiguration;
  return proto.cmd_resp_config.RespReadUnitNetworkConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new basic_pb.MonitorAndUserIds;
      reader.readMessage(value,basic_pb.MonitorAndUserIds.deserializeBinaryFromReader);
      msg.setIds(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMinFlashTime(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setUsingDatakeyEthernet(value);
      break;
    case 4:
      var value = new settings_pb.NetworkSettings;
      reader.readMessage(value,settings_pb.NetworkSettings.deserializeBinaryFromReader);
      msg.setNetworkConfiguration(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadUnitNetworkConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getIds();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      basic_pb.MonitorAndUserIds.serializeBinaryToWriter
    );
  }
  f = message.getMinFlashTime();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getUsingDatakeyEthernet();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
  f = message.getNetworkConfiguration();
  if (f != null) {
    writer.writeMessage(
      4,
      f,
      settings_pb.NetworkSettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional basic.MonitorAndUserIds ids = 1;
 * @return {?proto.basic.MonitorAndUserIds}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.getIds = function() {
  return /** @type{?proto.basic.MonitorAndUserIds} */ (
    jspb.Message.getWrapperField(this, basic_pb.MonitorAndUserIds, 1));
};


/**
 * @param {?proto.basic.MonitorAndUserIds|undefined} value
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} returns this
*/
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.setIds = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} returns this
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.clearIds = function() {
  return this.setIds(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.hasIds = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional uint32 min_flash_time = 2;
 * @return {number}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.getMinFlashTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} returns this
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.setMinFlashTime = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional bool using_datakey_ethernet = 3;
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.getUsingDatakeyEthernet = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} returns this
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.setUsingDatakeyEthernet = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};


/**
 * optional settings.NetworkSettings network_configuration = 4;
 * @return {?proto.settings.NetworkSettings}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.getNetworkConfiguration = function() {
  return /** @type{?proto.settings.NetworkSettings} */ (
    jspb.Message.getWrapperField(this, settings_pb.NetworkSettings, 4));
};


/**
 * @param {?proto.settings.NetworkSettings|undefined} value
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} returns this
*/
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.setNetworkConfiguration = function(value) {
  return jspb.Message.setWrapperField(this, 4, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadUnitNetworkConfiguration} returns this
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.clearNetworkConfiguration = function() {
  return this.setNetworkConfiguration(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadUnitNetworkConfiguration.prototype.hasNetworkConfiguration = function() {
  return jspb.Message.getField(this, 4) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadActiveNetworkConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadActiveNetworkConfiguration}
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadActiveNetworkConfiguration;
  return proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadActiveNetworkConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadActiveNetworkConfiguration}
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadActiveNetworkConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.CmdReadActiveNetworkConfiguration} returns this
 */
proto.cmd_resp_config.CmdReadActiveNetworkConfiguration.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadActiveNetworkConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
usingDatakeyEthernet: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
networkConfiguration: (f = msg.getNetworkConfiguration()) && settings_pb.NetworkSettings.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration}
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadActiveNetworkConfiguration;
  return proto.cmd_resp_config.RespReadActiveNetworkConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration}
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setUsingDatakeyEthernet(value);
      break;
    case 2:
      var value = new settings_pb.NetworkSettings;
      reader.readMessage(value,settings_pb.NetworkSettings.deserializeBinaryFromReader);
      msg.setNetworkConfiguration(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadActiveNetworkConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getUsingDatakeyEthernet();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getNetworkConfiguration();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      settings_pb.NetworkSettings.serializeBinaryToWriter
    );
  }
};


/**
 * optional bool using_datakey_ethernet = 1;
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.getUsingDatakeyEthernet = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration} returns this
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.setUsingDatakeyEthernet = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional settings.NetworkSettings network_configuration = 2;
 * @return {?proto.settings.NetworkSettings}
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.getNetworkConfiguration = function() {
  return /** @type{?proto.settings.NetworkSettings} */ (
    jspb.Message.getWrapperField(this, settings_pb.NetworkSettings, 2));
};


/**
 * @param {?proto.settings.NetworkSettings|undefined} value
 * @return {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration} returns this
*/
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.setNetworkConfiguration = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.cmd_resp_config.RespReadActiveNetworkConfiguration} returns this
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.clearNetworkConfiguration = function() {
  return this.setNetworkConfiguration(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.cmd_resp_config.RespReadActiveNetworkConfiguration.prototype.hasNetworkConfiguration = function() {
  return jspb.Message.getField(this, 2) != null;
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadPerChannelConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadPerChannelConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
channelList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelConfiguration}
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadPerChannelConfiguration;
  return proto.cmd_resp_config.CmdReadPerChannelConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadPerChannelConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelConfiguration}
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedUint32() : [reader.readUint32()]);
      for (var i = 0; i < values.length; i++) {
        msg.addChannel(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadPerChannelConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadPerChannelConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannelList();
  if (f.length > 0) {
    writer.writePackedUint32(
      1,
      f
    );
  }
};


/**
 * repeated uint32 channel = 1;
 * @return {!Array<number>}
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.prototype.getChannelList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.cmd_resp_config.CmdReadPerChannelConfiguration} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.prototype.setChannelList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_config.CmdReadPerChannelConfiguration} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.prototype.addChannel = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelConfiguration} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelConfiguration.prototype.clearChannelList = function() {
  return this.setChannelList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadPerChannelConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadPerChannelConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
chSettingsList: jspb.Message.toObjectList(msg.getChSettingsList(),
    settings_pb.PerChannelSettings.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadPerChannelConfiguration}
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadPerChannelConfiguration;
  return proto.cmd_resp_config.RespReadPerChannelConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadPerChannelConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadPerChannelConfiguration}
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.PerChannelSettings;
      reader.readMessage(value,settings_pb.PerChannelSettings.deserializeBinaryFromReader);
      msg.addChSettings(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadPerChannelConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadPerChannelConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChSettingsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      settings_pb.PerChannelSettings.serializeBinaryToWriter
    );
  }
};


/**
 * repeated settings.PerChannelSettings ch_settings = 1;
 * @return {!Array<!proto.settings.PerChannelSettings>}
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.prototype.getChSettingsList = function() {
  return /** @type{!Array<!proto.settings.PerChannelSettings>} */ (
    jspb.Message.getRepeatedWrapperField(this, settings_pb.PerChannelSettings, 1));
};


/**
 * @param {!Array<!proto.settings.PerChannelSettings>} value
 * @return {!proto.cmd_resp_config.RespReadPerChannelConfiguration} returns this
*/
proto.cmd_resp_config.RespReadPerChannelConfiguration.prototype.setChSettingsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.settings.PerChannelSettings=} opt_value
 * @param {number=} opt_index
 * @return {!proto.settings.PerChannelSettings}
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.prototype.addChSettings = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.settings.PerChannelSettings, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.RespReadPerChannelConfiguration} returns this
 */
proto.cmd_resp_config.RespReadPerChannelConfiguration.prototype.clearChSettingsList = function() {
  return this.setChSettingsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
channelList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings}
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings;
  return proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings}
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedUint32() : [reader.readUint32()]);
      for (var i = 0; i < values.length; i++) {
        msg.addChannel(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannelList();
  if (f.length > 0) {
    writer.writePackedUint32(
      1,
      f
    );
  }
};


/**
 * repeated uint32 channel = 1;
 * @return {!Array<number>}
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.prototype.getChannelList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.prototype.setChannelList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.prototype.addChannel = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.prototype.clearChannelList = function() {
  return this.setChannelList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
chSettingsList: jspb.Message.toObjectList(msg.getChSettingsList(),
    settings_pb.PerChannelCurrentSenseSettings.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings}
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings;
  return proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings}
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.PerChannelCurrentSenseSettings;
      reader.readMessage(value,settings_pb.PerChannelCurrentSenseSettings.deserializeBinaryFromReader);
      msg.addChSettings(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChSettingsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      settings_pb.PerChannelCurrentSenseSettings.serializeBinaryToWriter
    );
  }
};


/**
 * repeated settings.PerChannelCurrentSenseSettings ch_settings = 1;
 * @return {!Array<!proto.settings.PerChannelCurrentSenseSettings>}
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.prototype.getChSettingsList = function() {
  return /** @type{!Array<!proto.settings.PerChannelCurrentSenseSettings>} */ (
    jspb.Message.getRepeatedWrapperField(this, settings_pb.PerChannelCurrentSenseSettings, 1));
};


/**
 * @param {!Array<!proto.settings.PerChannelCurrentSenseSettings>} value
 * @return {!proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings} returns this
*/
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.prototype.setChSettingsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.settings.PerChannelCurrentSenseSettings=} opt_value
 * @param {number=} opt_index
 * @return {!proto.settings.PerChannelCurrentSenseSettings}
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.prototype.addChSettings = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.settings.PerChannelCurrentSenseSettings, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings} returns this
 */
proto.cmd_resp_config.RespReadPerChannelCurrentSenseSettings.prototype.clearChSettingsList = function() {
  return this.setChSettingsList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
channelList: (f = jspb.Message.getRepeatedField(msg, 1)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings}
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings;
  return proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings}
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedUint32() : [reader.readUint32()]);
      for (var i = 0; i < values.length; i++) {
        msg.addChannel(values[i]);
      }
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannelList();
  if (f.length > 0) {
    writer.writePackedUint32(
      1,
      f
    );
  }
};


/**
 * repeated uint32 channel = 1;
 * @return {!Array<number>}
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.prototype.getChannelList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 1));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.prototype.setChannelList = function(value) {
  return jspb.Message.setField(this, 1, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.prototype.addChannel = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 1, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings} returns this
 */
proto.cmd_resp_config.CmdReadPerChannelPermissiveSettings.prototype.clearChannelList = function() {
  return this.setChannelList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadPerChannelPermissiveSettings} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.toObject = function(includeInstance, msg) {
  var f, obj = {
chSettingsList: jspb.Message.toObjectList(msg.getChSettingsList(),
    settings_pb.PerChannelPermissives.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadPerChannelPermissiveSettings}
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadPerChannelPermissiveSettings;
  return proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadPerChannelPermissiveSettings} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadPerChannelPermissiveSettings}
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.PerChannelPermissives;
      reader.readMessage(value,settings_pb.PerChannelPermissives.deserializeBinaryFromReader);
      msg.addChSettings(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadPerChannelPermissiveSettings} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChSettingsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      settings_pb.PerChannelPermissives.serializeBinaryToWriter
    );
  }
};


/**
 * repeated settings.PerChannelPermissives ch_settings = 1;
 * @return {!Array<!proto.settings.PerChannelPermissives>}
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.prototype.getChSettingsList = function() {
  return /** @type{!Array<!proto.settings.PerChannelPermissives>} */ (
    jspb.Message.getRepeatedWrapperField(this, settings_pb.PerChannelPermissives, 1));
};


/**
 * @param {!Array<!proto.settings.PerChannelPermissives>} value
 * @return {!proto.cmd_resp_config.RespReadPerChannelPermissiveSettings} returns this
*/
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.prototype.setChSettingsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.settings.PerChannelPermissives=} opt_value
 * @param {number=} opt_index
 * @return {!proto.settings.PerChannelPermissives}
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.prototype.addChSettings = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.settings.PerChannelPermissives, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.RespReadPerChannelPermissiveSettings} returns this
 */
proto.cmd_resp_config.RespReadPerChannelPermissiveSettings.prototype.clearChSettingsList = function() {
  return this.setChSettingsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
always0: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration}
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration;
  return proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration}
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setAlways0(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAlways0();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
};


/**
 * optional uint32 always0 = 1;
 * @return {number}
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.prototype.getAlways0 = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration} returns this
 */
proto.cmd_resp_config.CmdReadFlashingYellowArrowConfiguration.prototype.setAlways0 = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.toObject = function(includeInstance, msg) {
  var f, obj = {
fyaSettingsList: jspb.Message.toObjectList(msg.getFyaSettingsList(),
    settings_pb.FlashingYellowArrowSettings.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration}
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration;
  return proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration}
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new settings_pb.FlashingYellowArrowSettings;
      reader.readMessage(value,settings_pb.FlashingYellowArrowSettings.deserializeBinaryFromReader);
      msg.addFyaSettings(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFyaSettingsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      settings_pb.FlashingYellowArrowSettings.serializeBinaryToWriter
    );
  }
};


/**
 * repeated settings.FlashingYellowArrowSettings fya_settings = 1;
 * @return {!Array<!proto.settings.FlashingYellowArrowSettings>}
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.prototype.getFyaSettingsList = function() {
  return /** @type{!Array<!proto.settings.FlashingYellowArrowSettings>} */ (
    jspb.Message.getRepeatedWrapperField(this, settings_pb.FlashingYellowArrowSettings, 1));
};


/**
 * @param {!Array<!proto.settings.FlashingYellowArrowSettings>} value
 * @return {!proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration} returns this
*/
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.prototype.setFyaSettingsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.settings.FlashingYellowArrowSettings=} opt_value
 * @param {number=} opt_index
 * @return {!proto.settings.FlashingYellowArrowSettings}
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.prototype.addFyaSettings = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.settings.FlashingYellowArrowSettings, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration} returns this
 */
proto.cmd_resp_config.RespReadFlashingYellowArrowConfiguration.prototype.clearFyaSettingsList = function() {
  return this.setFyaSettingsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.CmdRemoteReset.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.CmdRemoteReset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.CmdRemoteReset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdRemoteReset.toObject = function(includeInstance, msg) {
  var f, obj = {
resetType: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.CmdRemoteReset}
 */
proto.cmd_resp_config.CmdRemoteReset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.CmdRemoteReset;
  return proto.cmd_resp_config.CmdRemoteReset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.CmdRemoteReset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.CmdRemoteReset}
 */
proto.cmd_resp_config.CmdRemoteReset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.cmd_resp_config.CpRemoteResetType} */ (reader.readEnum());
      msg.setResetType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.CmdRemoteReset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.CmdRemoteReset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.CmdRemoteReset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.CmdRemoteReset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResetType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional CpRemoteResetType reset_type = 1;
 * @return {!proto.cmd_resp_config.CpRemoteResetType}
 */
proto.cmd_resp_config.CmdRemoteReset.prototype.getResetType = function() {
  return /** @type {!proto.cmd_resp_config.CpRemoteResetType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.cmd_resp_config.CpRemoteResetType} value
 * @return {!proto.cmd_resp_config.CmdRemoteReset} returns this
 */
proto.cmd_resp_config.CmdRemoteReset.prototype.setResetType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.cmd_resp_config.RespRemoteReset.prototype.toObject = function(opt_includeInstance) {
  return proto.cmd_resp_config.RespRemoteReset.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.cmd_resp_config.RespRemoteReset} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespRemoteReset.toObject = function(includeInstance, msg) {
  var f, obj = {
result: jspb.Message.getFieldWithDefault(msg, 1, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.cmd_resp_config.RespRemoteReset}
 */
proto.cmd_resp_config.RespRemoteReset.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.cmd_resp_config.RespRemoteReset;
  return proto.cmd_resp_config.RespRemoteReset.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.cmd_resp_config.RespRemoteReset} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.cmd_resp_config.RespRemoteReset}
 */
proto.cmd_resp_config.RespRemoteReset.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.settings.EWriteResult} */ (reader.readEnum());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.cmd_resp_config.RespRemoteReset.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.cmd_resp_config.RespRemoteReset.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.cmd_resp_config.RespRemoteReset} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.cmd_resp_config.RespRemoteReset.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResult();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
};


/**
 * optional settings.EWriteResult result = 1;
 * @return {!proto.settings.EWriteResult}
 */
proto.cmd_resp_config.RespRemoteReset.prototype.getResult = function() {
  return /** @type {!proto.settings.EWriteResult} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.settings.EWriteResult} value
 * @return {!proto.cmd_resp_config.RespRemoteReset} returns this
 */
proto.cmd_resp_config.RespRemoteReset.prototype.setResult = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * @enum {number}
 */
proto.cmd_resp_config.CpRemoteResetType = {
  REMOTE_RESET_TYPE_UNSPECIFIED: 0,
  REMOTE_RESET_TYPE_NONE: 1,
  REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE: 2,
  REMOTE_RESET_TYPE_FAULT_CLEAR: 3
};

goog.object.extend(exports, proto.cmd_resp_config);
