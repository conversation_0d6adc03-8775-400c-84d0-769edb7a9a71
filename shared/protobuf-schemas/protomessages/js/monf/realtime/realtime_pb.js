// source: realtime.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
var mon_faults_pb = require('./mon_faults_pb.js');
goog.object.extend(proto, mon_faults_pb);
goog.exportSymbol('proto.realtime.ChannelStatusData', null, global);
goog.exportSymbol('proto.realtime.ChannelVoltsCurrentsAndTimers', null, global);
goog.exportSymbol('proto.realtime.DisplayLineControl', null, global);
goog.exportSymbol('proto.realtime.EDisplayChannelsType', null, global);
goog.exportSymbol('proto.realtime.LcdDisplayLine', null, global);
goog.exportSymbol('proto.realtime.MmuMonitoredInputsVoltages', null, global);
goog.exportSymbol('proto.realtime.MonitorDisplayData', null, global);
goog.exportSymbol('proto.realtime.MonitorPresentStatus', null, global);
goog.exportSymbol('proto.realtime.MonitorPresentStatus.MonitoredInputStatesCase', null, global);
goog.exportSymbol('proto.realtime.MonitorPresentStatus.MonitoredInputVoltsCase', null, global);
goog.exportSymbol('proto.realtime.MonitorPresentStatus.SupplyCharacteristicCase', null, global);
goog.exportSymbol('proto.realtime.MonitorPresentStatus.SupplyVoltageCase', null, global);
goog.exportSymbol('proto.realtime.PanelLedsBitmap', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.ChannelVoltsCurrentsAndTimers = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.realtime.ChannelVoltsCurrentsAndTimers, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.ChannelVoltsCurrentsAndTimers.displayName = 'proto.realtime.ChannelVoltsCurrentsAndTimers';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.ChannelStatusData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.realtime.ChannelStatusData.repeatedFields_, null);
};
goog.inherits(proto.realtime.ChannelStatusData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.ChannelStatusData.displayName = 'proto.realtime.ChannelStatusData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.DisplayLineControl = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.realtime.DisplayLineControl, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.DisplayLineControl.displayName = 'proto.realtime.DisplayLineControl';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.LcdDisplayLine = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.realtime.LcdDisplayLine, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.LcdDisplayLine.displayName = 'proto.realtime.LcdDisplayLine';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.MonitorDisplayData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.realtime.MonitorDisplayData.repeatedFields_, null);
};
goog.inherits(proto.realtime.MonitorDisplayData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.MonitorDisplayData.displayName = 'proto.realtime.MonitorDisplayData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.PanelLedsBitmap = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.realtime.PanelLedsBitmap, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.PanelLedsBitmap.displayName = 'proto.realtime.PanelLedsBitmap';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.MmuMonitoredInputsVoltages = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.realtime.MmuMonitoredInputsVoltages, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.MmuMonitoredInputsVoltages.displayName = 'proto.realtime.MmuMonitoredInputsVoltages';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.realtime.MonitorPresentStatus = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.realtime.MonitorPresentStatus.oneofGroups_);
};
goog.inherits(proto.realtime.MonitorPresentStatus, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.realtime.MonitorPresentStatus.displayName = 'proto.realtime.MonitorPresentStatus';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.ChannelVoltsCurrentsAndTimers.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.ChannelVoltsCurrentsAndTimers} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.toObject = function(includeInstance, msg) {
  var f, obj = {
channel: jspb.Message.getFieldWithDefault(msg, 1, 0),
redVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
yellowVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 3, 0.0),
greenVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 4, 0.0),
walkVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 5, 0.0),
redAmps: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0),
yellowAmps: jspb.Message.getFloatingPointFieldWithDefault(msg, 7, 0.0),
greenAmps: jspb.Message.getFloatingPointFieldWithDefault(msg, 8, 0.0),
walkAmps: jspb.Message.getFloatingPointFieldWithDefault(msg, 9, 0.0),
redOffTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 10, 0.0),
yellowOffTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 11, 0.0),
greenOffTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 12, 0.0),
walkOffTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 13, 0.0),
redOnTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 14, 0.0),
yellowOnTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 15, 0.0),
greenOnTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 16, 0.0),
walkOnTimeMs: jspb.Message.getFloatingPointFieldWithDefault(msg, 17, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.ChannelVoltsCurrentsAndTimers;
  return proto.realtime.ChannelVoltsCurrentsAndTimers.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.ChannelVoltsCurrentsAndTimers} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setChannel(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setRedVolts(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setYellowVolts(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setGreenVolts(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setWalkVolts(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setRedAmps(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setYellowAmps(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setGreenAmps(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setWalkAmps(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setRedOffTimeMs(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setYellowOffTimeMs(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setGreenOffTimeMs(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setWalkOffTimeMs(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setRedOnTimeMs(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setYellowOnTimeMs(value);
      break;
    case 16:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setGreenOnTimeMs(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setWalkOnTimeMs(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.ChannelVoltsCurrentsAndTimers.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.ChannelVoltsCurrentsAndTimers} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChannel();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getRedVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = message.getYellowVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      3,
      f
    );
  }
  f = message.getGreenVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      4,
      f
    );
  }
  f = message.getWalkVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      5,
      f
    );
  }
  f = message.getRedAmps();
  if (f !== 0.0) {
    writer.writeFloat(
      6,
      f
    );
  }
  f = message.getYellowAmps();
  if (f !== 0.0) {
    writer.writeFloat(
      7,
      f
    );
  }
  f = message.getGreenAmps();
  if (f !== 0.0) {
    writer.writeFloat(
      8,
      f
    );
  }
  f = message.getWalkAmps();
  if (f !== 0.0) {
    writer.writeFloat(
      9,
      f
    );
  }
  f = message.getRedOffTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      10,
      f
    );
  }
  f = message.getYellowOffTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      11,
      f
    );
  }
  f = message.getGreenOffTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      12,
      f
    );
  }
  f = message.getWalkOffTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      13,
      f
    );
  }
  f = message.getRedOnTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      14,
      f
    );
  }
  f = message.getYellowOnTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      15,
      f
    );
  }
  f = message.getGreenOnTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      16,
      f
    );
  }
  f = message.getWalkOnTimeMs();
  if (f !== 0.0) {
    writer.writeFloat(
      17,
      f
    );
  }
};


/**
 * optional uint32 channel = 1;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getChannel = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setChannel = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional float red_volts = 2;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getRedVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setRedVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional float yellow_volts = 3;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getYellowVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setYellowVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 3, value);
};


/**
 * optional float green_volts = 4;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getGreenVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 4, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setGreenVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 4, value);
};


/**
 * optional float walk_volts = 5;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getWalkVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 5, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setWalkVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 5, value);
};


/**
 * optional float red_amps = 6;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getRedAmps = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setRedAmps = function(value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};


/**
 * optional float yellow_amps = 7;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getYellowAmps = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 7, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setYellowAmps = function(value) {
  return jspb.Message.setProto3FloatField(this, 7, value);
};


/**
 * optional float green_amps = 8;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getGreenAmps = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 8, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setGreenAmps = function(value) {
  return jspb.Message.setProto3FloatField(this, 8, value);
};


/**
 * optional float walk_amps = 9;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getWalkAmps = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 9, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setWalkAmps = function(value) {
  return jspb.Message.setProto3FloatField(this, 9, value);
};


/**
 * optional float red_off_time_ms = 10;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getRedOffTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 10, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setRedOffTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 10, value);
};


/**
 * optional float yellow_off_time_ms = 11;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getYellowOffTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 11, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setYellowOffTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 11, value);
};


/**
 * optional float green_off_time_ms = 12;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getGreenOffTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 12, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setGreenOffTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 12, value);
};


/**
 * optional float walk_off_time_ms = 13;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getWalkOffTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 13, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setWalkOffTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 13, value);
};


/**
 * optional float red_on_time_ms = 14;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getRedOnTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 14, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setRedOnTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 14, value);
};


/**
 * optional float yellow_on_time_ms = 15;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getYellowOnTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 15, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setYellowOnTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 15, value);
};


/**
 * optional float green_on_time_ms = 16;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getGreenOnTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 16, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setGreenOnTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 16, value);
};


/**
 * optional float walk_on_time_ms = 17;
 * @return {number}
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.getWalkOnTimeMs = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 17, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers} returns this
 */
proto.realtime.ChannelVoltsCurrentsAndTimers.prototype.setWalkOnTimeMs = function(value) {
  return jspb.Message.setProto3FloatField(this, 17, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.realtime.ChannelStatusData.repeatedFields_ = [10];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.ChannelStatusData.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.ChannelStatusData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.ChannelStatusData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.ChannelStatusData.toObject = function(includeInstance, msg) {
  var f, obj = {
bluesOnChmap: jspb.Message.getFieldWithDefault(msg, 1, 0),
redsOnChmap: jspb.Message.getFieldWithDefault(msg, 2, 0),
yellowsOnChmap: jspb.Message.getFieldWithDefault(msg, 3, 0),
greensOnChmap: jspb.Message.getFieldWithDefault(msg, 4, 0),
walksOnChmap: jspb.Message.getFieldWithDefault(msg, 5, 0),
redsFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 6, 0),
yellowsFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 7, 0),
greensFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 8, 0),
walksFieldCheckChmap: jspb.Message.getFieldWithDefault(msg, 9, 0),
channelIndicatorsVITList: jspb.Message.toObjectList(msg.getChannelIndicatorsVITList(),
    proto.realtime.ChannelVoltsCurrentsAndTimers.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.ChannelStatusData}
 */
proto.realtime.ChannelStatusData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.ChannelStatusData;
  return proto.realtime.ChannelStatusData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.ChannelStatusData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.ChannelStatusData}
 */
proto.realtime.ChannelStatusData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setBluesOnChmap(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsOnChmap(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsOnChmap(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensOnChmap(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksOnChmap(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedsFieldCheckChmap(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowsFieldCheckChmap(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreensFieldCheckChmap(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalksFieldCheckChmap(value);
      break;
    case 10:
      var value = new proto.realtime.ChannelVoltsCurrentsAndTimers;
      reader.readMessage(value,proto.realtime.ChannelVoltsCurrentsAndTimers.deserializeBinaryFromReader);
      msg.addChannelIndicatorsVIT(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.ChannelStatusData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.ChannelStatusData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.ChannelStatusData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.ChannelStatusData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getBluesOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      1,
      f
    );
  }
  f = message.getRedsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      2,
      f
    );
  }
  f = message.getYellowsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      3,
      f
    );
  }
  f = message.getGreensOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      4,
      f
    );
  }
  f = message.getWalksOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      5,
      f
    );
  }
  f = message.getRedsFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      6,
      f
    );
  }
  f = message.getYellowsFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      7,
      f
    );
  }
  f = message.getGreensFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      8,
      f
    );
  }
  f = message.getWalksFieldCheckChmap();
  if (f !== 0) {
    writer.writeFixed32(
      9,
      f
    );
  }
  f = message.getChannelIndicatorsVITList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      10,
      f,
      proto.realtime.ChannelVoltsCurrentsAndTimers.serializeBinaryToWriter
    );
  }
};


/**
 * optional fixed32 blues_on_chmap = 1;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getBluesOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setBluesOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional fixed32 reds_on_chmap = 2;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getRedsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setRedsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional fixed32 yellows_on_chmap = 3;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getYellowsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setYellowsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional fixed32 greens_on_chmap = 4;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getGreensOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setGreensOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional fixed32 walks_on_chmap = 5;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getWalksOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setWalksOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional fixed32 reds_field_check_chmap = 6;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getRedsFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setRedsFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional fixed32 yellows_field_check_chmap = 7;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getYellowsFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setYellowsFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional fixed32 greens_field_check_chmap = 8;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getGreensFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setGreensFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional fixed32 walks_field_check_chmap = 9;
 * @return {number}
 */
proto.realtime.ChannelStatusData.prototype.getWalksFieldCheckChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.setWalksFieldCheckChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * repeated ChannelVoltsCurrentsAndTimers channel_indicators_v_i_t = 10;
 * @return {!Array<!proto.realtime.ChannelVoltsCurrentsAndTimers>}
 */
proto.realtime.ChannelStatusData.prototype.getChannelIndicatorsVITList = function() {
  return /** @type{!Array<!proto.realtime.ChannelVoltsCurrentsAndTimers>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.realtime.ChannelVoltsCurrentsAndTimers, 10));
};


/**
 * @param {!Array<!proto.realtime.ChannelVoltsCurrentsAndTimers>} value
 * @return {!proto.realtime.ChannelStatusData} returns this
*/
proto.realtime.ChannelStatusData.prototype.setChannelIndicatorsVITList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 10, value);
};


/**
 * @param {!proto.realtime.ChannelVoltsCurrentsAndTimers=} opt_value
 * @param {number=} opt_index
 * @return {!proto.realtime.ChannelVoltsCurrentsAndTimers}
 */
proto.realtime.ChannelStatusData.prototype.addChannelIndicatorsVIT = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 10, opt_value, proto.realtime.ChannelVoltsCurrentsAndTimers, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.realtime.ChannelStatusData} returns this
 */
proto.realtime.ChannelStatusData.prototype.clearChannelIndicatorsVITList = function() {
  return this.setChannelIndicatorsVITList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.DisplayLineControl.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.DisplayLineControl.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.DisplayLineControl} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.DisplayLineControl.toObject = function(includeInstance, msg) {
  var f, obj = {
modified: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
inUse: jspb.Message.getBooleanFieldWithDefault(msg, 2, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.DisplayLineControl}
 */
proto.realtime.DisplayLineControl.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.DisplayLineControl;
  return proto.realtime.DisplayLineControl.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.DisplayLineControl} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.DisplayLineControl}
 */
proto.realtime.DisplayLineControl.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setModified(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setInUse(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.DisplayLineControl.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.DisplayLineControl.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.DisplayLineControl} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.DisplayLineControl.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getModified();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getInUse();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
};


/**
 * optional bool modified = 1;
 * @return {boolean}
 */
proto.realtime.DisplayLineControl.prototype.getModified = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.realtime.DisplayLineControl} returns this
 */
proto.realtime.DisplayLineControl.prototype.setModified = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool in_use = 2;
 * @return {boolean}
 */
proto.realtime.DisplayLineControl.prototype.getInUse = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.realtime.DisplayLineControl} returns this
 */
proto.realtime.DisplayLineControl.prototype.setInUse = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.LcdDisplayLine.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.LcdDisplayLine.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.LcdDisplayLine} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.LcdDisplayLine.toObject = function(includeInstance, msg) {
  var f, obj = {
lineNumber: jspb.Message.getFieldWithDefault(msg, 1, 0),
format: (f = msg.getFormat()) && proto.realtime.DisplayLineControl.toObject(includeInstance, f),
lineText: msg.getLineText_asB64(),
lineColors: msg.getLineColors_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.LcdDisplayLine}
 */
proto.realtime.LcdDisplayLine.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.LcdDisplayLine;
  return proto.realtime.LcdDisplayLine.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.LcdDisplayLine} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.LcdDisplayLine}
 */
proto.realtime.LcdDisplayLine.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLineNumber(value);
      break;
    case 2:
      var value = new proto.realtime.DisplayLineControl;
      reader.readMessage(value,proto.realtime.DisplayLineControl.deserializeBinaryFromReader);
      msg.setFormat(value);
      break;
    case 3:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setLineText(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setLineColors(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.LcdDisplayLine.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.LcdDisplayLine.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.LcdDisplayLine} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.LcdDisplayLine.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLineNumber();
  if (f !== 0) {
    writer.writeUint32(
      1,
      f
    );
  }
  f = message.getFormat();
  if (f != null) {
    writer.writeMessage(
      2,
      f,
      proto.realtime.DisplayLineControl.serializeBinaryToWriter
    );
  }
  f = message.getLineText_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      3,
      f
    );
  }
  f = message.getLineColors_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
};


/**
 * optional uint32 line_number = 1;
 * @return {number}
 */
proto.realtime.LcdDisplayLine.prototype.getLineNumber = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.LcdDisplayLine} returns this
 */
proto.realtime.LcdDisplayLine.prototype.setLineNumber = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional DisplayLineControl format = 2;
 * @return {?proto.realtime.DisplayLineControl}
 */
proto.realtime.LcdDisplayLine.prototype.getFormat = function() {
  return /** @type{?proto.realtime.DisplayLineControl} */ (
    jspb.Message.getWrapperField(this, proto.realtime.DisplayLineControl, 2));
};


/**
 * @param {?proto.realtime.DisplayLineControl|undefined} value
 * @return {!proto.realtime.LcdDisplayLine} returns this
*/
proto.realtime.LcdDisplayLine.prototype.setFormat = function(value) {
  return jspb.Message.setWrapperField(this, 2, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.realtime.LcdDisplayLine} returns this
 */
proto.realtime.LcdDisplayLine.prototype.clearFormat = function() {
  return this.setFormat(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.LcdDisplayLine.prototype.hasFormat = function() {
  return jspb.Message.getField(this, 2) != null;
};


/**
 * optional bytes line_text = 3;
 * @return {string}
 */
proto.realtime.LcdDisplayLine.prototype.getLineText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * optional bytes line_text = 3;
 * This is a type-conversion wrapper around `getLineText()`
 * @return {string}
 */
proto.realtime.LcdDisplayLine.prototype.getLineText_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getLineText()));
};


/**
 * optional bytes line_text = 3;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getLineText()`
 * @return {!Uint8Array}
 */
proto.realtime.LcdDisplayLine.prototype.getLineText_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getLineText()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.realtime.LcdDisplayLine} returns this
 */
proto.realtime.LcdDisplayLine.prototype.setLineText = function(value) {
  return jspb.Message.setProto3BytesField(this, 3, value);
};


/**
 * optional bytes line_colors = 4;
 * @return {string}
 */
proto.realtime.LcdDisplayLine.prototype.getLineColors = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes line_colors = 4;
 * This is a type-conversion wrapper around `getLineColors()`
 * @return {string}
 */
proto.realtime.LcdDisplayLine.prototype.getLineColors_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getLineColors()));
};


/**
 * optional bytes line_colors = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getLineColors()`
 * @return {!Uint8Array}
 */
proto.realtime.LcdDisplayLine.prototype.getLineColors_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getLineColors()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.realtime.LcdDisplayLine} returns this
 */
proto.realtime.LcdDisplayLine.prototype.setLineColors = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.realtime.MonitorDisplayData.repeatedFields_ = [11];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.MonitorDisplayData.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.MonitorDisplayData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.MonitorDisplayData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.MonitorDisplayData.toObject = function(includeInstance, msg) {
  var f, obj = {
enableFieldDisplay: jspb.Message.getBooleanFieldWithDefault(msg, 12, false),
displayChannels: jspb.Message.getFieldWithDefault(msg, 13, 0),
redFieldLedsOnChmap: jspb.Message.getFieldWithDefault(msg, 1, 0),
yellowFieldLedsOnChmap: jspb.Message.getFieldWithDefault(msg, 2, 0),
greenFieldLedsOnChmap: jspb.Message.getFieldWithDefault(msg, 3, 0),
walkFieldLedsOnChmap: jspb.Message.getFieldWithDefault(msg, 4, 0),
blueFaultLedsOnChmap: jspb.Message.getFieldWithDefault(msg, 5, 0),
redFcsChmap: jspb.Message.getFieldWithDefault(msg, 6, 0),
yellowFcsChmap: jspb.Message.getFieldWithDefault(msg, 7, 0),
greenFcsChmap: jspb.Message.getFieldWithDefault(msg, 8, 0),
walkFcsChmap: jspb.Message.getFieldWithDefault(msg, 9, 0),
lineCount: jspb.Message.getFieldWithDefault(msg, 10, 0),
displayLinesList: jspb.Message.toObjectList(msg.getDisplayLinesList(),
    proto.realtime.LcdDisplayLine.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.MonitorDisplayData}
 */
proto.realtime.MonitorDisplayData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.MonitorDisplayData;
  return proto.realtime.MonitorDisplayData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.MonitorDisplayData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.MonitorDisplayData}
 */
proto.realtime.MonitorDisplayData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 12:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setEnableFieldDisplay(value);
      break;
    case 13:
      var value = /** @type {!proto.realtime.EDisplayChannelsType} */ (reader.readEnum());
      msg.setDisplayChannels(value);
      break;
    case 1:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedFieldLedsOnChmap(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowFieldLedsOnChmap(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreenFieldLedsOnChmap(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalkFieldLedsOnChmap(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setBlueFaultLedsOnChmap(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setRedFcsChmap(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setYellowFcsChmap(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setGreenFcsChmap(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setWalkFcsChmap(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLineCount(value);
      break;
    case 11:
      var value = new proto.realtime.LcdDisplayLine;
      reader.readMessage(value,proto.realtime.LcdDisplayLine.deserializeBinaryFromReader);
      msg.addDisplayLines(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.MonitorDisplayData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.MonitorDisplayData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.MonitorDisplayData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.MonitorDisplayData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getEnableFieldDisplay();
  if (f) {
    writer.writeBool(
      12,
      f
    );
  }
  f = message.getDisplayChannels();
  if (f !== 0.0) {
    writer.writeEnum(
      13,
      f
    );
  }
  f = message.getRedFieldLedsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      1,
      f
    );
  }
  f = message.getYellowFieldLedsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      2,
      f
    );
  }
  f = message.getGreenFieldLedsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      3,
      f
    );
  }
  f = message.getWalkFieldLedsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      4,
      f
    );
  }
  f = message.getBlueFaultLedsOnChmap();
  if (f !== 0) {
    writer.writeFixed32(
      5,
      f
    );
  }
  f = message.getRedFcsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      6,
      f
    );
  }
  f = message.getYellowFcsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      7,
      f
    );
  }
  f = message.getGreenFcsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      8,
      f
    );
  }
  f = message.getWalkFcsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      9,
      f
    );
  }
  f = message.getLineCount();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getDisplayLinesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      11,
      f,
      proto.realtime.LcdDisplayLine.serializeBinaryToWriter
    );
  }
};


/**
 * optional bool enable_field_display = 12;
 * @return {boolean}
 */
proto.realtime.MonitorDisplayData.prototype.getEnableFieldDisplay = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 12, false));
};


/**
 * @param {boolean} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setEnableFieldDisplay = function(value) {
  return jspb.Message.setProto3BooleanField(this, 12, value);
};


/**
 * optional EDisplayChannelsType display_channels = 13;
 * @return {!proto.realtime.EDisplayChannelsType}
 */
proto.realtime.MonitorDisplayData.prototype.getDisplayChannels = function() {
  return /** @type {!proto.realtime.EDisplayChannelsType} */ (jspb.Message.getFieldWithDefault(this, 13, 0));
};


/**
 * @param {!proto.realtime.EDisplayChannelsType} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setDisplayChannels = function(value) {
  return jspb.Message.setProto3EnumField(this, 13, value);
};


/**
 * optional fixed32 red_field_leds_on_chmap = 1;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getRedFieldLedsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setRedFieldLedsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional fixed32 yellow_field_leds_on_chmap = 2;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getYellowFieldLedsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setYellowFieldLedsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional fixed32 green_field_leds_on_chmap = 3;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getGreenFieldLedsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setGreenFieldLedsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional fixed32 walk_field_leds_on_chmap = 4;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getWalkFieldLedsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setWalkFieldLedsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional fixed32 blue_fault_leds_on_chmap = 5;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getBlueFaultLedsOnChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setBlueFaultLedsOnChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional fixed32 red_fcs_chmap = 6;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getRedFcsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setRedFcsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional fixed32 yellow_fcs_chmap = 7;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getYellowFcsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setYellowFcsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 7, value);
};


/**
 * optional fixed32 green_fcs_chmap = 8;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getGreenFcsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setGreenFcsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional fixed32 walk_fcs_chmap = 9;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getWalkFcsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setWalkFcsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional uint32 line_count = 10;
 * @return {number}
 */
proto.realtime.MonitorDisplayData.prototype.getLineCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.setLineCount = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * repeated LcdDisplayLine display_lines = 11;
 * @return {!Array<!proto.realtime.LcdDisplayLine>}
 */
proto.realtime.MonitorDisplayData.prototype.getDisplayLinesList = function() {
  return /** @type{!Array<!proto.realtime.LcdDisplayLine>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.realtime.LcdDisplayLine, 11));
};


/**
 * @param {!Array<!proto.realtime.LcdDisplayLine>} value
 * @return {!proto.realtime.MonitorDisplayData} returns this
*/
proto.realtime.MonitorDisplayData.prototype.setDisplayLinesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 11, value);
};


/**
 * @param {!proto.realtime.LcdDisplayLine=} opt_value
 * @param {number=} opt_index
 * @return {!proto.realtime.LcdDisplayLine}
 */
proto.realtime.MonitorDisplayData.prototype.addDisplayLines = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 11, opt_value, proto.realtime.LcdDisplayLine, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.realtime.MonitorDisplayData} returns this
 */
proto.realtime.MonitorDisplayData.prototype.clearDisplayLinesList = function() {
  return this.setDisplayLinesList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.PanelLedsBitmap.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.PanelLedsBitmap.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.PanelLedsBitmap} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.PanelLedsBitmap.toObject = function(includeInstance, msg) {
  var f, obj = {
power: jspb.Message.getBooleanFieldWithDefault(msg, 1, false),
fault: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
diag: jspb.Message.getBooleanFieldWithDefault(msg, 3, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.PanelLedsBitmap}
 */
proto.realtime.PanelLedsBitmap.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.PanelLedsBitmap;
  return proto.realtime.PanelLedsBitmap.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.PanelLedsBitmap} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.PanelLedsBitmap}
 */
proto.realtime.PanelLedsBitmap.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setPower(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setFault(value);
      break;
    case 3:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDiag(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.PanelLedsBitmap.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.PanelLedsBitmap.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.PanelLedsBitmap} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.PanelLedsBitmap.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPower();
  if (f) {
    writer.writeBool(
      1,
      f
    );
  }
  f = message.getFault();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getDiag();
  if (f) {
    writer.writeBool(
      3,
      f
    );
  }
};


/**
 * optional bool power = 1;
 * @return {boolean}
 */
proto.realtime.PanelLedsBitmap.prototype.getPower = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 1, false));
};


/**
 * @param {boolean} value
 * @return {!proto.realtime.PanelLedsBitmap} returns this
 */
proto.realtime.PanelLedsBitmap.prototype.setPower = function(value) {
  return jspb.Message.setProto3BooleanField(this, 1, value);
};


/**
 * optional bool fault = 2;
 * @return {boolean}
 */
proto.realtime.PanelLedsBitmap.prototype.getFault = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.realtime.PanelLedsBitmap} returns this
 */
proto.realtime.PanelLedsBitmap.prototype.setFault = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional bool diag = 3;
 * @return {boolean}
 */
proto.realtime.PanelLedsBitmap.prototype.getDiag = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 3, false));
};


/**
 * @param {boolean} value
 * @return {!proto.realtime.PanelLedsBitmap} returns this
 */
proto.realtime.PanelLedsBitmap.prototype.setDiag = function(value) {
  return jspb.Message.setProto3BooleanField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.MmuMonitoredInputsVoltages.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.MmuMonitoredInputsVoltages} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.MmuMonitoredInputsVoltages.toObject = function(includeInstance, msg) {
  var f, obj = {
input124vInhibitVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 1, 0.0),
input224vMonitor1Volts: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
input324vMonitor2Volts: jspb.Message.getFloatingPointFieldWithDefault(msg, 3, 0.0),
input4ControllerVoltage: jspb.Message.getFloatingPointFieldWithDefault(msg, 4, 0.0),
input5TypeSelectVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 5, 0.0),
input6RedEnableVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0),
input7ExternalResetVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 7, 0.0),
input8Port1DisableVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 8, 0.0),
input9ExternalWatchdogVolts: jspb.Message.getFloatingPointFieldWithDefault(msg, 9, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.MmuMonitoredInputsVoltages}
 */
proto.realtime.MmuMonitoredInputsVoltages.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.MmuMonitoredInputsVoltages;
  return proto.realtime.MmuMonitoredInputsVoltages.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.MmuMonitoredInputsVoltages} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.MmuMonitoredInputsVoltages}
 */
proto.realtime.MmuMonitoredInputsVoltages.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput124vInhibitVolts(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput224vMonitor1Volts(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput324vMonitor2Volts(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput4ControllerVoltage(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput5TypeSelectVolts(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput6RedEnableVolts(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput7ExternalResetVolts(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput8Port1DisableVolts(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setInput9ExternalWatchdogVolts(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.MmuMonitoredInputsVoltages.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.MmuMonitoredInputsVoltages} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.MmuMonitoredInputsVoltages.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getInput124vInhibitVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      1,
      f
    );
  }
  f = message.getInput224vMonitor1Volts();
  if (f !== 0.0) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = message.getInput324vMonitor2Volts();
  if (f !== 0.0) {
    writer.writeFloat(
      3,
      f
    );
  }
  f = message.getInput4ControllerVoltage();
  if (f !== 0.0) {
    writer.writeFloat(
      4,
      f
    );
  }
  f = message.getInput5TypeSelectVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      5,
      f
    );
  }
  f = message.getInput6RedEnableVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      6,
      f
    );
  }
  f = message.getInput7ExternalResetVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      7,
      f
    );
  }
  f = message.getInput8Port1DisableVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      8,
      f
    );
  }
  f = message.getInput9ExternalWatchdogVolts();
  if (f !== 0.0) {
    writer.writeFloat(
      9,
      f
    );
  }
};


/**
 * optional float input1_24v_inhibit_volts = 1;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput124vInhibitVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 1, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput124vInhibitVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 1, value);
};


/**
 * optional float input2_24v_monitor_1_volts = 2;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput224vMonitor1Volts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput224vMonitor1Volts = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional float input3_24v_monitor_2_volts = 3;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput324vMonitor2Volts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput324vMonitor2Volts = function(value) {
  return jspb.Message.setProto3FloatField(this, 3, value);
};


/**
 * optional float input4_controller_voltage = 4;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput4ControllerVoltage = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 4, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput4ControllerVoltage = function(value) {
  return jspb.Message.setProto3FloatField(this, 4, value);
};


/**
 * optional float input5_type_select_volts = 5;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput5TypeSelectVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 5, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput5TypeSelectVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 5, value);
};


/**
 * optional float input6_red_enable_volts = 6;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput6RedEnableVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput6RedEnableVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};


/**
 * optional float input7_external_reset_volts = 7;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput7ExternalResetVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 7, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput7ExternalResetVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 7, value);
};


/**
 * optional float input8_port1_disable_volts = 8;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput8Port1DisableVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 8, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput8Port1DisableVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 8, value);
};


/**
 * optional float input9_external_watchdog_volts = 9;
 * @return {number}
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.getInput9ExternalWatchdogVolts = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 9, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MmuMonitoredInputsVoltages} returns this
 */
proto.realtime.MmuMonitoredInputsVoltages.prototype.setInput9ExternalWatchdogVolts = function(value) {
  return jspb.Message.setProto3FloatField(this, 9, value);
};



/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.realtime.MonitorPresentStatus.oneofGroups_ = [[12,13],[14,15],[26],[27]];

/**
 * @enum {number}
 */
proto.realtime.MonitorPresentStatus.SupplyVoltageCase = {
  SUPPLY_VOLTAGE_NOT_SET: 0,
  AC_MAINS_VOLTAGE: 12,
  DC_SUPPLY_VOLTAGE: 13
};

/**
 * @return {proto.realtime.MonitorPresentStatus.SupplyVoltageCase}
 */
proto.realtime.MonitorPresentStatus.prototype.getSupplyVoltageCase = function() {
  return /** @type {proto.realtime.MonitorPresentStatus.SupplyVoltageCase} */(jspb.Message.computeOneofCase(this, proto.realtime.MonitorPresentStatus.oneofGroups_[0]));
};

/**
 * @enum {number}
 */
proto.realtime.MonitorPresentStatus.SupplyCharacteristicCase = {
  SUPPLY_CHARACTERISTIC_NOT_SET: 0,
  AC_FREQUENCY_HZ: 14,
  DC_RIPPLE_MV: 15
};

/**
 * @return {proto.realtime.MonitorPresentStatus.SupplyCharacteristicCase}
 */
proto.realtime.MonitorPresentStatus.prototype.getSupplyCharacteristicCase = function() {
  return /** @type {proto.realtime.MonitorPresentStatus.SupplyCharacteristicCase} */(jspb.Message.computeOneofCase(this, proto.realtime.MonitorPresentStatus.oneofGroups_[1]));
};

/**
 * @enum {number}
 */
proto.realtime.MonitorPresentStatus.MonitoredInputStatesCase = {
  MONITORED_INPUT_STATES_NOT_SET: 0,
  MMU_STATES: 26
};

/**
 * @return {proto.realtime.MonitorPresentStatus.MonitoredInputStatesCase}
 */
proto.realtime.MonitorPresentStatus.prototype.getMonitoredInputStatesCase = function() {
  return /** @type {proto.realtime.MonitorPresentStatus.MonitoredInputStatesCase} */(jspb.Message.computeOneofCase(this, proto.realtime.MonitorPresentStatus.oneofGroups_[2]));
};

/**
 * @enum {number}
 */
proto.realtime.MonitorPresentStatus.MonitoredInputVoltsCase = {
  MONITORED_INPUT_VOLTS_NOT_SET: 0,
  MMU_VOLTAGES: 27
};

/**
 * @return {proto.realtime.MonitorPresentStatus.MonitoredInputVoltsCase}
 */
proto.realtime.MonitorPresentStatus.prototype.getMonitoredInputVoltsCase = function() {
  return /** @type {proto.realtime.MonitorPresentStatus.MonitoredInputVoltsCase} */(jspb.Message.computeOneofCase(this, proto.realtime.MonitorPresentStatus.oneofGroups_[3]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.realtime.MonitorPresentStatus.prototype.toObject = function(opt_includeInstance) {
  return proto.realtime.MonitorPresentStatus.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.realtime.MonitorPresentStatus} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.MonitorPresentStatus.toObject = function(includeInstance, msg) {
  var f, obj = {
faultCode: jspb.Message.getFieldWithDefault(msg, 1, 0),
diagnosticFault: jspb.Message.getBooleanFieldWithDefault(msg, 2, false),
faultSubcode: (f = msg.getFaultSubcode()) && mon_faults_pb.MmuSubFaultTypeValue.toObject(includeInstance, f),
diagnosticCode: jspb.Message.getFieldWithDefault(msg, 8, 0),
faultChannelsChmap: jspb.Message.getFieldWithDefault(msg, 9, 0),
lastFaultId: jspb.Message.getFieldWithDefault(msg, 10, 0),
leds: (f = msg.getLeds()) && proto.realtime.PanelLedsBitmap.toObject(includeInstance, f),
acMainsVoltage: (f = jspb.Message.getOptionalFloatingPointField(msg, 12)) == null ? undefined : f,
dcSupplyVoltage: (f = jspb.Message.getOptionalFloatingPointField(msg, 13)) == null ? undefined : f,
acFrequencyHz: (f = jspb.Message.getOptionalFloatingPointField(msg, 14)) == null ? undefined : f,
dcRippleMv: (f = jspb.Message.getOptionalFloatingPointField(msg, 15)) == null ? undefined : f,
temperatureDegf: jspb.Message.getFloatingPointFieldWithDefault(msg, 16, 0.0),
minFlashRemainingS: jspb.Message.getFieldWithDefault(msg, 17, 0),
controllerTimeoutCount: jspb.Message.getFieldWithDefault(msg, 18, 0),
controllerDateTime: (f = msg.getControllerDateTime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
rtcDateTime: (f = msg.getRtcDateTime()) && basic_pb.LocalDateTime.toObject(includeInstance, f),
totalRunTime: jspb.Message.getFieldWithDefault(msg, 22, 0),
totalFlashTime: jspb.Message.getFieldWithDefault(msg, 23, 0),
totalFaultTime: jspb.Message.getFieldWithDefault(msg, 24, 0),
lastConfigId: jspb.Message.getFieldWithDefault(msg, 25, 0),
mmuStates: (f = msg.getMmuStates()) && mon_faults_pb.MmuMonitoredInputsStatusBitmap.toObject(includeInstance, f),
mmuVoltages: (f = msg.getMmuVoltages()) && proto.realtime.MmuMonitoredInputsVoltages.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.realtime.MonitorPresentStatus}
 */
proto.realtime.MonitorPresentStatus.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.realtime.MonitorPresentStatus;
  return proto.realtime.MonitorPresentStatus.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.realtime.MonitorPresentStatus} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.realtime.MonitorPresentStatus}
 */
proto.realtime.MonitorPresentStatus.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.mon_faults.EFaultCode} */ (reader.readEnum());
      msg.setFaultCode(value);
      break;
    case 2:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDiagnosticFault(value);
      break;
    case 28:
      var value = new mon_faults_pb.MmuSubFaultTypeValue;
      reader.readMessage(value,mon_faults_pb.MmuSubFaultTypeValue.deserializeBinaryFromReader);
      msg.setFaultSubcode(value);
      break;
    case 8:
      var value = /** @type {!proto.mon_faults.ESubFaultDiagnostic} */ (reader.readEnum());
      msg.setDiagnosticCode(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readFixed32());
      msg.setFaultChannelsChmap(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastFaultId(value);
      break;
    case 11:
      var value = new proto.realtime.PanelLedsBitmap;
      reader.readMessage(value,proto.realtime.PanelLedsBitmap.deserializeBinaryFromReader);
      msg.setLeds(value);
      break;
    case 12:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setAcMainsVoltage(value);
      break;
    case 13:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setDcSupplyVoltage(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setAcFrequencyHz(value);
      break;
    case 15:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setDcRippleMv(value);
      break;
    case 16:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setTemperatureDegf(value);
      break;
    case 17:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setMinFlashRemainingS(value);
      break;
    case 18:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setControllerTimeoutCount(value);
      break;
    case 20:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setControllerDateTime(value);
      break;
    case 21:
      var value = new basic_pb.LocalDateTime;
      reader.readMessage(value,basic_pb.LocalDateTime.deserializeBinaryFromReader);
      msg.setRtcDateTime(value);
      break;
    case 22:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTotalRunTime(value);
      break;
    case 23:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTotalFlashTime(value);
      break;
    case 24:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setTotalFaultTime(value);
      break;
    case 25:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setLastConfigId(value);
      break;
    case 26:
      var value = new mon_faults_pb.MmuMonitoredInputsStatusBitmap;
      reader.readMessage(value,mon_faults_pb.MmuMonitoredInputsStatusBitmap.deserializeBinaryFromReader);
      msg.setMmuStates(value);
      break;
    case 27:
      var value = new proto.realtime.MmuMonitoredInputsVoltages;
      reader.readMessage(value,proto.realtime.MmuMonitoredInputsVoltages.deserializeBinaryFromReader);
      msg.setMmuVoltages(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.realtime.MonitorPresentStatus.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.realtime.MonitorPresentStatus.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.realtime.MonitorPresentStatus} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.realtime.MonitorPresentStatus.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFaultCode();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getDiagnosticFault();
  if (f) {
    writer.writeBool(
      2,
      f
    );
  }
  f = message.getFaultSubcode();
  if (f != null) {
    writer.writeMessage(
      28,
      f,
      mon_faults_pb.MmuSubFaultTypeValue.serializeBinaryToWriter
    );
  }
  f = message.getDiagnosticCode();
  if (f !== 0.0) {
    writer.writeEnum(
      8,
      f
    );
  }
  f = message.getFaultChannelsChmap();
  if (f !== 0) {
    writer.writeFixed32(
      9,
      f
    );
  }
  f = message.getLastFaultId();
  if (f !== 0) {
    writer.writeUint32(
      10,
      f
    );
  }
  f = message.getLeds();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      proto.realtime.PanelLedsBitmap.serializeBinaryToWriter
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 12));
  if (f != null) {
    writer.writeFloat(
      12,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 13));
  if (f != null) {
    writer.writeFloat(
      13,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 14));
  if (f != null) {
    writer.writeFloat(
      14,
      f
    );
  }
  f = /** @type {number} */ (jspb.Message.getField(message, 15));
  if (f != null) {
    writer.writeFloat(
      15,
      f
    );
  }
  f = message.getTemperatureDegf();
  if (f !== 0.0) {
    writer.writeFloat(
      16,
      f
    );
  }
  f = message.getMinFlashRemainingS();
  if (f !== 0) {
    writer.writeUint32(
      17,
      f
    );
  }
  f = message.getControllerTimeoutCount();
  if (f !== 0) {
    writer.writeUint32(
      18,
      f
    );
  }
  f = message.getControllerDateTime();
  if (f != null) {
    writer.writeMessage(
      20,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getRtcDateTime();
  if (f != null) {
    writer.writeMessage(
      21,
      f,
      basic_pb.LocalDateTime.serializeBinaryToWriter
    );
  }
  f = message.getTotalRunTime();
  if (f !== 0) {
    writer.writeUint32(
      22,
      f
    );
  }
  f = message.getTotalFlashTime();
  if (f !== 0) {
    writer.writeUint32(
      23,
      f
    );
  }
  f = message.getTotalFaultTime();
  if (f !== 0) {
    writer.writeUint32(
      24,
      f
    );
  }
  f = message.getLastConfigId();
  if (f !== 0) {
    writer.writeUint32(
      25,
      f
    );
  }
  f = message.getMmuStates();
  if (f != null) {
    writer.writeMessage(
      26,
      f,
      mon_faults_pb.MmuMonitoredInputsStatusBitmap.serializeBinaryToWriter
    );
  }
  f = message.getMmuVoltages();
  if (f != null) {
    writer.writeMessage(
      27,
      f,
      proto.realtime.MmuMonitoredInputsVoltages.serializeBinaryToWriter
    );
  }
};


/**
 * optional mon_faults.EFaultCode fault_code = 1;
 * @return {!proto.mon_faults.EFaultCode}
 */
proto.realtime.MonitorPresentStatus.prototype.getFaultCode = function() {
  return /** @type {!proto.mon_faults.EFaultCode} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.mon_faults.EFaultCode} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setFaultCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional bool diagnostic_fault = 2;
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.getDiagnosticFault = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 2, false));
};


/**
 * @param {boolean} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setDiagnosticFault = function(value) {
  return jspb.Message.setProto3BooleanField(this, 2, value);
};


/**
 * optional mon_faults.MmuSubFaultTypeValue fault_subcode = 28;
 * @return {?proto.mon_faults.MmuSubFaultTypeValue}
 */
proto.realtime.MonitorPresentStatus.prototype.getFaultSubcode = function() {
  return /** @type{?proto.mon_faults.MmuSubFaultTypeValue} */ (
    jspb.Message.getWrapperField(this, mon_faults_pb.MmuSubFaultTypeValue, 28));
};


/**
 * @param {?proto.mon_faults.MmuSubFaultTypeValue|undefined} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
*/
proto.realtime.MonitorPresentStatus.prototype.setFaultSubcode = function(value) {
  return jspb.Message.setWrapperField(this, 28, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearFaultSubcode = function() {
  return this.setFaultSubcode(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasFaultSubcode = function() {
  return jspb.Message.getField(this, 28) != null;
};


/**
 * optional mon_faults.ESubFaultDiagnostic diagnostic_code = 8;
 * @return {!proto.mon_faults.ESubFaultDiagnostic}
 */
proto.realtime.MonitorPresentStatus.prototype.getDiagnosticCode = function() {
  return /** @type {!proto.mon_faults.ESubFaultDiagnostic} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {!proto.mon_faults.ESubFaultDiagnostic} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setDiagnosticCode = function(value) {
  return jspb.Message.setProto3EnumField(this, 8, value);
};


/**
 * optional fixed32 fault_channels_chmap = 9;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getFaultChannelsChmap = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setFaultChannelsChmap = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


/**
 * optional uint32 last_fault_id = 10;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getLastFaultId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setLastFaultId = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional PanelLedsBitmap leds = 11;
 * @return {?proto.realtime.PanelLedsBitmap}
 */
proto.realtime.MonitorPresentStatus.prototype.getLeds = function() {
  return /** @type{?proto.realtime.PanelLedsBitmap} */ (
    jspb.Message.getWrapperField(this, proto.realtime.PanelLedsBitmap, 11));
};


/**
 * @param {?proto.realtime.PanelLedsBitmap|undefined} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
*/
proto.realtime.MonitorPresentStatus.prototype.setLeds = function(value) {
  return jspb.Message.setWrapperField(this, 11, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearLeds = function() {
  return this.setLeds(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasLeds = function() {
  return jspb.Message.getField(this, 11) != null;
};


/**
 * optional float ac_mains_voltage = 12;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getAcMainsVoltage = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 12, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setAcMainsVoltage = function(value) {
  return jspb.Message.setOneofField(this, 12, proto.realtime.MonitorPresentStatus.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearAcMainsVoltage = function() {
  return jspb.Message.setOneofField(this, 12, proto.realtime.MonitorPresentStatus.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasAcMainsVoltage = function() {
  return jspb.Message.getField(this, 12) != null;
};


/**
 * optional float dc_supply_voltage = 13;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getDcSupplyVoltage = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 13, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setDcSupplyVoltage = function(value) {
  return jspb.Message.setOneofField(this, 13, proto.realtime.MonitorPresentStatus.oneofGroups_[0], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearDcSupplyVoltage = function() {
  return jspb.Message.setOneofField(this, 13, proto.realtime.MonitorPresentStatus.oneofGroups_[0], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasDcSupplyVoltage = function() {
  return jspb.Message.getField(this, 13) != null;
};


/**
 * optional float ac_frequency_hz = 14;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getAcFrequencyHz = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 14, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setAcFrequencyHz = function(value) {
  return jspb.Message.setOneofField(this, 14, proto.realtime.MonitorPresentStatus.oneofGroups_[1], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearAcFrequencyHz = function() {
  return jspb.Message.setOneofField(this, 14, proto.realtime.MonitorPresentStatus.oneofGroups_[1], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasAcFrequencyHz = function() {
  return jspb.Message.getField(this, 14) != null;
};


/**
 * optional float dc_ripple_mv = 15;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getDcRippleMv = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 15, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setDcRippleMv = function(value) {
  return jspb.Message.setOneofField(this, 15, proto.realtime.MonitorPresentStatus.oneofGroups_[1], value);
};


/**
 * Clears the field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearDcRippleMv = function() {
  return jspb.Message.setOneofField(this, 15, proto.realtime.MonitorPresentStatus.oneofGroups_[1], undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasDcRippleMv = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional float temperature_degf = 16;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getTemperatureDegf = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 16, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setTemperatureDegf = function(value) {
  return jspb.Message.setProto3FloatField(this, 16, value);
};


/**
 * optional uint32 min_flash_remaining_s = 17;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getMinFlashRemainingS = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 17, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setMinFlashRemainingS = function(value) {
  return jspb.Message.setProto3IntField(this, 17, value);
};


/**
 * optional uint32 controller_timeout_count = 18;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getControllerTimeoutCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 18, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setControllerTimeoutCount = function(value) {
  return jspb.Message.setProto3IntField(this, 18, value);
};


/**
 * optional basic.LocalDateTime controller_date_time = 20;
 * @return {?proto.basic.LocalDateTime}
 */
proto.realtime.MonitorPresentStatus.prototype.getControllerDateTime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 20));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
*/
proto.realtime.MonitorPresentStatus.prototype.setControllerDateTime = function(value) {
  return jspb.Message.setWrapperField(this, 20, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearControllerDateTime = function() {
  return this.setControllerDateTime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasControllerDateTime = function() {
  return jspb.Message.getField(this, 20) != null;
};


/**
 * optional basic.LocalDateTime rtc_date_time = 21;
 * @return {?proto.basic.LocalDateTime}
 */
proto.realtime.MonitorPresentStatus.prototype.getRtcDateTime = function() {
  return /** @type{?proto.basic.LocalDateTime} */ (
    jspb.Message.getWrapperField(this, basic_pb.LocalDateTime, 21));
};


/**
 * @param {?proto.basic.LocalDateTime|undefined} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
*/
proto.realtime.MonitorPresentStatus.prototype.setRtcDateTime = function(value) {
  return jspb.Message.setWrapperField(this, 21, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearRtcDateTime = function() {
  return this.setRtcDateTime(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasRtcDateTime = function() {
  return jspb.Message.getField(this, 21) != null;
};


/**
 * optional uint32 total_run_time = 22;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getTotalRunTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 22, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setTotalRunTime = function(value) {
  return jspb.Message.setProto3IntField(this, 22, value);
};


/**
 * optional uint32 total_flash_time = 23;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getTotalFlashTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 23, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setTotalFlashTime = function(value) {
  return jspb.Message.setProto3IntField(this, 23, value);
};


/**
 * optional uint32 total_fault_time = 24;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getTotalFaultTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 24, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setTotalFaultTime = function(value) {
  return jspb.Message.setProto3IntField(this, 24, value);
};


/**
 * optional uint32 last_config_id = 25;
 * @return {number}
 */
proto.realtime.MonitorPresentStatus.prototype.getLastConfigId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 25, 0));
};


/**
 * @param {number} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.setLastConfigId = function(value) {
  return jspb.Message.setProto3IntField(this, 25, value);
};


/**
 * optional mon_faults.MmuMonitoredInputsStatusBitmap mmu_states = 26;
 * @return {?proto.mon_faults.MmuMonitoredInputsStatusBitmap}
 */
proto.realtime.MonitorPresentStatus.prototype.getMmuStates = function() {
  return /** @type{?proto.mon_faults.MmuMonitoredInputsStatusBitmap} */ (
    jspb.Message.getWrapperField(this, mon_faults_pb.MmuMonitoredInputsStatusBitmap, 26));
};


/**
 * @param {?proto.mon_faults.MmuMonitoredInputsStatusBitmap|undefined} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
*/
proto.realtime.MonitorPresentStatus.prototype.setMmuStates = function(value) {
  return jspb.Message.setOneofWrapperField(this, 26, proto.realtime.MonitorPresentStatus.oneofGroups_[2], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearMmuStates = function() {
  return this.setMmuStates(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasMmuStates = function() {
  return jspb.Message.getField(this, 26) != null;
};


/**
 * optional MmuMonitoredInputsVoltages mmu_voltages = 27;
 * @return {?proto.realtime.MmuMonitoredInputsVoltages}
 */
proto.realtime.MonitorPresentStatus.prototype.getMmuVoltages = function() {
  return /** @type{?proto.realtime.MmuMonitoredInputsVoltages} */ (
    jspb.Message.getWrapperField(this, proto.realtime.MmuMonitoredInputsVoltages, 27));
};


/**
 * @param {?proto.realtime.MmuMonitoredInputsVoltages|undefined} value
 * @return {!proto.realtime.MonitorPresentStatus} returns this
*/
proto.realtime.MonitorPresentStatus.prototype.setMmuVoltages = function(value) {
  return jspb.Message.setOneofWrapperField(this, 27, proto.realtime.MonitorPresentStatus.oneofGroups_[3], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.realtime.MonitorPresentStatus} returns this
 */
proto.realtime.MonitorPresentStatus.prototype.clearMmuVoltages = function() {
  return this.setMmuVoltages(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.realtime.MonitorPresentStatus.prototype.hasMmuVoltages = function() {
  return jspb.Message.getField(this, 27) != null;
};


/**
 * @enum {number}
 */
proto.realtime.EDisplayChannelsType = {
  DISP_CH_UNSPECIFIED: 0,
  DISP_CH_3: 1,
  DISP_CH_6: 2,
  DISP_CH_12: 3,
  DISP_CH_16: 4,
  DISP_CH_16X: 5
};

goog.object.extend(exports, proto.realtime);
