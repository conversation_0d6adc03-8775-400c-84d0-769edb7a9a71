// source: dfu.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var basic_pb = require('./basic_pb.js');
goog.object.extend(proto, basic_pb);
goog.exportSymbol('proto.dfu.DfuManifestEntry', null, global);
goog.exportSymbol('proto.dfu.DfuManifestStatusEntry', null, global);
goog.exportSymbol('proto.dfu.EDfuResultCode', null, global);
goog.exportSymbol('proto.dfu.EFirmwareUpdateStatus', null, global);
goog.exportSymbol('proto.dfu.EFirmwareVersionsManifest', null, global);
goog.exportSymbol('proto.dfu.EImageType', null, global);
goog.exportSymbol('proto.dfu.EManifestEntryStatus', null, global);
goog.exportSymbol('proto.dfu.EMonitorModelNumber', null, global);
goog.exportSymbol('proto.dfu.EProcessorType', null, global);
goog.exportSymbol('proto.dfu.FirmwareImageVersion', null, global);
goog.exportSymbol('proto.dfu.FirmwareVersionSimple', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.dfu.DfuManifestEntry = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.dfu.DfuManifestEntry, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.dfu.DfuManifestEntry.displayName = 'proto.dfu.DfuManifestEntry';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.dfu.DfuManifestStatusEntry = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.dfu.DfuManifestStatusEntry, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.dfu.DfuManifestStatusEntry.displayName = 'proto.dfu.DfuManifestStatusEntry';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.dfu.FirmwareImageVersion = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.dfu.FirmwareImageVersion, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.dfu.FirmwareImageVersion.displayName = 'proto.dfu.FirmwareImageVersion';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.dfu.FirmwareVersionSimple = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.dfu.FirmwareVersionSimple, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.dfu.FirmwareVersionSimple.displayName = 'proto.dfu.FirmwareVersionSimple';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.dfu.DfuManifestEntry.prototype.toObject = function(opt_includeInstance) {
  return proto.dfu.DfuManifestEntry.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.dfu.DfuManifestEntry} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.DfuManifestEntry.toObject = function(includeInstance, msg) {
  var f, obj = {
imageVersion: (f = msg.getImageVersion()) && proto.dfu.FirmwareImageVersion.toObject(includeInstance, f),
filename: jspb.Message.getFieldWithDefault(msg, 2, ""),
hash: msg.getHash_asB64(),
sizeBytes: jspb.Message.getFieldWithDefault(msg, 5, 0),
entryStatus: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.dfu.DfuManifestEntry}
 */
proto.dfu.DfuManifestEntry.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.dfu.DfuManifestEntry;
  return proto.dfu.DfuManifestEntry.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.dfu.DfuManifestEntry} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.dfu.DfuManifestEntry}
 */
proto.dfu.DfuManifestEntry.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.dfu.FirmwareImageVersion;
      reader.readMessage(value,proto.dfu.FirmwareImageVersion.deserializeBinaryFromReader);
      msg.setImageVersion(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilename(value);
      break;
    case 4:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setHash(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setSizeBytes(value);
      break;
    case 6:
      var value = /** @type {!proto.dfu.EManifestEntryStatus} */ (reader.readEnum());
      msg.setEntryStatus(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.dfu.DfuManifestEntry.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.dfu.DfuManifestEntry.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.dfu.DfuManifestEntry} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.DfuManifestEntry.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getImageVersion();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.dfu.FirmwareImageVersion.serializeBinaryToWriter
    );
  }
  f = message.getFilename();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getHash_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      4,
      f
    );
  }
  f = message.getSizeBytes();
  if (f !== 0) {
    writer.writeUint32(
      5,
      f
    );
  }
  f = message.getEntryStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      6,
      f
    );
  }
};


/**
 * optional FirmwareImageVersion image_version = 1;
 * @return {?proto.dfu.FirmwareImageVersion}
 */
proto.dfu.DfuManifestEntry.prototype.getImageVersion = function() {
  return /** @type{?proto.dfu.FirmwareImageVersion} */ (
    jspb.Message.getWrapperField(this, proto.dfu.FirmwareImageVersion, 1));
};


/**
 * @param {?proto.dfu.FirmwareImageVersion|undefined} value
 * @return {!proto.dfu.DfuManifestEntry} returns this
*/
proto.dfu.DfuManifestEntry.prototype.setImageVersion = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.dfu.DfuManifestEntry} returns this
 */
proto.dfu.DfuManifestEntry.prototype.clearImageVersion = function() {
  return this.setImageVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.dfu.DfuManifestEntry.prototype.hasImageVersion = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional string filename = 2;
 * @return {string}
 */
proto.dfu.DfuManifestEntry.prototype.getFilename = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.dfu.DfuManifestEntry} returns this
 */
proto.dfu.DfuManifestEntry.prototype.setFilename = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional bytes hash = 4;
 * @return {string}
 */
proto.dfu.DfuManifestEntry.prototype.getHash = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * optional bytes hash = 4;
 * This is a type-conversion wrapper around `getHash()`
 * @return {string}
 */
proto.dfu.DfuManifestEntry.prototype.getHash_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getHash()));
};


/**
 * optional bytes hash = 4;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getHash()`
 * @return {!Uint8Array}
 */
proto.dfu.DfuManifestEntry.prototype.getHash_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getHash()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.dfu.DfuManifestEntry} returns this
 */
proto.dfu.DfuManifestEntry.prototype.setHash = function(value) {
  return jspb.Message.setProto3BytesField(this, 4, value);
};


/**
 * optional uint32 size_bytes = 5;
 * @return {number}
 */
proto.dfu.DfuManifestEntry.prototype.getSizeBytes = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.dfu.DfuManifestEntry} returns this
 */
proto.dfu.DfuManifestEntry.prototype.setSizeBytes = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional EManifestEntryStatus entry_status = 6;
 * @return {!proto.dfu.EManifestEntryStatus}
 */
proto.dfu.DfuManifestEntry.prototype.getEntryStatus = function() {
  return /** @type {!proto.dfu.EManifestEntryStatus} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {!proto.dfu.EManifestEntryStatus} value
 * @return {!proto.dfu.DfuManifestEntry} returns this
 */
proto.dfu.DfuManifestEntry.prototype.setEntryStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 6, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.dfu.DfuManifestStatusEntry.prototype.toObject = function(opt_includeInstance) {
  return proto.dfu.DfuManifestStatusEntry.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.dfu.DfuManifestStatusEntry} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.DfuManifestStatusEntry.toObject = function(includeInstance, msg) {
  var f, obj = {
imageVersion: (f = msg.getImageVersion()) && proto.dfu.FirmwareImageVersion.toObject(includeInstance, f),
entryStatus: jspb.Message.getFieldWithDefault(msg, 2, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.dfu.DfuManifestStatusEntry}
 */
proto.dfu.DfuManifestStatusEntry.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.dfu.DfuManifestStatusEntry;
  return proto.dfu.DfuManifestStatusEntry.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.dfu.DfuManifestStatusEntry} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.dfu.DfuManifestStatusEntry}
 */
proto.dfu.DfuManifestStatusEntry.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.dfu.FirmwareImageVersion;
      reader.readMessage(value,proto.dfu.FirmwareImageVersion.deserializeBinaryFromReader);
      msg.setImageVersion(value);
      break;
    case 2:
      var value = /** @type {!proto.dfu.EManifestEntryStatus} */ (reader.readEnum());
      msg.setEntryStatus(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.dfu.DfuManifestStatusEntry.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.dfu.DfuManifestStatusEntry.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.dfu.DfuManifestStatusEntry} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.DfuManifestStatusEntry.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getImageVersion();
  if (f != null) {
    writer.writeMessage(
      1,
      f,
      proto.dfu.FirmwareImageVersion.serializeBinaryToWriter
    );
  }
  f = message.getEntryStatus();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
};


/**
 * optional FirmwareImageVersion image_version = 1;
 * @return {?proto.dfu.FirmwareImageVersion}
 */
proto.dfu.DfuManifestStatusEntry.prototype.getImageVersion = function() {
  return /** @type{?proto.dfu.FirmwareImageVersion} */ (
    jspb.Message.getWrapperField(this, proto.dfu.FirmwareImageVersion, 1));
};


/**
 * @param {?proto.dfu.FirmwareImageVersion|undefined} value
 * @return {!proto.dfu.DfuManifestStatusEntry} returns this
*/
proto.dfu.DfuManifestStatusEntry.prototype.setImageVersion = function(value) {
  return jspb.Message.setWrapperField(this, 1, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.dfu.DfuManifestStatusEntry} returns this
 */
proto.dfu.DfuManifestStatusEntry.prototype.clearImageVersion = function() {
  return this.setImageVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.dfu.DfuManifestStatusEntry.prototype.hasImageVersion = function() {
  return jspb.Message.getField(this, 1) != null;
};


/**
 * optional EManifestEntryStatus entry_status = 2;
 * @return {!proto.dfu.EManifestEntryStatus}
 */
proto.dfu.DfuManifestStatusEntry.prototype.getEntryStatus = function() {
  return /** @type {!proto.dfu.EManifestEntryStatus} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.dfu.EManifestEntryStatus} value
 * @return {!proto.dfu.DfuManifestStatusEntry} returns this
 */
proto.dfu.DfuManifestStatusEntry.prototype.setEntryStatus = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.dfu.FirmwareImageVersion.prototype.toObject = function(opt_includeInstance) {
  return proto.dfu.FirmwareImageVersion.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.dfu.FirmwareImageVersion} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.FirmwareImageVersion.toObject = function(includeInstance, msg) {
  var f, obj = {
targetMcu: jspb.Message.getFieldWithDefault(msg, 1, 0),
imageType: jspb.Message.getFieldWithDefault(msg, 2, 0),
version: jspb.Message.getFieldWithDefault(msg, 3, ""),
versionBuild: jspb.Message.getFieldWithDefault(msg, 4, ""),
buildDate: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.dfu.FirmwareImageVersion}
 */
proto.dfu.FirmwareImageVersion.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.dfu.FirmwareImageVersion;
  return proto.dfu.FirmwareImageVersion.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.dfu.FirmwareImageVersion} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.dfu.FirmwareImageVersion}
 */
proto.dfu.FirmwareImageVersion.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EProcessorType} */ (reader.readEnum());
      msg.setTargetMcu(value);
      break;
    case 2:
      var value = /** @type {!proto.dfu.EImageType} */ (reader.readEnum());
      msg.setImageType(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setVersion(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setVersionBuild(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setBuildDate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.dfu.FirmwareImageVersion.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.dfu.FirmwareImageVersion.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.dfu.FirmwareImageVersion} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.FirmwareImageVersion.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTargetMcu();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getImageType();
  if (f !== 0.0) {
    writer.writeEnum(
      2,
      f
    );
  }
  f = message.getVersion();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getVersionBuild();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getBuildDate();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional EProcessorType target_mcu = 1;
 * @return {!proto.dfu.EProcessorType}
 */
proto.dfu.FirmwareImageVersion.prototype.getTargetMcu = function() {
  return /** @type {!proto.dfu.EProcessorType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EProcessorType} value
 * @return {!proto.dfu.FirmwareImageVersion} returns this
 */
proto.dfu.FirmwareImageVersion.prototype.setTargetMcu = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional EImageType image_type = 2;
 * @return {!proto.dfu.EImageType}
 */
proto.dfu.FirmwareImageVersion.prototype.getImageType = function() {
  return /** @type {!proto.dfu.EImageType} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {!proto.dfu.EImageType} value
 * @return {!proto.dfu.FirmwareImageVersion} returns this
 */
proto.dfu.FirmwareImageVersion.prototype.setImageType = function(value) {
  return jspb.Message.setProto3EnumField(this, 2, value);
};


/**
 * optional string version = 3;
 * @return {string}
 */
proto.dfu.FirmwareImageVersion.prototype.getVersion = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.dfu.FirmwareImageVersion} returns this
 */
proto.dfu.FirmwareImageVersion.prototype.setVersion = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string version_build = 4;
 * @return {string}
 */
proto.dfu.FirmwareImageVersion.prototype.getVersionBuild = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.dfu.FirmwareImageVersion} returns this
 */
proto.dfu.FirmwareImageVersion.prototype.setVersionBuild = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string build_date = 5;
 * @return {string}
 */
proto.dfu.FirmwareImageVersion.prototype.getBuildDate = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.dfu.FirmwareImageVersion} returns this
 */
proto.dfu.FirmwareImageVersion.prototype.setBuildDate = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.dfu.FirmwareVersionSimple.prototype.toObject = function(opt_includeInstance) {
  return proto.dfu.FirmwareVersionSimple.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.dfu.FirmwareVersionSimple} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.FirmwareVersionSimple.toObject = function(includeInstance, msg) {
  var f, obj = {
targetMcu: jspb.Message.getFieldWithDefault(msg, 1, 0),
version: (f = msg.getVersion()) && basic_pb.VersionStrThree.toObject(includeInstance, f),
buildDate: (f = msg.getBuildDate()) && basic_pb.DateStr.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.dfu.FirmwareVersionSimple}
 */
proto.dfu.FirmwareVersionSimple.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.dfu.FirmwareVersionSimple;
  return proto.dfu.FirmwareVersionSimple.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.dfu.FirmwareVersionSimple} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.dfu.FirmwareVersionSimple}
 */
proto.dfu.FirmwareVersionSimple.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.dfu.EProcessorType} */ (reader.readEnum());
      msg.setTargetMcu(value);
      break;
    case 3:
      var value = new basic_pb.VersionStrThree;
      reader.readMessage(value,basic_pb.VersionStrThree.deserializeBinaryFromReader);
      msg.setVersion(value);
      break;
    case 5:
      var value = new basic_pb.DateStr;
      reader.readMessage(value,basic_pb.DateStr.deserializeBinaryFromReader);
      msg.setBuildDate(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.dfu.FirmwareVersionSimple.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.dfu.FirmwareVersionSimple.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.dfu.FirmwareVersionSimple} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.dfu.FirmwareVersionSimple.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTargetMcu();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getVersion();
  if (f != null) {
    writer.writeMessage(
      3,
      f,
      basic_pb.VersionStrThree.serializeBinaryToWriter
    );
  }
  f = message.getBuildDate();
  if (f != null) {
    writer.writeMessage(
      5,
      f,
      basic_pb.DateStr.serializeBinaryToWriter
    );
  }
};


/**
 * optional EProcessorType target_mcu = 1;
 * @return {!proto.dfu.EProcessorType}
 */
proto.dfu.FirmwareVersionSimple.prototype.getTargetMcu = function() {
  return /** @type {!proto.dfu.EProcessorType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.dfu.EProcessorType} value
 * @return {!proto.dfu.FirmwareVersionSimple} returns this
 */
proto.dfu.FirmwareVersionSimple.prototype.setTargetMcu = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional basic.VersionStrThree version = 3;
 * @return {?proto.basic.VersionStrThree}
 */
proto.dfu.FirmwareVersionSimple.prototype.getVersion = function() {
  return /** @type{?proto.basic.VersionStrThree} */ (
    jspb.Message.getWrapperField(this, basic_pb.VersionStrThree, 3));
};


/**
 * @param {?proto.basic.VersionStrThree|undefined} value
 * @return {!proto.dfu.FirmwareVersionSimple} returns this
*/
proto.dfu.FirmwareVersionSimple.prototype.setVersion = function(value) {
  return jspb.Message.setWrapperField(this, 3, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.dfu.FirmwareVersionSimple} returns this
 */
proto.dfu.FirmwareVersionSimple.prototype.clearVersion = function() {
  return this.setVersion(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.dfu.FirmwareVersionSimple.prototype.hasVersion = function() {
  return jspb.Message.getField(this, 3) != null;
};


/**
 * optional basic.DateStr build_date = 5;
 * @return {?proto.basic.DateStr}
 */
proto.dfu.FirmwareVersionSimple.prototype.getBuildDate = function() {
  return /** @type{?proto.basic.DateStr} */ (
    jspb.Message.getWrapperField(this, basic_pb.DateStr, 5));
};


/**
 * @param {?proto.basic.DateStr|undefined} value
 * @return {!proto.dfu.FirmwareVersionSimple} returns this
*/
proto.dfu.FirmwareVersionSimple.prototype.setBuildDate = function(value) {
  return jspb.Message.setWrapperField(this, 5, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.dfu.FirmwareVersionSimple} returns this
 */
proto.dfu.FirmwareVersionSimple.prototype.clearBuildDate = function() {
  return this.setBuildDate(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.dfu.FirmwareVersionSimple.prototype.hasBuildDate = function() {
  return jspb.Message.getField(this, 5) != null;
};


/**
 * @enum {number}
 */
proto.dfu.EProcessorType = {
  PROCESSOR_UNSPECIFIED: 0,
  PROCESSOR_MAIN: 1,
  PROCESSOR_ISOLATED: 2,
  PROCESSOR_DISPLAY: 3,
  PROCESSOR_COMMS: 4,
  PROCESSOR_BLE: 5,
  PROCESSOR_PACKAGE: 6
};

/**
 * @enum {number}
 */
proto.dfu.EImageType = {
  IMAGE_UNSPECIFIED: 0,
  IMAGE_MCUBOOT: 1,
  IMAGE_APPLICATION: 2,
  IMAGE_SECURE_CODE: 3,
  IMAGE_PROTECTED_STORAGE: 4,
  IMAGE_BOOT_SECURE_DATA: 5
};

/**
 * @enum {number}
 */
proto.dfu.EFirmwareUpdateStatus = {
  FW_UPDATE_STATUS_UNSPECIFIED: 0,
  FW_UPDATE_STATUS_SUCCESS: 1,
  FW_UPDATE_STATUS_DOWNLOADING: 2,
  FW_UPDATE_STATUS_COPYING: 3,
  FW_UPDATE_STATUS_PENDING: 4,
  FW_UPDATE_STATUS_FAILED: 5,
  FW_UPDATE_STATUS_NOT_POSSIBLE_NOW: 6,
  FW_UPDATE_STATUS_FAILED_INVALID_PARAM: 7,
  FW_UPDATE_STATUS_FAILED_NO_SUPPORTED_MODEL: 8,
  FW_UPDATE_STATUS_FAILED_TIMEOUT: 9
};

/**
 * @enum {number}
 */
proto.dfu.EFirmwareVersionsManifest = {
  FW_MANIFEST_UNSPECIFIED: 0,
  FW_MANIFEST_PRESENT: 1,
  FW_MANIFEST_UPDATE: 2,
  FW_MANIFEST_NONE: 3
};

/**
 * @enum {number}
 */
proto.dfu.EManifestEntryStatus = {
  FW_IMAGE_STATUS_UNSPECIFIED: 0,
  FW_IMAGE_STATUS_NEEDS_UPDATE: 1,
  FW_IMAGE_STATUS_NO_UPDATE: 2,
  FW_IMAGE_STATUS_DOWNLOADED: 3,
  FW_IMAGE_STATUS_SUCCESS: 4,
  FW_IMAGE_STATUS_FAILED: 5,
  FW_IMAGE_STATUS_FAILED_INVALID_PARAM: 6,
  FW_IMAGE_STATUS_FAILED_IMAGE_INVALID: 7,
  FW_IMAGE_STATUS_FAILED_IMAGE_VERSION_INCOMPATIBLE: 8,
  FW_IMAGE_STATUS_PENDING_RESTART: 9
};

/**
 * @enum {number}
 */
proto.dfu.EDfuResultCode = {
  RESULT_UNSPECIFIED: 0,
  RESULT_SUCCESS: 1,
  RESULT_INVALID_PARAM: 2,
  RESULT_NOT_POSSIBLE_NOW: 3,
  RESULT_COMMAND_NOT_ACCEPTED: 4,
  RESULT_FAILED_AT_DEVICE: 5,
  RESULT_FAILED_UNAVAILABLE: 6,
  RESULT_FAILED_MISSING_PARAM: 7,
  RESULT_FAILED_OUT_OF_ORDER: 8,
  RESULT_FAILED_FILE_IO_ERROR: 9,
  RESULT_FAILED_COMM_ERROR: 10,
  RESULT_FAILED_NO_UPDATE_IN_PROGRESS: 11,
  RESULT_FAILED_PROCESSING_ERROR: 12
};

/**
 * @enum {number}
 */
proto.dfu.EMonitorModelNumber = {
  MODEL_NUMBER_UNSPECIFIED: 0,
  MODEL_NUMBER_MMU2_16LEIP_RM_SF: 1,
  MODEL_NUMBER_DK_NUCLEO_MMU: 2
};

goog.object.extend(exports, proto.dfu);
