// source: rushhour/v1/envelopeType.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

goog.exportSymbol('proto.rushhour.v1.EnvelopeType', null, global);
/**
 * @enum {number}
 */
proto.rushhour.v1.EnvelopeType = {
  ENVELOPE_UNKNOWN: 0,
  ENVELOPE_WRAPPER_COMMAND: 1,
  ENVELOPE_WRAPPER_RESPONSE: 2,
  ENVELOPE_COMMAND_JSON: 3,
  ENVELOPE_COMMAND_PROTOBUF: 4
};

goog.object.extend(exports, proto.rushhour.v1);
