// source: rushhour/v1/socketEnvelope.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var rushhour_v1_envelopeType_pb = require('../../rushhour/v1/envelopeType_pb.js');
goog.object.extend(proto, rushhour_v1_envelopeType_pb);
var rushhour_v1_originType_pb = require('../../rushhour/v1/originType_pb.js');
goog.object.extend(proto, rushhour_v1_originType_pb);
goog.exportSymbol('proto.rushhour.v1.SocketEnvelope', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.rushhour.v1.SocketEnvelope = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.rushhour.v1.SocketEnvelope, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.rushhour.v1.SocketEnvelope.displayName = 'proto.rushhour.v1.SocketEnvelope';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.rushhour.v1.SocketEnvelope.prototype.toObject = function(opt_includeInstance) {
  return proto.rushhour.v1.SocketEnvelope.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.rushhour.v1.SocketEnvelope} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.rushhour.v1.SocketEnvelope.toObject = function(includeInstance, msg) {
  var f, obj = {
type: jspb.Message.getFieldWithDefault(msg, 1, 0),
requestId: jspb.Message.getFieldWithDefault(msg, 2, 0),
sessionId: jspb.Message.getFieldWithDefault(msg, 3, ""),
userId: jspb.Message.getFieldWithDefault(msg, 4, ""),
deviceId: jspb.Message.getFieldWithDefault(msg, 5, ""),
organizationId: jspb.Message.getFieldWithDefault(msg, 6, ""),
origin: jspb.Message.getFieldWithDefault(msg, 7, 0),
payload: msg.getPayload_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.rushhour.v1.SocketEnvelope}
 */
proto.rushhour.v1.SocketEnvelope.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.rushhour.v1.SocketEnvelope;
  return proto.rushhour.v1.SocketEnvelope.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.rushhour.v1.SocketEnvelope} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.rushhour.v1.SocketEnvelope}
 */
proto.rushhour.v1.SocketEnvelope.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {!proto.rushhour.v1.EnvelopeType} */ (reader.readEnum());
      msg.setType(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readUint32());
      msg.setRequestId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSessionId(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setUserId(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeviceId(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setOrganizationId(value);
      break;
    case 7:
      var value = /** @type {!proto.rushhour.v1.OriginType} */ (reader.readEnum());
      msg.setOrigin(value);
      break;
    case 8:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setPayload(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.rushhour.v1.SocketEnvelope.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.rushhour.v1.SocketEnvelope.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.rushhour.v1.SocketEnvelope} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.rushhour.v1.SocketEnvelope.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getType();
  if (f !== 0.0) {
    writer.writeEnum(
      1,
      f
    );
  }
  f = message.getRequestId();
  if (f !== 0) {
    writer.writeUint32(
      2,
      f
    );
  }
  f = message.getSessionId();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getUserId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDeviceId();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getOrganizationId();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getOrigin();
  if (f !== 0.0) {
    writer.writeEnum(
      7,
      f
    );
  }
  f = message.getPayload_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      8,
      f
    );
  }
};


/**
 * optional EnvelopeType type = 1;
 * @return {!proto.rushhour.v1.EnvelopeType}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getType = function() {
  return /** @type {!proto.rushhour.v1.EnvelopeType} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {!proto.rushhour.v1.EnvelopeType} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setType = function(value) {
  return jspb.Message.setProto3EnumField(this, 1, value);
};


/**
 * optional uint32 request_id = 2;
 * @return {number}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getRequestId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setRequestId = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string session_id = 3;
 * @return {string}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getSessionId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setSessionId = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string user_id = 4;
 * @return {string}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getUserId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setUserId = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string device_id = 5;
 * @return {string}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getDeviceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setDeviceId = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string organization_id = 6;
 * @return {string}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getOrganizationId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setOrganizationId = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional OriginType origin = 7;
 * @return {!proto.rushhour.v1.OriginType}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getOrigin = function() {
  return /** @type {!proto.rushhour.v1.OriginType} */ (jspb.Message.getFieldWithDefault(this, 7, 0));
};


/**
 * @param {!proto.rushhour.v1.OriginType} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setOrigin = function(value) {
  return jspb.Message.setProto3EnumField(this, 7, value);
};


/**
 * optional bytes payload = 8;
 * @return {string}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getPayload = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * optional bytes payload = 8;
 * This is a type-conversion wrapper around `getPayload()`
 * @return {string}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getPayload_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getPayload()));
};


/**
 * optional bytes payload = 8;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getPayload()`
 * @return {!Uint8Array}
 */
proto.rushhour.v1.SocketEnvelope.prototype.getPayload_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getPayload()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.rushhour.v1.SocketEnvelope} returns this
 */
proto.rushhour.v1.SocketEnvelope.prototype.setPayload = function(value) {
  return jspb.Message.setProto3BytesField(this, 8, value);
};


goog.object.extend(exports, proto.rushhour.v1);
