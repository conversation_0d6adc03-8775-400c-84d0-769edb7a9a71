// source: rushhour/v1/originType.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

goog.exportSymbol('proto.rushhour.v1.OriginType', null, global);
/**
 * @enum {number}
 */
proto.rushhour.v1.OriginType = {
  ORIGIN_UNKNOWN: 0,
  ORIGIN_GATEWAY: 1,
  ORIGIN_FSA: 2,
  ORIGIN_RUSHHOUR: 3,
  ORIGIN_ONRAMP: 4
};

goog.object.extend(exports, proto.rushhour.v1);
