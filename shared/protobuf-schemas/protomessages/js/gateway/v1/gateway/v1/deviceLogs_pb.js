// source: gateway/v1/deviceLogs.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    (function () { return this; }).call(null) ||
    Function('return this')();

var gateway_v1_deviceType_pb = require('../../gateway/v1/deviceType_pb.js');
goog.object.extend(proto, gateway_v1_deviceType_pb);
goog.exportSymbol('proto.gateway.v1.DeviceLogs', null, global);
goog.exportSymbol('proto.gateway.v1.LogEntry', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.gateway.v1.DeviceLogs = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.gateway.v1.DeviceLogs.repeatedFields_, null);
};
goog.inherits(proto.gateway.v1.DeviceLogs, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.gateway.v1.DeviceLogs.displayName = 'proto.gateway.v1.DeviceLogs';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.gateway.v1.LogEntry = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.gateway.v1.LogEntry.repeatedFields_, null);
};
goog.inherits(proto.gateway.v1.LogEntry, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.gateway.v1.LogEntry.displayName = 'proto.gateway.v1.LogEntry';
}

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.gateway.v1.DeviceLogs.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.gateway.v1.DeviceLogs.prototype.toObject = function(opt_includeInstance) {
  return proto.gateway.v1.DeviceLogs.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.gateway.v1.DeviceLogs} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.gateway.v1.DeviceLogs.toObject = function(includeInstance, msg) {
  var f, obj = {
deviceId: jspb.Message.getFieldWithDefault(msg, 1, ""),
logsList: jspb.Message.toObjectList(msg.getLogsList(),
    proto.gateway.v1.LogEntry.toObject, includeInstance),
deviceType: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.gateway.v1.DeviceLogs}
 */
proto.gateway.v1.DeviceLogs.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.gateway.v1.DeviceLogs;
  return proto.gateway.v1.DeviceLogs.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.gateway.v1.DeviceLogs} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.gateway.v1.DeviceLogs}
 */
proto.gateway.v1.DeviceLogs.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeviceId(value);
      break;
    case 2:
      var value = new proto.gateway.v1.LogEntry;
      reader.readMessage(value,proto.gateway.v1.LogEntry.deserializeBinaryFromReader);
      msg.addLogs(value);
      break;
    case 3:
      var value = /** @type {!proto.gateway.v1.DeviceType} */ (reader.readEnum());
      msg.setDeviceType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.gateway.v1.DeviceLogs.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.gateway.v1.DeviceLogs.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.gateway.v1.DeviceLogs} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.gateway.v1.DeviceLogs.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDeviceId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLogsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.gateway.v1.LogEntry.serializeBinaryToWriter
    );
  }
  f = message.getDeviceType();
  if (f !== 0.0) {
    writer.writeEnum(
      3,
      f
    );
  }
};


/**
 * optional string device_id = 1;
 * @return {string}
 */
proto.gateway.v1.DeviceLogs.prototype.getDeviceId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.gateway.v1.DeviceLogs} returns this
 */
proto.gateway.v1.DeviceLogs.prototype.setDeviceId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated LogEntry logs = 2;
 * @return {!Array<!proto.gateway.v1.LogEntry>}
 */
proto.gateway.v1.DeviceLogs.prototype.getLogsList = function() {
  return /** @type{!Array<!proto.gateway.v1.LogEntry>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.gateway.v1.LogEntry, 2));
};


/**
 * @param {!Array<!proto.gateway.v1.LogEntry>} value
 * @return {!proto.gateway.v1.DeviceLogs} returns this
*/
proto.gateway.v1.DeviceLogs.prototype.setLogsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.gateway.v1.LogEntry=} opt_value
 * @param {number=} opt_index
 * @return {!proto.gateway.v1.LogEntry}
 */
proto.gateway.v1.DeviceLogs.prototype.addLogs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.gateway.v1.LogEntry, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.gateway.v1.DeviceLogs} returns this
 */
proto.gateway.v1.DeviceLogs.prototype.clearLogsList = function() {
  return this.setLogsList([]);
};


/**
 * optional DeviceType device_type = 3;
 * @return {!proto.gateway.v1.DeviceType}
 */
proto.gateway.v1.DeviceLogs.prototype.getDeviceType = function() {
  return /** @type {!proto.gateway.v1.DeviceType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {!proto.gateway.v1.DeviceType} value
 * @return {!proto.gateway.v1.DeviceLogs} returns this
 */
proto.gateway.v1.DeviceLogs.prototype.setDeviceType = function(value) {
  return jspb.Message.setProto3EnumField(this, 3, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.gateway.v1.LogEntry.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.gateway.v1.LogEntry.prototype.toObject = function(opt_includeInstance) {
  return proto.gateway.v1.LogEntry.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.gateway.v1.LogEntry} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.gateway.v1.LogEntry.toObject = function(includeInstance, msg) {
  var f, obj = {
logType: jspb.Message.getFieldWithDefault(msg, 1, ""),
messageList: msg.getMessageList_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.gateway.v1.LogEntry}
 */
proto.gateway.v1.LogEntry.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.gateway.v1.LogEntry;
  return proto.gateway.v1.LogEntry.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.gateway.v1.LogEntry} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.gateway.v1.LogEntry}
 */
proto.gateway.v1.LogEntry.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setLogType(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.addMessage(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.gateway.v1.LogEntry.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.gateway.v1.LogEntry.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.gateway.v1.LogEntry} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.gateway.v1.LogEntry.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLogType();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getMessageList_asU8();
  if (f.length > 0) {
    writer.writeRepeatedBytes(
      2,
      f
    );
  }
};


/**
 * optional string log_type = 1;
 * @return {string}
 */
proto.gateway.v1.LogEntry.prototype.getLogType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.gateway.v1.LogEntry} returns this
 */
proto.gateway.v1.LogEntry.prototype.setLogType = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated bytes message = 2;
 * @return {!Array<string>}
 */
proto.gateway.v1.LogEntry.prototype.getMessageList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * repeated bytes message = 2;
 * This is a type-conversion wrapper around `getMessageList()`
 * @return {!Array<string>}
 */
proto.gateway.v1.LogEntry.prototype.getMessageList_asB64 = function() {
  return /** @type {!Array<string>} */ (jspb.Message.bytesListAsB64(
      this.getMessageList()));
};


/**
 * repeated bytes message = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getMessageList()`
 * @return {!Array<!Uint8Array>}
 */
proto.gateway.v1.LogEntry.prototype.getMessageList_asU8 = function() {
  return /** @type {!Array<!Uint8Array>} */ (jspb.Message.bytesListAsU8(
      this.getMessageList()));
};


/**
 * @param {!(Array<!Uint8Array>|Array<string>)} value
 * @return {!proto.gateway.v1.LogEntry} returns this
 */
proto.gateway.v1.LogEntry.prototype.setMessageList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {!(string|Uint8Array)} value
 * @param {number=} opt_index
 * @return {!proto.gateway.v1.LogEntry} returns this
 */
proto.gateway.v1.LogEntry.prototype.addMessage = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.gateway.v1.LogEntry} returns this
 */
proto.gateway.v1.LogEntry.prototype.clearMessageList = function() {
  return this.setMessageList([]);
};


goog.object.extend(exports, proto.gateway.v1);
