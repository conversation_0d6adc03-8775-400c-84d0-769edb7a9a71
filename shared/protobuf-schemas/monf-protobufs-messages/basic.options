 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   BASIC
#   nanopb options file for basic.proto


# VersionStrThree
#   Max string lengths, allow up to 4-digit versions
basic.VersionStrThree.major      max_size:5
basic.VersionStrThree.minor      max_size:5
basic.VersionStrThree.revision   max_size:5

# DateStr
#   Max string lengths, allow up 2 character months, 2 char days, and 4 char years
basic.DateStr.month      max_size:3
basic.DateStr.day        max_size:3
basic.DateStr.year       max_size:5

# ModelAndSerialNumber
#   Max string fixed lengths per ATC Standard, used for future compatability
basic.ModelAndSerialNumber.serial   max_size:21
basic.ModelAndSerialNumber.model    max_size:31

# FaultHeaderLogEntryMmu
#   Max string fixed lengths per ATC Standard, used for future compatability
basic.MonitorAndUserIds.monitor      max_size:41
basic.MonitorAndUserIds.user         max_size:41
