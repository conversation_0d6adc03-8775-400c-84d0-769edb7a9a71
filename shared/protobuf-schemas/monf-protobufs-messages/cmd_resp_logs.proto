/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
*/

/*  CMD_RESP_LOGS
    Command (from app) and response (from monitor) message formats for logging related commands.
*/
syntax = "proto3";
package cmd_resp_logs;

//***************************************************************************************** IMPORTS
import "mon_logs.proto";
import "audit_logs.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  CmdRequestLogCounts is used to request the number of entries in monitor logs */
message CmdRequestLogCounts {

    // The monitor log type(s) to request.  May be MON_LOG_ALL to get entry counts for all logs
    repeated mon_logs.EMonitorLogType log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // CmdRequestLogCounts


/*  RespRequestLogCounts returns the entry counts for the requested logs. The format and size of
    a single log entry in the monitor log storage is also returned.*/
message RespRequestLogCounts {

    // A field repeat for each log in the command request.  If the command request was for
    // MON_LOG_ALL, then there is a repeated field for every available log.
    //
    // NOTE: for MON_LOG_FAULT_MEASUREMENT, MON_LOG_FAULT_SEQUENCE, & MON_LOG_FAULT_FACTS the entry 
    //       count is the maximum number of entries that may be returned for any particular Fault ID.
    repeated mon_logs.LogEntryCount log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // RespRequestLogCounts

//-------------------------------------------------------------------------------------------------

/*  CmdRequestLogClear is used to clear one or more monitor logs */
message CmdRequestLogClear {

    // The monitor log type(s) to clear.  May be MON_LOG_ALL to clear all logs.
    //
    // NOTE: Clearing the MON_LOG_FAULT_HEADER log will also clear the MON_LOG_FAULT_MEASUREMENT, 
    //       MON_LOG_FAULT_SEQUENCE, and MON_LOG_FAULT_FACTS logs.  Attempting to clear the 
    //       MON_LOG_FAULT_MEASUREMENT, MON_LOG_FAULT_SEQUENCE, or MON_LOG_FAULT_FACTS logs will 
    //       have no effect and return an error.
    repeated mon_logs.EMonitorLogType log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // CmdRequestLogClear


/*  RespRequestLogClear returns the cleared entry counts for the cleared logs. The format and size
    values are not used in this response. (Always 0)*/
message RespRequestLogClear {

    // A field repeat for each log in the command request.  If the command request was for
    // MON_LOG_ALL, then there is a repeated field for every available log.
    repeated mon_logs.LogEntryCount log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // RespRequestLogClear

//-------------------------------------------------------------------------------------------------

/*  CmdRequestLogEntries is used to request entries from a monitor log 
    NOTE: large log requests may be returned in multiple response messages. */
message CmdRequestLogEntries {

    // The monitor log to request entries from.  Entries may only be request from one log at a time.
    // A request for MON_LOG_ALL is illegal and will return an error in the response.
    mon_logs.EMonitorLogType log = 1;   

    // For log types MON_LOG_FAULT_MEASUREMENT, MON_LOG_FAULT_SEQUENCE, or MON_LOG_FAULT_FACTS
    // the associated fault_id is required.
    optional uint32 fault_id = 2;

    // The log entry ID at which to start retrieving entries. The first/oldest entry is value '1'. HOWEVER,
    // if the log has become full and starts deleting older entries, they are not renumbered.  Thus, entry
    // 1 (and up to the oldest undeleted entry) may not exist.
    uint32 starting_entry_id = 3;

    // The number of entries to return, beginning with the starting entry ID
    uint32 entry_return_count = 4;

} // CmdRequestLogEntries


/*  RespRequestLogEntries returns the requested log entries.
    NOTE: Large logs and/or large entry count requests may be returned in more than 1 response message.
          All response messages to a single CmdRequestLogEntries will have the same request_id.  
          Fewer entries than requested in CmdRequestLogEntries.entry_return_count may be returned if
          more entries were requested than exist in the log.  */
message RespRequestLogEntries {

    // The monitor log from which the entries were requested.
    mon_logs.EMonitorLogType log = 1;   

    // The total number of messages that comprise the response.  If all the requested entries fit in single message
    // this value will be 1
    uint32 total_message_count = 2;

    // The message sequence count.  This is the number of the response message out of the total_message_count
    // The first response message will have value 1 and the last message value will equal total_message_count
    uint32 message_sequence_count = 3;

    // The log entries.  Each of these messages contains a repeated field of the log entry message.
    // The total number of messages for a response and the number of entries in each message are dependant on the
    // size of the requested log entry, and the number of requested entries.
    // A request for a handful of entries from a log with large entries, like the config log, may be returned in
    // several response messages.  However, a request for dozens of entries from a small log, like the clock log,
    // may all be returned in a single response message.
    oneof entries {

        mon_logs.PowerLogMultipleEntriesMmu power_log = 4;
        mon_logs.ResetLogMultipleEntriesMmu reset_log = 5;
        mon_logs.ClockLogMultipleEntriesMmu clock_log = 6;
        mon_logs.ConfigLogMultipleEntriesMmu config_log = 7;
        mon_logs.Port1LogLogMultipleEntriesMmu port1_log = 8;
        mon_logs.FaultHeaderLogMultipleEntriesMmu fault_hdr_log = 9;
        mon_logs.FaultMeasurementLogMultipleEntriesMmu fault_msr_log = 10;
        mon_logs.FaultSequenceLogMultipleEntriesMmu fault_seq_log = 11;
        mon_logs.FaultFactsLogMultipleEntriesMmu fault_facts_log = 12;
        mon_logs.AlarmLogMultipleEntriesMmu alarm_log = 13;

    }

} // RespRequestLogEntries

//-------------------------------------------------------------------------------------------------

/*  CmdRequestAuditLogCounts is used to request the number of entries in audit logs, as well as
    the log max length setting. */
message CmdRequestAuditLogCounts {

    // The audit log type(s) to request.
    repeated audit_logs.EAuditLogType log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // CmdRequestAuditLogCounts


/*  RespRequestAuditLogCounts returns the entry counts for the requested logs.*/
message RespRequestAuditLogCounts {

    // If true, then the audit logs have reached the maximum allowed file system usage for logfiles.
    // Linear logs will be replacing the oldest entries with any new entries, and circular logs will
    // be limited to their current size.
    bool logs_space_limited = 2;

    // A field repeat for each log in the command request.
    repeated audit_logs.AuditLogEntryCount log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // RespRequestAuditLogCounts

//-------------------------------------------------------------------------------------------------

/*  CmdRequestAuditLogClear is used to clear one or more audit logs */
message CmdRequestAuditLogClear {

    // The audit log type(s) to clear. 
    repeated audit_logs.EAuditLogType log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // CmdRequestAuditLogClear


/*  RespRequestAuditLogClear returns the list of cleared logs. 
    Use CmdRequestAuditLogCounts to confirm the cleared logs */
message RespRequestAuditLogClear {

    // A field repeat for each log in the command request. 
    repeated audit_logs.EAuditLogType log = 1;    // Max repeat count set in cmd_resp_logs.options 

} // RespRequestAuditLogClear


//-------------------------------------------------------------------------------------------------

/*  CmdRequestAuditLogReset is used to clear one audit log and change the log maximum length setting.
    Use CmdRequestAuditLogCounts to confirm the reset log */
message CmdRequestAuditLogReset {

    // The audit log type to reset. 
    audit_logs.EAuditLogType log = 1;

    // The maximum number of entries to limit the log to.  If set to `0` then the log length is limited only
    // by file system space.
    // NOTE: The firmware has a minimum length increment (entries per file), and the requested max_entries
    //       log length will be rounded up to the nearest multiple of entries_per_file.    
    uint32 max_entries = 2;

} // CmdRequestAuditLogReset


/*  RespRequestAuditLogReset returns the reset log. */
message RespRequestAuditLogReset {

    // Echoes back the reset log.
    audit_logs.EAuditLogType log = 1;

} // RespRequestAuditLogReset

//-------------------------------------------------------------------------------------------------

/*  CmdRequestAuditLogEntries is used to request entries from an audit log 
    NOTE: large log requests may be returned in multiple response messages. */
message CmdRequestAuditLogEntries {

    // The audit log to request entries from.  Entries may only be request from one log at a time.
    audit_logs.EAuditLogType log = 1;   

    // The log entry index at which to start retrieving entries.  The first entry is value '1'.
    uint32 starting_entry_index = 2;

    // The number of entries to return, beginning with the starting entry index
    /* NOTE: Requesting a large number of entries (2000+ although poor network conditions may reduce that)
             may cause a timeout in the MMU due to the network interface getting backed up.  A combination
             of the network throughput between the MMU and PC App, and how quickly the PC App is able to 
             receive and process responses contribute to this.  If the PC App is getting 
             WrapperResponse.code = RESP_TIMEOUT errors during log reads, it should use more log read commands
             requesting fewer entries per command to complete a log read. */
    uint32 entry_return_count = 3;

} // CmdRequestAuditLogEntries


/*  RespRequestAuditLogEntries returns the requested log entries.
    NOTE: Large logs and/or large entry count requests may be returned in more than 1 response message.
          All response messages to a single CmdRequestAuditLogEntries will have the same wrapper request_id.  
          Fewer entries than requested in CmdRequestAuditLogEntries.entry_return_count may be returned if
          more entries were requested than exist in the log.  */
message RespRequestAuditLogEntries {

    // The audit log from which the entries were requested.
    audit_logs.EAuditLogType log = 1;   

    // The total number of messages that comprise the response.  If all the requested entries fit in single message
    // this value will be 1
    uint32 total_message_count = 2;

    // The message sequence count.  This is the number of the response message out of the total_message_count
    // The first response message will have value 1 and the last message value will equal total_message_count
    uint32 message_sequence_count = 3;

    // The log entries.  Each of these messages contains a repeated field of the log entry message.
    // The total number of messages for a response and the number of entries in each message are dependant on the
    // size of the requested log entry, and the number of requested entries.
    oneof entries {
        // in case there is ever another log format.
        audit_logs.AuditLogMultipleEntriesComms format1 = 4;
    }

} // RespRequestAuditLogEntries
