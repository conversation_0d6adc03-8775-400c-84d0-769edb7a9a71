/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  CMD_RESP_REALTIME
    Commands and responses for managing realtime data, and message formats for sending realtime data
*/
syntax = "proto3";
package cmd_resp_realtime;

//***************************************************************************************** IMPORTS
import "realtime.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  CmdStartRealtimeData enables publishing of real-time data messages with the selected data at
    the selected interval.
    This command may be sent while real-time data is already enabled to change the selected data
    and/or intervals.  This command may also stop real-time data by setting channel_status_chmap = 0
    and all "bool" fields to false, and/or sending send_interval_ms = 0 */
message CmdStartRealtimeData {

    // If this field is non-zero, then a "RealtimeData1" message will be sent with the field "channel_status",
    // which will have measured values for the selected channels.  Channels are selected by setting the 
    // bit in this field for the channel.
    // The value is a bitmap of the channels for which channel data will be sent.  ('1' bit = enabled) LSbit = Ch.1
    fixed32 send_channel_status_chmap = 1; 

    // Fault information, general status, and monitored input voltages will be sent if true. 
    // The data is sent in a "RealtimeData1" message field "monitor_fault_and_status"
    bool send_monitor_fault_and_status = 2; 

    // The monitor panel LED states and LCD display text will be sent if true.  This will be sent in a 
    // "RealtimeDisplay1" message field "monitor_display"
    bool send_monitor_display = 3;

    // If true, then after the first message only display text lines that have changed will be sent,
    // though the update_full_display_data_seconds will be used subsequently to determine when and if
    // the full display data is sent again.
    // This can reduce the total realtime data significantly.
    // If false, the full display data is always sent when send_monitor_display is true.
    bool monitor_display_changed_only = 4;

    // The interval at which to send the real-time data in milliseconds.  The value will be rounded
    // up to the next multiple of 100 ms.  A value greater than 10,000 (10 seconds) will be set as
    // 10,000.  A value of 0 will be rounded up to 100.
    uint32 send_interval_ms = 5;
    
    // If monitor_display_changed_only is true, this is the number of seconds between updates of the full display data.
    // If 0 when monitor_display_changed_only is true, then the full display data is only sent once.
    uint32 update_full_display_data_seconds = 6;

} // CmdStartRealtimeData


/*  RespStartRealtimeData - confirms the enable or update of real-time data */
message RespStartRealtimeData  {

    // The actual real-time send interval (after rounding) that is used.  If 0, then real-time
    // data was stopped.
    uint32 send_interval_ms = 1; 

} // RespStartRealtimeData

//-------------------------------------------------------------------------------------------------

/*  CmdStopRealtimeData will stop any real-time data being sent */
message CmdStopRealtimeData {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdStopRealtimeData


/*  RespStopRealtimeData - confirms the enable or update of real-time data */
message RespStopRealtimeData  {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // RespStopRealtimeData

//-------------------------------------------------------------------------------------------------


/*  RealtimeData1 - This message is used to send real-time operational data to the application */
message RealtimeData1  {

    // Realtime data message sequence number.  Increments for each message (of this type) sent. 
    // It may also be reset to 1 at any time.
    // May be used by the App to determine if it is missing messages.
    uint32 sequence_number = 1;  

    // provides the indicator on/off and field checks for all channels, and voltages
    // and timers for desired channels.
    optional realtime.ChannelStatusData channel_status = 2; 

    // provides fault, status, and monitored inputs
    optional realtime.MonitorPresentStatus monitor_fault_and_status = 3;

} // RealtimeData1


/*  RealtimeDisplay1 - This message is used to send real-time display info to the application */
message RealtimeDisplay1  {

    // Realtime display message sequence number.  Increments for each message (of this type) sent.
    // It may also be reset to 1 at any time.
    // May be used by the App to determine if it is missing messages.
    uint32 sequence_number = 1;      

    // provides the panel indicator displays for all channels and the display text. 
    realtime.MonitorDisplayData monitor_display = 2;

} // RealtimeDisplay1

