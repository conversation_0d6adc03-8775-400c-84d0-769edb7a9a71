 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   CMD_RESP_CONFIG
#   nanopb options file for cmd_resp_config.proto

# RespReadDataKey
#   Max data length
cmd_resp_config.RespReadDataKey.data_key_data           max_size:512

# CmdWriteDataKey
#   Max data length
cmd_resp_config.CmdWriteDataKey.data_key_data           max_size:512

# RespReadMonitorData
#   Max string lengths & repeated field counts 
#   Length for monitor_id and user_id are fixed per ATC Standard, used for future compatability
cmd_resp_config.RespReadMonitorData.monitor_id          max_size:41
cmd_resp_config.RespReadMonitorData.user_id             max_size:41
cmd_resp_config.RespReadMonitorData.fw_versions         max_count:8

# CmdReadPerChannelConfiguration
#   Max repeated field counts 
cmd_resp_config.CmdReadPerChannelConfiguration.channel          max_count:32

# RespReadPerChannelConfiguration
#   Max repeated field counts 
cmd_resp_config.RespReadPerChannelConfiguration.ch_settings     max_count:32

# CmdReadPerChannelCurrentSenseSettings
#   Max repeated field counts 
cmd_resp_config.CmdReadPerChannelCurrentSenseSettings.channel   max_count:32

# RespReadPerChannelCurrentSenseSettings
#   Max repeated field counts 
cmd_resp_config.RespReadPerChannelCurrentSenseSettings.ch_settings  max_count:32

# CmdReadPerChannelPermissiveSettings
#   Max repeated field counts 
cmd_resp_config.CmdReadPerChannelPermissiveSettings.channel   max_count:32

# RespReadPerChannelPermissiveSettings
#   Max repeated field counts 
cmd_resp_config.RespReadPerChannelPermissiveSettings.ch_settings  max_count:32

# RespReadFlashingYellowArrowConfiguration
#   Max repeated field counts 
cmd_resp_config.RespReadFlashingYellowArrowConfiguration.fya_settings    max_count:4
