/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  SETTINGS
    This file contains device settings and response codes not part of the logging system.
*/
syntax = "proto3";
package settings;

//***************************************************************************************** IMPORTS
import "basic.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  NetworkSettings - common network settings */
message NetworkSettings  {

    // True when the monitor expects its IP Address to be assigned by a DHCP server
    // False when IP address is statically assigned.
    bool dhcp_client_enabled = 1;

    // True if the monitor will act as a DHCP server (always disabled in current implementation)
    bool dhcp_server_enabled = 2;

    // The Ethernet hostname assigned to the monitor
    string ethernet_hostname = 3;       // Max string length set in settings.options 

    // The monitor's Ethernet IP Address
    basic.IpAddressV4 monitor_ipv4_address = 4;

    // The monitor's Ethernet Subnet Mask
    basic.IpAddressV4 monitor_ipv4_subnet = 5;

    // The monitor's Ethernet Gateway Address
    basic.IpAddressV4 monitor_ipv4_gateway = 6;

} // NetworkSettings


/*  FactorySettings - monitor settings for production */
message FactorySettings {

    // monitor serial number & model number
    basic.ModelAndSerialNumber numbers = 1;

    // manufacturer's device description
    string device_description = 2;          // Max string length set in settings.options  

    // Manufacture date
    basic.DateStr manufacture_date = 3;

    // The Main Processor PCB Option Jumpers in the monitor
    PcbOptionsMmu pcb_options = 4; 

    // The Factory options of the monitor. (32-bit value)
    FactoryOptionsMmu factory_options = 5;

} // FactorySettings


/*  UserOptionsBitmap - This message matches the options in the Main processor command 0x24 Read User Settings
    bitmap field "User Options" */
message UserOptionsBitmap {

    // TBD
    bool option_1 = 1;  

} // UserOptionsBitmap


/*  ENUM EStatusDisplay possible status displays . */
enum EStatusDisplay {

    STATUS_DISPLAY_UNSPECIFIED = 0;
    STATUS_DISPLAY_MAIN = 1;
    STATUS_DISPLAY_FAULT = 2;
    STATUS_DISPLAY_ETHERNET = 3;
    STATUS_DISPLAY_POWER = 4;
    STATUS_DISPLAY_FW_VERSIONS = 5;

} // enum EStatusDisplay

/*  UserSettings - System user settings. */
message UserSettings {

    // User settings options
    UserOptionsBitmap user_options = 1;   

    // The number of minutes from last panel button press until display is turned off
    uint32 sleep_time_minutes = 2;

    // Display dimming level (0 = full brightness, 255 = off) while on 
    uint32 display_dimming = 3;

    // The last selected status display 
    EStatusDisplay last_status_display = 4;

} // UserSettings

/*  ENUM EDisableOverrideState possible Disable Override states. 
 * There are 3 states for the Port 1 Disable Override:
 *  - No Override is being used
 *  - The Override to disable Port 1 is being forced ON - used in ATSI testing and only temporarily in the field.
 *  - The Override to disable Port 1 is being forced OFF - ie, Port 1 is not disabled.
 */
enum EDisableOverrideState {

    DISABLE_OVERRIDE_STATE_UNSPECIFIED = 0;
    DISABLE_OVERRIDE_STATE_NONE = 1;
    DISABLE_OVERRIDE_STATE_FORCE_ON = 2;
    DISABLE_OVERRIDE_STATE_FORCE_OFF = 3;

} // enum EDisableOverrideState


/*  PerChannelSettings - settings per channel   */
message PerChannelSettings  {

    // channel 1-32 that the settings apply to
    uint32 channel = 1;

    // whether the channel is enabled
    bool enabled = 2;

    // Bitmap of permissive channels, LSbit = Ch.1 
    fixed32 permissives_chmap = 3;

    // Green-Yellow multiple indication
    bool multiple_indication_grn_yel = 4;

    // Yellow-Red multiple indication
    bool multiple_indication_yel_red = 5;    

    // Green-Red multiple indication
    bool multiple_indication_grn_red = 6;

    // Lack of indication signal (dark)
    bool lack_of_signal = 7;

    // Yellow is not monitored for this channel (typ for ped channel)
    // If enabled, yellow must be on within 100 ms of green channel off
    bool yellow_disable = 8;

    // Checks that yellow is on for minimum time (2.7s)
    bool min_yellow_change_enable = 9;

    // Checks that conflicting green is not turned on within minimum (2.7s) time of channel green off.
    bool min_yellow_plus_red_enable = 10;

    // Controller - monitor load switch state mismatch
    bool field_check_red = 11;

    // Controller - monitor load switch state mismatchs
    bool field_check_yellow = 12;

    // Controller - monitor load switch state mismatch
    bool field_check_green = 13;

    // Indication load switch current sense is low
    bool current_sense_red = 14;

    // Indication load switch current sense is low
    bool current_sense_yellow= 15;

    // Indication load switch current sense is low
    bool current_sense_green= 16;

    // Current threshold for detection indication on in mA, Red
    uint32 red_on_threshold_current_ma = 17;

    // Current threshold for detection indication on in mA, Yellow
    uint32 yellow_on_threshold_current_ma = 18;

    // Current threshold for detection indication on in mA, Green
    uint32 green_on_threshold_current_ma = 19;

} // PerChannelSettings


/*  PerChannelCurrentSenseSettings - settings per channel   */
message PerChannelCurrentSenseSettings  {

    // channel 1-32 that the values apply to
    uint32 channel = 1;

    // red current threshold for ON indication, in mA
    uint32 red_on_threshold_ma = 2;

    // yellow current threshold for ON indication on, in mA
    uint32 yellow_on_threshold_ma = 3;

    // green current threshold for ON indication on, in mA
    uint32 green_on_threshold_ma = 4;

    // Walk current threshold for ON indication on, in mA
    optional uint32 walk_on_threshold_ma = 5;    

} // PerChannelCurrentSenseSettings


/*  PerChannelPermissives - settings per channel   */
message PerChannelPermissives  {

    // channel 1-32 that the value applies to
    uint32 channel = 1;

    // bitmap of channel permissives.  LSbit = Ch.1
    fixed32 permissives_chmap = 2;

} // PerChannelPermissives


/*  ENUM EFlashingYellowArrowMode possible Flashing Yellow Arrow modes . */
enum EFlashingYellowArrowMode {

    FYA_MODE_UNSPECIFIED = 0;
    FYA_MODE_A = 1;
    FYA_MODE_B = 2;
    FYA_MODE_C = 3;
    FYA_MODE_D = 4;
    FYA_MODE_E = 5;
    FYA_MODE_F = 6;
    FYA_MODE_G = 7;
    FYA_MODE_H = 8;
    FYA_MODE_I = 9;
    FYA_MODE_J = 10;
    FYA_MODE_K = 11;
    FYA_MODE_L = 12;
    FYA_MODE_M = 13;
    FYA_MODE_N = 14;
    FYA_MODE_O = 15;
    FYA_MODE_P = 16;

} // enum EFlashingYellowArrowMode


/*  FlashingYellowArrowSettings   */
message FlashingYellowArrowSettings  {

    // configuration instance 1-4
    uint32 instance = 1;

    // false if OFF, true if enabled
    bool enabled = 2;

    // Mode setting
    EFlashingYellowArrowMode mode = 3;

    // FYA flashing frequency check
    bool flash_rate_detect_enabled = 4;

    // Yellow trap detection
    bool yellow_trap_detect_enabled = 5;

    // The overlap channel for the FYA, 1-32
    uint32 overlap_channel = 6;

    // The associated left turn phase channel, 1-32
    uint32 left_turn_channel = 7;

    // The turn's opposing through phase channel, 1-32
    uint32 opposing_thru_channel = 8;
    
} // FlashingYellowArrowSettings

/*  ENUM EConfigDataLocation specifies locations for the config data.
 * 
 *  The Jumperless Program Card is the factory default; if any of its
 *  jumpers are set by the Agency, then it becomes a Jumpered Program Card,
 *  and the fields covered by the jumpers cannot be changed (overwritten),
 *  and CmdWriteDataKey must exactly match those jumpered fields.
 */
enum EConfigDataLocation {

    CONFIG_DATA_LOC_UNSPECIFIED = 0;
    CONFIG_DATA_LOC_JUMPERED_PROGRAM_CARD = 1;
    CONFIG_DATA_LOC_JUMPERLESS_PROGRAM_CARD = 2;
    CONFIG_DATA_LOC_DATA_KEY = 3;
    CONFIG_DATA_LOC_LEGACY_EDI = 4;
    CONFIG_DATA_LOC_LEGACY_RENO = 5;
    CONFIG_DATA_LOC_LEGACY_NO_MEM = 6;
    CONFIG_DATA_LOC_NONE = 255;
} // enum EConfigDataLocation

/*  Type of the Program Card / Data Key data to be read.
    Either Current or Pending data may be requested;
    if Pending is requested but there is no pending data, the response will be for PC_DK_NONE.
*/
enum EPcDkReadType {
    PC_DK_UNSPECIFIED = 0;
    PC_DK_NONE = 1;    // No Program Card / Data Key available per the request
    PC_DK_CURRENT = 2; // The Current Program Card / Data Key data
    PC_DK_PENDING = 3; // The Pending Program Card / Data Key data
}

/*  ENUM EWriteResult gives the results for a write of configuration or settings to the unit.
 * Some of these are mapped from Serial NACK status codes. */
enum EWriteResult {

    WRITE_RESULT_UNSPECIFIED = 0;
    WRITE_RESULT_SUCCESS = 1; 
    WRITE_RESULT_CRC_INVALID = 2;               // data key or user settings write
    WRITE_RESULT_DESTINATION_UNAVAILABLE = 3;   // unavailable or has pending changes
    WRITE_RESULT_WRITE_PROTECTED = 4;           // data key write
    WRITE_RESULT_WRITE_PCB_OPTION_MISMATCH = 5; // factory settings write
    WRITE_RESULT_WRITE_OUT_OF_RANGE = 6;        // overrides write
    WRITE_RESULT_WRITE_INVALID_DATA = 7;        // user settings write
    WRITE_RESULT_BUSY = 8;                      // Cannot accept right now; user settings write
    WRITE_RESULT_NOT_PERMITTED = 9;             // Command was not permitted (maybe user permissions)

} // enum EWriteResult


/*  FlashAreaStatistics - flash integrity check values for an area */
message FlashAreaStatistics  {

    // The most recent checksum calculated over the flash area
    fixed32 calculated_checksum = 1;

    // The checksum stored in the flash area
    fixed32 stored_checksum = 2;

    // The size of the flash area in bytes
    uint32 size_in_bytes = 3;    

} // FlashAreaStatistics


/*  ENUM EMonitorStatistics are values for different types of monitor statistics. */
enum EMonitorStatistics {

    STATISTICS_UNSPECIFIED = 0;
    STATISTICS_ALL = 1;
    STATISTICS_PORT1 = 2;               // controller serial bus                  
    STATISTICS_DATA_KEY = 3;            // or program card
    STATISTICS_MAIN_TO_ISOLATED = 4;    // main mcu to isolated mcu serial comms (main side)
    STATISTICS_MAIN_TO_COMMS = 5;       // main mcu to communications mcu serial comms (main side)
    STATISTICS_COMMS_TO_MAIN = 6;       // communications mcu to main mcu serial comms (communications side)

} // enum EMonitorStatistics


/*  ENUM ETimeSetResult are result values for setting time. */
enum ETimeSetResult {

    TIME_SET_UNSPECIFIED = 0;
    TIME_SET_SUCCESS = 1;
    TIME_SET_DATE_TIME_OUT_OF_RANGE = 2;
    TIME_SET_DST_OUT_OF_RANGE = 3;
    TIME_SET_LOG_ERR = 4;   // Failed to log to clock log when time changed

} // enum ETimeSetResult

/*  PcbOptionsMmu defines possible PCB Options read from jumper settings. 
    Currently only 4 bits of data are provided, abstracted as bools, corresponding to 
    the 4 IO jumpers read in from the Main processor. 
    Will probably be renamed or remapped later. */
message PcbOptionsMmu {

    bool option1 = 1;
    bool option2 = 2;
    bool option3 = 3;
    bool option4 = 4;

} // PcbOptionsMmu


/*  ENUM EOemType defines the OEM Type that MMU is built for */
enum EOemType {
    OEM_TYPE_UNSPECIFIED = 0;
    OEM_TYPE_EDI = 1;
    OEM_TYPE_RENO = 2;
    OEM_TYPE_MCCAIN = 3;
    OEM_TYPE_ECONOLITE = 4;
    OEM_TYPE_SAFETRAN = 5;
    OEM_TYPE_ORIUX = 6;
    OEM_TYPE_MOBOTREX = 7;
    OEM_TYPE_CUBIC = 8;
} // enum EOemType

/*  FactoryOptionsMmu defines possible Factory Options written to the monitor. 
    This is mapped from a 32-bit field in the serial data (td_Factory_Options). */
message FactoryOptionsMmu {

    // OEM Type that MMU is built for
    EOemType oem_type = 1;

    // If true, the monitor supports 16 channels plus walk (16+ type)
    bool ch_16_w_walk = 2;

    // If true, the monitor supports Canadian Features (Fast flashing greens, European date format, and Celsius)
    bool canadian_features = 3;

    // If true, the monitor supports LSU (Load Sensing Unit)
    bool lsu_enable = 4;

     // More may be added later

} // FactoryOptionsMmu

/*  AgencyOptionsMmu - defines Agency Options for the monitor */
message AgencyOptionsMmu {
    // Must user validate through Bluetooth before allowing front panel edits
    bool must_log_in_to_edit = 1;

    // Must user validate through Bluetooth before allowing front panel view
    bool must_log_in_to_view = 2;

    // Program Card data cannot be changed through the front panel
    bool no_fp_config_change = 3;

    // Allow the Remote Configuration Acceptance via the Remote Reset command
    bool permit_remote_config_acceptance = 4;

    // Allow the Remote Fault Clear via the Remote Reset command
    // Note: in the future, we may add more, and more distinct, fault clear options
    bool permit_remote_fault_clear = 5;

} // AgencyOptionsMmu

/*  ENUM ERemoteResetType are types for Fault Clear / Configuration Acceptance (Remote Reset) */
enum ERemoteResetType {
    REMOTE_RESET_TYPE_UNSPECIFIED = 0;
    REMOTE_RESET_TYPE_FAULT_CLEAR = 1;
    REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE = 2;
}
