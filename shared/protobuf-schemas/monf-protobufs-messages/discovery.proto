/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  DISCOVERY
    These messages are used for MMU disovery by the PC App over a UDP socket, and must be framed
    using the Authentication Transport Framing 
*/
syntax = "proto3";
package discovery;

//***************************************************************************************** IMPORTS
import "basic.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES


/*  DiscHelloDevice is sent by the PC Application to request a DiscHelloApp be sent back.  This message
    may be sent as a UDP broadcast to identify all devices on the local network, or as a directed
    UDP message to request a response from a specific unit. */
message DiscHelloDevice {

    // the hello message version
    uint32 version = 1;

    // The IP address and port where the PC App wants the device to send the DiscHelloApp response.
    // If pc_app_address is absent or blank, the device will send the response to the socket it
    // received the DiscHelloDevice datagram from.
    basic.IpAddressV4 pc_app_ip4_address = 2;
    uint32 pc_app_port = 3;

    // The device type(s) that should reply.  If DISC_TYPE_UNSPECIFIED, then ALL device types
    // should respond.
    EDiscoveryDeviceType device_type = 5;

    // The target group number of devices for a Broadcast DiscHelloDevice.  If non-zero, only devices
    // assigned to that group number will respond.  Used to limit the responses on a large network.
    // If group_number==0, the ALL devices will respond, regardless of their group number.  
    // For a Directed DiscHelloDevice, this should be 0 to ensure the device responds.
    uint32 group_number = 4;    // MAX 32767

} // DiscHelloDevice


/*  DiscHelloApp is sent by the device firmware in response to the DiscHelloDevice message. */
message DiscHelloApp {

    // the hello message version
    // NOTE: This may be less than the DiscHelloDevice.version, meaning the the firmware
    //       only supports the version 1 fields in the DiscHelloDevice message and has 
    //       responded accordingly.
    uint32 version = 1;

    // The IP address of the device.  Normally this should match the socket the app received the datagram
    // from.
    basic.IpAddressV4 device_ip4_address = 2; 

    // The type of device that is sending this message
    EDiscoveryDeviceType device_type = 3;

    // The group number that this device is assigned to.  If no group number is assigned,
    // the value will be zero (0)
    uint32 group_number = 4;

    // A unique identifier for this device.  May be the serial number.
    bytes device_id = 5;        // Max data length set in discovery.options

    // A text string with the user assigned name of the device.
    string name_string = 6;     // Max string length set in discovery.options

} // DiscHelloApp


/*  ENUM EDiscoveryDeviceType identifies the type of device. */
enum EDiscoveryDeviceType {

    DISC_TYPE_UNSPECIFIED = 0;      // For DiscHelloDevice, this means "ALL" device types
    DISC_TYPE_MMU = 1;              // The responding device is an MMU
    // TBD other device types

} // enum EDiscoveryDeviceType


//-------------------------------------------------------------------------------------------------



