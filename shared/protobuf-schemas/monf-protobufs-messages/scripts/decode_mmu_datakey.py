#!/usr/bin/env python3

"""
Decode a 512-byte MMU data key binary file back into JSON format.

Usage:
    python decode_mmu_datakey.py <binary_file>

Example:
    python decode_mmu_datakey.py "example.bin"
    # This will create example_decoded.json in the same directory
"""

import argparse
import json
import os
import sys
from typing import Dict, Any, List

def compute_crc16(data: bytes, seed: int = 0xFFFF) -> int:
    """Compute CRC-16 over the given data using the specified seed value.
    
    Args:
        data: The bytes to compute the CRC over
        seed: The initial CRC value (default: 0xFFFF)
        
    Returns:
        The 16-bit CRC value
    """
    crc = seed
    for byte in data:
        crc ^= (byte << 8)
        for _ in range(8):
            if crc & 0x8000:
                crc = (crc << 1) ^ 0x1021
            else:
                crc = crc << 1
            crc &= 0xFFFF
    return crc

def decode_binary_data(data: bytes) -> Dict[str, Any]:
    """Convert a 512-byte binary buffer back into a data key dictionary.
    
    Args:
        data: The 512-byte binary data to decode
        
    Returns:
        A dictionary containing the decoded data key values
    """
    if len(data) != 512:
        raise ValueError(f"Input file must be exactly 512 bytes, got {len(data)} bytes")
    
    # Compute CRC-16 over the first 510 bytes
    computed_crc = compute_crc16(data[:510])
    stored_crc = (data[510] << 8) | data[511]
    
    # Print CRC verification message
    if computed_crc == stored_crc:
        print(f"CRC-16 verification passed: 0x{computed_crc:04x}")
    else:
        print(f"CRC-16 verification failed:")
        print(f"  Computed CRC: 0x{computed_crc:04x}")
        print(f"  Stored CRC:   0x{stored_crc:04x}")
    
    # Helper function to read a value from the buffer
    # The offset is given as a 1-based index, since that is how the data struct comments are written
    def read_value(offset: int, size: int = 1, is_string: bool = False) -> Any:
        offset -= 1  # Convert to 0-based index
        if is_string:
            # For strings, read until null terminator or end of size
            value = data[offset:offset + size]
            null_pos = value.find(b'\x00')
            if null_pos != -1:
                value = value[:null_pos]
            return value.decode('ascii')
        elif size == 1:
            return f"0x{data[offset]:02x}"
        elif size == 2:
            value = (data[offset] << 8) | data[offset + 1]
            return f"0x{value:04x}"
        elif size == 4:
            value = (data[offset] << 24) | (data[offset + 1] << 16) | (data[offset + 2] << 8) | data[offset + 3]
            return f"0x{value:08x}"
        else:
            raise ValueError(f"Invalid size: {size}")
    
    # Create the data key structure
    data_key = {
        "Version": read_value(1),  # Version (1 byte)
        
        # Permissives (63 bytes)
        "Permissives": [read_value(2 + i) for i in range(63)],
        
        # Main configuration fields (4 bytes each)
        "LOS_En": read_value(64, 4),
        "Dark_Map_1": read_value(68, 4),
        "Dark_Map_2": read_value(72, 4),
        "Dark_Map_3": read_value(76, 4),
        "Dark_Map_4": read_value(80, 4),
        "GY_En": read_value(84, 4),
        "YR_En": read_value(88, 4),
        "GR_En": read_value(92, 4),
        "Min_Y_En": read_value(96, 4),
        "Min_YR_En": read_value(100, 4),
        "Y_Dis": read_value(104, 4),
        "FDW_En": read_value(108, 4),  # Flashing Dont Walk Enables
        "Cur_Sns_R_En": read_value(112, 4),
        "Cur_Sns_Y_En": read_value(116, 4),
        "Cur_Sns_G_En": read_value(120, 4),
        
        # Current sense levels (32 bytes each)
        "Cur_Sns_Lvl_R": [read_value(124 + i) for i in range(32)],
        "Cur_Sns_Lvl_Y": [read_value(156 + i) for i in range(32)],
        "Cur_Sns_Lvl_G": [read_value(188 + i) for i in range(32)],
        
        # Field check enables (4 bytes each)
        "Fld_Chk_R_En": read_value(220, 4),
        "Fld_Chk_Y_En": read_value(224, 4),
        "Fld_Chk_G_En": read_value(228, 4),
        "Fld_Chk_W_En": read_value(232, 4),
        
        # Unit settings and configuration (1 byte each)
        "Min_Flash_Time": int(read_value(236), 16),
        "UnitSetting1": read_value(237),
        "UnitSetting2": read_value(238),
        "UnitSetting3": read_value(239),
        "UnitSetting4": read_value(240),
        "PC_Latches": read_value(241),
        
        # Reserved bytes (9 bytes)
        "Reserved": [read_value(242 + i) for i in range(9)],
        
        # Channel enables (4 bytes)
        "Ch_En": read_value(252, 4),
        
        # IDs (40 bytes each)
        "Monitor_ID": read_value(256, 40, True),
        "User_ID": read_value(296, 40, True),
        
        # Network configuration
        "Enet_Control": read_value(336),
        "Enet_IP_4": int(read_value(337), 16),
        "Enet_IP_3": int(read_value(338), 16),
        "Enet_IP_2": int(read_value(339), 16),
        "Enet_IP_1": int(read_value(340), 16),
        "Enet_Sub_4": int(read_value(341), 16),
        "Enet_Sub_3": int(read_value(342), 16),
        "Enet_Sub_2": int(read_value(343), 16),
        "Enet_Sub_1": int(read_value(344), 16),
        "Enet_Gate_4": int(read_value(345), 16),
        "Enet_Gate_3": int(read_value(346), 16),
        "Enet_Gate_2": int(read_value(347), 16),
        "Enet_Gate_1": int(read_value(348), 16),
        
        # Host name (15 bytes)
        "Enet_Host_Name": read_value(349, 15, True),
        
        # FYA settings
        "FYA_Unit_Settings": read_value(363),
        "FYA_Output_Group1": [read_value(364 + i) for i in range(7)],
        "FYA_Output_Group2": [read_value(371 + i) for i in range(7)],
        "FYA_Output_Group3": [read_value(378 + i) for i in range(7)],
        "FYA_Output_Group4": [read_value(385 + i) for i in range(7)],
        
        # Application specific buffer
        "App_Spec_Buf_Sz": int(read_value(392), 16),
        "App_Spec_Buf": [read_value(393 + i) for i in range(117)],
        
        # Frame check sequence (2 bytes)
        "FCS_MSB": read_value(512),
        "FCS_LSB": read_value(511)
    }
    
    return data_key

def main():
    parser = argparse.ArgumentParser(
        description='Decode a 512-byte MMU data key binary file back into JSON format',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Decode a binary data key file
    python decode_mmu_datakey.py "example.bin"
    # This will create example_decoded.json in the same directory
        """
    )
    parser.add_argument('binary_file', help='Path to the binary file to decode')
    
    args = parser.parse_args()
    
    try:
        # Read the binary file
        with open(args.binary_file, 'rb') as f:
            data = f.read()
        
        # Decode the binary data
        data_key = decode_binary_data(data)
        
        # Create the output filename
        output_file = os.path.splitext(args.binary_file)[0] + '_decoded.json'
        
        # Write the JSON file
        with open(output_file, 'w') as f:
            json.dump(data_key, f, indent=2)
        print(f"Successfully decoded binary data to {output_file}")
        
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    main() 