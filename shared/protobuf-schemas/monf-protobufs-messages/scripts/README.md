# Parsing Light-o-Rama scripts into Fault Log data

## Script parse_light_o_rama.py

Copy the Light-o-Rama script(s) of interest from the repo <https://bitbucket.org/synapse-its/mont-lor-tester/src/main/V2%20Synapse%20Cube/ConvertedLASFiles>
to this scripts folder, for convenience.

To simplify the generating of representative data, we tell the script which fault is occurring, on which channel, and when.
There is good support for Conflict and Multiple Indication faults.

Each run will generate 3 output files, a Fault Header log file with one entry,
a Measurements Log file with entries for each 10 centisecond step in the Light-o-Rama script
up to the time of the fault, and a Fault Sequence Log with entries covering the steps
in the LoR script leading up to the fault.
The sequence IDs and the order of the entries for the Measurements and Sequence logs start at 1 at the time of the fault,
and increment for each step working back from the fault (ie, reverse order).

The usage for this script is given via its help:

```
python scripts/parse_light_o_rama.py help

usage: parse_light_o_rama.py [-h] [--fault-id FAULT_ID] --prefix PREFIX --fault-code
                             {FAULT_CODE_UNSPECIFIED,FAULT_CODE_SERIAL_PORT1,FAULT_CODE_MONITOR_MAINS,FAULT_CODE_FLASH,FAULT_CODE_CONTROLLER_VOLTAGE,FAULT_CODE_24V_MONITOR_1,FAULT_CODE_24V_MONITOR_2,FAULT_CODE_CONFLICT,FAULT_CODE_MULTIPLE_INDICATION,FAULT_CODE_LACK_OF_SIGNAL,FAULT_CODE_MINIMUM_Y_CLEARANCE,FAULT_CODE_SKIPPED_Y_CLEARANCE,FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE,FAULT_CODE_FIELD_CHECK,FAULT_CODE_FLASHING_Y_ARROW,FAULT_CODE_DATAKEY_DATA,FAULT_CODE_DATAKEY_ABSENT}
                             [--fault-subcode {SUBFLT_MULTIND_UNSPECIFIED,SUBFLT_MULTIND_GREEN_YELLOW,SUBFLT_MULTIND_GREEN_RED,SUBFLT_MULTIND_YELLOW_RED,SUBFLT_MULTIND_MULTIPLE,SUBFLT_MULTIND_FLASHING_Y_ARROW,SUBFLT_LACKOFSIG_UNSPECIFIED,SUBFLT_LACKOFSIG_NORMAL,SUBFLT_LACKOFSIG_FLASHING_Y_ARROW}]
                             --first-fault-channel FIRST_FAULT_CHANNEL --fault-time-cs FAULT_TIME_CS
                             filename
parse_light_o_rama.py: error: the following arguments are required: --prefix, --fault-code, --first-fault-channel, --fault-time-cs
```

## Build Fault Log files for a Conflict

Using the LoR script for a conflict in the Yellows for channels 3 and 4, which is declared
to be at the 1250 centisecond mark (second instance, actually, after the first at 800)
this is the command line:

```
python scripts/parse_light_o_rama.py "scripts/5B - 2018 - 3 to 4 with 10 FYA-G - 3Y 4Y Conflict.las" --fault-id 2 --prefix "LOR_34ConflictY_10" --fault-code FAULT_CODE_CONFLICT --first-fault-channel 3 --fault-time-cs 1250
```

The output are three JSON files, LOR_34ConflictY_10_Fault_Header_Log.json, LOR_34ConflictY_10_Measurements_Log.json, and LOR_34ConflictY_10_Sequence_Log.json.

Appropriate choices are made for much of the data (eg, 120.0 or 0.0 volts).

In one respect, the Measurement and Sequence logs might not be correct: the fault time given by the command line, and might be chosen to fall
sometime after the first actual fault condition that would have been detected by a real monitor.

## Build Fault Log files for a Multiple Indication Fault

Here we give the SubFault argument as well for Red-Green on channel 10 at 1200 centiseconds
(actually the 4th instance, but allowing for more data capture).

```
python scripts/parse_light_o_rama.py "scripts/5B - 2018 - 3 with 10 FYA-G - Dual FR.las" --fault-id 3 --prefix "LOR_3_10RG" --fault-code FAULT_CODE_MULTIPLE_INDICATION --fault-subcode SUBFLT_MULTIND_GREEN_RED --first-fault-channel 10 --fault-time-cs 1200
```

## Script parse_mmu_logs_xls.py

This script parses an Excel file that contains representative data for the other log types
into the JSON presentation of their protobuf equivalent messages.

Its usage is:

usage: parse_mmu_logs_xls.py [-h] --prefix PREFIX excel_file 

It produces files {prefix}_Clock_Log.json, {prefix}_Power_Log.json, {prefix}_Reset_Log.json,
{prefix}_Config_Log.json, {prefix}_Port1_Log.json, and {prefix}_Alarm_Log.json.

For example, the command used here (run from the scripts folder) was:

`python parse_mmu_logs_xls.py ".\NextGen MMU Sample Logs.xlsx" --prefix MMU_Sample`

## Script parse_mmu_datakeys.py

This script parses an Excel file that contains representative data for Data Key or
Program Card data, and parses it into both a JSON representation of the td_PC_DK_Mem
data structure and into a 512 byte binary ".bin" file (similar to the EDI MonitorKey program).

The Excel file can list multiple DataKey file structures in the "Data Key Files" sheet, 
with the output filename given in the first column as "File_Name" (without the extension).

As an intermediate output, the Permissives are generated from the "Permissive Files" sheet,
as JSON files containing byte arrays, whose names are given in the first "Permissive FileName"
column and referenced as such in the "Permissives" column of the Data Key Files sheet.
These byte arrays will be absorbed into the DataKey output.

The script's usage is:

usage: parse_mmu_datakeys.py [-h] excel_file

For the sample spreadsheet, the command, run from the root folder, is:

`python scripts/parse_mmu_datakeys.py "scripts/NextGen MMU Sample Logs.xlsx"`

## Script decode_mmu_datakey.py

This script does the inverse of parse_mmu_datakeys.py - it processes a binary input (DataKey) file of 512 bytes and produces the JSON equivalent of the td_PC_DK_Mem data structure.
It also verifies that the CRC at the end of the input file is correct.

You can then compare an input JSON file with the output of this script for round-trip
verification of the commands to Write and Read the Data Key / Program Card data.
There are minor differences in the output: the text strings are terminated in the new
output and the CRC value is shown.

The script's usage is:

`python decode_mmu_datakey.py "example.bin"`

which will produce an output file named "example_decoded.json". 
For example, this was tested with the command (from the root folder):

`python scripts/decode_mmu_datakey.py "scripts/MMU_Data_Key4.bin"`
