#!/usr/bin/env python3

"""
Parse MMU Clock Log and Power Log Excel files and generate JSON output.

Usage:
    python parse_mmu_logs_xls.py <excel_file> --prefix <prefix>

Example:
    python parse_mmu_logs_xls.py "NextGen MMU Sample Logs.xlsx" --prefix "sample"
    # This will create: sample_Clock_Log.json and sample_Power_Log.json in the same directory as the script

The script reads the "Clock Log" and "Power Log" sheets from the specified Excel file and generates
JSON files containing the parsed entries. The output files will be saved in the same directory as
the script with the naming patterns: {prefix}_Clock_Log.json and {prefix}_Power_Log.json

The output format follows the ClockLogEntry protobuf message format:
- entry_id: unique identifier for each entry
- clock_source: source of the date/time change
- previous_datetime: previous date/time in LocalDateTime format
- new_datetime: new date/time in LocalDateTime format
- run_time: total run time in seconds
"""

import argparse
import json
import os
import sys
import openpyxl
from datetime import datetime
from typing import List, Dict, Any

import pandas as pd

def convert_to_local_datetime(dt_str: str) -> Dict[str, int]:
    """Convert datetime string to LocalDateTime format.
    Must handle an initial date/time of 00/00/00 00:00:13"""
    try:
        # Split into date and time parts
        date_part, time_part = dt_str.split()
        
        # Handle special case where date is "00/00/00"
        if date_part == "00/00/00":
            # Parse time components normally
            hours, minutes, seconds = map(int, time_part.split(':'))
            return {
                "year": 0,
                "month": 0,
                "day": 0,
                "hour": hours,
                "minute": minutes,
                "second": seconds
            }
            
        # Normal datetime parsing for all other cases
        dt = pd.to_datetime(dt_str)
        return {
            "year": dt.year,
            "month": dt.month,
            "day": dt.day,
            "hour": dt.hour,
            "minute": dt.minute,
            "second": dt.second
        }
    except:
        return None

def convert_run_time_to_seconds(run_time_str: str) -> int:
    """Convert run time from calendar format to seconds.
    
    Args:
        run_time_str: String in format "DD/MM/YY HH:MM:SS" where:
            DD = days
            MM = months
            YY = years
            HH = hours
            MM = minutes
            SS = seconds
    """
    try:
        # Split into date and time parts
        date_part, time_part = run_time_str.split()
        
        # Parse date components (DD/MM/YY)
        days, months, years = map(int, date_part.split('/'))
        
        # Parse time components (HH:MM:SS)
        hours, minutes, seconds = map(int, time_part.split(':'))
        
        # Convert to seconds
        total_seconds = (
            years * 31536000 +  # 365 days * 24 hours * 60 minutes * 60 seconds
            months * 2592000 +  # 30 days * 24 hours * 60 minutes * 60 seconds
            days * 86400 +      # 24 hours * 60 minutes * 60 seconds
            hours * 3600 +      # 60 minutes * 60 seconds
            minutes * 60 +      # 60 seconds
            seconds
        )
        return total_seconds
    except:
        return 0

def create_now_min_max_float(present: float, min_val: float, max_val: float) -> Dict[str, float]:
    """Create a NowMinMaxFloat message."""
    return {
        "present": float(present),
        "minimum": float(min_val),
        "maximum": float(max_val)
    }

def get_power_log_event_type(event_type_hex: str) -> str:
    """Convert hexadecimal event type to EPowerLogEventType enum value."""
    try:
        # Convert hex string to integer
        event_type_int = int(event_type_hex, 16)
        
        # Map Serial Comms Lib integer to pbuf enum value
        # This is not a complete set, as some pbuf enums are for the CMU only   
        event_type_map = {
            0: "PWR_LOG_EVENT_POWER_UP",
            1: "PWR_LOG_EVENT_POWER_DOWN",
            2: "PWR_LOG_EVENT_LOW_VOLTAGE",
            3: "PWR_LOG_EVENT_HIGH_VOLTAGE",
            4: "PWR_LOG_EVENT_POWER_INTERRUPTED",
            5: "PWR_LOG_EVENT_TIMED",
            6: "PWR_LOG_EVENT_LOW_VOLTAGE_RECOVERY",
            7: "PWR_LOG_EVENT_HIGH_VOLTAGE_RECOVERY",
            8: "PWR_LOG_EVENT_FREQUENCY_LOW",
            9: "PWR_LOG_EVENT_FREQUENCY_HIGH",
            254: "PWR_LOG_EVENT_TEST"   # 0xFE
        }
        return event_type_map.get(event_type_int, "PWR_LOG_EVENT_UNSPECIFIED")
    except:
        return "PWR_LOG_EVENT_UNSPECIFIED"

def parse_power_log_sheet(excel_file: str) -> List[Dict[str, Any]]:
    """Parse the Power Log sheet from the Excel file and return a list of entries."""
    try:
        # Read the Power Log sheet
        df = pd.read_excel(excel_file, sheet_name="Power Log")
        
        entries = []
        for idx, row in df.iterrows():
            # Create power monitors
            power_monitors = {
                "ac_mains_volts": create_now_min_max_float(
                    row.get("AC_Line_Volts Present", 0),
                    row.get("AC_Line_Volts Min", 0),
                    row.get("AC_Line_Volts Max", 0)
                ),
                "dc_24_volts_mon1": create_now_min_max_float(
                    row.get("24V_1_Volts Present", 0),
                    row.get("24V_1_Volts Min", 0),
                    row.get("24V_1_Volts Max", 0)
                ),
                "dc_24_volts_mon2": create_now_min_max_float(
                    row.get("24V_2_Volts Present", 0),
                    row.get("24V_2_Volts Min", 0),
                    row.get("24V_2_Volts Max", 0)
                ),
                "cvm_volt": create_now_min_max_float(
                    row.get("CVM_Volts Present", 0),
                    row.get("CVM_Volts Min", 0),
                    row.get("CVM_Volts Max", 0)
                ),
                "line_frequency_hz": create_now_min_max_float(
                    row.get("Line_Freq Present", 0),
                    row.get("Line_Freq Min", 0),
                    row.get("Line_Freq Max", 0)
                )
            }
            
            # Create temperature
            temperature = create_now_min_max_float(
                row.get("Temperature Present", 0),
                row.get("Temperature Min", 0),
                row.get("Temperature Max", 0)
            )
            
            # Create entry
            entry = {
                "entry_id": idx + 1,
                "event_type": get_power_log_event_type(row.get("Event_Type", "")),
                "entry_timestamp": convert_to_local_datetime(row.get("Date_Time", "")),
                "power_voltages": power_monitors,
                "temperature_degf": temperature,
                "entry_period_minutes": row.get("Time_Per_Rec", 0) * 60  # convert to minutes
            }
            
            # Add power_interrupt_time_ms if present
            if pd.notna(row.get("Power Interrupt Time")):
                entry["power_interrupt_time_ms"] = int(row.get("Power Interrupt Time"))
            
            entries.append(entry)
            
        return entries
        
    except Exception as e:
        print(f"Error parsing Power Log sheet: {e}", file=sys.stderr)
        sys.exit(1)

def parse_clock_log_sheet(excel_file: str) -> List[Dict[str, Any]]:
    """Parse the Clock Log sheet from the Excel file and return a list of entries."""
    try:
        # Read the Clock Log sheet
        df = pd.read_excel(excel_file, sheet_name="Clock Log")
        
        # Convert DataFrame to list of dictionaries
        entries = []
        for idx, row in df.iterrows():
            entry = {
                "entry_id": idx + 1,  # Generate unique entry_id
                "clock_source": row.get("Source", "UNKNOWN"),
                "previous_datetime": convert_to_local_datetime(row.get("Org_Date_Time")),
                "new_datetime": convert_to_local_datetime(row.get("New_Date_Time")),
                "run_time": convert_run_time_to_seconds(row.get("RunTime", "0"))
            }
            # Only add entries that have valid datetime conversions
            if entry["previous_datetime"] and entry["new_datetime"]:
                entries.append(entry)
            
        return entries
        
    except Exception as e:
        print(f"Error parsing Excel file: {e}", file=sys.stderr)
        sys.exit(1)

def get_fault_code(fault_hex: str) -> str:
    """Convert hexadecimal fault code to EFaultCode enum value."""
    try:
        # Convert hex string to integer
        fault_int = int(fault_hex, 16)
        
        # Map integer to enum value
        fault_map = {
            0x00000: "FAULT_CODE_UNSPECIFIED",
            0x00001: "FAULT_CODE_SERIAL_PORT1",
            0x00004: "FAULT_CODE_MONITOR_MAINS",
            0x00010: "FAULT_CODE_FLASH",
            0x00100: "FAULT_CODE_CONTROLLER_VOLTAGE",
            0x00200: "FAULT_CODE_24V_MONITOR_1",
            0x00400: "FAULT_CODE_24V_MONITOR_2",
            0x00800: "FAULT_CODE_CONFLICT",
            0x02000: "FAULT_CODE_MULTIPLE_INDICATION",
            0x08000: "FAULT_CODE_LACK_OF_SIGNAL",
            0x10000: "FAULT_CODE_MINIMUM_Y_CLEARANCE",
            0x20000: "FAULT_CODE_SKIPPED_Y_CLEARANCE",
            0x40000: "FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE",
            0x80000: "FAULT_CODE_FIELD_CHECK",
            0x100000: "FAULT_CODE_FLASHING_Y_ARROW",
            0x400000: "FAULT_CODE_DATAKEY_DATA",
            0x800000: "FAULT_CODE_DATAKEY_ABSENT"
        }
        return fault_map.get(fault_int, "FAULT_CODE_UNSPECIFIED")
    except:
        return "FAULT_CODE_UNSPECIFIED"

def get_reset_source(reset_source_str: str) -> str:
    """Convert reset source string to EResetLogSource enum value."""
    reset_source_map = {
        "FRONT PANEL": "RST_LOG_SOURCE_FRONT_PANEL",
        "EXTERNAL RESET": "RST_LOG_SOURCE_EXTERNAL",
        "NON-LATCHED": "RST_LOG_SOURCE_NON_LATCHED",
        "POWER CYCLE": "RST_LOG_SOURCE_POWER_CYCLE",
        "ADU": "RST_LOG_SOURCE_ADU",
        "CONFIG CHANGED": "RST_LOG_SOURCE_CONFIG_CHANGED",
        "REMOTE RESET": "RST_LOG_SOURCE_REMOTE",
        "TEST": "RST_LOG_SOURCE_TEST"
    }
    return reset_source_map.get(reset_source_str.upper(), "RST_LOG_SOURCE_UNSPECIFIED")

def parse_reset_log_sheet(excel_file: str) -> List[Dict[str, Any]]:
    """Parse the Reset Log sheet from the Excel file and return a list of entries."""
    try:
        # Read the Reset Log sheet
        df = pd.read_excel(excel_file, sheet_name="Reset Log")
        
        entries = []
        for idx, row in df.iterrows():
            # Create the entry
            entry = {
                "entry_id": idx + 1,
                "reset_source": get_reset_source(row.get("Reset_Source", "")),
                "entry_timestamp": convert_to_local_datetime(row.get("Date_Time", "")),
                "fault_cleared": get_fault_code(row.get("Fault", "")),
                "diagnostic_cleared": False,  # Default to False as it's not in the input
                "fault_id": int(row.get("Fault_ID", 0))
            }
            entries.append(entry)
            
        return entries
        
    except Exception as e:
        print(f"Error parsing Reset Log sheet: {e}", file=sys.stderr)
        sys.exit(1)

def parse_config_log_sheet(excel_file: str) -> List[Dict[str, Any]]:
    """Parse the Config Log sheet from the Excel file and return a list of entries."""
    try:
        # Read the Config Log sheet
        df = pd.read_excel(excel_file, sheet_name="Config Log")
        
        entries = []
        for idx, row in df.iterrows():
            # Create hardware revisions
            hwdrev_disp = int(row.get("HwdRev_Disp", 0))
            hardware = {
                "main_hardware_revision": int(row.get("HwdRev_Main", 0)),
                "iso_hardware_revision": int(row.get("HwdRev_Iso", 0)),
                "display_hardware_revision": hwdrev_disp & 0x0F,  # lower nibble
                "display_rms_hardware_revision": (hwdrev_disp >> 4) & 0x0F,  # upper nibble
                "comms_hardware_revision": int(row.get("HwdRev_Comm", 0))
            }
            
            # Create model and serial number
            numbers = {
                "serial": str(row.get("Serial", "")),
                "model": str(row.get("Model", ""))
            }
            
            # Create firmware versions and dates
            def create_version_str_three(version_str: str) -> Dict[str, str]:
                if pd.isna(version_str):
                    return {"major": "", "minor": "", "revision": ""}
                parts = str(version_str).split('.')
                return {
                    "major": parts[0] if len(parts) > 0 else "",
                    "minor": parts[1] if len(parts) > 1 else "",
                    "revision": parts[2] if len(parts) > 2 else ""
                }
            
            def create_date_str(date_str: str) -> Dict[str, str]:
                if pd.isna(date_str):
                    return {"month": "", "day": "", "year": ""}
                try:
                    dt = pd.to_datetime(date_str)
                    return {
                        "month": str(dt.month),
                        "day": str(dt.day),
                        "year": str(dt.year)
                    }
                except:
                    return {"month": "", "day": "", "year": ""}
            
            # Create data key data (512 bytes with sequential values)
            data_key_data = [i + 1 for i in range(512)]
            
            # Get data key CRC and monitor CRC (handling hex values)
            try:
                data_key_crc = int(str(row.get("Key_CRC", "0")).replace("0x", ""), 16)
            except:
                data_key_crc = 0
            data_key_data[510] = (data_key_crc & 0xFF00) >> 8
            data_key_data[511] = data_key_crc & 0x00FF
            
            try:
                monitor_crc = int(str(row.get("Mon_CRC", "0")).replace("0x", ""), 16)
            except:
                monitor_crc = 0
            # Get factory options, PCB options, and Agency Options bitmaps from hex values
            # The first two map to a set of bools, the last one is a uint32
            factory_options_bitmap = int(str(row.get("Factory_Options", "0")).replace("0x", ""), 16)
            pcb_options_bitmap = int(str(row.get("PCB_Options", "0")).replace("0x", ""), 16)
            agency_options = int(str(row.get("Agency_Options", "0")).replace("0x", ""), 16)
            
            # Map bitmaps to boolean options
            factory_options = {
                "option1": bool(factory_options_bitmap & 0x01),
                "option2": bool(factory_options_bitmap & 0x02),
                "option3": bool(factory_options_bitmap & 0x04),
                "option4": bool(factory_options_bitmap & 0x08)
            }
            
            pcb_options = {
                "option1": bool(pcb_options_bitmap & 0x01),
                "option2": bool(pcb_options_bitmap & 0x02),
                "option3": bool(pcb_options_bitmap & 0x04),
                "option4": bool(pcb_options_bitmap & 0x08)
            }
            
            # Create entry
            entry = {
                "config_id": idx + 1,
                "hardware": hardware,
                "entry_timestamp": convert_to_local_datetime(row.get("Date_Time", "")),
                "numbers": numbers,
                "main_mcu_fw_version": create_version_str_three(row.get("Firmware_Main_Version[6]", "")),
                "main_mcu_fw_date": create_date_str(row.get("Firmware_Main_Date[8]", "")),
                "isolated_mcu_fw_version": create_version_str_three(row.get("Firmware_Iso_Version[6]", "")),
                "isolated_mcu_fw_date": create_date_str(row.get("Firmware_Iso_Date[8]", "")),
                "disp_mcu_fw_version": create_version_str_three(row.get("Firmware_Disp_Version[6]", "")),
                "disp_mcu_fw_date": create_date_str(row.get("Firmware_Disp_Date[8]", "")),
                "comms_mcu_fw_version": create_version_str_three(row.get("Firmware_Comm_Version[6]", "")),
                "comms_mcu_fw_date": create_date_str(row.get("Firmware_Comm_Date[8]", "")),
                "ble_mcu_fw_version": create_version_str_three(row.get("Firmware_BLE_Version[6]", "")),
                "ble_mcu_fw_date": create_date_str(row.get("Firmware_BLE_Date[8]", "")),
                "pkg_mcu_fw_version": create_version_str_three(row.get("Firmware_Package_Version[6]", "")),
                "pkg_mcu_fw_date": create_date_str(row.get("Firmware_Package_Date[8]", "")),
                "data_key_data": data_key_data,
                "data_key_crc": data_key_crc,
                "monitor_crc": monitor_crc,
                "factory_options": factory_options,
                "pcb_options": pcb_options,
                "agency_options": agency_options
            }
            
            entries.append(entry)
            
        return entries
        
    except Exception as e:
        print(f"Error parsing Config Log sheet: {e}", file=sys.stderr)
        sys.exit(1)

def parse_port1_log_sheet(excel_file: str) -> List[Dict[str, Any]]:
    """Parse the SDLC Fault sheet from the Excel file and return a list of Port1LogEntryMmu entries."""
    try:
        # Read the SDLC Fault sheet
        df = pd.read_excel(excel_file, sheet_name="SDLC Fault")
        
        entries = []
        for idx, row in df.iterrows():
            # Create entry
            entry = {
                "entry_id": idx + 1,
                "entry_timestamp": convert_to_local_datetime(row.get("Date_Time", "")),
                "timestamp_when_cleared": convert_to_local_datetime(row.get("Reset_Date_Time", "")),
                "crc_error_count": int(row.get("CRC_Errs", 0)),
                "idle_error_count": int(row.get("Idle_Errs", 0)),
                "frame_error_count": int(row.get("Frame_Errs", 0)),
                "timeout_error_count": int(row.get("Timeout_Errs", 0)),
                "short_error_count": int(row.get("Short_Errs", 0)),
                "long_error_count": int(row.get("Long_Errs", 0)),
                "unknown_error_count": int(row.get("Unknown_Errs", 0))
            }
            entries.append(entry)
            
        return entries
        
    except Exception as e:
        print(f"Error parsing SDLC Fault sheet: {e}", file=sys.stderr)
        sys.exit(1)

def get_alarm_severity(alarm_type_hex: str) -> str:
    """Convert upper nibble of alarm type to EAlarmSeverity enum value."""
    try:
        # Convert hex string to integer
        alarm_type_int = int(alarm_type_hex, 16)
        # Get upper nibble by shifting right 4 bits
        severity = (alarm_type_int >> 4) & 0x0F
        
        # Map to enum values
        severity_map = {
            0x00: "ALARM_SEVERITY_CRITICAL",
            0x01: "ALARM_SEVERITY_MAJOR",
            0x02: "ALARM_SEVERITY_MINOR",
            0x03: "ALARM_SEVERITY_INFORM",
            0x0F: "ALARM_SEVERITY_TEST"
        }
        return severity_map.get(severity, "ALARM_SEVERITY_CRITICAL")
    except:
        return "ALARM_SEVERITY_CRITICAL"

def get_alarm_source(alarm_type_hex: str) -> str:
    """Convert lower nibble of alarm type to EAlarmSource enum value."""
    try:
        # Convert hex string to integer
        alarm_type_int = int(alarm_type_hex, 16)
        # Get lower nibble by masking with 0x0F
        source = alarm_type_int & 0x0F
        
        # Map to enum values
        source_map = {
            0x00: "ALARM_SOURCE_MONITOR_OPERATION",
            0x01: "ALARM_SOURCE_MONITOR_HARDWARE",
            0x02: "ALARM_SOURCE_OTHER_CABINET_HARDWARE",
            0x03: "ALARM_SOURCE_EXTERNAL_SOURCE",
            0x04: "ALARM_SOURCE_INTERNAL_COMMUNICATION",
            0x05: "ALARM_SOURCE_EXTERNAL_COMMUNICATION",
            0x0E: "ALARM_SOURCE_TEST",
            0x0F: "ALARM_SOURCE_UNSPECIFIED"
        }
        return source_map.get(source, "ALARM_SOURCE_UNSPECIFIED")
    except:
        return "ALARM_SOURCE_UNSPECIFIED"

def parse_alarm_log_sheet(excel_file: str) -> List[Dict[str, Any]]:
    """Parse the Alarm Log sheet from the Excel file and return a list of entries."""
    try:
        # Read the Alarm Log sheet
        df = pd.read_excel(excel_file, sheet_name="Alarm Log")
        
        entries = []
        for idx, row in df.iterrows():
            entry = {
                "entry_id": idx + 1,
                "severity": get_alarm_severity(row.get("Alarm_Type", "")),
                "source": get_alarm_source(row.get("Alarm_Type", "")),
                "datetime": convert_to_local_datetime(row.get("Date_Time", "")),
                "text": str(row.get("Alarm_Text", ""))
            }
            # Only add entries that have valid datetime conversions
            if entry["datetime"]:
                entries.append(entry)
            
        return entries
        
    except Exception as e:
        print(f"Error parsing Alarm Log sheet: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Parse MMU Clock Log, Power Log, Reset Log, Config Log, and Port1 Log Excel files and generate JSON output',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Parse an Excel file and save with prefix "sample"
    python parse_mmu_logs_xls.py "NextGen MMU Sample Logs.xlsx" --prefix sample

    # Parse an Excel file and save with prefix "test"
    python parse_mmu_logs_xls.py "test_logs.xlsx" --prefix test

The output will be saved as: {prefix}_Clock_Log.json, {prefix}_Power_Log.json, {prefix}_Reset_Log.json, {prefix}_Config_Log.json, and {prefix}_Port1_Log.json in the same directory as the script
        """
    )
    parser.add_argument('excel_file', help='Path to the Excel file to parse')
    parser.add_argument('--prefix', required=True, help='Prefix for the output JSON files (e.g., "sample" will create "sample_Clock_Log.json", "sample_Power_Log.json", "sample_Reset_Log.json", "sample_Config_Log.json", and "sample_Port1_Log.json")')
    
    args = parser.parse_args()
    
    # Get script directory for output files
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Parse Clock Log sheet
    clock_entries = parse_clock_log_sheet(args.excel_file)
    clock_output_file = os.path.join(script_dir, f'{args.prefix}_Clock_Log.json')
    try:
        with open(clock_output_file, 'w') as f:
            json.dump(clock_entries, f, indent=2)
        print(f"Successfully wrote {len(clock_entries)} entries to {clock_output_file}")
    except Exception as e:
        print(f"Error writing Clock Log JSON file: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Parse Power Log sheet
    power_entries = parse_power_log_sheet(args.excel_file)
    power_output_file = os.path.join(script_dir, f'{args.prefix}_Power_Log.json')
    try:
        with open(power_output_file, 'w') as f:
            json.dump(power_entries, f, indent=2)
        print(f"Successfully wrote {len(power_entries)} entries to {power_output_file}")
    except Exception as e:
        print(f"Error writing Power Log JSON file: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Parse Reset Log sheet
    reset_entries = parse_reset_log_sheet(args.excel_file)
    reset_output_file = os.path.join(script_dir, f'{args.prefix}_Reset_Log.json')
    try:
        with open(reset_output_file, 'w') as f:
            json.dump(reset_entries, f, indent=2)
        print(f"Successfully wrote {len(reset_entries)} entries to {reset_output_file}")
    except Exception as e:
        print(f"Error writing Reset Log JSON file: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Parse Config Log sheet
    config_entries = parse_config_log_sheet(args.excel_file)
    config_output_file = os.path.join(script_dir, f'{args.prefix}_Config_Log.json')
    try:
        with open(config_output_file, 'w') as f:
            json.dump(config_entries, f, indent=2)
        print(f"Successfully wrote {len(config_entries)} entries to {config_output_file}")
    except Exception as e:
        print(f"Error writing Config Log JSON file: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Parse Port1 Log sheet (SDLC Fault)
    port1_entries = parse_port1_log_sheet(args.excel_file)
    port1_output_file = os.path.join(script_dir, f'{args.prefix}_Port1_Log.json')
    try:
        with open(port1_output_file, 'w') as f:
            json.dump(port1_entries, f, indent=2)
        print(f"Successfully wrote {len(port1_entries)} entries to {port1_output_file}")
    except Exception as e:
        print(f"Error writing Port1 Log JSON file: {e}", file=sys.stderr)
        sys.exit(1)

    # Parse Alarm Log sheet
    alarm_entries = parse_alarm_log_sheet(args.excel_file)
    alarm_output_file = os.path.join(script_dir, f'{args.prefix}_Alarm_Log.json')
    try:
        with open(alarm_output_file, 'w') as f:
            json.dump(alarm_entries, f, indent=2)
        print(f"Successfully wrote {len(alarm_entries)} entries to {alarm_output_file}")
    except Exception as e:
        print(f"Error writing Alarm Log JSON file: {e}", file=sys.stderr)
        sys.exit(1)

if __name__ == '__main__':
    main() 