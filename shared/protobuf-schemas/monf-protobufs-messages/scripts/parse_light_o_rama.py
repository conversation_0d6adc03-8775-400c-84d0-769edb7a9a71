"""
Parse a Light-O-Rama LAS file and create FaultMeasurementLogEntryMmu and FaultSequenceLogEntryMmu entries.

Usage:
    # Parse with default fault_id (1) and write to files
    python parse_light_o_rama.py "5B - 2018 - 3 with 10 FYA-G - Dual FR.las" --prefix "test"

    # Parse with specific fault_id and write to files
    python parse_light_o_rama.py "5B - 2018 - 3 with 10 FYA-G - Dual FR.las" --fault-id 42 --prefix "test"

    # Show help
    python parse_light_o_rama.py --help
"""

import xml.etree.ElementTree as ET
import json
import argparse
import os
from collections import defaultdict
import datetime
from enum import Enum
import pandas as pd
import sys
from typing import List, Dict, Any

class EFaultCode(Enum):
    FAULT_CODE_UNSPECIFIED = 0
    FAULT_CODE_SERIAL_PORT1 = 1
    FAULT_CODE_MONITOR_MAINS = 3
    FAULT_CODE_FLASH = 5
    FAULT_CODE_CONTROLLER_VOLTAGE = 9
    FAULT_CODE_24V_MONITOR_1 = 10
    FAULT_CODE_24V_MONITOR_2 = 11
    FAULT_CODE_CONFLICT = 12
    FAULT_CODE_MULTIPLE_INDICATION = 14
    FAULT_CODE_LACK_OF_SIGNAL = 16
    FAULT_CODE_MINIMUM_Y_CLEARANCE = 17
    FAULT_CODE_SKIPPED_Y_CLEARANCE = 18
    FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE = 19
    FAULT_CODE_FIELD_CHECK = 20
    FAULT_CODE_FLASHING_Y_ARROW = 21
    FAULT_CODE_DATAKEY_DATA = 23
    FAULT_CODE_DATAKEY_ABSENT = 24

class ESubFaultMultipleIndication(Enum):
    SUBFLT_MULTIND_UNSPECIFIED = 0
    SUBFLT_MULTIND_GREEN_YELLOW = 1
    SUBFLT_MULTIND_GREEN_RED = 2
    SUBFLT_MULTIND_YELLOW_RED = 3
    SUBFLT_MULTIND_MULTIPLE = 4
    SUBFLT_MULTIND_FLASHING_Y_ARROW = 5

class ESubFaultLackOfSignal(Enum):
    SUBFLT_LACKOFSIG_UNSPECIFIED = 0
    SUBFLT_LACKOFSIG_NORMAL = 1
    SUBFLT_LACKOFSIG_FLASHING_Y_ARROW = 2

def set_bit(value, bit):
    """Set the specified bit in the value."""
    return value | (1 << (bit - 1))

def create_sequence_log_entry(fault_id, entry_id, period_count, reds_on_normal_chmap, yellows_on_normal_chmap, greens_on_normal_chmap, walks_on_normal_chmap):
    """Create a FaultSequenceLogEntryMmu entry."""
    return {
        "fault_id": fault_id,
        "entry_id": entry_id,
        "period_count": period_count,
        "control_states": {
            "reset_event": False,
            "start_delay_relay": False,
            "ac_line_valid": True,
            "fault_relay": False
        },
        "input_states": {
            "monitor_24v_1": True,
            "monitor_24v_2": True,
            "controller_voltage": True,
            "type_select": True,
            "red_enable": True,
            "program_card_inserted": True,
            "external_watchdog": False,
            "external_reset": False,
            "port1_disable": False,
            "local_flash": False,
            "inhibit_24v_monitor": False
        },
        "reds_on_normal_chmap": reds_on_normal_chmap,
        "yellows_on_normal_chmap": yellows_on_normal_chmap,
        "greens_on_normal_chmap": greens_on_normal_chmap,
        "walks_on_normal_chmap": walks_on_normal_chmap,
        "reds_on_lackofsignal_chmap": reds_on_normal_chmap,
        "yellows_on_lackofsignal_chmap": yellows_on_normal_chmap,
        "greens_on_lackofsignal_chmap": greens_on_normal_chmap,
        "walks_on_lackofsignal_chmap": walks_on_normal_chmap,
        "reds_on_current_chmap": 0,
        "yellows_on_current_chmap": 0,
        "greens_on_current_chmap": 0,
        "walks_on_current_chmap": 0,
        "reds_field_check_chmap": 0,
        "yellows_field_check_chmap": 0,
        "greens_field_check_chmap": 0,
        "walks_field_check_chmap": 0
    }

def get_channel_voltage_at_time(channel, time_cs):
    """Get the voltage value for a channel at a specific time."""
    color = int(channel.get('color', '0'))
    effects = channel.findall('.//effect')
    
    
    if not effects:
        # No effects, use default voltage based on color
        # Special handling for RED EN channel - note the inverted logic for color to voltage
        if channel.get('name') == 'RED EN':
            return 120.0 if color > 1000 else 0.0
        # Special handling for 24V MON channel and similar channels
        if channel.get('name') == '24V MON' or channel.get('name') == 'RESET' or channel.get('name') == 'EXT WD':
            return 24.0 if color > 1000 else 0.0
        return 120.0 if color < 1000 else 0.0
    
    # Sort effects by start time
    effects = sorted(effects, key=lambda x: int(x.get('startCentisecond', '0')))
    
    # Start by getting the default voltage
    current_voltage = 120.0 if color < 1000 else 0.0
    last_end_time = 0
    
    # Now toggle the voltage between 120V and 0V based on the effects until we get to the 
    # period that the current time falls into.
    for effect in effects:
        start_time = int(effect.get('startCentisecond', '0'))
        end_time = int(effect.get('endCentisecond', '0'))

        # A common case: start time is 0
        if start_time == 0 and time_cs <= end_time:
            # Special handling for RED EN channel - inverse of "current_voltage"
            if channel.get('name') == 'RED EN':
                return 120.0 if current_voltage == 0.0 else 0.0
            # Special handling for 24V MON channel and similar channels
            if channel.get('name') == '24V MON' or channel.get('name') == 'RESET' or channel.get('name') == 'EXT WD':
                return 24.0 if current_voltage == 0.0 else 0.0
            return current_voltage

        # Allow for first effect to start after 0
        if last_end_time == 0 and start_time > 0:
            last_end_time = start_time
            if time_cs < start_time:
                # Special handling for RED EN channel - inverse of "current_voltage"
                if channel.get('name') == 'RED EN':
                    return 120.0 if current_voltage == 0.0 else 0.0
                # Special handling for 24V MON channel and similar channels
                if channel.get('name') == '24V MON' or channel.get('name') == 'RESET' or channel.get('name') == 'EXT WD':
                    return 24.0 if current_voltage == 0.0 else 0.0
                # The voltage is correct, so we can return it
                return current_voltage
        
        # Check if there's a gap between the last end time and this start time
        if last_end_time < start_time - 1:
            # Toggle voltage for the gap
            current_voltage = 0.0 if current_voltage == 120.0 else 120.0
            
            # If time_cs falls in the gap, return the current voltage
            if last_end_time <= time_cs < start_time:
                # Special handling for RED EN channel - inverse of "current_voltage"
                if channel.get('name') == 'RED EN':
                    return 0.0 if current_voltage == 120.0 else 120.0
                # Special handling for 24V MON channel and similar channels
                if channel.get('name') == '24V MON' or channel.get('name') == 'RESET' or channel.get('name') == 'EXT WD':
                    return 0.0 if current_voltage == 120.0 else 24.0
                return current_voltage
        
        # Toggle the working voltage for the next range
        current_voltage = 0.0 if current_voltage == 120.0 else 120.0
        last_end_time = end_time
        # If in this range, return the current voltage
        if time_cs < end_time:
            # Special handling for RED EN channel - inverse of "current_voltage"
            if channel.get('name') == 'RED EN':
                return 0.0 if current_voltage == 120.0 else 120.0
            # Special handling for 24V MON channel and similar channels
            if channel.get('name') == '24V MON' or channel.get('name') == 'RESET' or channel.get('name') == 'EXT WD':
                return 0.0 if current_voltage == 120.0 else 24.0
            return current_voltage
    
    # If we've gone through all effects and time_cs is beyond the last end_time,
    # we need to toggle the voltage one more time
    if time_cs >= last_end_time:
        current_voltage = 0.0 if current_voltage == 120.0 else 120.0
    
    # Special handling for RED EN channel - inverse of "current_voltage"
    if channel.get('name') == 'RED EN':
        return 0.0 if current_voltage == 120.0 else 120.0
    # Special handling for 24V MON channel and similar channels
    if channel.get('name') == '24V MON' or channel.get('name') == 'RESET' or channel.get('name') == 'EXT WD':
        return 0.0 if current_voltage == 120.0 else 24.0
    return current_voltage

def create_fault_header_log_entry(fault_id, step_time_cs, measurement_count, sequence_count, fault_code, fault_subcode, first_fault_channel, fault_time_cs, measurement_entries):
    """Create a FaultHeaderLogEntryMmu entry."""
    now = datetime.datetime.now()
    current_time = {
        "year": now.year,
        "month": now.month,
        "day": now.day,
        "hour": now.hour,
        "minute": now.minute,
        "second": now.second
    }
    log_frequency = 100 / step_time_cs  # Convert step time to frequency
    
    # Find the measurement entry at the fault time, which is the first entry in the list now
    fault_entry = measurement_entries[0]
    # print(f"Building fault header log entry for fault {fault_id} at time {fault_time_cs} with measurement entry {fault_entry}")

    # Copy channel maps from the measurement entry
    reds_on_chmap = fault_entry["reds_on_normal_chmap"]
    yellows_on_chmap = fault_entry["yellows_on_normal_chmap"]
    greens_on_chmap = fault_entry["greens_on_normal_chmap"]
    walks_on_chmap = fault_entry["walks_on_normal_chmap"]
    
    # Initialize channels_chmap with first fault channel
    channels_chmap = set_bit(0, first_fault_channel)
    
    # Initialize indication channel maps
    red_indications_chmap = 0
    yellow_indications_chmap = 0
    green_indications_chmap = 0
    
    # If this is a conflict fault, check the channel maps and set indication maps
    if fault_code == EFaultCode.FAULT_CODE_CONFLICT:
        channels_chmap = set_bit(channels_chmap, first_fault_channel + 1)
        
        # Check each color's channel map and set indication maps accordingly
        if reds_on_chmap & (1 << (first_fault_channel - 1)):
            red_indications_chmap = set_bit(red_indications_chmap, first_fault_channel)
            red_indications_chmap = set_bit(red_indications_chmap, first_fault_channel + 1)
        if yellows_on_chmap & (1 << (first_fault_channel - 1)):
            yellow_indications_chmap = set_bit(yellow_indications_chmap, first_fault_channel)
            yellow_indications_chmap = set_bit(yellow_indications_chmap, first_fault_channel + 1)
        if greens_on_chmap & (1 << (first_fault_channel - 1)):
            green_indications_chmap = set_bit(green_indications_chmap, first_fault_channel)
            green_indications_chmap = set_bit(green_indications_chmap, first_fault_channel + 1)
    
    # For multiple indication faults, set the appropriate indication channel maps
    elif fault_code == EFaultCode.FAULT_CODE_MULTIPLE_INDICATION and fault_subcode:
        if fault_subcode == ESubFaultMultipleIndication.SUBFLT_MULTIND_GREEN_YELLOW:
            green_indications_chmap = set_bit(green_indications_chmap, first_fault_channel)
            yellow_indications_chmap = set_bit(yellow_indications_chmap, first_fault_channel)
        elif fault_subcode == ESubFaultMultipleIndication.SUBFLT_MULTIND_GREEN_RED:
            green_indications_chmap = set_bit(green_indications_chmap, first_fault_channel)
            red_indications_chmap = set_bit(red_indications_chmap, first_fault_channel)
        elif fault_subcode == ESubFaultMultipleIndication.SUBFLT_MULTIND_YELLOW_RED:
            yellow_indications_chmap = set_bit(yellow_indications_chmap, first_fault_channel)
            red_indications_chmap = set_bit(red_indications_chmap, first_fault_channel)
    
    return {
        "fault_id": fault_id,
        "entry_timestamp": current_time,
        "config_id_in_use": 1,
        "data_key_crc": 0xA0B1,
        "model": "Synapse-ITS MMU2-16LEip",
        "serial_number": "12345678901234567890",
        "monitor_id": "4th & Main",     # Taken from the spreadsheet
        "user_id": "Asset ID: 256493",  # Taken from the spreadsheet
        "measurement_log_frequency": log_frequency,
        "sequence_log_frequency": log_frequency,
        "measurement_log_entry_count": measurement_count,
        "sequence_log_entry_count": sequence_count,
        "fault_code": fault_code.value,
        "fault_subcode": fault_subcode.value if fault_subcode else 0,
        "diagnostic_fault": False,
        "diagnostic_code": 0,
        "channels_chmap": channels_chmap,
        "reds_on_chmap": reds_on_chmap,
        "yellows_on_chmap": yellows_on_chmap,
        "greens_on_chmap": greens_on_chmap,
        "walks_on_chmap": walks_on_chmap,
        "red_indications_chmap": red_indications_chmap,
        "yellow_indications_chmap": yellow_indications_chmap,
        "green_indications_chmap": green_indications_chmap,
        "fault_time_cs": fault_time_cs,
        "line_frequency": 60.0,
        "temperature_degf": 98.5
    }

def parse_las_file(file_path, fault_id, fault_time_cs):
    # Parse the XML file
    tree = ET.parse(file_path)
    root = tree.getroot()
    
    # Get the total duration from the first channel's centiseconds attribute
    first_channel = root.find('.//channel')
    if first_channel is None:
        raise ValueError("No channels found in LAS file")
    
    step_time_cs = 10  # 10 centiseconds per step
    # Revised to now stop at the fault time, including that last step
    # total_duration_cs = int(first_channel.get('centiseconds', '0'))
    total_duration_cs = fault_time_cs + step_time_cs
    
    # Initialize lists for channel types
    channel_types = {
        'R': [],  # Red channels
        'Y': [],  # Yellow channels
        'G': [],  # Green channels
        'W': [],  # Walk channels
        'O': []   # Other channels
    }
    
    # Process each channel element
    for channel in root.findall('.//channel'):
        name = channel.get('name', '')
        parts = name.split()
        if len(parts) == 2 and parts[0].isdigit() and parts[1] in ['R', 'Y', 'G', 'W']:
            channel_num = int(parts[0])
            channel_type = parts[1].upper()
            channel_types[channel_type].append((channel_num, channel))
        # Special handling for channels "RED EN" and "24V MON"
        # Use channel_type 'O' for other channels and channel_num 0
        if channel.get('name') == 'RED EN' or channel.get('name') == '24V MON' \
                or channel.get('name') == 'RESET' or channel.get('name') == 'EXT WD':
            channel_type = 'O'
            channel_num = 0
            channel_types[channel_type].append((channel_num, channel))

    # Generate measurement log entries for each time step
    measurement_entries = []
    sequence_entries = []
    current_entry_id = 0
    current_period_count = 1
    
    # Initialize previous state
    prev_reds_chmap = 0
    prev_yellows_chmap = 0
    prev_greens_chmap = 0
    prev_walks_chmap = 0
    
    for step in range(0, total_duration_cs, step_time_cs):
        # Initialize channel bitmaps for this step
        reds_on_normal_chmap = 0
        yellows_on_normal_chmap = 0
        greens_on_normal_chmap = 0
        walks_on_normal_chmap = 0
        
        # Initialize channel lists for this step
        red_channels = []
        yellow_channels = []
        green_channels = []
        walk_channels = []
        
        # Process each channel type
        for channel_type, channels in channel_types.items():
            for channel_num, channel in channels:
                if channel_num > 16:
                    continue    # MMU only has 16 channels, skip any beyond that
                voltage = get_channel_voltage_at_time(channel, step)
                channel_entry = {
                    "channel": channel_num,
                    "voltage_v": voltage
                }
                
                # Add to appropriate list and update bitmap
                if channel_type == 'R':
                    red_channels.append(channel_entry)
                    if voltage > 90:
                        reds_on_normal_chmap = set_bit(reds_on_normal_chmap, channel_num)
                elif channel_type == 'Y':
                    yellow_channels.append(channel_entry)
                    if voltage > 90:
                        yellows_on_normal_chmap = set_bit(yellows_on_normal_chmap, channel_num)
                elif channel_type == 'G':
                    green_channels.append(channel_entry)
                    if voltage > 90:
                        greens_on_normal_chmap = set_bit(greens_on_normal_chmap, channel_num)
                elif channel_type == 'W':
                    walk_channels.append(channel_entry)
                    if voltage > 90:
                        walks_on_normal_chmap = set_bit(walks_on_normal_chmap, channel_num)

        # Create measurement log entry for this step
        entry = {
            "fault_id": fault_id,
            # For the entry_id, we need to start at 0 at the fault time and work back
            "entry_id": ((fault_time_cs  - step) // step_time_cs),
            "mains_ac_volts": 121.5,
            "reds_on_normal_chmap": reds_on_normal_chmap,
            "yellows_on_normal_chmap": yellows_on_normal_chmap,
            "greens_on_normal_chmap": greens_on_normal_chmap,
            "walks_on_normal_chmap": walks_on_normal_chmap,
            "reds_on_lackofsignal_chmap": reds_on_normal_chmap,
            "yellows_on_lackofsignal_chmap": yellows_on_normal_chmap,
            "greens_on_lackofsignal_chmap": greens_on_normal_chmap,
            "walks_on_lackofsignal_chmap": walks_on_normal_chmap,
            "reds_on_current_chmap": 0,
            "yellows_on_current_chmap": 0,
            "greens_on_current_chmap": 0,
            "walks_on_current_chmap": 0,
            "reds_field_check_chmap": 0,
            "yellows_field_check_chmap": 0,
            "greens_field_check_chmap": 0,
            "walks_field_check_chmap": 0,
            "red_channels": red_channels,
            "yellow_channels": yellow_channels,
            "green_channels": green_channels,
            "walk_channels": walk_channels,
            "input_states": {
                "monitor_24v_inhibit": False,
                "monitor_24v_1": True,
                "monitor_24v_2": True,
                "controller_voltage": False,
                "type_select": True,
                "red_enable": True,
                "external_reset": False,
                "port1_disable": False,
                "program_card_inserted": False,
                "local_flash": False,
                "external_watchdog": False,
                "alarm": False
            }
        }
        # See if we have the "other" channels with channel_num 0
        if 'O' in channel_types:
            other_channels = channel_types['O']
            # if we have "24V MON", set monitor_24v_1 to the voltage
            for channel_num, channel in other_channels:
                if channel.get('name') == '24V MON':
                    entry["monitor_24v_1_volts"] = get_channel_voltage_at_time(channel, step)
                elif channel.get('name') == 'RED EN':
                    entry["red_enabled_volts"] = get_channel_voltage_at_time(channel, step)
                elif channel.get('name') == 'RESET':
                    entry["external_reset_volts"] = get_channel_voltage_at_time(channel, step)
                elif channel.get('name') == 'EXT WD':
                    entry["external_watchdog_volts"] = get_channel_voltage_at_time(channel, step)

        # Insert the new entry at the beginning of the list, to reverse the chronological order
        measurement_entries.insert(0, entry)
        
        # Check if any channel maps have changed
        if (reds_on_normal_chmap != prev_reds_chmap or
            yellows_on_normal_chmap != prev_yellows_chmap or
            greens_on_normal_chmap != prev_greens_chmap or
            walks_on_normal_chmap != prev_walks_chmap):
            
            # Reset period_count
            current_period_count = 1

            # Create new sequence entry
            sequence_entry = create_sequence_log_entry(
                fault_id,
                current_entry_id,
                current_period_count,
                reds_on_normal_chmap,
                yellows_on_normal_chmap,
                greens_on_normal_chmap,
                walks_on_normal_chmap
            )
            # Insert the new sequence entry at the beginning of the list, to reverse the chronological order
            sequence_entries.insert(0, sequence_entry)
            
            # Update state for next iteration
            prev_reds_chmap = reds_on_normal_chmap
            prev_yellows_chmap = yellows_on_normal_chmap
            prev_greens_chmap = greens_on_normal_chmap
            prev_walks_chmap = walks_on_normal_chmap
            
            # Increment entry_id 
            current_entry_id += 1

        else:
            # Update the first (latest) sequence entry's period_count
            current_period_count += 1
            if sequence_entries:
                sequence_entries[0]["period_count"] = current_period_count

    # To simulate the extra length of the sequence log, add 10 to the last (oldest) period_count
    if sequence_entries:
        sequence_entries[-1]["period_count"] += 10

    # Now revise the sequence_entries' entry_id to start at 0 at the fault time and work back
    current_entry_id = 0
    for entry in sequence_entries:
        entry["entry_id"] = current_entry_id
        current_entry_id += 1
    
    return measurement_entries, sequence_entries

def parse_light_o_rama_xls(excel_file: str) -> List[Dict[str, Any]]:
    """Parse the Light-O-Rama Excel file and return a list of entries."""
    try:
        # Read the Excel file
        df = pd.read_excel(excel_file)
        
        entries = []
        for idx, row in df.iterrows():
            # Get the color value
            color = row.get("Color", 0)
            
            # Create the entry
            entry = {
                "entry_id": idx + 1,
                "start_centisecond": int(row.get("Start Centisecond", 0)),
                "end_centisecond": int(row.get("End Centisecond", 0)),
                "color": int(color)
            }
            
            # Add red_enabled_volts if color > 1000
            if color > 1000:
                entry["red_enabled_volts"] = 120.0
            
            entries.append(entry)
            
        return entries
        
    except Exception as e:
        print(f"Error parsing Excel file: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Parse LAS file and create FaultMeasurementLogEntryMmu and FaultSequenceLogEntryMmu entries')
    parser.add_argument('filename', help='Path to the LAS file to parse')
    parser.add_argument('--fault-id', type=int, default=1, help='Fault ID to use in the output (default: 1)')
    parser.add_argument('--prefix', required=True, help='Prefix for the output filename')
    parser.add_argument('--fault-code', required=True, choices=[e.name for e in EFaultCode], 
                       help='Fault code from EFaultCode enum')
    parser.add_argument('--fault-subcode', choices=[e.name for e in ESubFaultMultipleIndication] + 
                       [e.name for e in ESubFaultLackOfSignal],
                       help='Optional fault subcode from ESubFaultMultipleIndication or ESubFaultLackOfSignal')
    parser.add_argument('--first-fault-channel', type=int, required=True, 
                       help='First channel involved in the fault (1-based, where Channel 1 is the LSBit)')
    parser.add_argument('--fault-time-cs', type=int, required=True,
                       help='Time of the fault in centiseconds')
    
    # Parse command line arguments
    args = parser.parse_args()
    
    # Convert fault code and subcode strings to enum values
    fault_code = EFaultCode[args.fault_code]
    fault_subcode = None
    if args.fault_subcode:
        # Try to find the subcode in both enums
        try:
            fault_subcode = ESubFaultMultipleIndication[args.fault_subcode]
        except KeyError:
            try:
                fault_subcode = ESubFaultLackOfSignal[args.fault_subcode]
            except KeyError:
                raise ValueError(f"Invalid fault subcode: {args.fault_subcode}")
    
    # Parse the LAS file using the provided filename and fault_id
    measurement_entries, sequence_entries = parse_las_file(args.filename, args.fault_id, args.fault_time_cs)
    
    # Get the directory where the script is located
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Create output filenames in the script directory
    measurements_filename = os.path.join(script_dir, f"{args.prefix}_Measurements_Log.json")
    sequence_filename = os.path.join(script_dir, f"{args.prefix}_Sequence_Log.json")
    header_filename = os.path.join(script_dir, f"{args.prefix}_Fault_Header_Log.json")
    
    # Write the measurement log entries to a file
    with open(measurements_filename, 'w') as f:
        json.dump(measurement_entries, f, indent=2)
    
    # Write the sequence log entries to a file
    with open(sequence_filename, 'w') as f:
        json.dump({"log_entry": sequence_entries}, f, indent=2)
    
    # Create and write the fault header log entry
    header_entry = create_fault_header_log_entry(
        args.fault_id, 
        10,  # Using 10 as step_time_cs
        len(measurement_entries),
        len(sequence_entries),
        fault_code,
        fault_subcode,
        args.first_fault_channel,
        args.fault_time_cs,
        measurement_entries
    )
    with open(header_filename, 'w') as f:
        json.dump({"log_entry": [header_entry]}, f, indent=2)
    
    print(f"Wrote {len(measurement_entries)} measurement log entries to {measurements_filename}")
    print(f"Wrote {len(sequence_entries)} sequence log entries to {sequence_filename}")
    print(f"Wrote fault header log to {header_filename}")

if __name__ == "__main__":
    main() 