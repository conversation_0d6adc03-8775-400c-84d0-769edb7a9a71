#!/usr/bin/env python3

"""
Parse MMU Permissive Files and Data Key Files from Excel and generate JSON output.

Usage:
    python parse_mmu_datakeys.py <excel_file>

Example:
    python parse_mmu_datakeys.py "NextGen MMU Sample Logs.xlsx"
    # This will create JSON files in the same directory as the script based on the Permissive FileName and File_Name columns
"""

import argparse
import json
import os
import sys
import pandas as pd
from typing import List, Dict, Any

def compute_crc16(data: bytes, seed: int = 0xFFFF) -> int:
    """Compute CRC-16 over the given data using the specified seed value.
    
    Args:
        data: The bytes to compute the CRC over
        seed: The initial CRC value (default: 0xFFFF)
        
    Returns:
        The 16-bit CRC value
    """
    crc = seed
    for byte in data:
        crc ^= (byte << 8)
        for _ in range(8):
            if crc & 0x8000:
                crc = (crc << 1) ^ 0x1021
            else:
                crc = crc << 1
            crc &= 0xFFFF
    return crc

def parse_permissive_files_sheet(excel_file: str) -> Dict[str, List[int]]:
    """Parse the Permissive Files sheet from the Excel file and return a dictionary of file contents."""
    try:
        # Read the Permissive Files sheet
        df = pd.read_excel(excel_file, sheet_name="Permissive Files")
        
        # Dictionary to store file contents
        file_contents = {}
        
        # Process each row
        for _, row in df.iterrows():
            filename = row.get("Permissive FileName", "")
            if not filename:
                continue
                
            # Initialize byte array with 63 bytes
            bytes_array = [0] * 63
            bit_idx = 0
            
            # Process each bit column in this triangular pattern (1 & 2, 1 & 3, through 1 & 32, 2 & 3, through 31 & 32)
            for i in range(1, 31):
                for j in range(i+1, 32):
                    col_name = f"{i} & {j}"
                    if col_name in row:
                        value = row[col_name]
                        if pd.notna(value):
                            # Calculate byte index and bit position
                            bit_num = bit_idx
                            byte_idx = bit_num // 8  # Which byte this bit goes into
                            bit_pos = (bit_idx % 8)  # Which bit position (0-7, LSB first)
                            
                            # Set the bit in the byte array
                            if value == 1:
                                bytes_array[byte_idx] |= (1 << bit_pos)
                    bit_idx += 1
            
            # Store the byte array in the dictionary
            file_contents[filename] = bytes_array
            
        return file_contents
        
    except Exception as e:
        print(f"Error parsing Permissive Files sheet: {e}", file=sys.stderr)
        sys.exit(1)

def create_binary_data(data_key: Dict[str, Any]) -> bytes:
    """Convert the data key dictionary to a 512-byte binary buffer."""
    # Initialize a 512-byte buffer with zeros
    buffer = bytearray(512)
    
    # Helper function to write a value to the buffer
    # The offset is given as a 1-based index, since that is how the data struct comments are written
    # This function handles different types of values:
    # - Strings: If the value is a string, it can be a hex string (starting with '0x') or a regular string.
    #   - If it's a hex string, it converts the string to an integer.
    #   - If it's a regular string, it converts the string to bytes and pads with zeros to ensure the correct size.
    # - Lists: If the value is a list, it assumes each item is a hex string and converts them to integers.
    # - Numeric values: If the value is a numeric type (int, float), it writes the value directly to the buffer.
    def write_value(offset: int, value: Any, size: int = 1):
        offset -= 1
        if isinstance(value, str):
            if value.startswith('0x'):
                # Handle hex string
                value = int(value, 16)
            else:
                # Handle regular string - convert to bytes, replace trailing spaces with nulls and pad with zeros
                value_bytes = value.encode('ascii').rstrip(b' ') + b'\x00' * (size - len(value.encode('ascii').rstrip(b' ')))
                buffer[offset:offset + size] = value_bytes
                return
        elif isinstance(value, list):
            # Handle arrays
            for i, item in enumerate(value):
                if isinstance(item, str) and item.startswith('0x'):
                    buffer[offset + i] = int(item, 16)
            return
        
        # Handle numeric values
        if size == 1:
            buffer[offset] = value
        elif size == 2:
            buffer[offset] = (value >> 8) & 0xFF
            buffer[offset + 1] = value & 0xFF
        elif size == 4:
            buffer[offset] = (value >> 24) & 0xFF
            buffer[offset + 1] = (value >> 16) & 0xFF
            buffer[offset + 2] = (value >> 8) & 0xFF
            buffer[offset + 3] = value & 0xFF
    
    # Write each field to the buffer
    write_value(1, int(data_key['Version'], 16))  # Version (1 byte)
    
    # Permissives (63 bytes)
    if data_key['Permissives']:
        for i, value in enumerate(data_key['Permissives']):
            write_value(2 + i, value)
    
    # Write all other fields
    write_value(64, int(data_key['LOS_En'], 16), 4)
    write_value(68, int(data_key['Dark_Map_1'], 16), 4)
    write_value(72, int(data_key['Dark_Map_2'], 16), 4)
    write_value(76, int(data_key['Dark_Map_3'], 16), 4)
    write_value(80, int(data_key['Dark_Map_4'], 16), 4)
    write_value(84, int(data_key['GY_En'], 16), 4)
    write_value(88, int(data_key['YR_En'], 16), 4)
    write_value(92, int(data_key['GR_En'], 16), 4)
    write_value(96, int(data_key['Min_Y_En'], 16), 4)
    write_value(100, int(data_key['Min_YR_En'], 16), 4)
    write_value(104, int(data_key['Y_Dis'], 16), 4)
    write_value(108, int(data_key['FDW_En'], 16), 4)  # FDW_En (new field) 108 - Flashing Dont Walk Enables
    write_value(112, int(data_key['Cur_Sns_R_En'], 16), 4)
    write_value(116, int(data_key['Cur_Sns_Y_En'], 16), 4)
    write_value(120, int(data_key['Cur_Sns_G_En'], 16), 4)
    
    # Current sense levels (32 bytes each)
    for i in range(32):
        write_value(124 + i, int(data_key['Cur_Sns_Lvl_R'][i], 16))
    for i in range(32):
        write_value(156 + i, int(data_key['Cur_Sns_Lvl_Y'][i], 16))
    for i in range(32):
        write_value(188 + i, int(data_key['Cur_Sns_Lvl_G'][i], 16))
    
    write_value(220, int(data_key['Fld_Chk_R_En'], 16), 4)
    write_value(224, int(data_key['Fld_Chk_Y_En'], 16), 4)
    write_value(228, int(data_key['Fld_Chk_G_En'], 16), 4)
    write_value(232, int(data_key['Fld_Chk_W_En'], 16), 4)
    write_value(236, data_key['Min_Flash_Time'])
    write_value(237, int(data_key['UnitSetting1'], 16))
    write_value(238, int(data_key['UnitSetting2'], 16))
    write_value(239, int(data_key['UnitSetting3'], 16))
    write_value(240, int(data_key['UnitSetting4'], 16))
    write_value(241, int(data_key['PC_Latches'], 16))
    for i in range(9):
        write_value(242 + i, int(data_key['Reserved'][i], 16))
    write_value(252, int(data_key['Ch_En'], 16), 4)
    
    # Monitor_ID and User_ID (40 bytes each)
    write_value(256, data_key['Monitor_ID'], 40)
    write_value(296, data_key['User_ID'], 40)
    
    write_value(336, int(data_key['Enet_Control'], 16))
    write_value(337, data_key['Enet_IP_4'])
    write_value(338, data_key['Enet_IP_3'])
    write_value(339, data_key['Enet_IP_2'])
    write_value(340, data_key['Enet_IP_1'])
    write_value(341, data_key['Enet_Sub_4'])
    write_value(342, data_key['Enet_Sub_3'])
    write_value(343, data_key['Enet_Sub_2'])
    write_value(344, data_key['Enet_Sub_1'])
    write_value(345, data_key['Enet_Gate_4'])
    write_value(346, data_key['Enet_Gate_3'])
    write_value(347, data_key['Enet_Gate_2'])
    write_value(348, data_key['Enet_Gate_1'])
    
    # Enet_Host_Name (15 bytes)
    write_value(349, data_key['Enet_Host_Name'], 15)
    
    write_value(363, int(data_key['FYA_Unit_Settings'], 16))
    
    # FYA_Output_Group1 (7 bytes)
    for i, value in enumerate(data_key['FYA_Output_Group1']):
        write_value(364 + i, int(value, 16))
    
    # FYA_Output_Group2 (7 bytes)
    for i, value in enumerate(data_key['FYA_Output_Group2']):
        write_value(371 + i, int(value, 16))
    
    # FYA_Output_Group3 (7 bytes)
    for i, value in enumerate(data_key['FYA_Output_Group3']):
        write_value(378 + i, int(value, 16))
    
    # FYA_Output_Group4 (7 bytes)
    for i, value in enumerate(data_key['FYA_Output_Group4']):
        write_value(385 + i, int(value, 16))
    
    write_value(392, data_key['App_Spec_Buf_Sz'])
    
    # App_Spec_Buf (117 bytes)
    for i, value in enumerate(data_key['App_Spec_Buf']):
        write_value(393 + i, int(value, 16))
    
    # Compute CRC-16 over the first 510 bytes
    crc = compute_crc16(bytes(buffer[:510]))
    
    # Write CRC to the last two bytes (MSB first, then LSB)
    buffer[510] = (crc >> 8) & 0xFF  # MSB
    buffer[511] = crc & 0xFF         # LSB
    
    return bytes(buffer)

def parse_data_key_files_sheet(excel_file: str, script_dir: str) -> None:
    """Parse the Data Key Files sheet and create JSON files based on the td_PC_DK_Mem structure."""
    try:
        # Read the Data Key Files sheet
        df = pd.read_excel(excel_file, sheet_name="Data Key Files")
        
        # Process each row
        for _, row in df.iterrows():
            filename = row.get("File_Name", "")
            if not filename:
                continue
                
            # Create the data key structure
            data_key = {
                "Version": f"0x{int(str(row.get('Version', '0')).replace('0x', ''), 16):02x}",
                "Permissives": None,  # Will be loaded from the permissive file
                "LOS_En": f"0x{int(str(row.get('LOS_Enables', '0')).replace('0x', ''), 16):08x}",
                "Dark_Map_1": f"0x{int(str(row.get('Dark_Map_1', '0')).replace('0x', ''), 16):08x}",
                "Dark_Map_2": f"0x{int(str(row.get('Dark_Map_2', '0')).replace('0x', ''), 16):08x}",
                "Dark_Map_3": f"0x{int(str(row.get('Dark_Map_3', '0')).replace('0x', ''), 16):08x}",
                "Dark_Map_4": f"0x{int(str(row.get('Dark_Map_4', '0')).replace('0x', ''), 16):08x}",
                "GY_En": f"0x{int(str(row.get('GY_Enables', '0')).replace('0x', ''), 16):08x}",
                "YR_En": f"0x{int(str(row.get('YR_Enables', '0')).replace('0x', ''), 16):08x}",
                "GR_En": f"0x{int(str(row.get('GR_Enables', '0')).replace('0x', ''), 16):08x}",
                "Min_Y_En": f"0x{int(str(row.get('Min_Y_Enables', '0')).replace('0x', ''), 16):08x}",
                "Min_YR_En": f"0x{int(str(row.get('Min_YR_Enables', '0')).replace('0x', ''), 16):08x}",
                "Y_Dis": f"0x{int(str(row.get('Y_Disables', '0')).replace('0x', ''), 16):08x}",
                "FDW_En": f"0x{int(str(row.get('FDW_Enables', '0')).replace('0x', ''), 16):08x}",  # Flashing Dont Walk Enables
                "Cur_Sns_R_En": f"0x{int(str(row.get('Cur_Sns_R_Enables', '0')).replace('0x', ''), 16):08x}",
                "Cur_Sns_Y_En": f"0x{int(str(row.get('Cur_Sns_Y_Enables', '0')).replace('0x', ''), 16):08x}",
                "Cur_Sns_G_En": f"0x{int(str(row.get('Cur_Sns_G_Enables', '0')).replace('0x', ''), 16):08x}",
                "Cur_Sns_Lvl_R": [f"0x00" for _ in range(32)],
                "Cur_Sns_Lvl_Y": [f"0x00" for _ in range(32)],
                "Cur_Sns_Lvl_G": [f"0x00" for _ in range(32)],
                "Fld_Chk_R_En": f"0x{int(str(row.get('Fld_Chk_R_Enables', '0')).replace('0x', ''), 16):08x}",
                "Fld_Chk_Y_En": f"0x{int(str(row.get('Fld_Chk_Y_Enables', '0')).replace('0x', ''), 16):08x}",
                "Fld_Chk_G_En": f"0x{int(str(row.get('Fld_Chk_G_Enables', '0')).replace('0x', ''), 16):08x}",
                "Fld_Chk_W_En": f"0x{int(str(row.get('Fld_Chk_W_Enables', '0')).replace('0x', ''), 16):08x}",
                "Min_Flash_Time": int(row.get('Min_Flash_Time', 0)),
                "UnitSetting1": f"0x{int(str(row.get('UnitSetting1', '0')).replace('0x', ''), 16):02x}",
                "UnitSetting2": f"0x{int(str(row.get('UnitSetting2', '0')).replace('0x', ''), 16):02x}",
                "UnitSetting3": f"0x{int(str(row.get('UnitSetting3', '0')).replace('0x', ''), 16):02x}",
                "UnitSetting4": f"0x{int(str(row.get('UnitSetting4', '0')).replace('0x', ''), 16):02x}",
                "PC_Latches": f"0x{int(str(row.get('PC_Latches', '0')).replace('0x', ''), 16):02x}",
                "Reserved": [f"0x00" for _ in range(9)],
                "Ch_En": f"0x{int(str(row.get('Ch_Enables', '0')).replace('0x', ''), 16):08x}",
                "Monitor_ID": str(row.get('Monitor_ID', '')).ljust(40)[:40],
                "User_ID": str(row.get('User_ID', '')).ljust(40)[:40],
                "Enet_Control": f"0x{int(str(row.get('Enet_Control', '0')).replace('0x', ''), 16):02x}",
                "Enet_IP_4": int(row.get('Enet_IP_4', 0)),
                "Enet_IP_3": int(row.get('Enet_IP_3', 0)),
                "Enet_IP_2": int(row.get('Enet_IP_2', 0)),
                "Enet_IP_1": int(row.get('Enet_IP_1', 0)),
                "Enet_Sub_4": int(row.get('Enet_Sub_4', 0)),
                "Enet_Sub_3": int(row.get('Enet_Sub_3', 0)),
                "Enet_Sub_2": int(row.get('Enet_Sub_2', 0)),
                "Enet_Sub_1": int(row.get('Enet_Sub_1', 0)),
                "Enet_Gate_4": int(row.get('Enet_Gate_4', 0)),
                "Enet_Gate_3": int(row.get('Enet_Gate_3', 0)),
                "Enet_Gate_2": int(row.get('Enet_Gate_2', 0)),
                "Enet_Gate_1": int(row.get('Enet_Gate_1', 0)),
                "Enet_Host_Name": str(row.get('Enet_Host_Name', '')).ljust(15)[:15],
                "FYA_Unit_Settings": f"0x{int(str(row.get('FYA_Unit_Settings', '0')).replace('0x', ''), 16):02x}",
                "FYA_Output_Group1": [f"0x{int(str(x).replace('0x', ''), 16):02x}" for x in row.get('FYA_Output_Group1', '0,0,0,0,0,0,0').split(',')],
                "FYA_Output_Group2": [f"0x{int(str(x).replace('0x', ''), 16):02x}" for x in row.get('FYA_Output_Group2', '0,0,0,0,0,0,0').split(',')],
                "FYA_Output_Group3": [f"0x{int(str(x).replace('0x', ''), 16):02x}" for x in row.get('FYA_Output_Group3', '0,0,0,0,0,0,0').split(',')],
                "FYA_Output_Group4": [f"0x{int(str(x).replace('0x', ''), 16):02x}" for x in row.get('FYA_Output_Group4', '0,0,0,0,0,0,0').split(',')],
                "App_Spec_Buf_Sz": int(row.get('App_Spec_Buf_Sz', 0)),
                "App_Spec_Buf": [f"0x{int(str(x).replace('0x', ''), 16):02x}" for x in row.get('App_Spec_Buf', '').split(',')] + [f"0x00" for _ in range(117 - len(row.get('App_Spec_Buf', '').split(',')))],
                "FCS_MSB": "0x00",  # Will be computed
                "FCS_LSB": "0x00"   # Will be computed
            }
            
            # Load the permissive file
            permissive_file = row.get("Permissives", "")
            if permissive_file:
                permissive_path = os.path.join(script_dir, f"{permissive_file}.json")
                # print(f"Loading permissive file: {permissive_path}")
                try:
                    if not os.path.exists(permissive_path):
                        print(f"Permissive file not found: {permissive_path}", file=sys.stderr)
                        continue
                        
                    with open(permissive_path, 'r') as f:
                        permissive_data = json.load(f)
                        if not isinstance(permissive_data, list):
                            print(f"Invalid permissive file format in {permissive_path}: expected array", file=sys.stderr)
                            continue
                        data_key["Permissives"] = permissive_data
                except json.JSONDecodeError as e:
                    print(f"Error decoding permissive file {permissive_file}.json: {e}", file=sys.stderr)
                    continue
                except Exception as e:
                    print(f"Error loading permissive file {permissive_file}.json: {e}", file=sys.stderr)
                    continue
            
            # Write the JSON file
            output_file = os.path.join(script_dir, f"{filename}.json")
            try:
                with open(output_file, 'w') as f:
                    json.dump(data_key, f, indent=2)
                print(f"Successfully wrote data key to {output_file}")
            except Exception as e:
                print(f"Error writing {filename}.json: {e}", file=sys.stderr)
                continue
            
            # Write the binary file
            binary_file = os.path.join(script_dir, f"{filename}.bin")
            try:
                binary_data = create_binary_data(data_key)
                with open(binary_file, 'wb') as f:
                    f.write(binary_data)
                print(f"Successfully wrote binary data to {binary_file}")
            except Exception as e:
                print(f"Error writing {filename}.bin: {e}", file=sys.stderr)
                continue
            
    except Exception as e:
        print(f"Error parsing Data Key Files sheet: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Parse MMU Permissive Files and Data Key Files from Excel and generate JSON output',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Parse an Excel file and generate JSON files
    python parse_mmu_datakeys.py "NextGen MMU Sample Logs.xlsx"

The output will be saved as JSON files in the same directory as the script, with filenames based on the Permissive FileName and File_Name columns
        """
    )
    parser.add_argument('excel_file', help='Path to the Excel file to parse')
    
    args = parser.parse_args()
    
    # Get script directory for output files
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Parse Permissive Files sheet
    file_contents = parse_permissive_files_sheet(args.excel_file)
    
    # Write each permissive file's contents to a JSON file
    for filename, bytes_array in file_contents.items():
        output_file = os.path.join(script_dir, f'{filename}.json')
        try:
            # Convert bytes to hex strings
            hex_array = [f"0x{byte:02x}" for byte in bytes_array]
            with open(output_file, 'w') as f:
                json.dump(hex_array, f)
            print(f"Successfully wrote {len(bytes_array)} bytes to {output_file}")
        except Exception as e:
            print(f"Error writing {filename}.json: {e}", file=sys.stderr)
            sys.exit(1)
    
    # Parse Data Key Files sheet
    parse_data_key_files_sheet(args.excel_file, script_dir)

if __name__ == '__main__':
    main() 