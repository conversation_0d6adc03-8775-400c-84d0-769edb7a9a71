 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   CMD_RESP_COMMS
#   nanopb options file for cmd_resp_comms.proto

# RespAuthStatus
#   Max String lengths
cmd_resp_comms.RespAuthStatus.existing_connection_name   max_size:64

# CmdChunkTest
#   Max data lengths
cmd_resp_comms.CmdChunkTest.data   max_size: 4096

# RespChunkTest
#   Max data lengths
cmd_resp_comms.RespChunkTest.data_echo   max_size: 4096
