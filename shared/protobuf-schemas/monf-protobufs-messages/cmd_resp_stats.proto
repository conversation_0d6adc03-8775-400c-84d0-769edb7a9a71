/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  CMD_RESP_STATS
    Commands and responses for retrieving operational statistics.
*/
syntax = "proto3";
package cmd_resp_stats;

//***************************************************************************************** IMPORTS
import "basic.proto";
import "mon_logs.proto";
import "settings.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  CmdReadPort1Statistics is used to retrieve statistics on the controller port1 interface  */
message CmdReadPort1Statistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadPort1Statistics


/*  RespReadPort1Statistics returns statistics on the controller port1 interface.  
    The data comes from the Main processor response to the command 0x10 Retrieve Port 1 Statistics */
message RespReadPort1Statistics {

    // Frame reception counters
    uint32 frame_1_rx_count = 1;    
    uint32 frame_3_rx_count = 2;
    uint32 frame_9_rx_count = 3;

    // Frames where the length was less than expected.
    uint32 short_frame_error_count = 4;

    // The control byte value was unrecognized
    uint32 control_byte_error_count = 5;

    // frame CRC errors
    uint32 crc_error_count = 6;

    // Bus went idle without frame ending byte
    uint32 idle_state_error_count = 7;

    // Bus data started without frame start byte  
    uint32 framing_error_count = 8;

    // Frames where the length was greater than expected.
    uint32 long_frame_error_count = 9;

    // Controller 100 ms communication timeout
    uint32 frame_timeout_error_count = 10;

    // Count of all unknown frames since last cleared
    uint32 unknown_frame_error_count = 11;   

    // Most recent unknown frame number
    uint32 unknown_frame_number = 12;

    // timestamp when these statistics were last cleared
    basic.LocalDateTime last_clear_timestamp = 13;

} // RespReadPort1Statistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadDataKeyStatistics is used to retrieve statistics on the controller port1 interface  */
message CmdReadDataKeyStatistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadDataKeyStatistics


/*  RespReadDataKeyStatistics returns statistics on the Data Key or Program Card, depending on which is used
    in the monitor.  
    The data comes from the Main processor response to the command 0x13 Retrieve Program Card / Data Key Statistics */
message RespReadDataKeyStatistics {

    // This is the source of the data
    settings.EConfigDataLocation source = 1;     

    // total count of reads from the data key / program card
    uint32 read_count = 2;

    // Count of the total seconds the key has been removed, over all key removals
    uint32 removed_seconds = 3;

    // count of CRC errors when reading the data key / program card
    uint32 crc_error_count = 4;

    // count of faults caused by data key / program card read errors
    uint32 faults_from_bad_reads_count = 5;

    // Field of errors that have occurred
    mon_logs.DataKeyErrorCodeBitmap errors = 6;

    // timestamp when these statistics were last cleared
    basic.LocalDateTime last_clear_timestamp = 7;

} // RespReadDataKeyStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadMainToIsolatedCommStatistics is used to retrieve statistics on the serial interface 
    between the Main and Isolated microcontrollers */
message CmdReadMainToIsolatedCommStatistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadMainToIsolatedCommStatistics


/*  RespReadMainToIsolatedCommStatistics returns statistics on the inter-MCU communication bus between the Main
    and Isolated MCUs.  
    The data comes from the Main processor response to the command 
    0x14 Retrieve Main to Isolated processor Communication Statistics */
message RespReadMainToIsolatedCommStatistics {

    // total count of commands messages sent from the Mains MCU to the Isolated
    uint32 sent_count = 1;

    // total count of response messages received by the Mains MCU from the Isolated
    uint32 received_count = 2;   

    // total count of unexpected messages (responses) received by the Mains MCU from the Isolated
    uint32 unexpected_received_count = 3;     

    // total count of messages (responses) received by the Mains MCU from the Isolated
    // with checksum errors
    uint32 checksum_errors_count = 4;

    // total count of messages (responses) received by the Mains MCU from the Isolated
    // with unexpected data length
    uint32 data_length_errors_count = 5;

    // total count of timeouts on response messages expected by the Mains MCU from the Isolated
    uint32 timeout_errors_count = 6;    

    // total count of times the Main MCU reset its UART due to communication errors
    uint32 uart_reset_count = 7;

    // total count of messages (responses) received by the Main MCU with framing errors
    uint32 bad_frame_count = 8;      

    // timestamp when these statistics were last cleared
    basic.LocalDateTime last_clear_timestamp = 9;

} // RespReadMainToIsolatedCommStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadMainToDisplayCommStatistics is used to retrieve statistics on the serial interface 
    between the Main and Display microcontrollers */
message CmdReadMainToDisplayCommStatistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadMainToDisplayCommStatistics


/*  RespReadMainToDisplayCommStatistics returns statistics on the inter-MCU communication bus between the Main
    and Display MCUs.  
    The data comes from the Main processor response to the command 
    0x1A Retrieve Main to Display processor Communication Statistics */
message RespReadMainToDisplayCommStatistics {

    // total count of commands messages sent from the Mains MCU to the Display
    uint32 sent_count = 1;

    // total count of response messages received by the Mains MCU from the Display
    uint32 received_count = 2;   

    // total count of unexpected messages (responses) received by the Mains MCU from the Display
    uint32 unexpected_received_count = 3;     

    // total count of messages (responses) received by the Mains MCU from the Display
    // with checksum errors
    uint32 checksum_errors_count = 4;

    // total count of messages (responses) received by the Mains MCU from the Display
    // with unexpected data length
    uint32 data_length_errors_count = 5;

    // total count of timeouts on response messages expected by the Mains MCU from the Display
    uint32 timeout_errors_count = 6;    

    // total count of times the Main MCU reset its UART due to communication errors
    uint32 uart_reset_count = 7;

    // total count of messages (responses) received by the Main MCU with framing errors
    uint32 bad_frame_count = 8;        

    // timestamp when these statistics were last cleared
    basic.LocalDateTime last_clear_timestamp = 9;

} // RespReadMainToDisplayCommStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadMainToCommsCommStatistics is used to retrieve statistics on the serial interface 
    between the Main and Isolated microcontrollers */
message CmdReadMainToCommsCommStatistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadMainToCommsCommStatistics


/*  RespReadMainToCommsCommStatistics returns statistics on the inter-MCU communication bus between the Main
    and Communications MCUs for the Main MCU side.  
    The data comes from the Main processor response to the command 
    0x15 Retrieve Main to Comms processor Comm Statistics */
message RespReadMainToCommsCommStatistics {

    // total count of response messages sent from the Main MCU to the Communications
    uint32 sent_count = 1;

    // total count of commands messages received by the Main MCU from the Communications
    uint32 received_count = 2;   

    // total count of unknown messages (commands) received by the Main MCU from the Communications
    uint32 unknown_received_count = 3;     

    // total count of messages (commands) received by the Main MCU from the Communications
    // with checksum errors
    uint32 checksum_errors_count = 4;

    // total count of messages (commands) received by the Main MCU from the Communications
    // with unexpected data length
    uint32 data_length_errors_count = 5;

    // total count of timeouts on ping messages expected by the Main MCU from the Communications
    uint32 ping_timeout_count = 6;

    // total count of times the Main MCU reset its UART due to communication errors
    uint32 uart_reset_count = 7;

    // total count of messages (commands) received by the Main MCU with framing errors
    uint32 bad_frame_count = 8;        

    // timestamp when these statistics were last cleared
    basic.LocalDateTime last_clear_timestamp = 9;

} // RespReadMainToCommsCommStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadCommsToMainCommStatistics is used to retrieve statistics on the serial interface 
    between the Main and Isolated microcontrollers */
message CmdReadCommsToMainCommStatistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadCommsToMainCommStatistics


/*  RespReadCommsToMainCommStatistics returns statistics on the inter-MCU communication bus between the Main
    and Communications MCUs for the Communications MCU side.  
    The data comes from the Comms processor */
message RespReadCommsToMainCommStatistics {

    // total count of commands messages sent from the Communications MCU to the Main
    uint32 sent_count = 1;

    // total count of response messages received by the Communications MCU from the Main
    uint32 received_count = 2;   

    // total count of unexpected messages (response) received by the Communications MCU from the Main
    uint32 unexpected_received_count = 3;     

    // total count of messages (response) received by the Communications MCU from the Main
    // with checksum errors
    uint32 checksum_errors_count = 4;

    // total count of messages (response) received by the Communications MCU from the Main
    // with unexpected data length
    uint32 data_length_errors_count = 5;

    // total count of timeouts on responses expected by the Communications MCU from the Main
    uint32 timeout_errors_count = 6;

    // total count of messages (responses) received by the Comms MCU with framing errors
    uint32 bad_frame_count = 7;

    // timestamp when these statistics were last cleared
    basic.LocalDateTime last_clear_timestamp = 8;

} // RespReadCommsToMainCommStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadFlashStatistics is used to retrieve statistics on the Main MCU internal flash memory */
message CmdReadFlashStatistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadFlashStatistics


/*  RespReadFlashStatistics
    The data comes from the Main processor response to the command 0x16 Retrieve Flash Read Statistics */
message RespReadFlashStatistics {

    settings.FlashAreaStatistics factory_area = 1;

    settings.FlashAreaStatistics general_area = 2;

    settings.FlashAreaStatistics header_area = 3;

} // RespReadFlashStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadWatchdogStatistics is used to retrieve statistics on the Main MCU watchdog counts */
message CmdReadWatchdogStatistics {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadWatchdogStatistics


/*  RespReadWatchdogStatistics - These are the counts of Main MCU task for the last window period
    The data comes from the Main processor response to the command 0x17 Retrieve Watchdog Counts */
message RespReadWatchdogStatistics {

    uint32 main_loop_count = 1;

    uint32 tick_1ms_count = 2;

    uint32 fault_processing_count = 3;

    uint32 adc_processing_count = 4;

    uint32 rms_calcuation_count = 5;

    // communications MCU
    uint32 comm_mcu_rx_count = 6;

    // isolated MCU
    uint32 iso_mcu_tx_count = 7;

    // Program Card or Data Key number of reads
    uint32 pcard_dkey_read_count = 8;

    // GPIO monitoring 
    uint32 get_inputs_count = 9;

    uint32 set_leds_count = 10;

    uint32 display_mcu_tx_count = 11;

    uint32 background_checksum_count = 12;

} // RespReadWatchdogStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdReadInternalSupplyVoltages is used to check the monitor internal voltage rails */
message CmdReadInternalSupplyVoltages {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadInternalSupplyVoltages


/*  RespReadInternalSupplyVoltages
    The data comes from the Main processor response to the command 0x18 Retrieve Internal Supplies */
message RespReadInternalSupplyVoltages {

    float ac_mains_present_volts = 1;

    basic.NowMinMaxFloat main_mcu_5v_volts = 2;

    basic.NowMinMaxFloat main_mcu_3v3_volts = 3;

    basic.NowMinMaxFloat main_mcu_neg3v3_volts = 4;

    basic.NowMinMaxFloat isolated_mcu_5v_volts = 5;

    basic.NowMinMaxFloat isolated_mcu_3v3_volts = 6;

} // RespReadInternalSupplyVoltages

//-------------------------------------------------------------------------------------------------

/*  CmdGetTimeDatesDst is used to retrieve the monitor date / time and daylight savings settings. */
message CmdGetTimeDatesDst {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdGetTimeDatesDst


/*  RespGetTimeDatesDst
    The data comes from the Main processor response to the command 0x19 Get Date/Time and Daylight Savings */
message RespGetTimeDatesDst {

    // date and time of the monitor Main MCU  
    basic.LocalDateTime present_date_time = 1;

    // whether daylights savings time is enabled or not
    bool dst_enabled = 2;

    // the daylight savings settings in use on the monitor (if enabled)
    basic.DaylightSavingsSettings dst_settings = 3;

} // RespGetTimeDatesDst

//-------------------------------------------------------------------------------------------------

/*  CmdClearStatistics is used to clear monitor statistics */
message CmdClearStatistics {

    // The statistics to clear
    settings.EMonitorStatistics statistics_to_clear = 1; 

} // CmdClearStatistics


/*  RespClearStatistics
    The data comes from the Main processor response(s) to the command(s) 
    0x90 Reset Port 1 Statistics
    0x93 Reset Reset Data Key Statistics
    0x94 Reset Main to Isolated processor Communication Statistics
    0x95 Reset Main to Comms processor Communication Statistics */
message RespClearStatistics {

    // The statistics that were cleared
    settings.EMonitorStatistics statistics_cleared = 1; 

    // timestamp when the statistics were cleared
    // If the command statistics_to_clear = STATISTICS_ALL, this is the timestamp of the
    // last set of statstics cleared.
    basic.LocalDateTime timestamp = 2;

} // RespClearStatistics

//-------------------------------------------------------------------------------------------------

/*  CmdSetTimeDatesDst is used to set the monitor date / time and daylight savings settings. 
    This will send command 0x99 Get Date/Time and Daylight Savings to the Main processor 

    NOTE: If the monitor is in a cabinet that has Port 1 communications, any setting of the current
          date and time will be overwritten within one second when date/time is broadcast on Port 1 
          (every one second).*/
message CmdSetTimeDatesDst {

    // date and time to set for the Main MCU  
    basic.LocalDateTime present_date_time = 1;

    // whether daylights savings time is enabled or not
    bool dst_enabled = 2;

    // the daylight savings settings to use on the monitor (if enabled)
    basic.DaylightSavingsSettings dst_settings = 3;

} // CmdSetTimeDatesDst

/*  RespSetTimeDatesDst  */
message RespSetTimeDatesDst {

    // The result of the time set on the Main processor
    settings.ETimeSetResult result = 1;     

} // RespSetTimeDatesDst

//-------------------------------------------------------------------------------------------------

/*  ENUM EDisplayButtonEvent defines the values of the display button events; 
    one of Pressed, Idle, or else a Duration (in 0.1 second increments) that the button was "pressed". */
enum EDisplayButtonEvent {

    /* 0 = Button currently is pressed */
    DISPLAY_BUTTON_EVENT_PRESSED = 0;

    /* 1 - 254 = 0.1 to 25.4 second duration that it was pressed before it was released */

    /* 255 = Idle value (button not pressed) */
    DISPLAY_BUTTON_EVENT_IDLE = 255;
    /* Higher values are reserved for future use */

} // enum EDisplayButtonEvent


/*  CmdRemoteDisplayButtonEvent is used to remotely set the state of the display buttons,
 *  as though they were pressed by a user on the local Display.
 *  The command is sent on each edge transition of any button (pressed or released).
 *  The value for each button will be:
 *  - 0 = Button currently is pressed
 *  - 1 - 254 = 0.1 to 25.4 second duration that it was pressed before it was released
 *  - 255 = Idle value (button not pressed)
 *  Maps to the Serial Command 0x9A Set Display Button Event.
 */
message CmdRemoteDisplayButtonEvent {

    // State of the Help button
    EDisplayButtonEvent help = 1;
    // State of the Up (Next) button
    EDisplayButtonEvent up = 2;
    // State of the Down (Back) button
    EDisplayButtonEvent down = 3;
    // State of the Enter (Select) button
    EDisplayButtonEvent enter = 4;
    // State of the Back (Escape or Exit) button
    EDisplayButtonEvent back = 5;
    // State of the Left button
    EDisplayButtonEvent left = 6;
    // State of the Right button
    EDisplayButtonEvent right = 7;
    // State of the Reset button
    EDisplayButtonEvent reset = 8;

} // CmdRemoteDisplayButtonEvent

/*  RespRemoteDisplayButtonEvent  */
message RespRemoteDisplayButtonEvent {

    // The simple result of the Remote Display Button Status command
    settings.EWriteResult result = 1;     

} // RespRemoteDisplayButtonEvent

