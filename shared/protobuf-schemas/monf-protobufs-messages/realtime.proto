/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  REALTIME
    Messages for realtime data based on the SPI data from the Main processor.
*/
syntax = "proto3";
package realtime;

//***************************************************************************************** IMPORTS
import "basic.proto";
import "mon_faults.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  ChannelVoltsCurrentsAndTimers provides one channel's voltages and currents.  
    This is used in the ChannelStatusData message
    */
message ChannelVoltsCurrentsAndTimers {

    // The channel number
    uint32 channel = 1;

    // Channel indicator voltages in Volts
    float red_volts = 2;
    float yellow_volts = 3; 
    float green_volts = 4;  
    float walk_volts = 5;

    // Channel indicator currents in Amps
    float red_amps = 6;
    float yellow_amps = 7;    
    float green_amps = 8;    
    float walk_amps = 9;    

    // Channel indicator off times in milliseconds
    float red_off_time_ms = 10;
    float yellow_off_time_ms = 11;    
    float green_off_time_ms = 12;    
    float walk_off_time_ms = 13;  

    // Channel indicator on times in milliseconds
    float red_on_time_ms = 14;
    float yellow_on_time_ms = 15;    
    float green_on_time_ms = 16;    
    float walk_on_time_ms = 17;  

} // ChannelVoltsCurrentsAndTimers


/*  ChannelStatusData provides realtime data for all channels in use. 
    This comes from the Main processor SPI data Channel Data Block and Timers Data Block*/
message ChannelStatusData {

    // bitmaps of channel indications on.  LSbit = Ch.1
    fixed32 blues_on_chmap = 1;
    fixed32 reds_on_chmap = 2;
    fixed32 yellows_on_chmap = 3;
    fixed32 greens_on_chmap = 4;   
    fixed32 walks_on_chmap = 5;  

    // bitmaps of indication channels field check mismatch.  LSbit = Ch.1
    fixed32 reds_field_check_chmap = 6;    
    fixed32 yellows_field_check_chmap = 7;
    fixed32 greens_field_check_chmap = 8; 
    fixed32 walks_field_check_chmap = 9;

    // voltages, currents, and timers for each channel's indicators
    repeated ChannelVoltsCurrentsAndTimers channel_indicators_v_i_t = 10;  // Max repeat count set in realtime.options 

} // ChannelStatusData


/*  DisplayLineControl provides formatting options for a display line of text.  This comes from the 
    SPI CpsTextLine "Control Byte" */
message DisplayLineControl {

    // When true, this line was modified since last update.
    bool modified = 1;

    // When true, this line is being displayed.
    bool in_use = 2;

} // DisplayLineControl


/*  LcdDisplayLine provides a line of text from the monitor's display. This is used in MonitorDisplayData.
    This is a separate message as color and other formatting data may be added for each line. */
message LcdDisplayLine {

    // The monitor's display line number
    uint32 line_number = 1;

    // formatting information for this display line
    DisplayLineControl format = 2;

    // A string of ASCII characters for the given display line
    // NOTE: This is type "bytes" instead of "string" because the display uses non-printable ASCII char
    //       values for special display characters.  When Flutter/Dart consumes the protobuf as a "string"
    //       it may truncate a text line at one of these characters (esp. with MSB set) during the protobuf
    //       decoding into UTF-8, losing the remainder of the line text.  Using a type of "bytes" prevents 
    //       this from happening
    bytes line_text = 3;               // Max length set in realtime.options

    // An RGB color byte for each character in "line_text"
    // Refer to the Display documentation for the format of each byte.
    bytes line_colors = 4;              // Max length set in realtime.options

} // LcdDisplayLine

/*  ENUM EDisplayChannelsType indicates the channel configuration type of the display */
enum EDisplayChannelsType {

    DISP_CH_UNSPECIFIED = 0;
    DISP_CH_3 = 1;          // 3 channel type
    DISP_CH_6 = 2;          // 6 channel type
    DISP_CH_12 = 3;         // 12 channel type
    DISP_CH_16 = 4;         // 16 channel type
    DISP_CH_16X = 5;        // 16 channel + Walks type

} // enum EDisplayChannelsType

/*  MonitorDisplayData provides monitor LED and LCD display data. 
    This comes from the Main processor SPI data Display Data Block, CpsDisplayData*/
message MonitorDisplayData {

    /* True when the field display (non-text portion) of the screen is enabled */
    bool enable_field_display = 12;

    /* Enumerated value with the monitor channels to display */
    EDisplayChannelsType display_channels = 13;

    /* Bitmaps of channel LEDs on.  LSbit = Ch.1
     * 
     * Note that the LED and Field Check bits for Display Data are not updated when in a Fault state,
     * but remain what they were at the time of the fault.
     * The following are bitfields for the channel maps with LSB = Ch.1
     */
    fixed32 red_field_leds_on_chmap = 1;
    fixed32 yellow_field_leds_on_chmap = 2;
    fixed32 green_field_leds_on_chmap = 3;   
    fixed32 walk_field_leds_on_chmap = 4;
    fixed32 blue_fault_leds_on_chmap = 5;

    /* The Field Check States show what the traffic controller intended for each channel.
     * The field_leds_on values above show what the monitor actually detected;
     * any discrepancies between the intended and detected states are signficant.
     */
    fixed32 red_fcs_chmap = 6;
    fixed32 yellow_fcs_chmap = 7;
    fixed32 green_fcs_chmap = 8;
    fixed32 walk_fcs_chmap = 9;

    // The total number of text lines on the LCD display
    // Note that this may change in different display modes.
    uint32 line_count = 10;
    
    // LCD display lines
    repeated LcdDisplayLine display_lines = 11;        // Max repeat count set in realtime.options 

} // MonitorDisplayData


/*  PanelLedsBitmap provides panel LED states for the MonitorPresentStatus message. 
     */
message PanelLedsBitmap {

    bool power = 1;
    bool fault = 2;
    bool diag = 3;

} // PanelLedsBitmap


/*  MmuMonitoredInputsVoltages provides the voltages on the monitored inputs for the MMU. 
    This is used in the MonitorPresentStatus message */
message MmuMonitoredInputsVoltages {

    // values are voltages
    float input1_24v_inhibit_volts = 1;
    float input2_24v_monitor_1_volts = 2;
    float input3_24v_monitor_2_volts = 3;
    float input4_controller_voltage = 4;
    float input5_type_select_volts = 5;
    float input6_red_enable_volts = 6;
    float input7_external_reset_volts = 7;
    float input8_port1_disable_volts = 8;
    float input9_external_watchdog_volts = 9;

} // MmuMonitoredInputsVoltages


/*  MonitorPresentStatus provides general monitor status information. 
    This comes from the Main processor SPI data Current Status Block */
message MonitorPresentStatus {

    // The most recent fault(s)
    // The "fault_code" field indicates the present monitor fault, if any.
    // It is also possible that a diagnostic fault may also occur along with a monitor fault.
    // If a diagnostic fault has occurred (on its own or in additon to a monitor fault), then
    // the "diagnostic_fault" field will be 'true', and the "diagnostic_code" field indicates the 
    // fault type.
    mon_faults.EFaultCode fault_code = 1;
    bool diagnostic_fault = 2;

    // Subcode for fault
    mon_faults.MmuSubFaultTypeValue fault_subcode = 28; 

    // Code for a diagnostic fault.
    mon_faults.ESubFaultDiagnostic          diagnostic_code = 8; 

    // bitmap of channels involved in the fault.  LSbit = Ch.1.  May be all 0s for a diagnostic fault.
    fixed32 fault_channels_chmap = 9;

    // Incremented for each new fault and matches the Fault Log entry for the fault
    uint32 last_fault_id = 10;

    // The status of the monitor panel LEDs.
    PanelLedsBitmap leds = 11;    

    // Mains AC voltage or DC supply voltage in Volts.
    oneof supply_voltage {
        float ac_mains_voltage = 12;
        float dc_supply_voltage = 13;
    }    

    // Mains AC supply frequency in Hz or DC supply voltage ripple in mV, depending on model options.
    oneof supply_characteristic {
        float ac_frequency_hz = 14;
        float dc_ripple_mv = 15;
    }

    // Present monitor unit temperature in degrees F.
    float temperature_degf = 16;        

    // minimum flash time remaining in seconds.
    uint32 min_flash_remaining_s = 17;

    // Timeout count for controller, no message frame received in over 100 ms since midnight or last clear of statistics
    uint32 controller_timeout_count = 18; 

    // Controller time now
    basic.LocalDateTime controller_date_time = 20;

    // Monitor RTC time now
    basic.LocalDateTime rtc_date_time = 21;

    // (Uptime) Total run time for the monitor since being shipped, in seconds
    uint32 total_run_time = 22;

    // Total time the monitor has been in flash since being shipped, in seconds
    uint32 total_flash_time = 23;

    // Total time the monitor has been in a fault state since being shipped, in seconds
    uint32 total_fault_time = 24;

    // The most recent configuration log entry ID
    uint32 last_config_id = 25;

    // States of monitored inputs
    oneof monitored_input_states {
        mon_faults.MmuMonitoredInputsStatusBitmap mmu_states = 26;  
        //  CMU  
    }

    // monitored input voltages
    oneof monitored_input_volts {
        MmuMonitoredInputsVoltages mmu_voltages = 27;
        // TODO add CMU voltages
    }

    // RESERVED FIELD NUMBERS THAT MUST NOT BE RE-USED!!
    reserved 3 to 7;
    reserved 19;

} // MonitorPresentStatus



