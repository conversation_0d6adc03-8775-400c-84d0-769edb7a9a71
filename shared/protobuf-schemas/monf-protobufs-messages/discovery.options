 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   DISCOVERY
#   nanopb options file for discovery.proto

# DiscHelloApp
#   Max Data lengths
discovery.DiscHelloApp.device_id        max_size:64
#   Max String Lengths
discovery.DiscHelloApp.name_string      max_size:128


