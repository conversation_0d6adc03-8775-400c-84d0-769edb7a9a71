{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "Python Debugger: parse_mmu_logs_xls.py",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/parse_mmu_logs_xls.py",
            "console": "integratedTerminal",
            "args": [
                "scripts/NextGen MMU Sample Logs.xlsx",
                "--prefix",
                "MMU_Sample"
            ]
        },
        {
            "name": "Python Debugger: parse_mmu_datakeys.py",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/parse_mmu_datakeys.py",
            "console": "integratedTerminal",
            "args": [
                "scripts/NextGen MMU Sample Logs.xlsx"
            ]
        },
        {
            "name": "Python Debugger: decode_mmu_datakey.py",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/decode_mmu_datakey.py",
            "console": "integratedTerminal",
            "args": [
                "scripts/MMU_Data_Key4.bin"
            ]
        }
    ]
}