 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   CMD_RESP_DFU
#   nanopb options file for cmd_resp_dfu.proto

# RespManifestVersions
#   Max repeated field counts. 
cmd_resp_dfu.RespManifestVersions.image_statuses             max_count:12
#   Max repeated Model Number enums. 
cmd_resp_dfu.RespManifestVersions.supported_model_numbers    max_count:16
#   Max string lengths
cmd_resp_dfu.RespManifestVersions.package_version          max_size:12
cmd_resp_dfu.RespManifestVersions.package_build_date       max_size:11

# CmdFirmwareUpdateManifest
#   Max repeated field counts. 
cmd_resp_dfu.CmdFirmwareUpdateManifest.entries          max_count:12
#   Max repeated Model Number enums. 
cmd_resp_dfu.CmdFirmwareUpdateManifest.supported_model_numbers    max_count:16
#   Max string lengths
cmd_resp_dfu.CmdFirmwareUpdateManifest.package_version          max_size:12
cmd_resp_dfu.CmdFirmwareUpdateManifest.package_build_date       max_size:11

# CmdBeginFirmwareDownload
#   Max string lengths
cmd_resp_dfu.CmdBeginFirmwareDownload.filename          max_size:32

# CmdFirmwareDownloadChunk
#   Max Data lengths
# NOTE: This size should be returned in cmd_resp_dfu.RespBeginFirmwareDownload.max_chunk_size_bytes
# NOTE: For Monitor MMU, check that this is < K_MEMSLABS_LARGE_MESSAGE_SIZE
cmd_resp_dfu.CmdFirmwareDownloadChunk.chunk_data        max_size:4096
