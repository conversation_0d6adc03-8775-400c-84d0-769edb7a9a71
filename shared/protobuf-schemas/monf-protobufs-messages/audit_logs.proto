/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  AUDIT_LOGS
    Log entry message format for the communications processor logs.
*/
syntax = "proto3";
package audit_logs;

//***************************************************************************************** IMPORTS
import "basic.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  AuditLogEntryComms represents one entry of an audit log (any) on the Comms processor. 
    This message format is used both to store entries in the log files on the Comms processer
    file system, and when they are read out over the websocket interface. */
message AuditLogEntryComms {

    // Log entry timestamp
    basic.LocalDateTime entry_timestamp = 1;

    // The entry text, max length in audit_logs.options
    string entry_text = 2;

    // The entry property (value accompanying the text)
    oneof entry_property {
        int64   integer_val = 3;    // a signed integer value up to 64 bits (may be used for UNsigned 32-bit values)
        float   float_val = 4;      // floating point value
        string  string_text = 5;    // a text string, max length in audit_logs.options
    }     

} // AuditLogEntryComms


/*  AuditLogMultipleEntriesComms encapsulates repeated audit log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. 
    
    NOTE: The log entries are NOT stored in this format as a repeated field in the log files.
          Instead, the are separated by a short binary header.
          This message is ONLY used when log entries are read out as part of a command-response. */
message AuditLogMultipleEntriesComms {

    repeated AuditLogEntryComms log_entry = 1;  // Max repeat count set in audit_logs.options 

} // AuditLogMultipleEntriesComms


/*  ENUM EAuditLogType is used to specify a log for requests and reads. */
enum EAuditLogType {

    AUD_LOG_UNSPECIFIED = 0;
    AUD_LOG_ACCESS = 1;
    AUD_LOG_NETWORK = 2;
    AUD_LOG_CONFIGURATION = 3;
    AUD_LOG_BLUETOOTH = 4; 
    AUD_LOG_SYSTEM = 5; 

} // enum EAuditLogType


/*  AuditLogEntryCount is used to return the present total count of entries for a log, 
    along with the length limit on the log. */
message AuditLogEntryCount {
    
    // This is the log to which the count applies
    EAuditLogType log = 1;

    // Total number of present log entries.
    uint32 entries = 2;    

    // Log length limit in total entries.  If 0, the log has no entry limit and is only limited by
    // file system space.
    uint32 max_entries = 3;

    // log length minimum increment
    uint32 entries_per_file = 4;

} // AuditLogEntryCount

