/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  CMD_RESP_COMMS
    Command (from app) and response (from monitor) message formats for communications related commands.
*/
syntax = "proto3";
package cmd_resp_comms;

//***************************************************************************************** IMPORTS

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  ENUM EAuthenticationResult provides authentication status. */
enum EAuthenticationResult {

    AUTH_UNSPECIFIED = 0;
    AUTH_SUCCESS = 1;           // Authentication succeeded
    AUTH_NO_ACCESS = 2;         // No access allowed on this unit* (see note below)
    AUTH_HAS_CONNECTION = 7;    // An App is already connected to this device, new connection refused.

    /* NOTES:
        * This might occur if the mTLS succeeds as the certificates are valid, but a user claim in
          App's certificate extension does not allow access to this unit.
    */

    reserved 3,4,5,6;           // deprecated values - DO NOT USE
} // enum EAuthenticationResult


/*  RespAuthStatus is the first message sent after establishing a WebSocket connection, by the MMU
    (There is no command that generates it.)  It communicates the connection status to the connecting
    App.  In the case of AUTH_NO_ACCESS and AUTH_HAS_CONNECTION, the MMU will disconnect itself after
    sending this message. */
message RespAuthStatus {

    //  The result of the authentication
    EAuthenticationResult result = 1;

    // If the result is "AUTH_HAS_CONNECTION", then this contains the account name from the 
    // existing connection's certificate.  Otherwise, this field is empty.
    string existing_connection_name = 2;

} // RespAuthStatus

//-------------------------------------------------------------------------------------------------

/*  CmdChunkTest is used exclusively for testing interfaces (esp. BLE) with large messages  */
message CmdChunkTest {

    bytes data = 1;         // Max data length set in cmd_resp_comms.options  

} // CmdChunkTest


/*  RespChunkTest returns the contents of the bytes sent in CmdChunkTest.data */
message RespChunkTest {

    bytes data_echo = 1;    // Max data length set in cmd_resp_comms.options    

} // RespChunkTest

//-------------------------------------------------------------------------------------------------
