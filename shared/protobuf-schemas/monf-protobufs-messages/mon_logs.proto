/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  MON_LOGS
    Messages for monitor log entries.
*/
syntax = "proto3";
package mon_logs;

//***************************************************************************************** IMPORTS
import "basic.proto";
import "mon_faults.proto";
import "settings.proto";
//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

// ************************************************************************************************************

/*  ENUM EPowerLogEventType defines event types for PowerLogEntry.
    Some values only apply to certain monitors. */
enum EPowerLogEventType {

    PWR_LOG_EVENT_UNSPECIFIED = 0;
    PWR_LOG_EVENT_POWER_UP = 1;
    PWR_LOG_EVENT_POWER_DOWN = 2;
    PWR_LOG_EVENT_CONTROLLER_UP = 3;     // CMU only
    PWR_LOG_EVENT_CONTROLLER_DOWN = 4;   // CMU only
    PWR_LOG_EVENT_LOW_VOLTAGE = 5;
    PWR_LOG_EVENT_HIGH_VOLTAGE = 6;
    PWR_LOG_EVENT_POWER_INTERRUPTED = 7;
    PWR_LOG_EVENT_POWER_DOWN_TIMEOUT = 8;   // CMU only
    PWR_LOG_EVENT_NRESET_TIMEOUT = 9;       // CMU only
    PWR_LOG_EVENT_NRESET_RECOVERY = 10;     // CMU only
    PWR_LOG_EVENT_HDSP_TIMEOUT = 11;        // CMU only
    PWR_LOG_EVENT_FREQUENCY_LOW = 12;
    PWR_LOG_EVENT_FREQUENCY_HIGH = 13;
    PWR_LOG_EVENT_TIMED = 14;               // A Timed Event (scheduled)
    PWR_LOG_EVENT_LOW_VOLTAGE_RECOVERY = 15;
    PWR_LOG_EVENT_HIGH_VOLTAGE_RECOVERY = 16;
    PWR_LOG_EVENT_FREQUENCY_LOW_RECOVERY = 17;
    PWR_LOG_EVENT_FREQUENCY_HIGH_RECOVERY = 18;
    PWR_LOG_EVENT_TEST = 19;     // A developer test event

} // enum EPowerLogEventType

/*  ENUM EPowerLogEventTiming defines log period for PowerLogEntry. */
enum EPowerLogEventTiming {

    PWR_LOG_PERIOD_UNSPECIFIED = 0;
    PWR_LOG_PERIOD_NO_TIMED_EVENT = 1;
    PWR_LOG_PERIOD_EVERY_1_HR = 2;
    PWR_LOG_PERIOD_EVERY_2_HRS = 3;
    PWR_LOG_PERIOD_EVERY_4_HRS = 4;
    PWR_LOG_PERIOD_EVERY_8_HRS = 5;
    PWR_LOG_PERIOD_EVERY_12_HRS = 6;
    PWR_LOG_PERIOD_EVERY_1_DAY = 7;
    PWR_LOG_PERIOD_EVERY_2_DAYS = 8;
    PWR_LOG_PERIOD_EVERY_7_DAYS = 9;
    PWR_LOG_PERIOD_EVERY_14_DAYS = 10;
    PWR_LOG_PERIOD_EVERY_1_MONTH = 11;

} // enum EPowerLogEventTiming

/*  MmuPowerMonitors are MMU specific voltage monitors that are part of the monitor Power Log. 
    This is used in PowerLogEntryMmu */
message MmuPowerMonitors {

    // AC Mains voltage when entry was made (present), followed by the minimum and maximum recorded voltages
    basic.NowMinMaxFloat ac_mains_volts = 1;

    // DC 24 V monitor 1 voltage when entry was made (present), followed by the minimum and maximum recorded voltages
    basic.NowMinMaxFloat dc_24_volts_mon1 = 2;   

    // DC 24 V monitor 2 voltage when entry was made (present), followed by the minimum and maximum recorded voltages
    basic.NowMinMaxFloat dc_24_volts_mon2 = 3;

    // Controller Voltage Monitor voltage when entry was made (present), followed by the minimum and maximum recorded voltages
    basic.NowMinMaxFloat cvm_volt = 4; 

    // Line Frequency in Hertz when entry was made (present), followed by the minimum and maximum recorded frequencies
    basic.NowMinMaxFloat line_frequency_hz = 5;    

} // MmuPowerMonitors


/*  PowerLogEntryMmu represents one entry of the monitor Power Log for Mmu. 
    This comes from the Main processor response to the command 0x01 Retrieve Power Log */
message PowerLogEntryMmu {

    // unique per log entry
    uint32 entry_id = 1;

    // Enum with the log event
    EPowerLogEventType event_type = 2;

    // Log entry timestamp
    basic.LocalDateTime entry_timestamp = 3;

    // monitored voltages
    MmuPowerMonitors power_voltages = 4;

    // Temperature in degrees F when entry was made (present), followed by the minimum and maximum recorded temperatures
    basic.NowMinMaxFloat temperature_degf = 5;     

    // The period at which regular log entries are made
    EPowerLogEventTiming event_timing  = 8;

    // For a PWR_LOG_EVENT_POWER_INTERRUPTED event, this is the length of the interrupt in milliseconds.
    optional uint32 power_interrupt_time_ms = 7;

    reserved 6;
} // PowerLogEntryMmu


/*  PowerLogMultipleEntriesMmu encapsulates repeated Power Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message PowerLogMultipleEntriesMmu {

    repeated PowerLogEntryMmu log_entry = 1;  // Max repeat count set in mon_logs.options 

} // PowerLogMultipleEntriesMmu


/*  ENUM EResetLogSource defines sources of a reset for ResetLogEntry.
    Some values only apply to certain monitors. */
enum EResetLogSource {

    RST_LOG_SOURCE_UNSPECIFIED = 0;
    RST_LOG_SOURCE_FRONT_PANEL = 1;
    RST_LOG_SOURCE_EXTERNAL = 2;
    RST_LOG_SOURCE_NON_LATCHED = 3;
    RST_LOG_SOURCE_POWER_CYCLE = 4;
    RST_LOG_SOURCE_ADU = 5;
    RST_LOG_SOURCE_CONFIG_CHANGED = 6;  // Data Key
    RST_LOG_SOURCE_REMOTE = 7;          // Remote UI
    RST_LOG_SOURCE_TEST = 8;            // Test reset through debug command
    
} // enum EResetLogSource

/*  ResetLogEntryMmu represents one entry of the monitor Reset Log for Mmu. 
    This comes from the Main processor response to the command 0x02 Retrieve Reset Log */
message ResetLogEntryMmu {

    // unique per log entry
    uint32 entry_id = 1;

    // Enum with the source of the reset
    EResetLogSource reset_source = 2;

    // Log entry timestamp
    basic.LocalDateTime entry_timestamp = 3;

    // Fault that was cleared by the reset
    // A diagnostic fault may also be cleared along with another fault
    mon_faults.EFaultCode fault_cleared = 4;
    bool diagnostic_cleared = 5;

    // The Fault Log entry for the fault that was cleared
    uint32 fault_id = 6;

} // ResetLogEntryMmu


/*  ResetLogMultipleEntriesMmu encapsulates repeated Reset Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message ResetLogMultipleEntriesMmu {

    repeated ResetLogEntryMmu log_entry = 1;  // Max repeat count set in mon_logs.options 

} // ResetLogMultipleEntriesMmu


/*  ENUM EClockLogSource defines sources of a clock change for ClockLogEntry.
    Some values only apply to certain monitors. */
enum EClockLogSource {

    CLOCK_LOG_SOURCE_UNSPECIFIED = 0;
    CLOCK_LOG_SOURCE_CONTROLLER = 1;
    CLOCK_LOG_SOURCE_ETHERNET = 2;
    CLOCK_LOG_SOURCE_BLUETOOTH = 3;
    CLOCK_LOG_SOURCE_FRONTPANEL = 4;

} // enum EClockLogSource


/*  ClockLogEntry represents one entry of the Clock Log. 
    This comes from the Main processor response to the command 0x04 Retrieve Clock Log */
message ClockLogEntry {

    // unique per log entry
    uint32 entry_id = 1;

    // Date/Time source
    EClockLogSource clock_source = 2;

    // Previous Date/Time
    basic.LocalDateTime previous_datetime = 3;    

    // New Date/Time
    basic.LocalDateTime new_datetime = 4;     

    // (Uptime) Total run time for the monitor since being shipped, in seconds
    uint32 run_time = 5;

} // ClockLogEntry


/*  ClockLogMultipleEntriesMmu encapsulates repeated Clock Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message ClockLogMultipleEntriesMmu {

    repeated ClockLogEntry log_entry = 1;  // Max repeat count set in mon_logs.options 

} // ClockLogMultipleEntriesMmu


/*  HardwareRevisionsMmu contains the hardware revision values for the MMU system. */
message HardwareRevisionsMmu {

    // Main processor hardware revision, value 0-255
    uint32 main_hardware_revision = 1;

    // Isolated processor hardware revision, value 0-255
    uint32 iso_hardware_revision = 2;

    // Display processor hardware revision, 0-15
    uint32 display_hardware_revision = 3;

    // Display board RMS engine hardware revision, 0-15
    uint32 display_rms_hardware_revision = 4;

    // Communications processor hardware revision, 0-255
    uint32 comms_hardware_revision = 5;

} // HardwareRevisionsMmu

/*  ConfigLogEntryMmu represents one entry of the MMU Configuration Log. 
    This comes from the Main processor response to the command 0x05 Retrieve Configuration Log */
message ConfigLogEntryMmu {

    // unique per log entry, over the lifetime of the monitor
    uint32 config_id = 1;

    // monitor hardware revisions
    HardwareRevisionsMmu hardware = 2;    

    reserved 3;     // deprecated field value - DO NOT USE

    // CCIT CRC16 with seed value 0xFFFF over 'data_key_data'
    uint32 data_key_crc = 10;

    // Raw contents of the entire Data Key
    bytes data_key_data = 9;    // Max data length set in mon_logs.options    

    // The Factory options in use for this configuration.
    settings.FactoryOptionsMmu factory_options = 23;

    // The PCB options in use for this configuration.
    settings.PcbOptionsMmu pcb_options = 22; 

    // The Agency options in use for this configuration.
    settings.AgencyOptionsMmu agency_options = 24;

    // CCIT CRC16 with seed value 0xFFFF over 'data_key_data', 'pcb_options', 'factory_options', and 'agency_options'
    // The Monitor will compute this value.
    uint32 monitor_crc = 12;

    // Log entry timestamp
    basic.LocalDateTime entry_timestamp = 4;

    // MMU serial number & MMU model number
    basic.ModelAndSerialNumber numbers = 5;   

    // Main processor firmware version and date
    basic.VersionStrThree main_mcu_fw_version = 6;
    basic.DateStr main_mcu_fw_date = 13;

    // Isolated processor firmware version and date
    basic.VersionStrThree isolated_mcu_fw_version = 7;
    basic.DateStr isolated_mcu_fw_date = 14;
    
    // Display processor firmware version and date
    basic.VersionStrThree disp_mcu_fw_version = 15;
    basic.DateStr disp_mcu_fw_date = 16;

    // Communication processor firmware version and date
    basic.VersionStrThree comms_mcu_fw_version = 8;
    basic.DateStr comms_mcu_fw_date = 17;

    // BLE processor firmware version and date
    basic.VersionStrThree ble_mcu_fw_version = 18;
    basic.DateStr ble_mcu_fw_date = 19;

    // Firmware package firmware version and date
    basic.VersionStrThree pkg_mcu_fw_version = 20;
    basic.DateStr pkg_mcu_fw_date = 21;

    reserved 11;    // deprecated field value - DO NOT USE

    // Data source - program card type
    settings.EConfigDataLocation data_src = 25;
} // ConfigLogEntryMmu


/*  ConfigLogMultipleEntriesMmu encapsulates repeated Configuration Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message ConfigLogMultipleEntriesMmu {

    repeated ConfigLogEntryMmu log_entry = 1;  // Max repeat count set in mon_logs.options 

} // ConfigLogMultipleEntriesMmu


/*  Port1LogEntryMmu represents one entry of the MMu Port 1 Log.
    This comes from the Main processor response to the command 0x06 Retrieve Port 1 Log */
message Port1LogEntryMmu {
    
    // unique per log entry
    uint32 entry_id = 1;

    // Log entry timestamp
    basic.LocalDateTime entry_timestamp = 2;

    // Timestamp when the counts were cleared (reset)
    basic.LocalDateTime timestamp_when_cleared = 3;    

    // Count of frame CRC errors.  Maxes out at 65535
    uint32 crc_error_count = 4;

    // Count of frame idle errors, where the bus went idle with no closing frame byte.  Maxes out at 65535
    uint32 idle_error_count = 5;    

    // Count of frame errors, where bus data was received without a starting frame character.  Maxes out at 65535
    uint32 frame_error_count = 6;                    

    // Count of bus timeout errors, where no frame from the controller was received in over 100 ms.
    // Maxes out at 65535
    uint32 timeout_error_count = 7;

    // Count of short frame errors, where the frame length is less than expected. Maxes out at 65535
    uint32 short_error_count = 8;    

    // Count of long freame errors, where the frame length is more than expected.  Maxes out at 65535
    uint32 long_error_count = 9;

    // Count of unknown frame number errors.  Maxes out at 65535
    uint32 unknown_error_count = 10;    

} // Port1LogEntryMmu


/*  Port1LogLogMultipleEntriesMmu encapsulates repeated Port 1 Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message Port1LogLogMultipleEntriesMmu {

    repeated Port1LogEntryMmu log_entry = 1;  // Max repeat count set in mon_logs.options 

} // Port1LogLogMultipleEntriesMmu


/*  FaultHeaderLogEntryMmu represents one entry of the Fault Header Log for the MMU. 
    This comes from the Main processor response to the command 0x08 Retrieve Fault Header Log */
message FaultHeaderLogEntryMmu {
    
    // unique per log entry
    uint32 fault_id = 1;

    // Log entry timestamp
    basic.LocalDateTime entry_timestamp = 2;

    // The Configuration Log entry id in effect at the time of the fault
    uint32 config_id_in_use = 3;    

    // CCIT CRC16 with seed value 0xFFFF over 'data_key_data'
    uint32 data_key_crc = 4;  

    // MMU serial number & model number
    basic.ModelAndSerialNumber numbers = 5;   

    // Monitor and user ID strings
    basic.MonitorAndUserIds ids = 6;       

    // Measurement log frequency (resolution) in entries per second
    uint32 measurement_log_frequency = 7;

    // Measurement log count of entries for this fault
    uint32 measurement_log_entry_count = 8;

    // Sequence log frequency (resolution) in entries per second
    uint32 sequence_log_frequency = 9;

    // Sequence log count of entries for this fault
    uint32 sequence_log_entry_count = 10;    

    // Fault that occurred
    // A diagnostic fault may also occur along with another fault
    mon_faults.EFaultCode fault_code = 11;
    bool diagnostic_fault = 12;

    // Subcode for fault
    mon_faults.MmuSubFaultTypeValue fault_subcode = 13;   

    // First identified diagnostic fault code (if diagnostic_fault is TRUE)
    mon_faults.ESubFaultDiagnostic          diagnostic_code = 18;  

    // bitmap of channels involved in the fault.  LSbit = Ch.1
    fixed32 channels_chmap = 19;

    // bitmap of channel red indications involved in the fault.  LSbit = Ch.1
    fixed32 red_indications_chmap = 20;

    // bitmap of channel yellow indications involved in the fault.  LSbit = Ch.1
    fixed32 yellow_indications_chmap = 21;

    // bitmap of channel green indications involved in the fault.  LSbit = Ch.1
    fixed32 green_indications_chmap = 22;       

    // bitmap of channel walk indications involved in the fault.  LSbit = Ch.1
    fixed32 walk_indications_chmap = 23;        

    // bitmap of channel red indications on at the time of the fault.  LSbit = Ch.1
    fixed32 reds_on_chmap = 24;

    // bitmap of channel yellow indications on at the time of the fault.  LSbit = Ch.1
    fixed32 yellows_on_chmap = 25;

    // bitmap of channel green indications on at the time of the fault.  LSbit = Ch.1
    fixed32 greens_on_chmap = 26;        

    // bitmap of channel walk indications on at the time of the fault.  LSbit = Ch.1
    fixed32 walk_on_chmap = 27;        

    // Mains supply frequency in Hz
    float line_frequency = 28;

    // Temperature in degrees F when fault occurred.
    float temperature_degf = 29;     

} // FaultHeaderLogEntryMmu


/*  FaultHeaderLogMultipleEntriesMmu encapsulates repeated Fault Header Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message FaultHeaderLogMultipleEntriesMmu {

    repeated FaultHeaderLogEntryMmu log_entry = 1;  // Max repeat count set in mon_logs.options 

} // FaultHeaderLogMultipleEntriesMmu


/*  FaultMeasurementLogEntryMmu represents one entry of the Fault Measurement Log for the Mmu. 
    This comes from the Main processor response to the command 0x09 Retrieve Fault Measurement Log */
message FaultMeasurementLogEntryMmu {
    
    // The matching fault log entry.
    uint32 fault_id = 1;        
    
   // Each fault_id has its own set of numbered entry_ids.  Only the most recent faults may have measurement entries. 
    uint32 entry_id = 2;

    // Mains AC voltage, converted to float Volts
    optional float mains_ac_volts = 3;

    // bitmap of indication channels 'on' status bits using the normal threshold.  LSbit = Ch.1
    fixed32 reds_on_normal_chmap = 4;       
    fixed32 yellows_on_normal_chmap = 5; 
    fixed32 greens_on_normal_chmap = 6; 
    fixed32 walks_on_normal_chmap = 7; 

    // bitmap of indication channels 'on' status bits using the lack of signal threshold.  LSbit = Ch.1
    fixed32 reds_on_lackofsignal_chmap = 8;    
    fixed32 yellows_on_lackofsignal_chmap = 9;
    fixed32 greens_on_lackofsignal_chmap = 10; 
    fixed32 walks_on_lackofsignal_chmap = 11; 

    // bitmap of indication channels 'on' status bits using the current threshold.  LSbit = Ch.1
    fixed32 reds_on_current_chmap = 12;    
    fixed32 yellows_on_current_chmap = 13;
    fixed32 greens_on_current_chmap = 14; 
    fixed32 walks_on_current_chmap = 15;     

    // bitmap of indication channels field check mistmatch.  LSbit = Ch.1
    fixed32 reds_field_check_chmap = 16;    
    fixed32 yellows_field_check_chmap = 17;
    fixed32 greens_field_check_chmap = 18; 
    fixed32 walks_field_check_chmap = 19; 

    // voltage and current measurements for each indicator per channel.
    // The current is converted from the serial message 15mA per bit units to Amp units.
    repeated mon_faults.FaultIndicationChVoltCurrent red_channels = 20;     // Max repeat count set in mon_logs.options 
    repeated mon_faults.FaultIndicationChVoltCurrent yellow_channels = 21;  // Max repeat count set in mon_logs.options 
    repeated mon_faults.FaultIndicationChVoltCurrent green_channels = 22;   // Max repeat count set in mon_logs.options 
    repeated mon_faults.FaultIndicationChVoltCurrent walk_channels = 23;   // Max repeat count set in mon_logs.options 

    // 24V monitor inhibit signal voltage, converted to float Volts
    optional float inhibit_24v_monitor_volts = 24;

    // 24V monitor 1 signal voltage, converted to float Volts
    optional float monitor_24v_1_volts = 25;

    // 24V monitor 2 signal voltage, converted to float Volts
    optional float monitor_24v_2_volts = 26;

    // Controller voltage monitor signal voltage, converted to float Volts
    optional float controller_monitor_volts = 27;

    // Type select voltage, converted to float Volts
    optional float type_select_volts = 28;

    // Local Flash voltage, converted to float Volts
    optional float local_flash_volts = 29;    

    // External reset DC voltage, converted to float Volts
    optional float external_reset_volts = 30;

    // Port 1 disable voltage, converted to float Volts
    optional float port1_disable_volts = 31;

    // External Watchdog voltage, converted to float Volts
    optional float external_watchdog_volts = 32;    

    // Red enable voltage, converted to float Volts
    optional float red_enabled_volts = 33;

    // Monitored input state bitmap
    mon_faults.MmuMonitoredInputsStatusBitmap input_states = 34;

} // FaultMeasurementLogEntryMmu


/*  FaultMeasurementLogMultipleEntriesMmu encapsulates repeated Fault Measurement Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message FaultMeasurementLogMultipleEntriesMmu {

    repeated FaultMeasurementLogEntryMmu log_entry = 1;  // Max repeat count set in mon_logs.options 

} // FaultMeasurementLogMultipleEntriesMmu


/*  FaultSequenceLogEntryMmu represents one entry of the Fault Sequence Log for Mmu. 
    This comes from the Main processor response to the command 0x0A Retrieve Fault Sequence Log */
message FaultSequenceLogEntryMmu {

    // The matching fault log entry.
    uint32 fault_id = 1;        
    
   // Each fault_id has its own set of numbered entry_ids.  Only the most recent faults may have sequence entries. 
    uint32 entry_id = 2;

    // The number of periods with all data at the given states
    uint32 period_count = 3;

    // Monitored control states bitmap
    mon_faults.MmuMonitoredControlStatesBitmap control_states = 4;

    // Monitored input state bitmap
    mon_faults.MmuMonitoredInputsStatusBitmap input_states = 5;

    // bitmap of indication channels 'on' status bits using the normal threshold.  LSbit = Ch.1
    fixed32 reds_on_normal_chmap = 6;       
    fixed32 yellows_on_normal_chmap = 7; 
    fixed32 greens_on_normal_chmap = 8; 
    fixed32 walks_on_normal_chmap = 9; 

    // bitmap of indication channels 'on' status bits using the lack of signal threshold.  LSbit = Ch.1
    fixed32 reds_on_lackofsignal_chmap = 10;    
    fixed32 yellows_on_lackofsignal_chmap = 11;
    fixed32 greens_on_lackofsignal_chmap = 12; 
    fixed32 walks_on_lackofsignal_chmap = 13; 

    // bitmap of indication channels 'on' status bits using the current threshold.  LSbit = Ch.1
    fixed32 reds_on_current_chmap = 14;
    fixed32 yellows_on_current_chmap = 15;
    fixed32 greens_on_current_chmap = 16; 
    fixed32 walks_on_current_chmap = 17; 

    // bitmap of indication channels field check mistmatch.  LSbit = Ch.1
    fixed32 reds_field_check_chmap = 18;    
    fixed32 yellows_field_check_chmap = 19;
    fixed32 greens_field_check_chmap = 20; 
    fixed32 walks_field_check_chmap = 21; 

} // FaultSequenceLogEntryMmu


/*  FaultSequenceLogMultipleEntriesMmu encapsulates repeated Fault Sequence Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message FaultSequenceLogMultipleEntriesMmu {

    repeated FaultSequenceLogEntryMmu log_entry = 1;  // Max repeat count set in mon_logs.options 

} // FaultSequenceLogMultipleEntriesMmu


/*  ENUM ECaptureChannelInput defines the type of capture channel input sourcefor the FaultFactsLogEntry. */
enum ECaptureChannelInput {

    CH_INPUT_UNSPECIFIED = 0;
    CH_INPUT_RED_VOLTAGE = 1;
    CH_INPUT_YELLOW_VOLTAGE = 2;
    CH_INPUT_GREEN_VOLTAGE = 3;
    CH_INPUT_WALK_VOLTAGE = 4;
    CH_INPUT_RED_CURRENT = 5;
    CH_INPUT_YELLOW_CURRENT = 6;
    CH_INPUT_GREEN_CURRENT = 7;
    CH_INPUT_WALK_CURRENT = 8;
    CH_INPUT_AC_LINE_VOLTS = 9;
    CH_INPUT_24V_1_VOLTS = 10;
    CH_INPUT_24V_2_VOLTS = 11;
    CH_INPUT_CVM_VOLTS = 12;
    CH_INPUT_LINE_FREQUENCY = 13;
    CH_INPUT_RED_ENABLE_VOLTS = 14;
    CH_INPUT_EXTERNAL_WATCHDOG_VOLTS = 15;
    CH_INPUT_TYPE_SELECT_VOLTS = 16;
    CH_INPUT_SDLC_DISABLE_VOLTS = 17;
    CH_INPUT_24V_MONITOR_INHIBIT_VOLTS = 18;
    CH_INPUT_LOCAL_FLASH_VOLTS = 19;
    CH_INPUT_TEST = 0xFE; // test input source type

} // enum ECaptureChannelInput


/*  FaultFactsLogEntry represents one entry of the Fault FACTS Log. 
    This comes from the Main processor response to the command 0x0C Retrieve Fault FACTS Log */
message FaultFactsLogEntry {

    // The matching fault log entry.
    uint32 fault_id = 1;  

    // unique per FACTS log entry
    uint32 entry_id = 2;

    // Date/Time stamp
    basic.LocalDateTime datetime = 3;

    // ADC Samples of the input.  Each sample is 16 bits, in centiVolt (0.01 V), milliamps, or centihertz units
    bytes samples_16b_cv = 4;

    // The length of time the captured samples span, in milliseconds
    uint32 capture_time_ms = 5;

    // The channel from which the samples are captured.
    uint32 capture_channel = 6;

    // The type of channel signal input source that is captured
    ECaptureChannelInput channel_input = 7;

} // FaultFactsLogEntry


/*  FaultFactsLogMultipleEntriesMmu encapsulates repeated Fault Sequence Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message FaultFactsLogMultipleEntriesMmu {

    repeated FaultFactsLogEntry log_entry = 1;  // Max repeat count set in mon_logs.options 

} // FaultFactsLogMultipleEntriesMmu


/*  ENUM EAlarmSeverity defines alarm severities for the AlarmLogEntry.
 *  Should be kept in sync with the log_alarm_severity_t enums in the Serial Comms Library. 
 */
enum EAlarmSeverity {
    ALARM_SEVERITY_CRITICAL = 0x00;
    ALARM_SEVERITY_MAJOR = 0x01;
    ALARM_SEVERITY_MINOR = 0x02;
    ALARM_SEVERITY_INFORM = 0x03;
    ALARM_SEVERITY_TEST = 0x0F;
}

/*  ENUM EAlarmSource defines alarm sources for the AlarmLogEntry.
 *  Should be kept in sync with the log_alarm_source_t enums in the Serial Comms Library. 
 */
enum EAlarmSource {
    ALARM_SOURCE_MONITOR_OPERATION = 0x00;
    ALARM_SOURCE_MONITOR_HARDWARE = 0x01;
    ALARM_SOURCE_OTHER_CABINET_HARDWARE = 0x02;
    ALARM_SOURCE_EXTERNAL_SOURCE = 0x03;
    ALARM_SOURCE_INTERNAL_COMMUNICATION = 0x04;
    ALARM_SOURCE_EXTERNAL_COMMUNICATION = 0x05;
    ALARM_SOURCE_TEST = 0x0E;
    ALARM_SOURCE_UNSPECIFIED = 0x0F;
}

/*  AlarmLogEntry represents one entry of the Alarm Log. 
    This comes from the Main processor response to the command 0x0B Retrieve Alarm Log. */
message AlarmLogEntry {

    // unique per log entry
    uint32 entry_id = 1;

    // Alarm severity
    EAlarmSeverity severity = 2;

    // Alarm source
    EAlarmSource source = 3;

    // Date/Time stamp
    basic.LocalDateTime datetime = 4;    

    // Alarm text string
    string text = 5;  // Max string length set in mon_logs.options

} // AlarmLogEntry


/*  AlarmLogMultipleEntriesMmu encapsulates repeated Alarm Log entries.
    This is done because having repeated oneof{} fields in a messsage is 
    not allowed.  This allows having oneof{} in a message that may return
    multiple entries for different logs. */
message AlarmLogMultipleEntriesMmu {

    repeated AlarmLogEntry log_entry = 1;  // Max repeat count set in mon_logs.options 

} // AlarmLogMultipleEntriesMmu


/*  ENUM EMonitorLogType is used to specify a log for requests and results. */
enum EMonitorLogType {

    MON_LOG_UNSPECIFIED = 0;
    MON_LOG_ALL = 1;                // For requesting / specifying all logs
    MON_LOG_POWER = 2;
    MON_LOG_RESET = 3;
    MON_LOG_CLOCK = 4;
    MON_LOG_CONFIGURATION = 5;
    MON_LOG_PORT1 = 6;
    MON_LOG_FAULT_HEADER = 7;
    MON_LOG_FAULT_MEASUREMENT = 8;
    MON_LOG_FAULT_SEQUENCE = 9;
    MON_LOG_FAULT_FACTS = 10;
    MON_LOG_ALARM = 11;

} // enum EMonitorLogType


/*  LogEntryCount is used to return the total count of entries for a log.
    These come from the Main processor response to the command 0x0F Retrieve Logging System Details */
message LogEntryCount {
    
    // This is the log to which the count applies
    EMonitorLogType log = 1;

    // The number of the first/oldest entry available. (Always > 0)
    uint32 start_entry_id = 2;

    // Total number of log entries.
    uint32 entries = 3;

    // Log entry format - This is the format the log is stored in on the monitor.
    //                    This field is not used for clear log responses
    optional uint32 format = 4;     

} // LogEntryCount


/*  DataKeyErrorCodeBitmap are possible errors accessing the data key / program card. 
     */
message DataKeyErrorCodeBitmap {

    bool error_1 = 1;   // bitmap is TBD
    bool error_2 = 2;

} // DataKeyErrorCodeBitmap


