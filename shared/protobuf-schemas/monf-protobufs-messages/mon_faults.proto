/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  MON_FAULTS
    Enumerations and definitions for monitor faults.
*/
syntax = "proto3";
package mon_faults;

//***************************************************************************************** IMPORTS

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  ENUM EFaultCode defines possible fault sources.  This is a bitmap in the firmware that is converted
    to an enumerated type as only one fault code may be sent at a time, with the exception of a Diagnostic
    Fault.  Therefore, a field of this type is usually followed by a field for the Diagnostic fault.
    Some values only apply to certain monitors. */
enum EFaultCode {

    FAULT_CODE_UNSPECIFIED = 0;
    FAULT_CODE_SERIAL_PORT1 = 1;
    FAULT_CODE_MONITOR_MAINS = 3;
    FAULT_CODE_FLASH = 5;                      
    FAULT_CODE_CONTROLLER_VOLTAGE = 9;
    FAULT_CODE_24V_MONITOR_1 = 10;
    FAULT_CODE_24V_MONITOR_2 = 11;
    FAULT_CODE_CONFLICT = 12;
    FAULT_CODE_MULTIPLE_INDICATION = 14;
    FAULT_CODE_EXTERNAL_WATCHDOG = 15;
    FAULT_CODE_LACK_OF_SIGNAL = 16;
    FAULT_CODE_MINIMUM_Y_CLEARANCE = 17;        // Yellow
    FAULT_CODE_SKIPPED_Y_CLEARANCE = 18;        // Yellow
    FAULT_CODE_MINIMUM_Y_AND_R_CLEARANCE = 19;  // Yellow and Red
    FAULT_CODE_FIELD_CHECK = 20;
    FAULT_CODE_FLASHING_Y_ARROW = 21;           // Yellow
    FAULT_CODE_DATAKEY_DATA = 23;
    FAULT_CODE_DATAKEY_ABSENT = 24;
    FAULT_CODE_MULTIPLE_FAULTS = 100;           // More than one fault, other than a diag fault is set in mains

} // enum EFaultCode


/*  ENUM SubFaultSerialPort provides additional information for FAULT_CODE_SERIAL_PORT1. */
enum ESubFaultSerialPort {

    SUBFLT_SERIAL_UNSPECIFIED = 0;
    SUBFLT_SERIAL_TIMEOUT = 1;
    SUBFLT_SERIAL_QUALITY = 2;
    SUBFLT_SERIAL_RESET = 3;

} // enum ESubFaultSerialPort


/*  ENUM ESubFaultMultipleIndication provides additional information for FAULT_CODE_MULTIPLE_INDICATION. */
enum ESubFaultMultipleIndication {

    SUBFLT_MULTIND_UNSPECIFIED = 0;
    SUBFLT_MULTIND_GREEN_YELLOW = 1;
    SUBFLT_MULTIND_GREEN_RED = 2;
    SUBFLT_MULTIND_YELLOW_RED = 3;
    SUBFLT_MULTIND_MULTIPLE = 4;
    SUBFLT_MULTIND_FLASHING_Y_ARROW = 5;

} // enum ESubFaultMultipleIndication


/*  ENUM ESubFaultLackOfSignal provides additional information for FAULT_CODE_LACK_OF_SIGNAL. */
enum ESubFaultLackOfSignal {

    SUBFLT_LACKOFSIG_UNSPECIFIED = 0;
    SUBFLT_LACKOFSIG_NORMAL = 1;
    SUBFLT_LACKOFSIG_FLASHING_Y_ARROW = 2;

} // enum ESubFaultLackOfSignal


/*  ENUM ESubFaultSkippedYellow provides additional information for FAULT_CODE_SKIPPED_Y_CLEARANCE. */
enum ESubFaultSkippedYellow {

    SUBFLT_SKIPPEDY_UNSPECIFIED = 0;
    SUBFLT_SKIPPEDY_STANDARD = 1;
    SUBFLT_SKIPPEDY_FYA_FLASHING_Y = 2; // Flashing Yellow Arrow, Flashing Yellow
    SUBFLT_SKIPPEDY_FYA_GREEN = 3;      // Flashing Yellow Arrow

} // enum ESubFaultSkippedYellow


/*  ENUM ESubFaultDataKey provides additional information for FAULT_CODE_DATAKEY_DATA. */
enum ESubFaultDataKey {

    SUBFLT_DATAKEY_UNSPECIFIED = 0;
    SUBFLT_DATAKEY_CRC_ERROR = 1;
    SUBFLT_DATAKEY_FORMAT_ERROR = 2;
    SUBFLT_DATAKEY_DATA_ERROR = 3; 
    SUBFLT_DATAKEY_RAM_CRC_ERROR = 4;

} // enum ESubFaultDataKey


/*  MmuSubFaultTypeValue defines a message with the possible fault subcodes. */
message MmuSubFaultTypeValue {

    oneof subcode_type {
        mon_faults.ESubFaultSerialPort          serial_port = 1;
        mon_faults.ESubFaultMultipleIndication  multiple_indication = 2;
        mon_faults.ESubFaultLackOfSignal        lack_of_signal = 3;
        mon_faults.ESubFaultSkippedYellow       skipped_yellow = 4;
        mon_faults.ESubFaultDataKey             data_key = 5;   
    }

} // MmuSubFaultTypeValue


/*  ENUM ESubFaultDiagnostic provides additional information for diagnostic errors. */
enum ESubFaultDiagnostic {

    SUBFLT_DIAG_UNSPECIFIED = 0;
    SUBFLT_DIAG_1 = 1;      // TODO rename these for the actual errors
    SUBFLT_DIAG_2 = 2;
    SUBFLT_DIAG_3 = 3; 
    SUBFLT_DIAG_4 = 4;

} // enum ESubFaultDiagnostic


/*  MmuMonitoredControlStatesBitmap gives the states of various control states. 
    This matches the bitmap in a Fault Sequence Log Entry field "Controls"
 */
message MmuMonitoredControlStatesBitmap {

    reserved 1 to 3;   // Bits 0-2 are currently unassigned

    bool start_delay_relay = 4; // True if the start delay relay is energized
    bool reset_event = 5;  // True if reset bit is set in SDLC frame (bit 61 frame 129)
    bool startup_flash_call = 6; // True if startup flash bit is set in SDLC frame (bit 80 frame 129)
    bool ac_line_valid = 7; // True if the input AC power (mains) line is valid
    bool fault_relay = 8; // True if the fault relay is energized
} // MmuMonitoredControlStatesBitmap

    
/*  MmuMonitoredInputsStatusBitmap gives the status on monitored inputs. This matches the bitmap in
    a Fault Sequence Log Entry field "Inputs"
 */
message MmuMonitoredInputsStatusBitmap {

    bool monitor_24v_inhibit = 1; // TRUE means 24V is NOT being monitored
    // for the remaining fields, TRUE means status "OK"
    bool monitor_24v_1 = 2;
    bool monitor_24v_2 = 3;
    bool controller_voltage = 4;
    bool type_select = 5;
    bool red_enable = 6;
    bool external_reset = 7;
    bool port1_disable = 8;    
    bool program_card_inserted = 9;
    bool local_flash = 10;
    bool external_watchdog = 11;
    bool alarm = 12;    // True if an alarm is active

} // MmuMonitoredInputsStatusBitmap


/*  FaultIndicationChVoltCurrent represents one set of voltage and current measurements for an indicator channel. 
 */
message FaultIndicationChVoltCurrent {
    
    // The channel number
    uint32 channel = 1;

    // Indicator voltage in Volts
    float voltage_v = 2;

    // Indicator current in Amps, if provided.
    // In an MMU, this is not built-in, but can be provided by an LSU (Load Sensing Unit).
    optional float current_a = 3;   

} // FaultIndicationChVoltCurrent
