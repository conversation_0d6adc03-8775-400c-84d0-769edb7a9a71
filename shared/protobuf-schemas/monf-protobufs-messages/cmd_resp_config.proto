/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  CMD_RESP_CONFIG
    Command (from app) and response (from monitor) message formats for configuration related commands.
*/
syntax = "proto3";
package cmd_resp_config;

//***************************************************************************************** IMPORTS
import "basic.proto";
import "settings.proto";
import "dfu.proto";
import "mon_logs.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  CmdReadDataKey is used to Read the Program Card / Data Key (0x20)  */
message CmdReadDataKey {

    // Type of the Program Card / Data Key data to be read (Current or Pending).
    settings.EPcDkReadType pc_dk_type = 1; 

} // CmdReadDataKey


/*  RespReadDataKey returns the contents of the monitor's data key or program card.  
    The data comes from the Main processor response to the command 0x20 Read Data Key */
message RespReadDataKey {

    // This shows the source (data set) of the data that was read.
    settings.EConfigDataLocation source = 1;     

    // Raw contents of the entire Program Card or Data Key
    bytes data_key_data = 2;        // Max data length set in cmd_resp_config.options    

    // Type of the Program Card / Data Key data that was read.
    // If Pending was requested but there is no pending data, the response will be PC_DK_NONE
    // and the data_key_data will be empty.
    settings.EPcDkReadType pc_dk_type = 3; 

} // RespReadDataKey

//-------------------------------------------------------------------------------------------------

/*  CmdWriteDataKey is used to Write the Program Card / Data Key (0xA0)  */
message CmdWriteDataKey {

    // This is the destination (data set) for the data being written.
    settings.EConfigDataLocation destination = 1; 

    // Raw contents for the entire Program Card or Data Key
    bytes data_key_data = 2;        // Max data length set in cmd_resp_config.options  

    reserved 3;

} // CmdWriteDataKey


/*  RespWriteDataKey returns the result of the attempt to write the Pending Program Card / Data Key data.  
    The data comes from the Main processor response to the command 0xA0 Write Program Card / Data Key.
    NOTE: The data will be kept in a Pending state until an agency representative "accepts" the configuration. */
message RespWriteDataKey {

    // Result of the write operation
    settings.EWriteResult result = 1;           

} // RespWriteDataKey

//-------------------------------------------------------------------------------------------------

/*  CmdReadFactorySettings is used to read the monitor's factory configuration  */
message CmdReadFactorySettings {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadFactorySettings


/*  RespReadFactorySettings returns factory information from the Main processor response 
 to the command 0x21 Read Factory Settings */
message RespReadFactorySettings {

    // Monitor settings read from the unit
    settings.FactorySettings factory = 1;

    // monitor hardware revisions
    mon_logs.HardwareRevisionsMmu hardware = 2;       

    // the maximum number of channels this monitor supports.
    uint32 max_channels_supported = 3;

} // RespReadFactorySettings


//-------------------------------------------------------------------------------------------------

/*  CmdWriteFactorySettings is used to write the monitor's factory configuration.
 *  
 *  Details: Will wait for the Main processor to complete writing the settings to the unit
 *  before returning.
 *
 *  Note: The value for the pcb_options field must match the Main Processor PCB Option Jumpers, 
 *  as seen in the RespReadMonitorData message, for the command to be accepted. */
message CmdWriteFactorySettings {

    // Monitor settings to be written to the unit
    settings.FactorySettings factory = 1;

    reserved 2;     // deprecated field value - DO NOT USE

} // CmdWriteFactorySettings


/*  RespWriteFactorySettings result of writing the settings.    
    The data comes from the Main processor response to the command 0xA2 Write Factory Data 
    AND 0xA3 Check Write to Factory Data 
    NOTE: The monitor firmware will check the result and return the response at completion */
message RespWriteFactorySettings {

    settings.EWriteResult result = 1;

} // RespWriteFactorySettings

//-------------------------------------------------------------------------------------------------

/*  CmdWriteAgencyOptions is used to Write the Agency Options (0xAC) to the monitor.
 *  
 *  Details: Will wait for the Main processor to complete writing the new Config Log entry
 *  before returning. */
message CmdWriteAgencyOptions {

    // Agency settings to be written to the unit
    settings.AgencyOptionsMmu agency = 1;

} // CmdWriteAgencyOptions


/*  RespWriteAgencyOptions result of writing these options.    
    The data comes from the Main processor response to the Write Agency Options (0xAC) command.
    NOTE: The monitor firmware will check the result and return the response at completion */
message RespWriteAgencyOptions {

    settings.EWriteResult result = 1;

} // RespWriteAgencyOptions

//-------------------------------------------------------------------------------------------------

/*  CmdReadMonitorData is a single command to get both factory settings and firmware versions
    along with total monitor runtme  */
message CmdReadMonitorData {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadMonitorData


/*  RespReadMonitorData returns factory settings message with a firmware versions message 
    and the monitor runtime in seconds.  
    The data comes from the Main processor response to the command 0x23 Read Monitor Data */
message RespReadMonitorData  {

    // Monitor settings read from the unit
    settings.FactorySettings factory = 1;

    // The Monitor ID, assigned by the agency
    string monitor_id = 7;

    // User assigned description of the monitor
    string user_id = 2;             // Max string length set in cmd_resp_config.options 

    // monitor hardware revisions
    mon_logs.HardwareRevisionsMmu hardware = 3;       

    // supported number of channels
    uint32 supported_channel_count = 4;

    // (Uptime) Total run time for the monitor since being shipped, in seconds
    uint32 run_time_seconds = 5;

    // Firmware version information
    repeated dfu.FirmwareVersionSimple fw_versions = 6; // Max repeat count set in cmd_resp_config.options 

} // RespReadMonitorData


//-------------------------------------------------------------------------------------------------

/*  CmdReadUserSettings returns customer configurable settings  */
message CmdReadUserSettings {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadUserSettings


/*  RespReadUserSettings  
    The data comes from the Main processor response to the command 0x24 Read User Settings */
message RespReadUserSettings  {

    settings.UserSettings values = 1;

} // RespReadUserSettings


//-------------------------------------------------------------------------------------------------

/*  CmdWriteUserSettings sets customer configurable settings  */
message CmdWriteUserSettings {

    settings.UserSettings values = 1;

} // CmdWriteUserSettings


/*  RespWriteUserSettings  
    The data comes from the Main processor response to the command 0xA8 Write User Settings */
message RespWriteUserSettings  {

    settings.EWriteResult result = 1;

} // RespWriteUserSettings


//-------------------------------------------------------------------------------------------------

/*  CmdReadPort1DisableOverrides returns the state of the disable overrides for Serial Bus (SDLC)/Port 1  */
message CmdReadPort1DisableOverrides {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadPort1DisableOverrides


/*  RespReadPort1DisableOverrides  
    The data comes from the Main processor response to the command 0x25 Read Port 1 Disable Overrides */
message RespReadPort1DisableOverrides  {

    // State of the Port 1 Disable Override
    settings.EDisableOverrideState port1_disable_override = 1; 

    // Field checks disabled
    bool field_checks_disabled = 2;     

} // RespReadPort1DisableOverrides


//-------------------------------------------------------------------------------------------------

/*  CmdWritePort1DisableOverrides sets the state of the disable overrides for Serial Bus/Port 1  */
message CmdWritePort1DisableOverrides {

    // State of the Port 1 Disable Override
    settings.EDisableOverrideState port1_disable_override = 1; 

    // Field checks disable
    bool field_checks_disable = 2;  

} // CmdWritePort1DisableOverrides


/*  RespWritePort1DisableOverrides  
    The data comes from the Main processor response to the command 0xA9 Write Port 1 Disable Override */
message RespWritePort1DisableOverrides  {

    settings.EWriteResult result = 1;

} // RespWritePort1DisableOverrides


//-------------------------------------------------------------------------------------------------

/*  CmdReadUnitNetworkConfiguration returns monitor ID and network configuration stored
    in the monitor unit (as opposed to the data key)*/
message CmdReadUnitNetworkConfiguration {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadUnitNetworkConfiguration


/*  RespReadUnitNetworkConfiguration - this is the monitor ID and network configuration stored in the unit (not active values)
    The data comes from the Main processor response to the command 0x26 Read Configuration Unit Settings */
message RespReadUnitNetworkConfiguration {

    // Monitor and user ID strings
    basic.MonitorAndUserIds ids = 1;

    // Minimum flash time (values 6 - 15)
    uint32 min_flash_time = 2;

    // True when Ethernet settings from the data key (or program card) are in use
    bool using_datakey_ethernet = 3;

    // The unit's network configuration
    settings.NetworkSettings network_configuration = 4;
} // RespReadUnitNetworkConfiguration


//-------------------------------------------------------------------------------------------------

/*  CmdReadActiveNetworkConfiguration returns the system's presently active networking configuration  */
message CmdReadActiveNetworkConfiguration {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadActiveNetworkConfiguration


/*  RespReadActiveNetworkConfiguration - this is the presently active Ethernet config, including values
    assigned by a DCHP server if the DHCP client is active.
    The data comes from the Comms processor */
message RespReadActiveNetworkConfiguration  {

    // True when Ethernet settings from the data key (or program card) are in use
    bool using_datakey_ethernet = 1;

    // The active Ethernet interface values
    settings.NetworkSettings network_configuration = 2;

} // RespReadActiveNetworkConfiguration


//-------------------------------------------------------------------------------------------------

/*  CmdReadPerChannelConfiguration returns the configuration settings for one or more channels.
    Implementation note: The comms MCU will issue command 0x28 for each channel requested.  */
message CmdReadPerChannelConfiguration {

    // The channels (1-32) to request configuration 
    repeated uint32 channel = 1;     // Max repeat count set in cmd_resp_config.options 

} // CmdReadPerChannelConfiguration


/*  RespReadPerChannelConfiguration - returns the configuration settings for the requested channel(s)
    The data comes from the Main processor command 0x28 Read Configuration Per Channel Settings */
message RespReadPerChannelConfiguration  {

    // One field per requested channel
    repeated settings.PerChannelSettings ch_settings = 1;   // Max repeat count set in cmd_resp_config.options 

} // RespReadPerChannelConfiguration

//-------------------------------------------------------------------------------------------------

/*  CmdReadPerChannelCurrentSenseSettings returns the current sense settings for one or more channels
    Implmentation note: The Comms MCU will issue command 0x29 but only return the requested channels.  */
message CmdReadPerChannelCurrentSenseSettings {

    // The channels (1-32) to request configuration 
    repeated uint32 channel = 1;     // Max repeat count set in cmd_resp_config.options 

} // CmdReadPerChannelCurrentSenseSettings


/*  RespReadPerChannelCurrentSenseSettings - returns the current sense settings for the requested channel(s)
    The data comes from the Main processor command 0x29 Read Configuration Current Sense Settings */
message RespReadPerChannelCurrentSenseSettings  {

    // One field per requested channel
    // For 16X MMU, channels 2, 4, 6, & 8 may have Walk current thresholds
    repeated settings.PerChannelCurrentSenseSettings ch_settings = 1;   // Max repeat count set in cmd_resp_config.options 

} // RespReadPerChannelCurrentSenseSettings


//-------------------------------------------------------------------------------------------------

/*  CmdReadPerChannelPermissiveSettings returns the permissive turn channel(s) for a given channel  
    Implementation note: The Comms MCU will issue command 0x29 but only return the requested channels. */
message CmdReadPerChannelPermissiveSettings {

    // The channels (1-32) to request permissives 
    repeated uint32 channel = 1;     // Max repeat count set in cmd_resp_config.options 

} // CmdReadPerChannelPermissiveSettings


/*  RespReadPerChannelPermissiveSettings - returns the permissive settings for the requested channel(s)
    The data comes from the Main processor command 0x2A Read Configuration Permissive Settings */
message RespReadPerChannelPermissiveSettings  {

    // One field per requested channel
    repeated settings.PerChannelPermissives ch_settings = 1;    // Max repeat count set in cmd_resp_config.options 

} // RespReadPerChannelPermissiveSettings

//-------------------------------------------------------------------------------------------------

/*  CmdReadFlashingYellowArrowConfiguration returns all flashing yellow arrow configuration instances. 
*/
message CmdReadFlashingYellowArrowConfiguration {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdReadFlashingYellowArrowConfiguration


/*  RespReadFlashingYellowArrowConfiguration - returns all instances (typically 4)
    The data comes from the Main processor response to the command 0x2B Read Configuration FYA Settings */
message RespReadFlashingYellowArrowConfiguration  {

    // The FYA configuration for each instance
    repeated settings.FlashingYellowArrowSettings fya_settings = 1; // Max repeat count set in cmd_resp_config.options 

} // RespReadFlashingYellowArrowConfiguration

//-------------------------------------------------------------------------------------------------

/*  Types for Remote Configuration Acceptance / Fault Clear  */
enum CpRemoteResetType {
    REMOTE_RESET_TYPE_UNSPECIFIED = 0;
    REMOTE_RESET_TYPE_NONE = 1;
    REMOTE_RESET_TYPE_PENDING_CONFIG_ACCEPTANCE = 2;
    REMOTE_RESET_TYPE_FAULT_CLEAR = 3;
}

/*  CmdRemoteReset is used to do a Remote Fault Clear / Configuration Acceptance (Remote Reset 0xFE).
 * To be accepted, the AgencyOptionsMmu field permit_remote_config_acceptance or permit_remote_fault_clear
 * must have been set to true. */
message CmdRemoteReset {

    // The type of remote reset to perform.
    CpRemoteResetType reset_type = 1;

} // CmdRemoteReset


/*  RespRemoteReset - returns the result of the Remote Fault Clear / Configuration Acceptance (Remote Reset).
 * Note that if the command had no effect (eg, no Fault to clear or Configuration Pending to accept),
 * the command will still be accepted and the OK response will be returned. */
message RespRemoteReset {

    settings.EWriteResult result = 1;

} // RespRemoteReset

