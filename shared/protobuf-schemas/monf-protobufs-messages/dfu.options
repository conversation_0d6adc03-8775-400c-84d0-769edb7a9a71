 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   DFU
#   nanopb options file for dfu.proto

# DfuManifestEntry
#   Max string lengths
#   NOTE: For Zephyr implementations, check that this is <= CONFIG_FILE_SYSTEM_MAX_FILE_NAME
dfu.DfuManifestEntry.filename      max_size:32
#   Max data lengths. SHA256 hash result is 32 bytes
dfu.DfuManifestEntry.hash          max_size:32

# FirmwareImageVersion
#   Max string lengths
dfu.FirmwareImageVersion.version        max_size:12
dfu.FirmwareImageVersion.version_build  max_size:11
dfu.FirmwareImageVersion.build_date     max_size:11
