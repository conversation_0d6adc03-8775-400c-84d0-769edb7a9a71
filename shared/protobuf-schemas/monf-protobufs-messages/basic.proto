/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  BASIC
    General use messages for common complex fields.
*/
syntax = "proto3";
package basic;

//***************************************************************************************** IMPORTS

//***************************************************************************************** OPTIONS
// This proto file has a companion .options file for nanopb

//**************************************************************************************** MESSAGES

/*  LocalDateTime represents the time an event occurred on the device where it occurred, at the
    local time of the device.  This format is used to prevent misinterpretations of an epoch or
    similar time format due to timezone and daylight savings settings. */
message LocalDateTime {
    // Year value, e.g. 2024
    uint32 year = 1;

    // Month of the year, 1-12
    uint32 month = 2;

    // Day of the month, 1-31
    uint32 day = 3;

    // Hour of the day, 0-23
    uint32 hour = 4;    

    // Minute of the hour, 0-59
    uint32 minute = 5;      

    // Second of the minute, 0-59
    uint32 second = 6;       

} // LocalDateTime



/*  VersionStrThree is used for any version information with 3 parts, major.minor.revision,
    where the values are ASCII characters
 */
message VersionStrThree {
    string major = 1;       // Max string length set in basic.options
    string minor = 2;       // Max string length set in basic.options
    string revision = 3;    // Max string length set in basic.options
} // VersionStrThree


/*  DateStr is used for a date where where the values are ASCII characters.
 */
message DateStr {
    string month = 1;       // Max string length set in basic.options
    string day = 2;         // Max string length set in basic.options
    string year = 3;        // Max string length set in basic.options
} // DateStr


/*  IpAddressV4 provides an IPV4 Ethernet address: octet_1_ms0.octet_2.octet_3.octet_4_lso
 */
message IpAddressV4 {

    // Most Significant octet, XX.xx.xx.xx
    uint32 octet_1_mso = 1;

    // Second octet, xx.XX.xx.xx
    uint32 octet_2 = 2;

    // Third octet, xx.xx.XX.xx
    uint32 octet_3 = 3;

    // Least Significant octet, xx.xx.xx.XX
    uint32 octet_4_lso = 4;

} // IpAddressV4


/*  NowMinMaxFloat - Present, minimum, and maxiumum floating point values
 */
message NowMinMaxFloat {

    float present = 1;

    float minimum = 2;

    float maximum = 3;

} // NowMinMaxFloat


/*  ModelAndSerialNumber - This is a BASIC message mostly so that the maximum length set for
    nanopb on the embedded side can be in one place - the basic.options file, instead of sprinkled
    in several places throughout the protobuf .options files.  This is done to prevent errors caused
    by changing the length in one place and forgetting to update others.
 */
message ModelAndSerialNumber {

    // serial number, ASCII string (null terminated)
    string serial = 1;   // Max string length set in basic.options

    // model number, ASCII string (null terminated)
    string model = 2;    // Max string length set in basic.options   

} // ModelAndSerialNumber


/*  MonitorAndUserIds - This is a BASIC message mostly so that the maximum length set for
    nanopb on the embedded side can be in one place - the basic.options file, instead of sprinkled
    in several places throughout the protobuf .options files.  This is done to prevent errors caused
    by changing the length in one place and forgetting to update others.
 */
message MonitorAndUserIds {

    // Monitor ID, ASCII string (null terminated)
    string monitor = 1;   // Max string length set in basic.options

    // User ID, ASCII string (null terminated)
    string user = 2;    // Max string length set in basic.options   

} // MonitorAndUserIds

/*  ENUM EDaysOfTheWeek */
enum EDaysOfTheWeek {

    DAY_UNSPECIFIED = 0;
    DAY_SUNDAY = 1;
    DAY_MONDAY = 2;
    DAY_TUESDAY = 3;
    DAY_WEDNESDAY = 4;
    DAY_THURSDAY = 5;
    DAY_FRIDAY = 6;
    DAY_SATURDAY = 7;

} // enum EDaysOfTheWeek


/*  DaylightSavingsSettings - DST Start and End setting
 */
message DaylightSavingsSettings {

    // The number of the week in the month DST starts (1-4)
    uint32 start_week_of_month = 1;

    // The day of the week DST starts
    basic.EDaysOfTheWeek start_day_of_week = 2;

    // The month in which DST starts (1-12) 
    uint32 start_month_of_year = 3;

    // The number of the week in the month DST ends (1-4)
    uint32 end_week_of_month = 4;

    //The day of the week DST ends
    basic.EDaysOfTheWeek end_day_of_week = 5;

    // The month in which DST ends (1-12) 
    uint32 end_month_of_year = 6;

} // DaylightSavingsSettings

