 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   MON_LOGS
#   nanopb options file for mon_logs.proto

# NOTE: bytes / entry sizes are based on the Protocol Buffer representation.  This is often much 
#       larger than the inter-processor serial bus size because of added field identifier values,
#       and uint32 replacing smaller typed values (uint32 may be up to 5 bytes).

# PowerLogMultipleEntriesMmu
#   Max repeated field counts.  At 162 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.PowerLogMultipleEntriesMmu.log_entry    max_count:22

# ResetLogMultipleEntriesMmu
#   Max repeated field counts.  At 56 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.ResetLogMultipleEntriesMmu.log_entry    max_count:64

# ClockLogMultipleEntriesMmu
#   Max repeated field counts.  At 90 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.ClockLogMultipleEntriesMmu.log_entry    max_count:40

# ConfigLogEntryMmu
#   Max data length
mon_logs.ConfigLogEntryMmu.data_key_data       max_size:512

# ConfigLogMultipleEntriesMmu
#   Max repeated field counts.  At 910 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.ConfigLogMultipleEntriesMmu.log_entry    max_count:4

# Port1LogLogMultipleEntriesMmu
#   Max repeated field counts.  At 124 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.Port1LogLogMultipleEntriesMmu.log_entry    max_count:29

# FaultHeaderLogMultipleEntriesMmu
#   Max repeated field counts.  At 299 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.FaultHeaderLogMultipleEntriesMmu.log_entry    max_count:12

# FaultMeasurementLogEntryMmu
#   Max repeated field counts 
mon_logs.FaultMeasurementLogEntryMmu.red_channels       max_count:32
mon_logs.FaultMeasurementLogEntryMmu.yellow_channels    max_count:32
mon_logs.FaultMeasurementLogEntryMmu.green_channels     max_count:32
mon_logs.FaultMeasurementLogEntryMmu.walk_channels      max_count:32

# FaultMeasurementLogMultipleEntriesMmu
#   Max repeated field counts.  At 2624 bytes/entry this limits the entries to less than 3.5 KB 
mon_logs.FaultMeasurementLogMultipleEntriesMmu.log_entry    max_count:1

# FaultSequenceLogMultipleEntriesMmu
#   Max repeated field counts.  At 140 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.FaultSequenceLogMultipleEntriesMmu.log_entry    max_count:25

# FaultFactsLogEntry
#   Size of the sample array in bytes. Sized for 249 x 2-byte samples
mon_logs.FaultFactsLogEntry.samples_16b_cv              max_size:500

# FaultFactsLogMultipleEntriesMmu
#   Max repeated field counts.  At 568 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.FaultFactsLogMultipleEntriesMmu.log_entry    max_count:6

# AlarmLogEntry
#   Max string lengths
mon_logs.AlarmLogEntry.text                             max_size:61

# AlarmLogMultipleEntriesMmu
#   Max repeated field counts.  At 110 bytes/entry this limits the entries to about 3.5 KB 
mon_logs.AlarmLogMultipleEntriesMmu.log_entry    max_count:32
