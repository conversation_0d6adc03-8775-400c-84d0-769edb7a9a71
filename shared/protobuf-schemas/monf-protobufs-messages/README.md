# Monitors Protocol Buffers Repository

This repository contains protocol buffer definitions for communication between a monitor (MMU / CMU) and a PC App.

It is expected these messages will also form the basis for communication with the cloud and possibly over Bluetooth.

- [Monitors Protocol Buffers Repository](#monitors-protocol-buffers-repository)
  - [Protocol Buffer Language](#protocol-buffer-language)
    - [\*.options Files](#options-files)
    - [Other Files](#other-files)
    - [Protocol Buffer Conventions](#protocol-buffer-conventions)
    - [Maintaining .proto Files](#maintaining-proto-files)
    - [VS Code Extension](#vs-code-extension)
  - [Repository Organization](#repository-organization)
    - [Top Level Files](#top-level-files)
    - [Command and Response Definitions](#command-and-response-definitions)
    - [Message Field Files](#message-field-files)
  - [Message Sizes](#message-sizes)
  - [Testing Changes](#testing-changes)


## Protocol Buffer Language

These files use the [Protocol Buffers Language version 3](https://protobuf.dev/programming-guides/proto3/), also known
as `proto3`.Additional information is available by browsing the full documentation from the language link.

On the embedded side, the [compiler extension for the C language "nanopb"](https://github.com/nanopb/nanopb) is used
to compile the message definition files. 

### *.options Files

The `.options` files are used by the **nanopb** compiler, and set limits on the lengths of strings, byte arrays, and repeated
fields.  Firstly, this sets a limit on the possible size of decoded messages, which is important in the embedded environment.
Secondly, this simplifies both encoding and decoding as otherwise these fields must use callbacks during serialization and
de-serialization because the possible message length isn't known beforehand.

Note that it is possible to use protocol buffer compiler extensions to include these limits in the `.proto` file on the field
declaration line, for example:
  
`string name = 1 [(nanopb).max_size = 40];`  

While this improves readability, the drawback is that the standard `protoc` compiler will give warnings on all such extensions
when run without the **nanopb** extension.  Since this repository is used on both the software and firmware sides, having to
install **nanopb** on all software development environments and include it in compiler calls or wade through dozens of warnings
on a compile is seen as a bigger drawback than using separate `.options` files.
[A reference for the option file syntax is here.](https://jpa.kapsi.fi/nanopb/docs/reference.html#proto-file-options)

Not all `.proto` files have a corresponding `.options` files, as those `.proto` files do not use any undefined length fields.
(If such a field is added, then a corresponding `.options` file must be created.)

**String lengths**: In the nanopb generated code for protobuf files, strings are declared with the `.max_size` length value and
interpreted as null-terminated strings.  Therefore, if a string of up to 4 characters is desired, it must be sized with 
`.max_size=5` in the `.options` file.

### Other Files
`formatproto.blank` is a template file that can be used when creating new `.proto` files.

### Protocol Buffer Conventions
All the files in this respository should conform to the [protocol buffer style guide.](https://protobuf.dev/programming-guides/style/)

In addition, the following conventions apply:
- `enum` types should be named with a capitol 'E' prefixing the enumeration name.  When used as a field type in a message, this
distinguishes enumerations from message types.

```
            enum EResultList {
                LIST_ITEM_1 = 1;
                LIST_ITEM_2 = 2;
            } // enum EResultList

            message ResultSummary {
                EResultList items = 1;    // this field is an enumeration
                ListResults results = 2;  // this field is another message
            } // ResultSummary
```

- All `.proto` files must have a `package` name, and it must match the filename (before the `.proto` extension.)  When messages
and enumerations are used in another `.proto` file, they must be preceded by the package name and this makes finding the file
where they are declared easy.

- **Channel bitmap** fields where each bit represents a state on a monitor channel are named with the suffix `_chmap`.  In these
fields, the least significant bit 0 is the state on channel 1, bit 1 is the state on channel 2, bit 2 is channel 3, etc.  The name
before the prefix should follow standard boolean name conventions (i.e. make a statement or pose a question.)  
Example: 

> `fixed32 light_is_on_chmap = 1;  // state of the light on each of 32 channels`

- In general (excepting channel bitmaps as above), fields that are represented as **bitmaps** on the embedded side should be
converted to a message with a `bool` field for each defined bit.  Protocol buffers does not have good support for bitmaps, and
it's preferred to decouple the protobuf message from the original bit positions.  This allows the message to remain unchanged
even if the underlying bit representation is changed.  It also allows a single message to support multiple bitfield variations
(i.e. differences in bit meanings between similar fields for MMU vs CMU.)  Note that field values still at their default 
(= false for `bool`) are _not_ sent on the wire.  This means the length of the message is only affected by "true" fields, and
not the overall number of `bool` fields.


### Maintaining .proto Files

1. Field numbers assigned in a message may never be changed or reused.
   a. If a field is deleted, comment the field and add the number to a reserved list in the message.
2. ENUM values may never be changed or reused.
   a. If an enum value is deleted, comment the value and add the number to a reserved list in the enum.
   b. If an enum in the code diverges from the protobuf enum, then a translation must be made when
      creating a message.
3. Never move an existing message field into an existing "oneof".
4. Follow the Style Guide: https://protobuf.dev/programming-guides/style/
   a. For ENUMs, preface the name with "E", e.g. "EStatusCodes"
5. Use Best Practices: https://protobuf.dev/programming-guides/dos-donts/
6. Note that protocol buffer serialization is not canonical, i.e. the same message may be represented
   differently once serialized "on the wire."

### VS Code Extension

The VS Code extension **Protobuf (Protocol Buffers)** is a decent syntax highlighting, navigation, and auto-completion
utility.  Search the extensions marketplace for `pbkit.vscode-pbkit`.

The following limitations are known:
- When a syntax error exists, the syntax highlighting for the remainder of the file after the error often doesn't work.
If the highlighting doesn't seem to be working, there's probably a syntax error!
- The navigation feature doesn't extend outside the present file.  Use the package name to find the file with the object
definition.

## Repository Organization

### Top Level Files

The top level files in the repository are:
- `discovery.proto`: The message definitions for the UDP discovery socket.
- `authentication.proto`: This has the message definitions used over the UDP DTLS authentication socket between the PC App
and the monitor firmware.
- `wrappers.proto` : This file has the top-level message wrappers that are used for all messages exchanged over the
Websocket interface by the PC App and monitor firmware.  Every message sent by either side uses the respective wrapper, always.  
The command (or response) is defined by the `oneof{}` generated code "which_command" (or "which_response").  This also
identifies the message type containing the parameters/data for the command (or response).

### Command and Response Definitions

The `cmd_resp_xxxx.proto` files contain the command and response message definitions for commands related to different
aspects of the interface.  Each command and response message are defined as a pair together in the file.  Ideally,
these files contain no other message definitions than the upper level command and response pairs.

### Message Field Files

The remaining files group enumerations and message definitions used as fields within commands or responses, or within
other sub-message definitions.

`basic.proto` contains message defitions for common complex field types like dates, times, versions, IP addresses,
and commonly grouped values.

## Message Sizes

At this time in the project (5/2024), the goal is to try to keep the worst-case message sizes at the wrapper level
around 2 KB to 3 KB, though this may change later during development. 

## Testing Changes

You can quickly validate that your protobuf changes don't contain format errors by doing a test `protoc` compile
of your changed files.  Do something like this:

```bash
mkdir temp
protoc --cpp_out=temp dfu.proto
```

There should be no errors reported, and the temp folder should contain valid C++ source and header files.
