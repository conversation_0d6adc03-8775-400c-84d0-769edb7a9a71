/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  CMD_RESP_DFU
    Commands and responses for updating firmware in the monitor
*/
syntax = "proto3";
package cmd_resp_dfu;

//***************************************************************************************** IMPORTS
import "dfu.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  CmdManifestVersions - requests the firmware images and versions in the manifests on the system,
                          along with the update status. */
message CmdManifestVersions {

    // The choice of manifest to return the versions from.  
    // FW_MANIFEST_PRESENT = FW versions on the unit after an update is complete.  During an update,
    //                       these are the versions before the update started, and may not be accurate
    //                       while the update is still in process.
    // FW_MANIFEST_UPDATE =  During an update, this returns the versions that the firmware is being 
    //                       updated to.  After an update is completed successfully, this will not 
    //                       return any entries.
    // FW_MANIFEST_NONE =    No manifest entries are returned; used to just get fw_update_status
    dfu.EFirmwareVersionsManifest manifest_type = 1; 

} // CmdManifestVersions


/*  RespManifestVersions - gives the firmware image version information from the selected manifest.
 *  If the manifest_type is FW_MANIFEST_NONE, then only the manifest_type, fw_update_status, 
 *  and this_model_number fields will contain useful information; 
 *  the other (repeated or string) fields will be empty.  */
message RespManifestVersions  {

    // Echoes the choice of the manifest requested in the command
    dfu.EFirmwareVersionsManifest manifest_type = 1; 

    // Report the version information and update status for each image in the requested manifest. 
    repeated dfu.DfuManifestStatusEntry image_statuses = 2;   // Max repeat count set in cmd_resp_dfu.options 

    // Overall Status of any firmware update in progress.
    // This may be the only field returned when the manifest choice is FW_MANIFEST_NONE
    dfu.EFirmwareUpdateStatus fw_update_status = 3;    

    // Enum for the specific Model Number of this monitor
    dfu.EMonitorModelNumber this_model_number = 4;

    // List of the Enums for the Model Numbers that this manifest applies to.
    // This may be empty when reporting the FW_MANIFEST_PRESENT.
    repeated dfu.EMonitorModelNumber supported_model_numbers = 5;

    // Version (Major.Minor.Revision) for the overall package of firmware
    // This may differ from one or more of the image versions, unless all the images in 
    // the package have one unified version number.
    // Version in the format <major>.<minor>.<revision> with ASCII numerals for the values, up to 3 digits each.
    string package_version = 6;

    // Date the package was assembled, YYYY/MM/DD
    // Month (MM) and Day (DD) are always 2 digits, year is always 4 digits.
    // Separator is required and must be slash '/'
    string package_build_date = 7;    

} // RespManifestVersions

//-------------------------------------------------------------------------------------------------

/*  CmdRebootCommsMcu - reboot the comms processor */
message CmdRebootCommsMcu {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdRebootCommsMcu


/*  RespRebootCommsMcu - Confirms the reboot. */
message RespRebootCommsMcu  {

    // gives the milliseconds period until the reboot occurs
    uint32 reboot_in_ms = 1; 

    // Additional result code with more detail than errors returned in the wrapper field "code"
    dfu.EDfuResultCode result_code = 2; 

} // RespRebootCommsMcu

//-------------------------------------------------------------------------------------------------

/*  CmdInitiateFirmwareUpdate - Enables a Firmware Update process to begin */
message CmdInitiateFirmwareUpdate {

    // This is a placeholder field for future params
    uint32 always0 = 1; 

} // CmdInitiateFirmwareUpdate


/*  RespInitiateFirmwareUpdate - Confirms the update starting. */
message RespInitiateFirmwareUpdate  {

    // Additional result code with more detail than errors returned in the wrapper field "code"
    dfu.EDfuResultCode result_code = 1; 

} // RespInitiateFirmwareUpdate

//-------------------------------------------------------------------------------------------------

/* CmdFirmwareUpdateManifest - The Firmware Update Manifest message, containing all the image files to be updated.
 * The files should be listed in this order
 * - Other Processors connected to the Main Processor
 * - Main Processor application code
 * - (Uncommon) Main Processor MCUboot
 * - Protected Storage update for the Comms Processor
 * - Comms Processor application code
 * - (Uncommon) Comms Processor MCUboot
 * The manifest may list all of these, or just one file, or some subset, though 
 * it is recommended that all the images listed (except MCUboot) are updated together,
 * to keep the system in a consistent, validated state.
 */
message CmdFirmwareUpdateManifest {

    // Timeout for the whole update process
    uint32 timeout_seconds = 1;    

    // Entries for each image in the update
    repeated dfu.DfuManifestEntry entries = 2;      // Max repeat count set in cmd_resp_dfu.options 

    // List of the Enums for the Model Numbers that this manifest applies to.
    // The manifest will be rejected if the monitor's Model Number is not in this list.
    repeated dfu.EMonitorModelNumber supported_model_numbers = 3;

    // Version (Major.Minor.Revision) for the overall package of firmware
    // This may differ from one or more of the image versions, unless all the images in 
    // the package have one unified version number.
    // Version in the format <major>.<minor>.<revision> with ASCII numerals for the values, up to 3 digits each.
    string package_version = 4;

    // Date the package was assembled, YYYY/MM/DD
    // Month (MM) and Day (DD) are always 2 digits, year is always 4 digits.
    // Separator is required and must be slash '/'
    string package_build_date = 5;    

} // CmdFirmwareUpdateManifest


/*  RespFirmwareUpdateManifest - Confirms the update starting. */
message RespFirmwareUpdateManifest  {

    // Additional result code with more detail than errors returned in the wrapper field "code"
    dfu.EDfuResultCode result_code = 1; 

} // RespFirmwareUpdateManifest

//-------------------------------------------------------------------------------------------------

/*  CmdBeginFirmwareDownload - Start the download of an image file using CmdFirmwareDownloadChunk */
message CmdBeginFirmwareDownload {

    // MCU for which this file is intended
    dfu.EProcessorType target_mcu = 1;

    // Type of image being updated
    dfu.EImageType image_type = 2;

    // Name must be the same as provided in the DfuManifestEntry
    string filename = 3;        // Max string length set in cmd_resp_dfu.options

    // ID that will be used to identify the subsequent download chunk commands
    uint32 download_request_id = 4;

} // CmdBeginFirmwareDownload


/*  RespBeginFirmwareDownload - Confirms the image download start. */
message RespBeginFirmwareDownload  {

    // Additional result code with more detail than errors returned in the wrapper field "code"
    dfu.EDfuResultCode result_code = 1; 

    // gives the maximum allowed size in bytes of each image chunk
    // NOTE: This must not exceed the nanopb cmd_resp_dfu.options max size for cmd_resp_dfu.CmdFirmwareDownloadChunk.chunk_data
    uint32 max_chunk_size_bytes = 3;

} // RespBeginFirmwareDownload


//-------------------------------------------------------------------------------------------------

/*  CmdFirmwareDownloadChunk - send one chunk of the firmware image file */
message CmdFirmwareDownloadChunk {

    // ID that was provided in the CmdBeginFirmwareDownload
    uint32 download_request_id = 1;

    // Offset in the file where this chunk should be written
    fixed32 offset = 2;

    // Size of the chunk, in bytes
    uint32 size_bytes = 3;

    // True if this is the last chunk of the file
    bool last_chunk = 4; 

    // The byte array of chunk data
    bytes chunk_data = 5;  // Max data length set in cmd_resp_dfu.options

} // CmdFirmwareDownloadChunk


/*  RespFirmwareDownloadChunk - returns the expected and received offsets. 
    These will normally be the same.  If the response has a RESP_SEQUENCE_ERR code,
    these will show the disparity in the recevied chunk */
message RespFirmwareDownloadChunk  {

    // Offset in the file that was expect for this chunk
    fixed32 offset_expected = 1;

    // Offset in the file of this chunk
    fixed32 offset_received = 2;

    // This will be set to confirm the last chunk of the file was received
    bool complete = 3;

    // Additional result code with more detail than errors returned in the wrapper field "code"
    dfu.EDfuResultCode result_code = 4; 

} // RespFirmwareDownloadChunk



