 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   AUDIT_LOGS
#   nanopb options file for audit_logs.proto

# AuditLogEntryComms
#   Max string lengths
audit_logs.AuditLogEntryComms.entry_text                    max_size:192
audit_logs.AuditLogEntryComms.string_text                   max_size:64

# AuditLogMultipleEntriesComms
#   Max repeated field counts.  At 297 bytes/entry this limits the entries to about 2.5 KB (as read from Main proc)
audit_logs.AuditLogMultipleEntriesComms.log_entry           max_count:12
