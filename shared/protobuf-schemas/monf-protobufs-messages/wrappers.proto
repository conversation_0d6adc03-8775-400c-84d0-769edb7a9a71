/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  WRAPPERS
    Wrapper messages for command messages sent by the app to the monitor unit, and response messages sent
    by the monitor unit back to the app.
*/
syntax = "proto3";
package wrappers;

//***************************************************************************************** IMPORTS
import "cmd_resp_comms.proto";
import "cmd_resp_logs.proto";
import "cmd_resp_config.proto";
import "cmd_resp_stats.proto";
import "cmd_resp_dfu.proto";
import "cmd_resp_realtime.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES

/*  WrapperCommand is the wrapper for all websocket command messages from the application to the 
    monitor unit.  (Excluding authentication messages over raw TCP) */
message WrapperCommand {

    // The command interface version
    uint32 version = 1;

    // This value is assigned by the app and is echoed back in a response, to match responses
    // to commands.  
    // Some log read commands may generate multiple responses, and all the responses will have the
    // same request_id value.
    uint32 request_id = 2; 
    
    // The command with its parameters.  See the command message definition for details on the command.
    oneof command {

        // communications commands
        // (none defined yet)

        // log request commands
        cmd_resp_logs.CmdRequestLogCounts                       request_log_counts = 20;
        cmd_resp_logs.CmdRequestLogClear                        log_clear = 21;
        cmd_resp_logs.CmdRequestLogEntries                      request_log = 22;
        cmd_resp_logs.CmdRequestAuditLogCounts                  req_auditlog_counts = 23;
        cmd_resp_logs.CmdRequestAuditLogClear                   auditlog_clear = 24;
        cmd_resp_logs.CmdRequestAuditLogReset                   audit_log_reset= 25;
        cmd_resp_logs.CmdRequestAuditLogEntries                 request_auditlog = 26;

        // configuration commands
        cmd_resp_config.CmdReadMonitorData                      rd_monitor_data = 31;
        cmd_resp_config.CmdReadUnitNetworkConfiguration         rd_network_config_unit = 32;
        cmd_resp_config.CmdReadActiveNetworkConfiguration       rd_network_config_active = 33;   
        cmd_resp_config.CmdReadPerChannelConfiguration          rd_channel_config = 34;
        cmd_resp_config.CmdReadPerChannelCurrentSenseSettings   rd_channel_current_sense = 35;
        cmd_resp_config.CmdReadPerChannelPermissiveSettings     rd_channel_permissives = 36;
        cmd_resp_config.CmdReadFlashingYellowArrowConfiguration rd_fya_config = 37;             
        cmd_resp_config.CmdReadDataKey                          rd_data_key = 38;  // or program card
        cmd_resp_config.CmdWriteDataKey                         wr_data_key = 39; // or program card
        cmd_resp_config.CmdReadFactorySettings                  rd_factory_settings = 40;
        cmd_resp_config.CmdWriteFactorySettings                 wr_factory_settings = 41;
        cmd_resp_config.CmdReadUserSettings                     rd_user_settings = 42;
        cmd_resp_config.CmdWriteUserSettings                    wr_user_settings = 43;
        cmd_resp_config.CmdReadPort1DisableOverrides            rd_port1_disables = 44;
        cmd_resp_config.CmdWritePort1DisableOverrides           wr_port1_disables = 45;
        cmd_resp_config.CmdWriteAgencyOptions                   wr_agency_options = 46;
        cmd_resp_config.CmdRemoteReset                          remote_reset = 47;

        // status and statistics commands
        cmd_resp_stats.CmdReadPort1Statistics                   rd_port1_stats = 50;
        cmd_resp_stats.CmdReadDataKeyStatistics                 rd_data_key_stats = 51; // or program card
        cmd_resp_stats.CmdReadMainToIsolatedCommStatistics      rd_main_iso_comms_stats = 52;
        cmd_resp_stats.CmdReadMainToCommsCommStatistics         rd_main_comms_stats = 53;
        cmd_resp_stats.CmdReadCommsToMainCommStatistics         rd_comms_main_stats = 54;
        cmd_resp_stats.CmdReadFlashStatistics                   rd_flash_stats = 55;
        cmd_resp_stats.CmdReadWatchdogStatistics                rd_watchdog_stats = 56;
        cmd_resp_stats.CmdGetTimeDatesDst                       rd_date_time_dst = 57;
        cmd_resp_stats.CmdClearStatistics                       clear_stats = 58;
        cmd_resp_stats.CmdSetTimeDatesDst                       wr_date_time_dst = 59;
        cmd_resp_stats.CmdRemoteDisplayButtonEvent              remote_display_button_event = 65;

        // realtime data commands
        cmd_resp_realtime.CmdStartRealtimeData                  start_realtime = 60;
        cmd_resp_realtime.CmdStopRealtimeData                   stop_realtime = 61;
        // Note that 65 is used for remote_display_button_event above

        // DFU commands
        cmd_resp_dfu.CmdManifestVersions                        manifest_versions = 70;
        cmd_resp_dfu.CmdRebootCommsMcu                          reboot_comms_mcu = 71;
        cmd_resp_dfu.CmdInitiateFirmwareUpdate                  initiate_dfu = 72;
        cmd_resp_dfu.CmdFirmwareUpdateManifest                  send_fw_manifest = 73;
        cmd_resp_dfu.CmdBeginFirmwareDownload                   begin_firmware_download = 74;
        cmd_resp_dfu.CmdFirmwareDownloadChunk                   download_firmware_chunk = 75;

        // test commands
        cmd_resp_comms.CmdChunkTest                             test_chunk = 90;

    }

    reserved 10, 11;           // deprecated field values - DO NOT USE

} // WrapperCommand



/*  WrapperResponse is the wrapper for all websocket reaponse and realtime messages from the  
    monitor unit to the application.  (Excluding authentication messages over raw TCP) */
message WrapperResponse {

    // The response interface version
    // NOTE: This may not match the WrapperCommand.version!  This is the command and response version structures supported
    //       by this firmware.  Generally, versions will be backwards compatible, but firmware with a response version older
    //       than the command version will not know how to interpret new parameters (fields) in the new command version, and
    //       not return new fields or data in the responses, but should interpret the command and provide the response 
    //       according to the version format given here.
    uint32 version = 1;    

    // This value matches the request_id value in the command that generated the response.  
    // Some log read commands may generate multiple responses, and all the responses will have the
    // same request_id value.
    // For Realtime data messages, the request_id matches the last "start_realtime" command request_id
    // the started or modified the requested realtime data.
    uint32 request_id = 2; 

    // The response code returns any errors that occurred processing the command.
    EResponseCodes code = 3;

    // An optional string with Plaintext result of the command, for human eyes
    string result_txt = 4;  // Max string length set in cmd_resp_dfu.options 
    
    // A response to a command with its parameters, or realtime data.  See the response message definition for details on the response.
    oneof response {

        // communications responses
        cmd_resp_comms.RespAuthStatus                               auth_status = 11;

        // log request responses
        cmd_resp_logs.RespRequestLogCounts                          request_log_counts = 20;
        cmd_resp_logs.RespRequestLogClear                           log_clear = 21;
        cmd_resp_logs.RespRequestLogEntries                         request_log = 22;
        cmd_resp_logs.RespRequestAuditLogCounts                     req_auditlog_counts = 23;
        cmd_resp_logs.RespRequestAuditLogClear                      auditlog_clear = 24;
        cmd_resp_logs.RespRequestAuditLogReset                      audit_log_reset= 25;
        cmd_resp_logs.RespRequestAuditLogEntries                    request_auditlog = 26;        

        // configuration responses
        cmd_resp_config.RespReadMonitorData                         rd_monitor_data = 31;
        cmd_resp_config.RespReadUnitNetworkConfiguration            rd_network_config_unit = 32;
        cmd_resp_config.RespReadActiveNetworkConfiguration          rd_network_config_active = 33;   
        cmd_resp_config.RespReadPerChannelConfiguration             rd_channel_config = 34;
        cmd_resp_config.RespReadPerChannelCurrentSenseSettings      rd_channel_current_sense = 35;
        cmd_resp_config.RespReadPerChannelPermissiveSettings        rd_channel_permissives = 36;
        cmd_resp_config.RespReadFlashingYellowArrowConfiguration    rd_fya_config = 37;             
        cmd_resp_config.RespReadDataKey                             rd_data_key = 38;  // or program card
        cmd_resp_config.RespWriteDataKey                            wr_data_key = 39;  // or program card
        cmd_resp_config.RespReadFactorySettings                     rd_factory_settings = 40;
        cmd_resp_config.RespWriteFactorySettings                    wr_factory_settings = 41;
        cmd_resp_config.RespReadUserSettings                        rd_user_settings = 42;
        cmd_resp_config.RespWriteUserSettings                       wr_user_settings = 43;
        cmd_resp_config.RespReadPort1DisableOverrides               rd_port1_disables = 44;
        cmd_resp_config.RespWritePort1DisableOverrides              wr_port1_disables = 45;
        cmd_resp_config.RespWriteAgencyOptions                      wr_agency_options = 46;
        cmd_resp_config.RespRemoteReset                             remote_reset = 47;
        
        // status and statistics responses
        cmd_resp_stats.RespReadPort1Statistics                      rd_port1_stats = 50;
        cmd_resp_stats.RespReadDataKeyStatistics                    rd_data_key_stats = 51; // or program card
        cmd_resp_stats.RespReadMainToIsolatedCommStatistics         rd_main_iso_comms_stats = 52;
        cmd_resp_stats.RespReadMainToCommsCommStatistics            rd_main_comms_stats = 53;
        cmd_resp_stats.RespReadCommsToMainCommStatistics            rd_comms_main_stats = 54;
        cmd_resp_stats.RespReadFlashStatistics                      rd_flash_stats = 55;
        cmd_resp_stats.RespReadWatchdogStatistics                   rd_watchdog_stats = 56;
        cmd_resp_stats.RespGetTimeDatesDst                          rd_date_time_dst = 57;
        cmd_resp_stats.RespClearStatistics                          clear_stats = 58;
        cmd_resp_stats.RespSetTimeDatesDst                          wr_date_time_dst = 59;
        cmd_resp_stats.RespRemoteDisplayButtonEvent                 remote_display_button_event = 65;

        // realtime data responses
        cmd_resp_realtime.RespStartRealtimeData                     start_realtime = 60;
        cmd_resp_realtime.RespStopRealtimeData                      stop_realtime = 61;
        // Note that 65 is used for remote_display_button_event

        // DFU responses
        cmd_resp_dfu.RespManifestVersions                           manifest_versions = 70;
        cmd_resp_dfu.RespRebootCommsMcu                             reboot_comms_mcu = 71;
        cmd_resp_dfu.RespInitiateFirmwareUpdate                     initiate_dfu = 72;
        cmd_resp_dfu.RespFirmwareUpdateManifest                     send_fw_manifest = 73;
        cmd_resp_dfu.RespBeginFirmwareDownload                      begin_firmware_download = 74;
        cmd_resp_dfu.RespFirmwareDownloadChunk                      download_firmware_chunk = 75;        

        // realtime data messages -----------------------------------------------------------------
        cmd_resp_realtime.RealtimeData1                             realtime_data = 80;
        cmd_resp_realtime.RealtimeDisplay1                          realtime_display = 81;

        // test commands
        cmd_resp_comms.RespChunkTest                                test_chunk = 90;        

    }

    reserved 10;           // deprecated field values - DO NOT USE

} // WrapperResponse


/*  ENUM EResponseCodes provide error conditions . */
enum EResponseCodes {

    RESP_UNSPECIFIED = 0;
    RESP_SUCCESS = 1;           // command succeeded
    RESP_PROHIBITED = 2;        // Authentication permissions do not allow this command
    RESP_FAILED = 3;            // command execution failed (eg, getting data from the Main MCU)
    RESP_CMD_UNKNOWN = 4;       // command message type is unknown (or version is 0)
    RESP_BAD_VALUE = 5;         // a command parameter value is missing or out of range
    RESP_BAD_CHANNEL = 6;       // a command is requesting a non-existant channel
    RESP_CMD_ERROR = 7;         // an error occurred while processing the command
    RESP_OPTION_MISMATCH = 8;   // PCB Option jumpers or other options do not match
    RESP_UNAVAILABLE = 9;       // The resource or destination is unavailable (i.e. Attempt to write data key, but no data key)
    RESP_BUSY = 10;             // The resource is busy and unabled to handle the command (but may be available later)
    RESP_TIMEOUT = 11;          // A timeout has occurred during or before command execution
    RESP_SEQUENCE_ERR = 12;     // Command data was received out of sequence
    RESP_NOT_WRITABLE = 13;     // The device does not permit writing the command data
    RESP_DEVICE_NACK = 14;      // A connecteddevice NACKed the command (bad checksum, internal read/write failed, etc)
    RESP_FILE_SYSTEM_ERROR = 15; // An internal file system error occurred (eg, file i/o failed)

} // enum EResponseCodes
