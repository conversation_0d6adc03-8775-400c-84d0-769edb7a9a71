 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   CMD_RESP_LOGS
#   nanopb options file for cmd_resp_logs.proto

# CmdRequestLogCounts & RespRequestLogCounts
#   Max repeated field counts 
cmd_resp_logs.CmdRequestLogCounts.log           max_count:16
cmd_resp_logs.RespRequestLogCounts.log          max_count:16

# CmdRequestLogClear & RespRequestLogClear
#   Max repeated field counts 
cmd_resp_logs.CmdRequestLogClear.log            max_count:16
cmd_resp_logs.RespRequestLogClear.log           max_count:16

# CmdRequestAuditLogCounts & RespRequestAuditLogCounts
#   Max repeated field counts 
cmd_resp_logs.CmdRequestAuditLogCounts.log      max_count:16
cmd_resp_logs.RespRequestAuditLogCounts.log     max_count:16

# CmdRequestAuditLogClear & RespRequestAuditLogClear
#   Max repeated field counts 
cmd_resp_logs.CmdRequestAuditLogClear.log       max_count:16
cmd_resp_logs.RespRequestAuditLogClear.log      max_count:16


