 #********************************************************************************************************
 #* © Copyright 2024- Synapse ITS
 #********************************************************************************************************

#   nanopb C library options, used to constrain the maximum memory footprint for a message.


#   REALTIME
#   nanopb options file for realtime.proto


# ChannelStatusData
#   Max repeated field counts 
realtime.ChannelStatusData.channel_indicators_v_i_t     max_count:32

# LcdDisplayLine
#   Max string lengths, plus null char
realtime.LcdDisplayLine.line_text               max_size:33
realtime.LcdDisplayLine.line_colors             max_size:33

# MonitorDisplayData
#   Max repeated line entries 
realtime.MonitorDisplayData.display_lines      max_count:32
