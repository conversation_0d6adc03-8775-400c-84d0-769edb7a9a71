/**
 ********************************************************************************************************
 * © Copyright 2024- Synapse ITS
 ********************************************************************************************************

---------------------------------------------------------------------------------------------------------
>>>>>>>>>                                                                                     <<<<<<<<<<<
>>>>>>>>>    Before editing, please see the README.md section "Maintaining .proto Files"      <<<<<<<<<<<
>>>>>>>>>                                                                                     <<<<<<<<<<<
---------------------------------------------------------------------------------------------------------
 */

/*  DFU
    Messages and Enums for firmware update commands and responses.
*/
syntax = "proto3";
package dfu;

//***************************************************************************************** IMPORTS
import "basic.proto";

//***************************************************************************************** OPTIONS

//**************************************************************************************** MESSAGES


/*  DfuManifestEntry - One entry in the Firmware Update Manifest message, describing one image file. */
message DfuManifestEntry  {

    // Image Type, target, and version
    FirmwareImageVersion image_version = 1;

    // Name should contain the image type and be unique for each version
    string filename = 2;        // Max string length set in dfu.options

    // SHA256 hash of the file
    bytes hash = 4;             // Max length set in cmd_resp_dfu.options

    // Size of the file in bytes
    uint32 size_bytes = 5;

    // Indicates the update status of this entry.
    EManifestEntryStatus entry_status = 6;

} // DfuManifestEntry

/*  DfuManifestStatusEntry - One entry as reported in the RespManifestVersions message, describing one image's
 *  version information and its update status. 
 *  This is a shortened variation of DfuManifestEntry, suitable for reading back image statuses. */
message DfuManifestStatusEntry  {

    // Image Type, target, and version
    FirmwareImageVersion image_version = 1;

    // Indicates the update status of this entry.
    EManifestEntryStatus entry_status = 2;

} // DfuManifestStatusEntry



/*  FirmwareImageVersion - A firmware image type, target MCU, and version. */
message FirmwareImageVersion  {

    // MCU for which this file is intended
    EProcessorType target_mcu = 1;

    // Type of image being updated
    EImageType image_type = 2;

    // Version in the format <major>.<minor>.<revision> with ASCII numerals for the values, up to 3 digits each.
    // Examples:  "1.0.3"  "1.5.12"  "10.20.1" "1.31.186"
    // Incorrect: "1.8", "4.8.67.24"-(must be 3 values), "1.3.7c"-(values must be numerals),
    //            "2.4567.78"-(more than 3 digit values), "1-3-89"-(separaters must be periods)
    string version = 3;

    // version build, for the format <major>.<minor>.<revision>+<Build>
    // ASCII numerals only representing a 32-bit value (up to 10 digits)
    string version_build = 4;

    // Date of the build, YYYY/MM/DD
    // Month (MM) and Day (DD) are always 2 digits, year is always 4 digits.
    // Separator is required and must be slash '/'
    // Examples:  "2024/07/25" "2024/12/03"
    // Incorrect: "24/07/25" "2024/7/25" "2024-07-25" "07/24/2024"
    string build_date = 5;    

} // FirmwareImageVersion


/*  FirmwareVersionSimple - A firmware target MCU, version, and date. 
    NOTE: This is used in cmd_resp_config.RespReadMonitorData for the versions
          returned.*/
message FirmwareVersionSimple  {

    // MCU for which this file is intended
    EProcessorType target_mcu = 1;

    // version Major.Minor.Revision
    basic.VersionStrThree version = 3;

    // Date of the build, Month/Day/Year
    basic.DateStr build_date = 5;    

} // FirmwareVersionSimple


// ENUM EProcessorType - Possible processor types
enum EProcessorType {

    PROCESSOR_UNSPECIFIED = 0;
    PROCESSOR_MAIN = 1;
    PROCESSOR_ISOLATED = 2;
    PROCESSOR_DISPLAY = 3;
    PROCESSOR_COMMS = 4;
    PROCESSOR_BLE = 5;
    PROCESSOR_PACKAGE = 6;  // For overall update package version
    // ... other processors

} // enum EProcessorType

// ENUM EImageType - A designator for the type of image being updated or reported
enum EImageType {

    IMAGE_UNSPECIFIED = 0;
    IMAGE_MCUBOOT = 1;
    IMAGE_APPLICATION = 2;        // Including Non-Secure portion of Zephyr Application code
    IMAGE_SECURE_CODE = 3;        // Secure Application code, if separate from Application code
    IMAGE_PROTECTED_STORAGE = 4;  // The updateable Protected Storage area
    IMAGE_BOOT_SECURE_DATA = 5;   // The Secure MCUboot data (eg, keys, certs)
    // ... other image types

} // enum EImageType


/*  ENUM EFirmwareUpdateStatus values give overall status on a firmware update package's progress . */
enum EFirmwareUpdateStatus {

    FW_UPDATE_STATUS_UNSPECIFIED = 0;
    /* The process is done and the update has succeeded for all images. */
    FW_UPDATE_STATUS_SUCCESS = 1;
    /* An update is being prepared by downloading an update package. 
     * Initiated by CmdFirmwareUpdateManifest. */
    FW_UPDATE_STATUS_DOWNLOADING = 2;
    /* An update is in progress, with the images being copied to the secondary image 
     * locations on the target MCUs. 
     * Set after the last image in the manifest has been downloaded; 
     * will advance to PENDING when done. */
    FW_UPDATE_STATUS_COPYING = 3;
    /* An update is pending, waiting for a reboot or CmdRebootCommsMcu to start the update process 
     * that will be managed by MCUboot.
     * Set after the last image in the manifest has been copied. */
    FW_UPDATE_STATUS_PENDING = 4;
    /* An update failed for at least one of the images; see the images' EManifestEntryStatus for the reason. */
    FW_UPDATE_STATUS_FAILED = 5;
    /* An update could not be performed at this time, eg, due to power failure. */
    FW_UPDATE_STATUS_NOT_POSSIBLE_NOW = 6;
    /* Update failed before it was started; eg, invalid CPU number, bad version string. */
    FW_UPDATE_STATUS_FAILED_INVALID_PARAM = 7;
    /* Update rejected for this product; not one of the supported models. */
    FW_UPDATE_STATUS_FAILED_NO_SUPPORTED_MODEL = 8;
    /* Update timed out before it was completed. */
    FW_UPDATE_STATUS_FAILED_TIMEOUT = 9;

} // enum EFirmwareUpdateStatus


/*  ENUM EFirmwareVersionsManifest values select a manifest for returning version info. 
    See cmd_resp_dfu.CmdManifestVersions for usage info. */
enum EFirmwareVersionsManifest {

    FW_MANIFEST_UNSPECIFIED = 0;
    FW_MANIFEST_PRESENT = 1;
    FW_MANIFEST_UPDATE = 2;
    FW_MANIFEST_NONE = 3;

} // enum EFirmwareVersionsManifest


/*  ENUM EManifestEntryStatus values give the expected or actual status
    for one image entry, depending upon whether the App or Device is
    providing the Status value.
    The Happy Path flow for an update is 
    NEEDS_UPDATE -> DOWNLOADED -> PENDING_RESTART -> SUCCESS, 
    but that flow could terminate in any of the FAILED statuses.
 */
enum EManifestEntryStatus {

    FW_IMAGE_STATUS_UNSPECIFIED = 0;

    /* Set by the App to indicate that this image entry needs to be updated,
     * and consequently its image should be downloaded next. 
     * The Device will report this status until it can report FW_IMAGE_STATUS_DOWNLOADED. */
    FW_IMAGE_STATUS_NEEDS_UPDATE = 1;

    /* Set by the App to indicate that this image entry does not need to be updated,
     * and consequently there will be no image downloaded for it.
     * Its entry is provided for informational purposes, and for completeness. */
    FW_IMAGE_STATUS_NO_UPDATE = 2;

    /* Set by the Device once the update image has been received successfully, but
     * before it has been installed; this should be followed by the PENDING_RESTART
     * status after copying it to its secondary image location. */
    FW_IMAGE_STATUS_DOWNLOADED = 3;

    /* Set by the Device once the update image has been installed successfully.
     * This is also the final state after a successful update. */
    FW_IMAGE_STATUS_SUCCESS = 4;

    /* Set by the Device if the update image failed to be installed. */ 
    FW_IMAGE_STATUS_FAILED = 5;

    /* Update failed before it was started; eg, incorrect Model number, bad version string. */
    FW_IMAGE_STATUS_FAILED_INVALID_PARAM = 6;

    /* Update failed because an image was not valid; eg, bad hash, bad size. */
    FW_IMAGE_STATUS_FAILED_IMAGE_INVALID = 7;

    /* Update failed because an image version was not acceptable for upgrading. */
    FW_IMAGE_STATUS_FAILED_IMAGE_VERSION_INCOMPATIBLE = 8;

    /* Set by the Device after the update image has been installed (copied into its secondary 
     * image location) and is waiting for the restart that will kick off updating of this image
     * to the primary location and executing it from there; 
     * thus this is an "update pending" status. */
    FW_IMAGE_STATUS_PENDING_RESTART = 9;
 
} // enum EManifestEntryStatus

/*  ENUM EDfuResultCode values are the extra DFU-specific result_code values returned
 *  in the responses for some of the DFU commands. */
enum EDfuResultCode {

    RESULT_UNSPECIFIED = 0;
    // The normal result code for a successful operation
    RESULT_SUCCESS = 1;
    // The operation failed due to an invalid parameter in the command
    RESULT_INVALID_PARAM = 2;
    // The operation could not be executed at this time, eg, due to power failure
    RESULT_NOT_POSSIBLE_NOW = 3;
    // The command was not accepted by a device (check Update Manifest Status for more information)
    RESULT_COMMAND_NOT_ACCEPTED = 4;
    // The command failed due to some device-side issue (eg, file system failure)
    RESULT_FAILED_AT_DEVICE = 5;
    // Command could not be executed because a resource was not available or out of memory
    RESULT_FAILED_UNAVAILABLE = 6;
    // Command could not be executed because a parameter was missing or empty
    RESULT_FAILED_MISSING_PARAM = 7;
    // Command could not be executed because it is out of order - ie, a precursor step was not completed
    RESULT_FAILED_OUT_OF_ORDER = 8;
    // Command failed because of a file I/O error
    RESULT_FAILED_FILE_IO_ERROR = 9;
    // Command failed because of a comm error - eg, sending or receiving from another processor
    RESULT_FAILED_COMM_ERROR = 10;
    // Command could not be executed because no update was in progress
    RESULT_FAILED_NO_UPDATE_IN_PROGRESS = 11;
    // Command failed in one of its processing steps - eg, hash processing
    RESULT_FAILED_PROCESSING_ERROR = 12;

} // enum EDfuResultCode


/*  ENUM EMonitorModelNumber values are for the Model or Part Number of the Monitor
 * Products, such as "MMU2-16LEip-RM-SF". While these are given by a 30-character
 * string in the Serial protocol (field "Model Number" in "Read Factory Settings"),
 * for simplicity, efficiency, and to avoid text matching issues, the Model Numbers
 * will be captured here by a set of enums.
 * As new products are supported, this list of enums will be extended to cover them. */
enum EMonitorModelNumber {

    MODEL_NUMBER_UNSPECIFIED = 0;

    /* MMU2-16LEip-RM-SF - A 16 channel TS2 monitor with FYA support that is powered 
     * by 120VAC with an Ethernet port and LCD displays. Rack mounted. */
    MODEL_NUMBER_MMU2_16LEIP_RM_SF = 1;

    /* DK-Nucleo-MMU - For MMU/CMU development and testing, the "model" built from the
     * H573 DK board and H563 Nucleo board. */
    MODEL_NUMBER_DK_NUCLEO_MMU = 2;

    // ... other Model Number entries

} // enum EMonitorModelNumber 
