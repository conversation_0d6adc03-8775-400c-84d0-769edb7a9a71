{"name": "Protobuf Schemas Development (Compose)", "dockerComposeFile": "docker-compose.yml", "service": "protobuf-dev", "workspaceFolder": "/workspaces/protobuf-schemas", "customizations": {"vscode": {"extensions": ["golang.go", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.powershell", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint", "zxh404.vscode-proto3"], "settings": {"go.toolsManagement.checkForUpdates": "local", "go.useLanguageServer": true, "go.gopath": "/go", "go.goroot": "/usr/local/go", "files.associations": {"*.proto": "proto3"}, "protoc": {"path": "/usr/local/bin/protoc"}}}}, "postCreateCommand": "chmod +x .devcontainer/setup.sh && .devcontainer/setup.sh", "remoteUser": "vscode", "forwardPorts": [], "postStartCommand": "echo '🚀 Dev container ready! Run .devcontainer/setup.sh to verify your environment.'"}