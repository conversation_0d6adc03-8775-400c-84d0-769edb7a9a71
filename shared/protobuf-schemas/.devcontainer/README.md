# Protobuf Schemas Development Container

This development container provides a consistent environment for working with protobuf schemas, including all necessary tools for generating Go and JavaScript code from `.proto` files.

## Features

- **Go 1.24** with protobuf tooling
- **Node.js 20** with JavaScript protobuf plugins
- **Protocol Buffers Compiler (protoc) v30.2**
- **VS Code Extensions** for Go, YAML, JSON, and protobuf development

## Caching

The container uses Docker volumes for efficient caching:

- **Go Module Cache**: `go-cache-protobuf-devcontainer` - Caches downloaded Go modules
- **Go Build Cache**: `go-build-cache-protobuf-devcontainer` - Caches Go build artifacts
- **NPM Cache**: `npm-cache-protobuf-devcontainer` - Caches npm packages
- **VS Code Extensions**: `vscode-extensions` - Caches VS Code extensions

This ensures that subsequent builds and installations are much faster.

## Getting Started

1. Open this repository in VS Code
2. When prompted, click "Reopen in Container"
3. Wait for the container to build (first time will take longer due to downloads)
4. Run `.devcontainer/setup.sh` to verify your environment

## Available Tools

### Protocol Buffers
- `protoc` - Protocol Buffers compiler
- `protoc-gen-go` - Go code generator
- `protoc-gen-go-grpc` - Go gRPC code generator
- `protoc-gen-js` - JavaScript code generator
- `protoc-gen-grpc-web` - gRPC-Web code generator

### Go Tools
- `go` - Go compiler and toolchain
- `gopls` - Go language server
- `golangci-lint` - Go linter

### Node.js Tools
- `node` - Node.js runtime
- `npm` - Node.js package manager

## Usage

### Generating Go Code
```bash
protoc --go_out=. --go-grpc_out=. your_file.proto
```

### Generating JavaScript Code
```bash
protoc --js_out=. --grpc-web_out=. your_file.proto
```

### Running the Generation Script
```bash
# Generate all protobuf files
./scripts/gen-proto.ps1
```

## Troubleshooting

### Build Issues
If you encounter build issues:

1. **Clear Docker cache**: `docker system prune -a`
2. **Rebuild container**: In VS Code, run "Dev Containers: Rebuild Container"
3. **Check Docker logs**: `docker logs <container-name>`

### Permission Issues
The container runs as the `vscode` user. If you encounter permission issues, you can use `sudo` for administrative tasks.

### Network Issues
If you're behind a corporate firewall, you may need to configure Docker to use your proxy settings.

## Environment Variables

The container uses the following environment variables:
- `GOPATH=/go` - Go workspace
- `PATH` - Includes protoc and Go tools
- `GOROOT=/usr/local/go` - Go installation directory 