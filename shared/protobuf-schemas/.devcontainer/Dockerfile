FROM mcr.microsoft.com/devcontainers/go:1.24

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        wget \
        unzip \
        curl \
        ca-certificates \
        gnupg \
        lsb-release \
        build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get update && \
    apt-get install -y nodejs && \
    npm install -g npm@latest && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install protoc
RUN wget -O /tmp/protoc.zip https://github.com/protocolbuffers/protobuf/releases/download/v30.2/protoc-30.2-linux-x86_64.zip && \
    unzip /tmp/protoc.zip -d /usr/local && \
    rm /tmp/protoc.zip && \
    chmod +x /usr/local/bin/protoc

# Install Go protobuf plugins
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.36.6 && \
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.5.1

# Install JavaScript protobuf plugins
RUN npm install -g protoc-gen-js && \
    npm install -g protoc-gen-grpc-web

# Ensure protoc and plugins are on PATH
ENV PATH="/usr/local/bin:${GOPATH}/bin:${PATH}"

WORKDIR /workspaces/protobuf-schemas

# Keep container alive for VS Code client to attach
CMD ["sleep", "infinity"] 