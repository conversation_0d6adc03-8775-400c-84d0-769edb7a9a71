#!/bin/bash

# Development Container Setup Script
# This script helps verify that the dev container is properly configured

set -e

echo "Protobuf Schemas Development Container Setup"
echo "============================================"

# Check if we're in the dev container
if [ -z "$VSCODE_EXTENSION_DEVELOPMENT" ] && [ -z "$REMOTE_CONTAINERS" ]; then
    echo "   Warning: This script is designed to run inside a VS Code dev container"
    echo "   Please open this repository in VS Code and use 'Reopen in Container'"
    exit 1
fi

echo "   Running in development container"

# Check required tools
echo ""
echo "   Checking required tools..."

# Check protoc
if command -v protoc >/dev/null 2>&1; then
    echo "   protoc: $(protoc --version)"
else
    echo "   protoc: Not found"
    exit 1
fi

# Check Go plugins
if command -v protoc-gen-go >/dev/null 2>&1; then
    echo "   protoc-gen-go: Available"
else
    echo "   protoc-gen-go: Not found"
    exit 1
fi

if command -v protoc-gen-go-grpc >/dev/null 2>&1; then
    echo "   protoc-gen-go-grpc: Available"
else
    echo "   protoc-gen-go-grpc: Not found"
    exit 1
fi

# Check JavaScript plugins
if command -v protoc-gen-js >/dev/null 2>&1; then
    echo "   protoc-gen-js: Available"
else
    echo "   protoc-gen-js: Not found"
    exit 1
fi

if command -v protoc-gen-grpc-web >/dev/null 2>&1; then
    echo "   protoc-gen-grpc-web: Available"
else
    echo "   protoc-gen-grpc-web: Not found"
    exit 1
fi

# Check Go
if command -v go >/dev/null 2>&1; then
    echo "   Go: $(go version)"
else
    echo "   Go: Not found"
    exit 1
fi

# Check Node.js
if command -v node >/dev/null 2>&1; then
    echo "   Node.js: $(node --version)"
else
    echo "   Node.js: Not found"
    exit 1
fi

# Check Docker
if command -v docker >/dev/null 2>&1; then
    echo "   Docker: $(docker --version)"
else
    echo "   Docker: Not found"
    exit 1
fi

# Test protobuf generation
echo ""
echo "   Testing protobuf generation..."

# Create a test proto file
mkdir -p /tmp/test-proto
cat > /tmp/test-proto/test.proto << 'EOF'
syntax = "proto3";

package test;

option go_package = "test";

message TestMessage {
  string name = 1;
  int32 value = 2;
}
EOF

# Test Go generation
if protoc --proto_path=/tmp/test-proto --go_out=/tmp/test-proto --go_opt=paths=source_relative /tmp/test-proto/test.proto; then
    echo "   Go protobuf generation: Working"
else
    echo "   Go protobuf generation: Failed"
    exit 1
fi

# Test JavaScript generation
if protoc --proto_path=/tmp/test-proto --js_out=/tmp/test-proto --grpc-web_out=/tmp/test-proto --grpc-web_opt=import_style=commonjs,mode=grpcwebtext /tmp/test-proto/test.proto; then
    echo "   JavaScript protobuf generation: Working"
else
    echo "   JavaScript protobuf generation: Failed"
    exit 1
fi

# Clean up test files
rm -rf /tmp/test-proto

# Check project structure
echo ""
echo "   Checking project structure..."

if [ -d "models" ]; then
    echo "   models/ directory: Found"
else
    echo "   models/ directory: Not found"
fi

if [ -d "monf-protobufs-messages" ]; then
    echo "   monf-protobufs-messages/ directory: Found"
else
    echo "   monf-protobufs-messages/ directory: Not found"
fi

if [ -d "scripts" ]; then
    echo "   scripts/ directory: Found"
else
    echo "   scripts/ directory: Not found"
fi

# Test the generation script
echo ""
echo "   Testing generation script..."

if [ -f "scripts/gen-proto.ps1" ]; then
    echo "   PowerShell generation script: Found"
    echo "   You can run: ./scripts/gen-proto.ps1"
else
    echo "   PowerShell generation script: Not found"
fi

if [ -f "scripts/generate.sh" ]; then
    echo "   Bash generation script: Found"
    echo "   You can run: ./scripts/generate.sh"
else
    echo "   Bash generation script: Not found"
fi

echo ""
echo "   Setup complete!"
echo ""
echo "Next steps:"
echo "1. Run './scripts/gen-proto.ps1' to generate all protobuf files"
echo "2. Edit .proto files in the models/ directory"
echo "3. Use the generated Go and JavaScript files in protomessages/"
echo ""
echo "For more information, see:"
echo "- scripts/README.md - Detailed usage instructions"
echo "- .devcontainer/README.md - Dev container documentation" 