{"name": "Protobuf Sc<PERSON>as Development", "build": {"dockerfile": "Dockerfile"}, "workspaceFolder": "/workspaces/protobuf-schemas", "workspaceMount": "source=${localWorkspaceFolder},target=/workspaces/protobuf-schemas,type=bind,consistency=cached", "mounts": ["source=go-cache-protobuf-devcontainer,target=/go/pkg/mod,type=volume,consistency=cached", "source=go-build-cache-protobuf-devcontainer,target=/root/.cache/go-build,type=volume,consistency=cached", "source=npm-cache-protobuf-devcontainer,target=/home/<USER>/.npm,type=volume,consistency=cached", "source=vscode-extensions,target=/home/<USER>/.vscode-server/extensions,type=volume,consistency=cached"], "remoteUser": "vscode", "customizations": {"vscode": {"extensions": ["golang.go", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.powershell", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-eslint", "zxh404.vscode-proto3"], "settings": {"go.toolsManagement.checkForUpdates": "local", "go.useLanguageServer": true, "go.gopath": "/go", "go.goroot": "/usr/local/go", "files.associations": {"*.proto": "proto3"}, "protoc": {"path": "/usr/local/bin/protoc"}}}}, "postCreateCommand": "chmod +x .devcontainer/setup.sh && .devcontainer/setup.sh", "forwardPorts": [], "postStartCommand": "echo '🚀 Dev container ready! Run .devcontainer/setup.sh to verify your environment.'"}