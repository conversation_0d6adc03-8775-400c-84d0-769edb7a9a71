#!/usr/bin/env bash
set -euo pipefail

# where .proto files live
PROTO_DIR=./models
# where .js files land
JS_OUT_DIR=./protomessages/js

echo "Generating JavaScript files from custom models..."

# find and compile every .proto under $PROTO_DIR
success_count=0
error_count=0
total_files=0

while IFS= read -r proto; do
    total_files=$((total_files + 1))
    
    # Get the relative path from models directory
    rel_path="${proto#$PROTO_DIR/}"
    # Get the directory path (remove filename)
    dir_path="$JS_OUT_DIR/${rel_path%/*}"
    
    # Create the output directory
    mkdir -p "$dir_path"
    
    # Run protoc command
    if protoc \
        --proto_path="$PROTO_DIR" \
        --js_out="$dir_path" --js_opt=import_style=commonjs \
        --grpc-web_out="$dir_path" --grpc-web_opt=import_style=commonjs,mode=grpcwebtext \
        "$proto" > /dev/null 2>&1; then
        success_count=$((success_count + 1))
    else
        echo "Error generating JavaScript files for $(basename "$proto")" >&2
        error_count=$((error_count + 1))
    fi
done < <(find "$PROTO_DIR" -name '*.proto')

echo "Custom models JavaScript generation: $success_count successful, $error_count errors" 