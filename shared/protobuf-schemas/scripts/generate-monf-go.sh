#!/usr/bin/env bash
set -euo pipefail

# where .proto files live
PROTO_DIR=./monf-protobufs-messages
# where .go files land
GO_OUT_DIR=./protomessages/go

echo "Generating Go files from monf-protobufs-messages..."

# First, collect all package names and create a mapping for all proto files
declare -A package_mapping
while IFS= read -r proto; do
    package_name=$(grep "^package " "$proto" | head -1 | sed 's/package //' | sed 's/;//')
    if [ -n "$package_name" ]; then
        proto_basename=$(basename "$proto")
        package_mapping["$proto_basename"]="$package_name"
    fi
done < <(find "$PROTO_DIR" -name '*.proto')

# Build the M arguments for all proto files
GO_M_ARGS=""
for proto_file in "${!package_mapping[@]}"; do
    package_name="${package_mapping[$proto_file]}"
    # Create proper Go import path
    go_import_path="bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/monf/$package_name"
    GO_M_ARGS="$GO_M_ARGS --go_opt=M$proto_file=$go_import_path --go-grpc_opt=M$proto_file=$go_import_path"
done

# find and compile every .proto under $PROTO_DIR
success_count=0
error_count=0
total_files=0

while IFS= read -r proto; do
    total_files=$((total_files + 1))
    
    # Get the package name from the proto file
    package_name=$(grep "^package " "$proto" | head -1 | sed 's/package //' | sed 's/;//')
    
    if [ -z "$package_name" ]; then
        echo "Warning: No package declaration found in $(basename "$proto")" >&2
        continue
    fi
    
    # Create the output directory structure based on package name within monf
    go_package_dir="$GO_OUT_DIR/monf/$package_name"
    mkdir -p "$go_package_dir"
    
    # Run protoc command for Go - output to package-specific directory within monf
    if protoc \
        --proto_path="$PROTO_DIR" \
        --go_out="$go_package_dir" --go_opt=paths=source_relative \
        --go-grpc_out="$go_package_dir" --go-grpc_opt=paths=source_relative \
        $GO_M_ARGS \
        "$proto" > /dev/null 2>&1; then
        success_count=$((success_count + 1))
    else
        echo "Error generating Go files for $(basename "$proto")" >&2
        error_count=$((error_count + 1))
    fi
done < <(find "$PROTO_DIR" -name '*.proto')

echo "Monf Go generation: $success_count successful, $error_count errors" 