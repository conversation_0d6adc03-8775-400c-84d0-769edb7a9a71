#!/usr/bin/env bash
set -euo pipefail

# where .proto files live
PROTO_DIR=./models
# where .go files land
OUT_DIR=./protomessages/go

# Create output directory if it doesn't exist
mkdir -p "$OUT_DIR"

echo "Generating Go files from custom models..."

# find and compile every .proto under $PROTO_DIR
success_count=0
error_count=0
total_files=0

while IFS= read -r proto; do
    total_files=$((total_files + 1))
    
    # Run protoc command - output to base directory, let protoc handle structure
    if protoc \
        --proto_path="$PROTO_DIR" \
        --go_out="$OUT_DIR" --go_opt=paths=source_relative \
        --go-grpc_out="$OUT_DIR" --go-grpc_opt=paths=source_relative \
        "$proto" > /dev/null 2>&1; then
        success_count=$((success_count + 1))
    else
        echo "Error generating Go files for $(basename "$proto")" >&2
        error_count=$((error_count + 1))
    fi
done < <(find "$PROTO_DIR" -name '*.proto')

echo "Custom models Go generation: $success_count successful, $error_count errors" 