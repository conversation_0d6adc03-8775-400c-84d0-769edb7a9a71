<#
.SYNOPSIS
Optionally builds the proto-builder Docker image and generates protobuf code.
.DESCRIPTION
This script optionally rebuilds the Docker image tagged 'proto-builder' when invoked with
-BuildImage, then runs the container to execute the complete protobuf generation.
.PARAMETER BuildImage
If specified, rebuilds the Docker image before regenerating code. If omitted, skips the build.
.EXAMPLE
# Generate all protobuf files (custom models + monf-protobufs-messages)
PS> .\scripts\gen-proto.ps1

# Rebuild image then generate all protobuf files
PS> .\scripts\gen-proto.ps1 -BuildImage
#>

Param (
  [switch]$BuildImage
)

# Fail fast on errors
Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'

# Get the project root directory (where the Dockerfile is located)
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectRoot = Split-Path -Parent $ScriptDir

# Rebuild the Docker image if requested
if ($BuildImage) {
  Write-Host "Building proto-builder image..."
  docker build -t proto-builder $ProjectRoot
}
else {
  Write-Host "Skipping Docker image build. Use -BuildImage to force rebuild."
}

Write-Host "Generating protobuf files..."

# Run the Docker container (generation command is handled by Dockerfile ENTRYPOINT)
docker run --rm -v "${ProjectRoot}:/workspace" proto-builder

Write-Host "Protobuf code generation complete."
