#!/usr/bin/env bash
set -euo pipefail

echo "Starting complete protobuf generation..."

# Remove the protomessages directory completely for stateless generation
if [ -d "./protomessages" ]; then
    echo "Removing existing protomessages directory..."
    rm -rf ./protomessages
fi

# Create the proper directory structure
mkdir -p ./protomessages/go
mkdir -p ./protomessages/js

# Generate custom models (Go)
echo "=== Generating Go code from custom models ==="
./scripts/generate-models-go.sh

# Generate custom models (JavaScript)
echo "=== Generating JavaScript code from custom models ==="
./scripts/generate-models-js.sh

# Generate monf-protobufs-messages (Go)
echo "=== Generating Go code from monf-protobufs-messages ==="
./scripts/generate-monf-go.sh

# Generate monf-protobufs-messages (JavaScript)
echo "=== Generating JavaScript code from monf-protobufs-messages ==="
./scripts/generate-monf-js.sh

# Run go mod tidy and download dependencies (once after all Go generation)
if [ -f "go.mod" ]; then
  echo "=== Running go mod tidy and download ==="
  echo "Running go mod tidy..."
  go mod tidy || echo "Warning: go mod tidy failed (this is optional)"
  echo "Running go mod download..."
  go mod download || echo "Warning: go mod download failed (this is optional)"
fi

echo "Complete protobuf generation finished." 