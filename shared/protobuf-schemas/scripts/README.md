# Protobuf Generation Scripts

This directory contains scripts for generating Go and JavaScript code from protobuf definitions.

## Script Overview

### Individual Generation Scripts

- **`generate-models-go.sh`** - Generates Go files from custom models (`./models/`)
- **`generate-models-js.sh`** - Generates JavaScript files from custom models (`./models/`)
- **`generate-monf-go.sh`** - Generates Go files from monf-protobufs-messages (`./monf-protobufs-messages/`)
- **`generate-monf-js.sh`** - Generates JavaScript files from monf-protobufs-messages (`./monf-protobufs-messages/`)

### Master Scripts

- **`generate-all.sh`** - Runs all generation scripts in the correct order
- **`gen-proto.ps1`** - PowerShell script for Docker-based generation (Windows/Linux)

## Usage

### Generate All Code (Recommended)

```bash
# Using the master script directly
./scripts/generate-all.sh

# Or using PowerShell (Windows/Linux with Docker)
./scripts/gen-proto.ps1
```

### Generate Specific Code

```bash
# Generate only Go code from custom models
./scripts/generate-models-go.sh

# Generate only JavaScript code from custom models
./scripts/generate-models-js.sh

# Generate only Go code from monf-protobufs-messages
./scripts/generate-monf-go.sh

# Generate only JavaScript code from monf-protobufs-messages
./scripts/generate-monf-js.sh
```

## Output Structure

Generated files are organized as follows:

```
./protomessages/
├── go/
│   ├── gateway/v1/          # Custom models (Go)
│   ├── rushhour/v1/         # Custom models (Go)
│   └── monf/               # Monf-protobufs-messages (Go)
│       ├── audit_logs/
│       ├── basic/
│       └── ...
└── js/
    ├── gateway/v1/          # Custom models (JavaScript)
    ├── rushhour/v1/         # Custom models (JavaScript)
    └── monf/               # Monf-protobufs-messages (JavaScript)
        ├── audit_logs/
        ├── basic/
        └── ...
```

## Prerequisites

- `protoc` compiler installed
- Go protobuf plugins (`protoc-gen-go`, `protoc-gen-go-grpc`)
- JavaScript protobuf plugins (`protoc-gen-js`, `protoc-gen-grpc-web`)
- For Docker-based generation: Docker with the `proto-builder` image

## Notes

- All scripts use `set -euo pipefail` for strict error handling
- Scripts create necessary directories automatically
- Error counts and success counts are reported for each generation
- The PowerShell script uses Docker to run the `generate-all.sh` script in a consistent environment
- Individual scripts can be run directly for targeted generation
