#!/usr/bin/env bash
set -euo pipefail

# where .proto files live
PROTO_DIR=./monf-protobufs-messages
# where .js files land
JS_OUT_DIR=./protomessages/js

echo "Generating JavaScript files from monf-protobufs-messages..."

# find and compile every .proto under $PROTO_DIR
success_count=0
error_count=0
total_files=0

while IFS= read -r proto; do
    total_files=$((total_files + 1))
    
    # Get the package name from the proto file
    package_name=$(grep "^package " "$proto" | head -1 | sed 's/package //' | sed 's/;//')
    
    if [ -z "$package_name" ]; then
        echo "Warning: No package declaration found in $(basename "$proto")" >&2
        continue
    fi
    
    # Create the output directory structure based on package name within monf
    js_package_dir="$JS_OUT_DIR/monf/$package_name"
    mkdir -p "$js_package_dir"
    
    # Run protoc command for JavaScript
    if protoc \
        --proto_path="$PROTO_DIR" \
        --js_out="$js_package_dir" --js_opt=import_style=commonjs \
        --grpc-web_out="$js_package_dir" --grpc-web_opt=import_style=commonjs,mode=grpcwebtext \
        "$proto" > /dev/null 2>&1; then
        success_count=$((success_count + 1))
    else
        echo "Error generating JavaScript files for $(basename "$proto")" >&2
        error_count=$((error_count + 1))
    fi
done < <(find "$PROTO_DIR" -name '*.proto')

echo "Monf JavaScript generation: $success_count successful, $error_count errors" 