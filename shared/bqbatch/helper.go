package bqbatch

import (
	"bytes"
	"encoding/json"

	"cloud.google.com/go/bigquery"
)

// preSerializeRows serializes each row once and returns PreSerializedRow slices
func preSerializeRows(rows []bigquery.ValueSaver) ([]PreSerializedRow, error) {
	var result []PreSerializedRow
	for _, r := range rows {
		rowMap, _, err := r.Save()
		if err != nil {
			return nil, err
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			return nil, err
		}

		result = append(result, PreSerializedRow{
			JSONBytes: jsonBytes,
			Size:      len(jsonBytes) + 1, // +1 for newline in JSONL
		})
	}
	return result, nil
}

// splitBatch splits a batch of pre-serialized rows into sub-batches that fit within size limits
func splitBatch(rows []PreSerializedRow, maxSize int) [][]PreSerializedRow {
	var batches [][]PreSerializedRow
	var currentBatch []PreSerializedRow
	var currentSize int
	for _, row := range rows {
		if currentSize+row.Size > maxSize && len(currentBatch) > 0 {
			batches = append(batches, currentBatch)
			currentBatch = []PreSerializedRow{row}
			currentSize = row.Size
		} else {
			currentBatch = append(currentBatch, row)
			currentSize += row.Size
		}
	}
	if len(currentBatch) > 0 {
		batches = append(batches, currentBatch)
	}
	return batches
}

// splitRowsBatch splits a batch of ValueSaver rows into sub-batches by row count for streaming inserts
func splitRowsBatch(rows []bigquery.ValueSaver, maxRows int) [][]bigquery.ValueSaver {
	var batches [][]bigquery.ValueSaver
	for i := 0; i < len(rows); i += maxRows {
		end := i + maxRows
		if end > len(rows) {
			end = len(rows)
		}
		batches = append(batches, rows[i:end])
	}
	return batches
}

// estimateRowsSize estimates the total byte size of rows by converting them to JSON
func estimateRowsSize(rows []bigquery.ValueSaver) int {
	totalSize := 0
	for _, row := range rows {
		rowMap, _, err := row.Save()
		if err != nil {
			continue // Skip rows that can't be serialized for size estimation
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			continue
		}
		totalSize += len(jsonBytes) + 1 // +1 for newline
	}
	return totalSize
}

// convertRowsToJSONL converts ValueSaver rows to JSONL format for DLQ compatibility
func convertRowsToJSONL(rows []bigquery.ValueSaver) ([]byte, error) {
	var buf bytes.Buffer
	for _, row := range rows {
		rowMap, _, err := row.Save()
		if err != nil {
			return nil, err
		}
		jsonBytes, err := json.Marshal(rowMap)
		if err != nil {
			return nil, err
		}
		buf.Write(jsonBytes)
		buf.WriteByte('\n')
	}
	return buf.Bytes(), nil
}
