package test

import (
	"encoding"
	"encoding/json"
	"reflect"
	"testing"
)

// TestReporter interface allows for testing the AssertJSONTypeMarshalable function
type TestReporter interface {
	Errorf(format string, args ...interface{})
}

// AssertJSONTypeMarshalable ensures that the given type (struct or pointer to struct)
// and all of its exported‐field subtypes are supported by encoding/json.
func AssertJSONTypeMarshalable(t TestReporter, typ reflect.Type) {
	// unwrap pointers
	for typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	// Pre‐flight: reject kinds JSON never handles
	switch typ.Kind() {
	case reflect.Func, reflect.Chan, reflect.Complex64, reflect.Complex128, reflect.UnsafePointer:
		t.E<PERSON>rf("json: unsupported kind %s", typ.Kind())
		return
	}

	// If it's a struct, dive into its exported fields
	if typ.Kind() == reflect.Struct {
		// If the struct itself implements json.Marshaler, we'll catch errors below
		marshalerType := reflect.TypeOf((*json.Marshaler)(nil)).Elem()
		if !typ.Implements(marshalerType) {
			for i := 0; i < typ.NumField(); i++ {
				field := typ.Field(i)
				// skip unexported
				if field.PkgPath != "" {
					continue
				}
				AssertJSONTypeMarshalable(t, field.Type)
			}
		}
	}

	// Arrays and slices -> element type
	if typ.Kind() == reflect.Array || typ.Kind() == reflect.Slice {
		AssertJSONTypeMarshalable(t, typ.Elem())
	}

	// Maps -> check key type, then value type
	if typ.Kind() == reflect.Map {
		key := typ.Key()
		if key.Kind() != reflect.String {
			tm := reflect.TypeOf((*encoding.TextMarshaler)(nil)).Elem()
			if !(key.Implements(tm) || reflect.PointerTo(key).Implements(tm)) {
				t.Errorf("json: map key type %s is not string or TextMarshaler", key)
			}
		}
		AssertJSONTypeMarshalable(t, typ.Elem())
	}

	// Finally, try marshalling the zero value of this type
	zero := reflect.Zero(typ).Interface()
	if _, err := json.Marshal(zero); err != nil {
		t.Errorf("json.Marshal zero‐value of %s failed: %v", typ, err)
	}
}

// Ensure *testing.T implements TestReporter
var _ TestReporter = (*testing.T)(nil)
