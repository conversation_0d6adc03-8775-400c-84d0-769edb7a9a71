package test

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"
	"unsafe"

	"github.com/stretchr/testify/assert"
)

// Test types for comprehensive coverage

// Basic struct
type SimpleStruct struct {
	Name  string `json:"name"`
	Value int    `json:"value"`
}

// Struct with unexported fields
type StructWithUnexportedFields struct {
	PublicField  string `json:"public"`
	privateField string // unexported, should be skipped
}

// Struct implementing json.Marshaler
type CustomMarshalerStruct struct {
	Data string `json:"data"`
}

func (c CustomMarshalerStruct) MarshalJSON() ([]byte, error) {
	return json.Marshal(map[string]string{"custom": c.Data})
}

// Struct with nested structs
type NestedStruct struct {
	Simple SimpleStruct            `json:"simple"`
	Array  []SimpleStruct          `json:"array"`
	Map    map[string]SimpleStruct `json:"map"`
}

// Type implementing TextMarshaler for map keys
type TextMarshalerKey struct {
	Value string
}

func (t TextMarshalerKey) MarshalText() ([]byte, error) {
	return []byte(t.Value), nil
}

// Struct with TextMarshaler map key
type StructWithTextMarshalerKey struct {
	TextMap map[TextMarshalerKey]string `json:"text_map"`
}

// Pointer receiver TextMarshaler
type PointerTextMarshalerKey struct {
	Value string
}

func (t *PointerTextMarshalerKey) MarshalText() ([]byte, error) {
	return []byte(t.Value), nil
}

type StructWithPointerTextMarshalerKey struct {
	PointerTextMap map[PointerTextMarshalerKey]string `json:"pointer_text_map"`
}

// Problematic struct with invalid map key type
type StructWithInvalidMapKey struct {
	InvalidMap map[int]string `json:"invalid_map"`
}

// Struct with complex invalid map key
type ComplexKey struct {
	Field string
}

type StructWithComplexInvalidMapKey struct {
	ComplexMap map[ComplexKey]string `json:"complex_map"`
}

// Unmarshalable struct (causes json.Marshal to fail)
type UnmarshalableStruct struct {
	Ch chan int // channels can't be marshaled
}

// Mock testing.T for capturing errors
type mockTestingT struct {
	errors []string
}

func (m *mockTestingT) Errorf(format string, args ...interface{}) {
	m.errors = append(m.errors, fmt.Sprintf(format, args...))
}

func TestAssertJSONTypeMarshalable_BasicStruct(t *testing.T) {
	// Test basic struct - should pass
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(SimpleStruct{}))
	}, "Basic struct should be marshalable")
}

func TestAssertJSONTypeMarshalable_PointerToStruct(t *testing.T) {
	// Test pointer to struct - should unwrap and pass
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(&SimpleStruct{}))
	}, "Pointer to struct should be marshalable")
}

func TestAssertJSONTypeMarshalable_DoublePointer(t *testing.T) {
	// Test double pointer - should unwrap both levels
	var doublePtr **SimpleStruct
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(doublePtr))
	}, "Double pointer should be marshalable")
}

func TestAssertJSONTypeMarshalable_StructWithUnexportedFields(t *testing.T) {
	// Test struct with unexported fields - should skip them
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(StructWithUnexportedFields{}))
	}, "Struct with unexported fields should be marshalable")
}

func TestAssertJSONTypeMarshalable_CustomMarshalerStruct(t *testing.T) {
	// Test struct implementing json.Marshaler - should not dive into fields
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(CustomMarshalerStruct{}))
	}, "Custom marshaler struct should be marshalable")
}

func TestAssertJSONTypeMarshalable_NestedStruct(t *testing.T) {
	// Test struct with nested structs, arrays, and maps
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(NestedStruct{}))
	}, "Nested struct should be marshalable")
}

func TestAssertJSONTypeMarshalable_Array(t *testing.T) {
	// Test array type directly
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf([3]int{}))
	}, "Array should be marshalable")
}

func TestAssertJSONTypeMarshalable_Slice(t *testing.T) {
	// Test slice type directly
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf([]string{}))
	}, "Slice should be marshalable")
}

func TestAssertJSONTypeMarshalable_StringMap(t *testing.T) {
	// Test map with string keys
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(map[string]int{}))
	}, "String map should be marshalable")
}

func TestAssertJSONTypeMarshalable_TextMarshalerKey(t *testing.T) {
	// Test map with TextMarshaler key type
	assert.NotPanics(t, func() {
		AssertJSONTypeMarshalable(t, reflect.TypeOf(StructWithTextMarshalerKey{}))
	}, "TextMarshaler key map should be marshalable")
}

func TestAssertJSONTypeMarshalable_PointerTextMarshalerKey(t *testing.T) {
	// Test map with pointer TextMarshaler key type - this should fail because
	// JSON doesn't support pointer receiver TextMarshaler for map keys
	mockT := &mockTestingT{}

	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(StructWithPointerTextMarshalerKey{}))

	assert.NotEmpty(t, mockT.errors, "Expected error for pointer TextMarshaler map key")
	// Should fail at JSON marshaling step since pointer receiver TextMarshaler isn't supported for map keys
	assert.Contains(t, mockT.errors[0], "json: unsupported type",
		"Expected 'json: unsupported type' error")
}

func TestAssertJSONTypeMarshalable_BasicTypes(t *testing.T) {
	// Test basic JSON-compatible types
	types := []interface{}{
		int(0),
		int8(0),
		int16(0),
		int32(0),
		int64(0),
		uint(0),
		uint8(0),
		uint16(0),
		uint32(0),
		uint64(0),
		float32(0),
		float64(0),
		bool(false),
		string(""),
		[]byte{},
	}

	for _, typ := range types {
		assert.NotPanics(t, func() {
			AssertJSONTypeMarshalable(t, reflect.TypeOf(typ))
		}, "Basic type %T should be marshalable", typ)
	}
}

// Error condition tests - these should cause test failures

func TestAssertJSONTypeMarshalable_FunctionType(t *testing.T) {
	// Capture test failures
	mockT := &mockTestingT{}

	// Test function type - should fail
	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(func() {}))

	// Verify error was reported
	assert.NotEmpty(t, mockT.errors, "Expected error for function type")
	assert.Contains(t, mockT.errors[0], "unsupported kind func",
		"Expected 'unsupported kind func' error")
}

func TestAssertJSONTypeMarshalable_ChannelType(t *testing.T) {
	mockT := &mockTestingT{}

	// Test channel type - should fail
	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(make(chan int)))

	assert.NotEmpty(t, mockT.errors, "Expected error for channel type")
	assert.Contains(t, mockT.errors[0], "unsupported kind chan",
		"Expected 'unsupported kind chan' error")
}

func TestAssertJSONTypeMarshalable_Complex64Type(t *testing.T) {
	mockT := &mockTestingT{}

	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(complex64(0)))

	assert.NotEmpty(t, mockT.errors, "Expected error for complex64 type")
	assert.Contains(t, mockT.errors[0], "unsupported kind complex64",
		"Expected 'unsupported kind complex64' error")
}

func TestAssertJSONTypeMarshalable_Complex128Type(t *testing.T) {
	mockT := &mockTestingT{}

	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(complex128(0)))

	assert.NotEmpty(t, mockT.errors, "Expected error for complex128 type")
	assert.Contains(t, mockT.errors[0], "unsupported kind complex128",
		"Expected 'unsupported kind complex128' error")
}

func TestAssertJSONTypeMarshalable_UnsafePointerType(t *testing.T) {
	mockT := &mockTestingT{}

	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(unsafe.Pointer(nil)))

	assert.NotEmpty(t, mockT.errors, "Expected error for unsafe pointer type")
	assert.Contains(t, mockT.errors[0], "unsupported kind unsafe.Pointer",
		"Expected 'unsupported kind unsafe.Pointer' error")
}

func TestAssertJSONTypeMarshalable_InvalidMapKeyType(t *testing.T) {
	mockT := &mockTestingT{}

	// Test map with invalid key type (int, not string or TextMarshaler)
	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(StructWithInvalidMapKey{}))

	assert.NotEmpty(t, mockT.errors, "Expected error for invalid map key type")
	assert.Contains(t, mockT.errors[0], "map key type int is not string or TextMarshaler",
		"Expected 'map key type int is not string or TextMarshaler' error")
}

func TestAssertJSONTypeMarshalable_ComplexInvalidMapKeyType(t *testing.T) {
	mockT := &mockTestingT{}

	// Test map with complex invalid key type
	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(StructWithComplexInvalidMapKey{}))

	assert.NotEmpty(t, mockT.errors, "Expected error for complex invalid map key type")
	assert.Contains(t, mockT.errors[0], "is not string or TextMarshaler",
		"Expected TextMarshaler error")
}

func TestAssertJSONTypeMarshalable_UnmarshalableStruct(t *testing.T) {
	mockT := &mockTestingT{}

	// Test struct that can't be marshaled to JSON
	AssertJSONTypeMarshalable(mockT, reflect.TypeOf(UnmarshalableStruct{}))

	assert.NotEmpty(t, mockT.errors, "Expected error for unmarshalable struct")
	assert.Contains(t, mockT.errors[0], "unsupported kind chan",
		"Expected 'unsupported kind chan' error")
}
