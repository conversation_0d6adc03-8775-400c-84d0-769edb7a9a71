package connect

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"os"
	"sync"
	"time"

	"cloud.google.com/go/pubsub"
	"google.golang.org/api/option"
	"google.golang.org/api/option/internaloption"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"synapse-its.com/shared/logger"
)

var (
	pubsubNewClient = pubsub.NewClient
	timeSleepPubSub = time.Sleep
)

// TopicCache manages a cache of validated topics to reduce RPC calls
type TopicCache struct {
	cache map[string]PsTopic
	mutex sync.RWMutex
}

// NewTopicCache creates a new topic cache instance
func NewTopicCache() *TopicCache {
	return &TopicCache{
		cache: make(map[string]PsTopic),
	}
}

// Get retrieves a topic from the cache if it exists
func (tc *TopicCache) Get(topicName string) (PsTopic, bool) {
	tc.mutex.RLock()
	defer tc.mutex.RUnlock()
	topic, exists := tc.cache[topicName]
	return topic, exists
}

// Set adds a topic to the cache
func (tc *TopicCache) Set(topicName string, topic PsTopic) {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	tc.cache[topicName] = topic
}

// Remove removes a topic from the cache
func (tc *TopicCache) Remove(topicName string) {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	delete(tc.cache, topicName)
}

// Clear removes all topics from the cache
func (tc *TopicCache) Clear() {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	tc.cache = make(map[string]PsTopic)
}

// Size returns the number of cached topics
func (tc *TopicCache) Size() int {
	tc.mutex.RLock()
	defer tc.mutex.RUnlock()
	return len(tc.cache)
}

// Global topic cache instance
var globalTopicCache = NewTopicCache()

// GetTopicCache returns the global topic cache instance
func GetTopicCache() *TopicCache {
	return globalTopicCache
}

// RetryConfig holds configuration for retry operations
type RetryConfig struct {
	MaxRetries int
	BaseDelay  time.Duration
	MaxDelay   time.Duration
	Multiplier float64
	Jitter     bool
}

// DefaultRetryConfig returns sensible defaults for Pub/Sub operations
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxRetries: 3,
		BaseDelay:  100 * time.Millisecond,
		MaxDelay:   5 * time.Second,
		Multiplier: 2.0,
		Jitter:     true,
	}
}

// RetryWithBackoff executes a function with exponential backoff retry logic
func RetryWithBackoff(ctx context.Context, config RetryConfig, operation func() error) error {
	var lastErr error

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		// Execute the operation
		err := operation()
		if err == nil {
			return nil
		}

		lastErr = err

		// Don't retry on the last attempt
		if attempt == config.MaxRetries {
			break
		}

		// Calculate delay with exponential backoff
		delay := time.Duration(float64(config.BaseDelay) * math.Pow(config.Multiplier, float64(attempt)))
		if delay > config.MaxDelay {
			delay = config.MaxDelay
		}

		// Add jitter to prevent thundering herd
		if config.Jitter && delay > 0 {
			jitter := time.Duration(rand.Float64() * float64(delay) * 0.1) // 10% jitter
			delay += jitter
		}

		logger.Debugf("Retry attempt %d/%d failed, retrying in %s: %v", attempt+1, config.MaxRetries, delay, err)

		// Wait before retrying, respecting context cancellation
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// Continue to next attempt
		}
	}

	return fmt.Errorf("operation failed after %d attempts: %w", config.MaxRetries+1, lastErr)
}

var PubSub = func(ctx context.Context) (PsClient, error) {
	projectID := os.Getenv("GCP_PROJECT_ID")
	if projectID == "" {
		projectID = "test-project"
	}
	opts := []option.ClientOption{}
	endpoint := os.Getenv("PUBSUB_EMULATOR_HOST")
	if endpoint != "" {
		// https://github.com/googleapis/google-cloud-go/blob/bfb6c05baed4e9a22a84d600700e5954cc5fc55a/pubsub/pubsub.go#L155
		opts = []option.ClientOption{
			option.WithEndpoint(endpoint),
			option.WithGRPCDialOption(grpc.WithTransportCredentials(insecure.NewCredentials())),
			option.WithoutAuthentication(),
			option.WithTelemetryDisabled(),
			internaloption.SkipDialSettingsValidation(),
		}
	}

	var client *pubsub.Client
	var err error
	backoff := time.Second
	maxRetries := 5

	for i := range maxRetries {
		client, err = pubsubNewClient(ctx, projectID, opts...)
		if err == nil {
			break
		}
		logger.Infof("Attempt %d/%d: Failed to create PubSub client, retrying in %s: %v", i+1, maxRetries, backoff, err)
		timeSleepPubSub(backoff)
		backoff *= 2
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create PubSub client after %d attempts: %v", maxRetries, err)
	}
	return WrapPubsubClient(client), nil
}

// ——— Interfaces ——————————————————————————————————————

// PsClient abstracts *pubsub.Client
type PsClient interface {
	Topic(name string) PsTopic
	Subscription(name string) PsSubscription
	CreateTopic(ctx context.Context, name string) (PsTopic, error)
	CreateSubscription(ctx context.Context, name string, cfg SubscriptionConfig) (PsSubscription, error)
	Close() error
}

// PsTopic abstracts *pubsub.Topic
type PsTopic interface {
	// Publish returns a result you can call Get() on
	Publish(ctx context.Context, msg *pubsub.Message) PsPublishResult
	// Exists lets you check for existence
	Exists(ctx context.Context) (bool, error)
}

// PsPublishResult abstracts *pubsub.PublishResult
type PsPublishResult interface {
	Get(ctx context.Context) (string, error)
}

// PsSubscription abstracts *pubsub.Subscription
type PsSubscription interface {
	Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error
	Exists(ctx context.Context) (bool, error)
	SetReceiveSettings(settings *pubsub.ReceiveSettings)
	ID() string
	Close() error
}

// ——— Pubsub implementations ————————————————————————————

type PubsubClient struct{ c *pubsub.Client }

func WrapPubsubClient(c *pubsub.Client) PsClient { return &PubsubClient{c} }

func (r *PubsubClient) Topic(name string) PsTopic {
	return &PubsubTopic{r.c.Topic(name)}
}

func (r *PubsubClient) Subscription(name string) PsSubscription {
	return &PubsubSubscription{r.c.Subscription(name)}
}

func (r *PubsubClient) Close() error { return r.c.Close() }

type PubsubTopic struct{ t *pubsub.Topic }

func (r *PubsubTopic) Publish(ctx context.Context, msg *pubsub.Message) PsPublishResult {
	return &PubsubPublishResult{r.t.Publish(ctx, msg)}
}

func (r *PubsubTopic) Exists(ctx context.Context) (bool, error) {
	return r.t.Exists(ctx)
}

type PubsubPublishResult struct{ r *pubsub.PublishResult }

func (r *PubsubPublishResult) Get(ctx context.Context) (string, error) {
	return r.r.Get(ctx)
}

type PubsubSubscription struct{ s *pubsub.Subscription }

func (r *PubsubSubscription) Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error {
	return r.s.Receive(ctx, f)
}

func (r *PubsubSubscription) SetReceiveSettings(settings *pubsub.ReceiveSettings) {
	if settings != nil {
		r.s.ReceiveSettings = *settings
	}
}

func (r *PubsubSubscription) ID() string {
	return r.s.ID()
}

func (r *PubsubSubscription) Exists(ctx context.Context) (bool, error) {
	return r.s.Exists(ctx)
}

func (r *PubsubSubscription) Close() error { return nil }

type SubscriptionConfig struct {
	Topic       PsTopic
	AckDeadline time.Duration
}

func (c *PubsubClient) CreateTopic(ctx context.Context, name string) (PsTopic, error) {
	t, err := c.c.CreateTopic(ctx, name)
	if err != nil {
		return nil, err
	}
	return &PubsubTopic{t}, nil
}

func (c *PubsubClient) CreateSubscription(ctx context.Context, name string, cfg SubscriptionConfig) (PsSubscription, error) {
	// unwrap our PsTopic back to *pubsub.Topic
	realTopic := cfg.Topic.(*PubsubTopic).t
	sub, err := c.c.CreateSubscription(ctx, name, pubsub.SubscriptionConfig{
		Topic:       realTopic,
		AckDeadline: cfg.AckDeadline,
		// … any other fields …
	})
	if err != nil {
		return nil, err
	}
	return &PubsubSubscription{sub}, nil
}

// IsValidPubSubTopic checks if a topic exists with retry logic for resilience during high volume periods
var IsValidPubSubTopic = func(topicName string, ctx context.Context, client PsClient) (bool, error) {
	// Check cache first - if found, we know it exists (skip RPC call)
	if _, found := GetTopicCache().Get(topicName); found {
		logger.Debugf("Topic %q found in cache, skipping validation", topicName)
		return true, nil
	}

	topic := client.Topic(topicName)

	var validTopic bool
	var err error

	// Use retry logic for topic existence check
	retryConfig := DefaultRetryConfig()
	retryErr := RetryWithBackoff(ctx, retryConfig, func() error {
		validTopic, err = topic.Exists(ctx)
		if err != nil {
			logger.Debugf("Topic validation attempt failed for %q: %v", topicName, err)
			return err
		}
		return nil
	})

	if retryErr != nil {
		wrappedErr := fmt.Errorf("Error topic %q validation failed after retries: %w", topicName, retryErr)
		logger.Errorf("%v", wrappedErr)
		return false, wrappedErr
	}

	// Only cache the topic if it exists
	if validTopic {
		GetTopicCache().Set(topicName, topic)
		logger.Debugf("Topic %q validated and cached", topicName)
	}

	return validTopic, nil
}

// GetTopic returns a topic from cache if available, otherwise gets it from the client
// This function does not validate topic existence - use IsValidPubSubTopic for validation
func GetTopic(topicName string, client PsClient) PsTopic {
	// Check cache first
	if cachedTopic, found := GetTopicCache().Get(topicName); found {
		logger.Debugf("Topic %q retrieved from cache", topicName)
		return cachedTopic
	}

	// Not in cache, get from client (but don't cache it until validated)
	logger.Debugf("Topic %q not in cache, getting from client", topicName)
	return client.Topic(topicName)
}

// ValidateAndGetTopic validates a topic exists and returns it, caching for future use
func ValidateAndGetTopic(topicName string, ctx context.Context, client PsClient) (PsTopic, error) {
	// Check if topic is valid
	isValid, err := IsValidPubSubTopic(topicName, ctx, client)
	if err != nil {
		return nil, err
	}
	if !isValid {
		return nil, fmt.Errorf("topic %q does not exist", topicName)
	}

	// At this point, topic should be in cache or we can get it safely
	return GetTopic(topicName, client), nil
}

// PublishWithRetry publishes a message with retry logic
func PublishWithRetry(ctx context.Context, topic PsTopic, msg *pubsub.Message) (string, error) {
	var messageID string
	var err error

	retryConfig := DefaultRetryConfig()
	retryErr := RetryWithBackoff(ctx, retryConfig, func() error {
		result := topic.Publish(ctx, msg)
		messageID, err = result.Get(ctx)
		if err != nil {
			logger.Debugf("Publish attempt failed: %v", err)
			return err
		}
		return nil
	})

	if retryErr != nil {
		return "", fmt.Errorf("publish failed after retries: %w", retryErr)
	}

	return messageID, nil
}
