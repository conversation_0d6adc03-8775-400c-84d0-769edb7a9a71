package connect

import (
	"context"
	"testing"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// --- Test WithConnections and GetConnections ---

func TestWithAndGetConnections(t *testing.T) {
	ctx := context.Background()
	// Create a dummy connections instance. (Here we're not concerned with non-nil fields.)
	dummy := &Connections{
		Redis:    nil,
		Postgres: nil,
		Bigquery: nil,
		Pubsub:   nil,
	}

	// Inject the dummy connections into the context.
	ctxWith := WithConnections(ctx, dummy)

	// Retrieve them back.
	got, err := GetConnections(ctxWith, false)
	require.NoError(t, err, "Expected no error getting connections")
	assert.Same(t, dummy, got, "Retrieved connections should be the same instance")
}

func TestGetConnections_NotPresent(t *testing.T) {
	ctx := context.Background()
	_, err := GetConnections(ctx)
	assert.Error(t, err, "Expected error when connections are not set in context")
}

// Test GetConnections validation errors
func TestGetConnections_ValidationErrors(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name        string
		connections *Connections
		wantErr     string
	}{
		{
			name: "missing Redis",
			connections: &Connections{
				Redis:     nil,
				Postgres:  &PostgresExecutor{},
				Bigquery:  &BigQueryExecutor{},
				Pubsub:    WrapPubsubClient(nil),
				Firestore: &firestoreClientWrapper{},
			},
			wantErr: "no Redis connection found in context",
		},
		{
			name: "missing Postgres",
			connections: &Connections{
				Redis:     &redis.Client{},
				Postgres:  nil,
				Bigquery:  &BigQueryExecutor{},
				Pubsub:    WrapPubsubClient(nil),
				Firestore: &firestoreClientWrapper{},
			},
			wantErr: "no Postgres connection found in context",
		},
		{
			name: "missing BigQuery",
			connections: &Connections{
				Redis:     &redis.Client{},
				Postgres:  &PostgresExecutor{},
				Bigquery:  nil,
				Pubsub:    WrapPubsubClient(nil),
				Firestore: &firestoreClientWrapper{},
			},
			wantErr: "no Bigquery connection found in context",
		},
		{
			name: "missing PubSub",
			connections: &Connections{
				Redis:     &redis.Client{},
				Postgres:  &PostgresExecutor{},
				Bigquery:  &BigQueryExecutor{},
				Pubsub:    nil,
				Firestore: &firestoreClientWrapper{},
			},
			wantErr: "no Pubsub connection found in context",
		},
		{
			name: "missing Firestore",
			connections: &Connections{
				Redis:     &redis.Client{},
				Postgres:  &PostgresExecutor{},
				Bigquery:  &BigQueryExecutor{},
				Pubsub:    WrapPubsubClient(nil),
				Firestore: nil,
			},
			wantErr: "no Firestore connection found in context",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctxWith := WithConnections(ctx, tt.connections)
			_, err := GetConnections(ctxWith)
			assert.Error(t, err, "Expected error for missing connection")
			assert.Contains(t, err.Error(), tt.wantErr, "Error should contain expected message")
		})
	}
}

// --- Test NewConnections by overriding dependency functions ---
//
// For this test we override the globals so that NewConnections returns
// dummy (but recognizable) connection objects.

func TestNewConnections(t *testing.T) {
	// Backup the original functions.
	origGRI := GetReleaseIdentifier
	origRedis := Redis
	origRedisAwait := RedisAwait
	origPubSub := PubSub
	origBigQuery := BigQuery
	origPostgres := Postgres

	// Restore the original functions after the test.
	defer func() {
		GetReleaseIdentifier = origGRI
		Redis = origRedis
		RedisAwait = origRedisAwait
		PubSub = origPubSub
		BigQuery = origBigQuery
		Postgres = origPostgres
	}()

	// Dummy values for testing.
	dummyRelease := "dummy-release"

	// Create dummy structs for connections.
	dummyRedis := &redis.Client{}
	dummyPubsub := WrapPubsubClient(nil)
	dummyBQ := &BigQueryExecutor{}
	dummyPG := &PostgresExecutor{}

	// Override dependencies.
	GetReleaseIdentifier = func() (string, error) {
		return dummyRelease, nil
	}
	Redis = func(ctx context.Context) (*redis.Client, error) {
		return dummyRedis, nil
	}
	RedisAwait = func(ctx context.Context, client *redis.Client, key, release string) error {
		assert.Equal(t, dummyRelease, release, "Release should match expected value")
		return nil
	}
	PubSub = func(ctx context.Context) (PsClient, error) {
		return dummyPubsub, nil
	}
	BigQuery = func(ctx context.Context, config *DatabaseConfig, factory BQClientFactory) (*BigQueryExecutor, error) {
		return dummyBQ, nil
	}
	Postgres = func(ctx context.Context, _ *DatabaseConfig) (*PostgresExecutor, error) {
		return dummyPG, nil
	}

	// Finally, check that the connections are what we expect them to be.
	conns := NewConnections(context.Background())
	require.NotNil(t, conns, "Expected non-nil connections")
	assert.Same(t, dummyRedis, conns.Redis, "Redis client should match expected instance")
	assert.Same(t, dummyPubsub, conns.Pubsub, "PubSub client should match expected instance")
	assert.Same(t, dummyBQ, conns.Bigquery, "BigQuery executor should match expected instance")
	assert.Same(t, dummyPG, conns.Postgres, "Postgres executor should match expected instance")
}

// Here we simply demonstrate that calling Close does not panic.
func TestConnectionsClose_Nil(t *testing.T) {
	// If the Connections pointer itself is nil, Close() should do nothing.
	var conns *Connections = nil
	// Should not panic.
	assert.NotPanics(t, func() {
		conns.Close()
	}, "Close on nil connections should not panic")
}

// Test Connections.Close() with various scenarios
func TestConnections_CloseWithConnections(t *testing.T) {
	tests := []struct {
		name        string
		connections *Connections
	}{
		{
			name: "all connections nil",
			connections: &Connections{
				Redis:     nil,
				Postgres:  nil,
				Bigquery:  nil,
				Pubsub:    nil,
				Firestore: nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Should not panic
			assert.NotPanics(t, func() {
				tt.connections.Close()
			}, "Close should not panic")
		})
	}
}

// Test GetColumn function
func TestGetColumn(t *testing.T) {
	tests := []struct {
		name    string
		data    map[string]interface{}
		key     string
		wantErr bool
	}{
		{
			name: "string value",
			data: map[string]interface{}{"name": "test"},
			key:  "name",
		},
		{
			name: "int value",
			data: map[string]interface{}{"id": 123},
			key:  "id",
		},
		{
			name:    "missing key",
			data:    map[string]interface{}{"name": "test"},
			key:     "missing",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.wantErr {
				_, err := GetColumn[string](tt.data, tt.key)
				assert.Error(t, err, "Expected error for missing key")
			} else {
				switch tt.data[tt.key].(type) {
				case string:
					got, err := GetColumn[string](tt.data, tt.key)
					assert.NoError(t, err, "Expected no error for string value")
					assert.Equal(t, tt.data[tt.key], got, "String value should match")
				case int:
					got, err := GetColumn[int](tt.data, tt.key)
					assert.NoError(t, err, "Expected no error for int value")
					assert.Equal(t, tt.data[tt.key], got, "Int value should match")
				}
			}
		})
	}
}

// Test CombineTableNamespace function
func TestCombineTableNamespace(t *testing.T) {
	tests := []struct {
		name      string
		namespace string
		tableName string
		want      string
	}{
		{
			name:      "normal case",
			namespace: "dev",
			tableName: "users",
			want:      "dev__users",
		},
		{
			name:      "empty namespace",
			namespace: "",
			tableName: "users",
			want:      "__users",
		},
		{
			name:      "empty table name",
			namespace: "dev",
			tableName: "",
			want:      "dev__",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CombineTableNamespace(tt.namespace, tt.tableName)
			assert.Equal(t, tt.want, got, "Combined table namespace should match expected value")
		})
	}
}

// --- Test SplitSQLStatements ---

func TestSplitSQLStatements(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  []string
	}{
		{
			name:  "Single statement without semicolon",
			input: "SELECT * FROM users",
			want:  []string{"SELECT * FROM users"},
		},
		{
			name:  "Multiple statements",
			input: "SELECT * FROM users; SELECT * FROM orders;",
			want:  []string{"SELECT * FROM users", "SELECT * FROM orders"},
		},
		{
			name:  "Semicolon inside single quotes",
			input: "INSERT INTO logs (message) VALUES ('Value; not end'); SELECT * FROM logs;",
			want:  []string{"INSERT INTO logs (message) VALUES ('Value; not end')", "SELECT * FROM logs"},
		},
		{
			name:  "Semicolon inside double quotes",
			input: "INSERT INTO logs (message) VALUES (\"Value; not end\"); SELECT * FROM logs;",
			want:  []string{"INSERT INTO logs (message) VALUES (\"Value; not end\")", "SELECT * FROM logs"},
		},
		{
			name:  "Line comment with semicolon",
			input: "SELECT * FROM users -- comment; not a delimiter\n;SELECT * FROM orders;",
			want:  []string{"SELECT * FROM users -- comment; not a delimiter", "SELECT * FROM orders"},
		},
		{
			name:  "Block comment with semicolon",
			input: "SELECT * FROM users /* block; comment */; SELECT * FROM orders;",
			want:  []string{"SELECT * FROM users /* block; comment */", "SELECT * FROM orders"},
		},
		{
			name:  "Empty string",
			input: "",
			want:  nil,
		},
		{
			name:  "Single semicolon",
			input: ";",
			want:  nil,
		},
		{
			name:  "Multiple semicolons",
			input: "SELECT * FROM users;;",
			want:  []string{"SELECT * FROM users"},
		},
		{
			name:  "Multiple newlines",
			input: "\n\n\n\nSELECT * FROM users;\n\n\n\n\n\n\n;\n\n;\n\nSELECT 1; SELECT 2;",
			want:  []string{"SELECT * FROM users", "SELECT 1", "SELECT 2"},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := SplitSQLStatements(tc.input)
			assert.Equal(t, tc.want, got, "Split SQL statements should match expected result")
		})
	}
}
