package connect

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"cloud.google.com/go/pubsub/pstest"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/option"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// Mock PsTopic
type mockPsTopic struct {
	mock.Mock
}

func (m *mockPsTopic) Publish(ctx context.Context, msg *pubsub.Message) PsPublishResult {
	args := m.Called(ctx, msg)
	return args.Get(0).(PsPublishResult)
}

func (m *mockPsTopic) Exists(ctx context.Context) (bool, error) {
	args := m.Called(ctx)
	return args.Bool(0), args.Error(1)
}

// Mock PsClient
type mockPsClient struct {
	mock.Mock
}

func (m *mockPsClient) Topic(name string) PsTopic {
	args := m.Called(name)
	return args.Get(0).(PsTopic)
}

func (m *mockPsClient) Subscription(name string) PsSubscription {
	panic("not implemented") // not needed for this test
}

func (m *mockPsClient) CreateTopic(ctx context.Context, name string) (PsTopic, error) {
	panic("not implemented")
}

func (m *mockPsClient) CreateSubscription(ctx context.Context, name string, cfg SubscriptionConfig) (PsSubscription, error) {
	panic("not implemented")
}

func (m *mockPsClient) Close() error {
	panic("not implemented")
}

func Test_IsValidPubSubTopic_Success(t *testing.T) {
	ctx := context.Background()

	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(true, nil)

	mockClient := new(mockPsClient)
	mockClient.On("Topic", "existing-topic").Return(mockTopic)

	valid, err := IsValidPubSubTopic("existing-topic", ctx, mockClient)
	assert.NoError(t, err)
	assert.True(t, valid)

	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)
}

func Test_IsValidPubSubTopic_TopicNotExistsWithError(t *testing.T) {
	ctx := context.Background()

	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(false, errors.New("topic does not exist")).Times(4) // Called MaxRetries + 1 times

	mockClient := new(mockPsClient)
	mockClient.On("Topic", "missing-topic").Return(mockTopic)

	valid, err := IsValidPubSubTopic("missing-topic", ctx, mockClient)
	assert.Error(t, err)
	assert.False(t, valid)
	assert.Contains(t, err.Error(), "validation failed after retries")
	assert.Contains(t, err.Error(), "topic does not exist")

	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)
}

func Test_IsValidPubSubTopic_TopicNotExistsNoError(t *testing.T) {
	ctx := context.Background()

	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(false, nil) // Topic doesn't exist but no error

	mockClient := new(mockPsClient)
	mockClient.On("Topic", "nonexistent-topic").Return(mockTopic)

	valid, err := IsValidPubSubTopic("nonexistent-topic", ctx, mockClient)
	assert.NoError(t, err)
	assert.False(t, valid)

	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)
}

func TestWrapPubsubClient(t *testing.T) {
	// Arrange
	mockPubsubClient := &pubsub.Client{}

	// Act
	psClient := WrapPubsubClient(mockPubsubClient)

	// Check if psClient is of type interface PsClient
	assert.Implements(t, (*PsClient)(nil), psClient)

	// Check the concrete type of psClient
	_, ok := psClient.(*PubsubClient)
	assert.True(t, ok, "psClient should be of type *PubsubClient")

	// Check if the wrapped client is the same as the input client
	actualClient := psClient.(*PubsubClient).c
	assert.Equal(t, mockPubsubClient, actualClient, "Wrapped client should match input client")
}

func TestPubsubClient_Topic(t *testing.T) {
	// Arrange
	mockPubsubClient := &pubsub.Client{}
	// mockTopic := &pubsub.Topic{}
	client := &PubsubClient{c: mockPubsubClient}
	topicName := "test-topic"

	// Act
	result := client.Topic(topicName)

	// Check if the result implements the PsTopic interface
	assert.Implements(t, (*PsTopic)(nil), result)

	// Check if the wrapped topic has the correct type
	pubsubTopic, ok := result.(*PubsubTopic)
	assert.True(t, ok, "Result should be of type *PubsubTopic")

	// Verify the wrapped topic has the correct type
	assert.IsType(t, &pubsub.Topic{}, pubsubTopic.t, "Internal topic should be of type *pubsub.Topic")
}

func TestPubsubClient_Subscription(t *testing.T) {
	// Arrange
	mockPubsubClient := &pubsub.Client{}
	client := &PubsubClient{c: mockPubsubClient}
	subscriptionName := "test-subscription"

	// Act
	result := client.Subscription(subscriptionName)

	// Check if the result implements the PsSubscription interface
	assert.Implements(t, (*PsSubscription)(nil), result)

	// Check the concrete type of the result
	pubsubSubscription, ok := result.(*PubsubSubscription)
	assert.True(t, ok, "Result should be of type *PubsubSubscription")

	// Check if the wrapped subscription is of the correct type
	assert.IsType(t, &pubsub.Subscription{}, pubsubSubscription.s, "Internal subscription should be of type *pubsub.Subscription")
}

func TestPubsubClient_Close(t *testing.T) {
	ctx := context.Background()

	// Start the in-memory fake pubsub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := WrapPubsubClient(client)

	// Act: call Close
	err = psClient.Close()

	// Assert
	assert.NoError(t, err)
}

func TestPubsubTopic_Publish(t *testing.T) {
	ctx := context.Background()

	// Start fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create a topic
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	// Wrap topic using our abstraction
	psTopic := &PubsubTopic{t: topic}

	// Publish a message
	msg := &pubsub.Message{
		Data:       []byte("hello"),
		Attributes: map[string]string{"key": "value"},
	}
	result := psTopic.Publish(ctx, msg)

	// Assert: result should implement PsPublishResult
	id, err := result.Get(ctx)
	assert.NoError(t, err)
	assert.NotEmpty(t, id)
}

func TestPubsubTopic_Exists(t *testing.T) {
	ctx := context.Background()

	// Setup fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create a topic
	topicName := "existing-topic"
	topic, err := client.CreateTopic(ctx, topicName)
	assert.NoError(t, err)

	// Wrap topic
	psTopic := &PubsubTopic{t: topic}

	// Act & Assert
	exists, err := psTopic.Exists(ctx)
	assert.NoError(t, err)
	assert.True(t, exists)
}

func TestPubsubSubscription_Receive(t *testing.T) {
	ctx := context.Background()

	// Setup fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	// Initialize PubSub client using this connection
	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create topic and subscription
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, "test-sub", pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	assert.NoError(t, err)

	// Publish message to topic
	res := topic.Publish(ctx, &pubsub.Message{Data: []byte("hello")})
	_, err = res.Get(ctx)
	assert.NoError(t, err)

	// Wrap subscription into PubsubSubscription
	psSub := &PubsubSubscription{s: sub}

	// Setup context and callback for receiving message
	receiveCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	received := false
	err = psSub.Receive(receiveCtx, func(ctx context.Context, msg *pubsub.Message) {
		assert.Equal(t, []byte("hello"), msg.Data)
		msg.Ack()
		received = true
		cancel()
	})

	// Check if message was received successfully
	assert.NoError(t, err)
	assert.True(t, received, "Message should have been received")
}

func TestPubsubSubscription_Exists(t *testing.T) {
	ctx := context.Background()

	// Setup fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	// Initialize PubSub client using this connection
	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create topic and subscription
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, "test-sub", pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	assert.NoError(t, err)

	// Wrap subscription into PubsubSubscription
	psSub := &PubsubSubscription{s: sub}

	// Define the mock behavior for the Exists method
	// Simulate a successful existence check (returns true, nil)
	exists, err := psSub.Exists(context.Background())

	// Assert that there is no error and the subscription exists
	assert.NoError(t, err)
	assert.True(t, exists)
}

func TestPubsubSubscription_Close(t *testing.T) {
	ctx := context.Background()

	// Start the in-memory fake pubsub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, "test-sub", pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	assert.NoError(t, err)

	psSub := &PubsubSubscription{s: sub}

	// Act: call Close
	err = psSub.Close()

	// Assert
	assert.NoError(t, err)
}

func TestPubsubClient_CreateTopic(t *testing.T) {
	ctx := context.Background()

	// Start fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	// Create real pubsub client with mocked connection
	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Wrap client with our abstraction
	psClient := &PubsubClient{c: client}

	// Call the method to test
	psTopic, err := psClient.CreateTopic(ctx, "test-topic")

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, psTopic)

	// Further: verify topic exists
	exists, err := psTopic.Exists(ctx)
	assert.NoError(t, err)
	assert.True(t, exists)
}

func TestPubsubClient_CreateTopic_Duplicate(t *testing.T) {
	ctx := context.Background()

	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := &PubsubClient{c: client}

	// Create topic for the first time (should succeed)
	_, err = psClient.CreateTopic(ctx, "duplicate-topic")
	assert.NoError(t, err)

	// Try to create topic again (should fail)
	topic2, err := psClient.CreateTopic(ctx, "duplicate-topic")
	assert.Error(t, err)
	assert.Nil(t, topic2)
}

func TestPubsubClient_CreateSubscription_Success(t *testing.T) {
	ctx := context.Background()
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := &PubsubClient{c: client}

	// Create topic first
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)
	psTopic := &PubsubTopic{t: topic}

	// Create subscription
	cfg := SubscriptionConfig{
		Topic:       psTopic,
		AckDeadline: 10 * time.Second,
	}

	sub, err := psClient.CreateSubscription(ctx, "test-sub", cfg)
	assert.NoError(t, err)
	assert.NotNil(t, sub)
}

func TestPubsubClient_CreateSubscription_Duplicate(t *testing.T) {
	ctx := context.Background()
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := &PubsubClient{c: client}

	// Create topic first
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)
	psTopic := &PubsubTopic{t: topic}

	cfg := SubscriptionConfig{
		Topic:       psTopic,
		AckDeadline: 10 * time.Second,
	}

	// First creation (should succeed)
	_, err = psClient.CreateSubscription(ctx, "test-sub", cfg)
	assert.NoError(t, err)

	// Create duplicate subscription (should fail)
	sub2, err := psClient.CreateSubscription(ctx, "test-sub", cfg)
	assert.Error(t, err)
	assert.Nil(t, sub2)
}

func TestPubSub_SuccessWithEmulator(t *testing.T) {
	ctx := context.Background()
	srv := pstest.NewServer()
	defer srv.Close()

	// Setup env
	t.Setenv("GCP_PROJECT_ID", "")
	t.Setenv("PUBSUB_EMULATOR_HOST", srv.Addr)

	client, err := PubSub(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, client)

	// Ensure client can create a topic (sanity check)
	topic, err := client.CreateTopic(ctx, "demo")
	assert.NoError(t, err)
	assert.NotNil(t, topic)
}

func savePubSubVars() func() {
	origRedisNewClient := pubsubNewClient
	origtimeSleepPubSub := timeSleepPubSub
	return func() {
		pubsubNewClient = origRedisNewClient
		timeSleepPubSub = origtimeSleepPubSub
	}
}

func TestPubSub_Failure_MaxRetries(t *testing.T) {
	restore := savePubSubVars()
	defer restore()

	ctx := context.Background()

	// Unset emulator host to simulate real connection (which will fail in test env)
	t.Setenv("GCP_PROJECT_ID", "")
	t.Setenv("PUBSUB_EMULATOR_HOST", "")

	pubsubNewClient = func(ctx context.Context, projectID string, opts ...option.ClientOption) (*pubsub.Client, error) {
		return nil, errors.New("forced failure")
	}
	timeSleepPubSub = func(d time.Duration) {}

	client, err := PubSub(ctx)
	assert.Nil(t, client)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to create PubSub client after 5 attempts")
}

func TestDefaultRetryConfig(t *testing.T) {
	config := DefaultRetryConfig()
	assert.Equal(t, 3, config.MaxRetries)
	assert.Equal(t, 100*time.Millisecond, config.BaseDelay)
	assert.Equal(t, 5*time.Second, config.MaxDelay)
	assert.Equal(t, 2.0, config.Multiplier)
	assert.True(t, config.Jitter)
}

func TestRetryWithBackoff_Success(t *testing.T) {
	ctx := context.Background()
	config := RetryConfig{
		MaxRetries: 3,
		BaseDelay:  10 * time.Millisecond,
		MaxDelay:   100 * time.Millisecond,
		Multiplier: 2.0,
		Jitter:     false, // Disable jitter for predictable testing
	}

	callCount := 0
	operation := func() error {
		callCount++
		if callCount < 3 {
			return errors.New("simulated failure")
		}
		return nil
	}

	err := RetryWithBackoff(ctx, config, operation)
	assert.NoError(t, err)
	assert.Equal(t, 3, callCount)
}

func TestRetryWithBackoff_ExhaustsRetries(t *testing.T) {
	ctx := context.Background()
	config := RetryConfig{
		MaxRetries: 2,
		BaseDelay:  1 * time.Millisecond,
		MaxDelay:   10 * time.Millisecond,
		Multiplier: 2.0,
		Jitter:     false,
	}

	callCount := 0
	operation := func() error {
		callCount++
		return errors.New("persistent failure")
	}

	err := RetryWithBackoff(ctx, config, operation)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "operation failed after 3 attempts")
	assert.Equal(t, 3, callCount) // MaxRetries + 1
}

func TestRetryWithBackoff_ContextCancellation(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	config := RetryConfig{
		MaxRetries: 5,
		BaseDelay:  100 * time.Millisecond,
		MaxDelay:   1 * time.Second,
		Multiplier: 2.0,
		Jitter:     false,
	}

	callCount := 0
	operation := func() error {
		callCount++
		if callCount == 2 {
			cancel() // Cancel context after first retry
		}
		return errors.New("simulated failure")
	}

	err := RetryWithBackoff(ctx, config, operation)
	assert.Error(t, err)
	assert.Equal(t, context.Canceled, err)
	assert.Equal(t, 2, callCount)
}

func TestRetryWithBackoff_MaxDelayCapReached(t *testing.T) {
	ctx := context.Background()
	config := RetryConfig{
		MaxRetries: 3,
		BaseDelay:  1 * time.Second, // Large base delay
		MaxDelay:   2 * time.Second, // Small max delay - will trigger capping
		Multiplier: 10.0,            // Large multiplier to exceed max quickly
		Jitter:     false,           // No jitter for predictable testing
	}

	callCount := 0
	operation := func() error {
		callCount++
		return errors.New("persistent failure")
	}

	err := RetryWithBackoff(ctx, config, operation)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "operation failed after 4 attempts")
	assert.Equal(t, 4, callCount) // MaxRetries + 1
}

func TestRetryWithBackoff_WithJitter(t *testing.T) {
	ctx := context.Background()
	config := RetryConfig{
		MaxRetries: 2,
		BaseDelay:  10 * time.Millisecond,
		MaxDelay:   100 * time.Millisecond,
		Multiplier: 2.0,
		Jitter:     true, // Enable jitter to test jitter calculation
	}

	callCount := 0
	operation := func() error {
		callCount++
		return errors.New("failure with jitter")
	}

	err := RetryWithBackoff(ctx, config, operation)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "operation failed after 3 attempts")
	assert.Equal(t, 3, callCount) // MaxRetries + 1
}

func TestIsValidPubSubTopic_WithRetries_Success(t *testing.T) {
	// Clear cache to avoid interference from other tests
	GetTopicCache().Clear()

	ctx := context.Background()

	mockTopic := new(mockPsTopic)
	// First two calls fail, third succeeds
	mockTopic.On("Exists", ctx).Return(false, errors.New("transient error")).Twice()
	mockTopic.On("Exists", ctx).Return(true, nil).Once()

	mockClient := new(mockPsClient)
	mockClient.On("Topic", "test-topic").Return(mockTopic)

	valid, err := IsValidPubSubTopic("test-topic", ctx, mockClient)
	assert.NoError(t, err)
	assert.True(t, valid)

	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)
}

func TestIsValidPubSubTopic_WithRetries_PersistentFailure(t *testing.T) {
	// Clear cache to avoid interference from other tests
	GetTopicCache().Clear()

	ctx := context.Background()

	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(false, errors.New("persistent error")).Times(4) // MaxRetries + 1

	mockClient := new(mockPsClient)
	mockClient.On("Topic", "test-topic").Return(mockTopic)

	valid, err := IsValidPubSubTopic("test-topic", ctx, mockClient)
	assert.Error(t, err)
	assert.False(t, valid)
	assert.Contains(t, err.Error(), "validation failed after retries")

	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)
}

// Mock for testing PublishWithRetry
type mockPublishResult struct {
	mock.Mock
}

func (m *mockPublishResult) Get(ctx context.Context) (string, error) {
	args := m.Called(ctx)
	return args.String(0), args.Error(1)
}

type mockTopicForPublish struct {
	mock.Mock
}

func (m *mockTopicForPublish) Publish(ctx context.Context, msg *pubsub.Message) PsPublishResult {
	args := m.Called(ctx, msg)
	return args.Get(0).(PsPublishResult)
}

func (m *mockTopicForPublish) Exists(ctx context.Context) (bool, error) {
	args := m.Called(ctx)
	return args.Bool(0), args.Error(1)
}

func TestPublishWithRetry_Success(t *testing.T) {
	ctx := context.Background()
	msg := &pubsub.Message{Data: []byte("test")}

	mockResult := new(mockPublishResult)
	// First two calls fail, third succeeds
	mockResult.On("Get", ctx).Return("", errors.New("transient publish error")).Twice()
	mockResult.On("Get", ctx).Return("message-id-123", nil).Once()

	mockTopic := new(mockTopicForPublish)
	mockTopic.On("Publish", ctx, msg).Return(mockResult).Times(3)

	messageID, err := PublishWithRetry(ctx, mockTopic, msg)
	assert.NoError(t, err)
	assert.Equal(t, "message-id-123", messageID)

	mockResult.AssertExpectations(t)
	mockTopic.AssertExpectations(t)
}

func TestPublishWithRetry_PersistentFailure(t *testing.T) {
	ctx := context.Background()
	msg := &pubsub.Message{Data: []byte("test")}

	mockResult := new(mockPublishResult)
	mockResult.On("Get", ctx).Return("", errors.New("persistent publish error")).Times(4) // MaxRetries + 1

	mockTopic := new(mockTopicForPublish)
	mockTopic.On("Publish", ctx, msg).Return(mockResult).Times(4)

	messageID, err := PublishWithRetry(ctx, mockTopic, msg)
	assert.Error(t, err)
	assert.Empty(t, messageID)
	assert.Contains(t, err.Error(), "publish failed after retries")

	mockResult.AssertExpectations(t)
	mockTopic.AssertExpectations(t)
}

func TestPubsubSubscription_SetReceiveSettings(t *testing.T) {
	ctx := context.Background()

	// Start fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create a topic and subscription
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, "test-sub", pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	assert.NoError(t, err)

	psSub := &PubsubSubscription{s: sub}

	tests := []struct {
		name           string
		settings       *pubsub.ReceiveSettings
		expectedResult pubsub.ReceiveSettings
	}{
		{
			name: "Set valid receive settings",
			settings: &pubsub.ReceiveSettings{
				MaxExtension:           10 * time.Second,
				MaxOutstandingMessages: 100,
				MaxOutstandingBytes:    1e9,
				NumGoroutines:          10,
			},
			expectedResult: pubsub.ReceiveSettings{
				MaxExtension:           10 * time.Second,
				MaxOutstandingMessages: 100,
				MaxOutstandingBytes:    1e9,
				NumGoroutines:          10,
			},
		},
		{
			name: "Set different receive settings",
			settings: &pubsub.ReceiveSettings{
				MaxExtension:           30 * time.Second,
				MaxOutstandingMessages: 500,
				MaxOutstandingBytes:    2e9,
				NumGoroutines:          20,
			},
			expectedResult: pubsub.ReceiveSettings{
				MaxExtension:           30 * time.Second,
				MaxOutstandingMessages: 500,
				MaxOutstandingBytes:    2e9,
				NumGoroutines:          20,
			},
		},
		{
			name:     "Set nil settings (should not change existing settings)",
			settings: nil,
			expectedResult: pubsub.ReceiveSettings{
				MaxExtension:           30 * time.Second,
				MaxOutstandingMessages: 500,
				MaxOutstandingBytes:    2e9,
				NumGoroutines:          20,
			},
		},
		{
			name: "Set zero values",
			settings: &pubsub.ReceiveSettings{
				MaxExtension:           0,
				MaxOutstandingMessages: 0,
				MaxOutstandingBytes:    0,
				NumGoroutines:          0,
			},
			expectedResult: pubsub.ReceiveSettings{
				MaxExtension:           0,
				MaxOutstandingMessages: 0,
				MaxOutstandingBytes:    0,
				NumGoroutines:          0,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Act: call SetReceiveSettings
			psSub.SetReceiveSettings(tt.settings)

			// Assert: verify the settings were applied
			assert.Equal(t, tt.expectedResult.MaxExtension, psSub.s.ReceiveSettings.MaxExtension)
			assert.Equal(t, tt.expectedResult.MaxOutstandingMessages, psSub.s.ReceiveSettings.MaxOutstandingMessages)
			assert.Equal(t, tt.expectedResult.MaxOutstandingBytes, psSub.s.ReceiveSettings.MaxOutstandingBytes)
			assert.Equal(t, tt.expectedResult.NumGoroutines, psSub.s.ReceiveSettings.NumGoroutines)
		})
	}
}

func TestPubsubSubscription_ID(t *testing.T) {
	ctx := context.Background()

	// Start fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create a topic
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	tests := []struct {
		name             string
		subscriptionName string
		expectedID       string
		shouldContain    string
	}{
		{
			name:             "Simple subscription name",
			subscriptionName: "test-subscription",
			expectedID:       "test-subscription",
			shouldContain:    "test-subscription",
		},
		{
			name:             "Complex subscription name with dashes",
			subscriptionName: "complex-subscription-name-with-dashes",
			expectedID:       "complex-subscription-name-with-dashes",
			shouldContain:    "complex-subscription-name-with-dashes",
		},
		{
			name:             "Subscription name with underscores",
			subscriptionName: "subscription_with_underscores",
			expectedID:       "subscription_with_underscores",
			shouldContain:    "subscription_with_underscores",
		},
		{
			name:             "Short subscription name",
			subscriptionName: "sub",
			expectedID:       "sub",
			shouldContain:    "sub",
		},
		{
			name:             "Subscription name with numbers",
			subscriptionName: "subscription-123",
			expectedID:       "subscription-123",
			shouldContain:    "subscription-123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create subscription for this test case
			sub, err := client.CreateSubscription(ctx, tt.subscriptionName, pubsub.SubscriptionConfig{
				Topic:       topic,
				AckDeadline: 10 * time.Second,
			})
			assert.NoError(t, err)

			psSub := &PubsubSubscription{s: sub}

			// Act: call ID
			id := psSub.ID()

			// Assert: verify the ID matches the subscription name
			assert.Equal(t, tt.expectedID, id)
			assert.NotEmpty(t, id)
			assert.Contains(t, id, tt.shouldContain)
		})
	}
}

func TestTopicCache_CachesFunctionalityDemo(t *testing.T) {
	// Clear cache to start fresh
	GetTopicCache().Clear()

	ctx := context.Background()

	// Create mock topic that exists
	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(true, nil).Once() // Should only be called once

	// Create mock client
	mockClient := new(mockPsClient)
	mockClient.On("Topic", "cached-topic").Return(mockTopic)

	// First call - should validate and cache the topic
	valid1, err1 := IsValidPubSubTopic("cached-topic", ctx, mockClient)
	if err1 != nil {
		t.Fatalf("First validation failed: %v", err1)
	}
	if !valid1 {
		t.Fatal("First validation should return true")
	}

	// Verify topic is now in cache
	if GetTopicCache().Size() != 1 {
		t.Fatalf("Expected 1 topic in cache, got %d", GetTopicCache().Size())
	}

	// Second call - should use cache and NOT call Exists() again
	valid2, err2 := IsValidPubSubTopic("cached-topic", ctx, mockClient)
	if err2 != nil {
		t.Fatalf("Second validation failed: %v", err2)
	}
	if !valid2 {
		t.Fatal("Second validation should return true from cache")
	}

	// Verify expectations - Exists should have been called only once
	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)

	// Clean up
	GetTopicCache().Clear()
}

func TestTopicCache_Operations(t *testing.T) {
	cache := NewTopicCache()

	// Test empty cache
	if cache.Size() != 0 {
		t.Fatalf("Expected empty cache, got size %d", cache.Size())
	}

	// Test Get on empty cache
	if _, found := cache.Get("nonexistent"); found {
		t.Fatal("Expected false for nonexistent topic")
	}

	// Test Set and Get
	mockTopic := new(mockPsTopic)
	cache.Set("test-topic", mockTopic)

	if cache.Size() != 1 {
		t.Fatalf("Expected cache size 1, got %d", cache.Size())
	}

	if topic, found := cache.Get("test-topic"); !found {
		t.Fatal("Expected to find test-topic in cache")
	} else if topic != mockTopic {
		t.Fatal("Retrieved topic doesn't match original")
	}

	// Test Remove
	cache.Remove("test-topic")
	if cache.Size() != 0 {
		t.Fatalf("Expected empty cache after remove, got size %d", cache.Size())
	}

	if _, found := cache.Get("test-topic"); found {
		t.Fatal("Expected topic to be removed from cache")
	}

	// Test Clear
	cache.Set("topic1", mockTopic)
	cache.Set("topic2", mockTopic)
	if cache.Size() != 2 {
		t.Fatalf("Expected cache size 2, got %d", cache.Size())
	}

	cache.Clear()
	if cache.Size() != 0 {
		t.Fatalf("Expected empty cache after clear, got size %d", cache.Size())
	}
}

func TestGetTopic_WithCache(t *testing.T) {
	// Clear cache to start fresh
	GetTopicCache().Clear()

	ctx := context.Background()

	// Create mock topic
	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(true, nil)

	// Create mock client
	mockClient := new(mockPsClient)
	mockClient.On("Topic", "test-topic").Return(mockTopic)
	mockClient.On("Topic", "uncached-topic").Return(mockTopic)

	// First, cache the topic
	_, err := IsValidPubSubTopic("test-topic", ctx, mockClient)
	if err != nil {
		t.Fatalf("Failed to validate and cache topic: %v", err)
	}

	// Now GetTopic should return from cache
	topic := GetTopic("test-topic", mockClient)
	if topic == nil {
		t.Fatal("Expected topic from cache, got nil")
	}

	// Test GetTopic with uncached topic
	uncachedTopic := GetTopic("uncached-topic", mockClient)
	if uncachedTopic == nil {
		t.Fatal("Expected topic from client, got nil")
	}

	// Clean up
	GetTopicCache().Clear()
}

func TestValidateAndGetTopic(t *testing.T) {
	// Clear cache to start fresh
	GetTopicCache().Clear()

	ctx := context.Background()

	// Create mock topic that exists
	validMockTopic := new(mockPsTopic)
	validMockTopic.On("Exists", ctx).Return(true, nil)

	// Create mock topic that doesn't exist
	invalidMockTopic := new(mockPsTopic)
	invalidMockTopic.On("Exists", ctx).Return(false, nil)

	// Create mock client
	mockClient := new(mockPsClient)
	mockClient.On("Topic", "valid-topic").Return(validMockTopic)
	mockClient.On("Topic", "invalid-topic").Return(invalidMockTopic)

	// Test with valid topic
	topic, err := ValidateAndGetTopic("valid-topic", ctx, mockClient)
	if err != nil {
		t.Fatalf("Failed to validate and get topic: %v", err)
	}
	if topic == nil {
		t.Fatal("Expected valid topic, got nil")
	}

	// Test with invalid topic
	_, err = ValidateAndGetTopic("invalid-topic", ctx, mockClient)
	if err == nil {
		t.Fatal("Expected error for invalid topic, got nil")
	}

	// Test with topic that has validation error
	errorMockTopic := new(mockPsTopic)
	errorMockTopic.On("Exists", ctx).Return(false, fmt.Errorf("validation error"))
	mockClient.On("Topic", "error-topic").Return(errorMockTopic)

	_, err = ValidateAndGetTopic("error-topic", ctx, mockClient)
	if err == nil {
		t.Fatal("Expected error for topic validation failure, got nil")
	}
	if !strings.Contains(err.Error(), "validation failed after retries") {
		t.Fatalf("Expected validation error, got: %v", err)
	}

	// Clean up
	GetTopicCache().Clear()
}

// Test PubsubTopic wrapper
func TestPubsubTopic_Wrapper(t *testing.T) {
	mockTopic := &pubsub.Topic{}
	wrapper := &PubsubTopic{t: mockTopic}

	// Test that wrapper exists and has correct type
	assert.NotNil(t, wrapper)
	assert.IsType(t, &PubsubTopic{}, wrapper)
}

// Test PubsubSubscription wrapper
func TestPubsubSubscription_Wrapper(t *testing.T) {
	mockSub := &pubsub.Subscription{}
	wrapper := &PubsubSubscription{s: mockSub}

	// Test Close method - should not panic and return nil
	assert.NotPanics(t, func() {
		err := wrapper.Close()
		assert.NoError(t, err)
	})
}

// Test WrapPubsubClient function wrapper creation
func TestWrapPubsubClient_Basic(t *testing.T) {
	// Test WrapPubsubClient function with nil client
	wrapper := WrapPubsubClient(nil)
	assert.NotNil(t, wrapper)
	assert.IsType(t, &PubsubClient{}, wrapper)
}
