package authenticate

import (
	"net/http"

	response "synapse-its.com/shared/api/response"
)

// Interfaces for testing
type UserAccessor interface {
	UserAccess(userName, password string, w http.ResponseWriter, r *http.Request)
}

// For testing purposes
var (
	newUserHandlerFunc = func() UserAccessor { return NewUserHandler() }
)

func Handler(w http.ResponseWriter, r *http.Request) {
	// Process as a normal auth request
	userName, password, err := extractUserNameAndPassword(r.Body)
	if err != nil {
		response.CreateUnauthorizedResponse(w)
		return
	}
	userHandler := newUserHandlerFunc()
	userHandler.UserAccess(userName, password, w, r)
}
