package authenticate

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	security "synapse-its.com/shared/api/security"
	softwareGateway "synapse-its.com/shared/api/softwaregateway"
	connect "synapse-its.com/shared/connect"
	mocks "synapse-its.com/shared/mocks"
)

// fakeResult implements sql.Result for testing.
type fakeResult struct{}

func (r fakeResult) LastInsertId() (int64, error) { return 0, nil }
func (r fakeResult) RowsAffected() (int64, error) { return 1, nil }

// --- Store Original Functions to Enable Override ---
var (
	origParseRequest       = parseRequest
	origGetGatewayInfo     = getGatewayInfo
	origGetDeviceSettings  = getDeviceSettings
	origSetNewGatewayToken = setNewGatewayToken
	origGetJWTAsymmetric   = security.GetJWTAsymmetric
)

// restoreOverrides resets all package‑level function variables after each test.
func restoreOverrides() {
	parseRequest = origParseRequest
	getGatewayInfo = origGetGatewayInfo
	getDeviceSettings = origGetDeviceSettings
	setNewGatewayToken = origSetNewGatewayToken
	security.GetJWTAsymmetric = origGetJWTAsymmetric
}

// --- Tests for Handler ---

func TestHandler_ParseRequestError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "", "", "", errors.New("parse error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized due to parse error, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// Test when getGatewayInfo returns an error.
func TestHandler_GetGatewayInfoError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-missing", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return nil, errors.New("gateway info not found")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-missing")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized when getGatewayInfo fails, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// Test when config parsing fails (invalid JSON).
func TestHandler_InvalidConfig(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: "invalid json",
		}, nil
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to invalid config JSON, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when GetJWTAsymmetric returns an error.
func TestHandler_GetJWTError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: "{}",
		}, nil
	}
	// Override GetJWTAsymmetric to return an error.
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "", "", errors.New("JWT error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to GetJWTAsymmetric error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when getDeviceSettings returns an error.
func TestHandler_GetDeviceSettingsError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: "{}",
		}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return nil, errors.New("device settings error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to device settings error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when setNewGatewayToken returns an error.
func TestHandler_SetNewGatewayTokenError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: "{}",
		}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{
			{Device_ID: "dev1"},
		}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
		return nil, errors.New("set token error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to setNewGatewayToken error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when the Postgres connection is nil.
func TestHandler_GetConnectionsError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")

	// Don't set connections

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to nil Postgres, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when the Postgres connection is nil.
func TestHandler_NilPostgres(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")

	// Create a connections object with Postgres set to nil.
	conns := &connect.Connections{
		Postgres: nil,
	}
	req = req.WithContext(connect.WithConnections(context.Background(), conns))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to nil Postgres, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test the full successful flow.
func TestHandler_Success(t *testing.T) {
	defer restoreOverrides()
	// Override all dependencies to simulate a successful authentication.
	parseRequest = func(r *http.Request) (string, string, string, error) {
		return "gw-123", "abc", "", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string, rqToken string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			Id: "123", OrganizationId: "org1", APIKey: "key", Token: "abc", Config: `{"application_version": "1.0.0"}`,
		}, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId string) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{
			{Device_ID: "dev1"},
		}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string, gatewayVersion string) (*softwareGateway.CloudSettings, error) {
		return &softwareGateway.CloudSettings{Token: "new-token"}, nil
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d OK on success, got %d", http.StatusOK, rr.Code)
	}

	// The response envelope is created using CreateSuccessResponse.
	// Define a response type matching that envelope.
	type SuccessResponse struct {
		Status  string                         `json:"status"`
		Data    softwareGateway.GlobalSettings `json:"data"`
		Message string                         `json:"message"`
		Code    int                            `json:"code"`
	}

	var resp SuccessResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &resp); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	if resp.Status != "success" {
		t.Errorf("Expected status 'success', got %s", resp.Status)
	}
	if resp.Message != "Request Succeeded" {
		t.Errorf("Expected message 'Request Succeeded', got %s", resp.Message)
	}
	if resp.Code != http.StatusOK {
		t.Errorf("Expected code %d, got %d", http.StatusOK, resp.Code)
	}
	// Verify the global settings in the response.
	gs := resp.Data
	if gs.OrganizationId != "org1" {
		t.Errorf("Expected OrganizationId 'org1', got %s", gs.OrganizationId)
	}
	if gs.PublicKey != "public" {
		t.Errorf("Expected PublicKey 'public', got %s", gs.PublicKey)
	}
	if gs.AWS.Token != "new-token" {
		t.Errorf("Expected CloudSettings Token 'new-token', got %s", gs.AWS.Token)
	}
	if len(gs.Devices) != 1 {
		t.Errorf("Expected 1 device setting, got %d", len(gs.Devices))
	}
}

// --- Tests for parseRequest ---

func TestParseRequest_MissingGatewayDeviceID(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("message-type", "authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "header gateway-device-id is not present" {
		t.Errorf("Expected error for missing gateway-device-id, got: %v", err)
	}
}

func TestParseRequest_MissingMessageType(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "header message-type is not present" {
		t.Errorf("Expected error for missing message-type, got: %v", err)
	}
}

func TestParseRequest_InvalidMessageType(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "not-authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "header message-type != authenticate" {
		t.Errorf("Expected error for invalid message-type, got: %v", err)
	}
}

func TestParseRequest_InvalidJSONBody(t *testing.T) {
	body := `not a json`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil {
		t.Error("Expected error for invalid JSON body, got nil")
	}
}

func TestParseRequest_MissingTokenInBody(t *testing.T) {
	body := `{"notToken": "abc"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	_, _, _, err := parseRequest(req)
	if err == nil || err.Error() != "token does not exist in the body" {
		t.Errorf("Expected error for missing token in body, got: %v", err)
	}
}

func TestParseRequest_Success(t *testing.T) {
	const tokenVal = "abc123"
	body := `{"token": "` + tokenVal + `"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	gatewayID, token, _, err := parseRequest(req)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if gatewayID != "gateway-001" {
		t.Errorf("Expected gateway-device-id %q, got %q", "gateway-001", gatewayID)
	}
	if token != tokenVal {
		t.Errorf("Expected token %q, got %q", tokenVal, token)
	}
}

// --- Tests for getGatewayInfo ---

func TestGetGatewayInfo_Success(t *testing.T) {
	gatewayID := "gateway-001"
	rqToken := "request-token"
	queryResults := &dbGatewayInfo{
		Id:             "123",
		OrganizationId: "org-001",
		APIKey:         "api-key-value",
		Config:         `{"setting": "value"}`,
		Token:          "dbtoken",
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			db, ok := dest.(*dbGatewayInfo)
			if !ok {
				return errors.New("dest must be of type *dbGatewayInfo")
			}
			*db = *queryResults
			return nil
		},
	}

	gatewayInfo, err := getGatewayInfo(fakeDB, gatewayID, rqToken)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if gatewayInfo.Id != "123" {
		t.Errorf("Expected id '123', got %v", gatewayInfo.Id)
	}
	if gatewayInfo.OrganizationId != "org-001" {
		t.Errorf("Expected org 'org-001', got %v", gatewayInfo.OrganizationId)
	}
	if gatewayInfo.APIKey != "api-key-value" {
		t.Errorf("Expected apiKey 'api-key-value', got %v", gatewayInfo.APIKey)
	}
	if gatewayInfo.Token != "dbtoken" {
		t.Errorf("Expected dbToken 'dbtoken', got %v", gatewayInfo.Token)
	}
}

func TestGetGatewayInfo_NoRows(t *testing.T) {
	gatewayID := "unknown-gateway"
	rqToken := "request-token"
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return sql.ErrNoRows
		},
	}

	_, err := getGatewayInfo(fakeDB, gatewayID, rqToken)
	if err == nil || !strings.Contains(err.Error(), "was not found") {
		t.Errorf("Expected error for missing gateway, got: %v", err)
	}
}

func TestGetGatewayInfo_GenericQueryError(t *testing.T) {
	gatewayID := "unknown-gateway"
	rqToken := "request-token"
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("Generic error non-sql.norows error")
		},
	}

	_, err := getGatewayInfo(fakeDB, gatewayID, rqToken)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}

// ---------- Tests for getDeviceSettings ----------

func TestGetDeviceSettings_Success(t *testing.T) {
	// Use keys matching the DeviceSettings JSON tags.
	dbDeviceSettings := []softwareGateway.DeviceSettings{
		{
			Device_ID:          "dev-001",
			Latitude:           "12.34",
			Longitude:          "56.78",
			IP_Address:         "***********",
			Port:               "8080",
			FlushConnection_MS: "100",
			EnableRealtime:     "true",
		},
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			instrSlice, ok := dest.(*[]softwareGateway.DeviceSettings)
			if !ok {
				return errors.New("dest is not of type *[]softwareGateway.DeviceSettings")
			}
			*instrSlice = dbDeviceSettings
			return nil
		},
	}

	deviceSettings, err := getDeviceSettings(fakeDB, "test-gateway-id")
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if len(*deviceSettings) != 1 {
		t.Errorf("Expected 1 device configuration, got %d", len(*deviceSettings))
	}
	ds := (*deviceSettings)[0]
	if ds.Device_ID != "dev-001" {
		t.Errorf("Expected Device_ID 'dev-001', got %q", ds.Device_ID)
	}
	if ds.Latitude != "12.34" {
		t.Errorf("Expected Latitude '12.34', got %q", ds.Latitude)
	}
	if ds.Longitude != "56.78" {
		t.Errorf("Expected Longitude '56.78', got %q", ds.Longitude)
	}
	if ds.IP_Address != "***********" {
		t.Errorf("Expected IP_Address '***********', got %q", ds.IP_Address)
	}
	if ds.Port != "8080" {
		t.Errorf("Expected Port '8080', got %q", ds.Port)
	}
	if ds.FlushConnection_MS != "100" {
		t.Errorf("Expected FlushConnection_MS '100', got %q", ds.FlushConnection_MS)
	}
	if ds.EnableRealtime != "true" {
		t.Errorf("Expected EnableRealtime 'true', got %q", ds.EnableRealtime)
	}
}

func TestGetDeviceSettings_GenericQueryError(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("Generic error non-sql.norows error")
		},
	}

	_, err := getDeviceSettings(fakeDB, "test-gateway-id")
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected 'Generic error', got: %v", err)
	}
}

// ---------- Tests for setNewGatewayToken ----------

func TestSetNewGatewayToken_Success(t *testing.T) {
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			if len(args) != 4 {
				return nil, errors.New("wrong number of args")
			}
			tokenArg, ok := args[0].(string)
			if !ok || tokenArg == "" {
				return nil, errors.New("token is empty")
			}
			// Validate the timestamp format.
			if _, err := time.Parse(time.DateTime, args[1].(string)); err != nil {
				return nil, errors.New("invalid timestamp")
			}
			if args[3] != gatewayID {
				return nil, errors.New("gateway identifier mismatch")
			}
			return fakeResult{}, nil
		},
	}

	cloudSettings, err := setNewGatewayToken(fakeDB, gatewayID, "1.0.0")
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if cloudSettings.Token == "" {
		t.Errorf("Expected a non-empty new token, got: %v", cloudSettings.Token)
	}
}

func TestSetNewGatewayToken_ExecError(t *testing.T) {
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, errors.New("exec error")
		},
	}

	cloudSettings, err := setNewGatewayToken(fakeDB, gatewayID, "1.0.0")
	if err == nil {
		t.Error("Expected error due to Exec failure, got nil")
	}
	if cloudSettings != nil {
		if cloudSettings.Token != "" {
			t.Errorf("Expected empty token on error, got: %q", cloudSettings.Token)
		}
	}
}

func TestSetNewGatewayToken_GenTokenHexError(t *testing.T) {
	origRandRead := randRead
	defer func() { randRead = origRandRead }()
	readfail := errors.New("read fail")
	randRead = func(b []byte) (int, error) {
		return 0, readfail
	}
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, nil
		},
	}

	_, err := setNewGatewayToken(fakeDB, gatewayID, "1.0.0")
	if !errors.Is(err, readfail) {
		t.Error("Expected error due to randRead failure, got nil")
	}
}
