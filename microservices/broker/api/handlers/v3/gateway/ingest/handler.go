package ingest

import (
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/Masterminds/semver/v3"

	apiShared "synapse-its.com/shared/api"
	response "synapse-its.com/shared/api/response"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

func Handler(w http.ResponseWriter, r *http.Request) {
	// Parse the headers of the request
	headerDetails, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Get the connections.
	ctx := r.Context()
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		response.CreateInternalErrorResponse(w)
		logger.Debugf("%v", err)
		return
	}

	// Authenticates the api request and grabs the OrganizationIdentifier the gateway is assigned too.
	orgId, err := authenticateInfo(connections.Postgres, headerDetails)
	if err != nil {
		logger.Infof("unauthorized login attempt for gatewayId (%s): %v", headerDetails.GatewayDeviceID, err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		response.CreateInternalErrorResponse(w)
		logger.Debugf("error reading body")
		return
	}

	topicName := "broker-gateway-" + headerDetails.MessageType

	isValidTopic, err := connect.IsValidPubSubTopic(topicName, ctx, connections.Pubsub)
	if err != nil {
		response.CreateInternalErrorResponse(w)
		logger.Errorf("error validating topic name: %v", err)
		return
	}

	if !isValidTopic {
		response.CreateInternalErrorResponse(w)
		logger.Errorf("error invalid topic name: "+topicName+" %v", err)
		return
	}

	topic := connections.Pubsub.Topic(topicName)

	errCh := make(chan error, 2)
	var wg sync.WaitGroup
	responded := false

	switch headerDetails.MessageType {
	// Save rmsData to redis
	case "rmsData":
		// Immediate ACK so that gateway can close http connection and avoid long delays
		// This is for the effeciency of the gateway
		response.CreateSuccessResponse("", w)
		responded = true

		wg.Add(1)
		go func() {
			defer wg.Done()

			// Build & marshal redis data
			rd := apiShared.RedisData{
				MsgVersion:      headerDetails.MessageVersion,
				MsgData:         base64.StdEncoding.EncodeToString(bodyBytes), // Encode to base64 so message can be json marshalled
				MsgTimestamp:    time.Now().UTC().Format(time.RFC3339),
				GatewayTimezone: headerDetails.GatewayTimezone,
			}
			buf, _ := json.Marshal(rd)

			// Generate rmsData redis key
			key := fmt.Sprintf("GatewayRMSData:%s", headerDetails.GatewayDeviceID)

			// Set value in redis
			if err := connect.RedisSet(ctx, connections.Redis, key, string(buf)); err != nil {
				errCh <- fmt.Errorf("redis publish error: %w", err)
				return
			}

			errCh <- nil
		}()
	}

	// Save record to pubsub
	wg.Add(1)
	go func() {
		defer wg.Done()

		commonAttributes := pubsubdata.CommonAttributes{
			Topic:                  topicName,
			OrganizationIdentifier: orgId,
			DeviceType:             "gateway",
		}
		attrs := pubsubdata.BuildAttributes(commonAttributes, *headerDetails)

		// Publish to pubsub topic with retry logic
		id, err := connect.PublishWithRetry(ctx, topic, &pubsub.Message{Data: bodyBytes, Attributes: attrs})
		if err != nil {
			errCh <- fmt.Errorf("pubsub publish error: %w", err)
			return
		}

		logger.Debugf("Published message ID %s to topic %s for org %s", id, topicName, orgId)
		errCh <- nil
	}()

	// Close errCh once all workers are done
	go func() {
		wg.Wait()
		close(errCh)
	}()

	// Collect errors from errCh
	var failed bool
	for err := range errCh {
		if err != nil {
			logger.Errorf("Processing error: %v; request: %+v", err, headerDetails)
			failed = true
		}
	}

	// Finalize response
	if !responded {
		if failed {
			response.CreateInternalErrorResponse(w)
		} else {
			response.CreateSuccessResponse("", w)
		}
	}
}

// Parses all of the headers and validates the message-type and gateway-message-version
var parseRequest = func(r *http.Request) (*pubsubdata.HeaderDetails, error) {
	headerDetails := &pubsubdata.HeaderDetails{}
	headerDetails.Host = r.Header.Get("host")
	headerDetails.UserAgent = r.Header.Get("user-agent")
	headerDetails.ContentLength = r.Header.Get("content-length")
	headerDetails.ContentType = r.Header.Get("content-type")
	headerDetails.GatewayDeviceID = r.Header.Get("gateway-device-id")
	headerDetails.MessageVersion = r.Header.Get("message-version")
	headerDetails.MessageType = r.Header.Get("message-type")
	headerDetails.APIKey = r.Header.Get("x-api-key")
	headerDetails.GatewayTimezone = r.Header.Get("tz")

	if headerDetails.GatewayDeviceID == "" {
		return headerDetails, errors.New("header gateway-device-id is not present")
	}

	messageType := MessageType(headerDetails.MessageType)
	if !messageType.IsValid() {
		return headerDetails, fmt.Errorf("invalid message-type %v", headerDetails.MessageType)
	}

	if !isApprovedVersion(headerDetails.MessageVersion, ApprovedMessageVersions) {
		return headerDetails, fmt.Errorf("version %v is not approved", headerDetails.MessageVersion)
	}

	return headerDetails, nil
}

func isApprovedVersion(inputVersion string, ApprovedMessageVersions []string) bool {
	// Parse the input version; semver.NewVersion allows a leading "v".
	inputVer, err := semver.NewVersion(inputVersion)
	if err != nil {
		// The provided version is not a valid semantic version.
		return false
	}

	// Compare against each approved version.
	for _, v := range ApprovedMessageVersions {
		approvedVer, err := semver.NewVersion(v)
		if err != nil {
			logger.Errorf("Error: validating version %v", err)
			return false
		}
		if inputVer.Equal(approvedVer) {
			return true
		}
	}

	return false
}

// Authenticate the api-key
// NOTE: This function duplicates gateway authentication logic found in shared/api/authorizer.ValidateGatewayAuth().
// Key difference: This returns only OrganizationIdentifier (string) while the shared function returns
// GatewayInfo{GatewayID, OrgID}. Future refactoring opportunity: Could use shared function and extract
// just the OrgID, eliminating this duplicated MachineKey + APIKey validation logic.
var authenticateInfo = func(pg connect.DatabaseExecutor, headerDetails *pubsubdata.HeaderDetails) (string, error) {
	query := `
		SELECT 
			o.Id as organizationidentifier
		FROM {{SoftwareGateway}} sg
		LEFT JOIN {{Organization}} o
			ON o.Id = sg.organizationid
		WHERE sg.MachineKey = $1 AND sg.APIKey = $2  AND sg.IsEnabled`
	authData := &dbAuthData{}
	err := pg.QueryRowStruct(authData, query, headerDetails.GatewayDeviceID, headerDetails.APIKey)

	if errors.Is(err, sql.ErrNoRows) {
		// TODO: Add logic for failed login attempt
		logger.Debugf("incremented failed login attempt for Gateway: (%s), err: (%v)", headerDetails.GatewayDeviceID, err)
		return "", fmt.Errorf("%w: %v", ErrFailedLogin, err)
	}

	if err != nil {
		logger.Debugf("error querying gateway table: (%v)", err)
		return "", err
	}

	return authData.OrganizationIdentifier, nil
}
