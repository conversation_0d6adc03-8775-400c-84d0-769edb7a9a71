package brokerShared

import (
	"encoding/json"
	"errors"
	"testing"

	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"

	"github.com/stretchr/testify/assert"
)

func Test_ParseInt64OrUUID(t *testing.T) {
	t.<PERSON>()

	tests := []struct {
		name           string
		deviceIDRaw    json.RawMessage
		expectedOrigID int64
		expectedUUID   string
		expectedError  string
	}{
		{
			name:           "valid_orig_id_integer",
			deviceIDRaw:    json.RawMessage("123"),
			expectedOrigID: 123,
			expectedUUID:   "",
			expectedError:  "",
		},
		{
			name:           "valid_uuid_string",
			deviceIDRaw:    json.RawMessage(`"550e8400-e29b-41d4-a716-************"`),
			expectedOrigID: 0,
			expectedUUID:   "550e8400-e29b-41d4-a716-************",
			expectedError:  "",
		},
		{
			name:           "valid_uuid_uppercase",
			deviceIDRaw:    json.RawMessage(`"550E8400-E29B-41D4-A716-************"`),
			expectedOrigID: 0,
			expectedUUID:   "550e8400-e29b-41d4-a716-************", // Should be normalized to lowercase
			expectedError:  "",
		},
		{
			name:           "valid_orig_id_as_string",
			deviceIDRaw:    json.RawMessage(`"123456"`),
			expectedOrigID: 123456,
			expectedUUID:   "",
			expectedError:  "",
		},
		{
			name:           "empty_device_id",
			deviceIDRaw:    json.RawMessage(""),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID is required",
		},
		{
			name:           "nil_device_id",
			deviceIDRaw:    nil,
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID is required",
		},
		{
			name:           "invalid_uuid_format",
			deviceIDRaw:    json.RawMessage(`"not-a-valid-uuid"`),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID string is neither a valid UUID nor a valid integer",
		},
		{
			name:           "invalid_uuid_missing_hyphens",
			deviceIDRaw:    json.RawMessage(`"550e8400e29b41d4a716************"`),
			expectedOrigID: 0,
			expectedUUID:   "550e8400-e29b-41d4-a716-************",
			expectedError:  "",
		},
		{
			name:           "invalid_json_type",
			deviceIDRaw:    json.RawMessage(`{"invalid": "object"}`),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID must be either an integer (OrigID) or a UUID string",
		},
		{
			name:           "boolean_value",
			deviceIDRaw:    json.RawMessage("true"),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID must be either an integer (OrigID) or a UUID string",
		},
		{
			name:           "array_value",
			deviceIDRaw:    json.RawMessage("[1,2,3]"),
			expectedOrigID: 0,
			expectedUUID:   "",
			expectedError:  "ID must be either an integer (OrigID) or a UUID string",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			origID, uuid, err := ParseInt64OrUUID(tt.deviceIDRaw)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Equal(t, int64(0), origID)
				assert.Equal(t, "", uuid)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedOrigID, origID)
				assert.Equal(t, tt.expectedUUID, uuid)
			}
		})
	}
}

func Test_ValidateDeviceAccess(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                string
		deviceOrigID        int64
		deviceUUID          string
		userPermissions     *authorizer.UserPermissions
		requiredPermissions []string
		mockDBSetup         func() connect.DatabaseExecutor
		expectedDeviceUUID  string
		expectedError       string
		expectError         bool
	}{
		{
			name:         "UUID success with single permission",
			deviceOrigID: 0,
			deviceUUID:   "device-uuid-123",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
							deviceInfo.DeviceID = "device-uuid-123"
							deviceInfo.OrganizationID = "org-123"
							deviceInfo.DeviceGroupIDs = []string{}
							deviceInfo.LocationGroupIDs = []string{}
						}
						return nil
					},
				}
			},
			expectedDeviceUUID: "device-uuid-123",
			expectedError:      "",
			expectError:        false,
		},
		{
			name:         "UUID database error",
			deviceOrigID: 0,
			deviceUUID:   "device-uuid-error",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return errors.New("database connection failed")
					},
				}
			},
			expectedDeviceUUID: "",
			expectedError:      "failed to check device access by UUID",
			expectError:        true,
		},
		{
			name:         "UUID unauthorized access",
			deviceOrigID: 0,
			deviceUUID:   "device-uuid-unauthorized",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Don't populate the struct to simulate no access
						return nil
					},
				}
			},
			expectedDeviceUUID: "",
			expectedError:      "user does not have permission to manage device device-uuid-unauthorized",
			expectError:        true,
		},
		{
			name:         "OrigID success",
			deviceOrigID: 456,
			deviceUUID:   "",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
							deviceInfo.DeviceID = "device-uuid-456"
							deviceInfo.OrganizationID = "org-123"
							deviceInfo.DeviceGroupIDs = []string{}
							deviceInfo.LocationGroupIDs = []string{}
						}
						return nil
					},
				}
			},
			expectedDeviceUUID: "device-uuid-456",
			expectedError:      "",
			expectError:        false,
		},
		{
			name:         "OrigID database error",
			deviceOrigID: 999,
			deviceUUID:   "",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return errors.New("database timeout")
					},
				}
			},
			expectedDeviceUUID: "",
			expectedError:      "failed to check device access by OrigID",
			expectError:        true,
		},
		{
			name:         "OrigID unauthorized access",
			deviceOrigID: 888,
			deviceUUID:   "",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						// Don't populate the struct to simulate no access
						return nil
					},
				}
			},
			expectedDeviceUUID: "",
			expectedError:      "user does not have permission to manage device 888",
			expectError:        true,
		},
		{
			name:         "Multiple permissions success",
			deviceOrigID: 0,
			deviceUUID:   "device-uuid-789",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices", "org_manage_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices", "org_manage_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
							deviceInfo.DeviceID = "device-uuid-789"
							deviceInfo.OrganizationID = "org-123"
							deviceInfo.DeviceGroupIDs = []string{}
							deviceInfo.LocationGroupIDs = []string{}
						}
						return nil
					},
				}
			},
			expectedDeviceUUID: "device-uuid-789",
			expectedError:      "",
			expectError:        false,
		},
		{
			name:         "Empty permissions success",
			deviceOrigID: 0,
			deviceUUID:   "device-uuid-999",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"}, // Give user some permissions
					},
				},
			},
			requiredPermissions: []string{}, // But require no permissions
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						if deviceInfo, ok := dest.(*authorizer.DeviceInfo); ok {
							deviceInfo.DeviceID = "device-uuid-999"
							deviceInfo.OrganizationID = "org-123"
							deviceInfo.DeviceGroupIDs = []string{}
							deviceInfo.LocationGroupIDs = []string{}
						}
						return nil
					},
				}
			},
			expectedDeviceUUID: "device-uuid-999",
			expectedError:      "",
			expectError:        false,
		},
		{
			name:                "Nil user permissions",
			deviceOrigID:        0,
			deviceUUID:          "device-uuid-123",
			userPermissions:     nil,
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{}
			},
			expectedDeviceUUID: "",
			expectedError:      "user permissions cannot be nil",
			expectError:        true,
		},
		{
			name:         "Empty UUID and zero OrigID",
			deviceOrigID: 0,
			deviceUUID:   "",
			userPermissions: &authorizer.UserPermissions{
				UserID: "test-user",
				Permissions: []authorizer.Permission{
					{
						Scope:          "org",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			mockDBSetup: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						return errors.New("database error")
					},
				}
			},
			expectedDeviceUUID: "",
			expectedError:      "failed to check device access by OrigID",
			expectError:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			fakeDB := tt.mockDBSetup()
			deviceUUID, err := ValidateDeviceAccess(fakeDB, tt.userPermissions, tt.deviceOrigID, tt.deviceUUID, tt.requiredPermissions...)

			if tt.expectError {
				assert.Error(t, err)
				if tt.expectedError != "" {
					assert.Contains(t, err.Error(), tt.expectedError)
				}
			} else {
				assert.NoError(t, err)
			}
			assert.Equal(t, tt.expectedDeviceUUID, deviceUUID)
		})
	}
}
