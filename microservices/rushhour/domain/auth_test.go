package domain

import (
	"testing"
	"time"

	"github.com/zishang520/socket.io/v2/socket"
	"synapse-its.com/shared/api/authorizer"
)

func TestClientType_String(t *testing.T) {
	tests := []struct {
		clientType ClientType
		expected   string
	}{
		{ClientTypeGateway, "gateway"},
		{ClientTypeFSA, "fsa"},
		{ClientTypeOnramp, "onramp"},
		{ClientTypeUnknown, "unknown"},
		{ClientType(99), "unknown"}, // Test invalid value
	}

	for _, test := range tests {
		result := test.clientType.String()
		if result != test.expected {
			t.Errorf("ClientType(%d).String() = %s, expected %s", test.clientType, result, test.expected)
		}
	}
}

func TestAuthInfo_GetUserID(t *testing.T) {
	tests := []struct {
		name     string
		authInfo *AuthInfo
		expected string
	}{
		{
			name: "Gateway client returns GatewayID",
			authInfo: &AuthInfo{
				ClientType: ClientTypeGateway,
				GatewayID:  "gateway123",
				UserID:     "user456", // Should be ignored
			},
			expected: "gateway123",
		},
		{
			name: "FSA client returns UserID",
			authInfo: &AuthInfo{
				ClientType: ClientTypeFSA,
				UserID:     "user123",
				GatewayID:  "gateway456", // Should be ignored
			},
			expected: "user123",
		},
		{
			name: "Onramp client returns UserID",
			authInfo: &AuthInfo{
				ClientType: ClientTypeOnramp,
				UserID:     "user789",
				GatewayID:  "gateway456", // Should be ignored
			},
			expected: "user789",
		},
		{
			name: "Unknown client returns empty string",
			authInfo: &AuthInfo{
				ClientType: ClientTypeUnknown,
				UserID:     "user123",
				GatewayID:  "gateway456",
			},
			expected: "",
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := test.authInfo.GetUserID()
			if result != test.expected {
				t.Errorf("GetUserID() = %s, expected %s", result, test.expected)
			}
		})
	}
}

func TestGetDeviceStreamChannel(t *testing.T) {
	tests := []struct {
		orgID      string
		deviceID   string
		streamType string
		expected   string
	}{
		{
			orgID:      "org123",
			deviceID:   "device456",
			streamType: StreamTypeDisplay,
			expected:   "org:org123:device:device456:stream_display",
		},
		{
			orgID:      "org789",
			deviceID:   "device111",
			streamType: StreamTypeRMS,
			expected:   "org:org789:device:device111:stream_rms",
		},
		{
			orgID:      "special-org",
			deviceID:   "special-device",
			streamType: "custom_stream",
			expected:   "org:special-org:device:special-device:custom_stream",
		},
	}

	for _, test := range tests {
		result := GetDeviceStreamChannel(test.orgID, test.deviceID, test.streamType)
		if result != test.expected {
			t.Errorf("GetDeviceStreamChannel(%s, %s, %s) = %s, expected %s",
				test.orgID, test.deviceID, test.streamType, result, test.expected)
		}
	}
}

func TestParseChannelInfo(t *testing.T) {
	tests := []struct {
		name     string
		channel  string
		expected *ChannelInfo
	}{
		{
			name:    "Valid device channel",
			channel: "org:org123:device:device456:stream_display",
			expected: &ChannelInfo{
				Type:       "device",
				OrgID:      "org123",
				EntityID:   "device456",
				EntityType: "device",
				Subtype:    "stream_display",
			},
		},
		{
			name:    "Valid gateway channel",
			channel: "org:org789:gateway:gateway111:control",
			expected: &ChannelInfo{
				Type:       "gateway",
				OrgID:      "org789",
				EntityID:   "gateway111",
				EntityType: "gateway",
				Subtype:    "control",
			},
		},
		{
			name:    "Valid device channel with multi-part subtype",
			channel: "org:org123:device:device456:stream_display:high_quality",
			expected: &ChannelInfo{
				Type:       "device",
				OrgID:      "org123",
				EntityID:   "device456",
				EntityType: "device",
				Subtype:    "stream_display:high_quality",
			},
		},
		{
			name:    "Valid user channel",
			channel: "user:user123:private",
			expected: &ChannelInfo{
				Type:       "user",
				EntityID:   "user123",
				EntityType: "user",
				Subtype:    "private",
			},
		},
		{
			name:    "Invalid channel - too few parts",
			channel: "org:org123",
			expected: &ChannelInfo{
				Type: "unknown",
			},
		},
		{
			name:    "Invalid channel - unknown prefix",
			channel: "invalid:org123:device:device456:control",
			expected: &ChannelInfo{
				Type: "unknown",
			},
		},
		{
			name:    "Invalid org channel - too few parts",
			channel: "org:org123:device",
			expected: &ChannelInfo{
				Type: "unknown",
			},
		},
		{
			name:    "Invalid user channel - too few parts",
			channel: "user:user123",
			expected: &ChannelInfo{
				Type: "unknown",
			},
		},
		{
			name:    "Empty channel",
			channel: "",
			expected: &ChannelInfo{
				Type: "unknown",
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := ParseChannelInfo(test.channel)

			if result.Type != test.expected.Type {
				t.Errorf("Type = %s, expected %s", result.Type, test.expected.Type)
			}

			if result.OrgID != test.expected.OrgID {
				t.Errorf("OrgID = %s, expected %s", result.OrgID, test.expected.OrgID)
			}

			if result.EntityID != test.expected.EntityID {
				t.Errorf("EntityID = %s, expected %s", result.EntityID, test.expected.EntityID)
			}

			if result.EntityType != test.expected.EntityType {
				t.Errorf("EntityType = %s, expected %s", result.EntityType, test.expected.EntityType)
			}

			if result.Subtype != test.expected.Subtype {
				t.Errorf("Subtype = %s, expected %s", result.Subtype, test.expected.Subtype)
			}
		})
	}
}

func TestConnectionContext_Creation(t *testing.T) {
	// Test that ConnectionContext can be created with all required fields
	socketID := "socket123"
	clientType := ClientTypeFSA
	orgID := "org456"
	connectedAt := time.Now()

	authInfo := &AuthInfo{
		ClientType:  clientType,
		UserID:      "user123",
		OrgID:       orgID,
		Permissions: &authorizer.UserPermissions{},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	ctx := &ConnectionContext{
		SocketID:    socket.SocketId("socket123"),
		ClientType:  clientType,
		AuthInfo:    authInfo,
		OrgID:       orgID,
		ConnectedAt: connectedAt,
	}

	if string(ctx.SocketID) != socketID {
		t.Errorf("SocketID = %s, expected %s", string(ctx.SocketID), socketID)
	}

	if ctx.ClientType != clientType {
		t.Errorf("ClientType = %v, expected %v", ctx.ClientType, clientType)
	}

	if ctx.AuthInfo != authInfo {
		t.Error("AuthInfo reference not preserved")
	}

	if ctx.OrgID != orgID {
		t.Errorf("OrgID = %s, expected %s", ctx.OrgID, orgID)
	}

	if ctx.ConnectedAt != connectedAt {
		t.Error("ConnectedAt timestamp not preserved")
	}
}

func TestAuthInfo_Creation(t *testing.T) {
	// Test FSA/Onramp AuthInfo
	userID := "user123"
	orgID := "org456"
	permissions := &authorizer.UserPermissions{}
	expiresAt := time.Now().Add(time.Hour)

	fsaAuth := &AuthInfo{
		ClientType:  ClientTypeFSA,
		UserID:      userID,
		OrgID:       orgID,
		Permissions: permissions,
		ExpiresAt:   expiresAt,
	}

	if fsaAuth.ClientType != ClientTypeFSA {
		t.Errorf("ClientType = %v, expected %v", fsaAuth.ClientType, ClientTypeFSA)
	}

	if fsaAuth.UserID != userID {
		t.Errorf("UserID = %s, expected %s", fsaAuth.UserID, userID)
	}

	if fsaAuth.OrgID != orgID {
		t.Errorf("OrgID = %s, expected %s", fsaAuth.OrgID, orgID)
	}

	if fsaAuth.Permissions != permissions {
		t.Error("Permissions reference not preserved")
	}

	if fsaAuth.ExpiresAt != expiresAt {
		t.Error("ExpiresAt timestamp not preserved")
	}

	// Test Gateway AuthInfo
	gatewayID := "gateway789"

	gatewayAuth := &AuthInfo{
		ClientType: ClientTypeGateway,
		GatewayID:  gatewayID,
		OrgID:      orgID,
	}

	if gatewayAuth.ClientType != ClientTypeGateway {
		t.Errorf("ClientType = %v, expected %v", gatewayAuth.ClientType, ClientTypeGateway)
	}

	if gatewayAuth.GatewayID != gatewayID {
		t.Errorf("GatewayID = %s, expected %s", gatewayAuth.GatewayID, gatewayID)
	}

	if gatewayAuth.OrgID != orgID {
		t.Errorf("OrgID = %s, expected %s", gatewayAuth.OrgID, orgID)
	}
}

func TestJoinPayload_Creation(t *testing.T) {
	room := "org:org123:device:device456:stream_display"

	payload := &JoinPayload{
		Room: room,
	}

	if payload.Room != room {
		t.Errorf("Room = %s, expected %s", payload.Room, room)
	}
}

func TestConstants(t *testing.T) {
	// Test stream type constants
	if StreamTypeDisplay != "stream_display" {
		t.Errorf("StreamTypeDisplay = %s, expected stream_display", StreamTypeDisplay)
	}

	if StreamTypeRMS != "stream_rms" {
		t.Errorf("StreamTypeRMS = %s, expected stream_rms", StreamTypeRMS)
	}

	// Test namespace constants
	if NamespaceGateway != "/auth/gateway" {
		t.Errorf("NamespaceGateway = %s, expected /auth/gateway", NamespaceGateway)
	}

	if NamespaceFSA != "/auth/fsa" {
		t.Errorf("NamespaceFSA = %s, expected /auth/fsa", NamespaceFSA)
	}
}
