package domain

import (
	"testing"

	rushhourv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/rushhour/v1"
)

func TestEnvelopeTypeConstants(t *testing.T) {
	// Test that envelope type constants are properly mapped
	tests := []struct {
		name     string
		domain   EnvelopeType
		protobuf rushhourv1.EnvelopeType
	}{
		{"Unknown", EnvelopeTypeUnknown, rushhourv1.EnvelopeType_ENVELOPE_UNKNOWN},
		{"WrapperCommand", EnvelopeTypeWrapperCommand, rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND},
		{"WrapperResponse", EnvelopeTypeWrapperResponse, rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_RESPONSE},
		{"CommandJSON", EnvelopeTypeCommandJSON, rushhourv1.EnvelopeType_ENVELOPE_COMMAND_JSON},
		{"CommandProtobuf", EnvelopeTypeCommandProtobuf, rushhourv1.EnvelopeType_ENVELOPE_COMMAND_PROTOBUF},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if test.domain != test.protobuf {
				t.Errorf("Domain constant %v does not match protobuf constant %v", test.domain, test.protobuf)
			}
		})
	}
}

func TestOriginTypeConstants(t *testing.T) {
	// Test that origin type constants are properly mapped
	tests := []struct {
		name     string
		domain   OriginType
		protobuf rushhourv1.OriginType
	}{
		{"Unknown", OriginTypeUnknown, rushhourv1.OriginType_ORIGIN_UNKNOWN},
		{"Gateway", OriginTypeGateway, rushhourv1.OriginType_ORIGIN_GATEWAY},
		{"FSA", OriginTypeFSA, rushhourv1.OriginType_ORIGIN_FSA},
		{"Rushhour", OriginTypeRushhour, rushhourv1.OriginType_ORIGIN_RUSHHOUR},
		{"Onramp", OriginTypeOnramp, rushhourv1.OriginType_ORIGIN_ONRAMP},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if test.domain != test.protobuf {
				t.Errorf("Domain constant %v does not match protobuf constant %v", test.domain, test.protobuf)
			}
		})
	}
}

func TestLegacyConstants(t *testing.T) {
	// Test backward compatibility constants
	tests := []struct {
		name   string
		legacy OriginType
		new    OriginType
	}{
		{"App to FSA", OriginTypeApp, OriginTypeFSA},
		{"RH to Rushhour", OriginTypeRH, OriginTypeRushhour},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			if test.legacy != test.new {
				t.Errorf("Legacy constant %v does not match new constant %v", test.legacy, test.new)
			}
		})
	}
}

func TestTypeAliases(t *testing.T) {
	// Test that type aliases are properly defined

	// Test EnvelopeType alias
	var envType EnvelopeType = rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND
	if envType != rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND {
		t.Error("EnvelopeType alias is not working correctly")
	}

	// Test OriginType alias
	var originType OriginType = rushhourv1.OriginType_ORIGIN_GATEWAY
	if originType != rushhourv1.OriginType_ORIGIN_GATEWAY {
		t.Error("OriginType alias is not working correctly")
	}

	// Test SocketEnvelope alias
	var envelope SocketEnvelope = rushhourv1.SocketEnvelope{
		Type:   rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND,
		Origin: rushhourv1.OriginType_ORIGIN_FSA,
	}

	if envelope.Type != rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND {
		t.Error("SocketEnvelope alias is not working correctly")
	}

	if envelope.Origin != rushhourv1.OriginType_ORIGIN_FSA {
		t.Error("SocketEnvelope alias is not working correctly")
	}
}

func TestEnvelopeTypeValues(t *testing.T) {
	// Test specific integer values to ensure consistency
	expectedValues := map[EnvelopeType]int32{
		EnvelopeTypeUnknown:         0,
		EnvelopeTypeWrapperCommand:  1,
		EnvelopeTypeWrapperResponse: 2,
		EnvelopeTypeCommandJSON:     3,
		EnvelopeTypeCommandProtobuf: 4,
	}

	for envType, expectedValue := range expectedValues {
		if int32(envType) != expectedValue {
			t.Errorf("EnvelopeType %v has value %d, expected %d", envType, int32(envType), expectedValue)
		}
	}
}

func TestOriginTypeValues(t *testing.T) {
	// Test specific integer values to ensure consistency
	expectedValues := map[OriginType]int32{
		OriginTypeUnknown:  0,
		OriginTypeGateway:  1,
		OriginTypeFSA:      2,
		OriginTypeRushhour: 3,
		OriginTypeOnramp:   4,
	}

	for originType, expectedValue := range expectedValues {
		if int32(originType) != expectedValue {
			t.Errorf("OriginType %v has value %d, expected %d", originType, int32(originType), expectedValue)
		}
	}
}

func TestEnvelopeCreation(t *testing.T) {
	// Test creating a SocketEnvelope using domain constants
	envelope := &SocketEnvelope{
		Type:           EnvelopeTypeWrapperCommand,
		Origin:         OriginTypeFSA,
		OrganizationId: "org123",
		DeviceId:       "device456",
		Payload:        []byte("test payload"),
	}

	if envelope.Type != EnvelopeTypeWrapperCommand {
		t.Errorf("Envelope Type = %v, expected %v", envelope.Type, EnvelopeTypeWrapperCommand)
	}

	if envelope.Origin != OriginTypeFSA {
		t.Errorf("Envelope Origin = %v, expected %v", envelope.Origin, OriginTypeFSA)
	}

	if envelope.OrganizationId != "org123" {
		t.Errorf("OrganizationId = %s, expected org123", envelope.OrganizationId)
	}

	if envelope.DeviceId != "device456" {
		t.Errorf("DeviceId = %s, expected device456", envelope.DeviceId)
	}

	if string(envelope.Payload) != "test payload" {
		t.Errorf("Payload = %s, expected test payload", string(envelope.Payload))
	}
}

func TestLegacyUsage(t *testing.T) {
	// Test that legacy constants can be used interchangeably

	// Using legacy App constant
	envelope1 := &SocketEnvelope{
		Type:   EnvelopeTypeCommandJSON,
		Origin: OriginTypeApp, // Legacy constant
	}

	// Using new FSA constant
	envelope2 := &SocketEnvelope{
		Type:   EnvelopeTypeCommandJSON,
		Origin: OriginTypeFSA, // New constant
	}

	if envelope1.Origin != envelope2.Origin {
		t.Error("Legacy OriginTypeApp should be equivalent to OriginTypeFSA")
	}

	// Using legacy RH constant
	envelope3 := &SocketEnvelope{
		Type:   EnvelopeTypeWrapperResponse,
		Origin: OriginTypeRH, // Legacy constant
	}

	// Using new Rushhour constant
	envelope4 := &SocketEnvelope{
		Type:   EnvelopeTypeWrapperResponse,
		Origin: OriginTypeRushhour, // New constant
	}

	if envelope3.Origin != envelope4.Origin {
		t.Error("Legacy OriginTypeRH should be equivalent to OriginTypeRushhour")
	}
}

func TestConstantCoverage(t *testing.T) {
	// Ensure all protobuf enum values are covered by domain constants

	// Check EnvelopeType coverage
	envelopeTypes := []EnvelopeType{
		EnvelopeTypeUnknown,
		EnvelopeTypeWrapperCommand,
		EnvelopeTypeWrapperResponse,
		EnvelopeTypeCommandJSON,
		EnvelopeTypeCommandProtobuf,
	}

	if len(envelopeTypes) == 0 {
		t.Error("No envelope types defined")
	}

	// Check OriginType coverage
	originTypes := []OriginType{
		OriginTypeUnknown,
		OriginTypeGateway,
		OriginTypeFSA,
		OriginTypeRushhour,
		OriginTypeOnramp,
	}

	if len(originTypes) == 0 {
		t.Error("No origin types defined")
	}

	// Verify legacy constants exist
	legacyTypes := []OriginType{
		OriginTypeApp,
		OriginTypeRH,
	}

	if len(legacyTypes) != 2 {
		t.Error("Expected exactly 2 legacy constants")
	}
}
