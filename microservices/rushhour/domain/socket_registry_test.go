package domain

import (
	"testing"
	"time"

	"github.com/zishang520/socket.io/v2/socket"
	"synapse-its.com/shared/api/authorizer"
)

func TestNewSocketRegistry(t *testing.T) {
	registry := NewSocketRegistry()

	if registry == nil {
		t.Fatal("Expected non-nil socket registry")
	}

	if registry.socketToContext == nil {
		t.Error("Expected socketToContext map to be initialized")
	}

	if registry.userToSockets == nil {
		t.Error("Expected userToSockets map to be initialized")
	}
}

func TestSocketRegistry_RegisterSocket(t *testing.T) {
	registry := NewSocketRegistry()
	socketID := "socket123"

	// Test FSA client registration
	authInfo := &AuthInfo{
		ClientType:  ClientTypeFSA,
		UserID:      "user123",
		OrgID:       "org456",
		Permissions: &authorizer.UserPermissions{},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	ctx := &ConnectionContext{
		SocketID:    socket.SocketId(socketID),
		ClientType:  ClientTypeFSA,
		AuthInfo:    authInfo,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket(socketID, ctx)

	// Verify socket is registered
	retrievedCtx, exists := registry.GetContext(socketID)
	if !exists {
		t.Error("Expected socket to be registered")
	}

	if retrievedCtx != ctx {
		t.Error("Expected retrieved context to match registered context")
	}

	// Verify user mapping for FSA client
	userSockets := registry.GetUserSockets("user123")
	if len(userSockets) != 1 || userSockets[0] != socketID {
		t.Errorf("Expected user to have one socket %s, got %v", socketID, userSockets)
	}
}

func TestSocketRegistry_RegisterSocket_Gateway(t *testing.T) {
	registry := NewSocketRegistry()
	socketID := "gateway_socket123"

	// Test Gateway client registration
	authInfo := &AuthInfo{
		ClientType: ClientTypeGateway,
		GatewayID:  "gateway123",
		OrgID:      "org456",
	}

	ctx := &ConnectionContext{
		SocketID:    socket.SocketId(socketID),
		ClientType:  ClientTypeGateway,
		AuthInfo:    authInfo,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket(socketID, ctx)

	// Verify socket is registered
	retrievedCtx, exists := registry.GetContext(socketID)
	if !exists {
		t.Error("Expected socket to be registered")
	}

	if retrievedCtx.AuthInfo.GatewayID != "gateway123" {
		t.Error("Expected gateway ID to be set correctly")
	}

	// Gateway clients should not appear in user mappings
	userSockets := registry.GetUserSockets("gateway123")
	if len(userSockets) != 0 {
		t.Error("Expected gateway sockets not to appear in user mappings")
	}
}

func TestSocketRegistry_RegisterSocket_MultipleUserSockets(t *testing.T) {
	registry := NewSocketRegistry()
	userID := "user123"

	// Register multiple sockets for the same user
	for i := 0; i < 3; i++ {
		socketID := "socket" + string(rune('1'+i))

		authInfo := &AuthInfo{
			ClientType:  ClientTypeFSA,
			UserID:      userID,
			OrgID:       "org456",
			Permissions: &authorizer.UserPermissions{},
			ExpiresAt:   time.Now().Add(time.Hour),
		}

		ctx := &ConnectionContext{
			SocketID:    socket.SocketId(socketID),
			ClientType:  ClientTypeFSA,
			AuthInfo:    authInfo,
			OrgID:       "org456",
			ConnectedAt: time.Now(),
		}

		registry.RegisterSocket(socketID, ctx)
	}

	// Verify user has all sockets
	userSockets := registry.GetUserSockets(userID)
	if len(userSockets) != 3 {
		t.Errorf("Expected user to have 3 sockets, got %d", len(userSockets))
	}

	expectedSockets := map[string]bool{"socket1": true, "socket2": true, "socket3": true}
	for _, socketID := range userSockets {
		if !expectedSockets[socketID] {
			t.Errorf("Unexpected socket ID: %s", socketID)
		}
	}
}

func TestSocketRegistry_UnregisterSocket(t *testing.T) {
	registry := NewSocketRegistry()
	socketID := "socket123"
	userID := "user123"

	// Register a socket
	authInfo := &AuthInfo{
		ClientType:  ClientTypeFSA,
		UserID:      userID,
		OrgID:       "org456",
		Permissions: &authorizer.UserPermissions{},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	ctx := &ConnectionContext{
		SocketID:    socket.SocketId(socketID),
		ClientType:  ClientTypeFSA,
		AuthInfo:    authInfo,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket(socketID, ctx)

	// Unregister the socket
	unregisteredCtx := registry.UnregisterSocket(socketID)

	if unregisteredCtx != ctx {
		t.Error("Expected unregistered context to match original context")
	}

	// Verify socket is no longer registered
	_, exists := registry.GetContext(socketID)
	if exists {
		t.Error("Expected socket to be unregistered")
	}

	// Verify user mapping is cleaned up
	userSockets := registry.GetUserSockets(userID)
	if len(userSockets) != 0 {
		t.Error("Expected user socket mapping to be cleaned up")
	}
}

func TestSocketRegistry_UnregisterSocket_NonExistent(t *testing.T) {
	registry := NewSocketRegistry()

	// Try to unregister a non-existent socket
	ctx := registry.UnregisterSocket("nonexistent")

	if ctx != nil {
		t.Error("Expected nil context for non-existent socket")
	}
}

func TestSocketRegistry_UnregisterSocket_PartialUserCleanup(t *testing.T) {
	registry := NewSocketRegistry()
	userID := "user123"
	socket1 := "socket1"
	socket2 := "socket2"

	// Register two sockets for the same user
	for _, socketID := range []string{socket1, socket2} {
		authInfo := &AuthInfo{
			ClientType:  ClientTypeFSA,
			UserID:      userID,
			OrgID:       "org456",
			Permissions: &authorizer.UserPermissions{},
			ExpiresAt:   time.Now().Add(time.Hour),
		}

		ctx := &ConnectionContext{
			SocketID:    socket.SocketId(socketID),
			ClientType:  ClientTypeFSA,
			AuthInfo:    authInfo,
			OrgID:       "org456",
			ConnectedAt: time.Now(),
		}

		registry.RegisterSocket(socketID, ctx)
	}

	// Unregister one socket
	registry.UnregisterSocket(socket1)

	// Verify user still has the other socket
	userSockets := registry.GetUserSockets(userID)
	if len(userSockets) != 1 || userSockets[0] != socket2 {
		t.Errorf("Expected user to have one socket %s, got %v", socket2, userSockets)
	}

	// Unregister the last socket
	registry.UnregisterSocket(socket2)

	// Verify user mapping is completely cleaned up
	userSockets = registry.GetUserSockets(userID)
	if len(userSockets) != 0 {
		t.Error("Expected user socket mapping to be completely cleaned up")
	}
}

func TestSocketRegistry_GetContext(t *testing.T) {
	registry := NewSocketRegistry()
	socketID := "socket123"

	// Test non-existent socket
	_, exists := registry.GetContext(socketID)
	if exists {
		t.Error("Expected non-existent socket to return false")
	}

	// Register a socket
	authInfo := &AuthInfo{
		ClientType: ClientTypeGateway,
		GatewayID:  "gateway123",
		OrgID:      "org456",
	}

	ctx := &ConnectionContext{
		SocketID:    socket.SocketId(socketID),
		ClientType:  ClientTypeGateway,
		AuthInfo:    authInfo,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket(socketID, ctx)

	// Test existing socket
	retrievedCtx, exists := registry.GetContext(socketID)
	if !exists {
		t.Error("Expected existing socket to return true")
	}

	if retrievedCtx != ctx {
		t.Error("Expected retrieved context to match registered context")
	}
}

func TestSocketRegistry_GetUserSockets(t *testing.T) {
	registry := NewSocketRegistry()

	// Test non-existent user
	sockets := registry.GetUserSockets("nonexistent")
	if len(sockets) != 0 {
		t.Error("Expected empty slice for non-existent user")
	}

	// The slice should be a copy (test for race condition protection)
	userID := "user123"
	socketID := "socket123"

	authInfo := &AuthInfo{
		ClientType:  ClientTypeFSA,
		UserID:      userID,
		OrgID:       "org456",
		Permissions: &authorizer.UserPermissions{},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	ctx := &ConnectionContext{
		SocketID:    socket.SocketId(socketID),
		ClientType:  ClientTypeFSA,
		AuthInfo:    authInfo,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket(socketID, ctx)

	sockets1 := registry.GetUserSockets(userID)
	sockets2 := registry.GetUserSockets(userID)

	// Should be different slice instances
	if &sockets1[0] == &sockets2[0] {
		t.Error("Expected GetUserSockets to return a copy, not the same slice")
	}
}

func TestSocketRegistry_GetStats(t *testing.T) {
	registry := NewSocketRegistry()

	// Test empty registry
	stats := registry.GetStats()
	if stats["total_sockets"] != 0 {
		t.Errorf("Expected 0 total sockets, got %d", stats["total_sockets"])
	}
	if stats["unique_users"] != 0 {
		t.Errorf("Expected 0 unique users, got %d", stats["unique_users"])
	}

	// Add some sockets
	userID1 := "user1"
	userID2 := "user2"

	// User1 with 2 sockets
	for i := 0; i < 2; i++ {
		socketID := "user1_socket" + string(rune('1'+i))
		authInfo := &AuthInfo{
			ClientType:  ClientTypeFSA,
			UserID:      userID1,
			OrgID:       "org456",
			Permissions: &authorizer.UserPermissions{},
			ExpiresAt:   time.Now().Add(time.Hour),
		}

		ctx := &ConnectionContext{
			SocketID:    socket.SocketId(socketID),
			ClientType:  ClientTypeFSA,
			AuthInfo:    authInfo,
			OrgID:       "org456",
			ConnectedAt: time.Now(),
		}

		registry.RegisterSocket(socketID, ctx)
	}

	// User2 with 1 socket
	socketID := "user2_socket1"
	authInfo := &AuthInfo{
		ClientType:  ClientTypeOnramp,
		UserID:      userID2,
		OrgID:       "org789",
		Permissions: &authorizer.UserPermissions{},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	ctx := &ConnectionContext{
		SocketID:    socket.SocketId(socketID),
		ClientType:  ClientTypeOnramp,
		AuthInfo:    authInfo,
		OrgID:       "org789",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket(socketID, ctx)

	// Add a gateway (should not count as user)
	gatewaySocketID := "gateway_socket1"
	gatewayAuth := &AuthInfo{
		ClientType: ClientTypeGateway,
		GatewayID:  "gateway123",
		OrgID:      "org456",
	}

	gatewayCtx := &ConnectionContext{
		SocketID:    socket.SocketId(gatewaySocketID),
		ClientType:  ClientTypeGateway,
		AuthInfo:    gatewayAuth,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket(gatewaySocketID, gatewayCtx)

	// Check stats
	stats = registry.GetStats()
	if stats["total_sockets"] != 4 {
		t.Errorf("Expected 4 total sockets, got %d", stats["total_sockets"])
	}
	if stats["unique_users"] != 2 {
		t.Errorf("Expected 2 unique users, got %d", stats["unique_users"])
	}
}

func TestSocketRegistry_ListConnectedSockets(t *testing.T) {
	registry := NewSocketRegistry()

	// Test empty registry
	sockets := registry.ListConnectedSockets()

	if len(sockets[ClientTypeGateway]) != 0 {
		t.Error("Expected no gateway sockets")
	}
	if len(sockets[ClientTypeFSA]) != 0 {
		t.Error("Expected no FSA sockets")
	}
	if len(sockets[ClientTypeOnramp]) != 0 {
		t.Error("Expected no onramp sockets")
	}

	// Add sockets of different types
	// Gateway socket
	gatewayAuth := &AuthInfo{
		ClientType: ClientTypeGateway,
		GatewayID:  "gateway123",
		OrgID:      "org456",
	}

	gatewayCtx := &ConnectionContext{
		SocketID:    "gateway_socket1",
		ClientType:  ClientTypeGateway,
		AuthInfo:    gatewayAuth,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket("gateway_socket1", gatewayCtx)

	// FSA socket
	fsaAuth := &AuthInfo{
		ClientType:  ClientTypeFSA,
		UserID:      "user123",
		OrgID:       "org456",
		Permissions: &authorizer.UserPermissions{},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	fsaCtx := &ConnectionContext{
		SocketID:    "fsa_socket1",
		ClientType:  ClientTypeFSA,
		AuthInfo:    fsaAuth,
		OrgID:       "org456",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket("fsa_socket1", fsaCtx)

	// Onramp socket
	onrampAuth := &AuthInfo{
		ClientType:  ClientTypeOnramp,
		UserID:      "user456",
		OrgID:       "org789",
		Permissions: &authorizer.UserPermissions{},
		ExpiresAt:   time.Now().Add(time.Hour),
	}

	onrampCtx := &ConnectionContext{
		SocketID:    "onramp_socket1",
		ClientType:  ClientTypeOnramp,
		AuthInfo:    onrampAuth,
		OrgID:       "org789",
		ConnectedAt: time.Now(),
	}

	registry.RegisterSocket("onramp_socket1", onrampCtx)

	// Check results
	sockets = registry.ListConnectedSockets()

	if len(sockets[ClientTypeGateway]) != 1 || sockets[ClientTypeGateway][0] != "gateway_socket1" {
		t.Errorf("Expected one gateway socket, got %v", sockets[ClientTypeGateway])
	}

	if len(sockets[ClientTypeFSA]) != 1 || sockets[ClientTypeFSA][0] != "fsa_socket1" {
		t.Errorf("Expected one FSA socket, got %v", sockets[ClientTypeFSA])
	}

	if len(sockets[ClientTypeOnramp]) != 1 || sockets[ClientTypeOnramp][0] != "onramp_socket1" {
		t.Errorf("Expected one onramp socket, got %v", sockets[ClientTypeOnramp])
	}
}

// TestSocketRegistry_ConcurrentAccess tests thread safety
func TestSocketRegistry_ConcurrentAccess(t *testing.T) {
	registry := NewSocketRegistry()
	done := make(chan bool)

	// Simulate concurrent registration and unregistration
	go func() {
		for i := 0; i < 100; i++ {
			socketID := "socket" + string(rune(i%10+'0'))
			authInfo := &AuthInfo{
				ClientType:  ClientTypeFSA,
				UserID:      "user" + string(rune(i%5+'0')),
				OrgID:       "org456",
				Permissions: &authorizer.UserPermissions{},
				ExpiresAt:   time.Now().Add(time.Hour),
			}

			ctx := &ConnectionContext{
				SocketID:    socket.SocketId(socketID),
				ClientType:  ClientTypeFSA,
				AuthInfo:    authInfo,
				OrgID:       "org456",
				ConnectedAt: time.Now(),
			}

			registry.RegisterSocket(socketID, ctx)
		}
		done <- true
	}()

	go func() {
		for i := 0; i < 100; i++ {
			socketID := "socket" + string(rune(i%10+'0'))
			registry.UnregisterSocket(socketID)
		}
		done <- true
	}()

	go func() {
		for i := 0; i < 100; i++ {
			registry.GetStats()
			registry.ListConnectedSockets()
		}
		done <- true
	}()

	// Wait for all goroutines to complete
	for i := 0; i < 3; i++ {
		<-done
	}

	// Test should complete without data races or panics
}
