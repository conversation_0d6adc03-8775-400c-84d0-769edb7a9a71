package domain

import "time"

// Room represents a Socket.io room where clients can join and communicate
type Room struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	CreatedAt   time.Time `json:"createdAt"`
	MemberCount int       `json:"memberCount"`
}

// User represents a connected Socket.io client
type User struct {
	SocketID string    `json:"socketId"`
	UserID   string    `json:"userId,omitempty"`
	Name     string    `json:"name,omitempty"`
	JoinedAt time.Time `json:"joinedAt"`
}

// Event represents a Socket.io event
type Event struct {
	Type      string                 `json:"type"`
	RoomID    string                 `json:"roomId,omitempty"`
	UserID    string                 `json:"userId"`
	Data      map[string]interface{} `json:"data,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// Message represents a chat message in a room
type Message struct {
	ID        string                 `json:"id"`
	RoomID    string                 `json:"roomId"`
	UserID    string                 `json:"userId"`
	Content   string                 `json:"content"`
	Type      string                 `json:"type"` // text, image, file, etc.
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// StatusUpdate represents a status change event
type StatusUpdate struct {
	RoomID    string                 `json:"roomId"`
	UserID    string                 `json:"userId"`
	Status    string                 `json:"status"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// Connection events
const (
	EventTypeJoinRoom   = "join_room"
	EventTypeLeaveRoom  = "leave_room"
	EventTypeMessage    = "send_message"
	EventTypeStatus     = "update_status"
	EventTypeDisconnect = "disconnect"
)

// Outgoing events
const (
	EventTypeUserJoined      = "user_joined"
	EventTypeUserLeft        = "user_left"
	EventTypeMessageReceived = "message_received"
	EventTypeStatusUpdated   = "status_updated"
	EventTypeError           = "error"
)
