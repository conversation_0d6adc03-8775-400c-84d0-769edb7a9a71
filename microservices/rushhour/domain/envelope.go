package domain

import (
	rushhourv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/rushhour/v1"
)

// Re-export protobuf types for easier access within the domain
type (
	EnvelopeType   = rushhourv1.EnvelopeType
	OriginType     = rushhourv1.OriginType
	SocketEnvelope = rushhourv1.SocketEnvelope
)

// Envelope type constants for easier access
const (
	EnvelopeTypeUnknown         = rushhourv1.EnvelopeType_ENVELOPE_UNKNOWN
	EnvelopeTypeWrapperCommand  = rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_COMMAND
	EnvelopeTypeWrapperResponse = rushhourv1.EnvelopeType_ENVELOPE_WRAPPER_RESPONSE
	EnvelopeTypeCommandJSON     = rushhourv1.EnvelopeType_ENVELOPE_COMMAND_JSON
	EnvelopeTypeCommandProtobuf = rushhourv1.EnvelopeType_ENVELOPE_COMMAND_PROTOBUF
)

// Origin type constants for easier access
const (
	OriginTypeUnknown  = rushhourv1.OriginType_ORIGIN_UNKNOWN
	OriginTypeGateway  = rushhourv1.OriginType_ORIGIN_GATEWAY
	OriginTypeFSA      = rushhourv1.OriginType_ORIGIN_FSA
	OriginTypeRushhour = rushhourv1.OriginType_ORIGIN_RUSHHOUR
	OriginTypeOnramp   = rushhourv1.OriginType_ORIGIN_ONRAMP
)

// Legacy constants for backward compatibility
const (
	OriginTypeApp = OriginTypeFSA      // FSA is the new name for App
	OriginTypeRH  = OriginTypeRushhour // RH is short for Rushhour
)

// NOTE: RELAY WITH FILTERING ARCHITECTURE
// The actual device message content (WrapperCommand, WrapperResponse, RealtimeData, etc.)
// is stored in the Payload field as serialized protobuf bytes from the monf-protobufs-messages.
//
// rushhour acts as an INTELLIGENT RELAY with permission-based filtering, not a simple pass-through.
// The service deserializes the payload content to inspect and filter messages based on:
//   - User permissions (device access, command permissions, data visibility)
//   - Message content sensitivity (configuration data, logs, diagnostics)
//   - Organizational access controls
//
// The BinaryMessageHandler validates both envelope-level and command-level permissions before
// relaying messages between FSA clients and gateways. Sensitive data may be filtered or
// redacted based on the requesting user's permission level.
