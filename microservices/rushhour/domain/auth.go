package domain

import (
	"fmt"
	"strings"
	"time"

	"github.com/zishang520/socket.io/v2/socket"
	"synapse-its.com/shared/api/authorizer"
)

// ClientType represents the type of client connecting to rushhour
type ClientType int

const (
	ClientTypeUnknown ClientType = iota
	ClientTypeGateway
	ClientTypeFSA
	ClientTypeOnramp
)

func (ct ClientType) String() string {
	switch ct {
	case ClientTypeGateway:
		return "gateway"
	case ClientTypeFSA:
		return "fsa"
	case ClientTypeOnramp:
		return "onramp"
	default:
		return "unknown"
	}
}

// ConnectionContext stores authentication and connection information for each socket
// This is the primary context stored in SocketRegistry for direct socket messaging
type ConnectionContext struct {
	SocketID    socket.SocketId // Primary identifier - the Socket.IO session ID
	ClientType  ClientType      // Type of client (Gateway, FSA, Onramp)
	AuthInfo    *AuthInfo       // Authentication information
	OrgID       string          // Organization ID
	ConnectedAt time.Time       // When the connection was established
}

// AuthInfo contains unified authentication information for all client types
type AuthInfo struct {
	ClientType  ClientType
	UserID      string                      // For FSA/Onramp clients
	GatewayID   string                      // For Gateway clients
	OrgID       string                      // For all client types
	Permissions *authorizer.UserPermissions // For FSA/Onramp clients
	ExpiresAt   time.Time                   // For FSA/Onramp clients (JWT expiration)
}

// GetUserID returns the appropriate user identifier for the client type
func (ai *AuthInfo) GetUserID() string {
	switch ai.ClientType {
	case ClientTypeGateway:
		return ai.GatewayID
	case ClientTypeFSA, ClientTypeOnramp:
		return ai.UserID
	default:
		return ""
	}
}

// JoinPayload represents the payload for join room requests
// Used only for streaming room joins (device channels)
type JoinPayload struct {
	Room string `json:"room"`
}

// Stream type constants for different stream channels
const (
	StreamTypeDisplay = "stream_display"
	StreamTypeRMS     = "stream_rms"
)

// Namespace constants for Socket.IO namespaces
const (
	NamespaceGateway = "/auth/gateway"
	NamespaceFSA     = "/auth/fsa"
)

// GetDeviceStreamChannel returns the stream channel name for a device
// Only streaming uses rooms - device commands use direct socket messaging
func GetDeviceStreamChannel(orgID, deviceID, streamType string) string {
	return fmt.Sprintf("org:%s:device:%s:%s", orgID, deviceID, streamType)
}

// ChannelInfo represents parsed channel information
type ChannelInfo struct {
	Type       string
	OrgID      string
	EntityID   string // gateway_id, device_id, or user_id
	EntityType string // "gateway", "device", or "user"
	Subtype    string // "control", stream type, or "private"
}

// ParseChannelInfo parses a channel name and returns structured information
func ParseChannelInfo(channel string) *ChannelInfo {
	parts := strings.Split(channel, ":")

	if len(parts) < 3 {
		return &ChannelInfo{Type: "unknown"}
	}

	switch parts[0] {
	case "org":
		if len(parts) >= 5 {
			return &ChannelInfo{
				Type:       parts[2], // "gateway" or "device"
				OrgID:      parts[1],
				EntityID:   parts[3],
				EntityType: parts[2],
				Subtype:    strings.Join(parts[4:], ":"), // "control" or stream type
			}
		}
	case "user":
		if len(parts) >= 3 {
			return &ChannelInfo{
				Type:       "user",
				EntityID:   parts[1],
				EntityType: "user",
				Subtype:    parts[2], // "private"
			}
		}
	}

	return &ChannelInfo{Type: "unknown"}
}
