package domain

import (
	"encoding/json"
	"testing"
	"time"
)

func TestRoom_Creation(t *testing.T) {
	id := "room123"
	name := "Test Room"
	createdAt := time.Now()
	memberCount := 5

	room := &Room{
		ID:          id,
		Name:        name,
		CreatedAt:   createdAt,
		MemberCount: memberCount,
	}

	if room.ID != id {
		t.<PERSON><PERSON>("ID = %s, expected %s", room.ID, id)
	}

	if room.Name != name {
		t.<PERSON>("Name = %s, expected %s", room.Name, name)
	}

	if room.CreatedAt != createdAt {
		t.Error("CreatedAt timestamp not preserved")
	}

	if room.MemberCount != memberCount {
		t.<PERSON><PERSON>("MemberCount = %d, expected %d", room.MemberCount, memberCount)
	}
}

func TestRoom_JSONSerialization(t *testing.T) {
	room := &Room{
		ID:          "room123",
		Name:        "Test Room",
		CreatedAt:   time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
		MemberCount: 3,
	}

	// Test serialization
	data, err := json.Marshal(room)
	if err != nil {
		t.Fatalf("Failed to marshal Room: %v", err)
	}

	// Test deserialization
	var deserialized Room
	err = json.Unmarshal(data, &deserialized)
	if err != nil {
		t.Fatalf("Failed to unmarshal Room: %v", err)
	}

	if deserialized.ID != room.ID {
		t.Errorf("Deserialized ID = %s, expected %s", deserialized.ID, room.ID)
	}

	if deserialized.Name != room.Name {
		t.Errorf("Deserialized Name = %s, expected %s", deserialized.Name, room.Name)
	}

	if !deserialized.CreatedAt.Equal(room.CreatedAt) {
		t.Errorf("Deserialized CreatedAt = %v, expected %v", deserialized.CreatedAt, room.CreatedAt)
	}

	if deserialized.MemberCount != room.MemberCount {
		t.Errorf("Deserialized MemberCount = %d, expected %d", deserialized.MemberCount, room.MemberCount)
	}
}

func TestUser_Creation(t *testing.T) {
	socketID := "socket123"
	userID := "user456"
	name := "John Doe"
	joinedAt := time.Now()

	user := &User{
		SocketID: socketID,
		UserID:   userID,
		Name:     name,
		JoinedAt: joinedAt,
	}

	if user.SocketID != socketID {
		t.Errorf("SocketID = %s, expected %s", user.SocketID, socketID)
	}

	if user.UserID != userID {
		t.Errorf("UserID = %s, expected %s", user.UserID, userID)
	}

	if user.Name != name {
		t.Errorf("Name = %s, expected %s", user.Name, name)
	}

	if user.JoinedAt != joinedAt {
		t.Error("JoinedAt timestamp not preserved")
	}
}

func TestUser_JSONSerialization(t *testing.T) {
	user := &User{
		SocketID: "socket123",
		UserID:   "user456",
		Name:     "John Doe",
		JoinedAt: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
	}

	// Test serialization
	data, err := json.Marshal(user)
	if err != nil {
		t.Fatalf("Failed to marshal User: %v", err)
	}

	// Test deserialization
	var deserialized User
	err = json.Unmarshal(data, &deserialized)
	if err != nil {
		t.Fatalf("Failed to unmarshal User: %v", err)
	}

	if deserialized.SocketID != user.SocketID {
		t.Errorf("Deserialized SocketID = %s, expected %s", deserialized.SocketID, user.SocketID)
	}

	if deserialized.UserID != user.UserID {
		t.Errorf("Deserialized UserID = %s, expected %s", deserialized.UserID, user.UserID)
	}

	if deserialized.Name != user.Name {
		t.Errorf("Deserialized Name = %s, expected %s", deserialized.Name, user.Name)
	}

	if !deserialized.JoinedAt.Equal(user.JoinedAt) {
		t.Errorf("Deserialized JoinedAt = %v, expected %v", deserialized.JoinedAt, user.JoinedAt)
	}
}

func TestUser_OptionalFields(t *testing.T) {
	// Test User with only required fields
	user := &User{
		SocketID: "socket123",
		JoinedAt: time.Now(),
	}

	// Verify that optional fields can be empty
	if user.UserID != "" {
		t.Error("Expected UserID to be empty by default")
	}

	if user.Name != "" {
		t.Error("Expected Name to be empty by default")
	}

	// Test serialization with omitempty tags
	data, err := json.Marshal(user)
	if err != nil {
		t.Fatalf("Failed to marshal User with optional fields: %v", err)
	}

	var jsonMap map[string]interface{}
	err = json.Unmarshal(data, &jsonMap)
	if err != nil {
		t.Fatalf("Failed to unmarshal to map: %v", err)
	}

	// UserID and Name should be omitted due to omitempty tags
	if _, exists := jsonMap["userId"]; exists {
		t.Error("Expected userId to be omitted when empty")
	}

	if _, exists := jsonMap["name"]; exists {
		t.Error("Expected name to be omitted when empty")
	}
}

func TestEvent_Creation(t *testing.T) {
	eventType := EventTypeJoinRoom
	roomID := "room123"
	userID := "user456"
	data := map[string]interface{}{
		"message": "Hello, World!",
		"count":   42,
	}
	timestamp := time.Now()

	event := &Event{
		Type:      eventType,
		RoomID:    roomID,
		UserID:    userID,
		Data:      data,
		Timestamp: timestamp,
	}

	if event.Type != eventType {
		t.Errorf("Type = %s, expected %s", event.Type, eventType)
	}

	if event.RoomID != roomID {
		t.Errorf("RoomID = %s, expected %s", event.RoomID, roomID)
	}

	if event.UserID != userID {
		t.Errorf("UserID = %s, expected %s", event.UserID, userID)
	}

	if event.Data["message"] != data["message"] {
		t.Error("Data message not preserved")
	}

	if event.Data["count"] != data["count"] {
		t.Error("Data count not preserved")
	}

	if event.Timestamp != timestamp {
		t.Error("Timestamp not preserved")
	}
}

func TestMessage_Creation(t *testing.T) {
	id := "msg123"
	roomID := "room456"
	userID := "user789"
	content := "Hello, World!"
	messageType := "text"
	metadata := map[string]interface{}{
		"formatted": true,
		"priority":  "high",
	}
	timestamp := time.Now()

	message := &Message{
		ID:        id,
		RoomID:    roomID,
		UserID:    userID,
		Content:   content,
		Type:      messageType,
		Metadata:  metadata,
		Timestamp: timestamp,
	}

	if message.ID != id {
		t.Errorf("ID = %s, expected %s", message.ID, id)
	}

	if message.RoomID != roomID {
		t.Errorf("RoomID = %s, expected %s", message.RoomID, roomID)
	}

	if message.UserID != userID {
		t.Errorf("UserID = %s, expected %s", message.UserID, userID)
	}

	if message.Content != content {
		t.Errorf("Content = %s, expected %s", message.Content, content)
	}

	if message.Type != messageType {
		t.Errorf("Type = %s, expected %s", message.Type, messageType)
	}

	if message.Metadata["formatted"] != metadata["formatted"] {
		t.Error("Metadata formatted not preserved")
	}

	if message.Metadata["priority"] != metadata["priority"] {
		t.Error("Metadata priority not preserved")
	}

	if message.Timestamp != timestamp {
		t.Error("Timestamp not preserved")
	}
}

func TestStatusUpdate_Creation(t *testing.T) {
	roomID := "room123"
	userID := "user456"
	status := "online"
	metadata := map[string]interface{}{
		"lastSeen": "2023-01-01T12:00:00Z",
		"device":   "mobile",
	}
	timestamp := time.Now()

	statusUpdate := &StatusUpdate{
		RoomID:    roomID,
		UserID:    userID,
		Status:    status,
		Metadata:  metadata,
		Timestamp: timestamp,
	}

	if statusUpdate.RoomID != roomID {
		t.Errorf("RoomID = %s, expected %s", statusUpdate.RoomID, roomID)
	}

	if statusUpdate.UserID != userID {
		t.Errorf("UserID = %s, expected %s", statusUpdate.UserID, userID)
	}

	if statusUpdate.Status != status {
		t.Errorf("Status = %s, expected %s", statusUpdate.Status, status)
	}

	if statusUpdate.Metadata["lastSeen"] != metadata["lastSeen"] {
		t.Error("Metadata lastSeen not preserved")
	}

	if statusUpdate.Metadata["device"] != metadata["device"] {
		t.Error("Metadata device not preserved")
	}

	if statusUpdate.Timestamp != timestamp {
		t.Error("Timestamp not preserved")
	}
}

func TestEventConstants(t *testing.T) {
	// Test connection event constants
	if EventTypeJoinRoom != "join_room" {
		t.Errorf("EventTypeJoinRoom = %s, expected join_room", EventTypeJoinRoom)
	}

	if EventTypeLeaveRoom != "leave_room" {
		t.Errorf("EventTypeLeaveRoom = %s, expected leave_room", EventTypeLeaveRoom)
	}

	if EventTypeMessage != "send_message" {
		t.Errorf("EventTypeMessage = %s, expected send_message", EventTypeMessage)
	}

	if EventTypeStatus != "update_status" {
		t.Errorf("EventTypeStatus = %s, expected update_status", EventTypeStatus)
	}

	if EventTypeDisconnect != "disconnect" {
		t.Errorf("EventTypeDisconnect = %s, expected disconnect", EventTypeDisconnect)
	}

	// Test outgoing event constants
	if EventTypeUserJoined != "user_joined" {
		t.Errorf("EventTypeUserJoined = %s, expected user_joined", EventTypeUserJoined)
	}

	if EventTypeUserLeft != "user_left" {
		t.Errorf("EventTypeUserLeft = %s, expected user_left", EventTypeUserLeft)
	}

	if EventTypeMessageReceived != "message_received" {
		t.Errorf("EventTypeMessageReceived = %s, expected message_received", EventTypeMessageReceived)
	}

	if EventTypeStatusUpdated != "status_updated" {
		t.Errorf("EventTypeStatusUpdated = %s, expected status_updated", EventTypeStatusUpdated)
	}

	if EventTypeError != "error" {
		t.Errorf("EventTypeError = %s, expected error", EventTypeError)
	}
}

func TestComplexJSONSerialization(t *testing.T) {
	// Test complex nested structures
	event := &Event{
		Type:   EventTypeMessage,
		RoomID: "room123",
		UserID: "user456",
		Data: map[string]interface{}{
			"message": map[string]interface{}{
				"content": "Hello",
				"type":    "text",
				"metadata": map[string]interface{}{
					"urgent": true,
					"tags":   []string{"important", "update"},
				},
			},
			"room": map[string]interface{}{
				"id":      "room123",
				"members": []string{"user1", "user2", "user3"},
			},
		},
		Timestamp: time.Date(2023, 1, 1, 12, 0, 0, 0, time.UTC),
	}

	// Test serialization
	data, err := json.Marshal(event)
	if err != nil {
		t.Fatalf("Failed to marshal complex Event: %v", err)
	}

	// Test deserialization
	var deserialized Event
	err = json.Unmarshal(data, &deserialized)
	if err != nil {
		t.Fatalf("Failed to unmarshal complex Event: %v", err)
	}

	// Verify nested data
	messageData, ok := deserialized.Data["message"].(map[string]interface{})
	if !ok {
		t.Fatal("Expected message data to be a map")
	}

	if messageData["content"] != "Hello" {
		t.Error("Message content not preserved in complex structure")
	}

	metadataData, ok := messageData["metadata"].(map[string]interface{})
	if !ok {
		t.Fatal("Expected metadata to be a map")
	}

	if metadataData["urgent"] != true {
		t.Error("Urgent flag not preserved in nested metadata")
	}
}
