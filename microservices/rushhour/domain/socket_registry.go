package domain

import (
	"sync"
)

// SocketRegistry manages the mapping between Socket IDs and connection contexts
// This enables direct socket messaging and multi-connection support per user
//
// NOTE: Device and gateway mappings are now Redis-backed in the Service layer
// for horizontal scaling compatibility. See Service.GetGatewaySocketForDevice()
// and Service.RegisterGateway() for cross-instance support.
type SocketRegistry struct {
	mu sync.RWMutex

	// Primary mapping: Socket ID → Connection Context
	socketToContext map[string]*ConnectionContext

	// User ID → Socket IDs mapping (for scenarios requiring user-wide operations)
	userToSockets map[string][]string
}

// NewSocketRegistry creates a new socket registry
func NewSocketRegistry() *SocketRegistry {
	return &SocketRegistry{
		socketToContext: make(map[string]*ConnectionContext),
		userToSockets:   make(map[string][]string),
	}
}

// RegisterSocket registers a new socket connection with its context
func (sr *SocketRegistry) RegisterSocket(socketID string, ctx *ConnectionContext) {
	sr.mu.Lock()
	defer sr.mu.Unlock()

	// Store primary mapping
	sr.socketToContext[socketID] = ctx

	// Update user-to-sockets mapping for FSA/Onramp clients
	if ctx.AuthInfo.ClientType == ClientTypeFSA || ctx.AuthInfo.ClientType == ClientTypeOnramp {
		userID := ctx.AuthInfo.UserID
		sr.userToSockets[userID] = append(sr.userToSockets[userID], socketID)
	}

	// NOTE: Gateway mappings are now handled by Service.RegisterGateway() for Redis-backed cross-instance support
}

// UnregisterSocket removes a socket and cleans up all associated mappings
func (sr *SocketRegistry) UnregisterSocket(socketID string) *ConnectionContext {
	sr.mu.Lock()
	defer sr.mu.Unlock()

	ctx, exists := sr.socketToContext[socketID]
	if !exists {
		return nil
	}

	// Remove from primary mapping
	delete(sr.socketToContext, socketID)

	// Clean up user-to-sockets mapping
	if ctx.AuthInfo.ClientType == ClientTypeFSA || ctx.AuthInfo.ClientType == ClientTypeOnramp {
		userID := ctx.AuthInfo.UserID
		if sockets, exists := sr.userToSockets[userID]; exists {
			// Remove this socket from the user's socket list
			for i, sid := range sockets {
				if sid == socketID {
					sr.userToSockets[userID] = append(sockets[:i], sockets[i+1:]...)
					break
				}
			}
			// If no more sockets for this user, remove the user entry
			if len(sr.userToSockets[userID]) == 0 {
				delete(sr.userToSockets, userID)
			}
		}
	}

	// NOTE: Gateway cleanup is now handled by Service.UnregisterGateway() for Redis-backed cross-instance support

	return ctx
}

// GetContext retrieves the connection context for a socket ID
func (sr *SocketRegistry) GetContext(socketID string) (*ConnectionContext, bool) {
	sr.mu.RLock()
	defer sr.mu.RUnlock()

	ctx, exists := sr.socketToContext[socketID]
	return ctx, exists
}

// GetUserSockets returns all socket IDs for a given user ID
func (sr *SocketRegistry) GetUserSockets(userID string) []string {
	sr.mu.RLock()
	defer sr.mu.RUnlock()

	sockets, exists := sr.userToSockets[userID]
	if !exists {
		return []string{}
	}

	// Return a copy to avoid race conditions
	result := make([]string, len(sockets))
	copy(result, sockets)
	return result
}

// GetStats returns registry statistics for monitoring
func (sr *SocketRegistry) GetStats() map[string]int {
	sr.mu.RLock()
	defer sr.mu.RUnlock()

	return map[string]int{
		"total_sockets": len(sr.socketToContext),
		"unique_users":  len(sr.userToSockets),
		// NOTE: Gateway and device stats are now available from Service layer Redis-backed methods
	}
}

// ListConnectedSockets returns all currently connected socket IDs by type
func (sr *SocketRegistry) ListConnectedSockets() map[ClientType][]string {
	sr.mu.RLock()
	defer sr.mu.RUnlock()

	result := map[ClientType][]string{
		ClientTypeGateway: {},
		ClientTypeFSA:     {},
		ClientTypeOnramp:  {},
	}

	for socketID, ctx := range sr.socketToContext {
		result[ctx.AuthInfo.ClientType] = append(result[ctx.AuthInfo.ClientType], socketID)
	}

	return result
}
