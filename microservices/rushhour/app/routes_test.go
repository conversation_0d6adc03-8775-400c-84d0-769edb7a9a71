package app

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"synapse-its.com/shared/mocks"
)

func TestNewRouter(t *testing.T) {
	// Create mock dependencies
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}

	// Create router
	router := NewRouter(conns, batch)

	if router == nil {
		t.Fatal("Expected non-nil router")
	}

	// Test that router is functional by making a request
	// We'll use a simple test endpoint to verify middleware is applied
	router.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test"))
	}).Methods("GET")

	req, err := http.NewRequest("GET", "/test", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Router returned wrong status code: got %v want %v", status, http.StatusOK)
	}
}

func TestCORSMiddleware(t *testing.T) {
	// Create router with CORS middleware
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	// Add a test endpoint
	router.HandleFunc("/cors-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}).Methods("GET")

	// Test CORS headers on regular request
	req, err := http.NewRequest("GET", "/cors-test", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	// Check CORS headers
	expectedHeaders := map[string]string{
		"Access-Control-Allow-Origin":      "*",
		"Access-Control-Allow-Methods":     "GET, POST, PUT, DELETE, OPTIONS",
		"Access-Control-Allow-Headers":     "Origin, Content-Type, Accept, Authorization",
		"Access-Control-Allow-Credentials": "true",
	}

	for header, expectedValue := range expectedHeaders {
		actualValue := rr.Header().Get(header)
		if actualValue != expectedValue {
			t.Errorf("CORS header %s = %s, expected %s", header, actualValue, expectedValue)
		}
	}
}

func TestCORSPreflightRequest(t *testing.T) {
	// Create router with CORS middleware
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	// Add a test endpoint that supports OPTIONS
	router.HandleFunc("/cors-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}).Methods("GET", "OPTIONS")

	// Test OPTIONS preflight request to existing endpoint
	req, err := http.NewRequest("OPTIONS", "/cors-test", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	// OPTIONS request should return 200 OK when handled by CORS middleware
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("OPTIONS request returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Check CORS headers are present
	expectedHeaders := map[string]string{
		"Access-Control-Allow-Origin":      "*",
		"Access-Control-Allow-Methods":     "GET, POST, PUT, DELETE, OPTIONS",
		"Access-Control-Allow-Headers":     "Origin, Content-Type, Accept, Authorization",
		"Access-Control-Allow-Credentials": "true",
	}

	for header, expectedValue := range expectedHeaders {
		actualValue := rr.Header().Get(header)
		if actualValue != expectedValue {
			t.Errorf("CORS header %s = %s, expected %s", header, actualValue, expectedValue)
		}
	}
}

func TestMiddlewareApplication(t *testing.T) {
	// Create router
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	// Add a test endpoint that uses the middleware-provided connections
	router.HandleFunc("/middleware-test", func(w http.ResponseWriter, r *http.Request) {
		// Test that middleware has been applied by checking context
		// The ConnectionsMiddleware should add connections to the request context
		// We can't easily test this without more complex mocking, but we can verify
		// the endpoint is reachable and responds
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("middleware-applied"))
	}).Methods("GET")

	req, err := http.NewRequest("GET", "/middleware-test", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Middleware test returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	if body := rr.Body.String(); body != "middleware-applied" {
		t.Errorf("Expected response body 'middleware-applied', got %s", body)
	}
}

func TestRouterHTTPMethods(t *testing.T) {
	// Test that router properly handles different HTTP methods
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	// Add test endpoints for different methods
	router.HandleFunc("/get-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}).Methods("GET")

	router.HandleFunc("/post-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusCreated)
	}).Methods("POST")

	router.HandleFunc("/put-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusAccepted)
	}).Methods("PUT")

	router.HandleFunc("/delete-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusNoContent)
	}).Methods("DELETE")

	// Test each method
	testCases := []struct {
		method         string
		endpoint       string
		expectedStatus int
	}{
		{"GET", "/get-test", http.StatusOK},
		{"POST", "/post-test", http.StatusCreated},
		{"PUT", "/put-test", http.StatusAccepted},
		{"DELETE", "/delete-test", http.StatusNoContent},
	}

	for _, tc := range testCases {
		req, err := http.NewRequest(tc.method, tc.endpoint, nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != tc.expectedStatus {
			t.Errorf("%s %s returned wrong status code: got %v want %v",
				tc.method, tc.endpoint, status, tc.expectedStatus)
		}
	}
}

func TestRouterMethodNotAllowed(t *testing.T) {
	// Test that router returns 405 for unsupported methods
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	// Add a GET-only endpoint
	router.HandleFunc("/get-only", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}).Methods("GET")

	// Test unsupported methods
	unsupportedMethods := []string{"POST", "PUT", "DELETE", "PATCH"}

	for _, method := range unsupportedMethods {
		req, err := http.NewRequest(method, "/get-only", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusMethodNotAllowed {
			t.Errorf("%s /get-only should return 405, got %v", method, status)
		}
	}
}

func TestRouterNotFound(t *testing.T) {
	// Test that router returns 404 for non-existent endpoints
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	req, err := http.NewRequest("GET", "/non-existent", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusNotFound {
		t.Errorf("Non-existent endpoint should return 404, got %v", status)
	}
}

func TestCORSWithDifferentOrigins(t *testing.T) {
	// Test CORS behavior with different origins
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	router.HandleFunc("/origin-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	}).Methods("GET")

	// Test with different Origin headers
	origins := []string{
		"http://localhost:3000",
		"https://example.com",
		"https://app.synapse-its.com",
	}

	for _, origin := range origins {
		req, err := http.NewRequest("GET", "/origin-test", nil)
		if err != nil {
			t.Fatal(err)
		}
		req.Header.Set("Origin", origin)

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		// Should allow all origins (Access-Control-Allow-Origin: *)
		allowOrigin := rr.Header().Get("Access-Control-Allow-Origin")
		if allowOrigin != "*" {
			t.Errorf("Expected Access-Control-Allow-Origin to be *, got %s", allowOrigin)
		}
	}
}

func TestMiddlewareOrder(t *testing.T) {
	// Test that middleware is applied in the correct order
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}
	router := NewRouter(conns, batch)

	// Add a test endpoint
	router.HandleFunc("/order-test", func(w http.ResponseWriter, r *http.Request) {
		// All middleware should have been applied before reaching this handler
		w.WriteHeader(http.StatusOK)
	}).Methods("GET")

	req, err := http.NewRequest("GET", "/order-test", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	// Verify the request was successful (middleware didn't interfere)
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Middleware order test failed with status %v", status)
	}

	// Verify CORS headers are present (CORS middleware was applied)
	if origin := rr.Header().Get("Access-Control-Allow-Origin"); origin == "" {
		t.Error("CORS middleware was not applied")
	}
}

func TestRouterConfiguration(t *testing.T) {
	// Test that router is properly configured with dependencies
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}

	router := NewRouter(conns, batch)

	// Verify router is of the correct type
	if router == nil {
		t.Fatal("Expected router to be created")
	}

	// Test that the router can handle requests
	router.HandleFunc("/config-test", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("configured"))
	})

	req, err := http.NewRequest("GET", "/config-test", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Router configuration test failed with status %v", status)
	}

	if body := rr.Body.String(); body != "configured" {
		t.Errorf("Expected response 'configured', got %s", body)
	}
}
