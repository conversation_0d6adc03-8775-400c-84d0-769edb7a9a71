package app

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"synapse-its.com/shared/mocks"
)

func TestNewApp(t *testing.T) {
	// Use fake connections and batch
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}

	app, err := NewApp(conns, batch)
	if err != nil {
		t.Fatalf("Failed to create app: %v", err)
	}

	if app == nil {
		t.Fatal("Expected non-nil app")
	}

	if app.muxRouter == nil {
		t.Error("Expected mux router to be initialized")
	}

	if app.socketioService == nil {
		t.Error("Expected Socket.IO service to be initialized")
	}

	if app.socketioHandler == nil {
		t.Error("Expected Socket.IO handler to be initialized")
	}

	if app.connections != conns {
		t.Error("Expected connections to be preserved")
	}

	if app.batch != batch {
		t.Error("Expected batch to be preserved")
	}
}

func TestInfoHandler(t *testing.T) {
	// Create a request to the info endpoint
	req, err := http.NewRequest("GET", "/info", nil)
	if err != nil {
		t.Fatal(err)
	}

	// Create a ResponseRecorder to record the response
	rr := httptest.NewRecorder()

	// Call the handler directly
	InfoHandler(rr, req)

	// Check the status code
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Handler returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Check the content type
	expectedContentType := "application/json"
	if contentType := rr.Header().Get("Content-Type"); contentType != expectedContentType {
		t.Errorf("Handler returned wrong content type: got %v want %v", contentType, expectedContentType)
	}

	// Parse the JSON response
	var response map[string]interface{}
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	// Check required fields
	expectedService := "rushhour"
	if service, ok := response["service"].(string); !ok || service != expectedService {
		t.Errorf("Expected service to be %s, got %v", expectedService, response["service"])
	}

	if description, ok := response["description"].(string); !ok || description == "" {
		t.Error("Expected non-empty description")
	}

	if version, ok := response["version"].(string); !ok || version == "" {
		t.Error("Expected non-empty version")
	}

	// Check endpoints
	endpoints, ok := response["endpoints"].(map[string]interface{})
	if !ok {
		t.Fatal("Expected endpoints to be a map")
	}

	expectedEndpoints := map[string]string{
		"socketio": "/socket.io/",
		"info":     "/info",
	}

	for key, expectedValue := range expectedEndpoints {
		if value, exists := endpoints[key]; !exists {
			t.Errorf("Expected endpoint %s to exist", key)
		} else if value != expectedValue {
			t.Errorf("Expected endpoint %s to be %s, got %v", key, expectedValue, value)
		}
	}

	// Check timestamp format
	timestampStr, ok := response["timestamp"].(string)
	if !ok {
		t.Fatal("Expected timestamp to be a string")
	}

	// Parse timestamp to verify it's in RFC3339 format
	_, err = time.Parse(time.RFC3339, timestampStr)
	if err != nil {
		t.Errorf("Expected timestamp to be in RFC3339 format, got: %s", timestampStr)
	}
}

func TestApp_Serve(t *testing.T) {
	// Create app with mocked dependencies
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}

	app, err := NewApp(conns, batch)
	if err != nil {
		t.Fatalf("Failed to create app: %v", err)
	}

	// Get the configured router
	router := app.Serve()
	if router == nil {
		t.Fatal("Expected non-nil router")
	}

	// Test the info endpoint
	req, err := http.NewRequest("GET", "/info", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Info endpoint returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Test the root endpoint (should also return info)
	req, err = http.NewRequest("GET", "/", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr = httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Root endpoint returned wrong status code: got %v want %v", status, http.StatusOK)
	}

	// Verify it returns the same info content
	var response map[string]interface{}
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse JSON response from root endpoint: %v", err)
	}

	if service, ok := response["service"].(string); !ok || service != "rushhour" {
		t.Error("Root endpoint should return service info")
	}
}

func TestApp_ServeSocketIOEndpoint(t *testing.T) {
	// Create app with mocked dependencies
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}

	app, err := NewApp(conns, batch)
	if err != nil {
		t.Fatalf("Failed to create app: %v", err)
	}

	router := app.Serve()

	// Test Socket.IO endpoint exists (we can't test the actual Socket.IO functionality easily,
	// but we can verify the endpoint is registered)
	req, err := http.NewRequest("GET", "/socket.io/", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	router.ServeHTTP(rr, req)

	// Socket.IO handler should handle this request (may return various status codes
	// depending on the request format, but it should not be 404)
	if status := rr.Code; status == http.StatusNotFound {
		t.Error("Socket.IO endpoint should be registered and not return 404")
	}
}

func TestApp_HTTPMethods(t *testing.T) {
	// Create app with mocked dependencies
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}

	app, err := NewApp(conns, batch)
	if err != nil {
		t.Fatalf("Failed to create app: %v", err)
	}

	router := app.Serve()

	// Test that info endpoint only accepts GET
	methods := []string{"POST", "PUT", "DELETE", "PATCH"}

	for _, method := range methods {
		req, err := http.NewRequest(method, "/info", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusMethodNotAllowed {
			t.Errorf("Info endpoint should not accept %s method, got status %d", method, status)
		}
	}

	// Test that root endpoint only accepts GET
	for _, method := range methods {
		req, err := http.NewRequest(method, "/", nil)
		if err != nil {
			t.Fatal(err)
		}

		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)

		if status := rr.Code; status != http.StatusMethodNotAllowed {
			t.Errorf("Root endpoint should not accept %s method, got status %d", method, status)
		}
	}
}

func TestInfoHandler_ResponseFormat(t *testing.T) {
	req, err := http.NewRequest("GET", "/info", nil)
	if err != nil {
		t.Fatal(err)
	}

	rr := httptest.NewRecorder()
	InfoHandler(rr, req)

	// Test that response is valid JSON
	var response map[string]interface{}
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Response is not valid JSON: %v", err)
	}

	// Test specific structure
	requiredFields := []string{"service", "description", "version", "endpoints", "timestamp"}
	for _, field := range requiredFields {
		if _, exists := response[field]; !exists {
			t.Errorf("Response missing required field: %s", field)
		}
	}

	// Test that endpoints is properly structured
	endpoints, ok := response["endpoints"].(map[string]interface{})
	if !ok {
		t.Fatal("Endpoints should be a map")
	}

	requiredEndpoints := []string{"socketio", "info"}
	for _, endpoint := range requiredEndpoints {
		if _, exists := endpoints[endpoint]; !exists {
			t.Errorf("Endpoints missing required endpoint: %s", endpoint)
		}
	}
}

func TestApp_DependencyInjection(t *testing.T) {
	// Test that dependencies are properly injected and accessible
	conns := mocks.FakeConns()
	batch := &mocks.FakeBatcher{}

	app, err := NewApp(conns, batch)
	if err != nil {
		t.Fatalf("Failed to create app: %v", err)
	}

	// Verify dependencies are stored
	if app.connections != conns {
		t.Error("Connections not properly injected")
	}

	if app.batch != batch {
		t.Error("Batch not properly injected")
	}

	// Verify services are created
	if app.socketioService == nil {
		t.Error("Socket.IO service not created")
	}

	if app.socketioHandler == nil {
		t.Error("Socket.IO handler not created")
	}

	if app.muxRouter == nil {
		t.Error("Mux router not created")
	}
}

func TestInfoHandler_Idempotency(t *testing.T) {
	// Test that multiple calls to InfoHandler return consistent results
	req1, _ := http.NewRequest("GET", "/info", nil)
	req2, _ := http.NewRequest("GET", "/info", nil)

	rr1 := httptest.NewRecorder()
	rr2 := httptest.NewRecorder()

	InfoHandler(rr1, req1)
	InfoHandler(rr2, req2)

	var response1, response2 map[string]interface{}
	json.Unmarshal(rr1.Body.Bytes(), &response1)
	json.Unmarshal(rr2.Body.Bytes(), &response2)

	// Compare all fields except timestamp
	fieldsToCompare := []string{"service", "description", "version"}
	for _, field := range fieldsToCompare {
		if response1[field] != response2[field] {
			t.Errorf("Field %s differs between calls: %v vs %v", field, response1[field], response2[field])
		}
	}

	// Compare endpoints separately (they are maps)
	endpoints1, ok1 := response1["endpoints"].(map[string]interface{})
	endpoints2, ok2 := response2["endpoints"].(map[string]interface{})
	if !ok1 || !ok2 {
		t.Error("Endpoints should be maps")
	} else {
		for key, value1 := range endpoints1 {
			if value2, exists := endpoints2[key]; !exists || value1 != value2 {
				t.Errorf("Endpoint %s differs between calls: %v vs %v", key, value1, value2)
			}
		}
	}

	// Both responses should have timestamps (don't compare values since they might be identical)
	if _, ok := response1["timestamp"]; !ok {
		t.Error("Response 1 should have a timestamp")
	}
	if _, ok := response2["timestamp"]; !ok {
		t.Error("Response 2 should have a timestamp")
	}
}
