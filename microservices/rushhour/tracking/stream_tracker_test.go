package tracking

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
)

func TestNewStreamTracker(t *testing.T) {
	tracker := NewStreamTracker()

	assert.NotNil(t, tracker)
	assert.NotNil(t, tracker.viewers)
	assert.NotNil(t, tracker.connDevices)
	assert.Nil(t, tracker.redisClient)
}

func TestNewStreamTrackerWithRedis(t *testing.T) {
	// Create a mock Redis client for testing
	redisClient := redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
	})
	ctx := context.Background()

	tracker := NewStreamTrackerWithRedis(redisClient, ctx)

	assert.NotNil(t, tracker)
	assert.NotNil(t, tracker.viewers)
	assert.NotNil(t, tracker.connDevices)
	assert.Equal(t, redisClient, tracker.redisClient)
	assert.<PERSON>(t, ctx, tracker.ctx)
}

func TestStreamTracker_AddViewer_LocalMode(t *testing.T) {
	tests := []struct {
		name          string
		setupViewers  map[string]map[string]struct{}
		deviceID      string
		connID        string
		expectedFirst bool
		expectedCount int
	}{
		{
			name:          "first viewer for device",
			setupViewers:  make(map[string]map[string]struct{}),
			deviceID:      "device123",
			connID:        "conn456",
			expectedFirst: true,
			expectedCount: 1,
		},
		{
			name: "second viewer for device",
			setupViewers: map[string]map[string]struct{}{
				"device123": {"conn111": {}},
			},
			deviceID:      "device123",
			connID:        "conn456",
			expectedFirst: false,
			expectedCount: 2,
		},
		{
			name: "same viewer added twice",
			setupViewers: map[string]map[string]struct{}{
				"device123": {"conn456": {}},
			},
			deviceID:      "device123",
			connID:        "conn456",
			expectedFirst: false,
			expectedCount: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tracker := NewStreamTracker()
			tracker.viewers = tt.setupViewers

			isFirst := tracker.AddViewer(tt.deviceID, tt.connID)

			assert.Equal(t, tt.expectedFirst, isFirst)
			assert.Equal(t, tt.expectedCount, len(tracker.viewers[tt.deviceID]))
			assert.Contains(t, tracker.viewers[tt.deviceID], tt.connID)

			// Check reverse mapping
			assert.Contains(t, tracker.connDevices[tt.connID], tt.deviceID)
		})
	}
}

func TestStreamTracker_RemoveViewer_LocalMode(t *testing.T) {
	tests := []struct {
		name          string
		setupViewers  map[string]map[string]struct{}
		setupConnDev  map[string]map[string]struct{}
		deviceID      string
		connID        string
		expectedLast  bool
		expectedCount int
	}{
		{
			name: "remove last viewer",
			setupViewers: map[string]map[string]struct{}{
				"device123": {"conn456": {}},
			},
			setupConnDev: map[string]map[string]struct{}{
				"conn456": {"device123": {}},
			},
			deviceID:      "device123",
			connID:        "conn456",
			expectedLast:  true,
			expectedCount: 0,
		},
		{
			name: "remove one of multiple viewers",
			setupViewers: map[string]map[string]struct{}{
				"device123": {"conn456": {}, "conn789": {}},
			},
			setupConnDev: map[string]map[string]struct{}{
				"conn456": {"device123": {}},
				"conn789": {"device123": {}},
			},
			deviceID:      "device123",
			connID:        "conn456",
			expectedLast:  false,
			expectedCount: 1,
		},
		{
			name:          "remove non-existent viewer",
			setupViewers:  make(map[string]map[string]struct{}),
			setupConnDev:  make(map[string]map[string]struct{}),
			deviceID:      "device123",
			connID:        "conn456",
			expectedLast:  true, // Returns true because device has no viewers (empty)
			expectedCount: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tracker := NewStreamTracker()
			tracker.viewers = tt.setupViewers
			tracker.connDevices = tt.setupConnDev

			isLast := tracker.RemoveViewer(tt.deviceID, tt.connID)

			assert.Equal(t, tt.expectedLast, isLast)
			if tt.expectedCount == 0 {
				// If no viewers left, the device should be removed from the map
				assert.NotContains(t, tracker.viewers, tt.deviceID)
			} else {
				assert.Equal(t, tt.expectedCount, len(tracker.viewers[tt.deviceID]))
				assert.NotContains(t, tracker.viewers[tt.deviceID], tt.connID)
			}

			// Check reverse mapping cleanup
			if connDevices, exists := tracker.connDevices[tt.connID]; exists {
				assert.NotContains(t, connDevices, tt.deviceID)
			}
		})
	}
}

func TestStreamTracker_GetViewerCount_LocalMode(t *testing.T) {
	tests := []struct {
		name          string
		setupViewers  map[string]map[string]struct{}
		deviceID      string
		expectedCount int
	}{
		{
			name:          "device with no viewers",
			setupViewers:  make(map[string]map[string]struct{}),
			deviceID:      "device123",
			expectedCount: 0,
		},
		{
			name: "device with single viewer",
			setupViewers: map[string]map[string]struct{}{
				"device123": {"conn456": {}},
			},
			deviceID:      "device123",
			expectedCount: 1,
		},
		{
			name: "device with multiple viewers",
			setupViewers: map[string]map[string]struct{}{
				"device123": {"conn456": {}, "conn789": {}, "conn101": {}},
			},
			deviceID:      "device123",
			expectedCount: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tracker := NewStreamTracker()
			tracker.viewers = tt.setupViewers

			count := tracker.GetViewerCount(tt.deviceID)
			assert.Equal(t, tt.expectedCount, count)
		})
	}
}

func TestStreamTracker_GetDevicesFor_LocalMode(t *testing.T) {
	tests := []struct {
		name            string
		setupConnDev    map[string]map[string]struct{}
		connID          string
		expectedDevices []string
	}{
		{
			name:            "connection with no devices",
			setupConnDev:    make(map[string]map[string]struct{}),
			connID:          "conn456",
			expectedDevices: []string{},
		},
		{
			name: "connection with single device",
			setupConnDev: map[string]map[string]struct{}{
				"conn456": {"device123": {}},
			},
			connID:          "conn456",
			expectedDevices: []string{"device123"},
		},
		{
			name: "connection with multiple devices",
			setupConnDev: map[string]map[string]struct{}{
				"conn456": {"device123": {}, "device789": {}, "device101": {}},
			},
			connID:          "conn456",
			expectedDevices: []string{"device123", "device789", "device101"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tracker := NewStreamTracker()
			tracker.connDevices = tt.setupConnDev

			devices := tracker.GetDevicesFor(tt.connID)
			assert.ElementsMatch(t, tt.expectedDevices, devices)
		})
	}
}

func TestStreamTracker_RemoveConnection_LocalMode(t *testing.T) {
	tracker := NewStreamTracker()

	// Setup test data
	tracker.viewers = map[string]map[string]struct{}{
		"device123": {"conn456": {}, "conn789": {}},
		"device111": {"conn456": {}},
		"device222": {"conn999": {}},
	}
	tracker.connDevices = map[string]map[string]struct{}{
		"conn456": {"device123": {}, "device111": {}},
		"conn789": {"device123": {}},
		"conn999": {"device222": {}},
	}

	// Remove conn456 and get affected devices
	affectedDevices := tracker.RemoveConnection("conn456")

	// Should return only devices that became completely empty (no more viewers)
	assert.ElementsMatch(t, []string{"device111"}, affectedDevices)

	// Verify conn456 is removed from connDevices
	assert.NotContains(t, tracker.connDevices, "conn456")

	// Verify conn456 is removed from viewers
	assert.NotContains(t, tracker.viewers["device123"], "conn456")
	assert.NotContains(t, tracker.viewers, "device111") // device111 should be removed entirely

	// Verify other connections are unaffected
	assert.Contains(t, tracker.viewers["device123"], "conn789")
	assert.Contains(t, tracker.viewers["device222"], "conn999")
	assert.Contains(t, tracker.connDevices["conn789"], "device123")
	assert.Contains(t, tracker.connDevices["conn999"], "device222")
}

func TestStreamTracker_GetActiveDevices_LocalMode(t *testing.T) {
	tracker := NewStreamTracker()

	// Setup test data
	tracker.viewers = map[string]map[string]struct{}{
		"device123": {"conn456": {}},
		"device789": {"conn111": {}, "conn222": {}},
		"device333": {}, // Empty viewers map (should not be included)
	}

	devices := tracker.GetActiveDevices()

	expected := []string{"device123", "device789", "device333"} // GetActiveDevices returns all devices in viewers map
	assert.ElementsMatch(t, expected, devices)
}

func TestStreamTracker_HasViewers_LocalMode(t *testing.T) {
	tests := []struct {
		name         string
		setupViewers map[string]map[string]struct{}
		deviceID     string
		expected     bool
	}{
		{
			name:         "device with no viewers",
			setupViewers: make(map[string]map[string]struct{}),
			deviceID:     "device123",
			expected:     false,
		},
		{
			name: "device with viewers",
			setupViewers: map[string]map[string]struct{}{
				"device123": {"conn456": {}},
			},
			deviceID: "device123",
			expected: true,
		},
		{
			name: "device with empty viewers map",
			setupViewers: map[string]map[string]struct{}{
				"device123": {},
			},
			deviceID: "device123",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tracker := NewStreamTracker()
			tracker.viewers = tt.setupViewers

			result := tracker.HasViewers(tt.deviceID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestStreamTracker_GetStats_LocalMode(t *testing.T) {
	tracker := NewStreamTracker()

	// Setup test data
	tracker.viewers = map[string]map[string]struct{}{
		"device123": {"conn456": {}},
		"device789": {"conn111": {}, "conn222": {}},
	}
	tracker.connDevices = map[string]map[string]struct{}{
		"conn456": {"device123": {}},
		"conn111": {"device789": {}},
		"conn222": {"device789": {}},
	}

	stats := tracker.GetStats()

	expected := map[string]interface{}{
		"active_devices":     2,
		"active_connections": 3,
		"total_viewers":      3,
		"redis_backed":       false,
	}

	assert.Equal(t, expected, stats)
}

func TestStreamTracker_ConcurrentAccess(t *testing.T) {
	tracker := NewStreamTracker()

	// Test concurrent adds and removes
	done := make(chan bool, 100)

	// Spawn 50 goroutines adding viewers
	for i := 0; i < 50; i++ {
		go func(id int) {
			deviceID := fmt.Sprintf("device%d", id%5) // 5 different devices
			connID := fmt.Sprintf("conn%d", id)
			tracker.AddViewer(deviceID, connID)
			done <- true
		}(i)
	}

	// Spawn 50 goroutines removing viewers
	for i := 0; i < 50; i++ {
		go func(id int) {
			deviceID := fmt.Sprintf("device%d", id%5)
			connID := fmt.Sprintf("conn%d", id)
			tracker.RemoveViewer(deviceID, connID)
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 100; i++ {
		select {
		case <-done:
		case <-time.After(5 * time.Second):
			t.Fatal("Test timed out")
		}
	}

	// Verify no panics occurred and maps are consistent
	tracker.mu.RLock()
	defer tracker.mu.RUnlock()

	for deviceID, viewers := range tracker.viewers {
		for connID := range viewers {
			// Each viewer should be in the reverse mapping
			if connDevices, exists := tracker.connDevices[connID]; exists {
				assert.Contains(t, connDevices, deviceID)
			}
		}
	}
}

func TestStreamTracker_EdgeCases(t *testing.T) {
	tracker := NewStreamTracker()

	t.Run("empty strings", func(t *testing.T) {
		// Should handle empty strings gracefully
		isFirst := tracker.AddViewer("", "conn123")
		assert.True(t, isFirst)

		isLast := tracker.RemoveViewer("", "conn123")
		assert.True(t, isLast)

		count := tracker.GetViewerCount("")
		assert.Equal(t, 0, count)
	})

	t.Run("nil safety", func(t *testing.T) {
		// The implementation panics with nil maps, so we test that it panics
		emptyTracker := &StreamTracker{}

		assert.Panics(t, func() {
			emptyTracker.AddViewer("device1", "conn1")
		})

		// Initialize the maps to test other methods
		emptyTracker.viewers = make(map[string]map[string]struct{})
		emptyTracker.connDevices = make(map[string]map[string]struct{})

		assert.NotPanics(t, func() {
			emptyTracker.RemoveViewer("device1", "conn1")
			emptyTracker.GetViewerCount("device1")
			emptyTracker.RemoveConnection("conn1")
		})
	})
}
