package tracking

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"synapse-its.com/shared/logger"
)

// StreamTracker manages active viewers for device streams using Redis for horizontal scaling
//
// IMPORTANT: Only FSA (Field Service App) and Onramp clients should be tracked as "viewers".
// Gateway connections are data sources and should NOT be counted in viewer totals.
// The service layer (HandleDeviceChannelJoin/Leave) enforces this by only calling
// AddViewer/RemoveViewer for authenticated FSA and Onramp clients.
type StreamTracker struct {
	redisClient *redis.Client   // Redis client for cross-instance data
	ctx         context.Context // Context for Redis operations
	mu          sync.RWMutex    // Local mutex for fallback mode

	// Fallback maps for single-instance mode when Redis unavailable
	viewers     map[string]map[string]struct{} // deviceID → FSA/Onramp connIDs (viewers only)
	connDevices map[string]map[string]struct{} // FSA/Onramp connID → deviceIDs (reverse mapping)
}

// NewStreamTracker creates a new StreamTracker instance
func NewStreamTracker() *StreamTracker {
	return &StreamTracker{
		viewers:     make(map[string]map[string]struct{}),
		connDevices: make(map[string]map[string]struct{}),
	}
}

// NewStreamTrackerWithRedis creates a new StreamTracker instance with Redis support
func NewStreamTrackerWithRedis(redisClient *redis.Client, ctx context.Context) *StreamTracker {
	return &StreamTracker{
		redisClient: redisClient,
		ctx:         ctx,
		viewers:     make(map[string]map[string]struct{}),
		connDevices: make(map[string]map[string]struct{}),
	}
}

// AddViewer adds a viewer to a device stream and returns true if this is the first viewer
func (st *StreamTracker) AddViewer(deviceID, connID string) bool {
	// Use Redis for cross-instance coordination
	if st.redisClient != nil {
		return st.addViewerRedis(deviceID, connID)
	}

	// Fall back to local maps (single instance mode)
	return st.addViewerLocal(deviceID, connID)
}

// addViewerRedis handles Redis-backed viewer tracking for horizontal scaling
func (st *StreamTracker) addViewerRedis(deviceID, connID string) bool {
	deviceViewersKey := fmt.Sprintf("rushhour:stream:device:%s:viewers", deviceID)
	connDevicesKey := fmt.Sprintf("rushhour:stream:conn:%s:devices", connID)

	// Use Redis pipeline for atomic operations
	pipe := st.redisClient.Pipeline()

	// Get current viewer count before adding
	countCmd := pipe.SCard(st.ctx, deviceViewersKey)

	// Add viewer to device's viewer set
	pipe.SAdd(st.ctx, deviceViewersKey, connID)
	pipe.Expire(st.ctx, deviceViewersKey, 2*time.Hour) // Auto-cleanup

	// Add device to connection's device set
	pipe.SAdd(st.ctx, connDevicesKey, deviceID)
	pipe.Expire(st.ctx, connDevicesKey, 2*time.Hour) // Auto-cleanup

	// Execute pipeline
	_, err := pipe.Exec(st.ctx)
	if err != nil {
		logger.Warnf("Redis error adding viewer %s to device %s: %v", connID, deviceID, err)
		// Fall back to local tracking
		return st.addViewerLocal(deviceID, connID)
	}

	// Check if this was the first viewer
	wasEmpty := countCmd.Val() == 0
	logger.Debugf("Added viewer %s to device %s via Redis (first viewer: %v)", connID, deviceID, wasEmpty)
	return wasEmpty
}

// addViewerLocal handles local map-based viewer tracking (fallback mode)
func (st *StreamTracker) addViewerLocal(deviceID, connID string) bool {
	st.mu.Lock()
	defer st.mu.Unlock()

	// Initialize device viewers map if needed
	if st.viewers[deviceID] == nil {
		st.viewers[deviceID] = make(map[string]struct{})
	}

	// Initialize connection devices map if needed
	if st.connDevices[connID] == nil {
		st.connDevices[connID] = make(map[string]struct{})
	}

	// Add the viewer
	wasEmpty := len(st.viewers[deviceID]) == 0
	st.viewers[deviceID][connID] = struct{}{}
	st.connDevices[connID][deviceID] = struct{}{}

	logger.Debugf("Added viewer %s to device %s locally (first viewer: %v)", connID, deviceID, wasEmpty)
	return wasEmpty
}

// RemoveViewer removes a viewer from a device stream and returns true if this was the last viewer
func (st *StreamTracker) RemoveViewer(deviceID, connID string) bool {
	// Use Redis for cross-instance coordination
	if st.redisClient != nil {
		return st.removeViewerRedis(deviceID, connID)
	}

	// Fall back to local maps (single instance mode)
	return st.removeViewerLocal(deviceID, connID)
}

// removeViewerRedis handles Redis-backed viewer removal for horizontal scaling
func (st *StreamTracker) removeViewerRedis(deviceID, connID string) bool {
	deviceViewersKey := fmt.Sprintf("rushhour:stream:device:%s:viewers", deviceID)
	connDevicesKey := fmt.Sprintf("rushhour:stream:conn:%s:devices", connID)

	// Use Redis pipeline for atomic operations
	pipe := st.redisClient.Pipeline()

	// Remove viewer from device's viewer set
	pipe.SRem(st.ctx, deviceViewersKey, connID)

	// Remove device from connection's device set
	pipe.SRem(st.ctx, connDevicesKey, deviceID)

	// Get resulting viewer count after removal
	countCmd := pipe.SCard(st.ctx, deviceViewersKey)

	// Execute pipeline
	_, err := pipe.Exec(st.ctx)
	if err != nil {
		logger.Warnf("Redis error removing viewer %s from device %s: %v", connID, deviceID, err)
		// Fall back to local tracking
		return st.removeViewerLocal(deviceID, connID)
	}

	// Check if this was the last viewer
	isEmpty := countCmd.Val() == 0

	// Clean up empty sets
	if isEmpty {
		if err := st.redisClient.Del(st.ctx, deviceViewersKey).Err(); err != nil {
			logger.Warnf("Failed to clean up empty device viewers set for %s: %v", deviceID, err)
		}
	}

	logger.Debugf("Removed viewer %s from device %s via Redis (last viewer: %v)", connID, deviceID, isEmpty)
	return isEmpty
}

// removeViewerLocal handles local map-based viewer removal (fallback mode)
func (st *StreamTracker) removeViewerLocal(deviceID, connID string) bool {
	st.mu.Lock()
	defer st.mu.Unlock()

	// Remove from viewers
	if st.viewers[deviceID] != nil {
		delete(st.viewers[deviceID], connID)
	}

	// Remove from reverse mapping
	if st.connDevices[connID] != nil {
		delete(st.connDevices[connID], deviceID)
	}

	// Check if device has no more viewers
	isEmpty := len(st.viewers[deviceID]) == 0
	if isEmpty {
		delete(st.viewers, deviceID)
	}

	// Clean up empty connection map
	if len(st.connDevices[connID]) == 0 {
		delete(st.connDevices, connID)
	}

	logger.Debugf("Removed viewer %s from device %s locally (last viewer: %v)", connID, deviceID, isEmpty)
	return isEmpty
}

// GetViewersFor returns all connection IDs viewing a specific device
func (st *StreamTracker) GetViewersFor(deviceID string) []string {
	st.mu.RLock()
	defer st.mu.RUnlock()

	viewers := make([]string, 0, len(st.viewers[deviceID]))
	for connID := range st.viewers[deviceID] {
		viewers = append(viewers, connID)
	}
	return viewers
}

// GetDevicesFor returns all device IDs being viewed by a specific connection
func (st *StreamTracker) GetDevicesFor(connID string) []string {
	st.mu.RLock()
	defer st.mu.RUnlock()

	devices := make([]string, 0, len(st.connDevices[connID]))
	for deviceID := range st.connDevices[connID] {
		devices = append(devices, deviceID)
	}
	return devices
}

// GetViewerCount returns the number of active viewers for a device
func (st *StreamTracker) GetViewerCount(deviceID string) int {
	// Use Redis for cross-instance data
	if st.redisClient != nil {
		deviceViewersKey := fmt.Sprintf("rushhour:stream:device:%s:viewers", deviceID)
		count, err := st.redisClient.SCard(st.ctx, deviceViewersKey).Result()
		if err != nil {
			logger.Warnf("Redis error getting viewer count for device %s: %v", deviceID, err)
			// Fall back to local data
		} else {
			return int(count)
		}
	}

	// Fall back to local maps
	st.mu.RLock()
	defer st.mu.RUnlock()
	return len(st.viewers[deviceID])
}

// HasViewers returns true if the device has any active viewers
func (st *StreamTracker) HasViewers(deviceID string) bool {
	return st.GetViewerCount(deviceID) > 0
}

// GetActiveDevices returns all device IDs that currently have viewers
func (st *StreamTracker) GetActiveDevices() []string {
	st.mu.RLock()
	defer st.mu.RUnlock()

	devices := make([]string, 0, len(st.viewers))
	for deviceID := range st.viewers {
		devices = append(devices, deviceID)
	}
	return devices
}

// RemoveConnection removes all device associations for a connection (used on disconnect)
func (st *StreamTracker) RemoveConnection(connID string) []string {
	// Use Redis for cross-instance coordination
	if st.redisClient != nil {
		return st.removeConnectionRedis(connID)
	}

	// Fall back to local maps (single instance mode)
	return st.removeConnectionLocal(connID)
}

// removeConnectionRedis handles Redis-backed connection removal for horizontal scaling
func (st *StreamTracker) removeConnectionRedis(connID string) []string {
	connDevicesKey := fmt.Sprintf("rushhour:stream:conn:%s:devices", connID)

	// Get all devices for this connection
	devices, err := st.redisClient.SMembers(st.ctx, connDevicesKey).Result()
	if err != nil {
		logger.Warnf("Redis error getting devices for connection %s: %v", connID, err)
		// Fall back to local tracking
		return st.removeConnectionLocal(connID)
	}

	devicesWithLastViewer := make([]string, 0)

	// Remove connection from all devices and check for last viewer
	for _, deviceID := range devices {
		deviceViewersKey := fmt.Sprintf("rushhour:stream:device:%s:viewers", deviceID)

		// Use pipeline for atomic operations
		pipe := st.redisClient.Pipeline()

		// Remove viewer from device's viewer set
		pipe.SRem(st.ctx, deviceViewersKey, connID)

		// Get resulting viewer count after removal
		countCmd := pipe.SCard(st.ctx, deviceViewersKey)

		// Execute pipeline
		_, err := pipe.Exec(st.ctx)
		if err != nil {
			logger.Warnf("Redis error removing connection %s from device %s: %v", connID, deviceID, err)
			continue
		}

		// Check if this was the last viewer
		if countCmd.Val() == 0 {
			devicesWithLastViewer = append(devicesWithLastViewer, deviceID)

			// Clean up empty device viewer set
			if err := st.redisClient.Del(st.ctx, deviceViewersKey).Err(); err != nil {
				logger.Warnf("Failed to clean up empty device viewers set for %s: %v", deviceID, err)
			}
		}
	}

	// Clean up connection's device set
	if err := st.redisClient.Del(st.ctx, connDevicesKey).Err(); err != nil {
		logger.Warnf("Failed to clean up connection devices set for %s: %v", connID, err)
	}

	logger.Debugf("Removed connection %s from %d devices via Redis (%d need stop_stream)", connID, len(devices), len(devicesWithLastViewer))
	return devicesWithLastViewer
}

// removeConnectionLocal handles local map-based connection removal (fallback mode)
func (st *StreamTracker) removeConnectionLocal(connID string) []string {
	st.mu.Lock()
	defer st.mu.Unlock()

	devicesWithLastViewer := make([]string, 0)

	// Get all devices for this connection
	devices := make([]string, 0, len(st.connDevices[connID]))
	for deviceID := range st.connDevices[connID] {
		devices = append(devices, deviceID)
	}

	// Remove connection from all devices
	for _, deviceID := range devices {
		if st.viewers[deviceID] != nil {
			delete(st.viewers[deviceID], connID)

			// Check if this was the last viewer
			if len(st.viewers[deviceID]) == 0 {
				delete(st.viewers, deviceID)
				devicesWithLastViewer = append(devicesWithLastViewer, deviceID)
			}
		}
	}

	// Clean up connection mapping
	delete(st.connDevices, connID)

	logger.Debugf("Removed connection %s from %d devices locally (%d need stop_stream)", connID, len(devices), len(devicesWithLastViewer))
	return devicesWithLastViewer
}

// GetStats returns tracking statistics
func (st *StreamTracker) GetStats() map[string]interface{} {
	// Use Redis for cross-instance stats if available
	if st.redisClient != nil {
		return st.getStatsRedis()
	}

	// Fall back to local stats
	return st.getStatsLocal()
}

// getStatsRedis returns Redis-backed statistics (cross-instance)
func (st *StreamTracker) getStatsRedis() map[string]interface{} {
	// Get all device viewer keys
	deviceKeys, err := st.redisClient.Keys(st.ctx, "rushhour:stream:device:*:viewers").Result()
	if err != nil {
		logger.Warnf("Redis error getting device keys for stats: %v", err)
		return st.getStatsLocal()
	}

	// Get all connection device keys
	connKeys, err := st.redisClient.Keys(st.ctx, "rushhour:stream:conn:*:devices").Result()
	if err != nil {
		logger.Warnf("Redis error getting connection keys for stats: %v", err)
		return st.getStatsLocal()
	}

	// Count total viewers across all devices
	totalViewers := int64(0)
	for _, key := range deviceKeys {
		count, err := st.redisClient.SCard(st.ctx, key).Result()
		if err != nil {
			logger.Warnf("Redis error getting viewer count for key %s: %v", key, err)
			continue
		}
		totalViewers += count
	}

	return map[string]interface{}{
		"active_devices":     len(deviceKeys),
		"active_connections": len(connKeys),
		"total_viewers":      totalViewers,
		"redis_backed":       true,
	}
}

// getStatsLocal returns local map-based statistics (single instance)
func (st *StreamTracker) getStatsLocal() map[string]interface{} {
	st.mu.RLock()
	defer st.mu.RUnlock()

	totalViewers := 0
	for _, viewers := range st.viewers {
		totalViewers += len(viewers)
	}

	return map[string]interface{}{
		"active_devices":     len(st.viewers),
		"active_connections": len(st.connDevices),
		"total_viewers":      totalViewers,
		"redis_backed":       false,
	}
}
