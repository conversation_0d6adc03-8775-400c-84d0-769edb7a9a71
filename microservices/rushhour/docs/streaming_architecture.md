# Streaming Architecture - Device Room Model

## Overview

rushhour implements a unified streaming model where gateways, FSA clients, and Onramp clients can all join the same device rooms, but with different roles and behaviors:

- **Gateways**: Publishers only (send device data, don't receive messages)
- **FSA Clients**: Field technicians (receive device data, send device commands)
- **Onramp Clients**: Technical support (receive device data, send device commands with potentially greater permissions)
- **Viewer Counting**: Only FSA and Onramp clients count toward viewership totals

## Room Structure

### Device Streaming Rooms
```
Pattern: org:<org_id>:device:<device_id>:<stream_type>

Examples: 
  org:123:device:abc456:stream_display  // Visual/display data stream
  org:123:device:abc456:stream_rms      // RMS measurement data stream
```

**Stream Types:**
- `stream_display`: Visual display data for device monitoring interfaces
- `stream_rms`: RMS (Root Mean Square) measurement data for analytical purposes

### Gateway Control Rooms
```
Pattern: org:<org_id>:gateway:<gateway_id>:control

Example: org:123:gateway:xyz789:control
```

## Streaming Flow

### 1. FSA/Onramp Client Joins Device Stream Room
```typescript
// FSA or Onramp client wants to watch device display data
socket.emit("join", "org:123:device:abc456:stream_display")

// Or watch RMS measurement data
socket.emit("join", "org:123:device:abc456:stream_rms")
```

**Server Response:**
1. Validates FSA/Onramp client permissions for specific stream type
2. Adds FSA/Onramp client to device room
3. Tracks as first viewer for this stream type (if applicable)
4. If first viewer → signals gateway to start streaming this type

### 2. Gateway Receives Stream Start Command
```json
// Sent to gateway control room: org:123:gateway:xyz789:control
{
  "action": "join_device_room",
  "device_id": "abc456", 
  "device_room": "org:123:device:abc456:stream_display",
  "stream_type": "stream_display"
}
```

**Gateway Response:**
1. Joins the specific device stream room
2. Starts collecting device data for this stream type
3. Publishes data to the device room

### 3. Data Publishing
```typescript
// Gateway publishes to device room (both gateway and FSA are in room)
socket.emit("device_message", {
  device_id: "abc456",
  stream_type: "stream_display",
  data: displayData
})
```

**Behavior:**
- FSA and Onramp clients receive the data
- Gateway does NOT receive its own messages (no message handlers)

### 4. FSA/Onramp Client Leaves Device Stream Room
```typescript
// FSA or Onramp client stops watching display stream
socket.emit("leave", "org:123:device:abc456:stream_display")
```

**Server Response:**
1. Removes FSA/Onramp client from room
2. Checks if last viewer for this stream type
3. If last viewer → signals gateway to stop this stream type

### 5. Gateway Receives Stream Stop Command
```json
// Sent to gateway control room
{
  "action": "leave_device_room",
  "device_id": "abc456",
  "device_room": "org:123:device:abc456:stream_display", 
  "stream_type": "stream_display"
}
```

**Gateway Response:**
1. Leaves the specific device stream room
2. Stops collecting device data for this stream type

## Independent Stream Management

**Important:** Each stream type is managed independently:

- A device can have `stream_display` active while `stream_rms` is inactive
- Different FSA/Onramp clients can subscribe to different stream types
- Gateway joins/leaves specific rooms based on stream type demand
- Viewer counts are tracked separately per stream type

**Example Scenario:**
```
Device ABC456 state:
├─ stream_display: 3 FSA/Onramp viewers → Gateway in room, actively streaming
└─ stream_rms: 0 FSA/Onramp viewers → Gateway not in room, not streaming
```

## Key Benefits

### Granular Stream Control
- Independent control of display vs. RMS data streams
- Efficient bandwidth usage (only stream what's being watched)
- Separate permission control per stream type

### Accurate Per-Type Viewer Counting
- Only FSA and Onramp clients counted as viewers
- Gateway presence doesn't affect viewer counts
- Stream start/stop based on actual viewership per type

### Efficient Resource Usage
- Streaming only when FSA/Onramp clients are watching specific stream types
- Automatic cleanup when no viewers for a stream type
- Gateway automatically joins/leaves as needed per stream type

### Clear Role Separation
- **Gateways**: Data producers (join rooms, publish data, no receivers)
- **FSA Clients**: Field technicians (join rooms, receive data, send device commands)
- **Onramp Clients**: Technical support (join rooms, receive data, send device commands with potentially greater permissions)

## Implementation Details

### Stream Key Format
```go
// Viewer tracking uses composite keys for stream type separation
streamKey := fmt.Sprintf("%s:%s", deviceID, streamType)
// Examples: "device123:stream_display", "device123:stream_rms"
```

### Viewer Tracking
```go
// Only FSA and Onramp clients are counted as viewers for specific stream types
if ctx.AuthInfo == nil || 
   (ctx.AuthInfo.ClientType != domain.ClientTypeFSA && ctx.AuthInfo.ClientType != domain.ClientTypeOnramp) {
    return fmt.Errorf("only FSA and Onramp clients can join device stream channels")
}

// Track viewers per stream type
isFirstViewer := streamTracker.AddViewer(streamKey, connID)
```

### Gateway Publishing
```go
// Gateway publishes to room it has joined for specific stream type
deviceRoom := GetDeviceStreamChannel(orgID, deviceID, streamType)
server.To(Room(deviceRoom)).Emit("device_message", envelope)
```

### Room Membership Examples
- **Display Stream Room**: `org:123:device:abc456:stream_display`
  - Members: Gateway (publisher) + FSA/Onramp clients (subscribers)
- **RMS Stream Room**: `org:123:device:abc456:stream_rms`
  - Members: Gateway (publisher) + FSA/Onramp clients (subscribers)
- **Gateway Control Room**: `org:123:gateway:xyz789:control`
  - Members: Gateway only (for control commands)

## Stream Type Permissions

Different stream types can have different permission requirements:

```go
// Permission checking per stream type
streamAction := "stream_display"  // or "stream_rms"
if err := permissionChecker.CheckDeviceAccess(ctx, deviceID, streamAction); err != nil {
    return fmt.Errorf("access denied to device %s stream type %s", deviceID, streamType)
}
```

## Command Permissions (Future Implementation)

**Current Status: POC Phase**
- Both FSA and Onramp clients can send device commands
- Authorization currently relies on JWT/Token auth and basic device access permissions
- Onramp users (technical support) may have greater permissions than field technicians

**Planned Implementation:**
- Granular command-level permissions based on user roles
- Different permission levels for different command types:
  - Read-only commands (device status, configuration viewing)
  - Control commands (parameter changes, operational controls)  
  - Administrative commands (firmware updates, factory resets)
- Role-based access control (RBAC) with command-specific permissions

```go
// Future granular permission checking (not yet implemented)
switch envelope.CommandType {
case "device_status", "read_config":
    // Basic device access required
case "set_parameters", "control_operation":
    // Device control permissions required  
case "firmware_update", "factory_reset":
    // Administrative permissions required
}
```

This architecture provides granular stream control while maintaining clean separation of concerns and efficient resource usage. 