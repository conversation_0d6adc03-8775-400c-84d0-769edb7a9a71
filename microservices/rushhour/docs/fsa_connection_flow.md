# Field Service App (FSA) Connection Flow - Rush Hour

This document outlines the connection and authorization flow for the **Field Service App (FSA)**, a Flutter/Dart-based client, connecting to the Rush Hour (RH) Socket.IO server.

**Development and Testing Guide** - All code examples are based on tested, working implementations validated through end-to-end testing and ready for integration development.

---

## Overview

- The FSA authenticates using a **JWT** (JSON Web Token) via an existing identity provider
- The FSA connects to RH via the Socket.IO namespace: `/auth/fsa`
- RH validates the JWT and determines the user's `user_id` and the list of organizations they belong to
- RH registers the FSA socket for direct messaging capability
- The FSA can request to join device streaming rooms and send device commands via direct messaging

---

## 1. FSA Authentication Requirements

**Critical: FSA authentication has been a common source of integration issues. Follow these requirements:**

**NOTE: This is an opinionated document from an AI that has not actually tested the Dart code.  The overall concepts are fine, but use your experience, too.**

### Correct FSA Client Implementation (Dart/Flutter)

```dart
import 'package:socket_io_client/socket_io_client.dart' as IO;

final socket = IO.io(
  'https://your-rushhour-server.com/auth/fsa',  // Must use /auth/fsa namespace
  IO.OptionBuilder()
    .setTransports(['websocket', 'polling'])  // WebSocket preferred, polling fallback
    .setAuth({ 'token': jwt })   // REQUIRED: JWT in auth object with 'token' key
    .build(),
);

socket.onConnect((_) {
  print('Connected to Socket.IO');
});

// REQUIRED: Listen for auth_success event (not connect)
socket.on('auth_success', (data) {
  print('FSA authenticated successfully');
  print('user_id: ${data['user_id']}');
  print('expires_at: ${data['expires_at']}');
  // Now ready for device commands and streaming
});

socket.on('connect_error', (error) {
  print('Connection error: $error');
});

socket.on('error', (msg) {
  print('Auth error: $msg');
});

// Handle device responses
socket.on('device_message', (envelope) {
  print('Received device response: $envelope');
  // Process device response from gateway
});
```

### Common FSA Authentication Mistakes

**DO NOT use query parameters:**
```dart
// WRONG: Auth data in URL query parameters
final socket = IO.io('https://your-rushhour-server.com/auth/fsa?token=$jwt');
```

**DO NOT rely on connect event for auth:**
```dart
// WRONG: connect event fires before authentication
socket.onConnect((_) {
  // Authentication not guaranteed here!
});
```

**Transport Configuration Notes:**
```dart
// NORMAL USE: WebSocket with polling fallback (recommended)
.setTransports(['websocket', 'polling'])

// TESTING ONLY: Polling only for deterministic test timing
.setTransports(['polling'])  // Use only in test environments for consistent timing
```

---

## 2. RH Authentication Handler (Server Side - Go)

```go
server.OnConnect("/auth/fsa", func(conn socketio.Conn) error {
    token := conn.Auth()["token"]
    authInfo, err := validateUserJWT(token)
    if err != nil {
        return conn.Emit("error", "unauthorized")
    }

    ctx := &ConnectionContext{
        AuthInfo: &AuthInfo{
            ClientType: ClientTypeFSA,
            OrgID:      authInfo.PrimaryOrgID,
            UserID:     authInfo.UserID,
            ExpiresAt:  authInfo.ExpiresAt,
        },
    }
    
    // Register socket for direct messaging
    service.RegisterSocket(conn, ctx)

    return conn.Emit("auth_success", map[string]interface{}{
        "user_id":    authInfo.UserID,
        "expires_at": authInfo.ExpiresAt.Format(time.RFC3339),
    })
})
```

---

## 3. Token Validation (Placeholder)

```go
type OrganizationInfo struct {
    ID   string `json:"id"`
    Name string `json:"name"`
}

type FSAAuthInfo struct {
    PrimaryOrgID  string
    UserID        string
    Organizations []OrganizationInfo
}

func validateUserJWT(token string) (*FSAAuthInfo, error) {
    // Validate JWT and extract user/org details
    return &FSAAuthInfo{
        PrimaryOrgID: "org789",
        UserID:       "user123",
        Organizations: []OrganizationInfo{
            {ID: "org789", Name: "Org A"},
            {ID: "org456", Name: "Org B"},
        },
    }, nil
}
```

---

This flow securely authenticates the FSA app, registers it for direct socket messaging, and returns user identity information. The FSA can then send device commands via direct messaging and join device streaming rooms.

---

## FSA Authentication Quick Reference

### Working Pattern
1. JWT from broker authentication
2. WebSocket with polling fallback (or polling only for tests)
3. Auth data in `.auth` object with `"token"` key
4. Connect to `/auth/fsa` namespace
5. Wait for `auth_success` event (not `connect`)
6. Use `device_request` to send commands
7. Listen for `device_message` for responses

### Common Issues
- **Query parameters**: Server expects auth object
- **Wrong namespace**: Must use `/auth/fsa`
- **Wrong event**: Wait for `auth_success`, not `connect`
- **Test timing**: Use polling only in test environments for deterministic results

### Test Credentials (DEV environment)
```
Username: <EMAIL>
Password: puppies1234
Device ID: 87d94e14-e804-58b3-9f8c-a02e5de90aeb
```

