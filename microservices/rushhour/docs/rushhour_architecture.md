# Rush Hour (RH) - Developer Implementation Guide

**Development and Testing Guide** - All code examples are based on tested, working implementations validated through end-to-end testing and ready for integration development.

## Overview

Rush Hour (RH) is a horizontally-scalable Go microservice for real-time communication between:

- **Gateways** (each proxying \~500 devices)
- **FSA clients** (Field Service App, Onramp UI)

It uses **Socket.IO** with a **Redis adapter** for scalable real-time messaging. Messages are routed based on envelope metadata and are serialized using **Protobuf**.

---

## Critical Authentication Requirements

**Authentication has been a common source of integration issues. Follow these requirements exactly:**

### FSA Client Authentication (JWT)
```javascript
// CORRECT: Auth data in the Socket.IO auth object
const socket = io("https://your-rushhour-server.com/auth/fsa", {
  transports: ["websocket", "polling"],  // WebSocket preferred, polling fallback
  auth: {
    token: jwt_token  // Must be "token" key in auth object
  }
});

socket.on("auth_success", (data) => {
  console.log("FSA authenticated:", data);
  // Ready for device communication
});
```

### Gateway Client Authentication (Machine Key + API Key)
```javascript
// CORRECT: Auth data in the Socket.IO auth object  
const socket = io("https://your-rushhour-server.com/auth/gateway", {
  transports: ["websocket", "polling"],  // WebSocket preferred, polling fallback
  auth: {
    machine_key: "your_machine_key",
    api_key: "your_api_key"
  }
});

socket.on("gateway_init", (data) => {
  console.log("Gateway authenticated:", data);
  // Ready for device communication
});
```

### Common Authentication Mistakes

**DO NOT use query parameters for auth:**
```javascript
// WRONG: Auth in query parameters (will fail)
const socket = io("https://your-rushhour-server.com/auth/fsa?token=" + jwt_token);
```

**Transport Configuration Note:**
```javascript
// NORMAL USE: WebSocket with polling fallback (recommended)
const socket = io("https://your-rushhour-server.com/auth/fsa", {
  transports: ["websocket", "polling"]
});

// TESTING ONLY: Polling only for deterministic test timing
const socket = io("https://your-rushhour-server.com/auth/fsa", {
  transports: ["polling"]  // Use only in test environments for consistent timing
});
```

**Authentication Event Differences:**
- FSA clients receive `auth_success` event upon successful authentication
- Gateway clients receive `gateway_init` event upon successful authentication
- Allow 10-15 seconds for authentication timeout handling

---

## Technology Stack

- **Language**: Go
- **Socket.IO Library**: [`github.com/zishang520/socket.io`](https://github.com/zishang520/socket.io) (Redis adapter supported)
- **Serialization**: Protobuf
- **Scaling**: Redis pub/sub adapter + Kubernetes

---

## Communication Architecture

### Direct Socket Messaging

RH uses **Socket.IO Session IDs** for direct 1:1 communication between clients and gateways:

- **Device Commands**: FSA → RH → Gateway (direct socket messaging)
- **Device Responses**: Gateway → RH → FSA (direct socket messaging)  
- **Command Routing**: RH enriches envelopes with missing UUIDs, Session IDs, Gateway IDs

### Streaming Rooms (Broadcasting)

Rooms are used only for broadcasting streaming data to multiple viewers:

| Room                                             | Purpose                                            |
| ------------------------------------------------ | -------------------------------------------------- |
| `org:<org_id>:device:<device_id>:stream_display` | Broadcasts visual display frames from devices     |
| `org:<org_id>:device:<device_id>:stream_rms`     | Broadcasts RMS sensor data from devices           |

> **Key Benefits**: 
> - **Efficient 1:1 routing** via direct socket messaging
> - **Scalable broadcasting** via rooms for streaming data  
> - **No unnecessary room joins** for simple device commands

---

## Message Envelope (Binary-Only)

To avoid base64 overhead for large messages, RH uses a **pure binary transport**. The envelope is a serialized protobuf that contains routing metadata and the payload.

### Envelope (Protobuf)

```proto
enum EnvelopeType {
  UNKNOWN = 0;
  WRAPPER_COMMAND = 1;
  WRAPPER_RESPONSE = 2;
}

enum OriginType {
  ORIGIN_UNKNOWN = 0;
  ORIGIN_GATEWAY = 1;
  ORIGIN_APP = 2;
  ORIGIN_RH = 3;
  ORIGIN_ONRAMP = 4;
}

message SocketEnvelope {
  EnvelopeType type = 1;
  uint32 request_id = 2;
  string user_id = 3;
  string device_id = 4;
  string organization_id = 5;
  OriginType origin = 6;
  bytes payload = 7;
}
```

> Socket.IO's binary event support allows sending `[]byte` directly.

---

## RH Responsibilities

### 1. Namespace-Based Authentication

**Critical: RushHour uses namespace-based authentication with different handlers for FSA and Gateway clients.**

```go
// FSA Authentication Namespace: /auth/fsa
server.OnConnect("/auth/fsa", func(conn socketio.Conn) error {
    token, ok := conn.Auth()["token"].(string)
    if !ok || token == "" {
        return conn.Emit("error", "missing JWT token")
    }

    authInfo, err := validateUserJWT(token)
    if err != nil {
        return conn.Emit("error", "unauthorized")
    }

    ctx := &ConnectionContext{
        AuthInfo: &AuthInfo{
            ClientType: ClientTypeFSA,
            OrgID:      authInfo.PrimaryOrgID,
            UserID:     authInfo.UserID,
            ExpiresAt:  authInfo.ExpiresAt,
        },
    }
    
    service.RegisterSocket(conn, ctx)
    
    // Send auth_success event (FSA expects this specific event)
    return conn.Emit("auth_success", map[string]interface{}{
        "user_id":    authInfo.UserID,
        "expires_at": authInfo.ExpiresAt.Format(time.RFC3339),
    })
})

// Gateway Authentication Namespace: /auth/gateway  
server.OnConnect("/auth/gateway", func(conn socketio.Conn) error {
    machineKey, machineKeyOk := conn.Auth()["machine_key"].(string)
    apiKey, apiKeyOk := conn.Auth()["api_key"].(string)
    
    if !machineKeyOk || machineKey == "" || !apiKeyOk || apiKey == "" {
        return conn.Emit("error", "missing machine_key or api_key")
    }

    authInfo, err := validateGatewayAuth(machineKey, apiKey)
    if err != nil {
        return conn.Emit("error", "unauthorized")
    }

    ctx := &ConnectionContext{
        AuthInfo: &AuthInfo{
            ClientType: ClientTypeGateway,
            OrgID:      authInfo.OrgID,
            GatewayID:  authInfo.GatewayID,
        },
    }
    
    service.RegisterSocket(conn, ctx)

    // Send gateway_init event (Gateway expects this specific event)
    return conn.Emit("gateway_init", map[string]string{
        "status": "authenticated",
    })
})
```

### 2. Handle Device Communication Events

```go
// Handle device requests from FSA clients
server.OnEvent("/auth/fsa", "device_request", func(conn socketio.Conn, envelope *SocketEnvelope) {
  if !rh.CheckPermission(conn, envelope.DeviceId, envelope.Type) {
    conn.Emit("error", "unauthorized")
    return
  }

  // Get gateway socket ID for direct messaging
  gatewaySocketID := rh.socketRegistry.GetGatewayForDevice(envelope.DeviceId)
  if gatewaySocketID == "" {
    conn.Emit("error", "gateway not available")
    return
  }

  // Enrich envelope with session ID, user ID, and org ID for response routing
  envelope.SessionId = string(conn.ID())
  envelope.UserId = rh.GetUserID(conn)
  envelope.OrganizationId = rh.GetOrgID(conn)
  
  // Direct socket messaging to specific gateway
  server.To(socketio.Room(gatewaySocketID)).Emit("device_request", envelope)
})

// Handle device responses from Gateway clients
server.OnEvent("/auth/gateway", "device_message", func(conn socketio.Conn, envelope *SocketEnvelope) {
  // Route response back to FSA using SessionId for 1:1 delivery
  if envelope.SessionId != "" {
    server.To(socketio.Room(envelope.SessionId)).Emit("device_message", envelope)
  }
})
```

### 3. Stream Viewer Management

```go
func (rh *RushHour) OnWatchStream(conn socketio.Conn, deviceID, streamType string) {
  room := fmt.Sprintf("org:%s:device:%s:%s", orgID, deviceID, streamType)
  conn.Join(room)

  if rh.streamTracker.AddViewer(deviceID, streamType, conn.ID()) {
    env := &pb.SocketEnvelope{
      Type: pb.START_STREAM,
      DeviceId: deviceID,
      OrganizationId: orgID,
      Origin: pb.ORIGIN_RH,
    }
    data, _ := proto.Marshal(env)
    rh.EmitToGateway(deviceID, "device_request", data)
  }
}

func (rh *RushHour) OnDisconnect(conn socketio.Conn) {
  for _, info := range rh.streamTracker.GetStreamsFor(conn.ID()) {
    if rh.streamTracker.RemoveViewer(info.DeviceID, info.StreamType, conn.ID()) {
      env := &pb.SocketEnvelope{
        Type: pb.STOP_STREAM,
        DeviceId: info.DeviceID,
        OrganizationId: info.OrgID,
        Origin: pb.ORIGIN_RH,
      }
      data, _ := proto.Marshal(env)
      rh.EmitToGateway(info.DeviceID, "device_request", data)
    }
  }
}
```

---

## Redis Adapter Setup

```go
redisURL := os.Getenv("RH_REDIS")
if redisURL == "" {
  log.Fatal("RH_REDIS not set")
}

server := socketio.NewServer(nil)
adapter, err := socketio.NewRedisAdapter(redisURL, nil)
if err != nil {
  log.Fatal(err)
}
server.SetAdapter(adapter)
```

---

## Protobuf Dispatching

```go
var protoRegistry = map[pb.EnvelopeType]func() proto.Message{
  pb.LOG_RESPONSE: func() proto.Message { return &pb.LogResponse{} },
  pb.FRAME_DISPLAY: func() proto.Message { return &pb.StreamFrameDisplay{} },
  pb.FRAME_RMS: func() proto.Message { return &pb.StreamFrameRMS{} },
}

func decodeEnvelope(env *pb.SocketEnvelope) (proto.Message, error) {
  factory := protoRegistry[env.Type]
  msg := factory()
  return msg, proto.Unmarshal(env.Payload, msg)
}
```

---

## StreamTracker Example

```go
type streamKey struct {
  deviceID   string
  streamType string
}

type StreamTracker struct {
  viewers map[streamKey]map[string]struct{} // streamKey → connIDs
  mu      sync.Mutex
}

func (st *StreamTracker) AddViewer(deviceID, streamType, connID string) bool {
  st.mu.Lock()
  defer st.mu.Unlock()
  key := streamKey{deviceID, streamType}
  if st.viewers[key] == nil {
    st.viewers[key] = make(map[string]struct{})
  }
  st.viewers[key][connID] = struct{}{}
  return len(st.viewers[key]) == 1
}

func (st *StreamTracker) RemoveViewer(deviceID, streamType, connID string) bool {
  st.mu.Lock()
  defer st.mu.Unlock()
  key := streamKey{deviceID, streamType}
  delete(st.viewers[key], connID)
  return len(st.viewers[key]) == 0
}

type StreamInfo struct {
  DeviceID   string
  StreamType string
  OrgID      string
}

func (st *StreamTracker) GetStreamsFor(connID string) []StreamInfo {
  st.mu.Lock()
  defer st.mu.Unlock()
  var result []StreamInfo
  for key, viewers := range st.viewers {
    if _, ok := viewers[connID]; ok {
      result = append(result, StreamInfo{
        DeviceID:   key.deviceID,
        StreamType: key.streamType,
      })
    }
  }
  return result
}
```

---

## Working Integration Patterns Summary

### Complete End-to-End Flow (Based on Tested Implementation)

1. **FSA Authentication**: JWT token in auth object → `auth_success` event
2. **Gateway Authentication**: machine_key/api_key in auth object → `gateway_init` event  
3. **FSA sends device command**: `device_request` event with SocketEnvelope
4. **RushHour enriches**: Adds SessionId, UserId, OrganizationId
5. **Gateway receives**: `device_request` event with enriched envelope
6. **Gateway responds**: `device_message` event preserving SessionId
7. **FSA receives**: `device_message` event routed by SessionId

### Authentication Troubleshooting Guide

| Problem | Symptom | Solution |
|---------|---------|----------|
| Connection timeout | Client never connects | Check network connectivity and server availability |
| Auth failure | Connection but no auth event | Ensure auth data in `.auth` object, not query params |
| Wrong auth event | Connect but waiting forever | FSA expects `auth_success`, Gateway expects `gateway_init` |
| Message not routed | Commands sent but not received | Check namespace: `/auth/fsa` vs `/auth/gateway` |
| Response not returned | Gateway responds but FSA doesn't get it | Gateway must preserve `SessionId` from request |
| Test timing issues | Inconsistent test results | Use `transports: ["polling"]` for deterministic testing |

### Transport Configuration Guidelines

**For normal application use:**
- JavaScript: `transports: ["websocket", "polling"]` (WebSocket preferred, polling fallback)
- Dart/Flutter: `.setTransports(['websocket', 'polling'])`
- Go: `opts.SetTransports(types.NewSet(transports.WebSocket, transports.Polling))`

**For test environments (deterministic timing):**
- JavaScript: `transports: ["polling"]`
- Dart/Flutter: `.setTransports(['polling'])`
- Go: `opts.SetTransports(types.NewSet(transports.Polling))`

### Required Authentication Structure

**FSA (JWT):**
```json
{
  "auth": {
    "token": "jwt_token_here"
  }
}
```

**Gateway (Machine Key + API Key):**
```json
{
  "auth": {
    "machine_key": "machine_key_here",
    "api_key": "api_key_here"
  }
}
```

