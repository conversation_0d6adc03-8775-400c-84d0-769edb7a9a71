package main

import (
	"context"
	"errors"
	"net/http"
	"os"
	"syscall"
	"testing"
	"time"

	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/mocks"
)

// MockServer implements the Server interface for testing
type MockServer struct {
	ListenErr    error
	ShutdownErr  error
	ListenCalled bool
	ShutdownCtx  context.Context
}

func (m *MockServer) ListenAndServe() error {
	m.ListenCalled = true
	return m.ListenErr
}

func (m *MockServer) Shutdown(ctx context.Context) error {
	m.ShutdownCtx = ctx
	return m.ShutdownErr
}

// TestDefaultSignalChan tests the signal channel setup
func TestDefaultSignalChan(t *testing.T) {
	sigCh := DefaultSignalChan()

	if sigCh == nil {
		t.Fatal("Expected non-nil signal channel")
	}

	// Test that signal channel exists and is ready to receive
	// We can't easily test sending to it since it's receive-only from our perspective
	select {
	case <-sigCh:
		// Should not receive anything immediately
		t.Error("Signal channel should not have any signals initially")
	case <-time.After(10 * time.Millisecond):
		// This is expected - no signals should be present
	}
}

// TestDefaultServer tests the http.Server wrapper
func TestDefaultServer(t *testing.T) {
	addr := ":8080"
	handler := http.NewServeMux()

	server := DefaultServer(addr, handler)

	if server == nil {
		t.Fatal("Expected non-nil server")
	}

	// Verify it implements our Server interface
	var _ Server = server
}

// TestRun_Success tests the successful execution path
func TestRun_Success(t *testing.T) {
	ctx := context.Background()
	healthPort := "8081"
	addr := ":8080"

	// Mock dependencies
	mockServer := &MockServer{}
	mockHealthzServer := &mocks.FakeHealthzServer{}
	signalCh := make(chan os.Signal, 1)

	// Override global functions for testing
	originalHealthzNewServer := healthzNewServer
	healthzNewServer = func(port string) healthz.HealthzServer {
		return mockHealthzServer
	}
	defer func() { healthzNewServer = originalHealthzNewServer }()

	// Create mock connections and batch
	conns := mocks.FakeConns()
	batch := mocks.FakeBatcherWithOptions()

	// Send signal to trigger shutdown
	go func() {
		time.Sleep(50 * time.Millisecond)
		signalCh <- syscall.SIGTERM
	}()

	err := Run(
		ctx,
		healthPort,
		addr,
		func(context.Context) *connect.Connections { return conns },
		func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher { return batch },
		func(string, http.Handler) Server { return mockServer },
		func() <-chan os.Signal { return signalCh },
	)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}

	if !mockServer.ListenCalled {
		t.Error("Expected server ListenAndServe to be called")
	}

	if !mockHealthzServer.SetBootCalled {
		t.Error("Expected SetBootComplete to be called")
	}

	if !mockHealthzServer.SetReadyCalled {
		t.Error("Expected SetReady to be called")
	}

	if !mockHealthzServer.SetNotReadyCalled {
		t.Error("Expected SetNotReady to be called")
	}
}

// TestRun_ContextCancellation tests graceful shutdown via context cancellation
func TestRun_ContextCancellation(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	healthPort := "8081"
	addr := ":8080"

	// Mock dependencies
	mockServer := &MockServer{}
	mockHealthzServer := &mocks.FakeHealthzServer{}
	signalCh := make(chan os.Signal, 1)

	// Override global functions for testing
	originalHealthzNewServer := healthzNewServer
	healthzNewServer = func(port string) healthz.HealthzServer {
		return mockHealthzServer
	}
	defer func() { healthzNewServer = originalHealthzNewServer }()

	// Create mock connections and batch
	conns := mocks.FakeConns()
	batch := mocks.FakeBatcherWithOptions()

	// Cancel context to trigger shutdown
	go func() {
		time.Sleep(50 * time.Millisecond)
		cancel()
	}()

	err := Run(
		ctx,
		healthPort,
		addr,
		func(context.Context) *connect.Connections { return conns },
		func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher { return batch },
		func(string, http.Handler) Server { return mockServer },
		func() <-chan os.Signal { return signalCh },
	)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
}

// TestRun_ServerShutdownError tests error handling during server shutdown
func TestRun_ServerShutdownError(t *testing.T) {
	ctx := context.Background()
	healthPort := "8081"
	addr := ":8080"

	expectedErr := errors.New("shutdown failed")
	mockServer := &MockServer{ShutdownErr: expectedErr}
	mockHealthzServer := &mocks.FakeHealthzServer{}
	signalCh := make(chan os.Signal, 1)

	// Override global functions for testing
	originalHealthzNewServer := healthzNewServer
	healthzNewServer = func(port string) healthz.HealthzServer {
		return mockHealthzServer
	}
	defer func() { healthzNewServer = originalHealthzNewServer }()

	// Create mock connections and batch
	conns := mocks.FakeConns()
	batch := mocks.FakeBatcherWithOptions()

	// Send signal to trigger shutdown
	go func() {
		time.Sleep(50 * time.Millisecond)
		signalCh <- syscall.SIGTERM
	}()

	err := Run(
		ctx,
		healthPort,
		addr,
		func(context.Context) *connect.Connections { return conns },
		func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher { return batch },
		func(string, http.Handler) Server { return mockServer },
		func() <-chan os.Signal { return signalCh },
	)

	if err != expectedErr {
		t.Errorf("Expected error %v, got: %v", expectedErr, err)
	}

	// Verify shutdown context has a timeout
	if mockServer.ShutdownCtx == nil {
		t.Error("Expected shutdown context to be passed")
	} else {
		deadline, hasDeadline := mockServer.ShutdownCtx.Deadline()
		if !hasDeadline {
			t.Error("Expected shutdown context to have deadline")
		} else if time.Until(deadline) > 30*time.Second {
			t.Error("Expected shutdown timeout to be 30 seconds or less")
		}
	}
}
