package auth

import (
	"fmt"
	"time"

	"github.com/zishang520/socket.io/v2/socket"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// validateGatewayAuthFunc is a variable to allow dependency injection for testing
var validateGatewayAuthFunc = authorizer.ValidateGatewayAuth

// GatewayAuthenticator handles authentication for gateway clients
type GatewayAuthenticator struct {
	db connect.DatabaseExecutor
}

// NewGatewayAuthenticator creates a new gateway authenticator
func NewGatewayAuthenticator(db connect.DatabaseExecutor) *GatewayAuthenticator {
	return &GatewayAuthenticator{
		db: db,
	}
}

// ValidateGatewayAuth validates a gateway using machine key and API key (matching broker pattern)
func (ga *GatewayAuthenticator) ValidateGatewayAuth(machineKey, apiKey string) (*domain.AuthInfo, error) {
	// Use shared gateway authentication validation (MachineKey + APIKey)
	gatewayInfo, err := validateGatewayAuthFunc(ga.db, machine<PERSON><PERSON>, api<PERSON><PERSON>)
	if err != nil {
		logger.Warnf("Invalid gateway credentials: %v", err)
		return nil, fmt.Errorf("invalid gateway credentials")
	}

	// Convert to our unified domain model
	authInfo := &domain.AuthInfo{
		ClientType: domain.ClientTypeGateway,
		GatewayID:  gatewayInfo.GatewayID,
		OrgID:      gatewayInfo.OrgID,
	}

	return authInfo, nil
}

// SocketInterface defines the minimal socket interface needed for authentication
type SocketInterface interface {
	Id() socket.SocketId
	Emit(event string, data ...interface{}) error
}

// HandleGatewayAuth processes gateway authentication using machine key and API key
// Works with service socket registry for direct messaging
func (ga *GatewayAuthenticator) HandleGatewayAuth(conn SocketInterface, machineKey, apiKey string, service interface{}) error {
	if machineKey == "" {
		return fmt.Errorf("missing machine_key")
	}
	if apiKey == "" {
		return fmt.Errorf("missing api_key")
	}

	// Validate machine key and API key to get gateway info
	authInfo, err := ga.ValidateGatewayAuth(machineKey, apiKey)
	if err != nil {
		return fmt.Errorf("unauthorized: %v", err)
	}

	// Create connection context with socket-centric structure
	ctx := &domain.ConnectionContext{
		SocketID:    conn.Id(),
		ClientType:  domain.ClientTypeGateway,
		AuthInfo:    authInfo,
		OrgID:       authInfo.OrgID,
		ConnectedAt: time.Now(),
	}

	// Register with service socket registry and Redis for cross-instance routing
	if svc, ok := service.(interface {
		StoreConnectionContext(socketID string, ctx *domain.ConnectionContext) error
		RegisterGateway(gatewayID, socketID string)
		RecoverActiveStreamsForGateway(gatewayID string)
	}); ok {

		// Store connection context in Redis-backed storage
		if err := svc.StoreConnectionContext(string(conn.Id()), ctx); err != nil {
			logger.Warnf("Failed to store connection context for gateway %s: %v", conn.Id(), err)
		}

		// Register gateway for device routing
		svc.RegisterGateway(authInfo.GatewayID, string(conn.Id()))

		// Recover any active streams for this gateway (reconnection resilience)
		svc.RecoverActiveStreamsForGateway(authInfo.GatewayID)
	}

	// Send gateway initialization confirmation
	// Direct socket messaging means gateway doesn't need UUIDs for room construction
	initData := map[string]string{
		"status": "authenticated",
	}

	conn.Emit("gateway_init", initData)
	logger.Infof("Gateway authenticated: gateway_id=%s, org_id=%s", authInfo.GatewayID, authInfo.OrgID)

	return nil
}
