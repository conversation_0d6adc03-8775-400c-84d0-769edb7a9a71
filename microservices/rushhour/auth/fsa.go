package auth

import (
	"fmt"
	"time"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// validateJWTAndGetPermissionsFunc is a variable to allow dependency injection for testing
var validateJWTAndGetPermissionsFunc = authorizer.ValidateJWTAndGetPermissions

// FSAAuthenticator handles Field Service App authentication flow
type FSAAuthenticator struct {
	db connect.DatabaseExecutor
}

// NewFSAAuthenticator creates a new FSA authenticator
func NewFSAAuthenticator(db connect.DatabaseExecutor) *FSAAuthenticator {
	return &FSAAuthenticator{
		db: db,
	}
}

// ValidateUserJWT validates a user JWT token using the shared authorizer library
func (fa *FSAAuthenticator) ValidateUserJWT(token string, pg connect.DatabaseExecutor) (*domain.AuthInfo, error) {
	if token == "" {
		return nil, fmt.Errorf("empty token")
	}

	// Use shared JWT validation and permission loading
	userPermissions, err := validateJWTAndGetPermissionsFunc(pg, token)
	if err != nil {
		logger.Infof("JWT validation failed: %v", err)
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	logger.Infof("JWT validated successfully for user: %s", userPermissions.UserID)

	// Get organization ID from first permission (assumes user has at least one permission)
	var orgID string
	if len(userPermissions.Permissions) > 0 {
		orgID = userPermissions.Permissions[0].OrganizationID
	}

	// Convert to our unified domain model
	return &domain.AuthInfo{
		ClientType:  domain.ClientTypeFSA,
		UserID:      userPermissions.UserID,
		OrgID:       orgID,
		Permissions: userPermissions,
		ExpiresAt:   time.Now().Add(24 * time.Hour), // TODO: Extract actual expiration from JWT or DB
	}, nil
}

// HandleFSAAuth processes FSA authentication data from connection event
// Works with service socket registry for direct messaging
func (fa *FSAAuthenticator) HandleFSAAuth(client SocketInterface, authData map[string]interface{}, pg connect.DatabaseExecutor, service interface{}) error {
	// Extract token from auth data
	token, ok := authData["token"].(string)
	if !ok {
		logger.Infof("FSA connection missing token: %s", client.Id())
		return client.Emit("error", "missing token")
	}

	// Validate the JWT and load permissions from database
	authInfo, err := fa.ValidateUserJWT(token, pg)
	if err != nil {
		logger.Infof("FSA authentication failed for %s: %v", client.Id(), err)
		return client.Emit("error", "unauthorized")
	}

	// Create connection context with socket-centric structure
	ctx := &domain.ConnectionContext{
		SocketID:    client.Id(),
		ClientType:  domain.ClientTypeFSA,
		AuthInfo:    authInfo,
		OrgID:       authInfo.OrgID,
		ConnectedAt: time.Now(),
	}

	// Register with service socket registry and Redis for cross-instance access
	if svc, ok := service.(interface {
		StoreConnectionContext(socketID string, ctx *domain.ConnectionContext) error
	}); ok {
		// Store connection context in Redis-backed storage
		if err := svc.StoreConnectionContext(string(client.Id()), ctx); err != nil {
			logger.Warnf("Failed to store connection context for FSA %s: %v", client.Id(), err)
		}
	}

	logger.Infof("FSA authenticated: %s (user:%s)", client.Id(), authInfo.UserID)

	// Send success response
	// Direct socket messaging means FSA doesn't need private channel info
	return client.Emit("auth_success", map[string]interface{}{
		"user_id":    authInfo.UserID,
		"expires_at": authInfo.ExpiresAt.Format(time.RFC3339),
	})
}
