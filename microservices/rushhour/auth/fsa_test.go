package auth

import (
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/zishang520/socket.io/v2/socket"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// MockFSASocketInterface represents what we need from socket.Socket for testing
type MockFSASocketInterface interface {
	Id() socket.SocketId
	Emit(event string, data ...interface{}) error
}

// MockFSASocket for FSA testing
type MockFSASocket2 struct {
	id           string
	emittedData  map[string]interface{}
	emittedEvent string
	emitError    error
}

func NewMockFSASocket2(id string) MockFSASocketInterface {
	return &MockFSASocket2{
		id:          id,
		emittedData: make(map[string]interface{}),
	}
}

func (m *MockFSASocket2) Id() socket.SocketId {
	return socket.SocketId(m.id)
}

func (m *MockFSASocket2) Emit(event string, data ...interface{}) error {
	m.emittedEvent = event
	if len(data) > 0 {
		m.emittedData[event] = data[0]
	}
	return m.emitError
}

// MockFSAService for testing FSA authentication
type MockFSAService2 struct {
	StoreConnectionContextCalled bool
	StoreContextError            error
	StoredContext                *domain.ConnectionContext
	StoredSocketID               string
}

func (m *MockFSAService2) StoreConnectionContext(socketID string, ctx *domain.ConnectionContext) error {
	m.StoreConnectionContextCalled = true
	m.StoredSocketID = socketID
	m.StoredContext = ctx
	return m.StoreContextError
}

func TestNewFSAAuthenticator2(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	auth := NewFSAAuthenticator(db)

	if auth == nil {
		t.Fatal("Expected non-nil FSA authenticator")
	}

	if auth.db != db {
		t.Error("Expected database executor to be set correctly")
	}
}

func TestFSAAuthenticator_ValidateUserJWT_Success2(t *testing.T) {
	db := &mocks.FakeDBExecutor{}

	// Mock successful JWT validation
	expectedPermissions := &authorizer.UserPermissions{
		UserID: "user123",
		Permissions: []authorizer.Permission{
			{
				Scope:          "organization",
				ScopeID:        "org456",
				OrganizationID: "org456",
				Permissions:    []string{"device.read"},
			},
		},
	}

	// Override the ValidateJWTAndGetPermissions function for testing
	originalValidateJWT := validateJWTAndGetPermissionsFunc
	validateJWTAndGetPermissionsFunc = func(db connect.DatabaseExecutor, token string) (*authorizer.UserPermissions, error) {
		if token == "valid_jwt_token" {
			return expectedPermissions, nil
		}
		return nil, errors.New("invalid token")
	}
	defer func() { validateJWTAndGetPermissionsFunc = originalValidateJWT }()

	auth := NewFSAAuthenticator(db)
	authInfo, err := auth.ValidateUserJWT("valid_jwt_token", db)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if authInfo == nil {
		t.Fatal("Expected non-nil auth info")
	}

	if authInfo.ClientType != domain.ClientTypeFSA {
		t.Errorf("Expected client type FSA, got: %v", authInfo.ClientType)
	}

	if authInfo.UserID != expectedPermissions.UserID {
		t.Errorf("Expected user ID %s, got: %s", expectedPermissions.UserID, authInfo.UserID)
	}

	if authInfo.OrgID != expectedPermissions.Permissions[0].OrganizationID {
		t.Errorf("Expected org ID %s, got: %s", expectedPermissions.Permissions[0].OrganizationID, authInfo.OrgID)
	}

	if authInfo.Permissions != expectedPermissions {
		t.Error("Expected permissions to be preserved")
	}

	if authInfo.ExpiresAt.Before(time.Now()) {
		t.Error("Expected expiration time to be in the future")
	}
}

func TestFSAAuthenticator_ValidateUserJWT_EmptyToken2(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	auth := NewFSAAuthenticator(db)

	authInfo, err := auth.ValidateUserJWT("", db)

	if err == nil {
		t.Fatal("Expected error for empty token")
	}

	if err.Error() != "empty token" {
		t.Errorf("Expected 'empty token' error, got: %v", err)
	}

	if authInfo != nil {
		t.Error("Expected nil auth info for empty token")
	}
}

func TestFSAAuthenticator_HandleFSAAuth_Success2(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	mockSocket := NewMockFSASocket2("fsa_socket123")
	mockService := &MockFSAService2{}

	authData := map[string]interface{}{
		"token": "valid_jwt_token",
	}

	// Mock successful JWT validation
	expectedPermissions := &authorizer.UserPermissions{
		UserID: "user123",
		Permissions: []authorizer.Permission{
			{
				Scope:          "organization",
				ScopeID:        "org456",
				OrganizationID: "org456",
				Permissions:    []string{"device.read"},
			},
		},
	}

	// Override the ValidateJWTAndGetPermissions function for testing
	originalValidateJWT := validateJWTAndGetPermissionsFunc
	validateJWTAndGetPermissionsFunc = func(db connect.DatabaseExecutor, token string) (*authorizer.UserPermissions, error) {
		return expectedPermissions, nil
	}
	defer func() { validateJWTAndGetPermissionsFunc = originalValidateJWT }()

	auth := NewFSAAuthenticator(db)
	err := auth.HandleFSAAuth(mockSocket, authData, db, mockService)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	// Verify service methods were called
	if !mockService.StoreConnectionContextCalled {
		t.Error("Expected StoreConnectionContext to be called")
	}

	// Verify stored context
	if mockService.StoredContext == nil {
		t.Fatal("Expected stored context to be non-nil")
	}

	if string(mockService.StoredContext.SocketID) != "fsa_socket123" {
		t.Errorf("Expected socket ID fsa_socket123, got: %s", string(mockService.StoredContext.SocketID))
	}

	if mockService.StoredContext.ClientType != domain.ClientTypeFSA {
		t.Errorf("Expected client type FSA, got: %v", mockService.StoredContext.ClientType)
	}

	if mockService.StoredContext.AuthInfo.UserID != "user123" {
		t.Errorf("Expected user ID user123, got: %s", mockService.StoredContext.AuthInfo.UserID)
	}

	// Verify socket emission - cast back to concrete type to check emitted data
	concreteMock := mockSocket.(*MockFSASocket2)
	if concreteMock.emittedEvent != "auth_success" {
		t.Errorf("Expected auth_success event, got: %s", concreteMock.emittedEvent)
	}

	successData, ok := concreteMock.emittedData["auth_success"].(map[string]interface{})
	if !ok {
		t.Fatal("Expected auth_success data to be map[string]interface{}")
	}

	if successData["user_id"] != "user123" {
		t.Errorf("Expected user_id user123, got: %v", successData["user_id"])
	}

	if _, ok := successData["expires_at"].(string); !ok {
		t.Error("Expected expires_at to be a string")
	}
}

func TestFSAAuthenticator_HandleFSAAuth_MissingToken(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	mockSocket := NewMockFSASocket2("fsa_socket123")
	mockService := &MockFSAService2{}

	// Missing token in auth data
	authData := map[string]interface{}{
		"other_field": "value",
	}

	auth := NewFSAAuthenticator(db)
	err := auth.HandleFSAAuth(mockSocket, authData, db, mockService)

	// Should return nil (error emitted to socket, not returned)
	assert.NoError(t, err)

	// Verify error was emitted to socket
	concreteMock := mockSocket.(*MockFSASocket2)
	assert.Equal(t, "error", concreteMock.emittedEvent)
	assert.Equal(t, "missing token", concreteMock.emittedData["error"])
}

func TestFSAAuthenticator_HandleFSAAuth_InvalidTokenType(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	mockSocket := NewMockFSASocket2("fsa_socket123")
	mockService := &MockFSAService2{}

	// Token is not a string
	authData := map[string]interface{}{
		"token": 12345, // Integer instead of string
	}

	auth := NewFSAAuthenticator(db)
	err := auth.HandleFSAAuth(mockSocket, authData, db, mockService)

	// Should return nil (error emitted to socket, not returned)
	assert.NoError(t, err)

	// Verify error was emitted to socket
	concreteMock := mockSocket.(*MockFSASocket2)
	assert.Equal(t, "error", concreteMock.emittedEvent)
	assert.Equal(t, "missing token", concreteMock.emittedData["error"])
}

func TestFSAAuthenticator_HandleFSAAuth_JWTValidationFailure(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	mockSocket := NewMockFSASocket2("fsa_socket123")
	mockService := &MockFSAService2{}

	authData := map[string]interface{}{
		"token": "invalid_jwt_token",
	}

	// Mock JWT validation failure
	originalValidateJWT := validateJWTAndGetPermissionsFunc
	validateJWTAndGetPermissionsFunc = func(db connect.DatabaseExecutor, token string) (*authorizer.UserPermissions, error) {
		return nil, fmt.Errorf("invalid token")
	}
	defer func() { validateJWTAndGetPermissionsFunc = originalValidateJWT }()

	auth := NewFSAAuthenticator(db)
	err := auth.HandleFSAAuth(mockSocket, authData, db, mockService)

	// Should return nil (error emitted to socket, not returned)
	assert.NoError(t, err)

	// Verify error was emitted to socket
	concreteMock := mockSocket.(*MockFSASocket2)
	assert.Equal(t, "error", concreteMock.emittedEvent)
	assert.Equal(t, "unauthorized", concreteMock.emittedData["error"])
}

func TestFSAAuthenticator_HandleFSAAuth_ServiceTypeAssertionFailure(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	mockSocket := NewMockFSASocket2("fsa_socket123")

	// Pass a service that doesn't implement StoreConnectionContext
	invalidService := "not_a_service"

	authData := map[string]interface{}{
		"token": "valid_jwt_token",
	}

	// Mock successful JWT validation
	expectedPermissions := &authorizer.UserPermissions{
		UserID: "user123",
		Permissions: []authorizer.Permission{
			{
				Scope:          "organization",
				ScopeID:        "org456",
				OrganizationID: "org456",
				Permissions:    []string{"device.read"},
			},
		},
	}

	originalValidateJWT := validateJWTAndGetPermissionsFunc
	validateJWTAndGetPermissionsFunc = func(db connect.DatabaseExecutor, token string) (*authorizer.UserPermissions, error) {
		return expectedPermissions, nil
	}
	defer func() { validateJWTAndGetPermissionsFunc = originalValidateJWT }()

	auth := NewFSAAuthenticator(db)
	err := auth.HandleFSAAuth(mockSocket, authData, db, invalidService)

	// Should succeed despite service type assertion failure
	assert.NoError(t, err)

	// Verify success was still emitted to socket
	concreteMock := mockSocket.(*MockFSASocket2)
	assert.Equal(t, "auth_success", concreteMock.emittedEvent)
}

// MockServiceWithError implements StoreConnectionContext but returns an error
type MockServiceWithError struct{}

func (m *MockServiceWithError) StoreConnectionContext(socketID string, ctx *domain.ConnectionContext) error {
	return fmt.Errorf("storage error")
}

func TestFSAAuthenticator_HandleFSAAuth_StoreConnectionContextError(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	mockSocket := NewMockFSASocket2("fsa_socket123")
	mockService := &MockServiceWithError{}

	authData := map[string]interface{}{
		"token": "valid_jwt_token",
	}

	// Mock successful JWT validation
	expectedPermissions := &authorizer.UserPermissions{
		UserID: "user123",
		Permissions: []authorizer.Permission{
			{
				Scope:          "organization",
				ScopeID:        "org456",
				OrganizationID: "org456",
				Permissions:    []string{"device.read"},
			},
		},
	}

	originalValidateJWT := validateJWTAndGetPermissionsFunc
	validateJWTAndGetPermissionsFunc = func(db connect.DatabaseExecutor, token string) (*authorizer.UserPermissions, error) {
		return expectedPermissions, nil
	}
	defer func() { validateJWTAndGetPermissionsFunc = originalValidateJWT }()

	auth := NewFSAAuthenticator(db)
	err := auth.HandleFSAAuth(mockSocket, authData, db, mockService)

	// Should succeed despite storage error
	assert.NoError(t, err)

	// Verify success was still emitted to socket
	concreteMock := mockSocket.(*MockFSASocket2)
	assert.Equal(t, "auth_success", concreteMock.emittedEvent)
}

func TestFSAAuthenticator_ValidateUserJWT_ValidationError(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	auth := NewFSAAuthenticator(db)

	// Mock JWT validation error
	originalValidateJWT := validateJWTAndGetPermissionsFunc
	validateJWTAndGetPermissionsFunc = func(db connect.DatabaseExecutor, token string) (*authorizer.UserPermissions, error) {
		return nil, fmt.Errorf("token expired")
	}
	defer func() { validateJWTAndGetPermissionsFunc = originalValidateJWT }()

	authInfo, err := auth.ValidateUserJWT("expired_token", db)

	assert.Nil(t, authInfo)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "authentication failed")
	assert.Contains(t, err.Error(), "token expired")
}

func TestFSAAuthenticator_ValidateUserJWT_NoPermissions(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	auth := NewFSAAuthenticator(db)

	// Mock successful JWT validation but with no permissions
	emptyPermissions := &authorizer.UserPermissions{
		UserID:      "user123",
		Permissions: []authorizer.Permission{}, // Empty permissions
	}

	originalValidateJWT := validateJWTAndGetPermissionsFunc
	validateJWTAndGetPermissionsFunc = func(db connect.DatabaseExecutor, token string) (*authorizer.UserPermissions, error) {
		return emptyPermissions, nil
	}
	defer func() { validateJWTAndGetPermissionsFunc = originalValidateJWT }()

	authInfo, err := auth.ValidateUserJWT("valid_token", db)

	assert.NoError(t, err)
	assert.NotNil(t, authInfo)
	assert.Equal(t, "user123", authInfo.UserID)
	assert.Equal(t, "", authInfo.OrgID) // Should be empty when no permissions
	assert.Equal(t, domain.ClientTypeFSA, authInfo.ClientType)
	assert.NotNil(t, authInfo.Permissions)
	assert.Len(t, authInfo.Permissions.Permissions, 0)
}
