package auth

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/zishang520/socket.io/v2/socket"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// MockSocketInterface represents what we need from socket.Socket for testing
type MockSocketInterface interface {
	Id() socket.SocketId
	Emit(event string, data ...interface{}) error
}

// MockSocket implements MockSocketInterface for testing
type MockSocket struct {
	id           socket.SocketId
	emittedData  map[string]interface{}
	emittedEvent string
}

func NewMockSocket(id string) MockSocketInterface {
	return &MockSocket{
		id:          socket.SocketId(id),
		emittedData: make(map[string]interface{}),
	}
}

func (m *MockSocket) Id() socket.SocketId {
	return m.id
}

func (m *MockSocket) Emit(event string, data ...interface{}) error {
	m.emittedEvent = event
	if len(data) > 0 {
		m.emittedData[event] = data[0]
	}
	return nil
}

// MockService implements the service interface needed for gateway authentication
type MockService struct {
	StoreConnectionContextCalled bool
	RegisterGatewayCalled        bool
	RecoverActiveStreamsCalled   bool
	StoreContextError            error
	StoredContext                *domain.ConnectionContext
	StoredSocketID               string
	StoredGatewayID              string
}

func (m *MockService) StoreConnectionContext(socketID string, ctx *domain.ConnectionContext) error {
	m.StoreConnectionContextCalled = true
	m.StoredSocketID = socketID
	m.StoredContext = ctx
	return m.StoreContextError
}

func (m *MockService) RegisterGateway(gatewayID, socketID string) {
	m.RegisterGatewayCalled = true
	m.StoredGatewayID = gatewayID
}

func (m *MockService) RecoverActiveStreamsForGateway(gatewayID string) {
	m.RecoverActiveStreamsCalled = true
}

func TestNewGatewayAuthenticator(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	auth := NewGatewayAuthenticator(db)

	assert.NotNil(t, auth)
	assert.Equal(t, db, auth.db)
}

func TestGatewayAuthenticator_ValidateGatewayAuth(t *testing.T) {
	tests := []struct {
		name        string
		machineKey  string
		apiKey      string
		mockReturn  *authorizer.GatewayInfo
		mockError   error
		expectError bool
		expectedID  string
		expectedOrg string
	}{
		{
			name:       "successful validation",
			machineKey: "valid_machine_key",
			apiKey:     "valid_api_key",
			mockReturn: &authorizer.GatewayInfo{
				GatewayID: "gateway123",
				OrgID:     "org456",
			},
			mockError:   nil,
			expectError: false,
			expectedID:  "gateway123",
			expectedOrg: "org456",
		},
		{
			name:        "invalid credentials",
			machineKey:  "invalid_machine_key",
			apiKey:      "invalid_api_key",
			mockReturn:  nil,
			mockError:   errors.New("invalid credentials"),
			expectError: true,
		},
		{
			name:        "database error",
			machineKey:  "valid_machine_key",
			apiKey:      "valid_api_key",
			mockReturn:  nil,
			mockError:   errors.New("database connection failed"),
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := &mocks.FakeDBExecutor{}

			// Override the ValidateGatewayAuth function for testing
			originalValidateGatewayAuth := validateGatewayAuthFunc
			validateGatewayAuthFunc = func(db connect.DatabaseExecutor, machineKey, apiKey string) (*authorizer.GatewayInfo, error) {
				assert.Equal(t, tt.machineKey, machineKey)
				assert.Equal(t, tt.apiKey, apiKey)
				return tt.mockReturn, tt.mockError
			}
			defer func() { validateGatewayAuthFunc = originalValidateGatewayAuth }()

			auth := NewGatewayAuthenticator(db)
			authInfo, err := auth.ValidateGatewayAuth(tt.machineKey, tt.apiKey)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, authInfo)
			} else {
				require.NoError(t, err)
				require.NotNil(t, authInfo)
				assert.Equal(t, domain.ClientTypeGateway, authInfo.ClientType)
				assert.Equal(t, tt.expectedID, authInfo.GatewayID)
				assert.Equal(t, tt.expectedOrg, authInfo.OrgID)
			}
		})
	}
}

func TestGatewayAuthenticator_HandleGatewayAuth(t *testing.T) {
	tests := []struct {
		name               string
		machineKey         string
		apiKey             string
		mockGatewayInfo    *authorizer.GatewayInfo
		mockValidationErr  error
		mockServiceErr     error
		expectError        bool
		expectedErrorMsg   string
		expectServiceCalls bool
	}{
		{
			name:       "successful authentication",
			machineKey: "valid_machine_key",
			apiKey:     "valid_api_key",
			mockGatewayInfo: &authorizer.GatewayInfo{
				GatewayID: "gateway123",
				OrgID:     "org456",
			},
			mockValidationErr:  nil,
			mockServiceErr:     nil,
			expectError:        false,
			expectServiceCalls: true,
		},
		{
			name:             "missing machine key",
			machineKey:       "",
			apiKey:           "valid_api_key",
			expectError:      true,
			expectedErrorMsg: "missing machine_key",
		},
		{
			name:             "missing API key",
			machineKey:       "valid_machine_key",
			apiKey:           "",
			expectError:      true,
			expectedErrorMsg: "missing api_key",
		},
		{
			name:              "validation failure",
			machineKey:        "invalid_machine_key",
			apiKey:            "invalid_api_key",
			mockValidationErr: errors.New("invalid credentials"),
			expectError:       true,
		},
		{
			name:       "service storage error (non-fatal)",
			machineKey: "valid_machine_key",
			apiKey:     "valid_api_key",
			mockGatewayInfo: &authorizer.GatewayInfo{
				GatewayID: "gateway123",
				OrgID:     "org456",
			},
			mockServiceErr:     errors.New("storage error"),
			expectError:        false, // Storage error is logged but not fatal
			expectServiceCalls: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db := &mocks.FakeDBExecutor{}
			mockSocket := NewMockSocket("socket123")
			mockService := &MockService{
				StoreContextError: tt.mockServiceErr,
			}

			// Override the ValidateGatewayAuth function for testing
			originalValidateGatewayAuth := validateGatewayAuthFunc
			validateGatewayAuthFunc = func(db connect.DatabaseExecutor, machineKey, apiKey string) (*authorizer.GatewayInfo, error) {
				return tt.mockGatewayInfo, tt.mockValidationErr
			}
			defer func() { validateGatewayAuthFunc = originalValidateGatewayAuth }()

			auth := NewGatewayAuthenticator(db)
			err := auth.HandleGatewayAuth(mockSocket, tt.machineKey, tt.apiKey, mockService)

			if tt.expectError {
				assert.Error(t, err)
				if tt.expectedErrorMsg != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorMsg)
				}
			} else {
				assert.NoError(t, err)
			}

			if tt.expectServiceCalls {
				assert.True(t, mockService.StoreConnectionContextCalled)
				assert.True(t, mockService.RegisterGatewayCalled)
				assert.True(t, mockService.RecoverActiveStreamsCalled)

				require.NotNil(t, mockService.StoredContext)
				assert.Equal(t, string(mockSocket.Id()), string(mockService.StoredContext.SocketID))
				assert.Equal(t, domain.ClientTypeGateway, mockService.StoredContext.ClientType)

				// Verify socket emission
				concreteMock := mockSocket.(*MockSocket)
				assert.Equal(t, "gateway_init", concreteMock.emittedEvent)

				initData, ok := concreteMock.emittedData["gateway_init"].(map[string]string)
				require.True(t, ok)
				assert.Equal(t, "authenticated", initData["status"])
			} else {
				assert.False(t, mockService.StoreConnectionContextCalled)
				assert.False(t, mockService.RegisterGatewayCalled)
			}
		})
	}
}

func TestGatewayAuthenticator_HandleGatewayAuth_InvalidService(t *testing.T) {
	db := &mocks.FakeDBExecutor{}
	mockSocket := NewMockSocket("socket123")
	invalidService := "not a valid service"

	// Override the ValidateGatewayAuth function for testing
	originalValidateGatewayAuth := validateGatewayAuthFunc
	validateGatewayAuthFunc = func(db connect.DatabaseExecutor, machineKey, apiKey string) (*authorizer.GatewayInfo, error) {
		return &authorizer.GatewayInfo{
			GatewayID: "gateway123",
			OrgID:     "org456",
		}, nil
	}
	defer func() { validateGatewayAuthFunc = originalValidateGatewayAuth }()

	auth := NewGatewayAuthenticator(db)
	err := auth.HandleGatewayAuth(mockSocket, "valid_machine_key", "valid_api_key", invalidService)

	// Should not return error even if service doesn't implement interface
	assert.NoError(t, err)

	// Socket should still be notified
	concreteMock := mockSocket.(*MockSocket)
	assert.Equal(t, "gateway_init", concreteMock.emittedEvent)
}
