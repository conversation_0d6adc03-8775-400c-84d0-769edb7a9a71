package socketio

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zishang520/socket.io/v2/socket"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func TestNewHandler(t *testing.T) {
	// Test the simple NewHandler constructor
	// This doesn't require actual Socket.IO server, just tests the constructor

	var mockServer *socket.Server // nil is fine for constructor test
	service := createTestService(t)
	db := &mocks.FakeDBExecutor{}

	// Cast to the interface type
	var dbInterface connect.DatabaseExecutor = db

	handler := NewHandler(mockServer, service, &dbInterface)

	assert.NotNil(t, handler)
	assert.Equal(t, mockServer, handler.server)
	assert.Equal(t, service, handler.service)
	assert.Equal(t, &dbInterface, handler.db)
}

func TestHandler_SetupHandlers(t *testing.T) {
	// Test the SetupHandlers method
	// This will call setupEventHandlers but won't actually set up the handlers
	// since we don't have a real Socket.IO server

	var mockServer *socket.Server // nil server - will cause issues in actual setup
	service := createTestService(t)
	db := &mocks.FakeDBExecutor{}
	var dbInterface connect.DatabaseExecutor = db

	handler := NewHandler(mockServer, service, &dbInterface)

	// This will likely panic due to nil server, but tests that the method exists
	// and calls setupEventHandlers
	assert.Panics(t, func() {
		handler.SetupHandlers()
	})
}

func TestHandler_parseSocketEnvelope(t *testing.T) {
	service := createTestService(t)
	db := &mocks.FakeDBExecutor{}
	var dbInterface connect.DatabaseExecutor = db
	handler := NewHandler(nil, service, &dbInterface)

	tests := []struct {
		name          string
		data          interface{}
		expectError   bool
		errorContains string
		description   string
	}{
		{
			name:        "valid JSON string",
			data:        `{"type": 1, "deviceId": "device123", "organizationId": "org456", "payload": "dGVzdA=="}`,
			expectError: false,
			description: "Should parse valid JSON string",
		},
		{
			name:          "invalid JSON string",
			data:          `{"type": 1, "deviceId": "device123", "organizationId": "org456"`,
			expectError:   true,
			errorContains: "failed to parse JSON string",
			description:   "Should fail with invalid JSON string",
		},
		{
			name: "valid map interface",
			data: map[string]interface{}{
				"type":           1,
				"deviceId":       "device123",
				"organizationId": "org456",
				"payload":        []byte("test"),
			},
			expectError: false,
			description: "Should parse valid map interface",
		},
		{
			name: "map with invalid data for JSON marshal",
			data: map[string]interface{}{
				"invalid": make(chan int), // channels can't be marshaled to JSON
			},
			expectError:   true,
			errorContains: "failed to marshal map",
			description:   "Should fail when map contains unmarshallable data",
		},
		{
			name:          "binary data (not implemented)",
			data:          []byte("binary data"),
			expectError:   true,
			errorContains: "binary protobuf parsing with permission filtering not yet implemented",
			description:   "Should fail for binary data (not yet implemented)",
		},
		{
			name:          "unsupported data type",
			data:          123, // int is not supported
			expectError:   true,
			errorContains: "unsupported data type",
			description:   "Should fail for unsupported data types",
		},
		{
			name:          "nil data",
			data:          nil,
			expectError:   true,
			errorContains: "unsupported data type",
			description:   "Should fail for nil data",
		},
		{
			name:          "empty string",
			data:          "",
			expectError:   true,
			errorContains: "failed to parse JSON string",
			description:   "Should fail for empty string",
		},
		{
			name:        "empty map",
			data:        map[string]interface{}{},
			expectError: false,
			description: "Should handle empty map (creates empty envelope)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			envelope, err := handler.parseSocketEnvelope(tt.data)

			if tt.expectError {
				assert.Error(t, err, tt.description)
				assert.Nil(t, envelope)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err, tt.description)
				assert.NotNil(t, envelope)
			}
		})
	}
}
