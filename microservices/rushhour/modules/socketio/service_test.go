package socketio

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/zishang520/socket.io/v2/socket"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/rushhour/tracking"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// MockSocket implements the socket interface for testing
type MockSocket struct {
	id           socket.SocketId
	emittedData  map[string]interface{}
	emittedEvent string
	joinedRooms  []string
	leftRooms    []string
}

func NewMockSocket(id string) *MockSocket {
	return &MockSocket{
		id:          socket.SocketId(id),
		emittedData: make(map[string]interface{}),
		joinedRooms: make([]string, 0),
		leftRooms:   make([]string, 0),
	}
}

func (m *MockSocket) Id() socket.SocketId {
	return m.id
}

func (m *MockSocket) Emit(event string, data ...interface{}) error {
	m.emittedEvent = event
	if len(data) > 0 {
		m.emittedData[event] = data[0]
	}
	return nil
}

func (m *MockSocket) Join(room string) error {
	m.joinedRooms = append(m.joinedRooms, room)
	return nil
}

func (m *MockSocket) Leave(room string) error {
	m.leftRooms = append(m.leftRooms, room)
	return nil
}

// createTestService creates a service for testing without external dependencies
func createTestService(t *testing.T) *Service {
	ctx := context.Background()
	db := &mocks.FakeDBExecutor{}

	// Cast to the interface type that Service expects
	var dbInterface connect.DatabaseExecutor = db

	// Create service without Socket.IO server for pure unit testing
	service := &Service{
		ctx:               ctx,
		server:            nil, // Skip Socket.IO server for unit tests
		socketRegistry:    domain.NewSocketRegistry(),
		streamTracker:     tracking.NewStreamTracker(), // Local mode for tests
		permissionChecker: nil,                         // Will be set in individual tests
		gatewayAuth:       nil,                         // Will be set in individual tests
		fsaAuth:           nil,                         // Will be set in individual tests
		binaryHandler:     NewBinaryMessageHandler(),
		db:                &dbInterface, // Pass pointer to interface
		redisClient:       nil,          // No Redis for unit tests
	}

	return service
}

func TestService_GetServer(t *testing.T) {
	service := createTestService(t)

	// For unit testing, server can be nil
	server := service.GetServer()
	assert.Nil(t, server)
}

func TestService_StoreConnectionContext(t *testing.T) {
	tests := []struct {
		name          string
		socketID      string
		ctx           *domain.ConnectionContext
		expectError   bool
		errorContains string
	}{
		{
			name:     "valid context storage",
			socketID: "socket123",
			ctx: &domain.ConnectionContext{
				SocketID:    socket.SocketId("socket123"),
				ClientType:  domain.ClientTypeGateway,
				ConnectedAt: time.Now(),
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway456",
				},
			},
			expectError: false,
		},
		{
			name:     "empty socket ID - still stores context",
			socketID: "", // StoreConnectionContext doesn't validate socket ID
			ctx: &domain.ConnectionContext{
				SocketID:   socket.SocketId(""),
				ClientType: domain.ClientTypeGateway,
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway456",
				},
			},
			expectError: false, // StoreConnectionContext doesn't validate inputs
		},
		{
			name:     "context with FSA type - user mapping",
			socketID: "socket123",
			ctx: &domain.ConnectionContext{
				SocketID:   socket.SocketId("socket123"),
				ClientType: domain.ClientTypeFSA,
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user456", // FSA clients get added to user mapping
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := createTestService(t)
			err := service.StoreConnectionContext(tt.socketID, tt.ctx)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)

				// Verify context was stored
				storedCtx, exists := service.GetConnectionContext(tt.socketID)
				assert.True(t, exists)
				assert.Equal(t, tt.ctx.SocketID, storedCtx.SocketID)
				assert.Equal(t, tt.ctx.ClientType, storedCtx.ClientType)
			}
		})
	}
}

func TestService_GetConnectionContext(t *testing.T) {
	service := createTestService(t)

	// Test non-existent context
	ctx, exists := service.GetConnectionContext("nonexistent")
	assert.False(t, exists)
	assert.Nil(t, ctx)

	// Store and retrieve context
	testCtx := &domain.ConnectionContext{
		SocketID:   socket.SocketId("socket123"),
		ClientType: domain.ClientTypeGateway,
		AuthInfo: &domain.AuthInfo{
			ClientType: domain.ClientTypeGateway,
			GatewayID:  "gateway123",
		},
	}

	err := service.StoreConnectionContext("socket123", testCtx)
	require.NoError(t, err)

	retrievedCtx, exists := service.GetConnectionContext("socket123")
	assert.True(t, exists)
	assert.Equal(t, testCtx.SocketID, retrievedCtx.SocketID)
	assert.Equal(t, testCtx.ClientType, retrievedCtx.ClientType)
}

func TestService_RemoveConnectionContext(t *testing.T) {
	service := createTestService(t)

	// Store context
	testCtx := &domain.ConnectionContext{
		SocketID:   socket.SocketId("socket123"),
		ClientType: domain.ClientTypeGateway,
		AuthInfo: &domain.AuthInfo{
			ClientType: domain.ClientTypeGateway,
			GatewayID:  "gateway123",
		},
	}

	err := service.StoreConnectionContext("socket123", testCtx)
	require.NoError(t, err)

	// Verify it exists
	_, exists := service.GetConnectionContext("socket123")
	assert.True(t, exists)

	// Remove it
	service.RemoveConnectionContext("socket123")

	// Verify it's gone
	_, exists = service.GetConnectionContext("socket123")
	assert.False(t, exists)
}

func TestService_RegisterGateway(t *testing.T) {
	service := createTestService(t)

	gatewayID := "gateway123"
	socketID := "socket456"

	// Register gateway - first store a connection context for the socket
	ctx := &domain.ConnectionContext{
		SocketID:   socket.SocketId(socketID),
		ClientType: domain.ClientTypeGateway,
		AuthInfo: &domain.AuthInfo{
			ClientType: domain.ClientTypeGateway,
			GatewayID:  gatewayID,
		},
	}
	_ = service.StoreConnectionContext(socketID, ctx)

	// Register gateway
	service.RegisterGateway(gatewayID, socketID)

	// Verify registration - RegisterGateway is for Redis, not socket registry
	// The connection should be in socket registry from StoreConnectionContext
	stats := service.socketRegistry.GetStats()
	assert.Greater(t, stats["total_sockets"], 0)
}

func TestService_UnregisterGateway(t *testing.T) {
	service := createTestService(t)

	gatewayID := "gateway123"
	socketID := "socket456"

	// First register
	service.RegisterGateway(gatewayID, socketID)

	// Then unregister
	service.UnregisterGateway(gatewayID)

	// The socket registry should handle the cleanup
	// (specific verification depends on socket registry implementation)
}

func TestService_ProcessDeviceMessage(t *testing.T) {
	t.Run("nil envelope", func(t *testing.T) {
		// Since the actual method requires socket.Socket interface, skip this complex test for now
		// The method signature is: ProcessDeviceMessage(conn *socket.Socket, envelope *domain.SocketEnvelope) error
		t.Skip("Skipping ProcessDeviceMessage test - requires complex Socket.IO interface mocking")
	})
}

func TestService_ProcessDeviceRequest(t *testing.T) {
	t.Run("nil envelope", func(t *testing.T) {
		// Since the actual method requires socket.Socket interface, skip this complex test for now
		// The method signature is: ProcessDeviceRequest(conn *socket.Socket, envelope *domain.SocketEnvelope) error
		t.Skip("Skipping ProcessDeviceRequest test - requires complex Socket.IO interface mocking")
	})
}

func TestService_HandleClientDisconnect(t *testing.T) {
	service := createTestService(t)

	// Store a connection context
	testCtx := &domain.ConnectionContext{
		SocketID:   socket.SocketId("socket123"),
		ClientType: domain.ClientTypeGateway,
		AuthInfo: &domain.AuthInfo{
			ClientType: domain.ClientTypeGateway,
			GatewayID:  "gateway456",
		},
	}

	err := service.StoreConnectionContext("socket123", testCtx)
	require.NoError(t, err)

	// Handle disconnect
	service.HandleClientDisconnect("socket123")

	// Verify context was removed
	_, exists := service.GetConnectionContext("socket123")
	assert.False(t, exists)
}

func TestService_GetStats(t *testing.T) {
	service := createTestService(t)

	stats := service.GetStats()

	// Verify basic stats structure
	assert.Contains(t, stats, "active_streams")
	assert.Contains(t, stats, "stream_stats")

	// Should have array of active streams
	activeStreams, ok := stats["active_streams"].([]string)
	assert.True(t, ok)
	assert.NotNil(t, activeStreams)

	// Should have stream stats map
	streamStats, ok := stats["stream_stats"].(map[string]interface{})
	assert.True(t, ok)
	assert.NotNil(t, streamStats)
}

func TestService_EdgeCases(t *testing.T) {
	service := createTestService(t)

	t.Run("empty socket ID operations", func(t *testing.T) {
		// GetConnectionContext with empty ID
		ctx, exists := service.GetConnectionContext("")
		assert.False(t, exists)
		assert.Nil(t, ctx)

		// RemoveConnectionContext with empty ID should not panic
		assert.NotPanics(t, func() {
			service.RemoveConnectionContext("")
		})

		// HandleClientDisconnect with empty ID should not panic
		assert.NotPanics(t, func() {
			service.HandleClientDisconnect("")
		})
	})

	t.Run("duplicate context storage", func(t *testing.T) {
		testCtx := &domain.ConnectionContext{
			SocketID:   socket.SocketId("socket123"),
			ClientType: domain.ClientTypeGateway,
			AuthInfo: &domain.AuthInfo{
				ClientType: domain.ClientTypeGateway,
				GatewayID:  "gateway123",
			},
		}

		// Store same context twice
		err1 := service.StoreConnectionContext("socket123", testCtx)
		err2 := service.StoreConnectionContext("socket123", testCtx)

		assert.NoError(t, err1)
		assert.NoError(t, err2) // Should not error on duplicate
	})
}

func TestService_NewService_RedisConnectionFailure(t *testing.T) {
	// Test NewService when createSocketIOServerWithRedis fails
	// This would require mocking the server creation, which is complex
	// For now, we'll test the service creation with a mocked environment

	// This test would be complex to implement without significant refactoring
	// since NewService creates actual Socket.IO servers and Redis connections
	t.Skip("NewService requires complex mocking of Socket.IO server creation")
}

func TestService_GetConnectionContext_EdgeCases(t *testing.T) {
	service := createTestService(t)

	// Test retrieving context that doesn't exist
	ctx, exists := service.GetConnectionContext("nonexistent_socket")
	assert.False(t, exists)
	assert.Nil(t, ctx)

	// Test with empty socket ID
	ctx, exists = service.GetConnectionContext("")
	assert.False(t, exists)
	assert.Nil(t, ctx)

	// Store a context and then verify retrieval
	testCtx := &domain.ConnectionContext{
		SocketID:   socket.SocketId("test_socket"),
		ClientType: domain.ClientTypeFSA,
		AuthInfo: &domain.AuthInfo{
			ClientType: domain.ClientTypeFSA,
			UserID:     "user123",
		},
	}

	err := service.StoreConnectionContext("test_socket", testCtx)
	assert.NoError(t, err)

	// Verify we can retrieve it
	retrievedCtx, exists := service.GetConnectionContext("test_socket")
	assert.True(t, exists)
	assert.NotNil(t, retrievedCtx)
	assert.Equal(t, testCtx.SocketID, retrievedCtx.SocketID)
	assert.Equal(t, testCtx.ClientType, retrievedCtx.ClientType)
}

func TestService_RegisterGateway_WithRedis(t *testing.T) {
	service := createTestService(t)

	// Test RegisterGateway when Redis is nil (current test setup)
	// This tests the path where redisClient is nil
	gatewayID := "gateway789"
	socketID := "socket999"

	// This should not panic even with nil Redis client
	assert.NotPanics(t, func() {
		service.RegisterGateway(gatewayID, socketID)
	})
}

func TestService_UnregisterGateway_WithRedis(t *testing.T) {
	service := createTestService(t)

	// Test UnregisterGateway when Redis is nil (current test setup)
	gatewayID := "gateway789"

	// This should not panic even with nil Redis client
	assert.NotPanics(t, func() {
		service.UnregisterGateway(gatewayID)
	})
}

func TestService_RemoveConnectionContext_EdgeCases(t *testing.T) {
	service := createTestService(t)

	// Test removing context that doesn't exist
	assert.NotPanics(t, func() {
		service.RemoveConnectionContext("nonexistent_socket")
	})

	// Test removing with empty socket ID
	assert.NotPanics(t, func() {
		service.RemoveConnectionContext("")
	})

	// Store a context, then remove it twice
	testCtx := &domain.ConnectionContext{
		SocketID:   socket.SocketId("remove_test"),
		ClientType: domain.ClientTypeGateway,
		AuthInfo: &domain.AuthInfo{
			ClientType: domain.ClientTypeGateway,
			GatewayID:  "gateway123",
		},
	}

	err := service.StoreConnectionContext("remove_test", testCtx)
	assert.NoError(t, err)

	// Verify it exists
	_, exists := service.GetConnectionContext("remove_test")
	assert.True(t, exists)

	// Remove it once
	service.RemoveConnectionContext("remove_test")

	// Verify it's gone
	_, exists = service.GetConnectionContext("remove_test")
	assert.False(t, exists)

	// Remove it again (should not panic)
	assert.NotPanics(t, func() {
		service.RemoveConnectionContext("remove_test")
	})
}

func TestService_StoreConnectionContext_EdgeCases(t *testing.T) {
	service := createTestService(t)

	// Test storing context with various client types
	tests := []struct {
		name       string
		socketID   string
		clientType domain.ClientType
		authInfo   *domain.AuthInfo
	}{
		{
			name:       "gateway context",
			socketID:   "gateway_socket",
			clientType: domain.ClientTypeGateway,
			authInfo: &domain.AuthInfo{
				ClientType: domain.ClientTypeGateway,
				GatewayID:  "gateway123",
			},
		},
		{
			name:       "fsa context",
			socketID:   "fsa_socket",
			clientType: domain.ClientTypeFSA,
			authInfo: &domain.AuthInfo{
				ClientType: domain.ClientTypeFSA,
				UserID:     "user456",
			},
		},
		{
			name:       "onramp context",
			socketID:   "onramp_socket",
			clientType: domain.ClientTypeOnramp,
			authInfo: &domain.AuthInfo{
				ClientType: domain.ClientTypeOnramp,
				UserID:     "user789",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := &domain.ConnectionContext{
				SocketID:   socket.SocketId(tt.socketID),
				ClientType: tt.clientType,
				AuthInfo:   tt.authInfo,
			}

			err := service.StoreConnectionContext(tt.socketID, ctx)
			assert.NoError(t, err)

			// Verify storage
			retrievedCtx, exists := service.GetConnectionContext(tt.socketID)
			assert.True(t, exists)
			assert.Equal(t, ctx.SocketID, retrievedCtx.SocketID)
			assert.Equal(t, ctx.ClientType, retrievedCtx.ClientType)
		})
	}
}
