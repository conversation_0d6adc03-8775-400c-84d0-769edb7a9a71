package socketio

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/zishang520/socket.io/v2/socket"
	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// <PERSON><PERSON> manages Socket.io event handling and client connections
type Handler struct {
	server  *socket.Server
	service *Service
	db      *connect.DatabaseExecutor
}

// NewHandler creates a new Socket.io handler
func NewHandler(server *socket.Server, service *Service, db *connect.DatabaseExecutor) *Handler {
	return &Handler{
		server:  server,
		service: service,
		db:      db,
	}
}

// SetupHandlers initializes all Socket.io event handlers
func (h *Handler) SetupHandlers() {
	h.setupEventHandlers()
}

// setupEventHandlers configures all Socket.io event handlers
func (h *Handler) setupEventHandlers() {
	// Main namespace for basic functionality
	h.registerBasicClientEvents("/")

	// Gateway authentication namespace
	h.registerGatewayEvents(domain.NamespaceGateway)

	// FSA (Field Service App) authentication namespace
	h.registerFSAEvents(domain.NamespaceFSA)
}

// registerGatewayEvents sets up event handlers for gateway clients
func (h *Handler) registerGatewayEvents(namespace string) {
	// Gateway namespace handler
	gatewayNamespace := h.server.Of(namespace, nil)

	gatewayNamespace.On("connection", func(clients ...interface{}) {
		conn := clients[0].(*socket.Socket)
		logger.Infof("Gateway connecting: %s", conn.Id())

		// Extract machine key and API key from connection auth data
		authData := conn.Handshake().Auth
		if authMap, ok := authData.(map[string]interface{}); ok {
			machineKey, machineKeyExists := authMap["machine_key"].(string)
			apiKey, apiKeyExists := authMap["api_key"].(string)

			if machineKeyExists && apiKeyExists && machineKey != "" && apiKey != "" {
				// Authenticate using machine key and API key (pass service for socket registry)
				if err := h.service.gatewayAuth.HandleGatewayAuth(conn, machineKey, apiKey, h.service); err != nil {
					logger.Warnf("Gateway authentication failed: %s - %v", conn.Id(), err)
					conn.Emit("error", "unauthorized")
					conn.Disconnect(true)
					return
				}

				// Set up gateway-specific event handlers
				h.setupGatewayClientEvents(conn)
			} else {
				logger.Warnf("Gateway connection missing machine_key or api_key: %s", conn.Id())
				conn.Emit("error", "missing machine_key or api_key")
				conn.Disconnect(true)
				return
			}
		} else {
			logger.Warnf("Gateway connection invalid auth data: %s", conn.Id())
			conn.Emit("error", "invalid auth data")
			conn.Disconnect(true)
			return
		}
	})
}

// setupGatewayClientEvents sets up event handlers for individual gateway clients
func (h *Handler) setupGatewayClientEvents(conn *socket.Socket) {
	// Device message handling - unified handler for all device communication
	// This replaces the old stream_frame and device_status handlers
	conn.On("device_message", func(datas ...interface{}) {
		if len(datas) == 0 {
			return
		}

		envelope, err := h.parseSocketEnvelope(datas[0])
		if err != nil {
			logger.Warnf("Invalid device message from gateway %s: %v", conn.Id(), err)
			return
		}

		// Process the device message (includes former stream data and status)
		if err := h.service.ProcessDeviceMessage(conn, envelope); err != nil {
			logger.Warnf("Device message processing failed: %v", err)
		}
	})

	// Handle stream control commands (join/leave device rooms)
	conn.On("stream_control", func(datas ...interface{}) {
		if len(datas) == 0 {
			return
		}

		// Parse the stream control command
		cmdData, ok := datas[0].(map[string]interface{})
		if !ok {
			logger.Warnf("Invalid stream control command from gateway %s", conn.Id())
			return
		}

		action, _ := cmdData["action"].(string)
		deviceRoom, _ := cmdData["device_room"].(string)
		deviceID, _ := cmdData["device_id"].(string)

		switch action {
		case "join_device_room":
			// Gateway joins device room to start publishing data
			conn.Join(socket.Room(deviceRoom))
			logger.Infof("Gateway %s joined device room %s for device %s", conn.Id(), deviceRoom, deviceID)

		case "leave_device_room":
			// Gateway leaves device room to stop publishing data
			conn.Leave(socket.Room(deviceRoom))
			logger.Infof("Gateway %s left device room %s for device %s", conn.Id(), deviceRoom, deviceID)

		default:
			logger.Warnf("Unknown stream control action '%s' from gateway %s", action, conn.Id())
		}
	})

	// Handle verification requests from gateways (for test coordination)
	conn.On("verify_readiness", func(datas ...interface{}) {
		// Gateway is requesting verification that it can be looked up
		// This helps tests coordinate properly instead of using fixed sleeps
		
		// Get connection context to find gateway ID
		ctx, exists := h.service.GetConnectionContext(string(conn.Id()))
		if !exists || ctx.AuthInfo == nil {
			logger.Warnf("Verification request from unauthenticated gateway %s", conn.Id())
			conn.Emit("verification_failed", map[string]string{"error": "not authenticated"})
			return
		}
		
		gatewayID := ctx.AuthInfo.GatewayID
		
		// Simply verify that this gateway is properly registered by attempting a Redis lookup
		// We'll use a brief delay to ensure Redis registration has propagated
		go func() {
			time.Sleep(100 * time.Millisecond) // Brief delay for Redis consistency
			
			// Try to lookup this specific gateway in Redis  
			if socketID, err := h.service.getGatewaySocketID(gatewayID); err == nil && socketID == string(conn.Id()) {
				// Gateway lookup is working - respond with success
				conn.Emit("verification_complete", map[string]string{
					"status": "ready",
					"gateway_id": gatewayID,
				})
				logger.Debugf("Gateway %s verification successful", gatewayID)
			} else {
				conn.Emit("verification_failed", map[string]string{
					"error": "lookup failed",
					"gateway_id": gatewayID,
					"details": err.Error(),
				})
				logger.Warnf("Gateway %s verification failed: %v", gatewayID, err)
			}
		}()
	})

	// Handle disconnect
	conn.On("disconnect", func(datas ...interface{}) {
		logger.Infof("Gateway disconnected: %s", conn.Id())
		h.service.HandleClientDisconnect(string(conn.Id()))
	})
}

// registerFSAEvents sets up event handlers for Field Service App clients
func (h *Handler) registerFSAEvents(namespace string) {
	// FSA namespace handler
	fsaNamespace := h.server.Of(namespace, nil)

	fsaNamespace.On("connection", func(clients ...interface{}) {
		conn := clients[0].(*socket.Socket)
		logger.Infof("FSA connecting: %s", conn.Id())

		// Extract JWT token from connection auth data
		authData := conn.Handshake().Auth
		if authMap, ok := authData.(map[string]interface{}); ok {
			if _, tokenExists := authMap["token"].(string); tokenExists {
				// Authenticate FSA connection (pass service for socket registry)
				if err := h.service.fsaAuth.HandleFSAAuth(conn, authMap, *h.db, h.service); err != nil {
					logger.Warnf("FSA authentication failed: %s - %v", conn.Id(), err)
					conn.Emit("error", "unauthorized")
					conn.Disconnect(true)
					return
				}

				// Set up FSA-specific event handlers
				h.setupFSAClientEvents(conn)
			} else {
				logger.Warnf("FSA connection missing token: %s", conn.Id())
				conn.Emit("error", "missing token")
				conn.Disconnect(true)
				return
			}
		} else {
			logger.Warnf("FSA connection invalid auth data: %s", conn.Id())
			conn.Emit("error", "invalid auth data")
			conn.Disconnect(true)
			return
		}
	})
}

// setupFSAClientEvents sets up event handlers for individual FSA clients
func (h *Handler) setupFSAClientEvents(conn *socket.Socket) {
	// Device channel join - replaces watch_stream
	conn.On("join", func(datas ...interface{}) {
		if len(datas) == 0 {
			conn.Emit("error", "missing room name")
			return
		}

		roomName, ok := datas[0].(string)
		if !ok {
			conn.Emit("error", "invalid room name")
			return
		}

		// Get connection context to validate permissions
		ctx, exists := h.service.GetConnectionContext(string(conn.Id()))
		if !exists {
			conn.Emit("error", "not authenticated")
			return
		}

		// Validate room access permissions
		if err := h.service.permissionChecker.CheckRoomAccess(ctx, roomName); err != nil {
			logger.Warnf("Room join denied for %s: %v", conn.Id(), err)
			conn.Emit("error", "access denied")
			return
		}

		// Join the Socket.IO room
		conn.Join(socket.Room(roomName))

		// If this is a device channel, start streaming
		if err := h.service.HandleDeviceChannelJoin(conn, roomName, ctx.OrgID); err != nil {
			logger.Warnf("Device channel join failed for %s: %v", conn.Id(), err)
			conn.Emit("error", err.Error())
			return
		}

		logger.Infof("FSA %s joined channel %s", conn.Id(), roomName)
		conn.Emit("joined", roomName)
	})

	// Device channel leave - replaces unwatch_stream
	conn.On("leave", func(datas ...interface{}) {
		if len(datas) == 0 {
			conn.Emit("error", "missing room name")
			return
		}

		roomName, ok := datas[0].(string)
		if !ok {
			conn.Emit("error", "invalid room name")
			return
		}

		// Get connection context
		ctx, exists := h.service.GetConnectionContext(string(conn.Id()))
		if !exists {
			conn.Emit("error", "not authenticated")
			return
		}

		// Leave the Socket.IO room
		conn.Leave(socket.Room(roomName))

		// If this is a device channel, stop streaming
		if err := h.service.HandleDeviceChannelLeave(conn, roomName, ctx.OrgID); err != nil {
			logger.Warnf("Device channel leave failed for %s: %v", conn.Id(), err)
			// Don't emit error for leave operations
		}

		logger.Infof("FSA %s left channel %s", conn.Id(), roomName)
		conn.Emit("left", roomName)
	})

	// Device request/command handling
	conn.On("device_request", func(datas ...interface{}) {
		if len(datas) == 0 {
			return
		}

		envelope, err := h.parseSocketEnvelope(datas[0])
		if err != nil {
			logger.Warnf("Invalid device request from FSA %s: %v", conn.Id(), err)
			return
		}

		h.service.ProcessDeviceRequest(conn, envelope)
	})

	// Handle disconnect - clean up stream subscriptions
	conn.On("disconnect", func(datas ...interface{}) {
		logger.Infof("FSA disconnected: %s", conn.Id())
		h.service.HandleClientDisconnect(string(conn.Id()))
	})
}

// registerBasicClientEvents sets up basic event handlers for the root namespace
func (h *Handler) registerBasicClientEvents(namespace string) {
	// Root namespace handler
	rootNamespace := h.server.Of(namespace, nil)

	rootNamespace.On("connection", func(clients ...interface{}) {
		conn := clients[0].(*socket.Socket)
		logger.Infof("Client connected to root namespace: %s", conn.Id())

		// Basic ping handler
		conn.On("ping", func(datas ...interface{}) {
			conn.Emit("pong", "pong")
		})

		// Handle disconnect
		conn.On("disconnect", func(datas ...interface{}) {
			logger.Infof("Client disconnected from root namespace: %s", conn.Id())
		})
	})
}

// parseSocketEnvelope parses incoming data into a SocketEnvelope
func (h *Handler) parseSocketEnvelope(data interface{}) (*domain.SocketEnvelope, error) {
	envelope := &domain.SocketEnvelope{}

	switch v := data.(type) {
	case []byte:
		// Binary protobuf data - RELAY WITH FILTERING: Parse nested envelope structure
		// The binary data contains a rushhour envelope with a WrapperCommand/WrapperResponse payload
		// TODO: Integrate BinaryMessageHandler.ProcessBinaryMessage() with permission filtering
		// TODO: Extract user permissions from connection context for filtering
		return nil, fmt.Errorf("binary protobuf parsing with permission filtering not yet implemented")
	case string:
		// JSON string
		if err := json.Unmarshal([]byte(v), envelope); err != nil {
			return nil, fmt.Errorf("failed to parse JSON string: %w", err)
		}
	case map[string]interface{}:
		// JSON object
		jsonData, err := json.Marshal(v)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal map: %w", err)
		}
		if err := json.Unmarshal(jsonData, envelope); err != nil {
			return nil, fmt.Errorf("failed to parse JSON object: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported data type: %T", v)
	}

	return envelope, nil
}
