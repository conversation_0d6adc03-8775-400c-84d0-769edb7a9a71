package permissions

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"synapse-its.com/rushhour/domain"
	"synapse-its.com/shared/api/authorizer"
)

func TestNewPermission<PERSON>hecker(t *testing.T) {
	checker := NewPermissionChecker()
	assert.NotNil(t, checker)
}

func TestPermissionChecker_CheckDeviceAccess(t *testing.T) {
	tests := []struct {
		name          string
		ctx           *domain.ConnectionContext
		deviceID      string
		operation     string
		expectError   bool
		errorContains string
	}{
		{
			name:          "nil context",
			ctx:           nil,
			deviceID:      "device123",
			operation:     "view",
			expectError:   true,
			errorContains: "no connection context",
		},
		{
			name: "empty device ID",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
				},
			},
			deviceID:      "",
			operation:     "view",
			expectError:   true,
			errorContains: "device ID cannot be empty",
		},
		{
			name: "gateway access allowed",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway123",
				},
			},
			deviceID:    "device123",
			operation:   "view",
			expectError: false,
		},
		{
			name: "FSA with valid permissions",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"org_view_devices"},
							},
						},
					},
				},
			},
			deviceID:    "device123",
			operation:   "view",
			expectError: false,
		},
		{
			name: "FSA without required permissions",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"some_other_permission"},
							},
						},
					},
				},
			},
			deviceID:      "device123",
			operation:     "view",
			expectError:   true,
			errorContains: "does not have permission",
		},
		{
			name: "control operation requires management permissions",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"org_view_devices"}, // Only view, not manage
							},
						},
					},
				},
			},
			deviceID:      "device123",
			operation:     "control",
			expectError:   true,
			errorContains: "does not have permission",
		},
		{
			name: "control operation with management permissions",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"org_manage_devices"},
							},
						},
					},
				},
			},
			deviceID:    "device123",
			operation:   "control",
			expectError: false,
		},
		{
			name: "no valid authentication context",
			ctx: &domain.ConnectionContext{
				AuthInfo: nil,
			},
			deviceID:      "device123",
			operation:     "view",
			expectError:   true,
			errorContains: "no valid authentication context",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			checker := NewPermissionChecker()
			err := checker.CheckDeviceAccess(tt.ctx, tt.deviceID, tt.operation)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPermissionChecker_CheckRoomAccess(t *testing.T) {
	tests := []struct {
		name          string
		ctx           *domain.ConnectionContext
		roomName      string
		expectError   bool
		errorContains string
	}{
		{
			name:          "nil context",
			ctx:           nil,
			roomName:      "org:org456:device:device123:display",
			expectError:   true,
			errorContains: "no connection context",
		},
		{
			name: "device stream room access - gateway",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway123",
				},
			},
			roomName:    "org:org456:device:device123:display",
			expectError: false,
		},
		{
			name: "device stream room access - FSA with permissions",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"org_view_devices"},
							},
						},
					},
				},
			},
			roomName:    "org:org456:device:device123:display",
			expectError: false,
		},
		{
			name: "unknown room type",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway123",
				},
			},
			roomName:      "unknown:room:type",
			expectError:   true,
			errorContains: "unknown room type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			checker := NewPermissionChecker()
			err := checker.CheckRoomAccess(tt.ctx, tt.roomName)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPermissionChecker_CheckMessageSending(t *testing.T) {
	tests := []struct {
		name          string
		ctx           *domain.ConnectionContext
		msgType       string
		targetRoom    string
		expectError   bool
		errorContains string
	}{
		{
			name:          "nil context",
			ctx:           nil,
			msgType:       "device_message",
			targetRoom:    "org:org456:device:device123:display",
			expectError:   true,
			errorContains: "no connection context",
		},
		{
			name: "device_message from gateway",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway123",
				},
			},
			msgType:     "device_message",
			targetRoom:  "org:org456:device:device123:display",
			expectError: false,
		},
		{
			name: "device_message from FSA - not allowed",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"org_view_devices"}, // Give room access so message type check can run
							},
						},
					},
				},
			},
			msgType:       "device_message",
			targetRoom:    "org:org456:device:device123:display",
			expectError:   true,
			errorContains: "only allowed from gateways",
		},
		{
			name: "device_request from FSA",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"org_view_devices"},
							},
						},
					},
				},
			},
			msgType:     "device_request",
			targetRoom:  "org:org456:device:device123:display",
			expectError: false,
		},
		{
			name: "device_request from Onramp",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeOnramp,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID: "user123",
						Permissions: []authorizer.Permission{
							{
								Scope:          "organization",
								ScopeID:        "org456",
								OrganizationID: "org456",
								Permissions:    []string{"org_view_devices"},
							},
						},
					},
				},
			},
			msgType:     "device_request",
			targetRoom:  "org:org456:device:device123:display",
			expectError: false,
		},
		{
			name: "device_request from gateway - not allowed",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway123",
				},
			},
			msgType:       "device_request",
			targetRoom:    "org:org456:device:device123:display",
			expectError:   true,
			errorContains: "only allowed from FSA and Onramp",
		},
		{
			name: "stream_control - always allowed",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway123",
				},
			},
			msgType:     "stream_control",
			targetRoom:  "org:org456:device:device123:display",
			expectError: false,
		},
		{
			name: "unknown message type",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeGateway,
					GatewayID:  "gateway123",
				},
			},
			msgType:       "unknown_message",
			targetRoom:    "org:org456:device:device123:display",
			expectError:   true,
			errorContains: "unknown message type",
		},
		{
			name: "cannot access target room",
			ctx: &domain.ConnectionContext{
				AuthInfo: &domain.AuthInfo{
					ClientType: domain.ClientTypeFSA,
					UserID:     "user123",
					Permissions: &authorizer.UserPermissions{
						UserID:      "user123",
						Permissions: []authorizer.Permission{}, // No permissions
					},
				},
			},
			msgType:       "device_request",
			targetRoom:    "org:org456:device:device123:display",
			expectError:   true,
			errorContains: "cannot send message to room",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			checker := NewPermissionChecker()
			err := checker.CheckMessageSending(tt.ctx, tt.msgType, tt.targetRoom)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestPermissionChecker_GetPermissionsForUser(t *testing.T) {
	checker := NewPermissionChecker()

	perms, err := checker.GetPermissionsForUser("user123", "org456")

	// This method should return an error since it's not implemented in JWT approach
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "should be loaded via authorizer")
	assert.Empty(t, perms)
}

func TestPermissionChecker_GetPermissionsForGateway(t *testing.T) {
	tests := []struct {
		name          string
		gatewayID     string
		orgID         string
		expectedPerms []string
	}{
		{
			name:          "gateway permissions",
			gatewayID:     "gateway123",
			orgID:         "org456",
			expectedPerms: []string{"device_access", "stream_send"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			checker := NewPermissionChecker()
			perms, err := checker.GetPermissionsForGateway(tt.gatewayID, tt.orgID)

			assert.NoError(t, err)
			assert.ElementsMatch(t, tt.expectedPerms, perms)
		})
	}
}

func TestPermissionChecker_hasAnyPermission(t *testing.T) {
	tests := []struct {
		name                string
		userPermissions     *authorizer.UserPermissions
		requiredPermissions []string
		expected            bool
	}{
		{
			name: "user has required permission",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "organization",
						ScopeID:        "org456",
						OrganizationID: "org456",
						Permissions:    []string{"org_view_devices", "other_perm"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices"},
			expected:            true,
		},
		{
			name: "user has one of multiple required permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "organization",
						ScopeID:        "org456",
						OrganizationID: "org456",
						Permissions:    []string{"device_group_view_devices"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices", "location_group_view_devices"},
			expected:            true,
		},
		{
			name: "user has none of required permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "organization",
						ScopeID:        "org456",
						OrganizationID: "org456",
						Permissions:    []string{"some_other_permission"},
					},
				},
			},
			requiredPermissions: []string{"org_view_devices", "device_group_view_devices"},
			expected:            false,
		},
		{
			name: "user has no permissions at all",
			userPermissions: &authorizer.UserPermissions{
				UserID:      "user123",
				Permissions: []authorizer.Permission{},
			},
			requiredPermissions: []string{"org_view_devices"},
			expected:            false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			checker := NewPermissionChecker()
			result := checker.hasAnyPermission(tt.userPermissions, tt.requiredPermissions)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestPermissionChecker_EdgeCases(t *testing.T) {
	checker := NewPermissionChecker()

	t.Run("empty device ID in gateway context", func(t *testing.T) {
		ctx := &domain.ConnectionContext{
			AuthInfo: &domain.AuthInfo{
				ClientType: domain.ClientTypeGateway,
				GatewayID:  "", // Empty gateway ID
			},
		}

		err := checker.CheckDeviceAccess(ctx, "device123", "view")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no valid authentication context")
	})

	t.Run("nil auth info", func(t *testing.T) {
		ctx := &domain.ConnectionContext{
			AuthInfo: nil,
		}

		err := checker.CheckDeviceAccess(ctx, "device123", "view")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no valid authentication context")
	})

	t.Run("unknown client type", func(t *testing.T) {
		ctx := &domain.ConnectionContext{
			AuthInfo: &domain.AuthInfo{
				ClientType: domain.ClientTypeUnknown, // Unknown client type should fail
				UserID:     "user123",
			},
		}

		err := checker.CheckDeviceAccess(ctx, "device123", "view")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no valid authentication context")
	})
}
