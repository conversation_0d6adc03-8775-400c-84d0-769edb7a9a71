package organization

import (
	"net/http"

	RestInvites "synapse-its.com/shared/rest/onramp/invites"
	RestOrganization "synapse-its.com/shared/rest/onramp/organization"
	RestPermissions "synapse-its.com/shared/rest/onramp/permissions"
	RestRoles "synapse-its.com/shared/rest/onramp/roles"

	"github.com/gorilla/mux"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)
	router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.GetRolesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/roles", RestRoles.CreateRoleHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations/{organizationId}/roles/{roleId}", RestRoles.DeleteRoleHandler).Methods(http.MethodDelete)
	router.HandleFunc("/organizations/{organizationId}/role-templates", RestRoles.GetRoleTemplatesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/permissions", RestPermissions.GetPermissionsHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/permissions/{permissionId}/roles/{roleId}", RestPermissions.UpdatePermissionHandler).Methods(http.MethodPatch)

	// Add new invite routes
	router.HandleFunc("/organizations/{organizationId}/invites", RestInvites.CreateInviteHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations/{organizationId}/invites", RestInvites.ListUserInvitesForOrganizationHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/invites/{inviteId}", RestInvites.RevokeInviteHandler).Methods(http.MethodDelete)
	router.HandleFunc("/organizations/{organizationId}/invites/validate", RestInvites.ValidateInviteHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{organizationId}/invites/{inviteId}/resend", RestInvites.ResendInviteHandler).Methods(http.MethodPost)
}
