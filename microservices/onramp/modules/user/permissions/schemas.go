package permissions

// UserPermissionsResponse represents the API response for user permissions
type UserPermissionsResponse struct {
	UserID      string            `json:"userId"`
	Permissions []PermissionScope `json:"permissions"`
}

// PermissionScope represents permissions within a specific scope
type PermissionScope struct {
	Scope          string   `json:"scope"`
	ScopeID        *string  `json:"scopeId"`
	OrganizationID *string  `json:"organizationId"`
	Permissions    []string `json:"permissions"`
}
