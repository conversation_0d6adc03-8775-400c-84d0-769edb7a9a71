package user

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/onramp/domain"
	onrampMocks "synapse-its.com/onramp/mock"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/mocks"
)

// TestNewHandler tests the Handler constructor
func TestNewHandler(t *testing.T) {
	t.Run("NewHandler_Success", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}

		handler := NewHandler(mockSessionStore)

		assert.NotNil(t, handler)
		assert.Equal(t, mockSessionStore, handler.sessionStore)
	})

	t.Run("NewHandler_NilSessionStore", func(t *testing.T) {
		handler := NewHandler(nil)

		assert.NotNil(t, handler)
		assert.Nil(t, handler.sessionStore)
	})
}

// TestHandler_RegisterRoutes tests the route registration functionality
func TestHandler_RegisterRoutes(t *testing.T) {
	t.Run("RegisterRoutes_Success", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)
		router := mux.NewRouter()

		// Register routes
		handler.RegisterRoutes(router)

		// Collect registered routes
		var routes []string
		var methods []string
		err := router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
			pathTemplate, err := route.GetPathTemplate()
			if err == nil {
				routes = append(routes, pathTemplate)
			}

			methodsSlice, err := route.GetMethods()
			if err == nil && len(methodsSlice) > 0 {
				methods = append(methods, methodsSlice[0])
			}
			return nil
		})

		assert.NoError(t, err)
		assert.Contains(t, routes, "/user/permissions")
		assert.Contains(t, methods, http.MethodGet)
	})

	t.Run("RegisterRoutes_RouterIntegration", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)
		router := mux.NewRouter()

		// Set up mock connections for testing
		mockConnections := mocks.FakeConns()

		// Apply middleware that the handlers expect
		router.Use(middleware.ConnectionsMiddleware(mockConnections))
		router.Use(middleware.BQBatchMiddleware(bqbatch.New(mockConnections.Bigquery, mockConnections.Pubsub)))

		// Register routes
		handler.RegisterRoutes(router)

		// Test that routes are accessible (we expect errors due to missing context)
		testCases := []struct {
			name           string
			method         string
			path           string
			expectedStatus int
		}{
			{
				name:           "GET /user/permissions",
				method:         http.MethodGet,
				path:           "/user/permissions",
				expectedStatus: http.StatusInternalServerError, // Expected since no session
			},
			{
				name:           "POST /user/permissions - Method not allowed",
				method:         http.MethodPost,
				path:           "/user/permissions",
				expectedStatus: http.StatusNotFound,
			},
			{
				name:           "GET /user/nonexistent",
				method:         http.MethodGet,
				path:           "/user/nonexistent",
				expectedStatus: http.StatusNotFound,
			},
			{
				name:           "GET /user/{userId}/invites",
				method:         http.MethodGet,
				path:           "/user/{userId}/invites",
				expectedStatus: http.StatusBadRequest,
			},
			{
				name:           "DELETE /user/{userId}/invites/{inviteId}",
				method:         http.MethodDelete,
				path:           "/user/{userId}/invites/{inviteId}",
				expectedStatus: http.StatusBadRequest,
			},
			{
				name:           "POST /user/{userId}/invites/{inviteId}/redeem",
				method:         http.MethodPost,
				path:           "/user/{userId}/invites/{inviteId}/redeem",
				expectedStatus: http.StatusBadRequest,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				req := httptest.NewRequest(tc.method, tc.path, nil)
				rr := httptest.NewRecorder()

				router.ServeHTTP(rr, req)

				assert.Equal(t, tc.expectedStatus, rr.Code)
			})
		}
	})

	t.Run("RegisterRoutes_MiddlewareApplication", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)
		router := mux.NewRouter()

		// Register routes
		handler.RegisterRoutes(router)

		// Test that session middleware is applied by checking for session cookie handling
		req := httptest.NewRequest(http.MethodGet, "/user/permissions", nil)
		// Add a session cookie to test middleware behavior
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "test-session"})
		rr := httptest.NewRecorder()

		// Set up mock expectations for session middleware
		mockSessionStore.On("GetSession", "test-session").Return((*domain.Session)(nil), false)

		router.ServeHTTP(rr, req)

		// The middleware should have been called (even if the session doesn't exist)
		mockSessionStore.AssertExpectations(t)
	})

	t.Run("RegisterRoutes_PathPrefix", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)
		router := mux.NewRouter()

		// Register routes
		handler.RegisterRoutes(router)

		// Verify that all registered routes have the correct path prefix
		var foundUserRoutes []string
		err := router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
			pathTemplate, err := route.GetPathTemplate()
			if err == nil && pathTemplate != "" {
				foundUserRoutes = append(foundUserRoutes, pathTemplate)
			}
			return nil
		})

		assert.NoError(t, err)

		// Check that all user routes have the /user prefix
		for _, route := range foundUserRoutes {
			if route != "/" { // Skip root route
				assert.Contains(t, route, "/user", "All user routes should have /user prefix")
			}
		}
	})
}

// TestHandler_SessionStoreIntegration tests the handler with session store interactions
func TestHandler_SessionStoreIntegration(t *testing.T) {
	t.Run("SessionStore_ValidSession", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)
		router := mux.NewRouter()

		// Create a valid session
		validSession := &domain.Session{
			UserID: "user123",
		}

		// Set up mock expectations
		mockSessionStore.On("GetSession", "valid-session").Return(validSession, true)

		// Register routes
		handler.RegisterRoutes(router)

		// Test request with valid session (should work correctly)
		req := httptest.NewRequest(http.MethodGet, "/user/permissions", nil)
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "valid-session"})
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)

		// Verify mock expectations were met
		mockSessionStore.AssertExpectations(t)
		// We expect 200 because the endpoint works with a valid session
		assert.Equal(t, http.StatusOK, rr.Code)
	})

	t.Run("SessionStore_InvalidSession", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)
		router := mux.NewRouter()

		// Set up mock expectations for invalid session
		mockSessionStore.On("GetSession", "invalid-session").Return((*domain.Session)(nil), false)

		// Register routes
		handler.RegisterRoutes(router)

		// Test request with invalid session
		req := httptest.NewRequest(http.MethodGet, "/user/permissions", nil)
		req.AddCookie(&http.Cookie{Name: "session_id", Value: "invalid-session"})
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)

		// Should return internal server error due to missing context setup
		assert.Equal(t, http.StatusInternalServerError, rr.Code)
		mockSessionStore.AssertExpectations(t)
	})

	t.Run("SessionStore_NoSession", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)
		router := mux.NewRouter()

		// Register routes
		handler.RegisterRoutes(router)

		// Test request without session cookie
		req := httptest.NewRequest(http.MethodGet, "/user/permissions", nil)
		rr := httptest.NewRecorder()

		router.ServeHTTP(rr, req)

		// Should return internal server error due to missing session/context
		assert.Equal(t, http.StatusInternalServerError, rr.Code)
		// No mock expectations since no session cookie was provided
	})
}

// TestHandler_ErrorHandling tests error scenarios
func TestHandler_ErrorHandling(t *testing.T) {
	t.Run("RegisterRoutes_NilRouter", func(t *testing.T) {
		mockSessionStore := &onrampMocks.MockSessionStore{}
		handler := NewHandler(mockSessionStore)

		// This should panic when trying to use nil router
		assert.Panics(t, func() {
			handler.RegisterRoutes(nil)
		})
	})

	t.Run("Handler_WithNilSessionStore", func(t *testing.T) {
		handler := NewHandler(nil)
		router := mux.NewRouter()

		// Should not panic when registering routes with nil session store
		assert.NotPanics(t, func() {
			handler.RegisterRoutes(router)
		})

		// Test that routes are still registered
		req := httptest.NewRequest(http.MethodGet, "/user/permissions", nil)
		rr := httptest.NewRecorder()

		// This will likely fail due to nil session store in middleware
		router.ServeHTTP(rr, req)

		// We expect some kind of error response, not a successful one
		assert.NotEqual(t, http.StatusOK, rr.Code)
	})
}

// BenchmarkHandler_RegisterRoutes benchmarks route registration performance
func BenchmarkHandler_RegisterRoutes(b *testing.B) {
	mockSessionStore := &onrampMocks.MockSessionStore{}
	handler := NewHandler(mockSessionStore)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		router := mux.NewRouter()
		handler.RegisterRoutes(router)
	}
}

// BenchmarkHandler_RequestHandling benchmarks request handling performance
func BenchmarkHandler_RequestHandling(b *testing.B) {
	mockSessionStore := &onrampMocks.MockSessionStore{}
	handler := NewHandler(mockSessionStore)
	router := mux.NewRouter()
	handler.RegisterRoutes(router)

	// Set up mock for benchmark
	mockSessionStore.On("GetSession", mock.AnythingOfType("string")).Return((*domain.Session)(nil), false)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/user/permissions", nil)
		rr := httptest.NewRecorder()
		router.ServeHTTP(rr, req)
	}
}
