package softwaregateway

import (
	"net/http"

	RestSoftwareGateway "synapse-its.com/shared/rest/onramp/softwaregateway"
	RestSoftwareGatewayConfig "synapse-its.com/shared/rest/onramp/softwaregateway/config"

	"github.com/gorilla/mux"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/softwaregateway", RestSoftwareGateway.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/softwaregateway", RestSoftwareGateway.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregateway/{identifier}", RestSoftwareGateway.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregateway/{identifier}", RestSoftwareGateway.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/softwaregateway/{identifier}", RestSoftwareGateway.DeleteHandler).Methods(http.MethodDelete)
	router.HandleFunc("/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.UpdateHandler).Methods(http.MethodPatch)
}
