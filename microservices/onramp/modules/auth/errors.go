package auth

import "errors"

var (
	ErrMissingIDToken           = errors.New("missing id_token in OAuth response")
	ErrExchangeFailed           = errors.New("failed to exchange OAuth code for token")
	ErrInvalidIDToken           = errors.New("invalid id_token in OAuth response")
	ErrGetUserPermissionsFailed = errors.New("failed to get user permissions")
	ErrGetConnectionsFailed     = errors.New("failed to get connections")
)
