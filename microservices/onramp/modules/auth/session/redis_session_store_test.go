package session

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/api/authorizer"
)

func TestNewRedisSessionStore(t *testing.T) {
	tests := []struct {
		name        string
		redisClient *redis.Client
		expectNil   bool
	}{
		{
			name:        "Valid Redis Client",
			redisClient: &redis.Client{},
			expectNil:   false,
		},
		{
			name:        "Nil Redis Client",
			redisClient: nil,
			expectNil:   false, // Constructor doesn't validate client
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			store := NewRedisSessionStore(tt.redisClient)

			if tt.expectNil {
				assert.Nil(t, store)
			} else {
				assert.NotNil(t, store)
				assert.Implements(t, (*domain.SessionStore)(nil), store)
			}
		})
	}
}

func TestRedisSessionStore_GetSession(t *testing.T) {
	tests := []struct {
		name            string
		sessionID       string
		setupMock       func(redismock.ClientMock)
		expectedSession *domain.Session
		expectedFound   bool
		expectedError   bool
	}{
		{
			name:      "Session Found Successfully",
			sessionID: "test-session-123",
			setupMock: func(mock redismock.ClientMock) {
				session := &domain.Session{
					UserID: "user-123",
					OAuthToken: &domain.Token{
						Token: oauth2.Token{
							AccessToken: "access-token-123",
							TokenType:   "Bearer",
							Expiry:      time.Now().Add(time.Hour),
						},
					},
					UserPermissions: &domain.UserPermissions{
						UserID:      "user-123",
						Permissions: []authorizer.Permission{},
					},
				}
				sessionJSON, _ := json.Marshal(session)
				key := RedisSessionKeyPrefix + "test-session-123"
				mock.ExpectGet(key).SetVal(string(sessionJSON))
			},
			expectedSession: &domain.Session{
				UserID: "user-123",
				OAuthToken: &domain.Token{
					Token: oauth2.Token{
						AccessToken: "access-token-123",
						TokenType:   "Bearer",
					},
				},
				UserPermissions: &domain.UserPermissions{
					UserID:      "user-123",
					Permissions: []authorizer.Permission{},
				},
			},
			expectedFound: true,
			expectedError: false,
		},
		{
			name:      "Session Not Found - Redis Nil",
			sessionID: "nonexistent-session",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "nonexistent-session"
				mock.ExpectGet(key).RedisNil()
			},
			expectedSession: nil,
			expectedFound:   false,
			expectedError:   false,
		},
		{
			name:      "Redis Error",
			sessionID: "error-session",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "error-session"
				mock.ExpectGet(key).SetErr(errors.New("redis connection error"))
			},
			expectedSession: nil,
			expectedFound:   false,
			expectedError:   true,
		},
		{
			name:      "Invalid JSON in Redis",
			sessionID: "invalid-json-session",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "invalid-json-session"
				mock.ExpectGet(key).SetVal("invalid json data")
			},
			expectedSession: nil,
			expectedFound:   false,
			expectedError:   true,
		},
		{
			name:      "Empty Session ID",
			sessionID: "",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + ""
				mock.ExpectGet(key).RedisNil()
			},
			expectedSession: nil,
			expectedFound:   false,
			expectedError:   false,
		},
		{
			name:      "Session with Minimal Data",
			sessionID: "minimal-session",
			setupMock: func(mock redismock.ClientMock) {
				session := &domain.Session{
					UserID: "user-456",
				}
				sessionJSON, _ := json.Marshal(session)
				key := RedisSessionKeyPrefix + "minimal-session"
				mock.ExpectGet(key).SetVal(string(sessionJSON))
			},
			expectedSession: &domain.Session{
				UserID: "user-456",
			},
			expectedFound: true,
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock Redis client
			db, mock := redismock.NewClientMock()
			store := NewRedisSessionStore(db)

			// Setup mock expectations
			tt.setupMock(mock)

			// Execute the method
			session, found := store.GetSession(tt.sessionID)

			// Assertions
			if tt.expectedError {
				assert.Nil(t, session)
				assert.False(t, found)
			} else {
				if tt.expectedFound {
					assert.NotNil(t, session)
					assert.True(t, found)
					assert.Equal(t, tt.expectedSession.UserID, session.UserID)

					// Compare OAuthToken if expected
					if tt.expectedSession.OAuthToken != nil {
						assert.NotNil(t, session.OAuthToken)
						assert.Equal(t, tt.expectedSession.OAuthToken.AccessToken, session.OAuthToken.AccessToken)
						assert.Equal(t, tt.expectedSession.OAuthToken.TokenType, session.OAuthToken.TokenType)
					}

					// Compare UserPermissions if expected
					if tt.expectedSession.UserPermissions != nil {
						assert.NotNil(t, session.UserPermissions)
						assert.Equal(t, tt.expectedSession.UserPermissions.UserID, session.UserPermissions.UserID)
					}
				} else {
					assert.Nil(t, session)
					assert.False(t, found)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestRedisSessionStore_SetSession(t *testing.T) {
	tests := []struct {
		name        string
		sessionID   string
		session     *domain.Session
		setupMock   func(redismock.ClientMock)
		expectError bool
	}{
		{
			name:      "Set Session Successfully",
			sessionID: "test-session-123",
			session: &domain.Session{
				UserID: "user-123",
				OAuthToken: &domain.Token{
					Token: oauth2.Token{
						AccessToken: "access-token-123",
						TokenType:   "Bearer",
						Expiry:      time.Now().Add(time.Hour),
					},
				},
				UserPermissions: &domain.UserPermissions{
					UserID:      "user-123",
					Permissions: []authorizer.Permission{},
				},
			},
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "test-session-123"
				// We can't predict the exact JSON, so we use Regexp to match any valid JSON
				mock.Regexp().ExpectSet(key, `.*`, 0).SetVal("OK")
			},
			expectError: false,
		},
		{
			name:      "Set Session with Redis Error",
			sessionID: "error-session",
			session: &domain.Session{
				UserID: "user-456",
			},
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "error-session"
				mock.Regexp().ExpectSet(key, `.*`, 0).SetErr(errors.New("redis set error"))
			},
			expectError: true,
		},
		{
			name:      "Set Nil Session",
			sessionID: "nil-session",
			session:   nil,
			setupMock: func(mock redismock.ClientMock) {
				// No Redis call expected since the implementation returns early for nil sessions
				// The method logs an error and returns without making any Redis operations
			},
			expectError: false,
		},
		{
			name:      "Set Session with Empty ID",
			sessionID: "",
			session: &domain.Session{
				UserID: "user-789",
			},
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + ""
				mock.Regexp().ExpectSet(key, `.*`, 0).SetVal("OK")
			},
			expectError: false,
		},
		{
			name:      "Set Session with Minimal Data",
			sessionID: "minimal-session",
			session: &domain.Session{
				UserID: "user-minimal",
			},
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "minimal-session"
				mock.Regexp().ExpectSet(key, `.*`, 0).SetVal("OK")
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock Redis client
			db, mock := redismock.NewClientMock()
			store := NewRedisSessionStore(db)

			// Setup mock expectations
			tt.setupMock(mock)

			// Execute the method
			store.SetSession(tt.sessionID, tt.session)

			// Verify all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestRedisSessionStore_ClearSession(t *testing.T) {
	tests := []struct {
		name        string
		sessionID   string
		setupMock   func(redismock.ClientMock)
		expectError bool
	}{
		{
			name:      "Clear Session Successfully",
			sessionID: "test-session-123",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "test-session-123"
				mock.ExpectDel(key).SetVal(1)
			},
			expectError: false,
		},
		{
			name:      "Clear Non-existent Session",
			sessionID: "nonexistent-session",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "nonexistent-session"
				mock.ExpectDel(key).SetVal(0)
			},
			expectError: false,
		},
		{
			name:      "Clear Session with Redis Error",
			sessionID: "error-session",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "error-session"
				mock.ExpectDel(key).SetErr(errors.New("redis del error"))
			},
			expectError: true,
		},
		{
			name:      "Clear Session with Empty ID",
			sessionID: "",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + ""
				mock.ExpectDel(key).SetVal(1)
			},
			expectError: false,
		},
		{
			name:      "Clear Session with Special Characters in ID",
			sessionID: "session-with-special-chars-!@#$%",
			setupMock: func(mock redismock.ClientMock) {
				key := RedisSessionKeyPrefix + "session-with-special-chars-!@#$%"
				mock.ExpectDel(key).SetVal(1)
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock Redis client
			db, mock := redismock.NewClientMock()
			store := NewRedisSessionStore(db)

			// Setup mock expectations
			tt.setupMock(mock)

			// Execute the method
			store.ClearSession(tt.sessionID)

			// Verify all expectations were met
			assert.NoError(t, mock.ExpectationsWereMet())
		})
	}
}

func TestRedisSessionStore_Integration(t *testing.T) {
	t.Run("Complete Session Lifecycle", func(t *testing.T) {
		// Create mock Redis client
		db, mock := redismock.NewClientMock()
		store := NewRedisSessionStore(db)

		sessionID := "integration-test-session"
		session := &domain.Session{
			UserID: "integration-user",
			OAuthToken: &domain.Token{
				Token: oauth2.Token{
					AccessToken: "integration-token",
					TokenType:   "Bearer",
				},
			},
			UserPermissions: &domain.UserPermissions{
				UserID:      "integration-user",
				Permissions: []authorizer.Permission{},
			},
		}

		// Test SetSession
		key := RedisSessionKeyPrefix + sessionID
		mock.Regexp().ExpectSet(key, `.*`, 0).SetVal("OK")

		store.SetSession(sessionID, session)

		// Test GetSession
		sessionJSON, _ := json.Marshal(session)
		mock.ExpectGet(key).SetVal(string(sessionJSON))

		retrievedSession, found := store.GetSession(sessionID)
		assert.True(t, found)
		assert.NotNil(t, retrievedSession)
		assert.Equal(t, session.UserID, retrievedSession.UserID)

		// Test ClearSession
		mock.ExpectDel(key).SetVal(1)

		store.ClearSession(sessionID)

		// Verify session is cleared
		mock.ExpectGet(key).RedisNil()

		clearedSession, found := store.GetSession(sessionID)
		assert.False(t, found)
		assert.Nil(t, clearedSession)

		// Verify all expectations were met
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisSessionStore_KeyFormat(t *testing.T) {
	t.Run("Verify Key Format", func(t *testing.T) {
		db, mock := redismock.NewClientMock()
		store := NewRedisSessionStore(db)

		sessionID := "test-session"
		expectedKey := RedisSessionKeyPrefix + sessionID

		// Verify the key format is correct
		assert.Equal(t, RedisSessionKeyPrefix+"test-session", expectedKey)

		// Test that the key is used correctly in operations
		mock.ExpectGet(expectedKey).RedisNil()
		store.GetSession(sessionID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}

func TestRedisSessionStore_ContextUsage(t *testing.T) {
	t.Run("Verify Context Usage", func(t *testing.T) {
		// This test verifies that the implementation uses context.Background()
		// We can't directly test this with redismock, but we can verify the behavior
		db, mock := redismock.NewClientMock()
		store := NewRedisSessionStore(db)

		sessionID := "context-test-session"
		key := RedisSessionKeyPrefix + sessionID

		// The implementation uses context.Background() internally
		// redismock will handle this automatically
		mock.ExpectGet(key).RedisNil()
		store.GetSession(sessionID)

		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
