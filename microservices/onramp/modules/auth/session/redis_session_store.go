package session

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/redis/go-redis/v9"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

const (
	RedisSessionKeyPrefix = "OnrampSession:"
)

type redisSessionStore struct {
	redisClient *redis.Client
}

func NewRedisSessionStore(redisClient *redis.Client) domain.SessionStore {
	return &redisSessionStore{
		redisClient: redisClient,
	}
}

func (s *redisSessionStore) GetSession(sessionID string) (*domain.Session, bool) {
	sessionKey := fmt.Sprintf("%s%s", RedisSessionKeyPrefix, sessionID)
	jsonString, err := connect.RedisGet(context.Background(), s.redisClient, sessionKey)
	if err != nil {
		logger.Errorf("Error getting session from Redis: %v", err)
		return nil, false
	}
	var session domain.Session
	err = json.Unmarshal([]byte(jsonString), &session)
	if err != nil {
		logger.Errorf("Error unmarshalling session from Redis: %v", err)
		return nil, false
	}
	return &session, true
}

func (s *redisSessionStore) SetSession(sessionID string, session *domain.Session) {
	sessionKey := fmt.Sprintf("%s%s", RedisSessionKeyPrefix, sessionID)
	if session == nil {
		logger.Errorf("Session data is nil, skipping set")
		return
	}
	jsonData, _ := json.Marshal(session)
	err := connect.RedisSet(context.Background(), s.redisClient, sessionKey, string(jsonData))
	if err != nil {
		logger.Errorf("Error setting session in Redis: %v", err)
		return
	}
}

func (s *redisSessionStore) ClearSession(sessionID string) {
	sessionKey := fmt.Sprintf("%s%s", RedisSessionKeyPrefix, sessionID)
	err := s.redisClient.Del(context.Background(), sessionKey).Err()
	if err != nil {
		logger.Errorf("Error clearing session in Redis: %v", err)
		return
	}
}
