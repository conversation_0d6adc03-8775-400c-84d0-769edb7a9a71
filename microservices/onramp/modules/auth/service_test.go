package auth

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/data"
	"synapse-its.com/onramp/domain"
	onrampMocks "synapse-its.com/onramp/mock"
)

func TestNewService(t *testing.T) {
	t.Parallel()

	// Arrange
	mockRepo := &onrampMocks.MockAuthRepository{}
	mockHasher := &onrampMocks.MockPasswordHasher{}
	mockTokenGen := &onrampMocks.MockTokenGenerator{}

	// Act
	service := NewService(mockRepo, mockHasher, mockTokenGen)

	// Assert
	assert.NotNil(t, service)
	assert.Implements(t, (*AuthService)(nil), service)
}

func TestService_BasicAuth(t *testing.T) {
	t.<PERSON>()

	tests := []struct {
		name           string
		request        *data.BasicAuthRequest
		setupMocks     func(*onrampMocks.MockAuthRepository, *onrampMocks.MockPasswordHasher, *onrampMocks.MockTokenGenerator)
		expectedResult *data.LoginResponse
		expectedError  error
	}{
		{
			name: "successful authentication",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.On("GetByUsername", ctx, "testuser").Return(user, authMethod, nil)
				repo.On("GetUserPermissions", ctx, user.ID).Return((*domain.UserPermissions)(nil))

				repo.On("UpdateLastLogin", ctx, user, authMethod).Return(nil)

				hasher.On("ComparePassword", "password123", "hashed_password").Return(true)

				tokenGen.On("GenerateToken", "testuser").Return("jwt_token", time.Now().Add(time.Hour), nil)
			},
			expectedResult: &data.LoginResponse{
				User:  createTestUser(),
				Token: "jwt_token",
			},
			expectedError: nil,
		},
		{
			name: "empty username",
			request: &data.BasicAuthRequest{
				Username: "",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "empty password",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "user not found",
			request: &data.BasicAuthRequest{
				Username: "nonexistent",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				repo.On("GetByUsername", ctx, "nonexistent").Return(nil, nil, nil)
			},
			expectedResult: nil,
			expectedError:  domain.ErrUserNotFound,
		},
		{
			name: "repository error",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				repo.On("GetByUsername", ctx, "testuser").Return(nil, nil, errors.New("database error"))
			},
			expectedResult: nil,
			expectedError:  errors.New("database error"),
		},
		{
			name: "user found but auth method missing",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				repo.On("GetByUsername", ctx, "testuser").Return(user, nil, nil)
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "auth method found but user missing",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				authMethod := createTestAuthMethod(uuid.New())
				repo.On("GetByUsername", ctx, "testuser").Return(nil, authMethod, nil)
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "account disabled",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				authMethod.IsEnabled = false

				repo.On("GetByUsername", ctx, "testuser").Return(user, authMethod, nil)
			},
			expectedResult: nil,
			expectedError:  domain.ErrAccountDisabled,
		},
		{
			name: "invalid password",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "wrongpassword",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.On("GetByUsername", ctx, "testuser").Return(user, authMethod, nil)

				hasher.On("ComparePassword", "wrongpassword", "hashed_password").Return(false)
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "update last login error",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.On("GetByUsername", ctx, "testuser").Return(user, authMethod, nil)

				repo.On("UpdateLastLogin", ctx, user, authMethod).Return(errors.New("update error"))

				hasher.On("ComparePassword", "password123", "hashed_password").Return(true)
			},
			expectedResult: nil,
			expectedError:  errors.New("update error"),
		},
		{
			name: "token generation error",
			request: &data.BasicAuthRequest{
				Username: "testuser",
				Password: "password123",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)

				repo.On("GetByUsername", ctx, "testuser").Return(user, authMethod, nil)
				repo.On("GetUserPermissions", ctx, user.ID).Return((*domain.UserPermissions)(nil))

				repo.On("UpdateLastLogin", ctx, user, authMethod).Return(nil)

				hasher.On("ComparePassword", "password123", "hashed_password").Return(true)

				tokenGen.On("GenerateToken", "testuser").Return("", time.Time{}, errors.New("token generation error"))
			},
			expectedResult: nil,
			expectedError:  errors.New("token generation error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockRepo := &onrampMocks.MockAuthRepository{}
			mockHasher := &onrampMocks.MockPasswordHasher{}
			mockTokenGen := &onrampMocks.MockTokenGenerator{}

			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockHasher, mockTokenGen)
			}

			service := NewService(mockRepo, mockHasher, mockTokenGen)
			ctx := context.Background()

			// Act
			result, err := service.BasicAuth(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Token, result.Token)
				assert.NotNil(t, result.User)
			}
		})
	}
}

func TestService_Register(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		request       *data.RegisterRequest
		setupMocks    func(*onrampMocks.MockAuthRepository, *onrampMocks.MockPasswordHasher, *onrampMocks.MockTokenGenerator)
		expectedError error
	}{
		{
			name: "successful registration",
			request: &data.RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "newuser",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				// User doesn't exist
				repo.On("GetByUsername", ctx, "newuser").Return(nil, nil, nil)

				hasher.On("HashPassword", "password123").Return("hashed_" + "password123")

				repo.On("CreateBasicAuthUser", ctx, mock.MatchedBy(func(u *domain.User) bool {
					return u.FirstName == "John" && u.LastName == "Doe"
				}), mock.MatchedBy(func(a *domain.AuthMethod) bool {
					return a.Type == domain.AuthMethodTypeUsernamePassword && a.UserName == "newuser" && a.PasswordHash == "hashed_password123" && a.Email == "<EMAIL>"
				})).Return(nil)
			},
			expectedError: nil,
		},
		{
			name: "empty username",
			request: &data.RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed
			},
			expectedError: domain.ErrInvalidInput,
		},
		{
			name: "empty password",
			request: &data.RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "newuser",
				Password:  "",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed
			},
			expectedError: domain.ErrInvalidInput,
		},
		{
			name: "user already exists",
			request: &data.RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "existinguser",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				authMethod.UserName = "existinguser"

				repo.On("GetByUsername", ctx, "existinguser").Return(user, authMethod, nil)
			},
			expectedError: domain.ErrUserAlreadyExists,
		},
		{
			name: "case insensitive username check",
			request: &data.RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "EXISTINGUSER",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				authMethod.UserName = "existinguser"

				repo.On("GetByUsername", ctx, "EXISTINGUSER").Return(user, authMethod, nil)
			},
			expectedError: domain.ErrUserAlreadyExists,
		},
		{
			name: "create user error",
			request: &data.RegisterRequest{
				FirstName: "John",
				LastName:  "Doe",
				Username:  "newuser",
				Password:  "password123",
				Email:     "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				repo.On("GetByUsername", ctx, "newuser").Return(nil, nil, nil)

				hasher.On("HashPassword", "password123").Return("hashed_" + "password123")

				repo.On("CreateBasicAuthUser", ctx, mock.MatchedBy(func(u *domain.User) bool {
					return u.FirstName == "John" && u.LastName == "Doe"
				}), mock.MatchedBy(func(a *domain.AuthMethod) bool {
					return a.Type == domain.AuthMethodTypeUsernamePassword && a.UserName == "newuser" && a.PasswordHash == "hashed_password123" && a.Email == "<EMAIL>"
				})).Return(errors.New("database error"))
			},
			expectedError: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockRepo := &onrampMocks.MockAuthRepository{}
			mockHasher := &onrampMocks.MockPasswordHasher{}
			mockTokenGen := &onrampMocks.MockTokenGenerator{}

			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockHasher, mockTokenGen)
			}

			service := NewService(mockRepo, mockHasher, mockTokenGen)
			ctx := context.Background()

			// Act
			err := service.Register(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestService_HandleOIDCLogin(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		request        *data.OIDCLoginRequest
		setupMocks     func(*onrampMocks.MockAuthRepository, *onrampMocks.MockPasswordHasher, *onrampMocks.MockTokenGenerator)
		expectedResult *data.LoginResponse
		expectedError  error
	}{
		{
			name: "new user creation",
			request: &data.OIDCLoginRequest{
				Subject: "oidc-subject",
				Issuer:  "oidc-issuer",
				Name:    "Jane Doe",
				Email:   "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				// Use a deterministic UUID for the created user
				user := &domain.User{
					ID:           uuid.MustParse("*************-479f-b899-f80db6611a2a"),
					FirstName:    "John",
					LastName:     "Doe",
					Mobile:       "+1234567890",
					IanaTimezone: "America/Chicago",
					Description:  "Test user",
				}
				repo.On("GetByOIDCSubject", ctx, "oidc-issuer", "oidc-subject").Return(nil, nil, nil)
				repo.On("CreateOIDCUser", ctx, mock.AnythingOfType("*domain.User"), mock.AnythingOfType("*domain.AuthMethod")).Return(nil).Run(func(args mock.Arguments) {
					// Simulate DB setting the user ID after creation
					if u, ok := args.Get(1).(*domain.User); ok {
						u.ID = user.ID
					}
				})
				repo.On("GetUserPermissions", ctx, user.ID).Return((*domain.UserPermissions)(nil))
				tokenGen.On("GenerateToken", user.ID.String()).Return("jwt_token", time.Now().Add(time.Hour), nil)
			},
			expectedResult: &data.LoginResponse{
				Token: "jwt_token",
			},
			expectedError: nil,
		},
		{
			name: "existing user",
			request: &data.OIDCLoginRequest{
				Subject: "oidc-subject",
				Issuer:  "oidc-issuer",
				Name:    "Jane Doe",
				Email:   "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				repo.On("GetByOIDCSubject", ctx, "oidc-issuer", "oidc-subject").Return(user, authMethod, nil)
				repo.On("UpdateLastLogin", ctx, user, authMethod).Return(nil)
				repo.On("GetUserPermissions", ctx, user.ID).Return((*domain.UserPermissions)(nil))
				tokenGen.On("GenerateToken", user.ID.String()).Return("jwt_token", time.Now().Add(time.Hour), nil)
			},
			expectedResult: &data.LoginResponse{
				Token: "jwt_token",
			},
			expectedError: nil,
		},
		{
			name: "invalid input (missing subject)",
			request: &data.OIDCLoginRequest{
				Subject: "",
				Issuer:  "oidc-issuer",
			},
			setupMocks:     nil,
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "invalid input (missing issuer)",
			request: &data.OIDCLoginRequest{
				Subject: "oidc-subject",
				Issuer:  "",
			},
			setupMocks:     nil,
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "repo error on GetByOIDCSubject",
			request: &data.OIDCLoginRequest{
				Subject: "oidc-subject",
				Issuer:  "oidc-issuer",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				repo.On("GetByOIDCSubject", ctx, "oidc-issuer", "oidc-subject").Return(nil, nil, errors.New("db error"))
			},
			expectedResult: nil,
			expectedError:  errors.New("db error"),
		},
		{
			name: "CreateOIDCUser error",
			request: &data.OIDCLoginRequest{
				Subject: "oidc-subject",
				Issuer:  "oidc-issuer",
				Name:    "Jane Doe",
				Email:   "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				repo.On("GetByOIDCSubject", ctx, "oidc-issuer", "oidc-subject").Return(nil, nil, nil)
				repo.On("CreateOIDCUser", ctx, mock.AnythingOfType("*domain.User"), mock.AnythingOfType("*domain.AuthMethod")).Return(errors.New("create error"))
			},
			expectedResult: nil,
			expectedError:  errors.New("create error"),
		},
		{
			name: "UpdateLastLogin error for existing user",
			request: &data.OIDCLoginRequest{
				Subject: "oidc-subject",
				Issuer:  "oidc-issuer",
				Name:    "Jane Doe",
				Email:   "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				repo.On("GetByOIDCSubject", ctx, "oidc-issuer", "oidc-subject").Return(user, authMethod, nil)
				repo.On("UpdateLastLogin", ctx, user, authMethod).Return(errors.New("update error"))
			},
			expectedResult: nil,
			expectedError:  errors.New("update error"),
		},
		{
			name: "token generation error",
			request: &data.OIDCLoginRequest{
				Subject: "oidc-subject",
				Issuer:  "oidc-issuer",
				Name:    "Jane Doe",
				Email:   "<EMAIL>",
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				ctx := context.Background()
				user := createTestUser()
				authMethod := createTestAuthMethod(user.ID)
				repo.On("GetByOIDCSubject", ctx, "oidc-issuer", "oidc-subject").Return(user, authMethod, nil)
				repo.On("UpdateLastLogin", ctx, user, authMethod).Return(nil)
				repo.On("GetUserPermissions", ctx, user.ID).Return((*domain.UserPermissions)(nil))
				tokenGen.On("GenerateToken", user.ID.String()).Return("", time.Time{}, errors.New("token error"))
			},
			expectedResult: nil,
			expectedError:  errors.New("token error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockRepo := &onrampMocks.MockAuthRepository{}
			mockHasher := &onrampMocks.MockPasswordHasher{}
			mockTokenGen := &onrampMocks.MockTokenGenerator{}

			if tt.setupMocks != nil {
				tt.setupMocks(mockRepo, mockHasher, mockTokenGen)
			}

			service := NewService(mockRepo, mockHasher, mockTokenGen)
			ctx := context.Background()

			// Act
			result, err := service.HandleOIDCLogin(ctx, tt.request)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.expectedResult.Token, result.Token)
			}
		})
	}
}

func TestService_ProcessOAuth2Callback(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		request        *data.OAuth2CallbackRequest
		setupMocks     func(*onrampMocks.MockAuthRepository, *onrampMocks.MockPasswordHasher, *onrampMocks.MockTokenGenerator)
		expectedResult *data.OAuth2CallbackResponse
		expectedError  error
	}{
		{
			name: "successful OAuth2 callback - existing user",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"sub":   "test-subject",
					"iss":   "test-issuer",
					"email": "<EMAIL>",
					"name":  "John Doe",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				user := createTestUser()
				authMethod := &domain.AuthMethod{
					ID:        uuid.New(),
					UserID:    user.ID,
					Type:      domain.AuthMethodTypeOIDC,
					Sub:       "test-subject",
					Issuer:    "test-issuer",
					Email:     "<EMAIL>",
					IsEnabled: true,
				}

				repo.On("GetByOIDCSubject", mock.Anything, mock.Anything, mock.Anything).Return(user, authMethod, nil)

				repo.On("UpdateLastLogin", mock.Anything, mock.Anything, mock.Anything).Return(nil)

				repo.On("GetUserPermissions", mock.Anything, mock.Anything).Return((*domain.UserPermissions)(nil))

				tokenGen.On("GenerateToken", mock.Anything).Return("test-token", time.Now().Add(time.Hour), nil)
			},
			expectedResult: &data.OAuth2CallbackResponse{
				User:  createTestUser(),
				Token: "test-token",
			},
			expectedError: nil,
		},
		{
			name: "successful OAuth2 callback - new user",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"sub":   "new-subject",
					"iss":   "new-issuer",
					"email": "<EMAIL>",
					"name":  "Jane Smith",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// User doesn't exist initially
				repo.On("GetByOIDCSubject", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil, nil)

				// Create new user
				repo.On("CreateOIDCUser", mock.Anything, mock.Anything, mock.Anything).Return(nil)

				repo.On("GetUserPermissions", mock.Anything, mock.Anything).Return((*domain.UserPermissions)(nil))

				tokenGen.On("GenerateToken", mock.Anything).Return("new-user-token", time.Now().Add(time.Hour), nil)
			},
			expectedResult: &data.OAuth2CallbackResponse{
				User: &domain.User{
					ID:           uuid.UUID{}, // Will be set by CreateOIDCUser
					FirstName:    "Jane",
					LastName:     "Smith",
					Mobile:       "",
					IanaTimezone: "",
					Description:  "",
				},
				Token: "new-user-token",
			},
			expectedError: nil,
		},
		{
			name: "invalid state mismatch",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "wrong-state",
				StateCookie: "correct-state",
				Claims: map[string]interface{}{
					"sub":   "test-subject",
					"iss":   "test-issuer",
					"email": "<EMAIL>",
					"name":  "John Doe",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "missing sub claim",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"iss":   "test-issuer",
					"email": "<EMAIL>",
					"name":  "John Doe",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "sub claim is not string",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"sub":   123, // Not a string
					"iss":   "test-issuer",
					"email": "<EMAIL>",
					"name":  "John Doe",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "iss claim is not string",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"sub":   "test-subject",
					"iss":   456, // Not a string
					"email": "<EMAIL>",
					"name":  "John Doe",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "missing iss claim",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"sub":   "test-subject",
					"email": "<EMAIL>",
					"name":  "John Doe",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				// No mocks needed for this case
			},
			expectedResult: nil,
			expectedError:  domain.ErrInvalidCredentials,
		},
		{
			name: "HandleOIDCLogin returns error",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"sub":   "test-subject",
					"iss":   "test-issuer",
					"email": "<EMAIL>",
					"name":  "John Doe",
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				repo.On("GetByOIDCSubject", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil, errors.New("database error"))
			},
			expectedResult: nil,
			expectedError:  errors.New("database error"),
		},
		{
			name: "optional claims are missing",
			request: &data.OAuth2CallbackRequest{
				Code:        "test-code",
				State:       "test-state",
				StateCookie: "test-state",
				Claims: map[string]interface{}{
					"sub": "test-subject",
					"iss": "test-issuer",
					// email and name are missing
				},
				OAuthToken: &oauth2.Token{
					AccessToken: "test-access-token",
					TokenType:   "Bearer",
				},
			},
			setupMocks: func(repo *onrampMocks.MockAuthRepository, hasher *onrampMocks.MockPasswordHasher, tokenGen *onrampMocks.MockTokenGenerator) {
				user := createTestUser()
				authMethod := &domain.AuthMethod{
					ID:        uuid.New(),
					UserID:    user.ID,
					Type:      domain.AuthMethodTypeOIDC,
					Sub:       "test-subject",
					Issuer:    "test-issuer",
					Email:     "",
					IsEnabled: true,
				}

				repo.On("GetByOIDCSubject", mock.Anything, mock.Anything, mock.Anything).Return(user, authMethod, nil)

				repo.On("UpdateLastLogin", mock.Anything, mock.Anything, mock.Anything).Return(nil)

				repo.On("GetUserPermissions", mock.Anything, mock.Anything).Return((*domain.UserPermissions)(nil))

				tokenGen.On("GenerateToken", mock.Anything).Return("test-token", time.Now().Add(time.Hour), nil)
			},
			expectedResult: &data.OAuth2CallbackResponse{
				User:  createTestUser(),
				Token: "test-token",
			},
			expectedError: nil,
		},
	}

	for _, tc := range tests {
		tc := tc // Capture range variable
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			mockRepo := &onrampMocks.MockAuthRepository{}
			mockHasher := &onrampMocks.MockPasswordHasher{}
			mockTokenGen := &onrampMocks.MockTokenGenerator{}

			if tc.setupMocks != nil {
				tc.setupMocks(mockRepo, mockHasher, mockTokenGen)
			}

			service := NewService(mockRepo, mockHasher, mockTokenGen)

			// Act
			result, err := service.ProcessOAuth2Callback(context.Background(), tc.request)

			// Assert
			if tc.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tc.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tc.expectedResult.User.FirstName, result.User.FirstName)
				assert.Equal(t, tc.expectedResult.User.LastName, result.User.LastName)
				assert.Equal(t, tc.expectedResult.Token, result.Token)
			}
		})
	}
}
