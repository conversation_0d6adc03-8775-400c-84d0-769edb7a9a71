package pkg

import security "synapse-its.com/shared/api/security"

type PasswordHasher interface {
	HashPassword(password string) string
	ComparePassword(password, hash string) bool
}

func NewPasswordHasher() PasswordHasher {
	return &passwordHasher{}
}

type passwordHasher struct{}

func (h *passwordHasher) HashPassword(password string) string {
	return security.CalculateSHA256(password)
}

func (h *passwordHasher) ComparePassword(password, hash string) bool {
	return security.CalculateSHA256(password) == hash
}
