package pkg

import (
	"time"

	jwttokens "synapse-its.com/shared/api/jwttokens"
)

type TokenGenerator interface {
	GenerateToken(username string) (string, time.Time, error)
}

func NewTokenGenerator() TokenGenerator {
	return &tokenGenerator{}
}

type tokenGenerator struct{}

func (t *tokenGenerator) GenerateToken(username string) (string, time.Time, error) {
	return jwttokens.CreateJwtTokenUsingDuration(username, 1*time.Hour, jwttokens.UserPermissions{})
}
