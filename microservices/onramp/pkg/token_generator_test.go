package pkg

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestNewTokenGenerator(t *testing.T) {
	generator := NewTokenGenerator()

	assert.NotNil(t, generator, "NewTokenGenerator should return a non-nil generator")
	assert.Implements(t, (*TokenGenerator)(nil), generator, "Should implement TokenGenerator interface")

	// Verify it returns the concrete type
	_, ok := generator.(*tokenGenerator)
	assert.True(t, ok, "Should return a *tokenGenerator instance")
}

func TestTokenGenerator_GenerateToken(t *testing.T) {
	generator := NewTokenGenerator()

	tests := []struct {
		name     string
		username string
		wantErr  bool
	}{
		{
			name:     "valid username",
			username: "testuser",
			wantErr:  false,
		},
		{
			name:     "empty username",
			username: "",
			wantErr:  false, // Should still work with empty username
		},
		{
			name:     "username with special characters",
			username: "<EMAIL>",
			wantErr:  false,
		},
		{
			name:     "long username",
			username: "very_long_username_with_many_characters_that_should_still_work",
			wantErr:  false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			token, expiration, err := generator.GenerateToken(tc.username)

			if tc.wantErr {
				assert.Error(t, err)
				assert.Empty(t, token)
				assert.True(t, expiration.IsZero())
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, token, "Token should not be empty")
				assert.False(t, expiration.IsZero(), "Expiration should not be zero")

				// Verify the expiration is approximately 1 hour from now
				now := time.Now()
				expectedExpiration := now.Add(1 * time.Hour)
				timeDiff := expiration.Sub(expectedExpiration)

				// Allow for some time variation (within 1 minute)
				assert.True(t, timeDiff < time.Minute && timeDiff > -time.Minute,
					"Expiration should be approximately 1 hour from now")
			}
		})
	}
}

func TestTokenGenerator_GenerateToken_Consistency(t *testing.T) {
	generator := NewTokenGenerator()
	username := "testuser"

	// Generate multiple tokens for the same user
	tokens := make([]string, 3)
	expirations := make([]time.Time, 3)

	for i := 0; i < 3; i++ {
		token, exp, err := generator.GenerateToken(username)
		assert.NoError(t, err)

		tokens[i] = token
		expirations[i] = exp
	}

	// Tokens should be different (because they contain timestamps)
	// but all should be valid
	for i := 0; i < 3; i++ {
		assert.NotEmpty(t, tokens[i])
		assert.False(t, expirations[i].IsZero())
	}
}

func TestTokenGenerator_Interface_Compliance(t *testing.T) {
	// Verify that tokenGenerator implements TokenGenerator interface
	var _ TokenGenerator = (*tokenGenerator)(nil)

	// This test will fail at compile time if the interface is not implemented correctly
	generator := &tokenGenerator{}
	assert.NotNil(t, generator)

	// Test that the method exists and has correct signature
	token, expiration, err := generator.GenerateToken("test")
	assert.NotNil(t, token)
	assert.NotNil(t, expiration)
	assert.Nil(t, err)
}
