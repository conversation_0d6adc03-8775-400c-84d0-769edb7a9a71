import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class RolesPermissionsService {
  constructor(private http: HttpClient) { }
  getRoles(orgId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${orgId}/roles`);
  }
  createRole(orgId: any, roleData: any): Observable<any> {
    return this.http.post<any>(`/api/organizations/${orgId}/roles`, roleData);
  }
  deleteRoles(orgId: any, roleId: any): Observable<any[]> {
    return this.http.delete<any[]>(`/api/organizations/${orgId}/roles/${roleId}`);
  }
  getRolesTemplatesCustom(orgId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${orgId}/role-templates`);
  }
  getPermissions(orgId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${orgId}/permissions`);
  }
  updatePermissions(orgId: any, permissionId: any, roleId: any, payload: any): Observable<any[]> {
    return this.http.patch<any[]>(`/api/organizations/${orgId}/permissions/${permissionId}/roles/${roleId}`, payload);
  }
  // deletePermissions(orgId: any): Observable<any[]> {
  //   return this.http.put<any[]>(`/api/${orgId}/roles/&{roleId}`);
  // }
}