.register-container {
  max-width: 439px;
  margin: 0 auto;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #D9D9D9;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.register-container h2 {
  font-size: 24px;
  margin-bottom: 4px;
  font-weight: bold;
}

.register-subtitle {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.register-container .form-register nz-form-control ::ng-deep .ant-form-item-control-input-content {
  display: grid;
  gap: 8px;
}

.register-container .form-register button,
.register-container nz-form-control ::ng-deep .ant-form-item-control-input-content nz-input-group {
  height: 40px;
  border-radius: 8px;
}

nz-form-item,
.register-container .form-register hr,
.register-container .form-btn .btn-register {
  margin-bottom: 24px;
}

nz-form-control {
  width: 100%;
}

nz-button {
  width: 100%;
  margin-top: 10px;
}

nz-form-control .ant-form-item-explain-error {
  color: #ff4d4f;
  font-size: 12px;
}

nz-input-group {
  display: flex;
  align-items: center;
}

[nz-icon] {
  color: rgba(0, 0, 0, 0.45);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .register-container {
    max-width: 100%;
    margin: 20px 16px;
    padding: 16px;
    position: relative;
    top: auto;
    left: auto;
    transform: none;
  }

  .register-container h2 {
    font-size: 20px;
  }

  .register-subtitle {
    font-size: 12px;
    margin-bottom: 16px;
  }

  .register-container .form-register nz-form-control ::ng-deep .ant-form-item-control-input-content {
    gap: 6px;
  }

  .register-container .form-register button,
  .register-container nz-form-control ::ng-deep .ant-form-item-control-input-content input {
    height: 36px;
  }

  nz-form-item,
  .register-container .form-register hr,
  .register-container .form-btn .btn-register {
    margin-bottom: 16px;
  }

  nz-form-control .ant-form-item-explain-error {
    font-size: 11px;
  }

  nz-button {
    margin-top: 8px;
  }
}

/* Desktop Adjustments (for larger screens) */
@media (min-width: 769px) {
  .register-container {
    min-height: 500px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}