<div class="register-container">
  <h2>Create New Account</h2>
  <p class="register-subtitle">
    Use this option only if there is no SSO option available from your company.
  </p>

  <form nz-form class="form-register" [formGroup]="registerForm" (ngSubmit)="onSubmit()">
    <nz-form-item>
      <nz-form-control nzErrorTip="Please enter your First Name!">
        <span>First Name</span>
        <input id="firstname" nz-input formControlName="firstname" placeholder="First Name" />
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control nzErrorTip="Please enter your Last Name!">
        <span>Last Name</span>
        <input id="lastname" nz-input formControlName="lastname" placeholder="Last Name" />
      </nz-form-control>
    </nz-form-item>
    <hr>
    <nz-form-item>
      <nz-form-control nzErrorTip="Please enter a User Name!">
        <span>Username</span>
        <nz-input-group nzPrefixIcon="user">
          <input id="username" formControlName="username" nz-input placeholder="User Name" />
        </nz-input-group>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control nzErrorTip="Password must be at least 6 characters!">
        <span>Password</span>
        <nz-input-group [nzPrefixIcon]="passwordVisible ? 'eye' : 'lock'" [nzSuffix]="suffixTemplate">
          <input id="password" formControlName="password" nz-input placeholder="Password"
            [type]="passwordVisible ? 'text' : 'password'" autocomplete="current-password" />
        </nz-input-group>
        <ng-template #suffixTemplate>
          <span nz-icon [nzType]="passwordVisible ? 'eye-invisible' : 'eye'" style="cursor: pointer;"
            (click)="togglePasswordVisibility()"></span>
        </ng-template>
      </nz-form-control>
    </nz-form-item>
    <nz-form-item>
      <nz-form-control nzErrorTip="Please enter a valid email!">
        <span>E-mail Address</span>
        <nz-input-group nzPrefixIcon="mail">
          <input id="email" formControlName="email" nz-input placeholder="Email" />
        </nz-input-group>
      </nz-form-control>
    </nz-form-item>
    <div class="form-btn" style="text-align: center;">
      <button id="btn-submit" type="submit" class="btn-register" [nzLoading]="isLoading" nz-button nzType="default"
        nzBlock>
        Create New Account
      </button>
      <span>Already have an account? <a (click)="handleLogin()">Login now</a></span>
    </div>
  </form>
</div>