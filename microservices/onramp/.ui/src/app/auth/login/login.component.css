.login-container {
  max-width: 439px;
  margin: 0 auto;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #D9D9D9;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.login-container h2 {
  font-size: 24px;
  margin-bottom: 4px;
  font-weight: bold;
}

.login-container .form-login {
  border-bottom: 1px solid #000000;
  padding-bottom: 24px;
}

.login-container .form-login nz-form-control ::ng-deep .ant-form-item-control-input-content {
  display: grid;
  gap: 8px;
}

.login-container button,
.login-container .form-login nz-form-control ::ng-deep .ant-form-item-control-input-content nz-input-group {
  height: 40px;
  border-radius: 8px;
}

.login-subtitle,
.sso-subtitle,
.create-account-subtitle {
  color: #757575;
  font-size: 14px;
  margin-bottom: 24px;
}

nz-form-item {
  margin-bottom: 24px;
}

nz-form-label {
  font-weight: bold;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.button-group button {
  width: 48%;
}

.sso-section,
.create-account-section {
  margin-top: 30px;
}

.login-container h3 {
  font-size: 18px;
  margin-bottom: 4px;
  font-weight: bold;
  line-height: 29px;
}

nz-button {
  width: 100%;
  margin-top: 10px;
}

nz-input-group {
  display: flex;
  align-items: center;
}

[nz-icon] {
  color: rgba(0, 0, 0, 0.45);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .login-container {
    max-width: 100%;
    margin: 20px 16px;
    padding: 16px;
    position: relative;
    top: auto;
    left: auto;
    transform: none;
  }

  .login-container h2 {
    font-size: 20px;
  }

  .login-subtitle,
  .sso-subtitle,
  .create-account-subtitle {
    font-size: 12px;
    margin-bottom: 16px;
  }

  .login-container h3 {
    font-size: 16px;
    line-height: 24px;
  }

  .login-container .form-login {
    padding-bottom: 16px;
  }

  .login-container .form-login nz-form-control ::ng-deep .ant-form-item-control-input-content {
    gap: 6px;
  }

  .login-container button,
  .login-container .form-login nz-form-control ::ng-deep .ant-form-item-control-input-content nz-input-group {
    height: 36px;
  }

  nz-form-item {
    margin-bottom: 16px;
  }

  .button-group {
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
  }

  .button-group button {
    width: 100%;
  }

  .sso-section,
  .create-account-section {
    margin-top: 20px;
  }

  nz-button {
    margin-top: 8px;
  }
}

/* Desktop Adjustments (for larger screens) */
@media (min-width: 769px) {
  .login-container {
    min-height: 500px;
    /* Ensure consistent height for desktop */
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}