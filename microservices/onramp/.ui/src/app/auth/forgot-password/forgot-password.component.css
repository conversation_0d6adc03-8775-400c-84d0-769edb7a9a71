.forgot-password-container {
  max-width: 439px;
  margin: 100px auto;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #D9D9D9;
}

.forgot-password-container h2 {
  text-align: center;
  margin-bottom: 8px;
  font-size: 24px;
}

.forgot-password-subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 24px;
  font-size: 14px;
}

.form-forgot-password {
  display: grid;
  gap: 24px;
}

.forgot-password-container .text-danger {
  color: #ff4d4f;
  font-size: 12px;
  margin-left: 5px;
}

.forgot-password-container button,
.forgot-password-container .form-forgot-password nz-form-control ::ng-deep .ant-form-item-control-input-content nz-input-group {
  height: 40px;
  border-radius: 8px;
}

nz-form-item {
  margin-bottom: 16px;
}

nz-form-control span {
  display: flex;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
}

.form-forgot-password .button-group {
  display: flex;
  justify-content: space-between;
  padding-bottom: 24px;
  border-bottom: 1px solid #000000;
}

.form-forgot-password .button-group button {
  flex: 1;
}

.form-forgot-password .btn-action {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  color: #6C757D;
  cursor: pointer;
}

.form-forgot-password .btn-action:hover {
  color: #565E64;
}

nz-input-group {
  display: flex;
  align-items: center;
}

[nz-icon] {
  color: rgba(0, 0, 0, 0.45);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .forgot-password-container {
    max-width: 100%;
    margin: 20px 16px;
    padding: 16px;
    position: relative;
    top: auto;
    left: auto;
    transform: none;
  }

  .forgot-password-container h2 {
    font-size: 20px;
  }

  .forgot-password-subtitle {
    font-size: 12px;
    margin-bottom: 16px;
  }

  .form-forgot-password {
    gap: 16px;
  }

  nz-form-item {
    margin-bottom: 12px;
  }

  nz-form-control span {
    font-size: 12px;
    margin-bottom: 6px;
  }

  .forgot-password-container .text-danger {
    font-size: 11px;
    margin-left: 3px;
  }

  .forgot-password-container button,
  .forgot-password-container .form-forgot-password nz-form-control ::ng-deep .ant-form-item-control-input-content nz-input-group {
    height: 36px;
  }

  .form-forgot-password .button-group {
    flex-direction: column;
    gap: 8px;
    padding-bottom: 16px;
  }

  .form-forgot-password .button-group button {
    width: 100%;
  }
}