<nz-layout>
  <nz-header>
    <div class="form-header">
      <h1 routerLink="/">OEM Dashboard</h1>
      <div style="display: flex; align-items: center;">
        <ng-container *ngIf="isLoading; else content">
          <nz-avatar [nzText]="'L'"></nz-avatar>
          <span style="margin: 0 8px;">Loading...</span>
        </ng-container>
        <ng-template #content>
          <nz-avatar [nzText]="getCombinedAvatarText()"></nz-avatar>
          <span style="margin: 0 8px;">{{ getCombinedDisplayName() }}</span>
        </ng-template>
        <nav>
          <button class="btn-login h-4 br-8" nz-button nzType="primary" *ngIf="!userApi && !userName" (click)="login()">
            Login
          </button>
          <button id="btn-logout" class="btn-logout h-4 br-8" nz-button nzType="primary" *ngIf="userApi || userName"
            (click)="logout()">
            Log out
          </button>
        </nav>
      </div>
    </div>
  </nz-header>
</nz-layout>