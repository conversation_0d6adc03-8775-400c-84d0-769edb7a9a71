import { Component, OnInit } from '@angular/core';
import { AuthService, User, UserProfile } from '../../core/services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css'],
  standalone: false
})
export class HeaderComponent implements OnInit {
  userName: string | null = null;
  userApi: User | null = null;
  isLoading: boolean = true;
  constructor(
    private auth: AuthService,
    private router: Router,
  ) {
  }

  ngOnInit() {
    this.isLoading = true;
    this.auth.getProfile().subscribe({
      next: (profile: UserProfile) => {
        if (profile.name || profile.email) {
          this.userName = profile.name ?? profile.email;
        } else {
          this.userName = null;
        }
        this.isLoading = false;
      },
      error: () => {
        this.userName = null;
        this.isLoading = false;
      }
    });
  }
  getDisplayName(user: User | null): string {
    if (!user) return 'User';
    return `${user.firstName}`.trim() || user.username || 'User';
  }
  getCombinedDisplayName(): string {
    if (this.userApi) {
      return this.getDisplayName(this.userApi);
    }
    return this.userName || 'User';
  }

  getAvatarText(user: User | null): string {
    if (!user) return 'U';
    const name = this.getDisplayName(user);
    return name[0]?.toUpperCase() || 'U';
  }
  getCombinedAvatarText(): string {
    if (this.userApi) {
      return this.getAvatarText(this.userApi);
    }
    return (this.userName?.[0] ?? 'U').toUpperCase();
  }
  login() {
    this.router.navigate(['/login']);
  }

  logout() {
    this.auth.logout();
  }
}
