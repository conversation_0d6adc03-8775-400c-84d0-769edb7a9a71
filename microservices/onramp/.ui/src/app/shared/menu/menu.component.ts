import { Component, EventEmitter, Output } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-menu',
  standalone: false,
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.css'
})
export class MenuComponent {
  selectedKey: string | null = null;
  isCollapsed = false;
  openMap: { [name: string]: boolean } = {
    sub1: false,
    sub2: false,
    sub3: false,
    sub4: false
  };
  isFavorite = false;
  constructor(private router: Router) { }
  ngOnInit() {
    this.router.events.subscribe(() => {
      this.checkActiveMenu();
    });
  }
  openHandler(key: string, open?: boolean): void {
    if (open !== undefined) {
      this.openMap[key] = open;
    } else {
      this.openMap[key] = true;
    }
  }
  private checkActiveMenu(): void {
    const activeRoutes = ['/permissions', '/users'];
    if (activeRoutes.some(route => this.isActive(route))) {
      this.openMap['sub1'] = true;
      this.openHandler('sub1');
    } else {
      this.openMap['sub1'] = false;
    }
  }
  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
  }
  isActive(route: string): boolean {
    return this.router.url.includes(route);
  }
  handleFavorite() {
    this.isFavorite = !this.isFavorite;
  }
}
