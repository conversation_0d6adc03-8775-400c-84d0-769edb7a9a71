import { Component, OnInit } from '@angular/core';
import { AuthService, User, UserProfile } from '../core/services/auth.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css'],
  standalone: false,
})
export class HomeComponent implements OnInit {
  userApi: User | null = null;
  user: UserProfile | null = null;

  constructor(private auth: AuthService) { }

  ngOnInit() {
    this.auth.getProfile().subscribe({
      next: profile => this.user = profile,
      error: () => this.user = null
    });
  }
}
