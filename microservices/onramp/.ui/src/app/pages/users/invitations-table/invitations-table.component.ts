import { Component, Input } from '@angular/core';
import { Invitations } from '../../../core/models/users.model';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-invitations-table',
  standalone: false,
  templateUrl: './invitations-table.component.html',
  styleUrl: './invitations-table.component.css'
})
export class InvitationsTableComponent {
  @Input() orgId: any[] = [];
  isTableLoading = false;
  highlightedRowId: string | null = null;
  isLoadingResend: boolean[] = [];
  confirmModal?: NzModalRef;
  // simulator Invitations table
  listDataInvitations: Invitations[] = [
    {
      id: '001',
      email: '<EMAIL>',
      created: '2025-07-30T02:30:56.398892Z',
      timesNotified: '1',
      lastNotification: '2025-07-30T02:30:56.398892Z',
      accepted: '2025-07-30T02:30:56.398892Z'
    },
    {
      id: '002',
      email: '<EMAIL>',
      created: '2025-07-30T02:30:56.398892Z',
      timesNotified: '2',
      lastNotification: '2025-07-30T02:31:56.398892Z',
      accepted: '2025-07-30T02:30:56.398892Z'
    },
    {
      id: '003',
      email: '<EMAIL>',
      created: '2025-07-30T02:30:56.398892Z',
      timesNotified: '1',
      lastNotification: '2025-07-30T02:32:56.398892Z',
      accepted: '2025-07-30T02:30:56.398892Z'
    }
  ];
  constructor(
    private modalService: NzModalService,
    private message: NzMessageService,
  ) { }
  handleResend(value: any, index: any) {
    this.isLoadingResend[index] = true;
    setTimeout(() => {
      this.message.create('success', 'Message has been sent successfully!');
      this.isLoadingResend[index] = false;
    }, 3000);
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the <b>${value.email}</b>?`,
      nzClassName: 'delete-modal-invitations',
      nzWidth: '500px',
      nzOnOk: () => {
        console.log('click ok delete');

      }
    });
  }
}
