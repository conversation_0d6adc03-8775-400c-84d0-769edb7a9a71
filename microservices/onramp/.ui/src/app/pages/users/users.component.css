.users-container {
  margin: 0 20px;
}

.users-container .title-users h1 {
  margin-bottom: unset;
}

.users-container .form-breadcrumb ::ng-deep .ant-breadcrumb {
  line-height: 26px;
  margin-bottom: 16px;
  font-size: 16px;
}

.users-container .gr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.users-container .page-description-users {
  display: flex;
  height: 42px;
  border-top: 1px solid gray;
  border-bottom: 1px solid gray;
  align-items: center;
  padding: 10px 0;
}

.users-container .form-table-user {
  margin: 50px 0;
}

.users-container .highlight-row {
  background-color: #e6f7ff8a;
  transition: background-color 0.5s ease;
}

.users-container .default-row {
  transition: background-color 0.5s ease;
}