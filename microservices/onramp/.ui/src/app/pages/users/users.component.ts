import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { Users } from '../../core/models/users.model';

@Component({
  selector: 'app-users',
  standalone: false,
  templateUrl: './users.component.html',
  styleUrl: './users.component.css'
})
export class UsersComponent {
  isTableLoading = false;
  highlightedRowId: string | null = null;
  isModalVisible = false;
  isEditMode = false;
  // simulator data Users
  listDataUsers: Users[] = [
    {
      id: '001',
      firstName: 'Foo',
      lastName: 'Bar',
      username: 'Foo Foo',
      email: '<EMAIL>',
      authMethod: 'OIDC',
      orgRole: 'Administrator',
      lastLogin: '2025-07-30T02:30:56.398892Z',
    },
    {
      id: '002',
      firstName: 'Bar',
      lastName: 'Qux',
      username: 'baz-qux',
      email: '<EMAIL>',
      authMethod: 'username',
      orgRole: 'Manager',
      lastLogin: '2025-07-30T02:31:56.398892Z',
    },
    {
      id: '003',
      firstName: 'Person',
      lastName: 'Person',
      username: 'Person',
      email: '<EMAIL>',
      authMethod: 'OIDC',
      orgRole: 'Anonymous',
      lastLogin: '2025-07-30T02:32:56.398892Z',
    }
  ];
  orgRoleOptions: any[] = [];
  orgIdUsers: any;
  confirmModal?: NzModalRef;
  currentUser: Users | null = null;
  constructor(
    private route: ActivatedRoute,
    private modalService: NzModalService,
    private message: NzMessageService,
  ) { }
  ngOnInit() {
    const orgId = this.route.snapshot.paramMap.get('orgId');
    if (orgId) {
      this.getUsersList(orgId);
      this.getInvitationsList(orgId);
      this.orgIdUsers = orgId;
    }
  }
  onCreateUser() {
    this.isModalVisible = true;
  }
  handleModalSave(value: any) {
    if (this.isEditMode && this.currentUser) { }
  }
  handleUpdateUser(value: any) {
    console.log('update user:', value);
  }
  getUsersList(orgId: any) { }
  getInvitationsList(orgId: any) { }
  handleEdit(value: any) {
    this.isEditMode = true;
    this.isModalVisible = true;
    this.currentUser = value;
  }
  onCloseModal() {
    this.isModalVisible = false;
    this.isEditMode = false;
    this.currentUser = null;
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the <b>${value.username}</b>?`,
      nzClassName: 'custom-confirm-modal',
      nzWidth: '500px',
      nzOnOk: () => {
        console.log('click ok delete');

      }
    });
  }
}
