<div class="users-container">
  <div class="gr-header">
    <div class="form-left">
      <div class="title-users">
        <h1 id="title-users">Users</h1>
      </div>
      <div class="form-breadcrumb">
        <nz-breadcrumb>
          <nz-breadcrumb-item>
            Synapse
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            Admin
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            Users
          </nz-breadcrumb-item>
        </nz-breadcrumb>
      </div>
    </div>
    <div class="form-right">
      <button id="btn-create-users-modal" nz-button nzType="primary" class="add-btn br-8 h-4" (click)="onCreateUser()">
        Invite User
      </button>
    </div>
  </div>
  <div class="page-description-users">
    <span>This page allows you to manage the users within the <b>Synapse</b> organization</span>
  </div>
  <div class="form-table-user">
    <div class="title-user-table">
      <h1 id="title-main">Users In Organization</h1>
      <span>These Users are currently in the <b>Synapse</b> Organization and have been assigned the Role that is shown
        below.</span>
    </div>
    <nz-table id="users-list" class="mt-10" #basicTable nzBordered [nzData]="listDataUsers" nzShowSizeChanger
      nzShowPagination [nzLoading]="isTableLoading">
      <thead>
        <tr>
          <th nzWidth="10%">First Name</th>
          <th nzWidth="10%">Last Name</th>
          <th nzWidth="10%">Username</th>
          <th nzWidth="15%">Email</th>
          <th nzWidth="10%">Auth Method</th>
          <th nzWidth="10%">Organization Role</th>
          <th nzWidth="15%">Last Login</th>
          <th nzWidth="8%">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index"
          [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
          <td>{{ data.firstName }}</td>
          <td>{{ data.lastName }}</td>
          <td>{{ data.username }}</td>
          <td>{{ data.email }}</td>
          <td>{{ data.authMethod }}</td>
          <td>{{ data.orgRole }}</td>
          <td>{{ data.lastLogin | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
          <td>
            <div class="btn-action">
              <button nz-button nzType="primary" (click)="handleEdit(data)" nz-tooltip nzTooltipTitle="Edit"
                class="mr-10 br-8">
                <nz-icon nzType="edit" nzTheme="outline" />
              </button>
              <button nz-button nzType="primary" (click)="handleDelete(data)" nzDanger nz-tooltip
                nzTooltipTitle="Delete" class="br-8">
                <nz-icon nzType="delete" nzTheme="outline" />
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <app-invitations-table [orgId]="orgIdUsers" (confirm)="handleModalSave($event)">
    </app-invitations-table>
  </div>
  <app-add-edit-user [isVisible]="isModalVisible" [isEditMode]="isEditMode" [data]="currentUser"
    (close)="onCloseModal()" [listOrgRole]="orgRoleOptions"></app-add-edit-user>
</div>