::ng-deep .ant-modal .ant-modal-content {
  border-radius: 8px;
}

::ng-deep .ant-modal .ant-modal-header {
  border-bottom: unset;
  border-radius: 8px 8px 0 0;
  padding: 24px;
}

.modal-create-invite .modal-add-invite ::ng-deep .ant-form-item .ant-form-item-control-input-content {
  display: grid;
  gap: 8px;
}

.modal-add-invite label {
  font-size: 16px;
  color: #1E1E1E;
}

.modal-create-invite .modal-add-invite .select-template ::ng-deep .ant-select-selector {
  height: 40px;
  border-radius: 8px;
}

.modal-create-invite .modal-add-invite .select-template ::ng-deep .ant-select-selector .ant-select-selection-item,
.modal-create-invite .modal-add-invite .select-template ::ng-deep .ant-select-selector .ant-select-selection-search-input {
  margin: auto;
}

.modal-add-invite .select-template ::ng-deep .ant-select-selector {
  height: 40px;
  border-radius: 8px;
}

.btn-submit-invite {
  width: 100%;
}

::ng-deep .ant-modal .ant-modal-footer {
  border-top: unset;
  border-radius: 0 0 8px 8px;
  padding: 0px 24px 24px 24px;
}

::ng-deep .ant-modal .ant-modal-header .content-header h2 {
  font-weight: bold;
  line-height: 29px;
}

::ng-deep .ant-modal .ant-modal-header .content-header span {
  margin-top: 4px;
  font-size: 16px;
  color: #757575;
}