import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.css'
})
export class AddEditUserComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() isDetail = false;
  @Input() orgId: any;
  @Input() listOrgRole: any;
  @Input() data: any;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  form: FormGroup;
  constructor(
    private fb: FormBuilder,
  ) {
    this.form = this.fb.group({
      email: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      orgRole: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      message: ['', [
        Validators.maxLength(2000)
      ]]
    });
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isEditMode'] || changes['data']) {
      if (this.data && this.isEditMode) {
        this.form.reset({
          email: this.data?.email,
          orgRole: this.data?.orgRole,
          message: this.data?.username,
        })
      }
    }
  }
  handleSave() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const createRole = {
        email: formValue.email.trim(),
        orgRole: formValue.orgRole.trim(),
        message: formValue.message
      }
      this.confirm.emit(createRole);
      this.form.reset({
        email: '',
        orgRole: '',
        message: ''
      }, { emitEvent: false });
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  handleCancel() {
    console.log('click close modal');
    this.form.reset({
      email: '',
      orgRole: '',
      message: ''
    });
    this.close.emit();
  }
}
