<div class="authentication-container">
  <div class="gr-header">
    <div class="form-left">
      <div class="title-authentication">
        <h1 id="title-authentication">Authentication Methods</h1>
      </div>
      <div class="form-breadcrumb">
        <nz-breadcrumb>
          <nz-breadcrumb-item>
            My Account
          </nz-breadcrumb-item>
          <nz-breadcrumb-item>
            Authentication Methods
          </nz-breadcrumb-item>
        </nz-breadcrumb>
      </div>
    </div>
  </div>
  <div class="page-description-authentication">
    <span><b>Authentication Methods</b> are the different ways that you can log in to your account. <b>Organizations</b>
      add you based on your E-mail address that is linked to one of these <b>Authentication Methods</b>. If your login
      is a username and password on the <b>Onramp</b> website, then you can change your password here. Otherwise, your
      password and login are managed by your employer.</span>
    <span><b>Warning:</b> If you delete an authentication method, then you will <b>also</b> be removed from any
      Organization that is tied to that Authentication Method!</span>
  </div>
  <div class="form-table-authentication">
    <nz-table id="authentication-list" class="mt-10" #basicTable nzBordered [nzData]="listDataAuthentication"
      nzShowSizeChanger nzShowPagination [nzLoading]="isTableLoading">
      <thead>
        <tr>
          <th nzWidth="10%">First Name</th>
          <th nzWidth="10%">Last Name</th>
          <th nzWidth="10%">Username</th>
          <th nzWidth="15%">Email</th>
          <th nzWidth="10%">Organization</th>
          <th nzWidth="8%">Action</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data; let i = index"
          [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
          <td>{{ data.firstName }}</td>
          <td>{{ data.lastName }}</td>
          <td>{{ data.username }}</td>
          <td>{{ data.email }}</td>
          <td>
            <ng-container *ngIf="isObject(data.organization) && !isArray(data.organization)">
              <div class="item-obj">
                <span class="dot mr-5"></span> {{ getOrgName(data.organization) }}
              </div>
            </ng-container>
            <ng-container *ngIf="isArray(data.organization)">
              <div class="item-arr" *ngFor="let org of data.organization">
                <span class="dot mr-5"></span> {{ getOrgName(org) }}
              </div>
            </ng-container>
          </td>
          <td>
            <div class="btn-action">
              <button nz-button nzType="primary" (click)="handleDelete(data)" nzDanger nz-tooltip
                nzTooltipTitle="Delete" class="br-8 mr-5">
                <nz-icon nzType="delete" nzTheme="outline" />
              </button>
              <nz-divider nzType="vertical"></nz-divider>
              <a nz-button nzType="link" (click)="handleChangePassword(data)">
                Change Password
              </a>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
  <app-change-password [isVisible]="isModalVisible" (close)="onCloseModal()"
    (confirm)="handleModalSave($event)"></app-change-password>
</div>