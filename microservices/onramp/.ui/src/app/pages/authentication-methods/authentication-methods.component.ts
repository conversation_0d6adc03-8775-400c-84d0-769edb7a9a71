import { Component } from '@angular/core';
import { Authentication } from '../../core/models/account-authen.model';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-authentication-methods',
  standalone: false,
  templateUrl: './authentication-methods.component.html',
  styleUrl: './authentication-methods.component.css'
})
export class AuthenticationMethodsComponent {
  isTableLoading = false;
  highlightedRowId: string | null = null;
  isModalVisible = false;
  confirmModal?: NzModalRef;
  listDataAuthentication: Authentication[] = [
    {
      id: '0001',
      firstName: 'Foo',
      lastName: 'Bar',
      username: 'FooBar',
      email: '<EMAIL>',
      organization: {
        orgId: 'org1',
        name: 'Org 1',
      },
    },
    {
      id: '0002',
      firstName: 'Bar',
      lastName: 'Qux',
      username: 'BarQux',
      email: '<EMAIL>',
      organization: [
        {
          orgId: 'org2',
          name: 'Org 2',
        }, {
          orgId: 'org3',
          name: 'Org 3',
        }
      ],
    }
  ]
  constructor(
    private modalService: NzModalService,
    private message: NzMessageService,
  ) { }
  getOrgName(org: any): string {
    return org?.name ?? '';
  }
  isArray(value: any): value is any[] {
    return Array.isArray(value);
  }

  isObject(value: any): value is object {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }
  handleChangePassword(value: any) {
    console.log('data account', value);
    this.isModalVisible = true;
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the account <b>${value.username}</b>?`,
      nzClassName: 'custom-confirm-modal modal-delete-account',
      nzWidth: '500px',
      nzOkText: 'Confirm',
      nzOkDanger: true,
      nzOnOk: () => {
        console.log('click Confirm delete');
      }
    });
  }
  handleModalSave(value: any) {
    console.log('value save', value);
  }

  onCloseModal() {
    this.isModalVisible = false;
  }
}
