::ng-deep .ant-modal .ant-modal-content {
  border-radius: 8px;
}

::ng-deep .ant-modal .ant-modal-header {
  border-bottom: unset;
  border-radius: 8px 8px 0 0;
  padding: 30px 24px 24px 24px;
}

.modal-create-change .modal-add-change ::ng-deep .ant-form-item .ant-form-item-control-input-content {
  display: grid;
  gap: 8px;
}

.modal-add-change ::ng-deep .ant-form-item-label>label {
  font-size: 16px;
  color: #1E1E1E;
  font-weight: 700;
}

.modal-add-change ::ng-deep .ant-row {
  display: contents !important;
}

.btn-submit-change {
  width: 100%;
}

::ng-deep .ant-modal .ant-modal-footer {
  border-top: unset;
  border-radius: 0 0 8px 8px;
  padding: 0px 24px 30px 24px;
}

::ng-deep .ant-modal .ant-modal-header .content-header h2 {
  font-weight: bold;
  line-height: 29px;
}

::ng-deep .ant-modal .ant-modal-header .content-header span {
  margin-top: 4px;
  font-size: 16px;
  color: #757575;
}