import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';

@Component({
  selector: 'app-change-password',
  standalone: false,
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.css']
})
export class ChangePasswordComponent {
  @Input() isVisible = false;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  form: FormGroup;
  currentPasswordVisible = false;
  newPasswordVisible = false;
  confirmPasswordVisible = false;

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      currentPassword: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      newPassword: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(255),
        this.passwordStrengthValidator()
      ]],
      confirmPassword: ['', [
        Validators.required,
        Validators.maxLength(255),
        this.passwordMatchValidator()
      ]]
    }, { updateOn: 'change' });
  }

  // Custom validator for password strength
  passwordStrengthValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value || '';
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumber = /[0-9]/.test(value);
      const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);
      const valid = hasUpperCase && hasLowerCase && hasNumber && hasSpecialChar;
      return valid ? null : { passwordStrength: true };
    };
  }

  // Custom validator for password matching on confirmPassword
  passwordMatchValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const confirmPassword = control.value;
      const newPassword = this.form?.get('newPassword')?.value;
      return newPassword && confirmPassword && newPassword === confirmPassword
        ? null
        : { passwordMismatch: true };
    };
  }

  // Explicit method to check if passwords match
  checkPasswordsMatch(): boolean {
    const newPassword = this.form.get('newPassword')?.value;
    const confirmPassword = this.form.get('confirmPassword')?.value;
    return newPassword && confirmPassword && newPassword === confirmPassword;
  }

  togglePasswordVisibility(field: 'currentPassword' | 'newPassword' | 'confirmPassword'): void {
    if (field === 'currentPassword') {
      this.currentPasswordVisible = !this.currentPasswordVisible;
    } else if (field === 'newPassword') {
      this.newPasswordVisible = !this.newPasswordVisible;
    } else if (field === 'confirmPassword') {
      this.confirmPasswordVisible = !this.confirmPasswordVisible;
    }
  }

  isClearValue(field: 'currentPassword' | 'newPassword' | 'confirmPassword'): boolean {
    return !!this.form.get(field)?.value;
  }

  handleClear(field: 'currentPassword' | 'newPassword' | 'confirmPassword'): void {
    this.form.get(field)?.setValue('');
    this.form.get('confirmPassword')?.updateValueAndValidity(); // Trigger confirmPassword validation
  }

  handleSavePassword() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const createRole = {
        currentPassword: formValue.currentPassword.trim(),
        newPassword: formValue.newPassword.trim(),
        confirmPassword: formValue.confirmPassword.trim()
      };
      this.confirm.emit(createRole);
      this.handleCancel();
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleCancel() {
    this.form.reset({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    this.currentPasswordVisible = false;
    this.newPasswordVisible = false;
    this.confirmPasswordVisible = false;
    this.close.emit();
  }

  // Helper to get error message for a specific control
  getErrorMessage(controlName: string): string {
    const control = this.form.get(controlName);
    if (control?.errors) {
      if (control.errors['required']) {
        return `Please input ${controlName === 'currentPassword' ? 'current' : controlName === 'newPassword' ? 'new' : 'confirm'} password.`;
      }
      if (control.errors['maxlength']) {
        return 'Password cannot exceed 255 characters.';
      }
      if (controlName === 'newPassword' && control.errors['minlength']) {
        return 'New password must be at least 8 characters long.';
      }
      if (controlName === 'newPassword' && control.errors['passwordStrength']) {
        return 'New password must contain uppercase, lowercase, number, and special character.';
      }
      if (controlName === 'confirmPassword' && control.errors['passwordMismatch']) {
        return 'Confirm password must match new password.';
      }
    }
    return '';
  }
}