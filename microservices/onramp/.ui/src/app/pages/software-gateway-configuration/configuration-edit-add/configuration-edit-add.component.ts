import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-configuration-edit-add',
  standalone: false,
  templateUrl: './configuration-edit-add.component.html',
  styleUrl: './configuration-edit-add.component.css'
})
export class ConfigurationEditAddComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() data: any | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  @Output() loadingChange = new EventEmitter<boolean>();
  jsonContent: string = '';
  parsedData: any;
  private isParsing = false;

  constructor() {
    this.initializeContent();
  }
  private initializeContent() {
    if (this.data && typeof this.data === 'object') {
      this.parseJson();
    }
  }

  ngOnChanges() {
    if (this.isEditMode && this.data) {
      this.jsonContent = JSON.stringify(this.data, null, 2);
      this.parseJson();
    } else {
      this.jsonContent = '';
      this.parsedData = null;
    }
  }
  onPaste(event: ClipboardEvent) {
    if (!this.isParsing) {
      this.isParsing = true;
      const clipboardData = event.clipboardData?.getData('text')?.trim();
      if (clipboardData) {
        this.jsonContent = clipboardData;
        this.parseJson();
      }
      setTimeout(() => this.isParsing = false, 0);
    }
    event.preventDefault();
  }
  private parseJson() {
    try {
      this.parsedData = JSON.parse(this.jsonContent);
    } catch (e) {
      console.error('Invalid JSON format:', e);
      this.parsedData = null;
    }
  }
  onConfirm() {
    if (!this.isParsing) {
      this.isParsing = true;
      this.parseJson();
      if (this.parsedData) {
        this.confirm.emit(this.parsedData);
        this.close.emit();
        this.parsedData = null;
        this.jsonContent = '';
      }
      setTimeout(() => {
        this.isParsing = false;
      }, 0);
    }
  }

  onCancel() {
    this.close.emit();
  }
  onContentChange() {
    this.parseJson();
  }
}
