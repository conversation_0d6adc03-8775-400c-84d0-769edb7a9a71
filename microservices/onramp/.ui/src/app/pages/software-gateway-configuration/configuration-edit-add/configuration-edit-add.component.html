<nz-modal [(nzVisible)]="isVisible" [nzTitle]="'Paste JSON File'" (nzOnCancel)="onCancel()" (nzOnOk)="onConfirm()"
  [nzWidth]="650" [nzMaskClosable]="false">
  <ng-container *nzModalContent>
    <div>
      <label for="jsonInput">Paste JSON content here:</label>
      <textarea id="jsonInput" [(ngModel)]="jsonContent" (paste)="onPaste($event)" rows="15" cols="60"
        placeholder="Paste your JSON data here..." style="width: 100%;"></textarea>
    </div>
    <nz-alert *ngIf="parsedData === null && jsonContent" nzType="error" nzMessage="Invalid JSON format"></nz-alert>
  </ng-container>
  <ng-container *nzModalFooter>
    <button nz-button nzType="default" (click)="onCancel()">Cancel</button>
    <button nz-button nzType="primary" [nzLoading]="false" (click)="onConfirm()">Save</button>
  </ng-container>
</nz-modal>