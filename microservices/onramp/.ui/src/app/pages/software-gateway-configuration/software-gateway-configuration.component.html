<div class="json-viewer-container">
  <div>
    <nz-breadcrumb>
      <nz-breadcrumb-item>
        <a routerLink="/">Home</a></nz-breadcrumb-item>
      <nz-breadcrumb-item>
        <a routerLink="/software-gateway">Software Gateway</a>
      </nz-breadcrumb-item>
      <nz-breadcrumb-item>Software Gateway Configuration</nz-breadcrumb-item>
    </nz-breadcrumb>
    <h2>{{ nameGateway }}</h2>
  </div>
  <div class="title-oem">
    <h1>Software Gateway Configuration</h1>
  </div>
  <div class="form-header">
    <div class="form-btn">
      <button nz-button nzType="primary" class="edit-btn" (click)="editModal()">
        Edit
      </button>
    </div>
  </div>
  <nz-card [nzBordered]="false">
    <div class="table-content">
      <nz-table #jsonTable nzBordered [nzData]="tableData" [nzShowPagination]="false" [nzLoading]="isTableLoading"
        [nzPageSize]="pageSize">
        <thead>
          <tr>
            <th colspan="2">Config</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of jsonTable.data; let i = index">
            <td rowspan="1">{{ data.key }}</td>
            <td rowspan="1">{{ data.value }}</td>
          </tr>
        </tbody>
      </nz-table>
    </div>
  </nz-card>
  <app-configuration-edit-add [isVisible]="isVisible" [isEditMode]="isEditMode" [data]="currentJson"
    (close)="handleModalClose()" (confirm)="handleModalSave($event)"
    (loadingChange)="handleLoadingChange($event)"></app-configuration-edit-add>
</div>