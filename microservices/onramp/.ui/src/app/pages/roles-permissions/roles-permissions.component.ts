import { Component } from '@angular/core';
import { RolesPermissions } from '../../core/models/toles-permissions.model';
import { ActivatedRoute, Router } from '@angular/router';
import { RolesPermissionsService } from '../../core/services/roles_permissions.service';
import { Observable } from 'rxjs';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-roles-permissions',
  standalone: false,
  templateUrl: './roles-permissions.component.html',
  styleUrl: './roles-permissions.component.css'
})
export class RolesPermissionsComponent {
  isVisible = false;
  isTableLoading = false;
  listOfData: any[] = [];
  highlightedRowId: string | null = null;
  isModalVisible = false;
  isEditMode = false;
  isDetailOrg = false;
  currentOEM: RolesPermissions | null = null;
  dataPermissions: any[] = [];
  orgIdRole: any;
  templateOptions: any[] = [];
  confirmModal?: NzModalRef;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private rolesPermissionsService: RolesPermissionsService,
    private modalService: NzModalService,
    private message: NzMessageService,
  ) { }

  ngOnInit() {
    const orgId = this.route.snapshot.paramMap.get('orgId');
    if (orgId) {
      this.getRolesList(orgId);
      this.handleGetListTemplate(orgId);
      this.getPermissionsList(orgId);
      this.orgIdRole = orgId;
    }
  }

  openLoginPrompt(): Observable<boolean> {
    return new Observable<boolean>(observer => {
      const modal = this.modalService.warning({
        nzTitle: '<h2>Organization required</h2>',
        nzContent: 'Please select an organization to view or manage Roles And Permissions. Select Organization on the All Organizations tab.',
        nzClassName: 'custom-login-modal-permissions',
        nzOnOk: () => {
          this.router.navigate(['/organizations']);
          modal.destroy();
          observer.next(false);
          observer.complete();
        },
        nzWidth: '450px',
        nzClosable: false,
        nzMaskClosable: false
      });
    });
  }
  getRolesList(orgId: any) {
    this.isTableLoading = true;
    this.rolesPermissionsService.getRoles(orgId).subscribe((res: any) => {
      this.listOfData = res.data;
      this.isTableLoading = false;
    },
      (error) => {
        console.error('Error fetching Roles list:', error);
        this.isTableLoading = false;
        this.listOfData = [];
      });
  }
  getPermissionsList(orgId: any) {
    this.isTableLoading = true;
    this.rolesPermissionsService.getPermissions(orgId).subscribe((res: any) => {
      this.dataPermissions = res.data.permissions;
      this.isTableLoading = false;
    },
      (error) => {
        console.error('Error fetching Permissions list:', error);
        this.isTableLoading = false;
        this.dataPermissions = [];
      });
  }

  handleGetListTemplate(orgId: any) {
    this.rolesPermissionsService.getRolesTemplatesCustom(orgId).subscribe((res: any) => {
      if (res && res.data) {
        this.templateOptions = res.data.map((item: any) => ({
          label: item.name,
          value: item.identifier,
        }));
      } else {
        this.templateOptions = [];
      }
    }, (error) => {
      console.error('Error fetching role templates:', error);
    });
  }
  getLabelFromValue(templateRoleIdentifier: string): string | undefined {
    const role = this.templateOptions.find(option => option.value === templateRoleIdentifier);
    return role ? role.label : undefined;
  }
  openAddPopup() {
    this.isModalVisible = true;
    this.isEditMode = false;
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the <b>${value.name}</b>?`,
      nzClassName: 'custom-confirm-modal',
      nzWidth: '500px',
      nzOnOk: () =>
        new Promise<void>((resolve, reject) => {
          this.isTableLoading = true;
          this.rolesPermissionsService.deleteRoles(this.orgIdRole, value.id).subscribe({
            next: () => {
              this.listOfData = this.listOfData.filter(item => item.id !== value.id);
              this.message.success('Record deleted successfully!');
              this.getRolesList(this.orgIdRole);
              this.isTableLoading = false;
              resolve();
            },
            error: () => {
              this.isTableLoading = false;
              reject();
            }
          })
        }).catch(() => {
          this.message.error('Failed to delete record. Please try again.');
          this.isTableLoading = false;
        })
    });
  }

  handleModalSave(data: any) {
    this.isTableLoading = true;
    this.rolesPermissionsService.createRole(this.orgIdRole, data).subscribe((res: any) => {
      this.message.create('success', 'Role created successfully!');
      this.getRolesList(this.orgIdRole);
      this.isTableLoading = false;
      this.isModalVisible = false;
      this.isEditMode = false;
    }, (error) => {
      console.error('Error creating role:', error);
      this.message.create('error', 'Failed to create role. Please try again.');
      this.isTableLoading = false;
    });
  }
  closeModal() {
    this.isModalVisible = false;
    this.isEditMode = false;
    this.isDetailOrg = false;
    this.currentOEM = null;
  }
}
