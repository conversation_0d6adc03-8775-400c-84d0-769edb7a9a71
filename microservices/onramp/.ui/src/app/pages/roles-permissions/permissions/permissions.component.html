<div class="permission-container">
  <div class="form-header">
    <div class="title">
      <h1 id="title-permissions">Permissions</h1>
    </div>
    <div class="content-header">
      <span>Permissions allow users to perform certain actions and to access parts of the Organization. Each role
        inherits the permissions of the template from which it was created, but each permission can be overridden
        individually.</span>
    </div>
  </div>

  <div class="container-table">
    <ng-container *ngFor="let permission of sortedPermissions">
      <div class="form-header">
        <div class="title">
          <h1>{{ permission.name }}</h1>
        </div>
        <div class="content">
          <span>{{ permission.description }} (Scope: {{ permission.scope }})</span>
        </div>
      </div>
      <nz-table #permissionTable [nzData]="getPermissionNames(permission.roles)" nzBordered nzShowSizeChanger
        nzShowPagination [nzLoading]="isTableLoading">
        <thead>
          <tr>
            <th nzWidth="20%">Name</th>
            <th *ngFor="let role of permission.roles" nzWidth="15%">
              {{ role.name }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let permName of getPermissionNames(permission.roles)">
            <td>{{ permName }}</td>
            <td *ngFor="let role of permission.roles">
              <ng-container *ngFor="let perm of role.permissions">
                <nz-select *ngIf="perm.name === permName" [(ngModel)]="perm.value"
                  (ngModelChange)="onPermissionChange(perm, role, permission)">
                  <nz-option *ngIf="perm.inherited && perm.value === perm.default_value" [nzDisabled]="true"
                    [nzLabel]="perm.default_value ? 'Inherited (Yes)' : 'Inherited (No)'"
                    [nzValue]="perm.default_value"></nz-option>
                  <nz-option nzLabel="Yes" [nzValue]="true"></nz-option>
                  <nz-option nzLabel="No" [nzValue]="false"></nz-option>
                </nz-select>
              </ng-container>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </ng-container>
  </div>
</div>