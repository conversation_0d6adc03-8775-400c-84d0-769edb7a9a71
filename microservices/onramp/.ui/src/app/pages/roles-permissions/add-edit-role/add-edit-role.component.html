<nz-modal class="modal-create-role" [(nzVisible)]="isVisible" [nzTitle]="modalTitle" (nzOnCancel)="handleCancel()"
  [nzOkText]="'Save'" [nzFooter]="modalFooter" nzWidth="440px">
  <ng-template #modalTitle>
    <div class="content-header">
      <h2 style="margin-bottom: 0;">Create Role</h2>
      <span>This will create a new Role based on the specified
        template.</span>
    </div>
  </ng-template>
  <ng-container *nzModalContent>
    <nz-form [formGroup]="form" class="modal-add">
      <nz-form-item>
        <nz-form-control nzErrorTip="Please Input Role Name.">
          <label>Role Name</label>
          <nz-input-group nzErrorTip="Please Input Name.">
            <input class="h-4 br-8" nz-input id="new-organization-name-role" formControlName="name"
              placeholder="Enter Name" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control>
          <label>Role Template</label>
          <nz-form-control nzErrorTip="Please select a permission name!">
            <nz-select class="select-template" formControlName="template" nzPlaceHolder="Select a permission"
              [nzOptions]="listRolesTemp"></nz-select>
          </nz-form-control>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control>
          <label>Role Description</label>
          <nz-textarea-count [nzMaxCharacterCount]="2000">
            <textarea class="br-8" id="new-permission-role" formControlName="description" nz-input rows="4"
              placeholder="Enter Role Description"></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
    </nz-form>
  </ng-container>
  <ng-template #modalFooter>
    <button id="btn-create-role" class="btn-submit-role h-4 br-8" nz-button nzType="primary" (click)="handleSave()">
      Create Role
    </button>
  </ng-template>
</nz-modal>