import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RolesPermissions } from '../../../core/models/toles-permissions.model';
import { RolesPermissionsService } from '../../../core/services/roles_permissions.service';

@Component({
  selector: 'app-add-edit-role',
  standalone: false,
  templateUrl: './add-edit-role.component.html',
  styleUrl: './add-edit-role.component.css'
})
export class AddEditRoleComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() isDetail = false;
  @Input() orgId: any;
  @Input() listRolesTemp: any;
  @Input() data: RolesPermissions | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  form: FormGroup;
  templateOptions: any[] = [];
  constructor(
    private fb: FormBuilder,
    private rolesPermissions: RolesPermissionsService,
  ) {
    this.form = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      template: ['mun_admin', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      description: ['', [
        Validators.maxLength(2000)
      ]]
    });
  }
  handleSave() {
    if (this.form.valid) {
      const formValue = this.form.value;
      const createRole = {
        name: formValue.name.trim(),
        description: formValue.description.trim(),
        templateRoleIdentifier: formValue.template
      }
      this.confirm.emit(createRole);
      this.form.reset({
        name: '',
        description: '',
        template: 'mun_admin'
      }, { emitEvent: false });
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  handleCancel() {
    this.form.reset({
      name: '',
      description: '',
      template: 'mun_admin'
    }, { emitEvent: false });
    this.close.emit();
  }
}
