import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OrganizationsService } from '../../core/services/organization.service';
import { Organization } from '../../core/models/organizations.model';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-organizations',
  templateUrl: './organizations.component.html',
  standalone: false,
  styleUrl: './organizations.component.css'
})
export class OrganizationsComponent {
  isModalVisible = false;
  isEditMode = false;
  isDetailOrg = false;
  selectedData = null;
  listOfData: Organization[] = [];
  filteredList: Organization[] = [];
  searchTerm: string = '';
  currentOEM: Organization | null = null;
  editIndex: number | null = null;
  isTableLoading = false;
  organizationFilter: string | null = null;
  highlightedRowId: string | null = null;
  confirmModal?: NzModalRef;

  constructor(
    private organizationsService: OrganizationsService,
    private router: Router,
    private route: ActivatedRoute,
    private message: NzMessageService,
    private modal: NzModalService,
  ) { }

  ngOnInit() {
    this.getListOrganization();
    this.route.queryParams.subscribe(params => {
      if (params) {
        this.searchTerm = params['organizationId'];
        this.organizationFilter = params['organizationId'] || null;
      }
    });
  }

  getListOrganization() {
    this.isTableLoading = true;
    this.organizationsService.getOrganizations().subscribe((data: any) => {
      this.listOfData = data.data;
      this.filteredList = [...this.listOfData];
      this.isTableLoading = false;
      this.searchTable();
    },
      (error) => {
        console.error('Error fetching Organizations list:', error);
        this.isTableLoading = false;
        this.listOfData = [];
        this.filteredList = [];
      }
    );
  }
  searchTable() {
    this.filteredList = [...this.listOfData];
    if (this.searchTerm) {
      this.filteredList = this.listOfData.filter(item =>
        item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.id.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }
    if (this.organizationFilter) {
      this.filteredList = this.filteredList.filter(org =>
        org.id === this.organizationFilter,
      );
    }
  }

  openAddPopup() {
    this.isModalVisible = true;
    this.selectedData = null;
    this.isEditMode = false;
  }

  openEditPopup(index: number) {
    this.isModalVisible = true;
    this.isEditMode = true;
    this.editIndex = index;
    this.currentOEM = { ...this.listOfData[index] };
  }
  openDetailModal(value: any) {
    this.isModalVisible = true;
    this.selectedData = null;
    this.isDetailOrg = true;
    this.organizationsService.getOrganizationsId(value.id).subscribe((item: any) => {
      this.currentOEM = item.data;
    })
  }

  closeModal() {
    this.isModalVisible = false;
    this.selectedData = null;
    this.isEditMode = false;
    this.isDetailOrg = false;
    this.currentOEM = null;
  }

  handleModalSave(data: any) {
    if (this.isEditMode && this.editIndex !== null && this.currentOEM) {
      this.handleUpdateOrganization(data);
    } else {
      this.handleCreateOrganization(data);
    }
    this.filteredList = [...this.listOfData];
    this.isModalVisible = false;
    this.editIndex = null;
    this.currentOEM = null;
  }

  handleCreateOrganization(data: any) {
    this.isTableLoading = true;
    this.organizationsService.createOrganization(data).subscribe((item: any) => {
      this.message.create('success', 'Record created successfully!');
      this.getListOrganization();
      this.highlightedRowId = item.data.id;
      this.isTableLoading = false;
      this.listOfData = [...this.listOfData, item.data];
      setTimeout(() => {
        this.highlightedRowId = null;
      }, 3000);
    }, (error) => {
      console.error('Error creating Organization:', error);
      this.message.create('error', 'Failed to create record. Please try again.');
      this.highlightedRowId = null;
      this.isTableLoading = false;
    })
  }

  handleUpdateOrganization(data: any) {
    this.isTableLoading = true;
    const orgId = this.currentOEM?.id;
    if (!orgId) {
      this.message.create('error', 'Organization ID not found.');
      this.isTableLoading = false;
      return;
    }

    this.organizationsService.updateOrganization(orgId, data).subscribe(() => {
      this.message.create('success', 'Record updated successfully!');
      this.getListOrganization();
      this.isTableLoading = false;
    }, (error) => {
      console.error('Error updating Organization:', error);
      this.message.create('error', 'Failed to update record. Please try again.');
      this.isTableLoading = false;
    })
  }

  handleDeleteOrg(data: any) {
    this.confirmModal = this.modal.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the <b>${data.name}</b>?`,
      nzClassName: 'custom-confirm-modal',
      nzWidth: '500px',
      nzOnOk: () =>
        new Promise<void>((resolve, reject) => {
          this.isTableLoading = true;
          this.organizationsService.deleteOrganization(data.id).subscribe({
            next: () => {
              this.listOfData = this.listOfData.filter(item => item.id !== data.id);
              this.message.success('Record deleted successfully!');
              this.getListOrganization();
              this.isTableLoading = false;
              resolve();
            },
            error: (error) => {
              this.message.error('Failed to delete record. Please try again.');
              console.error('Delete failed:', error);
              this.isTableLoading = false;
              reject();
            }
          })
        }).catch(() => {
          this.message.error('Failed to delete record. Please try again.');
          this.isTableLoading = false;
        })
    });
  }

  onClickPermissions(value: any) {
    this.router.navigate([`/organization/${value}/permissions`]);
  }

  onClickUsers(value: any) {
    this.router.navigate([`/organization/${value}/users`]);
  }
}
