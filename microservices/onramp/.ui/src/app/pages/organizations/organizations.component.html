<div class="oem-container">
  <div class="title-oem">
    <h1 id="title-organizations">Organizations</h1>
  </div>
  <div class="form-header">
    <div class="form-btn">
      <button nz-button nzType="primary" class="add-btn br-8" (click)="openAddPopup()">
        Add New Row
      </button>
    </div>
    <div class="form-search">
      <nz-input-group nzSearch (nzOnSearch)="searchTable()" [nzAddOnAfter]="suffixIconButton">
        <input [(ngModel)]="searchTerm" (input)="searchTable()" (nzOnSearch)="searchTable()" type="text" nz-input
          placeholder="Filter Name" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSearch><nz-icon nzType="search" /></button>
      </ng-template>
    </div>
  </div>
  <nz-table #basicTable nzBordered id="organization-list" [nzData]="filteredList" nzShowSizeChanger nzShowPagination
    [nzLoading]="isTableLoading">
    <thead>
      <tr>
        <th nzWidth="28%">Name</th>
        <th nzWidth="28%">Description</th>
        <th nzWidth="28%">Type</th>
        <th nzWidth="16%">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data; let i = index"
        [ngClass]="data.id === highlightedRowId ? 'highlight-row' : 'default-row'">
        <td>
          <a (click)="openDetailModal(data)">{{ data.name }}</a>
        </td>
        <td>{{data.description}}</td>
        <td>{{data.orgtypeidentifier}}</td>
        <td>
          <div class="btn-action">
            <button id="btn-permission" class="br-8" nz-button nzType="default" (click)="onClickPermissions(data.id)">
              Permissions
            </button>
            <button class="br-8" nz-button nzType="default" (click)="onClickUsers(data.id)">Users</button>
            <button class="edit-btn br-8" nz-button nzType="primary" (click)="openEditPopup(i)" nz-tooltip
              nzTooltipTitle="Edit">
              <nz-icon nzType="edit" nzTheme="outline" />
            </button>
            <button class="br-8" nz-button nzType="primary" (click)="handleDeleteOrg(data)" nzDanger nz-tooltip
              nzTooltipTitle="Delete">
              <nz-icon nzType="delete" nzTheme="outline" />
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <app-edit-add [isVisible]="isModalVisible" [isEditMode]="isEditMode" [isDetail]="isDetailOrg" [data]="currentOEM"
    (close)="closeModal()" (confirm)="handleModalSave($event)">
  </app-edit-add>
</div>