import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Organization } from '../../../core/models/organizations.model';

@Component({
  selector: 'app-edit-add',
  standalone: false,
  templateUrl: './edit-add.component.html',
  styleUrl: './edit-add.component.css'
})
export class EditAddComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() isDetail = false;
  @Input() data: Organization | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<any>();
  form: FormGroup;

  constructor(
    private fb: FormBuilder,
  ) {
    this.form = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      description: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]],
      orgtypeidentifier: ['municipality', [
        Validators.required
      ]],
      id: [''],
      createdat: [''],
      updatedat: ['']
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isEditMode'] || changes['isDetail'] || changes['data']) {
      if (this.data && (this.isEditMode || this.isDetail)) {
        this.form.reset({
          id: this.data?.id,
          name: this.data?.name || '',
          description: this.data?.description || '',
          orgtypeidentifier: this.data?.orgtypeidentifier,
          createdat: this.data?.createdat,
          updatedat: this.data?.updatedat,
        }, { emitEvent: false });

        this.form.get('name')?.enable();
        this.form.get('description')?.enable();
        this.form.get('orgtypeidentifier')?.enable();

        if (this.isEditMode) {
          this.form.get('orgtypeidentifier')?.disable();
        }

        this.form.updateValueAndValidity();
      } else {
        this.form.reset({
          id: '',
          name: '',
          description: '',
          orgtypeidentifier: 'municipality'
        });
        this.form.get('name')?.enable();
        this.form.get('description')?.enable();
        this.form.get('orgtypeidentifier')?.enable();
      }

      if (this.data && this.isDetail) {
        this.form.get('name')?.disable();
        this.form.get('description')?.disable();
        this.form.get('orgtypeidentifier')?.disable();
        this.form.get('createdat')?.disable();
        this.form.get('updatedat')?.disable();
      }
    }
  }

  handleSave(): void {
    if (this.form.valid) {
      const formValue = this.form.value;
      const cleanName = formValue.name ? formValue.name.trim().replace(/\s+/g, ' ') : '';
      const cleanDescription = formValue.description ? formValue.description.trim().replace(/\s+/g, ' ') : '';
      if (!cleanName || !cleanDescription) {
        if (!cleanName) {
          this.form.get('name')?.setErrors({ 'required': true });
          this.form.get('name')?.markAsDirty();
        }
        if (!cleanDescription) {
          this.form.get('description')?.setErrors({ 'required': true });
          this.form.get('description')?.markAsDirty();
        }
        return;
      }
      if (this.isEditMode) {
        const updatePayload = {
          name: cleanName,
          description: cleanDescription
        };
        this.confirm.emit(updatePayload);
      } else {
        const createPayload = {
          name: cleanName,
          description: cleanDescription,
          orgtypeidentifier: formValue.orgtypeidentifier
        };
        this.confirm.emit(createPayload);
      }
      this.form.reset({
        id: '',
        name: '',
        description: '',
        orgtypeidentifier: 'municipality'
      });
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleCancel(): void {
    this.form.reset({
      id: '',
      name: '',
      description: '',
      orgtypeidentifier: 'municipality'
    });
    this.close.emit();
  }
}
