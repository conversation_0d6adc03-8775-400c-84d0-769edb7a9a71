<div class="software-container">
  <div class="title-oem">
    <h1>Software Gateway</h1>
  </div>
  <div class="form-header">
    <div class="form-btn">
      <button nz-button nzType="primary" class="add-btn" (click)="showModal()">
        Add New Row
      </button>
    </div>
    <div class="form-search">
      <nz-input-group nzSearch (nzOnSearch)="filterGateways()" [nzAddOnAfter]="suffixIconButton">
        <input [(ngModel)]="searchTerm" (input)="filterGateways()" (nzOnSearch)="filterGateways()" type="text" nz-input
          placeholder="Filter Name" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSearch><nz-icon nzType="search" /></button>
      </ng-template>
    </div>
  </div>

  <nz-table #basicTable nzBordered nzShowSizeChanger [nzData]="filteredList" [nzLoading]="isTableLoading"
    style="margin-top: 16px;">
    <thead nzSingleSort>
      <tr>
        <th nzShowSort nzSortKey="name" (click)="onColumnClick('name')"
          [nzSortOrder]="currentSortKey === 'name' ? sortOrder : null">
          Name
        </th>
        <th nzShowSort nzSortKey="orgId" (click)="onColumnClick('orgId')"
          [nzSortOrder]="currentSortKey === 'orgId' ? sortOrder : null">
          Organization ID
        </th>
        <th nzShowSort nzSortKey="gatewayId" (click)="onColumnClick('gatewayId')"
          [nzSortOrder]="currentSortKey === 'gatewayId' ? sortOrder : null">
          Identifier
        </th>
        <th nzShowSort nzSortKey="apiKey" (click)="onColumnClick('apiKey')"
          [nzSortOrder]="currentSortKey === 'apiKey' ? sortOrder : null">
          API Key
        </th>
        <th nzShowSort nzSortKey="dateLastCheckedInUTC" (click)="onColumnClick('dateLastCheckedInUTC')"
          [nzSortOrder]="currentSortKey === 'dateLastCheckedInUTC' ? sortOrder : null">
          Last Check
        </th>
        <th nzShowSort nzSortKey="isEnabled" (click)="onColumnClick('isEnabled')"
          [nzSortOrder]="currentSortKey === 'isEnabled' ? sortOrder : null">
          Status
        </th>
        <th nzWidth="20%">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td>
          <span nz-tooltip [nzTooltipTitle]="data?.name">
            {{ data.name ? (data.name.length > 20 ? data.name.substring(0, 20) + '...' : data.name) : '' }}
          </span>
        </td>
        <td>
          <a (click)="onClickOrganizations(data.orgId)">{{ data.orgId }}</a>
        </td>
        <td>{{ data.gatewayId }}</td>
        <td>{{ data.apiKey }}</td>
        <td>{{ data.dateLastCheckedInUTC | date: 'MM-dd-yyyy HH:mm:ss' : 'UTC' }}</td>
        <td>
          <nz-tag [nzColor]="getTagColor(data.isEnabled)">{{ getTagText(data.isEnabled) }}</nz-tag>
        </td>
        <td class="btn-action">
          <button nz-button nzType="default" (click)="handleConfig(data)" class="btn-soft">Configuration</button>
          <button nz-button nzType="default" (click)="handleDevices(data)" class="btn-soft">Devices</button>
          <button nz-button nzType="primary" (click)="editGateway(i)">Edit</button>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <app-gateway-edit-add [isVisible]="isVisible" [isEditMode]="isEditMode" [data]="currentGateway"
    (close)="handleModalClose()" (confirm)="handleModalSave($event)"
    (loadingChange)="handleLoadingChange($event)"></app-gateway-edit-add>
</div>