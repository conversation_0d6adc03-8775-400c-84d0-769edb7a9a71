<nz-modal [(nzVisible)]="isVisible" [nzTitle]="isEditMode ? 'Edit Gateway' : 'Add Gateway'" [nzOkText]="'Save'"
  (nzOnCancel)="handleCancel()" (nzOnOk)="handleSave()" nzWidth="600px">
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="form">
      <nz-form-item>
        <nz-form-label [nzSpan]="9" nzRequired>Name</nz-form-label>
        <nz-form-control [nzSpan]="14" nzErrorTip="Please Input Name.">
          <input nz-input formControlName="name" placeholder="Enter Name" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="9" nzFor="organizationId" nzRequired>Organization ID</nz-form-label>
        <nz-form-control [nzSpan]="14" nzErrorTip="Please select your Organization!">
          <nz-select id="organizationId" formControlName="orgId" nzPlaceHolder="Select a Organization" nzShowSearch>
            <nz-option *ngFor="let item of listOrganizations" [nzValue]="item.orgId" [nzLabel]="item.name"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSpan]="9" nzRequired>Status</nz-form-label>
        <nz-form-control [nzSpan]="14" nzErrorTip="Please Input Status.">
          <nz-switch formControlName="isEnabled" nzCheckedChildren="Enable" nzUnCheckedChildren="Disable"></nz-switch>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-container>
</nz-modal>