<nz-modal [(nzVisible)]="isVisible" [nzTitle]="'Import File Device'" [nzOkText]="'Save'" (nzOnCancel)="handleCancel()"
  (nzOnOk)="hanldeSave()" [nzWidth]="parsedData.length > 0 ? '80%' : '50%'">
  <ng-container *nzModalContent>
    <nz-upload nzType="drag" [nzFileList]="fileList" [nzBeforeUpload]="beforeUpload" (nzRemove)="handleRemove()"
      (nzChange)="handleChange($event)" [nzAccept]="'.csv'" [nzDisabled]="isLoading">
      <p class="ant-upload-drag-icon">
        <i nz-icon nzType="inbox"></i>
      </p>
      <p class="ant-upload-text">Click or drag file to this area to upload</p>
      <p class="ant-upload-hint">
        Only accept .csv files. File will be parsed automatically.
      </p>
    </nz-upload>

    <nz-card *ngIf="parsedData.length > 0" title="Imported Data" [nzLoading]="isLoading">
      <nz-table [nzData]="parsedData">
        <thead>
          <tr>
            <th>Identifier</th>
            <th>Name</th>
            <th>Latitude</th>
            <th>Longitude</th>
            <th>IP Address</th>
            <th>Port</th>
            <th>Flush Connection MS</th>
            <th>Enable Realtime</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of parsedData">
            <td>{{ data.devicesId }}</td>
            <td>{{ data.name }}</td>
            <td>{{ formatDecimal(data.latitude) }}
              <span *ngIf="false">{{ convertToDMS(data.latitude) }}</span>
            </td>
            <td>{{ formatDecimal(data.longitude) }}
              <span *ngIf="false">{{ convertToDMS(data.longitude) }}</span>
            </td>
            <td>{{ data.iPAddress }}</td>
            <td>{{ data.port }}</td>
            <td>{{ data.flushConnectionMs }}</td>
            <nz-tag [nzColor]="data.enableRealtime ? 'green' : 'red'">
              {{ data.enableRealtime ? 'Enabled' : 'Disabled' }}
            </nz-tag>
            <td>
              <nz-tag [nzColor]="getTagColor(data.isEnabled)">{{ getTagText(data.isEnabled) }}</nz-tag>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </nz-card>
  </ng-container>
</nz-modal>