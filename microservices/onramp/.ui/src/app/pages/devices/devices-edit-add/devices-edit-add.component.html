<nz-modal [(nzVisible)]="isVisible" [nzTitle]="isEditMode ? 'Edit Device' : 'Add Device'" [nzOkText]="'Save'"
  (nzOnCancel)="handleCancel()" (nzOnOk)="handleSave()" nzWidth="870px">
  <ng-container *nzModalContent>
    <form nz-form [formGroup]="form">
      <div nz-row>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>Name</nz-form-label>
            <nz-form-control [nzSpan]="14" nzErrorTip="Please Input Name.">
              <input nz-input formControlName="name" placeholder="Enter Name" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>Flush Connection MS</nz-form-label>
            <nz-form-control [nzSpan]="14" nzErrorTip="Please Input Flush Connection MS.">
              <input nz-input formControlName="flushConnectionMs" placeholder="Enter Flush Connection MS" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
      <div nz-row>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>Latitude</nz-form-label>
            <nz-form-control [nzSpan]="14" nzErrorTip="Please Input Latitude.">
              <input nz-input formControlName="latitude" placeholder="Enter Latitude" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>Longitude</nz-form-label>
            <nz-form-control [nzSpan]="14" nzErrorTip="Please Input Longitude.">
              <input nz-input formControlName="longitude" placeholder="Enter Longitude" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
      <div nz-row>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>IP Address</nz-form-label>
            <nz-form-control [nzSpan]="14" nzErrorTip="Please enter a valid IP Address (e.g., ***********)!.">
              <input nz-input formControlName="iPAddress" placeholder="Enter IP Address (e.g., ***********)" />
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>Port</nz-form-label>
            <nz-form-control [nzSpan]="14" [nzErrorTip]="portErrorTpl">
              <input nz-input formControlName="port" placeholder="Enter Port (0-65535)" />
            </nz-form-control>
            <ng-template #portErrorTpl let-control>
              <ng-container *ngIf="control.hasError('required')">
                Please enter a port number!
              </ng-container>
              <ng-container *ngIf="control.hasError('pattern')">
                Port must be a number!
              </ng-container>
              <ng-container *ngIf="control.hasError('min') || control.hasError('max')">
                Port must be between 0 and 65535!
              </ng-container>
            </ng-template>
          </nz-form-item>
        </div>
      </div>
      <div nz-row>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>Status</nz-form-label>
            <nz-form-control [nzSpan]="14">
              <nz-switch formControlName="isEnabled" nzCheckedChildren="Enable"
                nzUnCheckedChildren="Disable"></nz-switch>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col nzSpan="12">
          <nz-form-item>
            <nz-form-label [nzSpan]="9" nzRequired>Enable Realtime</nz-form-label>
            <nz-form-control [nzSpan]="14">
              <!-- <input nz-input formControlName="enableRealtime" placeholder="Enter Enable Realtime" /> -->
              <nz-switch formControlName="enableRealtime" nzCheckedChildren="Enable"
                nzUnCheckedChildren="Disable"></nz-switch>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </form>
  </ng-container>
</nz-modal>