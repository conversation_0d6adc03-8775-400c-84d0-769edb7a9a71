<div class="devices-container">
  <div>
    <nz-breadcrumb>
      <nz-breadcrumb-item>
        <a routerLink="/">Home</a>
      </nz-breadcrumb-item>
      <nz-breadcrumb-item>
        <a routerLink="/software-gateway">Software Gateway</a>
      </nz-breadcrumb-item>
      <nz-breadcrumb-item>
        Devices
      </nz-breadcrumb-item>
    </nz-breadcrumb>
    <h2>{{ nameGateway }}</h2>
  </div>
  <div class="title-oem">
    <h1>Devices</h1>
  </div>
  <div class="form-header">
    <div class="form-btn">
      <button nz-button nzType="primary" class="add-btn" (click)="openAddPopup()">Add New</button>
      <button nz-button nzType="primary" class="import-btn" (click)="handleImportFile()">Import</button>
    </div>
    <div class="form-search">
      <nz-input-group nzSearch (nzOnSearch)="filterGateways()" [nzAddOnAfter]="suffixIconButton">
        <input [(ngModel)]="searchTerm" (input)="filterGateways()" (nzOnSearch)="filterGateways()" type="text" nz-input
          placeholder="Filter Name" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSearch><nz-icon nzType="search" /></button>
      </ng-template>
    </div>
  </div>
  <nz-table #basicTable nzBordered [nzData]="filteredList" nzShowSizeChanger nzShowPagination
    [nzLoading]="isTableLoading">
    <thead>
      <tr>
        <th nzWidth="15%">UUID</th>
        <th>Name</th>
        <th>Latitude</th>
        <th>Longitude</th>
        <th>IP Address</th>
        <th>Port</th>
        <th>Flush Connection MS</th>
        <th>Enable Realtime</th>
        <th>Status</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td>{{ data.devicesId }}</td>
        <td>{{ data.name }}</td>
        <td>{{ formatDecimal(data.latitude) }}
          <span *ngIf="false">{{ convertToDMS(data.latitude) }}</span>
        </td>
        <td>{{ formatDecimal(data.longitude) }}
          <span *ngIf="false">{{ convertToDMS(data.longitude) }}</span>
        </td>
        <td>{{ data.iPAddress }}</td>
        <td>{{ data.port }}</td>
        <td>{{ data.flushConnectionMs }}</td>
        <td>
          <nz-tag [nzColor]="data.enableRealtime ? 'green' : 'red'">
            {{ data.enableRealtime ? 'Enabled' : 'Disabled' }}
          </nz-tag>
        </td>
        <td>
          <nz-tag [nzColor]="getTagColor(data.isEnabled)">{{ getTagText(data.isEnabled) }}</nz-tag>
        </td>
        <td class="btn-action">
          <!-- <button nz-button nzType="default" (click)="onClickSoftware(data.gatewayId)" class="btn-soft">
            Software Gateway
          </button> -->
          <button nz-button nzType="primary" (click)="openEditPopup(i)">Edit</button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <app-devices-edit-add [isVisible]="isVisible" [isEditMode]="isEditMode" [data]="currentDevice"
    (close)="handleModalClose()" (confirm)="handleModalSave($event)"
    (loadingChange)="handleLoadingChange($event)"></app-devices-edit-add>
  <app-import-device [isVisible]="isImportVisible" (close)="handleImportClose()"
    (confirm)="handleImportSave($event)"></app-import-device>
</div>