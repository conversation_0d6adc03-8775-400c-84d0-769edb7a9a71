<ng-container *ngIf="isWhiteScreenRoute(); else defaultLayout">
  <router-outlet></router-outlet>
</ng-container>

<ng-template #defaultLayout>
  <nz-layout>
    <nz-header>
      <app-header></app-header>
    </nz-header>
    <nz-layout>
      <nz-sider>
        <app-menu></app-menu>
      </nz-sider>
      <nz-content>
        <div style="padding: 24px; min-height: 100%; background: #ffffff;">
          <router-outlet></router-outlet>
        </div>
      </nz-content>
    </nz-layout>
  </nz-layout>
</ng-template>