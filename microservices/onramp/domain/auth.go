package domain

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/authorizer"
)

// Common errors
var (
	ErrInvalidCredentials = errors.New("invalid credentials")
	ErrUserNotFound       = errors.New("user not found")
	ErrAccountDisabled    = errors.New("account is disabled")
	ErrUserAlreadyExists  = errors.New("user already exists")
	ErrInvalidInput       = errors.New("invalid input")
)

type UserPermissions authorizer.UserPermissions

var DefaultPermissions = []authorizer.Permission{}

// AuthRepository defines the interface for auth business logic data access
type AuthRepository interface {
	GetByUsername(ctx context.Context, username string) (*User, *AuthMethod, error)
	GetByOIDCSubject(ctx context.Context, issuer, subject string) (*User, *AuthMethod, error)
	UpdateLastLogin(ctx context.Context, user *User, authMethod *AuthMethod) error
	CreateBasicAuthUser(ctx context.Context, user *User, authMethod *AuthMethod) error
	CreateOIDCUser(ctx context.Context, user *User, authMethod *AuthMethod) error
	GetUserPermissions(ctx context.Context, userID uuid.UUID) *UserPermissions
}

// TODO: Temporary key for the user in the context
type Key int

// TODO: This is a temporary key for the user in the context. Replace with session store later.
const UserKey Key = 0

// User represents a user in the system
type User struct {
	ID           uuid.UUID `json:"id"`
	FirstName    string    `json:"firstName"`
	LastName     string    `json:"lastName"`
	Mobile       string    `json:"mobile"`
	IanaTimezone string    `json:"ianaTimezone"`
	Description  string    `json:"description"`
}

// AuthMethod represents an authentication method for a user
type AuthMethod struct {
	ID           uuid.UUID              `json:"id"`
	UserID       uuid.UUID              `json:"userId"`
	Type         AuthMethodType         `json:"type"`
	Sub          string                 `json:"sub"`
	Issuer       string                 `json:"issuer"`
	UserName     string                 `json:"userName"`
	PasswordHash string                 `json:"passwordHash"`
	Email        string                 `json:"email"`
	Metadata     map[string]interface{} `json:"metadata"`
	IsEnabled    bool                   `json:"isEnabled"`
}

// AuthMethodType represents the type of authentication method
type AuthMethodType string

const (
	AuthMethodTypeUsernamePassword AuthMethodType = "USERNAME_PASSWORD"
	AuthMethodTypeOIDC             AuthMethodType = "OIDC"
)
