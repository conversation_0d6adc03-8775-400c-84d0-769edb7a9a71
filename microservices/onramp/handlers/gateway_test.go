package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGatewayHandler(t *testing.T) {
	// Create a request to pass to our handler (we don't care about method or URL here)
	req := httptest.NewRequest("GET", "/gateway", nil)
	rr := httptest.NewRecorder()

	// Invoke the handler
	GatewayHandler(rr, req)

	// 1. Should return HTTP 200 OK
	assert.Equal(t, 200, rr.Code, "status code")

	// 2. Should set Content-Type to application/json
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"), "Content-Type header")

	// 3. The body should be the JSON array of objects
	var got []map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &got)
	assert.NoError(t, err, "body should be valid JSON")

	// 4. Verify the contents
	want := []map[string]string{
		{
			"id":                    "001",
			"orgId":                 "org-001",
			"gatewayId":             "gw-001",
			"apiKey":                "keysw-abc-123",
			"tokenGateway":          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.V1QDaAApP2K0C8H0MSqj7mDiMg9qLR5O2r0IFcJz1oY",
			"dateLastCheckedInUTC":  "2025-06-12T11:39:00.00Z",
			"pushConfigOnNextCheck": "0",
			"isEnabled":             "0",
			"name":                  "Gateway 1",
		},
		{
			"id":                    "003",
			"orgId":                 "org-003",
			"gatewayId":             "gw-003",
			"apiKey":                "keysw-abc-789",
			"tokenGateway":          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.V1QDaAApP2K0C8H0MSqj7mDiMg9qLR5O2r0IFcJz3oY",
			"dateLastCheckedInUTC":  "2025-06-05T11:39:00.00Z",
			"pushConfigOnNextCheck": "0",
			"isEnabled":             "0",
			"name":                  "Gateway 3",
		},
		{
			"id":                    "002",
			"orgId":                 "org-002",
			"gatewayId":             "gw-002",
			"apiKey":                "keysw-abc-456",
			"tokenGateway":          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.V1QDaAApP2K0C8H0MSqj7mDiMg9qLR5O2r0IFcJz2oY",
			"dateLastCheckedInUTC":  "2025-06-09T11:39:00.00Z",
			"pushConfigOnNextCheck": "1",
			"isEnabled":             "1",
			"name":                  "Gateway 2",
		},
	}
	assert.Equal(t, want, got, "response JSON")
}
