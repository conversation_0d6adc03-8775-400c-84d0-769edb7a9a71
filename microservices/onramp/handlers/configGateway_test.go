package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestConfigGatewayHandler(t *testing.T) {
	// Create a request to pass to our handler (we don't care about method or URL here)
	req := httptest.NewRequest("GET", "/gateway-config", nil)
	rr := httptest.NewRecorder()

	// Invoke the handler
	ConfigGateway<PERSON><PERSON><PERSON>(rr, req)

	// 1. Should return HTTP 200 OK
	assert.Equal(t, 200, rr.Code, "status code")

	// 2. Should set Content-Type to application/json
	assert.Equal(t, "application/json", rr.<PERSON>er().Get("Content-Type"), "Content-Type header")

	// 3. The body should be the JSON array of objects
	var got []map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &got)
	assert.NoError(t, err, "body should be valid JSON")

	// 4. Verify the contents
	want := []map[string]string{
		{
			"log_level":                                          "debug",
			"log_filename":                                       "log/gateway-app.log",
			"log_max_backups":                                    "10",
			"application_version":                                "1.10",
			"log_max_age_in_days":                                "30",
			"log_compress_backups":                               "true",
			"log_file_max_size_mb":                               "10",
			"rest_api_device_endpoint":                           "https://api.dev.synapse-its.app/ingest/v3/state",
			"send_gateway_logs_to_cloud":                         "false",
			"edi_device_persist_connection":                      "true",
			"edi_device_processing_retries":                      "5",
			"record_http_requests_to_folder":                     "false",
			"device_state_send_frequency_seconds":                "60",
			"config_change_check_frequency_seconds":              "30",
			"send_gateway_performance_stats_to_cloud":            "true",
			"software_update_check_frequency_seconds":            "60",
			"channel_state_send_frequency_milliseconds":          "500",
			"gateway_performance_stats_output_frequency_seconds": "45",
			"ws_active":                                          "true",
			"ws_port":                                            "8079",
			"ws_endpoint":                                        "/gateway",
			"ws_max_connections":                                 "2",
			"ws_send_frequency_milliseconds":                     "5000",
			"ws_heartbeat_send_frequency_milliseconds":           "60000",
			"threshold_device_error_seconds":                     "90",
		},
	}
	assert.Equal(t, want, got, "response JSON")
}
