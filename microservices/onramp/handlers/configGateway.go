package handlers

import (
	"encoding/json"
	"net/http"
)

func ConfigGatewayHandler(w http.ResponseWriter, r *http.Request) {
	gateways := []map[string]string{
		{
			"log_level":                                          "debug",
			"log_filename":                                       "log/gateway-app.log",
			"log_max_backups":                                    "10",
			"application_version":                                "1.10",
			"log_max_age_in_days":                                "30",
			"log_compress_backups":                               "true",
			"log_file_max_size_mb":                               "10",
			"rest_api_device_endpoint":                           "https://api.dev.synapse-its.app/ingest/v3/state",
			"send_gateway_logs_to_cloud":                         "false",
			"edi_device_persist_connection":                      "true",
			"edi_device_processing_retries":                      "5",
			"record_http_requests_to_folder":                     "false",
			"device_state_send_frequency_seconds":                "60",
			"config_change_check_frequency_seconds":              "30",
			"send_gateway_performance_stats_to_cloud":            "true",
			"software_update_check_frequency_seconds":            "60",
			"channel_state_send_frequency_milliseconds":          "500",
			"gateway_performance_stats_output_frequency_seconds": "45",
			"ws_active":                                          "true",
			"ws_port":                                            "8079",
			"ws_endpoint":                                        "/gateway",
			"ws_max_connections":                                 "2",
			"ws_send_frequency_milliseconds":                     "5000",
			"ws_heartbeat_send_frequency_milliseconds":           "60000",
			"threshold_device_error_seconds":                     "90",
		},
	}
	w.Header().Set("Content-Type", "application/json")
	_ = json.NewEncoder(w).Encode(gateways)
}
