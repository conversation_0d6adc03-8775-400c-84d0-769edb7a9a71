package handlers

import (
	"encoding/json"
	"net/http"
)

func GatewayHandler(w http.ResponseWriter, r *http.Request) {
	gateways := []map[string]string{
		{
			"id":                    "001",
			"orgId":                 "org-001",
			"gatewayId":             "gw-001",
			"apiKey":                "keysw-abc-123",
			"tokenGateway":          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.V1QDaAApP2K0C8H0MSqj7mDiMg9qLR5O2r0IFcJz1oY",
			"dateLastCheckedInUTC":  "2025-06-12T11:39:00.00Z",
			"pushConfigOnNextCheck": "0",
			"isEnabled":             "0",
			"name":                  "Gateway 1",
		},
		{
			"id":                    "003",
			"orgId":                 "org-003",
			"gatewayId":             "gw-003",
			"apiKey":                "keysw-abc-789",
			"tokenGateway":          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.V1QDaAApP2K0C8H0MSqj7mDiMg9qLR5O2r0IFcJz3oY",
			"dateLastCheckedInUTC":  "2025-06-05T11:39:00.00Z",
			"pushConfigOnNextCheck": "0",
			"isEnabled":             "0",
			"name":                  "Gateway 3",
		},
		{
			"id":                    "002",
			"orgId":                 "org-002",
			"gatewayId":             "gw-002",
			"apiKey":                "keysw-abc-456",
			"tokenGateway":          "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMn0.V1QDaAApP2K0C8H0MSqj7mDiMg9qLR5O2r0IFcJz2oY",
			"dateLastCheckedInUTC":  "2025-06-09T11:39:00.00Z",
			"pushConfigOnNextCheck": "1",
			"isEnabled":             "1",
			"name":                  "Gateway 2",
		},
	}
	w.Header().Set("Content-Type", "application/json")
	_ = json.NewEncoder(w).Encode(gateways)
}
