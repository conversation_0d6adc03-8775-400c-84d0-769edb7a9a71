package handlers

import (
	"encoding/json"
	"net/http"
)

// fooHandler returns a static list of names in JSON.
func DevicesHandler(w http.ResponseWriter, r *http.Request) {
	data := []map[string]interface{}{
		{
			"gatewayId":         "gw-001",
			"devicesId":         "f47ac10b-58cc-4372-a567-0e02b2c3d479",
			"latitude":          "14.082513",
			"longitude":         "98.191238",
			"iPAddress":         "***************",
			"port":              "8080",
			"flushConnectionMs": "2000",
			"enableRealtime":    "true",
			"isEnabled":         "1",
			"name":              "Device Alpha",
			"type":              "Type A",
		},
		{
			"gatewayId":         "gw-002",
			"devicesId":         "f47ac10b-58cc-4372-a567-0e02b2c3d470",
			"latitude":          "15.082513",
			"longitude":         "93.191238",
			"iPAddress":         "***************",
			"port":              "8080",
			"flushConnectionMs": "1800",
			"enableRealtime":    "false",
			"isEnabled":         "0",
			"name":              "Device Beta",
			"type":              "Type B",
		},
	}
	w.Header().Set("Content-Type", "application/json")
	_ = json.NewEncoder(w).Encode(data)
}
