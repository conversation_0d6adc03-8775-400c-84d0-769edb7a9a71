package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDevicesHandler(t *testing.T) {
	// Create a request to pass to our handler (we don't care about method or URL here)
	req := httptest.NewRequest("GET", "/devices", nil)
	rr := httptest.NewRecorder()

	// Invoke the handler
	<PERSON><PERSON><PERSON><PERSON><PERSON>(rr, req)

	// 1. Should return HTTP 200 OK
	assert.Equal(t, 200, rr.Code, "status code")

	// 2. Should set Content-Type to application/json
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"), "Content-Type header")

	// 3. The body should be the JSON array of objects
	var got []map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &got)
	assert.NoError(t, err, "body should be valid JSON")

	// 4. Verify the contents
	want := []map[string]string{
		{
			"gatewayId":         "gw-001",
			"devicesId":         "f47ac10b-58cc-4372-a567-0e02b2c3d479",
			"latitude":          "14.082513",
			"longitude":         "98.191238",
			"iPAddress":         "***************",
			"port":              "8080",
			"flushConnectionMs": "2000",
			"enableRealtime":    "true",
			"isEnabled":         "1",
			"name":              "Device Alpha",
			"type":              "Type A",
		},
		{
			"gatewayId":         "gw-002",
			"devicesId":         "f47ac10b-58cc-4372-a567-0e02b2c3d470",
			"latitude":          "15.082513",
			"longitude":         "93.191238",
			"iPAddress":         "***************",
			"port":              "8080",
			"flushConnectionMs": "1800",
			"enableRealtime":    "false",
			"isEnabled":         "0",
			"name":              "Device Beta",
			"type":              "Type B",
		},
	}
	assert.Equal(t, want, got, "response JSON")
}
