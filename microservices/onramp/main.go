package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"synapse-its.com/onramp/app"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
)

// timeSleep and healthzShutdown are overridden in tests
var (
	timeSleep        = time.Sleep
	healthzNewServer = healthz.NewServer
	loggerFatalf     = logger.Fatalf
)

// Server defines the subset of http.Server methods we use
type Server interface {
	ListenAndServe() error
	Shutdown(context.Context) error
}

// DefaultSignal<PERSON>han sets up a channel for interrupt and terminate
func DefaultSignalChan() <-chan os.Signal {
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM)
	return sigCh
}

// DefaultServer wraps http.Server into our Server interface
func DefaultServer(addr string, handler http.Handler) Server {
	return &http.Server{Addr: addr, Handler: handler}
}

// main simply calls Run, so logic is testable in Run
func main() {
	Run(
		context.Background(),
		os.Getenv("HEALTH_PORT"),
		":8080",
		connect.NewConnections,
		bqbatch.NewDefault,
		DefaultServer,
		DefaultSignalChan,
	)
}

// Run wires up healthz, connections, batching, HTTP server, and graceful shutdown
func Run(
	ctx context.Context,
	healthPort string,
	addr string,
	newConns func(context.Context) *connect.Connections,
	newBatch func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher,
	newServer func(string, http.Handler) Server,
	newSignals func() <-chan os.Signal,
) error {
	// Start healthz endpoint
	healthzsrv := healthzNewServer(healthPort)
	if err := healthzsrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		loggerFatalf("healthz listen error: %v", err)
	}
	defer healthzsrv.Shutdown(ctx)

	logger.Info("Starting onramp...")

	// Initialize shared connections
	conns := newConns(ctx)
	defer conns.Close()

	// Initialize batcher
	batch := newBatch(conns.Bigquery, conns.Pubsub)
	defer func() {
		if err := batch.Shutdown(); err != nil {
			logger.Errorf("batch shutdown error: %v", err)
		}
	}()

	// Signal boot complete, then build HTTP handler
	healthzsrv.SetBootComplete()
	application := app.NewApp(conns, batch)
	srv := newServer(addr, application.Serve())

	// Create a waitgroup go goroutine
	// This only exists because, when testing, everything happens so quickly that
	// the test thinks that the server is not running when, in reality, the
	// goroutine has not had a chance to call ListenAndServe yet.
	var wg sync.WaitGroup
	wg.Add(1)

	// Start HTTP server in background
	go func() {
		wg.Done() // signal that goroutine is about to call Listen and serve
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			loggerFatalf("HTTP server error: %v", err)
		}
	}()

	// wait for goroutine to start before marking ready
	wg.Wait()

	logger.Infof("Onramp running on %s", addr)
	healthzsrv.SetReady()

	// Wait for shutdown signal or context cancellation
	sigCh := newSignals()
	select {
	case <-ctx.Done():
		// no-op for test coverage
		_ = true
	case <-sigCh:
		// no-op for test coverage
		_ = true
	}

	logger.Info("Shutdown signal received; stopping...")
	healthzsrv.SetNotReady()

	// Let in-flight requests drain
	timeSleep(2 * time.Second)

	// Graceful HTTP shutdown
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := srv.Shutdown(shutdownCtx); err != nil {
		logger.Errorf("Error shutting down HTTP server: %v", err)
	}

	return nil
}
