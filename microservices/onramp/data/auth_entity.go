package data

import (
	"github.com/google/uuid"
)

// UserAuthMethod represents the minimal user and auth method data needed for domain objects
type UserAuthMethod struct {
	// User fields (only what's needed for domain.User)
	UserID       uuid.UUID `db:"user_id"`
	FirstName    *string   `db:"user_firstname"`
	LastName     *string   `db:"user_lastname"`
	Mobile       *string   `db:"user_mobile"`
	IanaTimezone string    `db:"user_ianatimezone"`
	Description  *string   `db:"user_description"`

	// AuthMethod fields (only what's needed for domain.AuthMethod)
	AuthMethodID     uuid.UUID `db:"authmethod_id"`
	AuthMethodUserID uuid.UUID `db:"authmethod_userid"`
	Type             string    `db:"authmethod_type"`
	Sub              *string   `db:"authmethod_sub"`
	Issuer           *string   `db:"authmethod_issuer"`
	UserName         *string   `db:"authmethod_username"`
	PasswordHash     *string   `db:"authmethod_passwordhash"`
	Email            *string   `db:"authmethod_email"`
	Metadata         []byte    `db:"authmethod_metadata"`
	IsEnabled        bool      `db:"authmethod_isenabled"`
}
