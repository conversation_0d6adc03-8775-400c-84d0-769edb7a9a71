const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const { waitForPageLoad } = require('../support/utils');

Given('I am on the organizations page', async function () {
  await waitForPageLoad(this.driver, 20000);
  let submitButton = await this.driver.wait(until.elementLocated(By.id('select-organizations')), 10000);
  let menuItem = await this.driver.wait(until.elementIsVisible(submitButton), 10000);
  await menuItem.click();
  if (typeof this.waitForAngularStable === 'function') {
    await this.waitForAngularStable();
  }
  await waitForPageLoad(this.driver, 20000);
});

Then('I see the page title {string}', async function (expectedTitle) {
  await waitForPageLoad(this.driver, 20000);
  let titleOrgPage = await this.driver.wait(until.elementLocated(By.id('title-organizations')));
  let titleElement = await this.driver.wait(until.elementIsVisible(titleOrgPage), 10000);
  const actualTitle = await titleElement.getText();
  if (actualTitle !== expectedTitle) {
    throw new Error(`Expected title "${expectedTitle}" but got "${actualTitle}"`);
  }
});

Then('I see the organization list with at least one record', async function () {
  const orgRows = await this.driver.findElements(By.css('#organization-list .default-row, #organization-list .highlight-row'));
  if (orgRows.length === 0) {
    throw new Error('No organizations found in the list');
  }
});

Then('the first organization\'s name is not empty', async function () {
  const firstOrgName = await this.driver.findElement(By.css('#organization-list .default-row:first-child a, #organization-list .highlight-row:first-child a')).getText();
  if (!firstOrgName.trim()) {
    throw new Error('First organization name is empty');
  }
});

When('I enter the search keyword {string}', async function (searchTerm) {
  const searchInput = await this.driver.findElement(By.css('.form-search input[nz-input]'));
  await searchInput.clear();
  await searchInput.sendKeys(searchTerm);
  await this.driver.sleep(1000);

});

Then('I see the organization list only contains organizations named {string}', async function (expectedName) {
  const orgNames = await this.driver.findElements(By.css('#organization-list .default-row a, #organization-list .highlight-row a'));
  for (let name of orgNames) {
    const text = await name.getText();
    if (text !== expectedName) {
      throw new Error(`Found unexpected organization name "${text}", expected "${expectedName}"`);
    }
  }

});

When('I click the add organization button', async function () {
  const addButton = await this.driver.findElement(By.css('.add-btn'));
  await addButton.click();
  await waitForPageLoad(this.driver);
  const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
  let isVisible = await modal.isDisplayed();
  let attempts = 0;
  while (!isVisible && attempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await modal.isDisplayed();
    attempts++;
  }
  if (!isVisible) {
    throw new Error('Modal is not visible after waiting');
  }
});

When('I fill in the organization name {string}', async function (orgName) {
  const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
  let isVisible = await modal.isDisplayed();
  let modalAttempts = 0;
  while (!isVisible && modalAttempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await modal.isDisplayed();
    modalAttempts++;
  }
  if (!isVisible) {
    throw new Error('Modal is not visible after waiting');
  }
  const nameInput = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-name"], .cdk-overlay-pane .ant-modal input[name="name"]')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(nameInput), 5000);
  await nameInput.clear();
  await nameInput.sendKeys(orgName);
});

When('I fill in the organization description {string}', async function (orgDescription) {
  const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
  let isVisible = await modal.isDisplayed();
  let modalAttempts = 0;
  while (!isVisible && modalAttempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await modal.isDisplayed();
    modalAttempts++;
  }
  if (!isVisible) {
    throw new Error('Modal is not visible after waiting');
  }
  const descriptionInput = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-description"], .cdk-overlay-pane .ant-modal input[name="description"]')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(descriptionInput), 5000);
  await descriptionInput.clear();
  await descriptionInput.sendKeys(orgDescription);
});

When('I submit the new organization form', async function () {
  const submitButton = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')),
    15000
  );
  await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
  await submitButton.click();
  await this.driver.sleep(3000);
  await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
});

Then('I see {string} in the organization list', async function (orgName) {
  await this.driver.wait(until.elementLocated(By.id('organization-list')), 30000);
  const orgLinks = await this.driver.findElements(By.css('#organization-list .default-row a, #organization-list .highlight-row a'));
  let found = false;
  const allNames = [];
  for (let link of orgLinks) {
    const text = await link.getText();
    const trimmedText = text.trim();
    allNames.push(trimmedText);
    if (trimmedText === orgName) {
      found = true;
      break;
    }
  }
  if (!found) {
    throw new Error(`Expected to see "${orgName}" but found names: ${allNames.join(', ')}`);
  }
});

When('I click the edit button of the first organization', async function () {
  const editButton = await this.driver.findElement(By.css('#organization-list .default-row:first-child .edit-btn, #organization-list .highlight-row:first-child .edit-btn'));
  await editButton.click();
  const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
  let isVisible = await modal.isDisplayed();
  let attempts = 0;
  while (!isVisible && attempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await modal.isDisplayed();
    attempts++;
  }
  if (!isVisible) {
    throw new Error('Modal is not visible after waiting');
  }
});

When('I change the organization name to {string}', async function (newName) {
  const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
  let isVisible = await modal.isDisplayed();
  let modalAttempts = 0;
  while (!isVisible && modalAttempts < 3) {
    await this.driver.sleep(2000);
    isVisible = await modal.isDisplayed();
    modalAttempts++;
  }
  if (!isVisible) {
    throw new Error('Modal is not visible after waiting');
  }
  const nameInput = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-name"], .cdk-overlay-pane .ant-modal input[name="name"]')),
    15000
  );
  await this.driver.wait(until.elementIsVisible(nameInput), 5000);
  await nameInput.clear();
  await nameInput.sendKeys(newName);
});

When('I submit the edit form', async function () {
  const submitButton = await this.driver.wait(
    until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')),
    15000
  );
  await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
  await submitButton.click();
  await this.driver.sleep(3000);
  await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
});

When('I check the database for {string}', async function (orgName) {
  const baseUrl = process.env.BASE_URL || 'http://onramp:4200';
  let response = null;
  let attempts = 0;
  const maxAttempts = 3;
  while (attempts < maxAttempts) {
    try {
      response = await this.driver.executeAsyncScript(`
          var callback = arguments[arguments.length - 1];
          fetch('${baseUrl}/api/organizations?name=${encodeURIComponent(arguments[0])}', { method: 'GET' })
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
              }
              return response.json();
            })
            .then(data => callback(data))
            .catch(err => callback({ error: err.message }));
        `, orgName);
      if (response && !response.error) {
        break;
      }
      attempts++;
      await this.driver.sleep(2000);
    } catch (err) {
      attempts++;
      await this.driver.sleep(2000);
    }
  }
  if (response && response.error) {
    throw new Error(`API error: ${response.error}`);
  }
  if (!response) {
    throw new Error('Failed to fetch data after multiple attempts');
  }
  this.lastDbCheck = response && response.data && Array.isArray(response.data) && response.data.some(org => org && org.name && org.name.trim() === orgName.trim());
  if (!this.lastDbCheck) {
    throw new Error(`Organization "${orgName}" not found in API response: ${JSON.stringify(response, null, 2)}`);
  }
});

Then('the database contains {string}', async function (orgName) {
  if (typeof this.lastDbCheck === 'undefined' || this.lastDbCheck === null) {
    throw new Error('Database check failed or not performed');
  }
  if (!this.lastDbCheck) {
    throw new Error(`Database does not contain "${orgName}"`);
  }
});

When('I refresh the organization list', async function () {
  await this.driver.navigate().refresh();
  await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
});