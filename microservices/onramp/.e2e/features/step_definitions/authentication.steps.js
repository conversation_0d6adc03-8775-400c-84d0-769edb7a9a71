const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');
const fs = require('fs');
const { waitForPageLoad } = require('../support/utils');
const path = require('path');

const retry = async (fn, retries = 3, delay = 500) => {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (err) {
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw err;
    }
  }
};

async function closeAllModals(driver, excludeSelector = null) {
  const modals = await driver.findElements(By.css('.ant-modal-wrap, .nz-modal-confirm-container, .ant-modal-confirm'));
  for (const modal of modals) {
    if (excludeSelector && (await modal.getAttribute('class')).includes(excludeSelector.split('.')[1])) {
      continue;
    }
    if (await modal.isDisplayed()) {
      await driver.executeScript('arguments[0].style.display = "none";', modal);
    }
  }
}

// Background steps (kept unchanged)
Given('I am on the home page', async function () {
  const baseUrl = process.env.BASE_URL || 'http://localhost:4200';
  await this.driver.get(`${baseUrl}/`);
  await waitForPageLoad(this.driver, 15000, 'div.ant-modal-confirm.custom-login-modal');
});

Then('I should see a modal with the title {string}', async function (expectedTitle) {
  await waitForPageLoad(this.driver, 15000, 'div.ant-modal-confirm.custom-login-modal');

});

When('I click the {string} button with class {string}', async function (buttonText, className) {
  let btn;
  try {
    btn = await this.driver.wait(
      until.elementLocated(By.css('div.ant-modal-confirm.custom-login-modal-required .ant-modal-confirm-btns .ant-btn-primary')),
      30000,
      `Timeout waiting for button "${buttonText}" with class "${className}"`
    );
  } catch (primaryErr) {
    btn = await this.driver.wait(
      until.elementLocated(By.xpath(`//div[contains(@class, 'ant-modal-confirm') and contains(@class, 'custom-login-modal-required')]//button[contains(text(), "${buttonText}")]`)),
      15000,
      `Timeout waiting for fallback button "${buttonText}"`
    );
  }
  await this.driver.wait(until.elementIsVisible(btn), 10000);
  await this.driver.wait(until.elementIsEnabled(btn), 10000);
  await this.driver.executeScript('arguments[0].scrollIntoView(true);', btn);
  await retry(async () => {
    try {
      await btn.click();
    } catch (clickErr) {
      if (clickErr.name === 'ElementClickInterceptedError') {
        await this.driver.executeScript('arguments[0].click();', btn);
      } else {
        throw clickErr;
      }
    }
  });
  await waitForPageLoad(this.driver);
});

Then('I should see the login page with the title {string} in an h2 tag', async function (expectedTitle) {
  await waitForPageLoad(this.driver, 15000);
  const titleElement = await this.driver.wait(
    until.elementLocated(By.css('h2')),
    15000,
    `Timeout waiting for login page title "${expectedTitle}"`
  );
  await this.driver.wait(until.elementIsVisible(titleElement), 3000);
});

When('I click the {string} button with id {string}', async function (buttonText, id) {
  await waitForPageLoad(this.driver, 15000);
  await closeAllModals(this.driver);
  const btn = await this.driver.wait(
    until.elementLocated(By.id(id)),
    15000,
    `Timeout waiting for button "${buttonText}" with id "${id}"`
  );
  await this.driver.wait(until.elementIsVisible(btn), 3000);
  await this.driver.wait(until.elementIsEnabled(btn), 3000);
  await this.driver.executeScript('arguments[0].scrollIntoView(true);', btn);
  const btnText = await btn.getText();
  if (!btnText.toLowerCase().includes(buttonText.toLowerCase())) {
    throw new Error(`Expected button text "${buttonText}", but found "${btnText}"`);
  }
  try {
    await btn.click();
  } catch (clickErr) {
    if (clickErr.name === 'ElementClickInterceptedError' || clickErr.name === 'StaleElementReferenceError') {
      await this.driver.executeScript('arguments[0].click();', btn);
    } else {
      throw clickErr;
    }
  }
});

When('I complete the Keycloak login form with username {string} and password {string}', async function (username, password) {
  await waitForPageLoad(this.driver, 20000);

  let usernameField = await this.driver.wait(until.elementLocated(By.id("username")), 10000)
  let userNameEl = await this.driver.wait(until.elementIsVisible(usernameField), 3000);
  await userNameEl.clear();
  await userNameEl.sendKeys(username);
  let passwordField = await this.driver.wait(until.elementLocated(By.id("password")), 10000)
  let passwordEl = await this.driver.wait(until.elementIsVisible(passwordField), 3000);

  await passwordEl.clear();
  await passwordEl.sendKeys(password);
  let timestamp = Date.now();

  let screenshot = await this.driver.takeScreenshot();
  let screenshotDir = path.resolve('screenshots');
  fs.mkdirSync(screenshotDir, { recursive: true });
  let screenshotPath = path.join(screenshotDir, `${timestamp}.png`);
  fs.writeFileSync(screenshotPath, screenshot, 'base64');

  let submitButton = await this.driver.wait(until.elementLocated(By.id("kc-login")), 10000)
  await this.driver.wait(until.elementIsVisible(submitButton), 3000);
  await this.driver.wait(until.elementIsEnabled(submitButton), 5000);
  await this.driver.actions()
    .move({ origin: submitButton })
    .click()
    .perform();

  timestamp = Date.now();
  screenshot = await this.driver.takeScreenshot();
  screenshotPath = path.join(screenshotDir, `${timestamp}.png`);
  fs.writeFileSync(screenshotPath, screenshot, 'base64');

  let logoutElm = await this.driver.wait(until.elementLocated(By.id("btn-logout")), 20000)
  await this.driver.wait(until.elementIsVisible(logoutElm), 10000);

});

Then('I should be redirected back to the application', async function () {
  await waitForPageLoad(this.driver, 30000);
  const currentUrl = await this.driver.getCurrentUrl();
  const baseUrl = process.env.BASE_URL || 'http://localhost:4200';
  if (!currentUrl.startsWith(baseUrl)) {
    throw new Error(`Expected to be redirected to ${baseUrl}, but found ${currentUrl}`);
  }
});