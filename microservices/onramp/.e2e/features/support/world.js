const { setWorldConstructor } = require('@cucumber/cucumber');
const { until, By } = require('selenium-webdriver');

class CustomWorld {
  constructor() { }

  /**
   * Waits for the browser to redirect back to BASE_URL
   * and for Angular to render its <nav> inside <app-root>.
   */
  async waitForAppReady(timeout = 15000) {
    // 1) URL
    await this.driver.wait(
      async () => (await this.driver.getCurrentUrl()).startsWith(process.env.BASE_URL),
      timeout,
      `Expected URL to start with ${process.env.BASE_URL}`
    );

    // 2) DOM
    await this.driver.wait(
      until.elementLocated(By.css('app-root nav')),
      timeout,
      'Timed out waiting for <nav> inside <app-root>'
    );
  }

  /**
   * Wait until Angular reports itself as stable.
   */
  async waitForAngularStable(timeout = 15000) {
    await this.driver.wait(
      async () => {
        return this.driver.executeScript(() => {
          // In-browser function: return false until Angular testability is ready
          if (
            window.getAllAngularTestabilities &&
            Array.isArray(window.getAllAngularTestabilities())
          ) {
            return window
              .getAllAngularTestabilities()
              .every(testability => testability.isStable());
          }
          return false;
        });
      },
      timeout,
      'Timed out waiting for Angular to become stable'
    );
  }

  async clickTheAddOrganizationButton() {
    try {
      const addButton = await this.driver.findElement(By.css('.add-btn'));
      await addButton.click();
      const modal = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal')), 15000);
      let isVisible = await modal.isDisplayed();
      let attempts = 0;
      while (!isVisible && attempts < 3) {
        await this.driver.sleep(1000);
        isVisible = await modal.isDisplayed();
        attempts++;
      }
      if (!isVisible) {
        throw new Error('Modal is not visible after waiting');
      }
    } catch (err) {
      throw err;
    }
  }
  async fillInTheOrganizationName(orgName) {
    try {
      const nameInput = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-name"]')), 10000);
      await nameInput.clear();
      await nameInput.sendKeys(orgName);
    } catch (err) {
      throw err;
    }
  }
  async fillInTheOrganizationDescription(orgDescription) {
    try {
      const nameInput = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal input[id="new-organization-description"]')), 10000);
      await nameInput.clear();
      await nameInput.sendKeys(orgDescription);
    } catch (err) {
      throw err;
    }
  }

  async submitTheNewOrganizationForm() {
    try {
      const submitButton = await this.driver.wait(until.elementLocated(By.css('.cdk-overlay-pane .ant-modal button[nzType="primary"]:not([nzType="default"])')), 10000);
      await submitButton.click();
      await this.driver.sleep(3000);
      await this.driver.wait(until.elementLocated(By.id('organization-list')), 20000);
    } catch (err) {
      console.error('Error in submitTheNewOrganizationForm:', err);
      throw err;
    }
  }

  async checkTheDatabaseFor(orgName) {
    try {
      const baseUrl = process.env.BASE_URL || 'http://localhost:8080';
      const response = await this.driver.executeAsyncScript(`
        var callback = arguments[arguments.length - 1];
        fetch('${baseUrl}/api/organizations', { method: 'GET' })
          .then(response => response.json())
          .then(data => callback(data.data.some(org => org.name === '${orgName}')))
          .catch(err => callback(false));
      `);
      this.lastDbCheck = response;
      return response;
    } catch (err) {
      console.error('Error in checkTheDatabaseFor:', err);
      console.error('Page source at error:\n', await this.driver.getPageSource());
      throw err;
    }
  }
}

setWorldConstructor(CustomWorld);
