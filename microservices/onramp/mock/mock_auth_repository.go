package mock

import (
	"context"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/onramp/domain"
)

// MockAuthRepository is a testify-based mock for domain.AuthRepository
// Implements all methods of AuthRepository interface.
type MockAuthRepository struct {
	mock.Mock
}

func (m *MockAuthRepository) GetByUsername(ctx context.Context, username string) (*domain.User, *domain.AuthMethod, error) {
	args := m.Called(ctx, username)
	user, _ := args.Get(0).(*domain.User)
	authMethod, _ := args.Get(1).(*domain.AuthMethod)
	return user, authMethod, args.Error(2)
}

func (m *MockAuthRepository) UpdateLastLogin(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	args := m.Called(ctx, user, authMethod)
	return args.Error(0)
}

func (m *MockAuthRepository) CreateBasicAuthUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	args := m.Called(ctx, user, authMethod)
	return args.Error(0)
}

func (m *MockAuthRepository) GetByOIDCSubject(ctx context.Context, issuer, subject string) (*domain.User, *domain.AuthMethod, error) {
	args := m.Called(ctx, issuer, subject)
	user, _ := args.Get(0).(*domain.User)
	authMethod, _ := args.Get(1).(*domain.AuthMethod)
	return user, authMethod, args.Error(2)
}

func (m *MockAuthRepository) CreateOIDCUser(ctx context.Context, user *domain.User, authMethod *domain.AuthMethod) error {
	args := m.Called(ctx, user, authMethod)
	return args.Error(0)
}

func (m *MockAuthRepository) GetUserPermissions(ctx context.Context, userID uuid.UUID) *domain.UserPermissions {
	args := m.Called(ctx, userID)
	if perms, ok := args.Get(0).(*domain.UserPermissions); ok {
		return perms
	}
	return nil
}
