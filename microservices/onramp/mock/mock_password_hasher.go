package mock

import "github.com/stretchr/testify/mock"

// MockPasswordHasher is a testify-based mock for PasswordHasher
// Implements all methods of PasswordHasher interface.
type MockPasswordHasher struct {
	mock.Mock
}

func (m *MockPasswordHasher) HashPassword(password string) string {
	args := m.Called(password)
	return args.String(0)
}

func (m *MockPasswordHasher) ComparePassword(password, hash string) bool {
	args := m.Called(password, hash)
	return args.Bool(0)
}
