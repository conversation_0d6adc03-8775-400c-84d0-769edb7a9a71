package mock

import (
	"context"

	"github.com/stretchr/testify/mock"
	"synapse-its.com/onramp/data"
)

type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) BasicAuth(ctx context.Context, req *data.BasicAuthRequest) (*data.LoginResponse, error) {
	args := m.Called(ctx, req)
	resp, _ := args.Get(0).(*data.LoginResponse)
	return resp, args.Error(1)
}

func (m *MockAuthService) Register(ctx context.Context, req *data.RegisterRequest) error {
	args := m.Called(ctx, req)
	return args.Error(0)
}

func (m *MockAuthService) HandleOIDCLogin(ctx context.Context, req *data.OIDCLoginRequest) (*data.LoginResponse, error) {
	args := m.Called(ctx, req)
	resp, _ := args.Get(0).(*data.LoginResponse)
	return resp, args.Error(1)
}

func (m *MockAuthService) ProcessOAuth2Callback(ctx context.Context, req *data.OAuth2CallbackRequest) (*data.OAuth2CallbackResponse, error) {
	args := m.Called(ctx, req)
	resp, _ := args.Get(0).(*data.OAuth2CallbackResponse)
	return resp, args.Error(1)
}
