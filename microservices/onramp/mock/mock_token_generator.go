package mock

import (
	"time"

	"github.com/stretchr/testify/mock"
)

// MockTokenGenerator is a testify-based mock for TokenGenerator
// Implements all methods of TokenGenerator interface.
type MockTokenGenerator struct {
	mock.Mock
}

func (m *MockTokenGenerator) GenerateToken(username string) (string, time.Time, error) {
	args := m.Called(username)
	return args.String(0), args.Get(1).(time.Time), args.Error(2)
}
