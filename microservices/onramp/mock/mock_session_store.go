package mock

import (
	"github.com/stretchr/testify/mock"
	"synapse-its.com/onramp/domain"
)

// MockSessionStore is a mock implementation of domain.SessionStore using testify/mock.
type MockSessionStore struct {
	mock.Mock
}

func (m *MockSessionStore) GetSession(sessionID string) (*domain.Session, bool) {
	args := m.Called(sessionID)
	session, _ := args.Get(0).(*domain.Session)
	return session, args.Bool(1)
}

func (m *MockSessionStore) SetSession(sessionID string, session *domain.Session) {
	m.Called(sessionID, session)
}

func (m *MockSessionStore) ClearSession(sessionID string) {
	m.Called(sessionID)
}
