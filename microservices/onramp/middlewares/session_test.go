package middlewares

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/domain"
	onrampMocks "synapse-its.com/onramp/mock"
)

func TestSessionMiddleware(t *testing.T) {
	// Create a test handler that checks for session data in context
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		sessionData, ok := r.Context().Value(SessionContextKey).(*domain.Session)
		if !ok {
			http.Error(w, "no session in context", http.StatusInternalServerError)
			return
		}
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(sessionData)
	})

	// Create a mock session store
	mockSessionStore := &onrampMocks.MockSessionStore{}

	// Create test session data
	testSession := &domain.Session{
		UserID: "test-user-123",
		OAuthToken: domain.FromOAuth2Token(&oauth2.Token{
			AccessToken: "test-access-token",
		}),
		UserPermissions: &domain.UserPermissions{
			UserID:      "test-user-123",
			Permissions: domain.DefaultPermissions,
		},
	}

	// Set up mock expectations
	mockSessionStore.On("GetSession", "valid-session-id").Return(testSession, true)
	mockSessionStore.On("GetSession", "invalid-session-id").Return((*domain.Session)(nil), false)

	sessionHandler := SessionMiddleware(mockSessionStore)(testHandler)

	tests := []struct {
		name          string
		sessionCookie *http.Cookie
		wantCode      int
		expectSession bool
	}{
		{
			name:          "no session cookie",
			sessionCookie: nil,
			wantCode:      http.StatusInternalServerError, // Handler expects session but none provided
			expectSession: false,
		},
		{
			name:          "valid session cookie",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "valid-session-id"},
			wantCode:      http.StatusOK,
			expectSession: true,
		},
		{
			name:          "invalid session cookie",
			sessionCookie: &http.Cookie{Name: "session_id", Value: "invalid-session-id"},
			wantCode:      http.StatusInternalServerError, // Handler expects session but none provided
			expectSession: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/test", nil)
			if tc.sessionCookie != nil {
				req.AddCookie(tc.sessionCookie)
			}
			rr := httptest.NewRecorder()

			sessionHandler.ServeHTTP(rr, req)

			assert.Equal(t, tc.wantCode, rr.Code)

			if tc.expectSession {
				// Verify that session data was returned in response
				var responseSession domain.Session
				err := json.Unmarshal(rr.Body.Bytes(), &responseSession)
				assert.NoError(t, err)
				assert.Equal(t, testSession.UserID, responseSession.UserID)
				assert.Equal(t, testSession.OAuthToken.AccessToken, responseSession.OAuthToken.AccessToken)
			}
		})
	}

	// Verify that all expected mock calls were made
	mockSessionStore.AssertExpectations(t)
}
