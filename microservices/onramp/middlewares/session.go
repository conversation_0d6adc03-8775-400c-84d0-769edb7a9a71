package middlewares

import (
	"context"
	"net/http"

	"synapse-its.com/onramp/domain"
)

type sessionContextKey string

const SessionContextKey = sessionContextKey("session")

// SessionMiddleware injects session data from cookie into context
func SessionMiddleware(store domain.SessionStore) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			c, err := r.<PERSON>("session_id")
			if err != nil {
				next.ServeHTTP(w, r)
				return
			}
			sessionData, ok := store.GetSession(c.Value)
			if !ok {
				next.ServeHTTP(w, r)
				return
			}
			ctx := context.WithValue(r.Context(), SessionContextKey, sessionData)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
