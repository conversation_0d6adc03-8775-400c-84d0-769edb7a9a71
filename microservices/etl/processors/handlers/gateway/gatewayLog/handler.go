package gatewayLog

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"cloud.google.com/go/pubsub"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/etl/processors/handlers/gateway/helper"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc            func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc      func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQ<PERSON>ender                func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalGatewayLogsFunc func(raw []byte) (*gatewayv1.GatewayLogs, error)
	BatchGetter              func(ctx context.Context) (bqbatch.Batcher, error)
	ToBQConverter            func(orgID string, sgwID string, tz string, topic string, pubsubID string, pubsubTS time.Time, messageTime time.Time, logMessage []edihelper.LogEntry, rawMessage []byte) schemas.GatewayLogMessage
	ParseLogEntries          func(compressedData []byte, v *[]edihelper.LogEntry) error
	UncompressFunc           func([]byte, interface{}) error
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector            ConnectorFunc
	ParseAttributes      ParseAttributesFunc
	SendToDLQ            DLQSender
	UnmarshalGatewayLogs UnmarshalGatewayLogsFunc
	GetBatch             BatchGetter
	ToBQ                 ToBQConverter
	ParseLogEntries      ParseLogEntries
}

func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, sub connect.PsSubscription) {
	return func(ctx context.Context, sub connect.PsSubscription) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			return
		}
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", sub.ID(), string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshall protobuf message
			gatewayLogs, errUm := deps.UnmarshalGatewayLogs(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling protobuf message: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Gateway Logs: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Decompress the compressed data into a string
			var logEntries []edihelper.LogEntry
			err = deps.ParseLogEntries(gatewayLogs.GetMessage(), &logEntries)
			if err != nil {
				logger.Errorf("Error decompressing the Gateway Logs: %v", err)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error decompressing the Gateway Logs: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Add to the bigquery insert batch
			bqItem := deps.ToBQ(
				commonAttrs.OrganizationIdentifier,
				httpHeader.GatewayDeviceID,
				httpHeader.GatewayTimezone,
				commonAttrs.Topic,
				msg.ID,
				msg.PublishTime.UTC(),
				gatewayLogs.GetMessageTime().AsTime(),
				logEntries,
				gatewayLogs.GetMessage(),
			)
			if err = batch.Add(bqItem); err != nil {
				logger.Errorf("Error adding message to batch: %v", err)
				msg.Nack()
				return
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription %s: %v", sub.ID(), err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:            connect.GetConnections,
	ParseAttributes:      pubsubdata.ParseAttributes,
	SendToDLQ:            etlShared.SendToDLQ,
	UnmarshalGatewayLogs: etlShared.UnmarshalGatewayLogs,
	GetBatch:             bqbatch.GetBatch,
	ToBQ:                 edihelper.GatewayLogToBQ,
	ParseLogEntries:      parseLogEntries,
})

func parseLogEntriesWithDeps(
	uncompress UncompressFunc,
	compressed []byte,
	out *[]edihelper.LogEntry,
) error {
	// 1) uncompress into []string
	var rawStrings []string
	if err := uncompress(compressed, &rawStrings); err != nil {
		return fmt.Errorf("uncompress: %w", err)
	}

	// 2) for each JSON‐string, unmarshal into a LogEntry
	parsed := make([]edihelper.LogEntry, 0, len(rawStrings))
	for i, s := range rawStrings {
		var e edihelper.LogEntry
		if err := json.Unmarshal([]byte(s), &e); err != nil {
			return fmt.Errorf("invalid entry %d: %w", i, err)
		}
		parsed = append(parsed, e)
	}

	*out = parsed
	return nil
}

func parseLogEntries(data []byte, out *[]edihelper.LogEntry) error {
	return parseLogEntriesWithDeps(helper.UnmarshalCompressedBytes, data, out)
}
