package helper

import (
	"encoding/base64"
	"encoding/json"
	"testing"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/api/helper"
)

func TestUnmarshalBase64(t *testing.T) {
	tests := []struct {
		name        string
		b64         string
		setupMsg    func() proto.Message
		expectErr   bool
		errContains string
	}{
		{
			name: "Valid DeviceData",
			setupMsg: func() proto.Message {
				return &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{
							DeviceId: "test-device-123",
							Message:  []byte("test message"),
						},
					},
				}
			},
			expectErr: false,
		},
		{
			name: "Valid DeviceLogs",
			setupMsg: func() proto.Message {
				return &gatewayv1.DeviceLogs{
					DeviceId: "test-device-456",
					Logs: []*gatewayv1.LogEntry{
						{
							LogType: "info",
							Message: [][]byte{[]byte("log message")},
						},
					},
				}
			},
			expectErr: false,
		},
		{
			name: "Valid GatewayLogs",
			setupMsg: func() proto.Message {
				return &gatewayv1.GatewayLogs{
					Message: []byte("gateway log message"),
				}
			},
			expectErr: false,
		},
		{
			name: "Empty protobuf message",
			setupMsg: func() proto.Message {
				return &gatewayv1.DeviceData{}
			},
			expectErr: false,
		},
		{
			name:        "Invalid base64",
			b64:         "invalid-base64!@#$",
			expectErr:   true,
			errContains: "base64 decode failed",
		},
		{
			name:        "Valid base64 but invalid protobuf",
			b64:         base64.StdEncoding.EncodeToString([]byte("invalid protobuf data")),
			expectErr:   true,
			errContains: "proto unmarshal failed",
		},
		{
			name:      "Empty base64 string",
			b64:       "",
			expectErr: false, // Empty string decodes to empty bytes, which is valid protobuf
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var b64Input string
			var targetMsg proto.Message

			if tt.setupMsg != nil {
				// Marshal the message and encode to base64
				sourceMsg := tt.setupMsg()
				data, err := proto.Marshal(sourceMsg)
				require.NoError(t, err)
				b64Input = base64.StdEncoding.EncodeToString(data)

				// Create target message of same type
				switch sourceMsg.(type) {
				case *gatewayv1.DeviceData:
					targetMsg = &gatewayv1.DeviceData{}
				case *gatewayv1.DeviceLogs:
					targetMsg = &gatewayv1.DeviceLogs{}
				case *gatewayv1.GatewayLogs:
					targetMsg = &gatewayv1.GatewayLogs{}
				}
			} else {
				b64Input = tt.b64
				targetMsg = &gatewayv1.DeviceData{} // Default target
			}

			err := UnmarshalBase64(b64Input, targetMsg)

			if tt.expectErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, targetMsg)
			}
		})
	}
}

func TestUnmarshalDeviceData(t *testing.T) {
	tests := []struct {
		name        string
		setupData   func() string
		b64         string
		expectErr   bool
		errContains string
		validate    func(*testing.T, *gatewayv1.DeviceData)
	}{
		{
			name: "Valid DeviceData with single message",
			setupData: func() string {
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{
							DeviceId: "device-001",
							Message:  []byte("sensor data"),
						},
					},
				}
				data, _ := proto.Marshal(deviceData)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceData) {
				assert.Len(t, result.Messages, 1)
				assert.Equal(t, "device-001", result.Messages[0].DeviceId)
				assert.Equal(t, []byte("sensor data"), result.Messages[0].Message)
			},
		},
		{
			name: "Valid DeviceData with multiple messages",
			setupData: func() string {
				deviceData := &gatewayv1.DeviceData{
					Messages: []*gatewayv1.DeviceEntry{
						{
							DeviceId: "device-001",
							Message:  []byte("message 1"),
						},
						{
							DeviceId: "device-002",
							Message:  []byte("message 2"),
						},
					},
				}
				data, _ := proto.Marshal(deviceData)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceData) {
				assert.Len(t, result.Messages, 2)
				assert.Equal(t, "device-001", result.Messages[0].DeviceId)
				assert.Equal(t, "device-002", result.Messages[1].DeviceId)
			},
		},
		{
			name: "Empty DeviceData",
			setupData: func() string {
				deviceData := &gatewayv1.DeviceData{}
				data, _ := proto.Marshal(deviceData)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceData) {
				assert.Empty(t, result.Messages)
			},
		},
		{
			name:        "Invalid base64",
			b64:         "invalid-base64!@#$",
			expectErr:   true,
			errContains: "base64 decode failed",
		},
		{
			name:        "Valid base64 but invalid protobuf",
			b64:         base64.StdEncoding.EncodeToString([]byte("not protobuf data")),
			expectErr:   true,
			errContains: "proto unmarshal failed",
		},
		{
			name:      "Empty string",
			b64:       "",
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceData) {
				assert.NotNil(t, result)
				assert.Empty(t, result.Messages)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var input string
			if tt.setupData != nil {
				input = tt.setupData()
			} else {
				input = tt.b64
			}

			result, err := UnmarshalDeviceData(input)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.validate != nil {
					tt.validate(t, result)
				}
			}
		})
	}
}

func TestUnmarshalDeviceLogs(t *testing.T) {
	tests := []struct {
		name        string
		setupData   func() string
		b64         string
		expectErr   bool
		errContains string
		validate    func(*testing.T, *gatewayv1.DeviceLogs)
	}{
		{
			name: "Valid DeviceLogs",
			setupData: func() string {
				deviceLogs := &gatewayv1.DeviceLogs{
					DeviceId: "device-123",
					Logs: []*gatewayv1.LogEntry{
						{
							LogType: "info",
							Message: [][]byte{[]byte("device log entry")},
						},
					},
				}
				data, _ := proto.Marshal(deviceLogs)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceLogs) {
				assert.Equal(t, "device-123", result.DeviceId)
				assert.Len(t, result.Logs, 1)
				assert.Equal(t, "info", result.Logs[0].LogType)
				assert.Equal(t, [][]byte{[]byte("device log entry")}, result.Logs[0].Message)
			},
		},
		{
			name: "DeviceLogs with multiple log entries",
			setupData: func() string {
				deviceLogs := &gatewayv1.DeviceLogs{
					DeviceId: "device-456",
					Logs: []*gatewayv1.LogEntry{
						{
							LogType: "info",
							Message: [][]byte{[]byte("log 1")},
						},
						{
							LogType: "error",
							Message: [][]byte{[]byte("log 2"), []byte("log 3")},
						},
					},
				}
				data, _ := proto.Marshal(deviceLogs)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceLogs) {
				assert.Equal(t, "device-456", result.DeviceId)
				assert.Len(t, result.Logs, 2)
				assert.Equal(t, "info", result.Logs[0].LogType)
				assert.Equal(t, "error", result.Logs[1].LogType)
				assert.Len(t, result.Logs[1].Message, 2)
			},
		},
		{
			name: "Empty DeviceLogs",
			setupData: func() string {
				deviceLogs := &gatewayv1.DeviceLogs{}
				data, _ := proto.Marshal(deviceLogs)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceLogs) {
				assert.Empty(t, result.DeviceId)
				assert.Empty(t, result.Logs)
			},
		},
		{
			name:        "Invalid base64",
			b64:         "invalid-base64!@#$",
			expectErr:   true,
			errContains: "base64 decode failed",
		},
		{
			name:        "Valid base64 but invalid protobuf",
			b64:         base64.StdEncoding.EncodeToString([]byte("not protobuf data")),
			expectErr:   true,
			errContains: "proto unmarshal failed",
		},
		{
			name:      "Empty string",
			b64:       "",
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.DeviceLogs) {
				assert.NotNil(t, result)
				assert.Empty(t, result.DeviceId)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var input string
			if tt.setupData != nil {
				input = tt.setupData()
			} else {
				input = tt.b64
			}

			result, err := UnmarshalDeviceLogs(input)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.validate != nil {
					tt.validate(t, result)
				}
			}
		})
	}
}

func TestUnmarshalGatewayLogs(t *testing.T) {
	tests := []struct {
		name        string
		setupData   func() string
		b64         string
		expectErr   bool
		errContains string
		validate    func(*testing.T, *gatewayv1.GatewayLogs)
	}{
		{
			name: "Valid GatewayLogs",
			setupData: func() string {
				gatewayLogs := &gatewayv1.GatewayLogs{
					Message: []byte("gateway system log"),
				}
				data, _ := proto.Marshal(gatewayLogs)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.GatewayLogs) {
				assert.Equal(t, []byte("gateway system log"), result.Message)
			},
		},
		{
			name: "Empty GatewayLogs",
			setupData: func() string {
				gatewayLogs := &gatewayv1.GatewayLogs{}
				data, _ := proto.Marshal(gatewayLogs)
				return base64.StdEncoding.EncodeToString(data)
			},
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.GatewayLogs) {
				assert.Empty(t, result.Message)
			},
		},
		{
			name:        "Invalid base64",
			b64:         "invalid-base64!@#$",
			expectErr:   true,
			errContains: "base64 decode failed",
		},
		{
			name:        "Valid base64 but invalid protobuf",
			b64:         base64.StdEncoding.EncodeToString([]byte("not protobuf data")),
			expectErr:   true,
			errContains: "proto unmarshal failed",
		},
		{
			name:      "Empty string",
			b64:       "",
			expectErr: false,
			validate: func(t *testing.T, result *gatewayv1.GatewayLogs) {
				assert.NotNil(t, result)
				assert.Empty(t, result.Message)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var input string
			if tt.setupData != nil {
				input = tt.setupData()
			} else {
				input = tt.b64
			}

			result, err := UnmarshalGatewayLogs(input)

			if tt.expectErr {
				assert.Error(t, err)
				assert.Nil(t, result)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				if tt.validate != nil {
					tt.validate(t, result)
				}
			}
		})
	}
}

func TestUnmarshalCompressedBytes(t *testing.T) {
	// Test data structure
	type TestData struct {
		Name   string   `json:"name"`
		Value  int      `json:"value"`
		Active bool     `json:"active"`
		Items  []string `json:"items"`
	}

	tests := []struct {
		name        string
		setupData   func() []byte
		input       []byte
		expectErr   bool
		errContains string
		validate    func(*testing.T, TestData)
	}{
		{
			name: "Valid compressed JSON data",
			setupData: func() []byte {
				testData := TestData{
					Name:   "test-item",
					Value:  42,
					Active: true,
					Items:  []string{"item1", "item2", "item3"},
				}
				jsonData, _ := json.Marshal(testData)
				compressedData, _ := helper.CompressBytes(jsonData)
				return compressedData
			},
			expectErr: false,
			validate: func(t *testing.T, result TestData) {
				assert.Equal(t, "test-item", result.Name)
				assert.Equal(t, 42, result.Value)
				assert.True(t, result.Active)
				assert.Equal(t, []string{"item1", "item2", "item3"}, result.Items)
			},
		},
		{
			name: "Valid compressed empty JSON object",
			setupData: func() []byte {
				testData := TestData{}
				jsonData, _ := json.Marshal(testData)
				compressedData, _ := helper.CompressBytes(jsonData)
				return compressedData
			},
			expectErr: false,
			validate: func(t *testing.T, result TestData) {
				assert.Empty(t, result.Name)
				assert.Zero(t, result.Value)
				assert.False(t, result.Active)
				assert.Empty(t, result.Items)
			},
		},
		{
			name: "Valid compressed simple JSON",
			setupData: func() []byte {
				testData := TestData{Name: "simple"}
				jsonData, _ := json.Marshal(testData)
				compressedData, _ := helper.CompressBytes(jsonData)
				return compressedData
			},
			expectErr: false,
			validate: func(t *testing.T, result TestData) {
				assert.Equal(t, "simple", result.Name)
			},
		},
		{
			name:        "Invalid compressed data",
			input:       []byte("not compressed data"),
			expectErr:   true,
			errContains: "decompress bytes failed",
		},
		{
			name: "Valid compression but invalid JSON",
			setupData: func() []byte {
				invalidJSON := []byte("invalid json data")
				compressedData, _ := helper.CompressBytes(invalidJSON)
				return compressedData
			},
			expectErr:   true,
			errContains: "invalid character",
		},
		{
			name:        "Empty input",
			input:       []byte{},
			expectErr:   true,
			errContains: "decompress bytes failed",
		},
		{
			name:        "Nil input",
			input:       nil,
			expectErr:   true,
			errContains: "decompress bytes failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var input []byte
			if tt.setupData != nil {
				input = tt.setupData()
			} else {
				input = tt.input
			}

			var result TestData
			err := UnmarshalCompressedBytes(input, &result)

			if tt.expectErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
			} else {
				assert.NoError(t, err)
				if tt.validate != nil {
					tt.validate(t, result)
				}
			}
		})
	}
}

// Benchmark tests for performance
func BenchmarkUnmarshalDeviceData(b *testing.B) {
	// Setup test data
	deviceData := &gatewayv1.DeviceData{
		Messages: []*gatewayv1.DeviceEntry{
			{
				DeviceId: "benchmark-device",
				Message:  []byte("benchmark message data"),
			},
		},
	}
	data, _ := proto.Marshal(deviceData)
	b64Input := base64.StdEncoding.EncodeToString(data)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := UnmarshalDeviceData(b64Input)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkUnmarshalCompressedBytes(b *testing.B) {
	// Setup test data
	testData := map[string]interface{}{
		"name":   "benchmark-test",
		"value":  123,
		"active": true,
		"items":  []string{"item1", "item2", "item3"},
	}
	jsonData, _ := json.Marshal(testData)
	compressedData, _ := helper.CompressBytes(jsonData)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var result map[string]interface{}
		err := UnmarshalCompressedBytes(compressedData, &result)
		if err != nil {
			b.Fatal(err)
		}
	}
}
