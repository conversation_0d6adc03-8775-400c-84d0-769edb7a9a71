package helper

import (
	"encoding/base64"
	"encoding/json"
	"fmt"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/gateway/v1"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/api/helper"
)

// UnmarshalBase64 takes a base64-encoded protobuf message and
// unmarshals it into any proto.Message.
func UnmarshalBase64(b64 string, msg proto.Message) error {
	raw, err := base64.StdEncoding.DecodeString(b64)
	if err != nil {
		return fmt.Errorf("base64 decode failed: %w", err)
	}
	if err := proto.Unmarshal(raw, msg); err != nil {
		return fmt.Errorf("proto unmarshal failed: %w", err)
	}
	return nil
}

// UnmarshalDeviceData decodes & unmarshals into a DeviceData.
func UnmarshalDeviceData(b64 string) (*gatewayv1.DeviceData, error) {
	msg := &gatewayv1.DeviceData{}
	if err := UnmarshalBase64(b64, msg); err != nil {
		return nil, err
	}
	return msg, nil
}

// UnmarshalDeviceLogs decodes & unmarshals into a DeviceLogs.
func UnmarshalDeviceLogs(b64 string) (*gatewayv1.DeviceLogs, error) {
	msg := &gatewayv1.DeviceLogs{}
	if err := UnmarshalBase64(b64, msg); err != nil {
		return nil, err
	}
	return msg, nil
}

// UnmarshalGatewayLogs decodes & unmarshals into a GatewayLogs.
func UnmarshalGatewayLogs(b64 string) (*gatewayv1.GatewayLogs, error) {
	msg := &gatewayv1.GatewayLogs{}
	if err := UnmarshalBase64(b64, msg); err != nil {
		return nil, err
	}
	return msg, nil
}

// UnmarshalCompressedBytes decompresses the data and unmarshals it into a given type
func UnmarshalCompressedBytes(compressedData []byte, v any) error {
	decompressedData, err := helper.DecompressBytes(compressedData)
	if err != nil {
		return fmt.Errorf("decompress bytes failed: %w", err)
	}
	return json.Unmarshal(decompressedData, v)
}
