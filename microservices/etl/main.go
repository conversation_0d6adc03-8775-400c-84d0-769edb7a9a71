package main

import (
	"context"
	"errors"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/twilio/twilio-go"
	twilioClient "synapse-its.com/etl/processors/handlers/notifications/twilio"
	"synapse-its.com/etl/processors/subscriptions"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
)

func main() {
	Run(
		context.Background(),
		os.Getenv("HEALTH_PORT"),
		connect.NewConnections,
		bqbatch.NewDefault,
		subscriptions.DefaultSubscriptions(),
		DefaultSignalChan,
		twilioClient.NewClient,
	)
}

// Expose healthz.NewServer behind a variable so tests can override it.
var (
	healthzNewServer = healthz.NewServer
	loggerFatalf     = logger.Fatalf
)

// DefaultSignalChan returns a buffered channel that will get
// os.Interrupt and syscall.SIGTERM notifications.
func DefaultSignalChan() <-chan os.Signal {
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, os.Interrupt, syscall.SIGTERM)
	return sigCh
}

// subscriptionReadinessCheck returns a function that errors if any subscription in mgr is unhealthy.
func subscriptionReadinessCheck(mgr *subscriptions.Manager) func() error {
	return func() error {
		if mgr.IsAnyUnhealthy() {
			return errors.New("one or more subscriptions are unhealthy")
		}
		return nil
	}
}

// Run sets up connections, health checks, starts all subscriptions via Manager, and blocks until shutdown.
func Run(
	parentCtx context.Context,
	healthPort string,
	newConns func(context.Context) *connect.Connections,
	newBatch func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher,
	subsList []subscriptions.Subscription,
	newSignals func() <-chan os.Signal,
	newTwilioClient func(context.Context) (*twilio.RestClient, error),
) error {
	signals := newSignals()

	// Derive a cancellable context for the ETL process
	ctx, cancel := context.WithCancel(parentCtx)
	defer cancel()

	// Start healthz server (listening on HEALTH_PORT)
	healthzsrv := healthzNewServer(healthPort)
	if err := healthzsrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		loggerFatalf("%v", err)
	}
	defer healthzsrv.Shutdown(ctx)

	logger.Info("Starting ETL process...")

	// Create GCP connections (BigQuery, Pub/Sub, etc.)
	conns := newConns(ctx)
	defer conns.Close()

	// Initialize the batcher and defer its shutdown
	batch := newBatch(conns.Bigquery, conns.Pubsub)
	defer func() {
		if err := batch.Shutdown(); err != nil {
			loggerFatalf("error shutting down batcher: %v", err)
		}
	}()

	// Initialize Twilio client
	twClient, err := newTwilioClient(ctx)
	if err != nil {
		loggerFatalf("Failed to initialize Twilio client: %v", err)
		return err
	}

	// Mark initial boot complete, inject dependencies
	logger.Debug("Setting ETL boot complete and preparing subscriptions...")
	healthzsrv.SetBootComplete()

	ctx = connect.WithConnections(ctx, conns)
	ctx = bqbatch.WithBatch(ctx, batch)
	ctx = twilioClient.WithClient(ctx, twClient)

	// Build a Manager with the "default" subscriptions and a BackoffConfig
	mgr := subscriptions.NewManager(subsList, subscriptions.BackoffConfig{
		Initial:    2 * time.Second,
		Max:        5 * time.Minute,
		MaxRetries: 10,
	})

	// Start all workers
	mgr.Start(ctx, conns.Pubsub)

	// Mark ETL as ready (health checks will now pass if no worker is unhealthy)
	logger.Info("ETL is running.")
	healthzsrv.SetReady()

	// Install a custom readiness check that fails if any subscriptions are unhealthy
	checkFn := subscriptionReadinessCheck(mgr)
	healthzsrv.SetCustomReadinessCheck(checkFn)

	// Block until SIGINT/SIGTERM or ctx is canceled
	select {
	case <-ctx.Done():
		// no-op for test coverage
		_ = true
	case <-signals:
		// no-op for test coverage
		_ = true
	}

	logger.Info("Shutdown signal received, gracefully stopping…")
	healthzsrv.SetNotReady()
	cancel() // tell all subscription goroutines to exit

	// Wait up to 10 seconds for workers to finish
	stragglers := mgr.Wait(10 * time.Second)
	if len(stragglers) > 0 {
		logger.Warnf("Some subscriptions did not exit in time: %v", stragglers)
	}

	logger.Info("ETL shutdown complete.")
	return nil
}
