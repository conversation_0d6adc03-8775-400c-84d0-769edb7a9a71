package onramp_test

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	redisclient "github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/oauth2"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/modules/auth/session"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/logger"
)

// TestRedisSessionStoreIntegration tests the Redis session store with a real Redis connection
func TestRedisSessionStoreIntegration(t *testing.T) {
	// Connect to Redis using environment variables
	redisClient, cleanup := setupRedisConnection(t)
	defer cleanup()

	// Create session store
	sessionStore := session.NewRedisSessionStore(redisClient)

	// Clean up any existing test data
	cleanupTestData(t, redisClient)

	t.Run("SetSession_GetSession_Success", func(t *testing.T) {
		// Create test session data
		testSession := &domain.Session{
			UserID: "test-user-123",
			OAuthToken: domain.FromOAuth2Token(&oauth2.Token{
				AccessToken:  "test-access-token",
				TokenType:    "Bearer",
				RefreshToken: "test-refresh-token",
				Expiry:       time.Now().Add(1 * time.Hour),
			}),
			UserPermissions: &domain.UserPermissions{
				UserID: "test-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "organization",
						ScopeID:        "org-123",
						OrganizationID: "org-123",
						Permissions:    []string{"org_view_users", "org_manage_users"},
					},
				},
			},
		}

		sessionID := "test-session-id-1"

		// Test SetSession
		sessionStore.SetSession(sessionID, testSession)

		// Test GetSession - should find the session
		retrievedSession, found := sessionStore.GetSession(sessionID)
		assert.True(t, found, "Session should be found")
		assert.NotNil(t, retrievedSession, "Retrieved session should not be nil")
		assert.Equal(t, testSession.UserID, retrievedSession.UserID, "UserID should match")
		assert.Equal(t, testSession.OAuthToken.AccessToken, retrievedSession.OAuthToken.AccessToken, "Access token should match")
		assert.Equal(t, testSession.OAuthToken.TokenType, retrievedSession.OAuthToken.TokenType, "Token type should match")
		assert.Equal(t, testSession.OAuthToken.RefreshToken, retrievedSession.OAuthToken.RefreshToken, "Refresh token should match")
		assert.Equal(t, testSession.UserPermissions.UserID, retrievedSession.UserPermissions.UserID, "User permissions UserID should match")
		assert.Len(t, retrievedSession.UserPermissions.Permissions, 1, "Should have one permission group")
		assert.Equal(t, testSession.UserPermissions.Permissions[0].Scope, retrievedSession.UserPermissions.Permissions[0].Scope, "Permission scope should match")
		assert.Equal(t, testSession.UserPermissions.Permissions[0].ScopeID, retrievedSession.UserPermissions.Permissions[0].ScopeID, "Permission scope ID should match")
		assert.Equal(t, testSession.UserPermissions.Permissions[0].OrganizationID, retrievedSession.UserPermissions.Permissions[0].OrganizationID, "Permission organization ID should match")
		assert.Equal(t, testSession.UserPermissions.Permissions[0].Permissions, retrievedSession.UserPermissions.Permissions[0].Permissions, "Permission list should match")
	})

	t.Run("GetSession_NotFound", func(t *testing.T) {
		// Test GetSession with non-existent session
		retrievedSession, found := sessionStore.GetSession("non-existent-session-id")
		assert.False(t, found, "Session should not be found")
		assert.Nil(t, retrievedSession, "Retrieved session should be nil")
	})

	t.Run("SetSession_NilSession", func(t *testing.T) {
		// Test SetSession with nil session (should handle gracefully)
		sessionID := "test-session-id-nil"
		sessionStore.SetSession(sessionID, nil)

		// Verify that nothing was stored (or it was handled gracefully)
		retrievedSession, found := sessionStore.GetSession(sessionID)
		assert.False(t, found, "Nil session should not be stored")
		assert.Nil(t, retrievedSession, "Retrieved session should be nil")
	})

	t.Run("ClearSession_Success", func(t *testing.T) {
		// Create test session data
		testSession := &domain.Session{
			UserID: "test-user-456",
			OAuthToken: domain.FromOAuth2Token(&oauth2.Token{
				AccessToken: "test-access-token-456",
				TokenType:   "Bearer",
			}),
			UserPermissions: &domain.UserPermissions{
				UserID:      "test-user-456",
				Permissions: []authorizer.Permission{},
			},
		}

		sessionID := "test-session-id-clear"

		// Set the session
		sessionStore.SetSession(sessionID, testSession)

		// Verify it exists
		retrievedSession, found := sessionStore.GetSession(sessionID)
		assert.True(t, found, "Session should exist before clearing")
		assert.NotNil(t, retrievedSession, "Retrieved session should not be nil")

		// Clear the session
		sessionStore.ClearSession(sessionID)

		// Verify it's gone
		retrievedSession, found = sessionStore.GetSession(sessionID)
		assert.False(t, found, "Session should not be found after clearing")
		assert.Nil(t, retrievedSession, "Retrieved session should be nil after clearing")
	})

	t.Run("ClearSession_NonExistent", func(t *testing.T) {
		// Test clearing a non-existent session (should handle gracefully)
		sessionID := "non-existent-session-to-clear"
		sessionStore.ClearSession(sessionID)

		// Verify it's still not found
		retrievedSession, found := sessionStore.GetSession(sessionID)
		assert.False(t, found, "Non-existent session should still not be found")
		assert.Nil(t, retrievedSession, "Retrieved session should be nil")
	})

	t.Run("MultipleSessions_Isolation", func(t *testing.T) {
		// Test that multiple sessions are isolated from each other
		session1 := &domain.Session{
			UserID: "user-1",
			OAuthToken: domain.FromOAuth2Token(&oauth2.Token{
				AccessToken: "token-1",
			}),
			UserPermissions: &domain.UserPermissions{
				UserID:      "user-1",
				Permissions: []authorizer.Permission{},
			},
		}

		session2 := &domain.Session{
			UserID: "user-2",
			OAuthToken: domain.FromOAuth2Token(&oauth2.Token{
				AccessToken: "token-2",
			}),
			UserPermissions: &domain.UserPermissions{
				UserID:      "user-2",
				Permissions: []authorizer.Permission{},
			},
		}

		sessionID1 := "session-id-1"
		sessionID2 := "session-id-2"

		// Set both sessions
		sessionStore.SetSession(sessionID1, session1)
		sessionStore.SetSession(sessionID2, session2)

		// Verify both sessions exist independently
		retrieved1, found1 := sessionStore.GetSession(sessionID1)
		retrieved2, found2 := sessionStore.GetSession(sessionID2)

		assert.True(t, found1, "Session 1 should be found")
		assert.True(t, found2, "Session 2 should be found")
		assert.Equal(t, "user-1", retrieved1.UserID, "Session 1 should have correct user ID")
		assert.Equal(t, "user-2", retrieved2.UserID, "Session 2 should have correct user ID")

		// Clear one session and verify the other remains
		sessionStore.ClearSession(sessionID1)

		_, found1 = sessionStore.GetSession(sessionID1)
		retrieved2, found2 = sessionStore.GetSession(sessionID2)

		assert.False(t, found1, "Session 1 should not be found after clearing")
		assert.True(t, found2, "Session 2 should still be found after clearing session 1")
		assert.Equal(t, "user-2", retrieved2.UserID, "Session 2 should still have correct user ID")
	})

	t.Run("SessionWithComplexPermissions", func(t *testing.T) {
		// Test session with complex permission structure
		complexSession := &domain.Session{
			UserID: "complex-user-123",
			OAuthToken: domain.FromOAuth2Token(&oauth2.Token{
				AccessToken:  "complex-access-token",
				TokenType:    "Bearer",
				RefreshToken: "complex-refresh-token",
				Expiry:       time.Now().Add(24 * time.Hour),
			}),
			UserPermissions: &domain.UserPermissions{
				UserID: "complex-user-123",
				Permissions: []authorizer.Permission{
					{
						Scope:          "organization",
						ScopeID:        "org-456",
						OrganizationID: "org-456",
						Permissions:    []string{"org_view_users", "org_manage_users", "org_delete_users", "org_view_reports"},
					},
					{
						Scope:          "device_group",
						ScopeID:        "dg-789",
						OrganizationID: "org-456",
						Permissions:    []string{"device_group_manage_devices", "device_group_view_devices"},
					},
					{
						Scope:          "location_group",
						ScopeID:        "lg-101",
						OrganizationID: "org-456",
						Permissions:    []string{"location_group_view_locations"},
					},
				},
			},
		}

		sessionID := "complex-session-id"

		// Set the complex session
		sessionStore.SetSession(sessionID, complexSession)

		// Retrieve and verify
		retrievedSession, found := sessionStore.GetSession(sessionID)
		assert.True(t, found, "Complex session should be found")
		assert.NotNil(t, retrievedSession, "Retrieved complex session should not be nil")
		assert.Equal(t, complexSession.UserID, retrievedSession.UserID, "Complex session UserID should match")
		assert.Equal(t, complexSession.OAuthToken.AccessToken, retrievedSession.OAuthToken.AccessToken, "Complex session access token should match")
		assert.Equal(t, complexSession.OAuthToken.RefreshToken, retrievedSession.OAuthToken.RefreshToken, "Complex session refresh token should match")
		assert.Equal(t, complexSession.OAuthToken.TokenType, retrievedSession.OAuthToken.TokenType, "Complex session token type should match")
		assert.Len(t, retrievedSession.UserPermissions.Permissions, 3, "Complex session should have 3 permission groups")

		// Verify each permission group
		for i, expectedPerm := range complexSession.UserPermissions.Permissions {
			assert.Equal(t, expectedPerm.Scope, retrievedSession.UserPermissions.Permissions[i].Scope, "Permission scope should match")
			assert.Equal(t, expectedPerm.ScopeID, retrievedSession.UserPermissions.Permissions[i].ScopeID, "Permission scope ID should match")
			assert.Equal(t, expectedPerm.OrganizationID, retrievedSession.UserPermissions.Permissions[i].OrganizationID, "Permission organization ID should match")
			assert.Equal(t, expectedPerm.Permissions, retrievedSession.UserPermissions.Permissions[i].Permissions, "Permission list should match")
		}
	})

	// Clean up after all tests
	t.Cleanup(func() {
		cleanupTestData(t, redisClient)
	})
}

// setupRedisConnection connects to Redis using MEMORYSTORE_HOST and MEMORYSTORE_PORT environment variables
func setupRedisConnection(t *testing.T) (*redisclient.Client, func()) {
	// Get Redis connection details from environment variables
	redisHost := os.Getenv("MEMORYSTORE_HOST")
	if redisHost == "" {
		redisHost = "redis" // Default fallback
	}

	redisPort := os.Getenv("MEMORYSTORE_PORT")
	if redisPort == "" {
		redisPort = "6379" // Default fallback
	}

	redisAddr := fmt.Sprintf("%s:%s", redisHost, redisPort)
	logger.Infof("Connecting to Redis at %s", redisAddr)

	// Create Redis client
	redisClient := redisclient.NewClient(&redisclient.Options{
		Addr:     redisAddr,
		Password: "", // No password
		DB:       0,  // Default database
	})

	// Test connection with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := redisClient.Ping(ctx).Result()
	require.NoError(t, err, "Failed to connect to Redis")

	logger.Infof("Successfully connected to Redis at %s", redisAddr)

	return redisClient, func() {
		redisClient.Close()
	}
}

// cleanupTestData removes any test data from Redis
func cleanupTestData(t *testing.T, redisClient *redisclient.Client) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Get all keys with the test prefix
	pattern := session.RedisSessionKeyPrefix + "test-*"
	keys, err := redisClient.Keys(ctx, pattern).Result()
	if err != nil {
		logger.Warnf("Failed to get keys for cleanup: %v", err)
		return
	}

	// Delete all test keys
	if len(keys) > 0 {
		_, err = redisClient.Del(ctx, keys...).Result()
		if err != nil {
			logger.Warnf("Failed to delete test keys: %v", err)
		} else {
			logger.Debugf("Cleaned up %d test session keys", len(keys))
		}
	}
}
