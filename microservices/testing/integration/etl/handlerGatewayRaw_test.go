package etl

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
	"synapse-its.com/testing/utils"
)

// This tests the gateway/raw endpoint.
func TestGatewayIngestEndpoint_rmsData(t *testing.T) {
	var (
		gatewayDeviceID = "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f"
		messageType     = "rmsData"
		apiKey          = "qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd"
		tz              = "America/Chicago"
	)

	assert := assert.New(t)

	const messageVersion = "v1"

	// The base64 encoded protobuf
	const base64payload = `Co8BCiRmZGYxNjRlNi00ODk5LTQ3MjUtYmZhZS01MTQxZDc4ODg3ZDQSZ5M3AwFZFgEAdTwAAAAAACIAAAAAX90XWRQTCSOJgQEAAXR1dQByAHN0dHR0cnMJc3JyCXN1dQAAAAAAAAcHBwcHBwAABwd1dQcHBwcAAAcHBwdzBwAAdAcAAAAAAAAAAAAAAADBABUKkgEKJDNmYmU2MDE4LTllZWYtNGJiNi1hYjljLTAzNjYzY2M3YjNiYRJqkzgLAXUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//8AAII8EHiJKVUZAwQkAHgJCQkICQkJCQkJCQgJCgkJCQgJCAQEBAUJCQkICAkJCXh3d3h3d3d3eHd3eHd4d3cAAAAA/gAAAAAAMQrHAgokNDI2YjA5MTktODEyYi00OTBkLThiNDgtYjBhYzc4OGI5NzEyEp4Ck0EzUiIAAAAAAAAAM/UAAAAAAACICgAAAAAAAAAAAAAAAAAA+wB5eXh4eXp5eXoAAAAAAAAAAHl5AQF4eAEBeQF5AXl5eXoAAAAAAAAAAAAAAAAAAAAAAgIBAQICAQEAAAAAAgICAQAAAAAAAAAAAAAAAAAAAAACAgF4AgIBeQF5AXkCAQIBAAAAAAAAAAAAAAAAAgICAgEBAAABAQAAAQABAAEBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAQABAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAgxIAIEJF5ffgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8AegAAeg==`

	payload, _ := base64.StdEncoding.DecodeString(base64payload)

	// Wait for everything to be up and running.
	connections := connect.NewConnections(t.Context())
	bq := connections.Bigquery
	assert.NoError(utils.AwaitBroker(t.Context(), 30*time.Second))

	// Verify that there are no existing records in the database.
	query := fmt.Sprintf(`
		SELECT *
		FROM {{%s}}
		WHERE (Data = CAST($1 AS BYTES) OR Data = FROM_BASE64($2)) AND topic LIKE $3`, schemas.T_EdiRawMessages)
	rows := []schemas.EdiRawMessages{}
	err := bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(0, len(rows), "Expected 0 rows in BigQuery")

	// The URL under test
	url := "http://broker:8080/api/v3/gateway/ingest"

	reqBody := bytes.NewReader(payload)

	// Build the request
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		t.Fatalf("creating request: %v", err)
	}

	// Set headers exactly as in your curl
	req.Header.Set("Content-Type", "application/x-protobuf")
	req.Header.Set("gateway-device-id", gatewayDeviceID)
	req.Header.Set("message-version", messageVersion)
	req.Header.Set("message-type", messageType)
	req.Header.Set("x-api-key", apiKey)
	req.Header.Set("tz", tz)

	// Issue the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("making POST to %s: %v", url, err)
	}
	defer resp.Body.Close()

	// Read body for debugging if needed
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Verify status code
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("expected 200 OK, got %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Wait 5 seconds for the message to be processed.
	time.Sleep(10 * time.Second)

	// Verify that the database record was inserted.
	rows = []schemas.EdiRawMessages{}
	err = bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal("broker-gateway-rmsData", rows[0].Topic, "Topic should be correct")
	assert.NotEmpty(rows[0].Data, "Data should not be empty")
	assert.NotEmpty(rows[0].PubsubTimestamp, "PubsubTimestamp should not be empty")
	assert.NotEmpty(rows[0].PubsubID, "PubsubID should not be empty")
}

// This tests the gateway/raw endpoint for faultNotifications
func TestGatewayIngestEndpoint_faultNotification(t *testing.T) {
	var (
		gatewayDeviceID = "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f"
		messageType     = "faultNotification"
		apiKey          = "qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd"
		tz              = "America/Chicago"
		alertTopic      = pubsubdata.TopicETLNotifications
	)
	assert := assert.New(t)

	const messageVersion = "v1"

	// The base64 encoded protobuf
	const base64payload = `Co8BCiRmZGYxNjRlNi00ODk5LTQ3MjUtYmZhZS01MTQxZDc4ODg3ZDQSZ5M3AwFZFgEBdTwAAAAAACIAAAAAX90XWRQTCSOJgQEAAXR1dQByAHN0dHR0cnMJc3JyCXN1dQAAAAAAAAcHBwcHBwAABwd1dQcHBwcAAAcHBwdzBwAAdAcAAAAAAAAAAAAAAADBABQKkgEKJDNmYmU2MDE4LTllZWYtNGJiNi1hYjljLTAzNjYzY2M3YjNiYRJqkzgLAXUAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//8AAII8EHiJKVUZAwQkAHgJCQkICQkJCQkJCQgJCgkJCQgJCAQEBAUJCQkICAkJCXh3d3h3d3d3eHd3eHd4d3cAAAAA/gAAAAAALgrHAgokNDI2YjA5MTktODEyYi00OTBkLThiNDgtYjBhYzc4OGI5NzEyEp4Ck0EzUiIAAAUAAAAAM/UAAAAAAACICgAAAAAAAAAAAAAAAAAA+wB5eXh4eXp5eXoAAAAAAAAAAHl5AQF4eAEBeQF5AXl5eXoAAAAAAAAAAAAAAAAAAAAAAgIBAQICAQEAAAAAAgICAQAAAAAAAAAAAAAAAAAAAAACAgF4AgIBeQF5AXkCAQIBAAAAAAAAAAAAAAAAAgICAgEBAAABAQAAAQABAAEBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAQABAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAgxIAIEJF5ffgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8AegAAdQ==`

	payload, _ := base64.StdEncoding.DecodeString(base64payload)

	// Wait for everything to be up and running.
	connections := connect.NewConnections(t.Context())
	bq := connections.Bigquery
	assert.NoError(utils.AwaitBroker(t.Context(), 30*time.Second))

	// Verify that there are no existing records in the database.
	query := fmt.Sprintf(`
		SELECT *
		FROM {{%s}}
		WHERE (Data = CAST($1 AS BYTES) OR Data = FROM_BASE64($2)) AND topic LIKE $3`, schemas.T_EdiRawMessages)
	rows := []schemas.EdiRawMessages{}
	err := bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(0, len(rows), "Expected 0 rows in BigQuery")

	// Clean up the database of Notification Messages.
	queryNotificationMessages := fmt.Sprintf(`
		DELETE FROM {{%s}}
		WHERE topic = $1`, schemas.T_NotificationMessages)
	_, err = bq.Exec(queryNotificationMessages, alertTopic)
	assert.NoError(err, "Error deleting Notification Messages")

	// Verify that the fault notification is not in the database.
	queryNotificationMessages = fmt.Sprintf(`
		SELECT *
		FROM {{%s}}
		WHERE topic = $1`, schemas.T_NotificationMessages)
	rowsNotificationMessages := []schemas.NotificationMessages{}
	err = bq.QueryGenericSlice(&rowsNotificationMessages, queryNotificationMessages, alertTopic)
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(0, len(rowsNotificationMessages), "Expected 0 rows in BigQuery")

	// The URL under test
	url := "http://broker:8080/api/v3/gateway/ingest"

	reqBody := bytes.NewReader(payload)

	// Build the request
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		t.Fatalf("creating request: %v", err)
	}

	// Set headers exactly as in your curl
	req.Header.Set("Content-Type", "application/x-protobuf")
	req.Header.Set("gateway-device-id", gatewayDeviceID)
	req.Header.Set("message-version", messageVersion)
	req.Header.Set("message-type", messageType)
	req.Header.Set("x-api-key", apiKey)
	req.Header.Set("tz", tz)

	// Issue the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("making POST to %s: %v", url, err)
	}
	defer resp.Body.Close()

	// Read body for debugging if needed
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Verify status code
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("expected 200 OK, got %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Wait 5 seconds for the message to be processed.
	time.Sleep(10 * time.Second)

	// Verify that the database record was inserted.
	rows = []schemas.EdiRawMessages{}
	err = bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(1, len(rows), "Expected 1 row in BigQuery")

	// Validate that the data is populated/correct.
	assert.NotEmpty(rows[0].OrganizationIdentifier, "OrganizationIdentifier should not be empty")
	assert.Equal(gatewayDeviceID, rows[0].SoftwareGatewayIdentifier, "SoftwareGatewayIdentifier should match the header")
	assert.Equal("broker-gateway-faultNotification", rows[0].Topic, "Topic should be correct")
	assert.NotEmpty(rows[0].Data, "Data should not be empty")
	assert.NotEmpty(rows[0].PubsubTimestamp, "PubsubTimestamp should not be empty")
	assert.NotEmpty(rows[0].PubsubID, "PubsubID should not be empty")

	// Verify that Notification Messages are in the database.
	// The test protobuf data contains 3 devices that will trigger fault notifications.
	// When processed by the device parsers, these devices are detected as faulted and notifications are created.
	rowsNotificationMessages = []schemas.NotificationMessages{}
	err = bq.QueryGenericSlice(&rowsNotificationMessages, queryNotificationMessages, alertTopic)
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(3, len(rowsNotificationMessages), "Expected 3 notification messages in BigQuery")
	assert.Equal("sms", rowsNotificationMessages[0].NotificationType, "NotificationType should be sms")
	assert.Equal(alertTopic, rowsNotificationMessages[0].Topic, "Topic should be correct")
	assert.NotEmpty(rowsNotificationMessages[0].Payload, "Payload should not be empty")
	assert.NotEmpty(rowsNotificationMessages[0].Metadata, "Metadata should not be empty")
}
