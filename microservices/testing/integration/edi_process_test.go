package integration

// TODO: Add tests for the EDI process when we have data to test with

// import (
// 	"encoding/base64"
// 	"testing"

// 	"github.com/stretchr/testify/require"
// 	"synapse-its.com/shared/devices"
// 	"synapse-its.com/shared/pubsubdata"
// )

// // TestProcessLogFaultSignalSequence verifies that the ProcessLogFaultSignalSequence function
// // correctly decodes a Base64 payload and parses it without error.
// func TestProcessLogFaultSignalSequence(t *testing.T) {
// 	const rawB64 = "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"

// 	// Decode Base64 into bytes
// 	byteMsg, err := base64.StdEncoding.DecodeString(rawB64)
// 	require.NoError(t, err, "Base64 decoding should succeed")

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	records, hdr, err := devices.ProcessLogFaultSignalSequence(header, byteMsg)
// 	require.NoError(t, err, "ProcessLogFaultSignalSequence should not return an error")
// 	require.NotNil(t, hdr, "headerDetails should not be nil")
// 	require.NotNil(t, records, "records should not be nil")
// }

// func TestProcessLogConfiguration(t *testing.T) {
// 	const rawB64 = "lTkzARkAAAEgGAIAAKYAAAAjAACwAgAAAgAAKAAAIAAAoAAAIAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD/DwAAAAAAAAAAAAAAAAAAAAAAAP8AAAD/AAAA/w8AAP8AAAD/DwAAAAAAAAAAAAAAAAAAAAAAABQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFP8PAAD/AAAA/w8AAAwAAAAAAAAAAAAAAAAA/w8AAAATkyZDCBMFJaCUCAkKCxgZAQMFBxESAgQGCBUW5w=="

// 	// Decode Base64 into bytes
// 	byteMsg, err := base64.StdEncoding.DecodeString(rawB64)
// 	require.NoError(t, err, "Base64 decoding should succeed")

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "faultLogs",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	records, hdr, err := devices.ProcessLogConfiguration(header, byteMsg)
// 	require.NoError(t, err, "TestProcessLogConfiguration should not return an error")
// 	require.NotNil(t, hdr, "headerDetails should not be nil")
// 	require.NotNil(t, records, "records should not be nil")
// }

// func TestProcessRMSData(t *testing.T) {
// 	const rawB64 = "kzkzARkAAAAAAAAAuw8AAAAAAABEAAAAAAAAAAAAAAAAAAAA9wB6enl6eXp6AAAAAAAAAAAAAHp6AHl6egB5enp6egAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHkAAAB5AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB8fAB8gHwAeKSo1KgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAfAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADdAFxYFJV4AersPAAAAAAAAQAAAAAAAAAAAAAAAAAAAAD8AevsA6Q=="

// 	// Decode Base64 into bytes
// 	byteMsg, err := base64.StdEncoding.DecodeString(rawB64)
// 	require.NoError(t, err, "Base64 decoding should succeed")

// 	// Manually construct the HTTP header for the test
// 	header := &pubsubdata.HeaderDetails{
// 		Host:            "",
// 		UserAgent:       "Go-http-client/2.0",
// 		ContentLength:   "2452",
// 		ContentType:     "application/x-protobuf",
// 		GatewayDeviceID: "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
// 		MessageVersion:  "v1",
// 		MessageType:     "RMSData",
// 		GatewayTimezone: "America/Chicago",
// 	}
// 	// Invoke the function under test
// 	records, hdr, err := devices.ProcessRmsData(header, byteMsg)
// 	require.NoError(t, err, "TestProcessRMSData should not return an error")
// 	require.NotNil(t, hdr, "headerDetails should not be nil")
// 	require.NotNil(t, records, "records should not be nil")
// }
