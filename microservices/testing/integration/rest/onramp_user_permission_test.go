package rest

import (
	"encoding/json"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/testing/utils"
)

// LoginResponse represents the response from the login endpoint
type LoginResponse struct {
	Code    int         `json:"code"`
	Data    interface{} `json:"data"`
	Message string      `json:"message"`
	Status  string      `json:"status"`
}

func Test_OnrampUserPermission_Success(t *testing.T) {
	t.Parallel()
	assert := assert.New(t)
	ctx := t.Context()

	// Wait for onramp service to be ready
	assert.NoError(utils.AwaitOnramp(ctx, 30*time.Second), "onramp service should be ready")

	// Perform basic auth login to set session
	sessionCookie := utils.PerformBasicAuthLogin(t, "<EMAIL>", "puppies1234")
	assert.NotNil(sessionCookie, "Should have received a session cookie")

	// Set up database connections
	connections := connect.NewConnections(ctx)
	pg := connections.Postgres

	// Get user permissions for the specified user ID
	userPermissions, err := authorizer.GetUserPermissions(pg, "063f2596-9dea-57f8-84aa-5d693c59f4e4")
	logger.Debugf("userPermissions UserID: %+v", userPermissions.UserID)
	logger.Debugf("userPermissions: %+v", userPermissions)
	assert.NoError(err)
	assert.NotNil(userPermissions)
	assert.Equal(userPermissions.UserID, "063f2596-9dea-57f8-84aa-5d693c59f4e4")

	// Validate that we have at least one permission
	assert.Greater(len(userPermissions.Permissions), 0, "User should have at least one permission")

	// Log the actual permissions for debugging
	for i, perm := range userPermissions.Permissions {
		logger.Debugf("Permission %d: Scope=%s, ScopeID=%s, OrgID=%s, Permissions=%v",
			i, perm.Scope, perm.ScopeID, perm.OrganizationID, perm.Permissions)
	}

	// Create HTTP client
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Create request to the permissions endpoint
	req, err := http.NewRequest("GET", "http://onramp:8080/api/user/permissions", nil)
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}

	// Add the session cookie to the request
	req.AddCookie(sessionCookie)

	// Perform the request
	resp, err := client.Do(req)
	assert.NoError(err, "Should successfully make request to permissions endpoint")
	defer resp.Body.Close()

	// Check response status
	assert.Equal(http.StatusOK, resp.StatusCode, "Permissions endpoint should return 200 OK")

	// Parse and validate the response
	var permissionsResponse struct {
		Code    int         `json:"code"`
		Data    interface{} `json:"data"`
		Message string      `json:"message"`
		Status  string      `json:"status"`
	}

	// Read and parse response body
	body, err := io.ReadAll(resp.Body)
	assert.NoError(err, "Should be able to read response body")

	err = json.Unmarshal(body, &permissionsResponse)
	assert.NoError(err, "Should be able to parse JSON response")

	// Validate response structure
	assert.Equal(http.StatusOK, permissionsResponse.Code, "Response code should be 200")
	assert.Equal("success", permissionsResponse.Status, "Response status should be 'success'")
	assert.NotNil(permissionsResponse.Data, "Response should contain data")

	logger.Debugf("Permissions API response: %+v", permissionsResponse)
}
