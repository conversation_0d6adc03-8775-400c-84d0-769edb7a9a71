package shared

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/api/iterator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"synapse-its.com/shared/connect"
)

func TestFirestoreIntegration_AllInterfaces(t *testing.T) {
	if os.Getenv("FIRESTORE_EMULATOR_HOST") == "" {
		t.Skip("Skipping test: FIRESTORE_EMULATOR_HOST not set")
	}
	enc := os.Getenv("FIRESTORE_AUTH_ENCRYPTED")
	if enc == "" {
		t.Skip("Skipping integration test: FIRESTORE_AUTH_ENCRYPTED not set")
	}

	ctx := context.Background()

	client, err := connect.Firestore(ctx)
	assert.NoError(t, err, "connect.Firestore() failed")
	defer client.Close()

	assert.Equal(t, enc, client.EncryptedCreds(), "EncryptedCreds should match env var")

	const (
		colName = "it-all"
		docID1  = "one"
		docID2  = "two"
	)

	col := client.Collection(colName)
	doc1 := col.Doc(docID1)
	doc2 := col.Doc(docID2)

	// Set data
	data1 := map[string]any{"a": 1, "b": "first"}
	data2 := map[string]any{"a": 2, "b": "second"}

	_, err = doc1.Set(ctx, data1)
	assert.NoError(t, err, "Set doc1 failed")
	_, err = doc2.Set(ctx, data2)
	assert.NoError(t, err, "Set doc2 failed")

	// Get data
	snap1, err := doc1.Get(ctx)
	assert.NoError(t, err, "Get doc1 failed")
	assert.True(t, snap1.Exists())
	assert.Equal(t, data1["b"], snap1.Data()["b"])

	// Iterator test
	iter := col.Documents(ctx)
	defer iter.Stop()

	seen := map[string]bool{}
	for {
		doc, err := iter.Next()
		if err != nil {
			if status.Code(err) == codes.NotFound || err == iterator.Done {
				break
			}
			assert.NoError(t, err, "Iterator error")
			break
		}

		assert.True(t, doc.Exists(), "iterated doc should exist")
		id := doc.ID()
		seen[id] = true

		d := doc.Data()
		assert.Contains(t, d, "a")
		assert.Contains(t, d, "b")
	}

	assert.True(t, seen[docID1], "docID1 should have been iterated")
	assert.True(t, seen[docID2], "docID2 should have been iterated")

	// Bulk delete both docs
	bw := client.BulkWriter(ctx)
	assert.NoError(t, bw.Delete(doc1))
	assert.NoError(t, bw.Delete(doc2))
	assert.NoError(t, bw.Flush())

	// Verify deleted
	for _, d := range []connect.DocumentRefInterface{doc1, doc2} {
		snap, err := d.Get(ctx)
		if err == nil {
			assert.False(t, snap.Exists(), "Expected Exists() == false after delete")
		} else {
			assert.Equal(t, codes.NotFound, status.Code(err), "Expected NotFound after delete")
		}
	}
}
