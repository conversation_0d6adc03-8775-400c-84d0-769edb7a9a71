package shared

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	Util "synapse-its.com/shared/util"
	Utils "synapse-its.com/testing/utils"
)

const (
	namespace     = "BIGQUERY_BATCH_TEST"
	maxAttempts   = 10
	baseDelaySecs = 2
)

var (
	bqInitOnce sync.Once
	bqExecutor *connect.BigQueryExecutor
	bqInitErr  error
)

// BigQuery creates the dataset (if it doesn't already exist) and ensures it is
// managed under schema_mgmt control.  It then applies schema migrations to
// upgrade the dataset to the highest available version up to the specified
// version.  If the provided version is an empty string, the dataset is
// upgraded to the latest available schema.
// NOTE: we are using a background context because, if we used the test
// context, then the context would be cancelled when the test finishes, which,
// in turn, would cancel the BigQuery client and cause all other tests to fail.
func awaitBigQuerySetup() (*connect.BigQueryExecutor, error) {
	bqInitOnce.Do(func() {
		// 1) Create client
		bqExecutor, bqInitErr = connect.BigQuery(
			context.Background(),
			&connect.DatabaseConfig{
				Namespace: namespace,
			},
			nil,
		)
		if bqInitErr != nil {
			return
		}

		// 2) Apply migrations with retries
		var err error
		for attempt := 1; attempt <= maxAttempts; attempt++ {
			err = Utils.SetupBigQuery(bqExecutor, "data-core-bq", "")
			if err == nil {
				break
			}
			if attempt < maxAttempts {
				time.Sleep(time.Duration(baseDelaySecs*attempt) * time.Second)
			}
		}

		// 3) If the final attempt still failed, record it
		if err != nil {
			bqInitErr = err
			bqExecutor = nil
		}
	})

	// Everyone else blocks in .Do until the above completes,
	// and then simply returns the cached result.
	return bqExecutor, bqInitErr
}

// This test validates that the Add() method of the Batcher
// works as expected when the Batcher is shut down.
func TestAddBehavior_Shutdown_Success(t *testing.T) {
	assert := assert.New(t)

	bq, err := awaitBigQuerySetup()
	assert.NoError(err, "connect to BigQuery")

	b := bqbatch.New(bq, nil)
	// Register our BatchTest type
	b.Register(bqbatch.BatchTest{}, bqbatch.T_BQBATCH, bqbatch.QueueConfig{
		MaxSize:       1000,
		FlushInterval: 5000 * time.Second,
	})

	// Generate random key and value for the test.
	key := Util.RandomString(10)
	value := Util.RandomString(10)

	// Enqueue one row (will not flush until Shutdown())
	err = b.Add(bqbatch.BatchTest{
		Key:   key,
		Value: value,
		Obj: &bqbatch.BatchTestObj{
			Flag1: true,
			Flag2: false,
		},
		Arr: nil,
	})
	assert.NoError(err, "Add()")

	// query immediately --> should see 0 rows
	query := fmt.Sprintf(`
	  SELECT key, value, obj, arr
		FROM {{%s}}
		WHERE key = $1 AND value = $2`, bqbatch.T_BQBATCH)

	rows := []bqbatch.BatchTest{}
	err = bq.QueryGenericSlice(&rows, query, key, value)
	assert.NoError(err, "Query() after Add()")
	assert.Len(rows, 0, "expected no rows before Shutdown()")

	// now shut down --> this will flush
	err = b.Shutdown()
	assert.NoError(err, "Shutdown()")

	// query again --> should see exactly 1
	rows = []bqbatch.BatchTest{}
	err = bq.QueryGenericSlice(&rows, query, key, value)
	assert.NoError(err, "Query() after Shutdown()")
	assert.Len(rows, 1, "expected 1 row after Shutdown()")

	// verify the contents
	row := rows[0]
	assert.Equal(key, row.Key)
	assert.Equal(value, row.Value)
	assert.True(row.Obj.Flag1)
	assert.False(row.Obj.Flag2)
}

// This test simply validates that the Add() method of the Batcher
// works as expected in respect to whether or not the Batcher has been
// shut down, as well as for unknown types.
// It does not validate that the data is actually inserted into BigQuery.
func TestAddBehavior_Shutdown(t *testing.T) {
	assert := assert.New(t)

	bq, err := awaitBigQuerySetup()
	assert.NoError(err, "connect to BigQuery")

	b := bqbatch.New(bq, nil)
	// register only BatchTest
	b.Register(bqbatch.BatchTest{}, bqbatch.T_BQBATCH, bqbatch.QueueConfig{
		MaxSize:       1000,
		FlushInterval: 5000 * time.Second,
	})

	// an unregistered type
	type Foo struct{}

	tests := []struct {
		name      string
		row       interface{}
		shutdown  bool
		wantError bool
		wantErr   error
	}{
		{
			name:      "unknown type before shutdown",
			row:       Foo{},
			shutdown:  false,
			wantError: true,
			wantErr:   bqbatch.ErrUnknownType,
		},
		{
			name:      "registered type after shutdown",
			row:       bqbatch.BatchTest{},
			shutdown:  true,
			wantError: true,
			wantErr:   bqbatch.ErrBatcherIsShutDown,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// optionally shut down
			if tc.shutdown {
				if err := b.Shutdown(); err != nil {
					t.Fatalf("Shutdown() = %v; want nil", err)
				}
			}

			err := b.Add(tc.row)
			if tc.wantError {
				if err == nil {
					t.Fatalf("Add(%T) = nil; want error %v", tc.row, tc.wantErr)
				}
				if !errors.Is(err, tc.wantErr) {
					t.Errorf("Add(%T) error = %v; want %v", tc.row, err, tc.wantErr)
				}
			} else {
				if err != nil {
					t.Fatalf("Add(%T) = %v; want nil", tc.row, err)
				}
			}
		})
	}
}
