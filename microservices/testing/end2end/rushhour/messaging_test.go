/*
RushHour E2E Messaging Tests

This file contains end-to-end tests for the core messaging functionality of the rushhour microservice.
These tests validate the complete bidirectional communication flow between Field Service Applications (FSA)
and Gateway devices through the rushhour Socket.IO server.

Test Coverage:
- FSA<->Gateway bidirectional messaging through rushhour
- JWT authentication for FSA clients (via broker)
- Machine key/API key authentication for Gateway clients
- Message envelope enrichment (SessionId, UserId, OrganizationId)
- Session-based 1:1 message routing
- Request/response correlation and round-trip validation
- Real-time Socket.IO v4 connectivity over HTTP polling

Key Business Logic Validated:
1. Authentication: Both FSA (JWT) and Gateway (machine_key/api_key) authenticate successfully
2. Message Routing: Commands from FSA are properly routed to the correct Gateway
3. Envelope Enrichment: RushHour enriches messages with session metadata for routing
4. Response Routing: Gateway responses are routed back to the originating FSA using SessionId
5. Data Integrity: All message metadata (RequestId, DeviceId, OrganizationId) is preserved

Architecture Under Test:
FSA Client -> RushHour (Socket.IO Server) -> Gateway Client

	^                                               |
	<----------- Response Routing <-----------------

Dependencies:
- Broker service (for JWT authentication)
- RushHour service (Socket.IO server with Redis adapter)
- PostgreSQL (for authentication validation)
- Redis (for horizontal scaling and session management)
*/
package end2end

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/zishang520/engine.io-client-go/transports"
	"github.com/zishang520/engine.io/v2/types"
	"github.com/zishang520/socket.io-client-go/socket"

	rushhourv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/go/rushhour/v1"
	Utils "synapse-its.com/testing/utils"
)

// Type aliases for easier reference in tests
type SocketEnvelope = rushhourv1.SocketEnvelope

// Test credentials from DEV database
const (
	testUsername   = "<EMAIL>"
	testPassword   = "puppies1234"
	testAPIKey     = "qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd"
	testMachineKey = "localgateway"
	testDeviceID   = "87d94e14-e804-58b3-9f8c-a02e5de90aeb"
)

// TestRushHourMessaging tests the complete FSA->Gateway->FSA flow through rushhour
//
// This end-to-end test validates the complete message routing through rushhour:
// 1. Gateway authenticates with machine_key/api_key
// 2. FSA authenticates with JWT token from broker
// 3. FSA sends device_request with JSON envelope
// 4. RushHour enriches envelope with SessionId and routes to gateway
// 5. Gateway processes command and sends device_message response
// 6. RushHour routes response back to FSA using SessionId for 1:1 delivery
//
// SIMPLIFIED VERSION: Sequential connection to avoid race conditions
func TestRushHourMessaging(t *testing.T) {
	// Wait for services to be ready
	ctx := context.Background()
	Utils.AwaitBroker(ctx, time.Second)
	err := Utils.AwaitRushHour(ctx, time.Second)
	require.NoError(t, err, "RushHour service must be available for e2e testing")

	t.Run("Complete E2E FSA->Gateway->FSA Flow", func(t *testing.T) {
		// Step 1: Get JWT token for FSA authentication
		jwtToken := Utils.PerformJWTAuthentication(t, testUsername, testPassword)
		require.NotEmpty(t, jwtToken, "JWT token should not be empty")
		t.Logf("Obtained JWT token: %s...", jwtToken[:20])

		// Step 2: Connect Gateway first (most reliable)
		t.Log("Connecting Gateway client...")
		gatewayClient, gatewayCommands, err := connectGatewayClientSimple(t)
		require.NoError(t, err, "Gateway should connect successfully")
		defer gatewayClient.Disconnect()

		// Step 3: Connect FSA client (after gateway is established)
		// Add small delay to prevent race conditions between connections
		time.Sleep(100 * time.Millisecond)
		t.Log("Connecting FSA client...")
		fsaClient, fsaResponses, err := connectFSAClientSimple(t, jwtToken)
		require.NoError(t, err, "FSA should connect successfully")
		defer fsaClient.Disconnect()

		// Step 4: Send command from FSA to Gateway
		t.Log("Sending command from FSA to Gateway...")
		commandPayload, _ := json.Marshal(map[string]interface{}{
			"action": "get_status",
			"params": map[string]interface{}{"timeout": 30},
		})

		command := &SocketEnvelope{
			Type:      rushhourv1.EnvelopeType_ENVELOPE_COMMAND_JSON,
			RequestId: 12345,
			DeviceId:  testDeviceID,
			Origin:    rushhourv1.OriginType_ORIGIN_FSA,
			Payload:   commandPayload,
		}

		fsaClient.Emit("device_request", command)

		// Step 5: Wait for gateway to receive command
		t.Log("Waiting for gateway to receive command...")
		var receivedCommand *SocketEnvelope
		select {
		case receivedCommand = <-gatewayCommands:
			t.Log("Gateway received command from FSA")
		case <-time.After(10 * time.Second):
			t.Fatal("Timeout: Gateway did not receive command")
		}

		// Step 6: Validate command envelope
		validateCommandEnvelope(t, receivedCommand)
		validateRushHourEnrichment(t, receivedCommand, "command")

		// Step 7: Wait for FSA to receive response
		t.Log("Waiting for FSA to receive response...")
		var receivedResponse *SocketEnvelope
		select {
		case receivedResponse = <-fsaResponses:
			t.Log("FSA received response from gateway")
		case <-time.After(10 * time.Second):
			t.Fatal("Timeout: FSA did not receive response")
		}

		// Step 8: Validate response envelope
		validateResponseEnvelope(t, receivedResponse, receivedCommand)
		validateSessionIDRouting(t, receivedResponse, receivedCommand)

		t.Log("[OK] End-to-end test PASSED!")
	})
}

// connectGatewayClientSimple creates a simple gateway client with direct event handling
func connectGatewayClientSimple(t *testing.T) (*socket.Socket, <-chan *SocketEnvelope, error) {
	t.Log("Gateway: Connecting to rushhour with zishang520 Socket.IO client...")

	// Create command channel
	commandChan := make(chan *SocketEnvelope, 10)

	// Set up connection options with auth data in the auth object (not query params)
	opts := socket.DefaultOptions()
	// Force polling transport only to avoid WebSocket negotiation race conditions
	opts.SetTransports(types.NewSet(transports.Polling))

	// Add authentication data to the auth object (server expects this)
	authData := map[string]interface{}{
		"machine_key": testMachineKey,
		"api_key":     testAPIKey,
	}
	opts.SetAuth(authData)

	// Create manager and connect to gateway namespace
	manager := socket.NewManager("http://rushhour:8080", opts)
	client := manager.Socket("/auth/gateway", opts)

	// Set up authentication tracking
	authSuccess := make(chan bool, 1)
	connectionSuccess := make(chan bool, 1)

	client.On("connect", func(args ...any) {
		t.Log("Gateway: [OK] Socket.IO connected")
		connectionSuccess <- true
	})

	client.On("gateway_init", func(args ...any) {
		t.Logf("Gateway: [OK] Received gateway_init event: %v", args)
		authSuccess <- true
	})

	client.On("device_request", func(args ...any) {
		if len(args) == 0 {
			return
		}

		envelope, err := parseSocketEnvelopeSimple(args[0])
		if err != nil {
			t.Errorf("Gateway: Failed to parse command: %v", err)
			return
		}

		t.Logf("Gateway: Received command - Type: %v, Device: %s, Session: %s",
			envelope.Type, envelope.DeviceId, envelope.SessionId)

		// Send to command channel
		commandChan <- envelope

		// Auto-respond with success message
		go func() {
			time.Sleep(100 * time.Millisecond) // Brief processing delay

			responsePayload, _ := json.Marshal(map[string]interface{}{
				"status":  "success",
				"message": "Device command executed successfully",
				"data":    "Mock device response from gateway",
			})

			response := &SocketEnvelope{
				Type:           rushhourv1.EnvelopeType_ENVELOPE_COMMAND_JSON,
				RequestId:      envelope.RequestId,
				SessionId:      envelope.SessionId,
				DeviceId:       envelope.DeviceId,
				OrganizationId: envelope.OrganizationId,
				Origin:         rushhourv1.OriginType_ORIGIN_GATEWAY,
				Payload:        responsePayload,
			}

			client.Emit("device_message", response)
			t.Logf("Gateway: Sent response for request %d", envelope.RequestId)
		}()
	})

	client.On("connect_error", func(args ...any) {
		t.Logf("Gateway: Connection error: %v", args)
	})

	// Start the connection
	client.Connect()

	// Wait for connection
	select {
	case <-connectionSuccess:
		t.Log("Gateway: [OK] Basic connection established")
	case <-time.After(15 * time.Second):
		client.Disconnect()
		return nil, nil, fmt.Errorf("gateway connection timeout")
	}

	// Wait for authentication
	select {
	case <-authSuccess:
		t.Log("Gateway: [OK] Authentication successful!")
		return client, commandChan, nil
	case <-time.After(10 * time.Second):
		client.Disconnect()
		return nil, nil, fmt.Errorf("gateway authentication timeout")
	}
}

// connectFSAClientSimple creates a simple FSA client with direct event handling
func connectFSAClientSimple(t *testing.T, jwtToken string) (*socket.Socket, <-chan *SocketEnvelope, error) {
	t.Log("FSA: Connecting to rushhour with zishang520 Socket.IO client...")

	// Retry logic for intermittent connection issues
	var client *socket.Socket
	var responseChan <-chan *SocketEnvelope

	for attempt := 1; attempt <= 2; attempt++ {
		t.Logf("FSA: Connection attempt %d/2", attempt)
		var err error
		client, responseChan, err = attemptFSAConnection(t, jwtToken)
		if err == nil {
			return client, responseChan, nil
		}

		t.Logf("FSA: Attempt %d failed: %v", attempt, err)
		if attempt < 2 {
			time.Sleep(500 * time.Millisecond) // Brief delay before retry
		}
	}

	return nil, nil, fmt.Errorf("FSA connection failed after 2 attempts")
}

// attemptFSAConnection performs a single FSA connection attempt
func attemptFSAConnection(t *testing.T, jwtToken string) (*socket.Socket, <-chan *SocketEnvelope, error) {
	// Create response channel
	responseChan := make(chan *SocketEnvelope, 10)

	// Set up connection options with JWT token in the auth object (server expects this)
	opts := socket.DefaultOptions()
	// Force polling transport only to avoid WebSocket negotiation race conditions
	opts.SetTransports(types.NewSet(transports.Polling))

	// Add JWT token to the auth object (server expects this)
	authData := map[string]interface{}{
		"token": jwtToken,
	}
	opts.SetAuth(authData)

	// Create manager and connect to FSA namespace
	manager := socket.NewManager("http://rushhour:8080", opts)
	client := manager.Socket("/auth/fsa", opts)

	// Set up authentication tracking
	authSuccess := make(chan bool, 1)
	connectionSuccess := make(chan bool, 1)

	client.On("connect", func(args ...any) {
		t.Log("FSA: [OK] Socket.IO connected")
		connectionSuccess <- true
	})

	client.On("auth_success", func(args ...any) {
		t.Logf("FSA: [OK] Received auth_success event: %v", args)
		authSuccess <- true
	})

	client.On("device_message", func(args ...any) {
		if len(args) == 0 {
			return
		}

		envelope, err := parseSocketEnvelopeSimple(args[0])
		if err != nil {
			t.Errorf("FSA: Failed to parse response: %v", err)
			return
		}

		t.Logf("FSA: Received response - Type: %v, Origin: %v, Session: %s, Request: %d",
			envelope.Type, envelope.Origin, envelope.SessionId, envelope.RequestId)

		responseChan <- envelope
	})

	client.On("connect_error", func(args ...any) {
		t.Logf("FSA: Connection error: %v", args)
	})

	// Add more detailed error handling
	client.On("disconnect", func(args ...any) {
		t.Logf("FSA: Disconnected: %v", args)
	})

	// Start the connection with retry logic
	t.Log("FSA: Starting connection attempt...")
	client.Connect()

	// Wait for connection with better debugging
	select {
	case <-connectionSuccess:
		t.Log("FSA: [OK] Basic connection established")
	case <-time.After(15 * time.Second):
		t.Log("FSA: Connection timeout - client never connected to server")
		client.Disconnect()
		return nil, nil, fmt.Errorf("FSA connection timeout")
	}

	// Wait for authentication
	select {
	case <-authSuccess:
		t.Log("FSA: [OK] Authentication successful!")
		return client, responseChan, nil
	case <-time.After(10 * time.Second):
		client.Disconnect()
		return nil, nil, fmt.Errorf("FSA authentication timeout")
	}
}

// parseSocketEnvelopeSimple parses Socket.IO data into SocketEnvelope
func parseSocketEnvelopeSimple(data interface{}) (*SocketEnvelope, error) {
	envelope := &SocketEnvelope{}

	switch v := data.(type) {
	case string:
		if err := json.Unmarshal([]byte(v), envelope); err != nil {
			return nil, fmt.Errorf("failed to parse JSON string: %w", err)
		}
	case map[string]interface{}:
		jsonBytes, err := json.Marshal(v)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal map: %w", err)
		}
		if err := json.Unmarshal(jsonBytes, envelope); err != nil {
			return nil, fmt.Errorf("failed to parse JSON object: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported data type: %T", v)
	}

	return envelope, nil
}

// Validation functions for test assertions

// validateCommandEnvelope validates the command envelope received by gateway
func validateCommandEnvelope(t *testing.T, envelope *SocketEnvelope) {
	assert.Equal(t, rushhourv1.EnvelopeType_ENVELOPE_COMMAND_JSON, envelope.Type, "Command envelope should have JSON type")
	assert.Equal(t, testDeviceID, envelope.DeviceId, "Device ID should match test device")
	assert.NotEmpty(t, envelope.OrganizationId, "Organization ID should be populated by RushHour")
	assert.Equal(t, rushhourv1.OriginType_ORIGIN_FSA, envelope.Origin, "Origin should be FSA")
	assert.NotEmpty(t, envelope.SessionId, "Session ID should be populated by rushhour")
	assert.NotEmpty(t, envelope.Payload, "Command payload should be present")

	// Validate payload structure
	var commandData map[string]interface{}
	require.NoError(t, json.Unmarshal(envelope.Payload, &commandData), "Command payload should be valid JSON")

	action, exists := commandData["action"].(string)
	assert.True(t, exists, "Command payload should contain 'action' field")
	assert.NotEmpty(t, action, "Action field should not be empty")
}

// validateResponseEnvelope validates the response envelope received by FSA
func validateResponseEnvelope(t *testing.T, response, originalCommand *SocketEnvelope) {
	assert.Equal(t, rushhourv1.EnvelopeType_ENVELOPE_COMMAND_JSON, response.Type, "Response envelope should have JSON type")
	assert.Equal(t, originalCommand.DeviceId, response.DeviceId, "Device ID should match original command")
	assert.Equal(t, originalCommand.OrganizationId, response.OrganizationId, "Organization ID should match original command")
	assert.Equal(t, rushhourv1.OriginType_ORIGIN_GATEWAY, response.Origin, "Origin should be Gateway")
	assert.Equal(t, originalCommand.SessionId, response.SessionId, "Session ID should match for 1:1 routing")
	assert.Equal(t, originalCommand.RequestId, response.RequestId, "Request ID should match original command")
	assert.NotEmpty(t, response.Payload, "Response payload should be present")

	// Validate response payload structure
	var responseData map[string]interface{}
	require.NoError(t, json.Unmarshal(response.Payload, &responseData), "Response payload should be valid JSON")

	status, exists := responseData["status"].(string)
	assert.True(t, exists, "Response payload should contain 'status' field")
	assert.Equal(t, "success", status, "Response status should be 'success'")
}

// validateRushHourEnrichment validates that rushhour correctly enriches envelopes with metadata
func validateRushHourEnrichment(t *testing.T, envelope *SocketEnvelope, messageType string) {
	t.Logf("Validating RushHour enrichment for %s:", messageType)

	// RushHour should populate session ID for all messages
	assert.NotEmpty(t, envelope.SessionId, "RushHour must populate SessionId for message routing")
	t.Logf("  SessionId populated: %s", envelope.SessionId)

	// For commands from FSA, RushHour should populate user ID and org ID
	if envelope.Origin == rushhourv1.OriginType_ORIGIN_FSA {
		assert.NotEmpty(t, envelope.UserId, "RushHour must populate UserId for FSA messages")
		assert.NotEmpty(t, envelope.OrganizationId, "RushHour must populate OrganizationId for FSA messages")
		t.Logf("  UserId populated: %s", envelope.UserId)
		t.Logf("  OrganizationId populated: %s", envelope.OrganizationId)
	}

	// Validate that device ID and request ID are preserved
	assert.Equal(t, testDeviceID, envelope.DeviceId, "RushHour must preserve DeviceId")
	assert.NotZero(t, envelope.RequestId, "RushHour must preserve RequestId")
	t.Logf("  DeviceId preserved: %s", envelope.DeviceId)
	t.Logf("  RequestId preserved: %d", envelope.RequestId)
}

// validateSessionIDRouting validates that session ID routing works correctly for 1:1 communication
func validateSessionIDRouting(t *testing.T, response, originalCommand *SocketEnvelope) {
	t.Log("Validating Session ID routing for 1:1 communication:")

	// Critical: Session ID must match exactly for proper routing
	assert.Equal(t, originalCommand.SessionId, response.SessionId,
		"Session ID must match between command and response for 1:1 routing")
	t.Logf("  Command SessionId: %s", originalCommand.SessionId)
	t.Logf("  Response SessionId: %s", response.SessionId)

	// Validate that origin is correctly set by gateway
	assert.Equal(t, rushhourv1.OriginType_ORIGIN_GATEWAY, response.Origin,
		"Response origin must be Gateway")
	t.Logf("  Response origin correctly set to Gateway: %v", response.Origin)

	// Validate bidirectional metadata preservation
	assert.Equal(t, originalCommand.DeviceId, response.DeviceId,
		"DeviceId must be preserved in round-trip")
	assert.Equal(t, originalCommand.OrganizationId, response.OrganizationId,
		"OrganizationId must be preserved in round-trip")
	assert.Equal(t, originalCommand.RequestId, response.RequestId,
		"RequestId must be preserved in round-trip")

	t.Log("  All metadata correctly preserved in round-trip communication")
}
