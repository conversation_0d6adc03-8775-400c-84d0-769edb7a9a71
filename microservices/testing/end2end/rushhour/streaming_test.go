/*
RushHour E2E Streaming & Recovery Tests

This file contains end-to-end tests for the streaming functionality and connection resilience
of the rushhour microservice. These tests validate Socket.IO connectivity, authentication flows,
and system recovery capabilities for both FSA and Gateway clients.

Test Coverage:
- Basic Socket.IO connectivity validation
- FSA authentication using JWT tokens (via broker)
- Gateway authentication using machine_key/api_key pairs
- Connection resilience and recovery scenarios
- Socket.IO v4 protocol compatibility
- HTTP polling transport reliability

Test Functions:
1. TestRushHourStreaming:
  - Basic Gateway Connection: Validates gateway can connect and authenticate
  - Basic FSA Connection: Validates FSA can connect and authenticate with JWT

2. TestRushHourGatewayRecovery:
  - GatewayRecoveryResilience: Tests FSA connection stability and recovery

Key Validation Points:
- Socket.IO handshake and connection establishment
- Namespace-based authentication (/auth/gateway, /auth/fsa)
- Authentication event handling (gateway_init, auth_success)
- Connection state management and cleanup
- Service availability and readiness

Technical Implementation:
- Uses github.com/zishang520/socket.io-client-go library for Socket.IO client connections
- Eliminates WebSocket connectivity issues in test environments
- Provides foundation for future streaming data validation
- Validates authentication prerequisites for messaging tests

Dependencies:
- Broker service (for JWT token generation)
- RushHour service (Socket.IO server)
- PostgreSQL (for credential validation)
- Redis (for Socket.IO adapter and session management)
*/
package end2end

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/zishang520/engine.io-client-go/transports"
	"github.com/zishang520/engine.io/v2/types"
	"github.com/zishang520/socket.io-client-go/socket"

	Utils "synapse-its.com/testing/utils"
)

// StreamingData represents a data point in the stream
type StreamingData struct {
	Timestamp  time.Time              `json:"timestamp"`
	SequenceID int                    `json:"sequence_id"`
	DeviceID   string                 `json:"device_id"`
	DataType   string                 `json:"data_type"`
	Payload    map[string]interface{} `json:"payload"`
	StreamID   string                 `json:"stream_id"`
}

// TestRushHourStreaming tests streaming functionality using the new simple client
func TestRushHourStreaming(t *testing.T) {
	// Wait for services to be ready
	ctx := context.Background()
	err := Utils.AwaitRushHour(ctx, time.Second)
	require.NoError(t, err, "RushHour should be ready")

	t.Run("Basic Gateway Connection", func(t *testing.T) {
		t.Log("Testing basic gateway connection for streaming...")

		// Create gateway client with zishang520 library
		opts := socket.DefaultOptions()
		// Force polling transport only to avoid WebSocket negotiation race conditions
		opts.SetTransports(types.NewSet(transports.Polling))

		// Add authentication data to the auth object (server expects this)
		authData := map[string]interface{}{
			"machine_key": testMachineKey,
			"api_key":     testAPIKey,
		}
		opts.SetAuth(authData)

		// Create manager and connect to gateway namespace
		manager := socket.NewManager("http://rushhour:8080", opts)
		client := manager.Socket("/auth/gateway", opts)
		defer client.Disconnect()

		// Set up connection handler
		connected := make(chan bool, 1)
		authenticated := make(chan bool, 1)

		client.On("connect", func(args ...any) {
			t.Log("Gateway: [OK] Connected to Socket.IO")
			connected <- true
		})

		client.On("gateway_init", func(args ...any) {
			t.Logf("Gateway: [OK] Authenticated successfully: %v", args)
			authenticated <- true
		})

		client.On("connect_error", func(args ...any) {
			t.Errorf("Gateway: Connection error: %v", args)
		})

		// Connect
		client.Connect()

		// Wait for connection
		select {
		case <-connected:
			t.Log("[OK] Gateway connection successful")
		case <-time.After(15 * time.Second):
			t.Fatal("[FAIL] Gateway connection timeout")
		}

		// Wait for authentication
		select {
		case <-authenticated:
			t.Log("[OK] Gateway authentication successful")
		case <-time.After(10 * time.Second):
			t.Fatal("[FAIL] Gateway authentication timeout")
		}
	})

	t.Run("Basic FSA Connection", func(t *testing.T) {
		t.Log("Testing basic FSA connection for streaming...")

		// Get JWT token
		jwtToken := Utils.PerformJWTAuthentication(t, testUsername, testPassword)
		require.NotEmpty(t, jwtToken, "Should receive valid JWT token")

		// Create FSA client with zishang520 library
		opts := socket.DefaultOptions()
		// Force polling transport only to avoid WebSocket negotiation race conditions
		opts.SetTransports(types.NewSet(transports.Polling))

		// Add JWT token to the auth object (server expects this)
		authData := map[string]interface{}{
			"token": jwtToken,
		}
		opts.SetAuth(authData)

		// Create manager and connect to FSA namespace
		manager := socket.NewManager("http://rushhour:8080", opts)
		client := manager.Socket("/auth/fsa", opts)
		defer client.Disconnect()

		// Set up connection handler
		connected := make(chan bool, 1)
		authenticated := make(chan bool, 1)

		client.On("connect", func(args ...any) {
			t.Log("FSA: [OK] Connected to Socket.IO")
			connected <- true
		})

		client.On("auth_success", func(args ...any) {
			t.Logf("FSA: [OK] Authenticated successfully: %v", args)
			authenticated <- true
		})

		client.On("connect_error", func(args ...any) {
			t.Errorf("FSA: Connection error: %v", args)
		})

		// Connect
		client.Connect()

		// Wait for connection
		select {
		case <-connected:
			t.Log("[OK] FSA connection successful")
		case <-time.After(15 * time.Second):
			t.Fatal("[FAIL] FSA connection timeout")
		}

		// Wait for authentication
		select {
		case <-authenticated:
			t.Log("[OK] FSA authentication successful")
		case <-time.After(10 * time.Second):
			t.Fatal("[FAIL] FSA authentication timeout")
		}
	})
}

// TestRushHourGatewayRecovery tests gateway recovery functionality
func TestRushHourGatewayRecovery(t *testing.T) {
	// Wait for services to be ready
	ctx := context.Background()
	err := Utils.AwaitRushHour(ctx, time.Second)
	require.NoError(t, err, "RushHour should be ready")

	t.Run("GatewayRecoveryResilience", func(t *testing.T) {
		t.Log("Testing gateway recovery and reconnection...")

		// Get JWT token
		jwtToken := Utils.PerformJWTAuthentication(t, testUsername, testPassword)
		require.NotEmpty(t, jwtToken, "Should receive valid JWT token")

		// Create FSA client with zishang520 library
		opts := socket.DefaultOptions()
		// Force polling transport only to avoid WebSocket negotiation race conditions
		opts.SetTransports(types.NewSet(transports.Polling))

		// Add JWT token to the auth object (server expects this)
		authData := map[string]interface{}{
			"token": jwtToken,
		}
		opts.SetAuth(authData)

		// Create manager and connect to FSA namespace
		manager := socket.NewManager("http://rushhour:8080", opts)
		client := manager.Socket("/auth/fsa", opts)
		defer client.Disconnect()

		// Set up connection handler
		connected := make(chan bool, 1)
		authenticated := make(chan bool, 1)

		client.On("connect", func(args ...any) {
			t.Log("FSA: [OK] Connected to Socket.IO for recovery test")
			connected <- true
		})

		client.On("auth_success", func(args ...any) {
			t.Logf("FSA: [OK] Authenticated successfully: %v", args)
			authenticated <- true
		})

		client.On("connect_error", func(args ...any) {
			t.Errorf("FSA: Connection error: %v", args)
		})

		// Connect
		client.Connect()
		t.Log("FSA: Initiating connection...")

		// Wait for connection
		select {
		case <-connected:
			t.Log("[OK] FSA connection successful")
		case <-time.After(15 * time.Second):
			t.Fatal("[FAIL] FSA connection timeout")
		}

		// Wait for authentication
		select {
		case <-authenticated:
			t.Log("[OK] FSA authentication successful - recovery test passed")
		case <-time.After(10 * time.Second):
			t.Fatal("[FAIL] FSA authentication timeout")
		}
	})
}

// Helper function to connect a simple streaming gateway client
func connectStreamingGatewaySimple(t *testing.T) (*socket.Socket, error) {
	opts := socket.DefaultOptions()
	// Force polling transport only to avoid WebSocket negotiation race conditions
	opts.SetTransports(types.NewSet(transports.Polling))

	// Add authentication data to the auth object (server expects this)
	authData := map[string]interface{}{
		"machine_key": testMachineKey,
		"api_key":     testAPIKey,
	}
	opts.SetAuth(authData)

	// Create manager and connect to gateway namespace
	manager := socket.NewManager("http://rushhour:8080", opts)
	client := manager.Socket("/auth/gateway", opts)

	authSuccess := make(chan bool, 1)
	client.On("gateway_init", func(args ...any) {
		authSuccess <- true
	})

	client.On("connect_error", func(args ...any) {
		t.Errorf("Gateway connection error: %v", args)
	})

	client.Connect()

	select {
	case <-authSuccess:
		return client, nil
	case <-time.After(15 * time.Second):
		client.Disconnect()
		return nil, fmt.Errorf("gateway authentication timeout")
	}
}

// Helper function to connect a simple streaming FSA client
func connectStreamingFSASimple(t *testing.T, jwtToken string) (*socket.Socket, error) {
	opts := socket.DefaultOptions()
	// Force polling transport only to avoid WebSocket negotiation race conditions
	opts.SetTransports(types.NewSet(transports.Polling))

	// Add JWT token to the auth object (server expects this)
	authData := map[string]interface{}{
		"token": jwtToken,
	}
	opts.SetAuth(authData)

	// Create manager and connect to FSA namespace
	manager := socket.NewManager("http://rushhour:8080", opts)
	client := manager.Socket("/auth/fsa", opts)

	authSuccess := make(chan bool, 1)
	client.On("auth_success", func(args ...any) {
		authSuccess <- true
	})

	client.On("connect_error", func(args ...any) {
		t.Errorf("FSA connection error: %v", args)
	})

	client.Connect()

	select {
	case <-authSuccess:
		return client, nil
	case <-time.After(15 * time.Second):
		client.Disconnect()
		return nil, fmt.Errorf("FSA authentication timeout")
	}
}
