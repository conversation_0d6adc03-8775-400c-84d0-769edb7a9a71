package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"net/url"
	"strings"
	"testing"
	"time"

	"cloud.google.com/go/bigquery"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/schema_mgmt"
)

// AwaitBroker repeatedly checks the health endpoint until it reports healthy or the timeout is reached.
func AwaitBroker(ctx context.Context, interval time.Duration) error {
	// Utilize healthz service to determine if broker is ready
	url := "http://broker:8081/readyz"
	client := &http.Client{Timeout: 5 * time.Second}

	for {
		select {
		case <-ctx.Done():
			return errors.New("health check timed out")
		default:
			resp, err := client.Get(url)
			if err != nil {
				logger.Debugf("Error checking health endpoint: %v\n", err)
			} else {
				body, _ := io.ReadAll(resp.Body)
				resp.Body.Close()

				if resp.StatusCode == http.StatusOK {
					logger.Debugf("Service is healthy: %s\n", string(body))
					return nil
				}
				logger.Debugf("Service unhealthy (status: %d), retrying...\n", resp.StatusCode)
			}
			time.Sleep(interval)
		}
	}
}

// AwaitRushHour repeatedly checks the health endpoint until it reports healthy or the timeout is reached.
func AwaitRushHour(ctx context.Context, interval time.Duration) error {
	// Utilize healthz service to determine if rushhour is ready
	url := "http://rushhour:8081/readyz"
	client := &http.Client{Timeout: 5 * time.Second}

	for {
		select {
		case <-ctx.Done():
			return errors.New("health check timed out")
		default:
			resp, err := client.Get(url)
			if err != nil {
				logger.Debugf("Error checking health endpoint: %v\n", err)
			} else {
				body, _ := io.ReadAll(resp.Body)
				resp.Body.Close()

				if resp.StatusCode == http.StatusOK {
					logger.Debugf("Service is healthy: %s\n", string(body))
					return nil
				}
				logger.Debugf("Service unhealthy (status: %d), retrying...\n", resp.StatusCode)
			}
			time.Sleep(interval)
		}
	}
}

// AwaitOnramp repeatedly checks the health endpoint until it reports healthy or the timeout is reached.
func AwaitOnramp(ctx context.Context, interval time.Duration) error {
	// Utilize healthz service to determine if onramp is ready
	url := "http://onramp:8081/readyz"
	client := &http.Client{Timeout: 5 * time.Second}

	for {
		select {
		case <-ctx.Done():
			return errors.New("health check timed out")
		default:
			resp, err := client.Get(url)
			if err != nil {
				logger.Debugf("Error checking onramp health endpoint: %v\n", err)
			} else {
				body, _ := io.ReadAll(resp.Body)
				resp.Body.Close()

				if resp.StatusCode == http.StatusOK {
					logger.Debugf("Onramp service is healthy: %s\n", string(body))
					return nil
				}
				logger.Debugf("Onramp service unhealthy (status: %d), retrying...\n", resp.StatusCode)
			}
			time.Sleep(interval)
		}
	}
}

// SetupBigquery() is a helper function to correctly configure the BigQuery
// client and apply schema migrations.
// It is modeled after the coordinator's setup function, but is simplified for
// testing purposes.
func SetupBigQuery(bq *connect.BigQueryExecutor, schemaName string, version string) error {
	// Create a dataset (if it doesn't exist).
	datasetID := bq.Config.DBName
	dataset := bq.Client.Dataset(datasetID)
	if err := dataset.Create(bq.Ctx, &bigquery.DatasetMetadata{Location: "us-central1"}); err != nil {
		if !strings.Contains(err.Error(), "Already Exists") && !strings.Contains(err.Error(), "already created") {
			return err
		}
	}

	return schema_mgmt.ApplyMigrations(&schema_mgmt.BigQueryMigrationExecutor{
		Client: bq,
	}, schemaName, version)
}

// SetupPostgres() is a helper function to correctly configure the Postgres
// client and apply schema migrations.
// It is modeled after the coordinator's setup function, but is simplified for
// testing purposes.
func SetupPostgres(pg *connect.PostgresExecutor, schemaName string, version string) error {
	return schema_mgmt.ApplyMigrations(&schema_mgmt.PostgresMigrationExecutor{
		Client: pg,
	}, schemaName, version)
}

// PerformBasicAuthLogin performs a basic auth login and returns the session cookie
func PerformBasicAuthLogin(t *testing.T, username, password string) *http.Cookie {
	t.Helper()

	// Create HTTP client
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Prepare form data
	formData := url.Values{}
	formData.Set("username", username)
	formData.Set("password", password)

	// Create request
	req, err := http.NewRequest("POST", "http://onramp:4200/login", strings.NewReader(formData.Encode()))
	if err != nil {
		t.Fatalf("Failed to create login request: %v", err)
	}

	// Set content type for form data
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	// Perform the request
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("Failed to perform login request: %v", err)
	}
	defer resp.Body.Close()

	// Check if login was successful
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("Login failed with status %d", resp.StatusCode)
	}

	// Extract session cookie
	var sessionCookie *http.Cookie
	for _, cookie := range resp.Cookies() {
		if cookie.Name == "session_id" {
			sessionCookie = cookie
			break
		}
	}

	if sessionCookie == nil {
		t.Fatal("No session cookie found in response")
	}

	logger.Debugf("Login successful, session cookie: %s", sessionCookie.Value)
	return sessionCookie
}

// PerformJWTAuthentication performs JWT authentication with broker and returns the JWT token
func PerformJWTAuthentication(t *testing.T, username, password string) string {
	t.Helper()
	
	client := &http.Client{Timeout: 10 * time.Second}
	
	// Prepare authentication payload
	payload := map[string]string{
		"username": username,
		"password": password,
	}
	
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		t.Fatalf("Failed to marshal auth payload: %v", err)
	}
	
	// Make authentication request
	t.Logf("Attempting JWT authentication for user: %s", username)
	resp, err := client.Post("http://broker:8080/api/v3/user/authenticate", 
		"application/json", bytes.NewReader(payloadBytes))
	if err != nil {
		t.Fatalf("Authentication request failed - check if broker is running: %v", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		// Read the error response for better diagnostics
		body, _ := io.ReadAll(resp.Body)
		t.Fatalf("Authentication failed with status %d. Response: %s", resp.StatusCode, string(body))
	}
	
	// Parse authentication response - broker returns nested structure
	type AuthData struct {
		User  interface{} `json:"user"`
		Token string      `json:"token"`
	}
	type AuthResponse struct {
		Code    int      `json:"code"`
		Data    AuthData `json:"data"`
		Message string   `json:"message"`
		Status  string   `json:"status"`
	}
	
	var authResp AuthResponse
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		t.Fatalf("Failed to parse auth response: %v", err)
	}
	
	if authResp.Data.Token == "" {
		t.Fatalf("No token in authentication response. Full response: %+v", authResp)
	}
	
	t.Logf("JWT authentication successful for user: %s", username)
	return authResp.Data.Token
}
