package utils

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
)

func TestAwaitBrokerTimeout(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel immediately to avoid HTTP calls

	err := AwaitBroker(ctx, time.Millisecond)
	assert.<PERSON><PERSON><PERSON>(t, err)
	assert.Contains(t, err.<PERSON>rror(), "health check timed out")
}

func TestAwaitBrokerWithTimeout(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Nanosecond)
	defer cancel()

	time.Sleep(1 * time.Millisecond) // Ensure timeout

	err := AwaitBroker(ctx, time.Millisecond)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "health check timed out")
}

func TestAwaitBrokerZeroTimeout(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 0)
	defer cancel()

	err := AwaitBroker(ctx, time.Millisecond)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "health check timed out")
}

func TestSetupPostgresPanics(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			// Expected panic due to nil DB connection
			assert.NotNil(t, r)
		}
	}()

	pgExecutor := &connect.PostgresExecutor{}
	err := SetupPostgres(pgExecutor, "test", "v1")
	// If we reach here, something unexpected happened
	assert.Fail(t, "Expected panic but got: %v", err)
}

func TestSetupPostgresWithNil(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			// Expected panic
			assert.NotNil(t, r)
		}
	}()

	err := SetupPostgres(nil, "test", "v1")
	assert.Fail(t, "Expected panic but got: %v", err)
}

func TestSetupPostgresWithEmptyStrings(t *testing.T) {
	defer func() {
		recover() // Expected panic
	}()

	pgExecutor := &connect.PostgresExecutor{}
	_ = SetupPostgres(pgExecutor, "", "")
}

func TestSetupBigQueryPanics(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			// Expected panic due to nil client
			assert.NotNil(t, r)
		}
	}()

	bqExecutor := &connect.BigQueryExecutor{
		Config: connect.DatabaseConfig{DBName: "test"},
		Ctx:    context.Background(),
	}

	err := SetupBigQuery(bqExecutor, "test", "v1")
	assert.Fail(t, "Expected panic but got: %v", err)
}

func TestSetupBigQueryWithNil(t *testing.T) {
	defer func() {
		if r := recover(); r != nil {
			// Expected panic
			assert.NotNil(t, r)
		}
	}()

	err := SetupBigQuery(nil, "test", "v1")
	assert.Fail(t, "Expected panic but got: %v", err)
}

func TestSetupBigQueryWithEmptyStrings(t *testing.T) {
	defer func() {
		recover() // Expected panic
	}()

	bqExecutor := &connect.BigQueryExecutor{
		Config: connect.DatabaseConfig{DBName: ""},
		Ctx:    context.Background(),
	}

	_ = SetupBigQuery(bqExecutor, "", "")
}

func TestSetupBigQueryWithDifferentDBNames(t *testing.T) {
	testCases := []string{
		"test-dataset",
		"test_dataset",
		"testdataset123",
		"",
	}

	for _, dbName := range testCases {
		t.Run("DBName_"+dbName, func(t *testing.T) {
			defer func() {
				recover() // Expected panic due to nil client
			}()

			bqExecutor := &connect.BigQueryExecutor{
				Config: connect.DatabaseConfig{DBName: dbName},
				Ctx:    context.Background(),
			}

			_ = SetupBigQuery(bqExecutor, "test", "v1")
		})
	}
}
