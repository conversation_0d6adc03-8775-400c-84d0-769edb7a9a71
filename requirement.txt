Title:
As an Frontend Developer, I need endpoints to drive the "My Account" -> "Authentication Methods" page


Description

We will need endpoints to support the following frontend webpage:

Endpoints will be:

GET /user/{userId}/auth-methods

Returns a list of authentication methods tied to the logged in user (first name, last name, username, and email. The list of associated organization names are linked though the {{Memberships}} table)

In addition to the visible data, we will need to reutrn the auth method id, and the auth type to determine the types of actions available. USERNAME_PASSWORD auth will allow for the Change Password option, OIDC auth will allow for the Delete option.

DELETE /user/{userId}/auth-methods/{authmethodId}

Deletes auth methods if the option is available

PATCH /user/{userId}/auth-methods/{authmethodId}/password

Updates password

Body of request requires 3 fields:

Current Password (Validates user owns account)

New Password

Confirm Password (Validates no typo in New Password)

Backend will need to validate New Password matches Confirm Password, and that Current Password matches the database hash of the password. 

Password is currently hashed using the following function (but will need to be updated in the future)



func CalculateSHA256(input string) string {
	hasher := sha256.New()
	hasher.Write([]byte(input))
	hashBytes := hasher.Sum(nil)
	return hex.EncodeToString(hashBytes)
}