-- CAT-444: Convert schema fields from INTEGER to TEXT format
-- This migration converts the DeviceRMSEngine integer values to decimal format strings (e.g., 5 -> "5.0")
-- Original content from step9_convert_rms_fields_to_string.sql

-- Add temporary columns with the new data type
ALTER TABLE {{DeviceRMSEngine}} 
ADD COLUMN EngineVersion_temp TEXT,
ADD COLUMN EngineRevision_temp TEXT;

-- Convert existing integer values to decimal format strings
UPDATE {{DeviceRMSEngine}} 
SET 
  EngineVersion_temp = CASE 
    WHEN EngineVersion IS NULL THEN NULL
    ELSE CONCAT(EngineVersion::TEXT, '.0')
  END,
  EngineRevision_temp = CASE 
    WHEN EngineRevision IS NULL THEN NULL
    ELSE CONCAT(EngineRevision::TEXT, '.0')
  END;

-- Drop the old columns
ALTER TABLE {{DeviceRMSEngine}} 
DROP COLUMN EngineVersion,
DROP COLUMN EngineRevision;

-- Rename the temporary columns to the original names
ALTER TABLE {{DeviceRMSEngine}} 
RENAME COLUMN EngineVersion_temp TO EngineVersion;

ALTER TABLE {{DeviceRMSEngine}} 
RENAME COLUMN EngineRevision_temp TO EngineRevision; 