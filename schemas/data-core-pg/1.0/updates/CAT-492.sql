-- ==============================
-- CAT-492: Add IsDeletable to TemplateRole and CustomRole and migrate data
-- ==============================

-- Add IsDeletable to TemplateRole
ALTER TABLE {{TemplateRole}} ADD COLUMN IsDeletable BOOLEAN NOT NULL DEFAULT TRUE;

-- Set admin and anonymous roles to not deletable
UPDATE {{TemplateRole}} 
SET IsDeletable = FALSE 
WHERE Identifier IN ('syn_admin', 'syn_anonymous', 'mun_admin', 'mun_anonymous');

-- Add IsDeletable to CustomRole
ALTER TABLE {{CustomRole}} ADD COLUMN IsDeletable BOOLEAN NOT NULL DEFAULT TRUE;

-- Set admin roles to not deletable
UPDATE {{CustomRole}} 
SET IsDeletable = FALSE 
WHERE TemplateRoleIdentifier IN ('syn_admin', 'syn_anonymous', 'mun_admin', 'mun_anonymous');
