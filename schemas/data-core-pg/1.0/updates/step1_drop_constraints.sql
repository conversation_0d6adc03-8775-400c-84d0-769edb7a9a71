-- STEP 1: Drop all constraints and indexes from all tables
-- Drop foreign key constraints
ALTER TABLE {{DeviceCommunicationConfig}} DROP CONSTRAINT IF EXISTS {{DeviceCommunicationConfig_SoftwareGatewayID_DeviceIdentifier_IPAddress}};
ALTER TABLE {{SoftwareGateway}} DROP CONSTRAINT IF EXISTS {{SoftwareGateway_OrganizationId}};
ALTER TABLE {{SoftwareGatewayConfig}} DROP CONSTRAINT IF EXISTS {{SoftwareGatewayConfig_SoftwareGateway_Id}};
ALTER TABLE {{User}} DROP CONSTRAINT IF EXISTS {{User_Organization}};
ALTER TABLE {{User}} DROP CONSTRAINT IF EXISTS {{User_RoleId}};
ALTER TABLE {{UserSoftwareGateway}} DROP CONSTRAINT IF EXISTS {{UserSoftwareGateway_PermissionId}};
ALTER TABLE {{UserSoftwareGateway}} DROP CONSTRAINT IF EXISTS {{UserSoftwareGateway_SoftwareGatewayId}};
<PERSON>TER TABLE {{UserSoftwareGateway}} DROP CONSTRAINT IF EXISTS {{UserSoftwareGateway_UserId}};
ALTER TABLE {{UserToken}} DROP CONSTRAINT IF EXISTS {{UserToken_UserId}};
ALTER TABLE {{Device}} DROP CONSTRAINT IF EXISTS {{Device_SoftwareGateway}};
ALTER TABLE {{DeviceMonitorName}} DROP CONSTRAINT IF EXISTS {{DeviceMonitorName_DeviceIdentifier}};
ALTER TABLE {{DeviceRMSEngine}} DROP CONSTRAINT IF EXISTS {{DeviceRMSEngine_DeviceIdentifier}};
ALTER TABLE {{DeviceFault}} DROP CONSTRAINT IF EXISTS {{DeviceFault_DeviceIdentifier}};
ALTER TABLE {{DeviceLog}} DROP CONSTRAINT IF EXISTS {{DeviceLog_DeviceIdentifier}};
ALTER TABLE {{SoftwareGatewayInstruction}} DROP CONSTRAINT IF EXISTS {{SoftwareGatewayInstruction_DeviceId}};
ALTER TABLE {{SoftwareGatewayInstruction}} DROP CONSTRAINT IF EXISTS {{SoftwareGatewayInstruction_Instruction}};
ALTER TABLE {{SoftwareGatewayInstruction}} DROP CONSTRAINT IF EXISTS {{SoftwareGatewayInstruction_Status}};
ALTER TABLE {{SoftwareGatewayInstruction}} DROP CONSTRAINT IF EXISTS {{SoftwareGatewayInstruction_UserId}};
ALTER TABLE {{UserDevice}} DROP CONSTRAINT IF EXISTS {{UserDevice_DeviceId}};
ALTER TABLE {{UserDevice}} DROP CONSTRAINT IF EXISTS {{UserDevice_RoleId}};
ALTER TABLE {{UserDevice}} DROP CONSTRAINT IF EXISTS {{UserDevice_UserId}};
ALTER TABLE {{DeviceMacAddress}} DROP CONSTRAINT IF EXISTS {{DeviceMacAddress_DeviceIdentifier}};

-- Drop unique constraints
ALTER TABLE {{SoftwareGateway}} DROP CONSTRAINT IF EXISTS {{SoftwareGateway_SoftwareGatewayIdentifier}};
ALTER TABLE {{User}} DROP CONSTRAINT IF EXISTS {{User_UserName_UNIQUE}};
ALTER TABLE {{Device}} DROP CONSTRAINT IF EXISTS {{Device_DeviceIdentifier_UNIQUE}};

-- Drop all indexes (except primary keys)
-- Note: PostgreSQL automatically creates indexes for primary keys and unique constraints
-- These are the indexes that would be created by the constraints above
DROP INDEX IF EXISTS {{DeviceCommunicationConfig_SoftwareGatewayID_DeviceIdentifier_IPAddress}};
DROP INDEX IF EXISTS {{SoftwareGateway_SoftwareGatewayIdentifier}};
DROP INDEX IF EXISTS {{User_UserName_UNIQUE}};
DROP INDEX IF EXISTS {{Device_DeviceIdentifier_UNIQUE}}; 