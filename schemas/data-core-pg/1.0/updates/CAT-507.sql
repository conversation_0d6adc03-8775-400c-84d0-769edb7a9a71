-- CAT-507: Add DeviceType table and migrate data
-- DeviceType table: definition and insert values
-- Lookup table of valid device types
CREATE TABLE {{DeviceType}} (
  Type TEXT,
  Description TEXT NOT NULL,
  CONSTRAINT {{DeviceType_PK}} PRIMARY KEY (Type)
);

-- Insert DeviceType values
INSERT INTO {{DeviceType}} (Type, Description) VALUES 
  ('UNKNOWN_DEVICE', 'Unknown device type'), 
  ('EDI_LEGACY', 'EDI Legacy device'), 
  ('EDI_NEXT_GEN', 'EDI Next Gen device');


-- Device table: Rename Description to Name, Type to Description, and add new Type column
-- Step 1: Rename existing columns
-- First rename Description to Name
ALTER TABLE {{Device}} RENAME COLUMN Description TO Name;

-- Then rename the old Type column to Description  
ALTER TABLE {{Device}} RENAME COLUMN Type TO Description;

-- Step 2: Add the new Type column with correct name and defaults
ALTER TABLE {{Device}} ADD COLUMN Type TEXT NOT NULL DEFAULT 'UNKNOWN_DEVICE';

-- Step 3: Update any NULL values to the default
UPDATE {{Device}} SET Type = 'EDI_LEGACY' WHERE Type IS NULL;

-- Step 4: Add foreign key constraint for the new Type column
ALTER TABLE {{Device}} ADD CONSTRAINT {{Device_DeviceType_FK}} 
  FOREIGN KEY (Type) REFERENCES {{DeviceType}} (Type);

-- SoftwareGateway table: add gateway version column
ALTER TABLE {{SoftwareGateway}} ADD COLUMN GatewayVersion TEXT;
