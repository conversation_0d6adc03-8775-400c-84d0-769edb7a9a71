-- CAT-441: Add Keycloak OIDC authentication support
-- Dev-specific update for development environment only

-- Add Keycloak admin user (dev environment only)
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, IsDeleted) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 9999, 'Admin', 'Synapse', null, false, 'America/Chicago', 'Keycloak admin user for Synapse organization', false)
;

-- Add OIDC AuthMethod for Keycloak user
INSERT INTO {{AuthMethod}} (Id, UserId, Type, Sub, Issuer, Email, IsEnabled) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_keycloak_admin'), 'OIDC', '1b08b49c-804c-4d73-98e8-b23f885a3613', 'http://keycloak:8080/realms/onramp-dev', '<EMAIL>', true)
;

-- Add membership for Keycloak admin user to Synapse organization
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_keycloak_admin_synapse'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_keycloak_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'))
;

-- Assign Synapse admin role to Keycloak user
INSERT INTO {{OrgRoleAssignments}} (MembershipId, RoleId) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_keycloak_admin_synapse'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_admin'))
;

-- Update User sequence
SELECT setval('{{User_OrigId_SEQ}}', COALESCE((SELECT MAX(OrigId) FROM {{User}}), 1));
