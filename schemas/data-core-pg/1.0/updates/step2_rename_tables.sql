-- STEP 2: Rename all tables to Legacy<TableName>
ALTER TABLE {{DeviceCommunicationConfig}} RENAME TO {{LegacyDeviceCommunicationConfig}};
ALTER TABLE {{Organization}} RENAME TO {{LegacyOrganization}};
ALTER TABLE {{Permission}} RENAME TO {{LegacyPermission}};
ALTER TABLE {{Role}} RENAME TO {{LegacyRole}};
ALTER TABLE {{SoftwareGatewayInstructionList}} RENAME TO {{LegacySoftwareGatewayInstructionList}};
ALTER TABLE {{SoftwareGatewayInstructionStatus}} RENAME TO {{LegacySoftwareGatewayInstructionStatus}};
ALTER TABLE {{SoftwareGateway}} RENAME TO {{LegacySoftwareGateway}};
ALTER TABLE {{SoftwareGatewayConfig}} RENAME TO {{LegacySoftwareGatewayConfig}};
ALTER TABLE {{User}} RENAME TO {{LegacyUser}};
ALTER TABLE {{UserSoftwareGateway}} RENAME TO {{LegacyUserSoftwareGateway}};
ALTER TABLE {{UserToken}} RENAME TO {{LegacyUserToken}};
ALTER TABLE {{Device}} RENAME TO {{LegacyDevice}};
ALTER TABLE {{DeviceMonitorName}} RENAME TO {{LegacyDeviceMonitorName}};
ALTER TABLE {{DeviceRMSEngine}} RENAME TO {{LegacyDeviceRMSEngine}};
ALTER TABLE {{DeviceFault}} RENAME TO {{LegacyDeviceFault}};
ALTER TABLE {{DeviceLog}} RENAME TO {{LegacyDeviceLog}};
ALTER TABLE {{SoftwareGatewayInstruction}} RENAME TO {{LegacySoftwareGatewayInstruction}};
ALTER TABLE {{UserDevice}} RENAME TO {{LegacyUserDevice}};
ALTER TABLE {{DeviceMacAddress}} RENAME TO {{LegacyDeviceMacAddress}}; 