-- STEP 5: Migrate data from Legacy tables to new 1.0 tables

-- Migrate Organizations (convert to municipality type by default)
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier, IsDeleted, CreatedAt, UpdatedAt)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || Id::text),
  Description,
  Description,
  'municipality',
  IsDeleted,
  CreatedAt,
  UpdatedAt
FROM {{LegacyOrganization}};

-- Migrate Users (generate UUIDs and create AuthMethods)
INSERT INTO {{User}} (Id, OrigId, FirstName, LastName, Mobile, NotificationSmsEnabled, IanaTimezone, Description, LastLogin, IsDeleted, CreatedAt, UpdatedAt)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_' || Id::text),
  Id,
  FirstName,
  LastName,
  Mobile,
  NotificationSmsEnabled,
  IANATimezone,
  Description,
  LastLoginUTC,
  IsDeleted,
  CreatedAt,
  UpdatedAt
FROM {{LegacyUser}};

-- Create AuthMethods for each user
INSERT INTO {{AuthMethod}} (Id, UserId, Type, UserName, PasswordHash, Email, LastLogin)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_' || Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_' || Id::text),
  'USERNAME_PASSWORD',
  UserName,
  Password,
  Email,
  LastLoginUTC
FROM {{LegacyUser}};

-- Migrate SoftwareGateways
INSERT INTO {{SoftwareGateway}} (Id, MachineKey, OrganizationId, ApiKey, Token, DateLastCheckedIn, PushConfigOnNextCheck, IsEnabled, Config, Name, Description, CreatedAt, UpdatedAt, IsDeleted)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_' || Id::text),
  SoftwareGatewayIdentifier,
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || OrganizationId::text),
  APIKey,
  Token,
  DateLastCheckedInUTC,
  PushConfigOnNextCheck,
  IsEnabled,
  COALESCE((SELECT Config FROM {{LegacySoftwareGatewayConfig}} WHERE SoftwareGatewayId = sg.Id), ''),
  Description,
  Description,
  CreatedAt,
  UpdatedAt,
  IsDeleted
FROM {{LegacySoftwareGateway}} sg;

-- Create default locations for each organization
INSERT INTO {{Location}} (Id, OrganizationId, Name, Description, Latitude, Longitude)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_default_' || o.Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || Id::text),
  'Default Location',
  'Default location for organization ' || o.Description,
  0.0,
  0.0
FROM {{LegacyOrganization}} o;

-- Create individual locations for each device based on DeviceCommunicationConfig coordinates
INSERT INTO {{Location}} (Id, OrganizationId, Name, Description, Latitude, Longitude)
SELECT DISTINCT
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_device_' || d.Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || sg.OrganizationId::text),
  'Device Location: ' || d.Description,
  'Location for device ' || d.Description,
  COALESCE(dcc.Latitude::numeric, 0.0),
  COALESCE(dcc.Longitude::numeric, 0.0)
FROM {{LegacyDevice}} d
JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
LEFT JOIN {{LegacyDeviceCommunicationConfig}} dcc ON dcc.DeviceIdentifier::text = d.DeviceIdentifier::text
WHERE dcc.Latitude IS NOT NULL AND dcc.Longitude IS NOT NULL;

-- Migrate Devices (need to map to locations)
INSERT INTO {{Device}} (Id, OrigId, SoftwareGatewayId, LocationId, Description, Type, IpAddress, Port, FlushConnectionMs, EnableRealtime, IsEnabled, CreatedAt, UpdatedAt, IsDeleted)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_' || d.Id::text),
  d.Id,
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_softwaregateway_' || d.SoftwareGatewayId::text),
  -- Use device-specific location if coordinates exist, otherwise use default location
  CASE 
    WHEN dcc.Latitude IS NOT NULL AND dcc.Longitude IS NOT NULL 
    THEN uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_device_' || d.Id::text)
    ELSE uuid_generate_v5(uuid_nil(), 'SYNAPSE_location_default_' || sg.OrganizationId::text)
  END,
  d.Description,
  d.Type,
  COALESCE(dcc.IPAddress, '0.0.0.0'),
  COALESCE(dcc.Port::integer, 0),
  COALESCE(dcc.FlushConnectionMs::integer, 0),
  CASE WHEN dcc.EnableRealtime = 'true' THEN true ELSE false END,
  CASE WHEN dcc.IsEnabled = 1 THEN true ELSE false END,
  d.CreatedAt,
  d.UpdatedAt,
  d.IsDeleted
FROM {{LegacyDevice}} d
LEFT JOIN {{LegacyDeviceCommunicationConfig}} dcc ON dcc.DeviceIdentifier::text = d.DeviceIdentifier::text
JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId;

-- Create Memberships (one per user per organization)
INSERT INTO {{Memberships}} (Id, AuthMethodId, OrganizationId)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_membership_' || u.Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_' || u.Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || u.OrganizationId::text)
FROM {{LegacyUser}} u;

-- Create default DeviceGroups for each organization
INSERT INTO {{DeviceGroups}} (Id, OrganizationId, Name)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_devicegroup_default_' || o.Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || Id::text),
  'Default Device Group'
FROM {{LegacyOrganization}} o;

-- Create default LocationGroups for each organization
INSERT INTO {{LocationGroups}} (Id, OrganizationId, Name)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_locationgroup_default_' || o.Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || Id::text),
  'Default Location Group'
FROM {{LegacyOrganization}} o;

-- Populate LocationGroupLocations with default locations
INSERT INTO {{LocationGroupLocations}} (LocationGroupId, LocationId)
SELECT 
  lg.Id,
  l.Id
FROM {{LocationGroups}} lg
JOIN {{Location}} l ON l.OrganizationId = lg.OrganizationId
WHERE lg.Name = 'Default Location Group' AND l.Name = 'Default Location';

-- Create default custom roles for each organization based on template roles
INSERT INTO {{CustomRole}} (
  Id,
  OrganizationId,
  TemplateRoleIdentifier,
  OrgTypeIdentifier,
  Name,
  Description
)
SELECT
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_customrole_' || tr.Identifier || '_' || o.Id::text) as Id,
  o.Id as OrganizationId,
  tr.Identifier as TemplateRoleIdentifier,
  o.OrgTypeIdentifier as OrgTypeIdentifier,
  tr.Name,
  tr.Description
FROM {{TemplateRole}} tr
JOIN {{Organization}} o ON o.OrgTypeIdentifier = tr.OrgTypeIdentifier;

-- Generate device groups from existing user/device/org relationships (from CAT-400)
INSERT INTO {{DeviceGroups}}(
  Id,
  OrganizationId,
  Name
)
SELECT
  uogm.DeviceSet as Id,
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || uogm.OrgId::text) as OrganizationId,
  'migrated group: ' || uogm.DeviceSet::text as Name
FROM (
  SELECT
    u.Id AS UserId,
    o.Id AS OrgId,
    uuid_generate_v5(
      uuid_nil(),
      'SYNAPSE_devicegroup_' || (
        SELECT STRING_AGG(CAST(device_id AS TEXT), ',')
        FROM (
          SELECT DISTINCT d.Id AS device_id
          FROM {{LegacyUserDevice}} ud2
          JOIN {{LegacyDevice}} d ON d.Id = ud2.DeviceId
          JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
          JOIN {{LegacyOrganization}} o2 ON o2.Id = sg.OrganizationId
          WHERE ud2.UserId = u.Id AND o2.Id = sg.OrganizationId
          ORDER BY d.Id
        ) sub
      )
    ) AS DeviceSet
  FROM {{LegacyUserDevice}} ud
  JOIN {{LegacyDevice}} d ON d.Id = ud.DeviceId
  JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
  JOIN {{LegacyUser}} u ON u.Id = ud.UserId
  JOIN {{LegacyOrganization}} o ON o.Id = sg.OrganizationId
  GROUP BY u.Id, o.Id
) uogm
JOIN {{LegacyUserDevice}} ud ON ud.UserId = uogm.UserId
JOIN {{LegacyDevice}} d ON d.Id = ud.DeviceId
JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
JOIN {{LegacyOrganization}} o ON o.Id = sg.OrganizationId
WHERE o.Id = sg.OrganizationId
GROUP BY uogm.DeviceSet, uogm.OrgId;

-- Populate DeviceGroupDevices for the migrated groups
INSERT INTO {{DeviceGroupDevices}} (
  DeviceGroupId,
  DeviceId
)
SELECT
  uogm.DeviceSet Id,
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_' || uogm.device_id::text)
FROM (
  SELECT
    u.Id AS UserId,
    u.Id AS UserOrigId,
    o.Id AS OrgId,
    d.Id as device_id,
    uuid_generate_v5(
      uuid_nil(),
      'SYNAPSE_devicegroup_' || (
        SELECT STRING_AGG(CAST(device_id AS TEXT), ',')
        FROM (
          SELECT DISTINCT d.Id AS device_id
          FROM {{LegacyUserDevice}} ud2
          JOIN {{LegacyDevice}} d ON d.Id = ud2.DeviceId
          JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
          JOIN {{LegacyOrganization}} o2 ON o2.Id = sg.OrganizationId
          WHERE ud2.UserId = u.Id AND o2.Id = sg.OrganizationId
          ORDER BY d.Id
        ) sub
      )
    ) AS DeviceSet
  FROM {{LegacyUserDevice}} ud
  JOIN {{LegacyDevice}} d ON d.Id = ud.DeviceId
  JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
  JOIN {{LegacyUser}} u ON u.Id = ud.UserId
  JOIN {{LegacyOrganization}} o ON o.Id = sg.OrganizationId
  GROUP BY u.Id, o.Id, d.Id
) uogm
JOIN {{LegacyUserDevice}} ud ON ud.UserId = uogm.UserOrigId
JOIN {{LegacyDevice}} d ON d.Id = ud.DeviceId
JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
JOIN {{LegacyOrganization}} o ON o.Id = sg.OrganizationId
WHERE o.Id = sg.OrganizationId
GROUP BY uogm.DeviceSet, uogm.device_id;

-- Populate DeviceGroupRoleAssignments based on legacy UserDevice relationships
INSERT INTO {{DeviceGroupRoleAssignments}} (
  MembershipId,
  DeviceGroupId,
  RoleId
)
SELECT
  m.Id as MembershipId,
  uogm.DeviceSet as DeviceGroupId,
  (SELECT cr.Id FROM {{CustomRole}} cr WHERE cr.OrganizationId = uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || o.Id::text) AND cr.TemplateRoleIdentifier = 'mun_technician' LIMIT 1) as RoleId
FROM (
  SELECT
    u.Id AS UserId,
    u.Id as userorigid,
    o.Id AS OrgId,
    d.Id as device_id,
    uuid_generate_v5(
      uuid_nil(),
      'SYNAPSE_devicegroup_' || (
        SELECT STRING_AGG(CAST(device_id AS TEXT), ',')
        FROM (
          SELECT DISTINCT d.Id AS device_id
          FROM {{LegacyUserDevice}} ud2
          JOIN {{LegacyDevice}} d ON d.Id = ud2.DeviceId
          JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
          JOIN {{LegacyOrganization}} o2 ON o2.Id = sg.OrganizationId
          WHERE ud2.UserId = u.Id AND o2.Id = sg.OrganizationId
          ORDER BY d.Id
        ) sub
      )
    ) AS DeviceSet
  FROM {{LegacyUserDevice}} ud
  JOIN {{LegacyDevice}} d ON d.Id = ud.DeviceId
  JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
  JOIN {{LegacyUser}} u ON u.Id = ud.UserId
  JOIN {{LegacyOrganization}} o ON o.Id = sg.OrganizationId
  GROUP BY u.Id, o.Id, d.Id
) uogm
JOIN {{LegacyUserDevice}} ud ON ud.UserId = uogm.userorigid
JOIN {{LegacyDevice}} d ON d.Id = ud.DeviceId
JOIN {{LegacySoftwareGateway}} sg ON sg.Id = d.SoftwareGatewayId
JOIN {{LegacyOrganization}} o ON o.Id = sg.OrganizationId
JOIN {{AuthMethod}} a ON a.UserId = uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_' || uogm.UserId::text)
JOIN {{Memberships}} m ON m.AuthMethodId = a.Id AND m.OrganizationId = uuid_generate_v5(uuid_nil(), 'SYNAPSE_organization_' || o.Id::text)
WHERE o.Id = sg.OrganizationId
GROUP BY o.id, m.Id, uogm.DeviceSet;

-- Migrate UserToken
INSERT INTO {{UserToken}} (UserId, JwtToken, JwtTokenSha256, Created, Expiration)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_' || ut.UserId::text),
  ut.JWTToken,
  ut.JWTTokenSha256,
  ut.CreatedUTC,
  ut.ExpirationUTC
FROM {{LegacyUserToken}} ut;

-- Migrate SoftwareGatewayInstruction
INSERT INTO {{SoftwareGatewayInstruction}} (UserId, DeviceId, Instruction, DateQueued, DateReceived, Status)
SELECT 
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_' || sgi.UserId::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_device_' || sgi.DeviceId::text),
  sgi.Instruction,
  sgi.DateQueuedUTC,
  sgi.DateReceivedUTC,
  sgi.Status
FROM {{LegacySoftwareGatewayInstruction}} sgi;
