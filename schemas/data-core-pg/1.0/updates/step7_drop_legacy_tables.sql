-- STEP 7: Drop all Legacy tables after migration is complete
DROP TABLE IF EXISTS {{LegacyDeviceCommunicationConfig}} CASCADE;
DROP TABLE IF EXISTS {{LegacyOrganization}} CASCADE;
DROP TABLE IF EXISTS {{LegacyPermission}} CASCADE;
DROP TABLE IF EXISTS {{LegacyRole}} CASCADE;
DROP TABLE IF EXISTS {{LegacySoftwareGatewayInstructionList}} CASCADE;
DROP TABLE IF EXISTS {{LegacySoftwareGatewayInstructionStatus}} CASCADE;
DROP TABLE IF EXISTS {{LegacySoftwareGateway}} CASCADE;
DROP TABLE IF EXISTS {{LegacySoftwareGatewayConfig}} CASCADE;
DROP TABLE IF EXISTS {{LegacyUser}} CASCADE;
DROP TABLE IF EXISTS {{LegacyUserSoftwareGateway}} CASCADE;
DROP TABLE IF EXISTS {{LegacyUserToken}} CASCADE;
DROP TABLE IF EXISTS {{LegacyDevice}} CASCADE;
DROP TABLE IF EXISTS {{LegacyDeviceMonitorName}} CASCADE;
DROP TABLE IF EXISTS {{LegacyDeviceRMSEngine}} CASCADE;
DROP TABLE IF EXISTS {{LegacyDeviceFault}} CASCADE;
DROP TABLE IF EXISTS {{LegacyDeviceLog}} CASCADE;
DROP TABLE IF EXISTS {{LegacySoftwareGatewayInstruction}} CASCADE;
DROP TABLE IF EXISTS {{LegacyUserDevice}} CASCADE;
DROP TABLE IF EXISTS {{LegacyDeviceMacAddress}} CASCADE; 