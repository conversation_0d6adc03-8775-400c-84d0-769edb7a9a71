-- UserInvites table for storing invitation data
CREATE TABLE {{UserInvites}} (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  organizationidentifier UUID NOT NULL,
  tokenhash TEXT NOT NULL,
  email TEXT NOT NULL,
  inviterid UUID NOT NULL,
  customroleid UUID NOT NULL,
  status TEXT NOT NULL,
  message TEXT,
  requiresso BOOLEAN NOT NULL DEFAULT FALSE,
  retrycount INT NOT NULL DEFAULT 0,
  retried TIMESTAMPTZ,
  expired TIMESTAMPTZ,
  created TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  sent TIMESTAMPTZ,
  updated TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT {{UserInvites_PK}} PRIMARY KEY (id),
  CONSTRAINT {{UserInvites_tokenhash_UQ}} UNIQUE (tokenhash),
  CONSTRAINT {{UserInvites_inviterid_FK}} FOREIGN KEY (inviterid) REFERENCES {{User}} (id),
  CONSTRAINT {{UserInvites_org_FK}} FOREIGN KEY (organizationidentifier) REFERENCES {{Organization}} (id),
  CONSTRAINT {{UserInvites_status_CHK}} CHECK (status IN ('pending','redeemed','revoked','expired','rejected')),
  CONSTRAINT {{UserInvites_CustomRole_FK}} FOREIGN KEY (customroleid) REFERENCES {{CustomRole}} (Id)
);
CREATE INDEX {{UserInvites_organizationidentifier_IDX}} ON {{UserInvites}} (organizationidentifier);
CREATE INDEX {{UserInvites_email_IDX}} ON {{UserInvites}} (email);
CREATE INDEX {{UserInvites_status_IDX}} ON {{UserInvites}} (status);
