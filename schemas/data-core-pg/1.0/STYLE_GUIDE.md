# PostgreSQL Schema Style Guide

This document outlines the naming conventions, joining conventions, and formatting standards for the data-core-pg schema.

## Table Naming Conventions

### Primary Tables
- Use **PascalCase** for table names
- Use template variables with double curly braces: `{{TableName}}`
- Examples: `{{Organization}}`, `{{User}}`, `{{Device}}`

### Junction/Linking Tables
- Use **PascalCase** with descriptive names indicating the relationship
- Format: `{{Table1Table2}}` or `{{Table1Table2Relationship}}`
- Examples: `{{DeviceGroupDevices}}`, `{{LocationGroupLocations}}`, `{{OrgRoleAssignments}}`

## Column Naming Conventions

### Primary Keys
- Use `Id` (UUID type) for primary keys
- Use `OrigId` (INTEGER) for legacy/original IDs
- Examples: `Id`, `OrigId`

### Foreign Keys
- Use **PascalCase** with the referenced table name
- Format: `{ReferencedTable}Id`
- Examples: `OrganizationId`, `SoftwareGatewayId`, `LocationId`

### Boolean Columns
- Use `Is` prefix for boolean flags
- Examples: `IsDeleted`, `IsEnabled`, `IsHidden`

### Timestamp Columns
- Use **PascalCase** with descriptive suffixes
- Common patterns:
  - `CreatedAt` - Record creation timestamp
  - `UpdatedAt` - Last modification timestamp
  - `{Action}` - Specific action timestamps (e.g., `DateQueued`, `DateReceived`)

### Text/String Columns
- Use **PascalCase** for descriptive names
- Examples: `FirstName`, `LastName`, `Description`, `Name`

### Special Columns
- `OrgTypeIdentifier` - References organization type (TEXT)
- `AuthMethodId` - References authentication method (UUID)
- `PermissionIdentifier` - References permission (TEXT)
- `TemplateRoleIdentifier` - References template role (TEXT)

## Data Type Conventions

### Standard Types
- **UUID** for primary keys and foreign keys
- **TEXT** for string data (not VARCHAR)
- **BOOLEAN** for boolean flags (not BOOL)
- **TIMESTAMP** for date/time data (not TIMESTAMP WITH TIME ZONE)
- **NUMERIC(5,2)** for decimal values with precision
- **INTEGER** for whole numbers
- **SMALLINT** for small integers
- **JSONB** for JSON data

### Array Types
- Use PostgreSQL array syntax: `BOOLEAN[]`, `INTEGER[]`
- Examples: `ChannelGreenStatus BOOLEAN[]`, `VoltagesGreen INTEGER[]`

## Constraint Naming Conventions

### Primary Key Constraints
- Use template variables: `{{Table_PK}}`
- Examples: `{{DeviceGroupDevices_PK}}`, `{{LocationGroupLocations_PK}}`

### Foreign Key Constraints
- Format: `{{Table_ReferencedTable_FK}}`
- Examples: `{{Organization_OrgType_FK}}`, `{{Device_SoftwareGateway_FK}}`

### Unique Constraints
- Format: `{{Table_Column_UQ}}`
- Examples: `{{SoftwareGateway_MachineKey_UQ}}`, `{{AuthMethod_TypeIssuerSub_UQ}}`

### Check Constraints
- Use descriptive names with `CHECK` suffix
- Example: `Type CHECK (Type IN ('USERNAME_PASSWORD', 'OIDC'))`

## Index Naming Conventions

### Standard Indexes
- Format: `{{Table_Column_IDX}}`
- Examples: `{{Organization_OrigId_IDX}}`, `{{Device_SoftwareGatewayId_IDX}}`

### Unique Indexes
- Format: `{{Table_Column_UQ}}`
- Examples: `{{AuthMethod_UserName_UQ}}`

### Partial Indexes
- Include WHERE condition in name: `{{Table_Column_Condition_IDX}}`
- Example: `{{AuthMethod_UserName_UQ}} ON {{AuthMethod}} (UserName) WHERE Type = 'USERNAME_PASSWORD'`

## Template Variable Usage

### Table References
- Always use template variables for table names: `{{TableName}}`
- Use in CREATE TABLE, INSERT, and other DDL/DML statements

### Constraint References
- Use template variables for constraint names
- Format: `{{Table_ConstraintType}}`

### Index References
- Use template variables for index names
- Format: `{{Table_Column_IndexType}}`

## DDL Structure Conventions

### Table Definition Order
1. Table name and opening brace
2. Column definitions (one per line, indented)
3. Constraints (indented, after columns)
4. Closing brace and semicolon

### Column Definition Format
```sql
ColumnName DataType [NOT NULL] [DEFAULT value] [GENERATED BY DEFAULT AS IDENTITY],
```

### Constraint Definition Format
```sql
CONSTRAINT {{Table_ReferencedTable_FK}} FOREIGN KEY (ColumnName) REFERENCES {{ReferencedTable}} (ReferencedColumn)
```

## DML Structure Conventions

### INSERT Statements
- Use explicit column lists
- Align values for readability
- Use template variables for table names
- Group related inserts with comments

### Example INSERT Format
```sql
INSERT INTO {{TableName}} (Column1, Column2, Column3) VALUES
  (value1, value2, value3),
  (value4, value5, value6);
```

## Comment Conventions

### Table Comments
- Use `--` for single-line comments
- Place table description before CREATE TABLE
- Include purpose and relationships

### Section Comments
- Use descriptive section headers
- Separate major sections with `---------------------------------`

### Inline Comments
- Use `--` for brief explanations
- Place at end of line when possible

## File Organization

### DDL Files
- `schema.sql` - Main schema definition
- Use consistent ordering: extensions, lookup tables, core tables, junction tables

### DML Files
- `data.sql` - Reference/lookup data
- `dev.sql` - Development/test data
- Group related inserts together

## Best Practices

### Consistency
- Always use the same naming pattern for similar concepts
- Maintain consistent indentation (2 spaces)
- Use consistent spacing around operators and keywords

### Readability
- Break long statements across multiple lines
- Align related elements vertically
- Use descriptive names that clearly indicate purpose

### Maintainability
- Use template variables for all table/constraint names
- Document complex relationships with comments
- Keep related tables and constraints together

### Performance
- Create indexes on foreign key columns
- Use appropriate data types for the data
- Consider query patterns when designing indexes

## Migration Considerations

### Backward Compatibility
- When changing column names, update all references
- Maintain foreign key relationships
- Update DML files to match schema changes

### Template Variables
- Always use template variables for table names
- This enables easy schema customization per environment
- Template processing should handle all `{{VariableName}}` patterns

## Examples

### Complete Table Definition
```sql
-- Organization definition  
-- Core organizations in the system (municipalities, OEMs, internal Synapse)
CREATE TABLE {{Organization}} (
  Id UUID PRIMARY KEY,
  OrigId INTEGER GENERATED BY DEFAULT AS IDENTITY,
  Name TEXT NOT NULL,
  Description TEXT NOT NULL,
  OrgTypeIdentifier TEXT NOT NULL,
  IsDeleted BOOLEAN NOT NULL DEFAULT FALSE,
  CreatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  UpdatedAt TIMESTAMP NOT NULL DEFAULT NOW(),
  CONSTRAINT {{Organization_OrgType_FK}} FOREIGN KEY (OrgTypeIdentifier) REFERENCES {{OrgType}} (Identifier)
);

CREATE INDEX {{Organization_OrigId_IDX}} ON {{Organization}} (OrigId);
CREATE INDEX {{Organization_OrgTypeIdentifier_IDX}} ON {{Organization}} (OrgTypeIdentifier);
```

### Junction Table Definition
```sql
-- DeviceGroupDevices definition
-- Many-to-many mapping between device groups and devices
CREATE TABLE {{DeviceGroupDevices}} (
  DeviceGroupId UUID NOT NULL,
  DeviceId UUID NOT NULL,
  CONSTRAINT {{DeviceGroupDevices_PK}} PRIMARY KEY (DeviceGroupId, DeviceId),
  CONSTRAINT {{DeviceGroupDevices_DeviceGroup_FK}} FOREIGN KEY (DeviceGroupId) REFERENCES {{DeviceGroups}} (Id),
  CONSTRAINT {{DeviceGroupDevices_Device_FK}} FOREIGN KEY (DeviceId) REFERENCES {{Device}} (Id)
);

-- Indexes for DeviceGroupDevices table (bi-directional lookups)
CREATE INDEX {{DeviceGroupDevices_DeviceGroupId_IDX}} ON {{DeviceGroupDevices}} (DeviceGroupId);
CREATE INDEX {{DeviceGroupDevices_DeviceId_IDX}} ON {{DeviceGroupDevices}} (DeviceId);
```

This style guide ensures consistency, readability, and maintainability across the entire schema.
