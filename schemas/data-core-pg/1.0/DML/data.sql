-- Set OrgType
INSERT INTO {{OrgType}} (Identifier, Name, Description, IsHidden) VALUES
  ('synapse',      'Synapse',      'Internal Synapse organization', true),  -- Internal org for Synapse
  ('municipality', 'Municipality', 'Municipality organization',     false), -- External org for municipalities
  ('oem',          'OEM',          'OEM organization',              false)  -- External org for OEMs
;
-- Set PermissionGroup
INSERT INTO {{PermissionGroup}} (Identifier, Scope, Name, Weight,Description) VALUES
  ('synapse',        'synapse',        'Synapse',        1.00, 'Permission sets only available to Synapse and its internal users'), -- Internal permission group for Synapse
  ('organization',   'org',            'Organization',   2.00, 'Permissions applied across the organization'), -- Permission group for organizations
  ('device_group',   'device_group',   'Device Group',   3.00, 'Permissions applied for device groups assigned to users'), -- Permission group for device groups
  ('location_group', 'location_group', 'Location Group', 4.00, 'Permissions applied for location groups assigned to users'), -- Permission group for location groups
  ('reports',        'org',            'Reports',        5.00, 'Permissions applied for reports') -- Permission group for reports
;
-- Set Permission
INSERT INTO {{Permission}} (Identifier, PermissionGroupIdentifier, OrgTypeIdentifier, Weight, Name, Description) VALUES
-- Synapse OrgType Permissions
  ('synapse_view_synapse_users',              'synapse',        'synapse',      1.00, 'View Synapse Users', 'Grants ability to view all Synapse users.'),
  ('synapse_manage_synapse_users',            'synapse',        'synapse',      2.00, 'Update Synapse Users', 'Grants ability to edit all Synapse users.'),
  ('synapse_delete_synapse_users',            'synapse',        'synapse',      3.00, 'Delete Synapse Users', 'Grants ability to delete all Synapse users.'),
  ('synapse_view_synapse_reports',            'synapse',        'synapse',      4.00, 'View Synapse Reports', 'Grants ability to view reports in Synapse.'),
  ('synapse_view_synapse_admin_reports',      'synapse',        'synapse',      5.00, 'View Synapse Admin Reports', 'Grants ability to view admin reports in Synapse.'),
  ('synapse_view_organizations',              'organization',   'synapse',      1.00, 'View All Organizations', 'Grants ability to view all organizations.'),
  ('synapse_manage_organizations',            'organization',   'synapse',      2.00, 'Update All Organizations', 'Grants ability to manage all organizations.'),
  ('synapse_delete_organizations',            'organization',   'synapse',      3.00, 'Delete All Organizations', 'Grants ability to delete all organizations.'),
  ('synapse_view_organization_users',         'organization',   'synapse',      4.00, 'View Organization Users', 'Grants ability to view all users in an organization.'),
  ('synapse_manage_organization_users',       'organization',   'synapse',      5.00, 'Update Organization Users', 'Grants ability to edit all users in an organization.'),
  ('synapse_delete_organization_users',       'organization',   'synapse',      6.00, 'Delete Organization Users', 'Grants ability to delete all users in an organization.'),
  ('synapse_view_organization_reports',       'reports',        'synapse',      1.00, 'View Organization Reports', 'Grants ability to view reports in an organization.'),
  ('synapse_view_organization_admin_reports', 'reports',        'synapse',      2.00, 'View Organization Admin Reports', 'Grants ability to view admin reports in an organization.'),
-- Municipality OrgType Permissions
  ('org_view_users',                          'organization',   'municipality', 1.00, 'View Users', 'Grants ability to view all users in the organization.'),
  ('org_manage_users',                        'organization',   'municipality', 2.00, 'Update Users', 'Grants ability to invite and edit users in the organization.'),
  ('org_delete_users',                        'organization',   'municipality', 3.00, 'Remove Users', 'Grants ability to remove users from the organization.'),
  ('org_view_settings',                       'organization',   'municipality', 4.00, 'View Settings', 'Grants ability to view organization-wide settings and preferences.'),
  ('org_manage_settings',                     'organization',   'municipality', 5.00, 'Update Settings', 'Grants ability to change organization-wide settings and preferences.'),
  ('org_manage_device_groups',                'organization',   'municipality', 6.00, 'Update Device Groups', 'Grants ability to create, edit, and delete device groups in the organization.'),
  ('org_manage_location_groups',              'organization',   'municipality', 7.00, 'Update Location Groups', 'Grants ability to create, edit, and delete location groups in the organization.'),
  ('org_view_devices',                        'organization',   'municipality', 8.00, 'View All Devices', 'Grants ability to view all devices in the organization.'),
  ('org_manage_devices',                      'organization',   'municipality', 9.00, 'Update All Devices', 'Grants ability to update operational fields on all devices in the organization.'),
  ('org_delete_devices',                      'organization',   'municipality', 10.00, 'Delete Devices', 'Grants ability to delete devices in the organization.'),
  ('org_view_reports',                        'reports',        'municipality', 1.00, 'View Reports', 'Grants ability to view reports in the organization.'),
  ('org_view_admin_reports',                  'reports',        'municipality', 2.00, 'View Admin Reports', 'Grants ability to view admin reports in the organization.'),
--('org_export_reports',                      'reports',        'municipality', 3.00, 'Export Reports', 'Allows exporting downloadable reports.'), -- Not implemented yet
  ('device_group_view_devices',               'device_group',   'municipality', 1.00, 'View Devices', 'Grants ability to view devices within a device group.'),
  ('device_group_manage_devices',             'device_group',   'municipality', 1.00, 'Update Devices', 'Grants ability to update operational fields on devices within a device group.'),
  ('device_group_delete_devices',             'device_group',   'municipality', 2.00, 'Delete Devices', 'Grants ability to delete devices within a device group.'),
  ('location_group_view_devices',             'location_group', 'municipality', 3.00, 'View Devices', 'Grants ability to view devices within a location group.'),
  ('location_group_manage_devices',           'location_group', 'municipality', 4.00, 'Update Devices', 'Grants ability to update operational fields on devices within a location group.'),
  ('location_group_delete_devices',           'location_group', 'municipality', 5.00, 'Delete Devices', 'Grants ability to delete devices within a location group.');
-- Set TemplateRole
INSERT INTO {{TemplateRole}} (Identifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
-- Municipality permissions
  ('mun_admin',       'municipality', 'Admin',       'Administrative permissions', FALSE),
  ('mun_manager',     'municipality', 'Manager',     'Manager permissions', TRUE),
  ('mun_technician',  'municipality', 'Technician',  'Technician permissions', TRUE),
  ('mun_anonymous',   'municipality', 'Anonymous',   'Deny all permissions', FALSE),
-- Synapse permissions
  ('syn_admin',       'synapse', 'Admin',       'Administrative permissions', FALSE),
  ('syn_manager',     'synapse', 'Manager',     'Manager permissions', TRUE),
  ('syn_onboarder',   'synapse', 'Onboarder',   'Onboarding permissions', TRUE),
  ('syn_anonymous',   'synapse', 'Anonymous',   'Deny all permissions', FALSE)
;
-- Set TemplateRolePermission
INSERT INTO {{TemplateRolePermission}} (TemplateRoleIdentifier, PermissionIdentifier, DefaultValue) VALUES
-- municipality:admin permissions (full access)
  ('mun_admin', 'org_view_users',                    true),
  ('mun_admin', 'org_manage_users',                  true),
  ('mun_admin', 'org_delete_users',                  true),
  ('mun_admin', 'org_view_settings',                 true),
  ('mun_admin', 'org_manage_settings',               true),
  ('mun_admin', 'org_manage_device_groups',          true),
  ('mun_admin', 'org_manage_location_groups',        true),
  ('mun_admin', 'org_view_devices',                  true),
  ('mun_admin', 'org_manage_devices',                true),
  ('mun_admin', 'org_delete_devices',                true),
  ('mun_admin', 'org_view_reports',                  true),
  ('mun_admin', 'org_view_admin_reports',            true),
  ('mun_admin', 'device_group_view_devices',         true),
  ('mun_admin', 'device_group_manage_devices',       true),
  ('mun_admin', 'device_group_delete_devices',       true),
  ('mun_admin', 'location_group_view_devices',       true),
  ('mun_admin', 'location_group_manage_devices',     true),
  ('mun_admin', 'location_group_delete_devices',     true),
-- municipality:manager permissions (limited delete access)
  ('mun_manager', 'org_view_users',                  true),
  ('mun_manager', 'org_manage_users',                true),
  ('mun_manager', 'org_delete_users',                false),
  ('mun_manager', 'org_view_settings',               true),
  ('mun_manager', 'org_manage_settings',             false),
  ('mun_manager', 'org_manage_device_groups',        true),
  ('mun_manager', 'org_manage_location_groups',      true),
  ('mun_manager', 'org_view_devices',                true),
  ('mun_manager', 'org_manage_devices',              true),
  ('mun_manager', 'org_delete_devices',              false),
  ('mun_manager', 'org_view_reports',                true),
  ('mun_manager', 'org_view_admin_reports',          true),
  ('mun_manager', 'device_group_view_devices',       true),
  ('mun_manager', 'device_group_manage_devices',     true),
  ('mun_manager', 'device_group_delete_devices',     false),
  ('mun_manager', 'location_group_view_devices',     true),
  ('mun_manager', 'location_group_manage_devices',   true),
  ('mun_manager', 'location_group_delete_devices',   false),
-- municipality:technician permissions (device focused)
  ('mun_technician', 'org_view_users',               false),
  ('mun_technician', 'org_manage_users',             false),
  ('mun_technician', 'org_delete_users',             false),
  ('mun_technician', 'org_view_settings',            false),
  ('mun_technician', 'org_manage_settings',          false),
  ('mun_technician', 'org_manage_device_groups',     false),
  ('mun_technician', 'org_manage_location_groups',   false),
  ('mun_technician', 'org_view_devices',             true),
  ('mun_technician', 'org_manage_devices',           false),
  ('mun_technician', 'org_delete_devices',           false),
  ('mun_technician', 'org_view_reports',             true),
  ('mun_technician', 'org_view_admin_reports',       false),
  ('mun_technician', 'device_group_view_devices',    true),
  ('mun_technician', 'device_group_manage_devices',  true),
  ('mun_technician', 'device_group_delete_devices',  false),
  ('mun_technician', 'location_group_view_devices',  true),
  ('mun_technician', 'location_group_manage_devices', true),
  ('mun_technician', 'location_group_delete_devices', false),
-- municipality:anonymous permissions (no access)
  ('mun_anonymous', 'org_view_users',                false),
  ('mun_anonymous', 'org_manage_users',              false),
  ('mun_anonymous', 'org_delete_users',              false),
  ('mun_anonymous', 'org_view_settings',             false),
  ('mun_anonymous', 'org_manage_settings',           false),
  ('mun_anonymous', 'org_manage_device_groups',      false),
  ('mun_anonymous', 'org_manage_location_groups',    false),
  ('mun_anonymous', 'org_view_devices',              false),
  ('mun_anonymous', 'org_manage_devices',            false),
  ('mun_anonymous', 'org_delete_devices',            false),
  ('mun_anonymous', 'org_view_reports',              false),
  ('mun_anonymous', 'org_view_admin_reports',        false),
  ('mun_anonymous', 'device_group_view_devices',     false),
  ('mun_anonymous', 'device_group_manage_devices',   false),
  ('mun_anonymous', 'device_group_delete_devices',   false),
  ('mun_anonymous', 'location_group_view_devices',   false),
  ('mun_anonymous', 'location_group_manage_devices', false),
  ('mun_anonymous', 'location_group_delete_devices', false),
-- synapse:admin permissions (full synapse access)
  ('syn_admin', 'synapse_view_synapse_users',              true),
  ('syn_admin', 'synapse_manage_synapse_users',            true),
  ('syn_admin', 'synapse_delete_synapse_users',            true),
  ('syn_admin', 'synapse_view_synapse_reports',            true),
  ('syn_admin', 'synapse_view_synapse_admin_reports',      true),
  ('syn_admin', 'synapse_view_organizations',              true),
  ('syn_admin', 'synapse_manage_organizations',            true),
  ('syn_admin', 'synapse_delete_organizations',            true),
  ('syn_admin', 'synapse_view_organization_users',         true),
  ('syn_admin', 'synapse_manage_organization_users',       true),
  ('syn_admin', 'synapse_delete_organization_users',       true),
  ('syn_admin', 'synapse_view_organization_reports',       true),
  ('syn_admin', 'synapse_view_organization_admin_reports', true),
-- synapse:manager permissions (limited delete access)
  ('syn_manager', 'synapse_view_synapse_users',              true),
  ('syn_manager', 'synapse_manage_synapse_users',            true),
  ('syn_manager', 'synapse_delete_synapse_users',            false),
  ('syn_manager', 'synapse_view_synapse_reports',            true),
  ('syn_manager', 'synapse_view_synapse_admin_reports',      false),
  ('syn_manager', 'synapse_view_organizations',              true),
  ('syn_manager', 'synapse_manage_organizations',            true),
  ('syn_manager', 'synapse_delete_organizations',            false),
  ('syn_manager', 'synapse_view_organization_users',         true),
  ('syn_manager', 'synapse_manage_organization_users',       true),
  ('syn_manager', 'synapse_delete_organization_users',       false),
  ('syn_manager', 'synapse_view_organization_reports',       true),
  ('syn_manager', 'synapse_view_organization_admin_reports', false),
-- synapse:onboarder permissions (onboarding focused)
  ('syn_onboarder', 'synapse_view_synapse_users',              false),
  ('syn_onboarder', 'synapse_manage_synapse_users',            false),
  ('syn_onboarder', 'synapse_delete_synapse_users',            false),
  ('syn_onboarder', 'synapse_view_synapse_reports',            true),
  ('syn_onboarder', 'synapse_view_synapse_admin_reports',      false),
  ('syn_onboarder', 'synapse_view_organizations',              true),
  ('syn_onboarder', 'synapse_manage_organizations',            false),
  ('syn_onboarder', 'synapse_delete_organizations',            false),
  ('syn_onboarder', 'synapse_view_organization_users',         true),
  ('syn_onboarder', 'synapse_manage_organization_users',       false),
  ('syn_onboarder', 'synapse_delete_organization_users',       false),
  ('syn_onboarder', 'synapse_view_organization_reports',       true),
  ('syn_onboarder', 'synapse_view_organization_admin_reports', false),
-- synapse:anonymous permissions (no access)
  ('syn_anonymous', 'synapse_view_synapse_users',              false),
  ('syn_anonymous', 'synapse_manage_synapse_users',            false),
  ('syn_anonymous', 'synapse_delete_synapse_users',            false),
  ('syn_anonymous', 'synapse_view_synapse_reports',            false),
  ('syn_anonymous', 'synapse_view_synapse_admin_reports',      false),
  ('syn_anonymous', 'synapse_view_organizations',              false),
  ('syn_anonymous', 'synapse_manage_organizations',            false),
  ('syn_anonymous', 'synapse_delete_organizations',            false),
  ('syn_anonymous', 'synapse_view_organization_users',         false),
  ('syn_anonymous', 'synapse_manage_organization_users',       false),
  ('syn_anonymous', 'synapse_delete_organization_users',       false),
  ('syn_anonymous', 'synapse_view_organization_reports',       false),
  ('syn_anonymous', 'synapse_view_organization_admin_reports', false)
;
-- Set available instruction statuses
INSERT INTO {{SoftwareGatewayInstructionStatus}} (Status) VALUES 
  ('queued'), 
  ('received');
-- Set available instructions
INSERT INTO {{SoftwareGatewayInstructionList}} (Instruction) VALUES 
  ('get_device_logs');
-- Create a Synapse organization
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'Synapse', 'Synapse-Its.com Organization', 'synapse');
-- Create the Custom Roles for the Synapse organization, no permission overrides.
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_admin', 'synapse', 'Synapse Admin', 'Synapse Admin Role', FALSE),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_manager'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_manager', 'synapse', 'Synapse Manager', 'Synapse Manager Role', TRUE),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_onboarder'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_onboarder', 'synapse', 'Synapse Onboarder', 'Synapse Onboarder Role', TRUE),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_anonymous'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_anonymous', 'synapse', 'Synapse Anonymous', 'Synapse Anonymous Role', FALSE);
-- Set available device types
INSERT INTO {{DeviceType}} (Type, Description) VALUES 
  ('UNKNOWN_DEVICE', 'Unknown device type'), 
  ('EDI_LEGACY', 'EDI Legacy device'), 
  ('EDI_NEXT_GEN', 'EDI Next Gen device');

-- THESE MUST ALWAYS BE LAST
SELECT setval('{{Device_OrigId_SEQ}}', coalesce((select max(origid) from {{Device}}),1));
SELECT setval('{{User_OrigId_SEQ}}', coalesce((select max(origid) from {{User}}),1));
