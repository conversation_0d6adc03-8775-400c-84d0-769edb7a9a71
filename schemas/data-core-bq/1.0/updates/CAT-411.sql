-- Create a temp table to hold the data
CREATE TABLE {{FaultNotification_temp}} AS
SELECT * FROM {{FaultNotification}};

-- Drop the old table
DROP TABLE {{FaultNotification}};

-- Recreate FaultNotification table
CREATE TABLE {{FaultNotification}} (
  organizationidentifier  STRING    {% OPTIONS(description="OrganizationIdentifier from authentication") %},
  softwaregatewayid       STRING    {% OPTIONS(description="SoftwareGateway identifier from the HTTP headers") %},
  tz                      STRING    {% OPTIONS(description="SoftwareGateway timezone from the HTTP headers") %},
  topic                   STRING    {% OPTIONS(description="The pubsub topic name") %},
  pubsubtimestamp         TIMESTAMP {% OPTIONS(description="The timestamp the pubsub message timestamp") %},
  pubsubid                STRING    {% OPTIONS(description="The pubsub message identifier") %},
  deviceid                STRING    {% OPTIONS(description="Unique identifier for the device") %},
  header                  STRUCT<
    commversion      STRING {% OPTIONS(description="Device ECcom revision number") %},
    model            INT64 {% OPTIONS(description="Device model") %},
    firmwareversion  STRING {% OPTIONS(description="Device firmware version") %},
    firmwarerevision STRING {% OPTIONS(description="Device firmware revision") %},
    monitorid        INT64 {% OPTIONS(description="User defined monitor id") %},
    volt220          BOOL  {% OPTIONS(description="Flag for whether device supports monitoring 220 volt") %},
    voltdc           BOOL  {% OPTIONS(description="Flag for whether device supports monitoring dc voltage") %},
    mainsdc          BOOL  {% OPTIONS(description="Flag for whether device supports dc mains power") %},
    powerdownlevel   INT64 {% OPTIONS(description="Voltage level at which device will power down") %},
    blackoutlevel    INT64 {% OPTIONS(description="Voltage level at which device will blackout") %},
    maxchannels      INT64 {% OPTIONS(description="Maximum number of channels monitor can support") %}
                          >         {% OPTIONS(description="Standard fields derived from first 7 bytes of header") %},
  isfaulted               BOOL      {% OPTIONS(description="Flag indicating if the device is in a falted state") %},
  fault                   STRING    {% OPTIONS(description="The user-friendly fault") %},
  faultstatus             STRING    {% OPTIONS(description="The user-friendly fault status") %},
  monitortime             TIMESTAMP {% OPTIONS(description="The date/time of the monitor - converted to UTC") %},
  temperaturef            INT64     {% OPTIONS(description="Temperature in fahrenheit") %},
  channelgreenstatus      STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 green status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 green status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 green status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 green status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 green status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 green status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 green status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 green status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 green status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 green status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 green status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 green status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 green status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 green status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 green status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 green status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 green status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 green status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 green status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 green status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 green status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 green status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 green status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 green status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 green status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 green status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 green status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 green status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 green status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 green status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 green status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 green status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 green status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 green status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 green status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 green status flag") %}
                          >         {% OPTIONS(description="Struct of green channel status flags") %},
  channelyellowstatus     STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 yellow status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 yellow status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 yellow status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 yellow status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 yellow status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 yellow status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 yellow status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 yellow status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 yellow status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 yellow status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 yellow status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 yellow status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 yellow status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 yellow status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 yellow status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 yellow status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 yellow status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 yellow status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 yellow status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 yellow status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 yellow status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 yellow status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 yellow status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 yellow status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 yellow status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 yellow status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 yellow status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 yellow status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 yellow status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 yellow status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 yellow status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 yellow status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 yellow status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 yellow status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 yellow status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 yellow status flag") %}
                          >         {% OPTIONS(description="Struct of yellow channel status flags") %},
  channelredstatus STRUCT<
    channel01 BOOL {% OPTIONS(description="Channel 1 red status flag") %},
    channel02 BOOL {% OPTIONS(description="Channel 2 red status flag") %},
    channel03 BOOL {% OPTIONS(description="Channel 3 red status flag") %},
    channel04 BOOL {% OPTIONS(description="Channel 4 red status flag") %},
    channel05 BOOL {% OPTIONS(description="Channel 5 red status flag") %},
    channel06 BOOL {% OPTIONS(description="Channel 6 red status flag") %},
    channel07 BOOL {% OPTIONS(description="Channel 7 red status flag") %},
    channel08 BOOL {% OPTIONS(description="Channel 8 red status flag") %},
    channel09 BOOL {% OPTIONS(description="Channel 9 red status flag") %},
    channel10 BOOL {% OPTIONS(description="Channel 10 red status flag") %},
    channel11 BOOL {% OPTIONS(description="Channel 11 red status flag") %},
    channel12 BOOL {% OPTIONS(description="Channel 12 red status flag") %},
    channel13 BOOL {% OPTIONS(description="Channel 13 red status flag") %},
    channel14 BOOL {% OPTIONS(description="Channel 14 red status flag") %},
    channel15 BOOL {% OPTIONS(description="Channel 15 red status flag") %},
    channel16 BOOL {% OPTIONS(description="Channel 16 red status flag") %},
    channel17 BOOL {% OPTIONS(description="Channel 17 red status flag") %},
    channel18 BOOL {% OPTIONS(description="Channel 18 red status flag") %},
    channel19 BOOL {% OPTIONS(description="Channel 19 red status flag") %},
    channel20 BOOL {% OPTIONS(description="Channel 20 red status flag") %},
    channel21 BOOL {% OPTIONS(description="Channel 21 red status flag") %},
    channel22 BOOL {% OPTIONS(description="Channel 22 red status flag") %},
    channel23 BOOL {% OPTIONS(description="Channel 23 red status flag") %},
    channel24 BOOL {% OPTIONS(description="Channel 24 red status flag") %},
    channel25 BOOL {% OPTIONS(description="Channel 25 red status flag") %},
    channel26 BOOL {% OPTIONS(description="Channel 26 red status flag") %},
    channel27 BOOL {% OPTIONS(description="Channel 27 red status flag") %},
    channel28 BOOL {% OPTIONS(description="Channel 28 red status flag") %},
    channel29 BOOL {% OPTIONS(description="Channel 29 red status flag") %},
    channel30 BOOL {% OPTIONS(description="Channel 30 red status flag") %},
    channel31 BOOL {% OPTIONS(description="Channel 31 red status flag") %},
    channel32 BOOL {% OPTIONS(description="Channel 32 red status flag") %},
    channel33 BOOL {% OPTIONS(description="Channel 33 red status flag") %},
    channel34 BOOL {% OPTIONS(description="Channel 34 red status flag") %},
    channel35 BOOL {% OPTIONS(description="Channel 35 red status flag") %},
    channel36 BOOL {% OPTIONS(description="Channel 36 red status flag") %}
                          >         {% OPTIONS(description="Struct of red channel status flags") %},
  channelgreenvoltage     STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 green voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 green voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 green voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 green voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 green voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 green voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 green voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 green voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 green voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 green voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 green voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 green voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 green voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 green voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 green voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 green voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 green voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 green voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 green voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 green voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 green voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 green voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 green voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 green voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 green voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 green voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 green voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 green voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 green voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 green voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 green voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 green voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 green voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 green voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 green voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 green voltage") %}
                          >         {% OPTIONS(description="Struct of green channel voltages") %},
  channelyellowvoltage    STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 yellow voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 yellow voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 yellow voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 yellow voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 yellow voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 yellow voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 yellow voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 yellow voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 yellow voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 yellow voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 yellow voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 yellow voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 yellow voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 yellow voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 yellow voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 yellow voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 yellow voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 yellow voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 yellow voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 yellow voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 yellow voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 yellow voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 yellow voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 yellow voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 yellow voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 yellow voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 yellow voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 yellow voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 yellow voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 yellow voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 yellow voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 yellow voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 yellow voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 yellow voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 yellow voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 yellow voltage") %}
                          >         {% OPTIONS(description="Struct of yellow channel voltages") %},
  channelredvoltage STRUCT<
    channel01 INT64 {% OPTIONS(description="Channel 1 red voltage") %},
    channel02 INT64 {% OPTIONS(description="Channel 2 red voltage") %},
    channel03 INT64 {% OPTIONS(description="Channel 3 red voltage") %},
    channel04 INT64 {% OPTIONS(description="Channel 4 red voltage") %},
    channel05 INT64 {% OPTIONS(description="Channel 5 red voltage") %},
    channel06 INT64 {% OPTIONS(description="Channel 6 red voltage") %},
    channel07 INT64 {% OPTIONS(description="Channel 7 red voltage") %},
    channel08 INT64 {% OPTIONS(description="Channel 8 red voltage") %},
    channel09 INT64 {% OPTIONS(description="Channel 9 red voltage") %},
    channel10 INT64 {% OPTIONS(description="Channel 10 red voltage") %},
    channel11 INT64 {% OPTIONS(description="Channel 11 red voltage") %},
    channel12 INT64 {% OPTIONS(description="Channel 12 red voltage") %},
    channel13 INT64 {% OPTIONS(description="Channel 13 red voltage") %},
    channel14 INT64 {% OPTIONS(description="Channel 14 red voltage") %},
    channel15 INT64 {% OPTIONS(description="Channel 15 red voltage") %},
    channel16 INT64 {% OPTIONS(description="Channel 16 red voltage") %},
    channel17 INT64 {% OPTIONS(description="Channel 17 red voltage") %},
    channel18 INT64 {% OPTIONS(description="Channel 18 red voltage") %},
    channel19 INT64 {% OPTIONS(description="Channel 19 red voltage") %},
    channel20 INT64 {% OPTIONS(description="Channel 20 red voltage") %},
    channel21 INT64 {% OPTIONS(description="Channel 21 red voltage") %},
    channel22 INT64 {% OPTIONS(description="Channel 22 red voltage") %},
    channel23 INT64 {% OPTIONS(description="Channel 23 red voltage") %},
    channel24 INT64 {% OPTIONS(description="Channel 24 red voltage") %},
    channel25 INT64 {% OPTIONS(description="Channel 25 red voltage") %},
    channel26 INT64 {% OPTIONS(description="Channel 26 red voltage") %},
    channel27 INT64 {% OPTIONS(description="Channel 27 red voltage") %},
    channel28 INT64 {% OPTIONS(description="Channel 28 red voltage") %},
    channel29 INT64 {% OPTIONS(description="Channel 29 red voltage") %},
    channel30 INT64 {% OPTIONS(description="Channel 30 red voltage") %},
    channel31 INT64 {% OPTIONS(description="Channel 31 red voltage") %},
    channel32 INT64 {% OPTIONS(description="Channel 32 red voltage") %},
    channel33 INT64 {% OPTIONS(description="Channel 33 red voltage") %},
    channel34 INT64 {% OPTIONS(description="Channel 34 red voltage") %},
    channel35 INT64 {% OPTIONS(description="Channel 35 red voltage") %},
    channel36 INT64 {% OPTIONS(description="Channel 36 red voltage") %}
                          >         {% OPTIONS(description="Struct of red channel voltages") %},
  rawmessage              BYTES     {% OPTIONS(description="Raw byte message from device") %}
)
{% PARTITION BY DATE(pubsubtimestamp) CLUSTER BY organizationidentifier, softwaregatewayid, deviceid %};

-- Insert the data into the new table
INSERT INTO {{FaultNotification}} (
  organizationidentifier,
  softwaregatewayid,
  tz,
  topic,
  pubsubtimestamp,
  pubsubid,
  deviceid,
  header,
  isfaulted,
  fault,
  faultstatus,
  monitortime,
  temperaturef,
  channelgreenstatus,
  channelyellowstatus,
  channelredstatus,
  channelgreenvoltage,
  channelyellowvoltage,
  channelredvoltage,
  rawmessage
)
SELECT
  organizationidentifier,
  softwaregatewayid,
  tz,
  topic,
  pubsubtimestamp,
  pubsubid,
  deviceid,
  STRUCT(
    CAST(header.commversion AS STRING) AS commversion,
    header.model,
    CAST(header.firmwareversion AS STRING) AS firmwareversion,
    CAST(header.firmwarerevision AS STRING) AS firmwarerevision,
    header.monitorid,
    header.volt220,
    header.voltdc,
    header.mainsdc,
    header.powerdownlevel,
    header.blackoutlevel,
    header.maxchannels
  ) AS header,
  isfaulted,
  fault,
  faultstatus,
  monitortime,
  temperaturef,
  channelgreenstatus,
  channelyellowstatus,
  channelredstatus,
  channelgreenvoltage,
  channelyellowvoltage,
  channelredvoltage,
  rawmessage
FROM {{FaultNotification_temp}};

-- Drop the temp table
DROP TABLE {{FaultNotification_temp}};
