-- Not sure if this will be needed since we are storing the model name into every record anyways
-- INSERT INTO {{ModelName}} 
--   (code, name, model)
-- VALUES
--   ('C210',         '210C',                    b'\x00'),
--   ('N210',         '210N',                    b'\x01'),
--   ('Ecl210',       '210ECL',                  b'\x02'),
--   ('Ecl2010',      '2010ECL',                 b'\x03'),
--   ('E2010',        '2010',                    b'\x04'),
--   ('E210',         '210E',                    b'\x05'),
--   ('Ecl2048',      '2048ECL',                 b'\x06'),
--   ('K2018',        '2018K',                   b'\x07'),
--   ('Kcl2018',      '2018KCL',                 b'\x08'),
--   ('Ecl2018',      '2018ECL',                 b'\x09'),
--   ('Mmu16e',       'MMU-16E',                 b'\x0A'),
--   ('Mmu16le',      'MMU2-16LE SmartMonitor',  b'\x0B'),
--   ('Mmu48e',       'MMU-48E',                 b'\x0C'),
--   ('Mmu16lex',     'MMU2-16LEX SmartMonitor', b'\x0D'),
--   ('Ssm12e',       'SSM-12E',                 b'\x14'),
--   ('Ssm6e',        'SSM-6E',                  b'\x15'),
--   ('Nsm12',        'NSM-12',                  b'\x16'),
--   ('Nsm6',         'NSM-6',                   b'\x17'),
--   ('Nsm12e',       'NSM-12E',                 b'\x18'),
--   ('Nsm6e',        'NSM-6E',                  b'\x19'),
--   ('Nsm3e',        'NSM-3E',                  b'\x1A'),
--   ('Ssm12le',      'SSM-12LE',                b'\x1E'),
--   ('Ssm6le',       'SSM-6LE',                 b'\x1F'),
--   ('Ssm12lec',     'SSM-12LEC',               b'\x20'),
--   ('Ssm6lec',      'SSM-6LEC',                b'\x21'),
--   ('Ssm48le',      'SSM-48LE',                b'\x22'),
--   ('CMU212',       'CMU-212',                 b'\x28'),
--   ('CMU212_220',   'CMU-220VAC',              b'\x29'),
--   ('CMU212_48',    'CMU-48VDC',               b'\x2A'),
--   ('CMU212_48H',   'CMU-48DC Hybrid',         b'\x2B'),
--   ('CMU212_12',    'CMU-12VDC',               b'\x2C'),
--   ('CMU212_24',    'CMU-24VDC',               b'\x2D'),
--   ('CMU2212_hv',   'CMU2212-HV',              b'\x32'),
--   ('CMUip2212_hv', 'CMUip2212-HV',            b'\x33'),
--   ('CMU2212_lv',   'CMU2212-LV',              b'\x34'),
--   ('CMUip2212_lv', 'CMUip2212-LV',            b'\x35'),
--   ('CMU2212_vhv',  'CMU2212-VHV',             b'\x36'),
--   ('CMUip2212_vhv','CMUip2212-VHV',           b'\x37');

SELECT 1;
